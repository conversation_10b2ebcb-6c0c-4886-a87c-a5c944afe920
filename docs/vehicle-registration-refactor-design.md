# 车辆登记页面重构设计文档

## 1. 重构概述

### 1.1 重构目标
将 `VehicleRegistrationView.vue` 从 `src/views/customerOrderManagement/` 重构到符合项目规范的模块化结构，实现：
- 符合页面目录结构规范的模块化组织
- 标准化MyBatisPlus分页组件使用
- 规范化数据字典实现
- 统一API响应处理方式

### 1.2 当前问题分析

#### 1.2.1 目录结构问题
```
❌ 当前结构:
src/views/customerOrderManagement/VehicleRegistrationView.vue

✅ 目标结构:
src/views/sales/vehicleRegistration/
├── VehicleRegistrationView.vue           # 主页面文件（路由页面）
└── components/
    ├── VehicleRegistrationDetailDialog.vue  # 详情对话框（非路由页面）
    ├── PushConfirmDialog.vue             # 推送确认对话框
    └── RetryPushDialog.vue               # 重新推送对话框
src/api/modules/sales/
└── vehicleRegistration.ts               # API模块
src/types/sales/
└── vehicleRegistration.d.ts             # 类型定义
src/mock/data/sales/
└── vehicleRegistration.ts               # Mock数据
```

#### 1.2.2 MyBatisPlus分页问题分析
**当前实现问题**:
```typescript
// ❌ 分页参数不符合MyBatisPlus标准
const pagination = reactive({
  pageNumber: 1,        // ❌ 应为 pageNum
  pageSize: 20,         // ✅ 正确
  total: 0              // ✅ 正确
});

// ❌ 分页组件绑定不符合标准
<el-pagination
  v-model:current-page="pagination.pageNumber"  // ❌ 应绑定 pageNum
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
/>

// ✅ 响应处理正确
tableData.value = response.result.records || [];  // ✅ 正确使用 records
pagination.total = response.result.total || 0;    // ✅ 正确使用 total
```

**目标实现**:
```typescript
// ✅ 标准MyBatisPlus分页参数
interface VehicleRegistrationSearchParams {
  pageNum?: number;     // MyBatisPlus标准参数
  pageSize?: number;    // MyBatisPlus标准参数
  // ... 其他搜索参数
}

// ✅ 标准分页状态
const pagination = reactive({
  pageNum: 1,           // 修正为 pageNum
  pageSize: 20,
  total: 0
});

// ✅ 标准分页组件绑定
<el-pagination
  v-model:current-page="pagination.pageNum"    // 修正为 pageNum
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
  layout="total, sizes, prev, pager, next, jumper"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
/>
```

#### 1.2.3 数据字典问题分析
**当前实现问题**:
```typescript
// ❌ 硬编码字典值和转义逻辑
const getStatusTagType = (status: string): string => {
  switch (status) {
    case 'pending':
    case '待登记':
      return 'primary';
    case 'processing':
    case '登记中':
      return 'warning';
    // ... 硬编码映射
  }
};

const getStatusDisplayText = (status: string): string => {
  switch (status) {
    case 'pending':
      return '待登记';
    case 'processing':
      return '登记中';
    // ... 硬编码转义
  }
};

// ❌ 下拉选项硬编码
<el-option :label="t('registrationStatusPending')" value="pending" />
<el-option :label="t('registrationStatusProcessing')" value="processing" />
```

**目标实现**:
```typescript
// ✅ 使用标准数据字典
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.SALES_ADVISOR
]);

// ✅ 标准字典转义
const formatRegistrationStatus = (status: string) => 
  getNameByCode(DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS, status) || status;

// ✅ 标准下拉选项
<el-option
  v-for="option in getOptions(DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS)"
  :key="option.code"
  :label="option.name"
  :value="option.code"
/>
```

#### 1.2.4 API响应处理问题分析
**当前实现**:
```typescript
// ✅ 响应处理基本正确
const response = await getVehicleRegistrationList(filteredParams);
console.log('response', response);
tableData.value = response.result.records || [];
pagination.total = response.result.total || 0;
```

**需要优化的地方**:
```typescript
// ✅ 标准API响应处理模式
try {
  const response = await getVehicleRegistrationList(filteredParams);
  
  // 标准响应结构: { code, message, result: { records, total, pageNum, pageSize, pages } }
  if (response.code === '200' || response.code === 200) {
    tableData.value = response.result.records || [];
    pagination.total = response.result.total || 0;
    pagination.pageNum = response.result.pageNum || pagination.pageNum;
  } else {
    throw new Error(response.message || '获取数据失败');
  }
} catch (error) {
  console.error('获取车辆登记列表失败:', error);
  ElMessage.error(tc('loadFailed'));
  tableData.value = [];
  pagination.total = 0;
}
```

## 2. 重构实施方案

### 2.1 目录结构重构

#### 2.1.1 创建新的目录结构
```bash
# 创建页面目录
mkdir -p src/views/sales/vehicleRegistration/components

# 创建API模块目录
mkdir -p src/api/modules/sales

# 创建类型定义目录
mkdir -p src/types/sales

# 创建Mock数据目录
mkdir -p src/mock/data/sales
```

#### 2.1.2 文件迁移计划
1. **主页面文件**: `VehicleRegistrationView.vue` → `src/views/sales/vehicleRegistration/VehicleRegistrationView.vue`
2. **弹窗组件拆分**: 将三个弹窗拆分为独立组件放入 `components/` 目录
3. **API模块**: 创建 `src/api/modules/sales/vehicleRegistration.ts`
4. **类型定义**: 创建 `src/types/sales/vehicleRegistration.d.ts`
5. **Mock数据**: 创建 `src/mock/data/sales/vehicleRegistration.ts`

### 2.2 MyBatisPlus分页标准化

#### 2.2.1 分页参数标准化
```typescript
// src/types/sales/vehicleRegistration.d.ts

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 车辆登记搜索参数（继承分页参数）
export interface VehicleRegistrationSearchParams extends PageParams {
  orderNumber?: string;
  customerName?: string;
  customerPhone?: string;
  registrationStatus?: string;
  vin?: string;
  insuranceStatus?: string;
  salesAdvisor?: string;
  pushTimeStart?: string;
  pushTimeEnd?: string;
}
```

#### 2.2.2 分页组件标准化
```vue
<!-- 标准MyBatisPlus分页组件 -->
<div class="pagination-container">
  <el-pagination
    v-model:current-page="pagination.pageNum"
    v-model:page-size="pagination.pageSize"
    :page-sizes="[10, 20, 50, 100]"
    :total="pagination.total"
    layout="total, sizes, prev, pager, next, jumper"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</div>
```

#### 2.2.3 分页处理函数标准化
```typescript
// 分页处理（修正为MyBatisPlus标准）
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;  // ✅ 修正为pageNum
  loadData();
};

// API调用参数构建
const buildSearchParams = (): VehicleRegistrationSearchParams => {
  const params: VehicleRegistrationSearchParams = {
    pageNum: pagination.pageNum,      // ✅ 使用标准参数名
    pageSize: pagination.pageSize,
    ...searchParams
  };

  // 处理时间范围
  if (pushTimeRange.value) {
    params.pushTimeStart = pushTimeRange.value[0];
    params.pushTimeEnd = pushTimeRange.value[1];
  }

  return filterEmptyParams(params);
};
```

### 2.3 数据字典标准化

#### 2.3.1 字典类型定义
```typescript
// src/constants/dictionary.ts 中添加
export const DICTIONARY_TYPES = {
  // ... 现有字典类型
  VEHICLE_REGISTRATION_STATUS: 'VEHICLE_REGISTRATION_STATUS',  // 车辆登记状态
  INSURANCE_STATUS: 'INSURANCE_STATUS',                        // 保险状态
  SALES_ADVISOR: 'SALES_ADVISOR',                             // 销售顾问
} as const;
```

#### 2.3.2 字典使用标准化
```typescript
// 使用批量字典获取
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.SALES_ADVISOR
]);

// 计算属性获取选项
const registrationStatusOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS)
);
const insuranceStatusOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.INSURANCE_STATUS)
);
const salesAdvisorOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.SALES_ADVISOR)
);

// 标准转义函数
const formatRegistrationStatus = (status: string) => 
  getNameByCode(DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS, status) || status;

const formatInsuranceStatus = (status: string) => 
  getNameByCode(DICTIONARY_TYPES.INSURANCE_STATUS, status) || status;
```

#### 2.3.3 标签样式映射标准化
```typescript
// 状态标签样式映射
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS]: {
    'pending': 'primary',
    'processing': 'warning', 
    'success': 'success',
    'failed': 'danger'
  },
  [DICTIONARY_TYPES.INSURANCE_STATUS]: {
    'insured': 'success',
    'not_insured': 'info'
  }
};

const getStatusTagType = (status: string, dictionaryType: DictionaryType) => 
  STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info';
```

### 2.4 API响应处理标准化

#### 2.4.1 API函数标准化
```typescript
// src/api/modules/sales/vehicleRegistration.ts

import request from '@/api';
import type { 
  VehicleRegistrationSearchParams, 
  VehicleRegistrationPageResponse,
  VehicleRegistrationDetail,
  ApiResponse
} from '@/types/sales/vehicleRegistration';

export const getVehicleRegistrationList = (
  params: VehicleRegistrationSearchParams
): Promise<ApiResponse<VehicleRegistrationPageResponse>> => {
  if (USE_MOCK_API) {
    return getVehicleRegistrationListMock(params);
  } else {
    return request.get<any, ApiResponse<VehicleRegistrationPageResponse>>(
      '/sales/vehicle-registration/list', 
      { params }
    );
  }
};
```

#### 2.4.2 响应处理标准化
```typescript
// 标准API调用和响应处理
const loadData = async () => {
  try {
    loading.value = true;
    
    const params = buildSearchParams();
    const response = await getVehicleRegistrationList(params);
    
    // 标准响应处理
    if (response.code === '200' || response.code === 200) {
      tableData.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pageNum = response.result.pageNum || pagination.pageNum;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取车辆登记列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};
```

## 3. Mock数据设计

### 3.1 Mock数据结构
```typescript
// src/mock/data/sales/vehicleRegistration.ts

import type { 
  VehicleRegistrationSearchParams, 
  VehicleRegistrationPageResponse,
  VehicleRegistrationItem 
} from '@/types/sales/vehicleRegistration';

// 动态生成模拟数据（25-30条，便于测试分页）
function generateMockData(): VehicleRegistrationItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData: VehicleRegistrationItem[] = [];
  
  const statuses = ['pending', 'processing', 'success', 'failed'];
  const insuranceStatuses = ['insured', 'not_insured'];
  const vehicleModels = ['MYVI 1.5L', 'ALZA 1.5L', 'AXIA 1.0L', 'BEZZA 1.3L'];
  const colors = ['白色', '黑色', '银色', '红色', '蓝色'];
  
  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      id: `VR${String(i + 1).padStart(6, '0')}`,
      orderNo: `ORD${String(i + 1).padStart(6, '0')}`,
      customerName: `客户${i + 1}`,
      customerPhone: `1${String(Math.floor(Math.random() * *********) + *********)}`,
      vin: `WVWZZZ1JZ3W${String(Math.floor(Math.random() * 900000) + 100000)}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleColor: colors[Math.floor(Math.random() * colors.length)],
      insuranceStatus: insuranceStatuses[Math.floor(Math.random() * insuranceStatuses.length)],
      companyName: `保险公司${Math.floor(Math.random() * 5) + 1}`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      lastPushTime: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
      registrationFee: Math.floor(Math.random() * 1000) + 100,
      salesAdvisorName: `销售顾问${Math.floor(Math.random() * 10) + 1}`,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  
  return mockData;
}

const mockData = generateMockData();

export const getVehicleRegistrationListMock = (
  params: VehicleRegistrationSearchParams
): Promise<ApiResponse<VehicleRegistrationPageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];
      
      if (params.orderNumber) {
        filteredData = filteredData.filter(item =>
          item.orderNo.toLowerCase().includes(params.orderNumber!.toLowerCase())
        );
      }
      
      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.customerName.includes(params.customerName!)
        );
      }
      
      if (params.registrationStatus) {
        filteredData = filteredData.filter(item =>
          item.status === params.registrationStatus
        );
      }
      
      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        code: '200',
        message: '获取成功',
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        },
        timestamp: Date.now()
      });
    }, 500);
  });
};
```

## 4. 国际化文件更新

### 4.1 模块化国际化结构
```json
// src/locales/modules/sales/zh.json
{
  "vehicleRegistration": {
    "title": "车辆登记管理",
    "orderNumber": "订单号",
    "customerName": "客户姓名",
    "customerPhone": "客户电话",
    "registrationStatus": "登记状态",
    "vin": "车架号",
    "insuranceStatus": "保险状态",
    "pushTimeRange": "推送时间范围",
    "salesAdvisor": "销售顾问",
    "export": "导出",
    "pushRegistration": "推送登记",
    "retryPush": "重新推送",
    "viewDetails": "查看详情",
    "pushSuccess": "推送成功",
    "pushFailed": "推送失败",
    "retryPushSuccess": "重新推送成功",
    "retryPushFailed": "重新推送失败",
    "exportSuccess": "导出成功",
    "exportFailed": "导出失败"
  }
}
```

### 4.2 页面引用方式
```typescript
// 使用模块化引用
const { t, tc } = useModuleI18n('sales.vehicleRegistration');

// t() 访问当前模块翻译: sales.vehicleRegistration.title
// tc() 访问通用模块翻译: common.search
```

## 5. 重构验证清单

### 5.1 目录结构验证
- [ ] 页面文件移动到 `src/views/sales/vehicleRegistration/VehicleRegistrationView.vue`
- [ ] 弹窗组件拆分到 `components/` 目录
- [ ] API模块创建在 `src/api/modules/sales/vehicleRegistration.ts`
- [ ] Mock数据创建在 `src/mock/data/sales/vehicleRegistration.ts`
- [ ] 类型定义创建在 `src/types/sales/vehicleRegistration.d.ts`
- [ ] 国际化文件更新在 `src/locales/modules/sales/`

### 5.2 MyBatisPlus分页验证
- [ ] 分页参数使用 `pageNum` 和 `pageSize`
- [ ] 分页组件绑定 `pagination.pageNum`
- [ ] API响应使用 `response.result.records` 和 `response.result.total`
- [ ] 分页功能正常工作

### 5.3 数据字典验证
- [ ] 所有下拉选项使用字典数据
- [ ] 表格列显示使用字典转义
- [ ] 标签类型映射正确
- [ ] 字典数据加载正常

### 5.4 API响应处理验证
- [ ] 使用 `response.result` 获取数据
- [ ] 错误处理符合规范
- [ ] Mock数据和真实API切换正常

## 6. 类型定义文件

### 6.1 完整类型定义
```typescript
// src/types/sales/vehicleRegistration.d.ts

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 车辆登记列表项
export interface VehicleRegistrationItem {
  id: string;
  orderNo: string;
  customerName: string;
  customerPhone: string;
  vin: string;
  vehicleModel: string;
  vehicleColor: string;
  insuranceStatus: string;
  companyName: string;
  status: string;
  lastPushTime: string;
  registrationFee: number;
  salesAdvisorName: string;
  createdAt: string;
}

// 车辆登记搜索参数
export interface VehicleRegistrationSearchParams extends PageParams {
  orderNumber?: string;
  customerName?: string;
  customerPhone?: string;
  registrationStatus?: string;
  vin?: string;
  insuranceStatus?: string;
  salesAdvisor?: string;
  pushTimeStart?: string;
  pushTimeEnd?: string;
}

// 车辆登记分页响应
export interface VehicleRegistrationPageResponse extends PageResponse<VehicleRegistrationItem> {}

// 车辆登记详情
export interface VehicleRegistrationDetail {
  orderInfo: {
    orderNumber: string;
    orderStatus: string;
    lastPushTime: string;
    createdAt: string;
    salesAdvisor: string;
  };
  customerInfo: {
    name: string;
    idType: string;
    idNumber: string;
    phone: string;
    email: string;
    address: string;
    city: string;
    postcode: string;
    state: string;
  };
  vehicleInfo: {
    vin: string;
    model: string;
    color: string;
    engineNumber: string;
    modelCode: string;
    variant: string;
    productionYear: string;
    manufactureDate: string;
  };
  insuranceInfo: {
    status: string;
    company: string;
    policyNumber: string;
    period: string;
    date: string;
    fee: number;
  };
  jpjInfo: {
    status: string;
    certificateNumber: string;
    pushTime: string;
    completionTime: string;
    operator: string;
    failureReason?: string;
  };
  operationLogs: Array<{
    operationTime: string;
    operationType: string;
    operatorName: string;
    result: string;
    remark: string;
  }>;
}

// API响应类型
export interface ApiResponse<T> {
  code: string | number;
  message: string;
  result: T;
  timestamp: number;
}

// 用户角色类型
export type UserRole = 'vehicle_registration_officer' | 'sales_advisor' | 'manager';

// 销售顾问选项
export interface SalesAdvisorOption {
  value: string;
  label: string;
}
```

## 7. Mock数据完整实现

### 7.1 详情数据Mock
```typescript
// src/mock/data/sales/vehicleRegistration.ts (续)

export const getVehicleRegistrationDetailMock = (
  id: string
): Promise<ApiResponse<VehicleRegistrationDetail>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockData.find(item => item.id === id);
      if (!item) {
        resolve({
          code: '404',
          message: '记录不存在',
          result: null as any,
          timestamp: Date.now()
        });
        return;
      }

      const detail: VehicleRegistrationDetail = {
        orderInfo: {
          orderNumber: item.orderNo,
          orderStatus: item.status,
          lastPushTime: item.lastPushTime,
          createdAt: item.createdAt,
          salesAdvisor: item.salesAdvisorName
        },
        customerInfo: {
          name: item.customerName,
          idType: 'IC',
          idNumber: `${Math.floor(Math.random() * 900000) + 100000}-${Math.floor(Math.random() * 90) + 10}-${Math.floor(Math.random() * 9000) + 1000}`,
          phone: item.customerPhone,
          email: `customer${Math.floor(Math.random() * 1000)}@example.com`,
          address: `地址${Math.floor(Math.random() * 100) + 1}号`,
          city: '吉隆坡',
          postcode: `${Math.floor(Math.random() * 90000) + 10000}`,
          state: '雪兰莪'
        },
        vehicleInfo: {
          vin: item.vin,
          model: item.vehicleModel,
          color: item.vehicleColor,
          engineNumber: `ENG${Math.floor(Math.random() * 900000) + 100000}`,
          modelCode: `MC${Math.floor(Math.random() * 9000) + 1000}`,
          variant: 'Standard',
          productionYear: '2024',
          manufactureDate: '2024-01-15'
        },
        insuranceInfo: {
          status: item.insuranceStatus,
          company: item.companyName,
          policyNumber: `POL${Math.floor(Math.random() * 900000) + 100000}`,
          period: '1年',
          date: new Date().toISOString().split('T')[0],
          fee: Math.floor(Math.random() * 2000) + 1000
        },
        jpjInfo: {
          status: item.status,
          certificateNumber: item.status === 'success' ? `CERT${Math.floor(Math.random() * 900000) + 100000}` : '',
          pushTime: item.lastPushTime,
          completionTime: item.status === 'success' ? new Date().toISOString() : '',
          operator: '系统自动',
          failureReason: item.status === 'failed' ? '网络连接超时，请重试' : undefined
        },
        operationLogs: [
          {
            operationTime: item.createdAt,
            operationType: '创建订单',
            operatorName: item.salesAdvisorName,
            result: '成功',
            remark: '订单创建成功'
          },
          {
            operationTime: item.lastPushTime,
            operationType: '推送登记',
            operatorName: '系统自动',
            result: item.status === 'success' ? '成功' : '失败',
            remark: item.status === 'success' ? '登记成功' : '登记失败，需要重试'
          }
        ]
      };

      resolve({
        code: '200',
        message: '获取成功',
        result: detail,
        timestamp: Date.now()
      });
    }, 300);
  });
};

// 推送登记Mock
export const pushVehicleRegistrationMock = (
  id: string
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const success = Math.random() > 0.2; // 80%成功率

      if (success) {
        // 更新mock数据状态
        const item = mockData.find(item => item.id === id);
        if (item) {
          item.status = 'processing';
          item.lastPushTime = new Date().toISOString();
        }

        resolve({
          code: '200',
          message: '推送成功',
          result: true,
          timestamp: Date.now()
        });
      } else {
        resolve({
          code: '500',
          message: '推送失败，请重试',
          result: false,
          timestamp: Date.now()
        });
      }
    }, 1000);
  });
};

// 重新推送Mock
export const retryPushVehicleRegistrationMock = (
  id: string
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70%成功率

      if (success) {
        const item = mockData.find(item => item.id === id);
        if (item) {
          item.status = 'success';
          item.lastPushTime = new Date().toISOString();
        }

        resolve({
          code: '200',
          message: '重新推送成功',
          result: true,
          timestamp: Date.now()
        });
      } else {
        resolve({
          code: '500',
          message: '重新推送失败，请重试',
          result: false,
          timestamp: Date.now()
        });
      }
    }, 1000);
  });
};

// 获取销售顾问列表Mock
export const getSalesAdvisorsListMock = (): Promise<ApiResponse<SalesAdvisorOption[]>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const advisors: SalesAdvisorOption[] = [
        { value: 'advisor1', label: '销售顾问1' },
        { value: 'advisor2', label: '销售顾问2' },
        { value: 'advisor3', label: '销售顾问3' },
        { value: 'advisor4', label: '销售顾问4' },
        { value: 'advisor5', label: '销售顾问5' }
      ];

      resolve({
        code: '200',
        message: '获取成功',
        result: advisors,
        timestamp: Date.now()
      });
    }, 200);
  });
};

// 导出数据Mock
export const exportVehicleRegistrationDataMock = (
  params: VehicleRegistrationSearchParams
): Promise<ApiResponse<string>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟导出处理
      resolve({
        code: '200',
        message: '导出成功',
        result: 'export_file_url',
        timestamp: Date.now()
      });
    }, 2000);
  });
};
```

## 8. 组件拆分设计

### 8.1 详情弹窗组件
```vue
<!-- src/views/sales/vehicleRegistration/components/VehicleRegistrationDetailDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    :title="t('registrationDetails')"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <p>{{ t('loading') }}</p>
    </div>

    <div v-else-if="detailData" class="detail-content">
      <!-- 订单基本信息 -->
      <DetailSection
        :title="t('orderBasicInfo')"
        icon="el-icon-document"
      >
        <InfoGrid :data="orderInfoData" />
      </DetailSection>

      <!-- 购车人信息 -->
      <DetailSection
        :title="t('customerInfo')"
        icon="el-icon-user"
      >
        <InfoGrid :data="customerInfoData" />
      </DetailSection>

      <!-- 车辆信息 -->
      <DetailSection
        :title="t('vehicleInfo')"
        icon="el-icon-truck"
      >
        <InfoGrid :data="vehicleInfoData" />
      </DetailSection>

      <!-- 保险信息 -->
      <DetailSection
        :title="t('insuranceInfo')"
        icon="el-icon-shield"
      >
        <InfoGrid :data="insuranceInfoData" />
      </DetailSection>

      <!-- JPJ登记信息 -->
      <DetailSection
        :title="t('jpjRegistrationInfo')"
        icon="el-icon-edit-outline"
      >
        <InfoGrid :data="jpjInfoData" />

        <!-- 重新推送按钮 -->
        <div v-if="showRetryButton" class="retry-section">
          <el-button type="warning" @click="handleRetryPush">
            {{ t('retryPush') }}
          </el-button>
        </div>
      </DetailSection>

      <!-- 操作日志 -->
      <DetailSection
        :title="t('operationLogs')"
        icon="el-icon-time"
      >
        <OperationLogsTable :logs="detailData.operationLogs" />
      </DetailSection>
    </div>

    <template #footer>
      <el-button @click="handleClose">{{ t('close') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getVehicleRegistrationDetail } from '@/api/modules/sales/vehicleRegistration';
import type { VehicleRegistrationDetail } from '@/types/sales/vehicleRegistration';
import DetailSection from './DetailSection.vue';
import InfoGrid from './InfoGrid.vue';
import OperationLogsTable from './OperationLogsTable.vue';

interface Props {
  visible: boolean;
  orderId?: string;
  userRole?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'retry-push', orderId: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  userRole: 'vehicle_registration_officer'
});

const emit = defineEmits<Emits>();

const { t } = useModuleI18n('sales.vehicleRegistration');

const loading = ref(false);
const detailData = ref<VehicleRegistrationDetail | null>(null);

// 计算属性
const showRetryButton = computed(() =>
  detailData.value?.jpjInfo?.status === 'failed' &&
  props.userRole === 'vehicle_registration_officer'
);

const orderInfoData = computed(() => {
  if (!detailData.value) return [];
  return [
    { label: t('orderNumber'), value: detailData.value.orderInfo.orderNumber },
    { label: t('orderStatus'), value: detailData.value.orderInfo.orderStatus },
    { label: t('lastPushTime'), value: detailData.value.orderInfo.lastPushTime },
    { label: t('createdAt'), value: detailData.value.orderInfo.createdAt },
    { label: t('salesAdvisor'), value: detailData.value.orderInfo.salesAdvisor }
  ];
});

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.orderId) {
    loadDetailData();
  } else {
    detailData.value = null;
  }
});

// 加载详情数据
const loadDetailData = async () => {
  if (!props.orderId) return;

  try {
    loading.value = true;
    const response = await getVehicleRegistrationDetail(props.orderId);
    detailData.value = response.result;
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error(t('loadFailed'));
    handleClose();
  } finally {
    loading.value = false;
  }
};

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
};

// 处理重新推送
const handleRetryPush = () => {
  if (props.orderId) {
    emit('retry-push', props.orderId);
  }
};
</script>
```

### 8.2 推送确认弹窗组件
```vue
<!-- src/views/sales/vehicleRegistration/components/PushConfirmDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    :title="t('pushConfirmTitle')"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="push-confirm-content">
      <!-- 确认信息区域 -->
      <div class="confirm-message">
        <el-icon><InfoFilled /></el-icon>
        {{ t('pushConfirmMessage') }}
      </div>

      <!-- 订单信息展示 -->
      <div class="order-info-grid">
        <div class="info-row">
          <div class="info-item">
            <label>{{ t('orderNumber') }}</label>
            <div class="info-value">{{ orderData?.orderNo || '-' }}</div>
          </div>
          <div class="info-item">
            <label>{{ t('customerName') }}</label>
            <div class="info-value">{{ orderData?.customerName || '-' }}</div>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <label>{{ t('vin') }}</label>
            <div class="info-value">{{ orderData?.vin || '-' }}</div>
          </div>
          <div class="info-item">
            <label>{{ t('vehicleModel') }}</label>
            <div class="info-value">{{ orderData?.vehicleModel || '-' }}</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ t('cancel') }}</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          {{ t('confirmPush') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { VehicleRegistrationItem } from '@/types/sales/vehicleRegistration';

interface Props {
  visible: boolean;
  orderData?: VehicleRegistrationItem | null;
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

const { t } = useModuleI18n('sales.vehicleRegistration');

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirm = () => {
  emit('confirm');
};
</script>

<style scoped lang="scss">
.push-confirm-content {
  .confirm-message {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 24px;
    font-size: 14px;
    color: #434343;
    display: flex;
    align-items: flex-start;
    gap: 8px;
  }

  .order-info-grid {
    .info-row {
      display: flex;
      margin-bottom: 16px;
      gap: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-item {
      flex: 1;

      label {
        display: block;
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;
        font-weight: normal;
      }

      .info-value {
        background-color: #f5f5f5;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 14px;
        color: #333;
        min-height: 20px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    min-width: 80px;
  }
}
</style>
```

## 9. 路由配置更新

### 9.1 路由路径调整
```typescript
// src/router/modules/sales.ts

{
  path: '/sales/vehicle-registration',
  name: 'VehicleRegistration',
  component: () => import('@/views/sales/vehicleRegistration/VehicleRegistrationView.vue'),
  meta: {
    title: 'menu.vehicleRegistration',
    requiresAuth: true,
    icon: 'Files',
    roles: ['vehicle_registration_officer', 'sales_advisor', 'manager']
  }
}
```

### 9.2 菜单配置更新
```json
// src/locales/modules/common/zh.json
{
  "menu": {
    "vehicleRegistration": "车辆登记管理"
  }
}
```

## 10. 完整实施步骤

### 10.1 第一阶段：基础结构创建

#### 步骤1：创建目录结构
```bash
# 创建页面目录
mkdir -p src/views/sales/vehicleRegistration/components

# 创建API模块目录
mkdir -p src/api/modules/sales

# 创建类型定义目录
mkdir -p src/types/sales

# 创建Mock数据目录
mkdir -p src/mock/data/sales
```

#### 步骤2：创建类型定义文件
```typescript
// 创建 src/types/sales/vehicleRegistration.d.ts
// 内容参考第6节的完整类型定义
```

#### 步骤3：创建Mock数据文件
```typescript
// 创建 src/mock/data/sales/vehicleRegistration.ts
// 内容参考第7节的Mock数据实现
```

#### 步骤4：创建API模块文件
```typescript
// 创建 src/api/modules/sales/vehicleRegistration.ts
import request from '@/api';
import type {
  VehicleRegistrationSearchParams,
  VehicleRegistrationPageResponse,
  VehicleRegistrationDetail,
  ApiResponse,
  SalesAdvisorOption
} from '@/types/sales/vehicleRegistration';
import {
  getVehicleRegistrationListMock,
  getVehicleRegistrationDetailMock,
  pushVehicleRegistrationMock,
  retryPushVehicleRegistrationMock,
  getSalesAdvisorsListMock,
  exportVehicleRegistrationDataMock
} from '@/mock/data/sales/vehicleRegistration';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getVehicleRegistrationList = (
  params: VehicleRegistrationSearchParams
): Promise<ApiResponse<VehicleRegistrationPageResponse>> => {
  if (USE_MOCK_API) {
    return getVehicleRegistrationListMock(params);
  } else {
    return request.get<any, ApiResponse<VehicleRegistrationPageResponse>>(
      '/sales/vehicle-registration/list',
      { params }
    );
  }
};

export const getVehicleRegistrationDetail = (
  id: string
): Promise<ApiResponse<VehicleRegistrationDetail>> => {
  if (USE_MOCK_API) {
    return getVehicleRegistrationDetailMock(id);
  } else {
    return request.get<any, ApiResponse<VehicleRegistrationDetail>>(
      `/sales/vehicle-registration/detail/${id}`
    );
  }
};

export const pushVehicleRegistration = (
  id: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return pushVehicleRegistrationMock(id);
  } else {
    return request.post<any, ApiResponse<boolean>>(
      `/sales/vehicle-registration/push/${id}`
    );
  }
};

export const retryPushVehicleRegistration = (
  id: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return retryPushVehicleRegistrationMock(id);
  } else {
    return request.post<any, ApiResponse<boolean>>(
      `/sales/vehicle-registration/retry-push/${id}`
    );
  }
};

export const getSalesAdvisorsList = (): Promise<ApiResponse<SalesAdvisorOption[]>> => {
  if (USE_MOCK_API) {
    return getSalesAdvisorsListMock();
  } else {
    return request.get<any, ApiResponse<SalesAdvisorOption[]>>(
      '/sales/advisors/list'
    );
  }
};

export const exportVehicleRegistrationData = (
  params: VehicleRegistrationSearchParams
): Promise<ApiResponse<string>> => {
  if (USE_MOCK_API) {
    return exportVehicleRegistrationDataMock(params);
  } else {
    return request.post<any, ApiResponse<string>>(
      '/sales/vehicle-registration/export',
      params
    );
  }
};
```

### 10.2 第二阶段：组件重构

#### 步骤5：创建子组件
```typescript
// 创建以下组件文件：
// - src/views/sales/vehicleRegistration/components/VehicleRegistrationDetailDialog.vue
// - src/views/sales/vehicleRegistration/components/PushConfirmDialog.vue
// - src/views/sales/vehicleRegistration/components/RetryPushDialog.vue
// - src/views/sales/vehicleRegistration/components/DetailSection.vue
// - src/views/sales/vehicleRegistration/components/InfoGrid.vue
// - src/views/sales/vehicleRegistration/components/OperationLogsTable.vue
```

#### 步骤6：重构主页面文件
```vue
<!-- src/views/sales/vehicleRegistration/VehicleRegistrationView.vue -->
<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <!-- 第一行筛选字段 -->
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('orderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerName')">
              <el-input
                v-model="searchParams.customerName"
                :placeholder="t('customerNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerPhone')">
              <el-input
                v-model="searchParams.customerPhone"
                :placeholder="t('customerPhonePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('registrationStatus')">
              <el-select
                v-model="searchParams.registrationStatus"
                :placeholder="tc('all')"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in registrationStatusOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行筛选字段 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('vin')">
              <el-input
                v-model="searchParams.vin"
                :placeholder="t('vinPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('insuranceStatus')">
              <el-select
                v-model="searchParams.insuranceStatus"
                :placeholder="tc('all')"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in insuranceStatusOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('pushTimeRange')">
              <el-date-picker
                v-model="pushTimeRange"
                type="datetimerange"
                :placeholder="t('pushTimeRange')"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesAdvisor')">
              <el-select
                v-model="searchParams.salesAdvisor"
                :placeholder="tc('all')"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in salesAdvisorOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 按钮区域 -->
        <el-row :gutter="20">
          <el-col :span="24" class="buttons-col">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ tc('search') }}
            </el-button>
            <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20 operation-card">
      <el-button :icon="Download" @click="handleExport">{{ t('export') }}</el-button>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        :element-loading-text="tc('loading')"
      >
        <el-table-column :label="tc('index')" type="index" width="80" align="center" />
        <el-table-column :label="t('orderNumber')" prop="orderNo" min-width="120" />
        <el-table-column :label="t('customerName')" prop="customerName" min-width="100">
          <template #default="{ row }">
            {{ maskCustomerName(row.customerName) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('customerPhone')" prop="customerPhone" min-width="120">
          <template #default="{ row }">
            {{ maskPhoneNumber(row.customerPhone) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vin')" prop="vin" min-width="140" />
        <el-table-column :label="t('vehicleModel')" prop="vehicleModel" min-width="100" />
        <el-table-column :label="t('vehicleColor')" prop="vehicleColor" min-width="80" />
        <el-table-column :label="t('insuranceStatus')" prop="insuranceStatus" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.insuranceStatus, DICTIONARY_TYPES.INSURANCE_STATUS)">
              {{ formatInsuranceStatus(row.insuranceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('companyName')" prop="companyName" min-width="120" />
        <el-table-column :label="t('status')" prop="status" min-width="110">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status, DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS)">
              {{ formatRegistrationStatus(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('lastPushTime')" prop="lastPushTime" min-width="140" />
        <el-table-column :label="t('registrationFee')" prop="registrationFee" min-width="120" align="right">
          <template #default="{ row }">
            <span v-if="row.registrationFee > 0" class="fee-amount">
              {{ t('feeAmount', { amount: formatAmount(row.registrationFee) }) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('salesAdvisorName')" prop="salesAdvisorName" min-width="100" />
        <el-table-column :label="tc('actions')" width="180" fixed="right" align="center">
          <template #default="{ row }">
            <!-- 待登记状态 -->
            <template v-if="row.status === 'pending'">
              <el-button
                v-if="userRole === 'vehicle_registration_officer'"
                type="primary"
                link
                @click="handlePushRegistration(row)"
              >
                {{ t('pushRegistration') }}
              </el-button>
            </template>
            <!-- 登记失败状态 -->
            <template v-if="row.status === 'failed'">
              <el-button
                v-if="userRole === 'vehicle_registration_officer'"
                type="warning"
                link
                @click="handleRetryPush(row)"
                style="margin-right: 8px;"
              >
                {{ t('retryPush') }}
              </el-button>
            </template>
            <!-- 所有状态都显示查看详情 -->
            <el-button type="success" link @click="handleViewDetails(row)">
              {{ tc('detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 弹窗组件 -->
    <PushConfirmDialog
      v-model:visible="pushDialogVisible"
      :order-data="selectedOrder"
      :loading="pushLoading"
      @confirm="confirmPushRegistration"
    />

    <RetryPushDialog
      v-model:visible="retryDialogVisible"
      :order-data="selectedOrder"
      :detail-data="selectedOrderDetail"
      :loading="retryLoading"
      @confirm="confirmRetryPush"
    />

    <VehicleRegistrationDetailDialog
      v-model:visible="detailDialogVisible"
      :order-id="selectedOrder?.id"
      :user-role="userRole"
      @retry-push="handleRetryPush"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { ElMessage } from 'element-plus';
import { Search, Download } from '@element-plus/icons-vue';
import type {
  VehicleRegistrationSearchParams,
  VehicleRegistrationItem,
  VehicleRegistrationDetail,
  UserRole
} from '@/types/sales/vehicleRegistration';
import {
  getVehicleRegistrationList,
  pushVehicleRegistration,
  retryPushVehicleRegistration,
  getSalesAdvisorsList,
  exportVehicleRegistrationData
} from '@/api/modules/sales/vehicleRegistration';
import PushConfirmDialog from './components/PushConfirmDialog.vue';
import RetryPushDialog from './components/RetryPushDialog.vue';
import VehicleRegistrationDetailDialog from './components/VehicleRegistrationDetailDialog.vue';

// 使用国际化
const { t, tc } = useModuleI18n('sales.vehicleRegistration');

// 使用字典数据
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.SALES_ADVISOR
]);

// 计算属性获取字典选项
const registrationStatusOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS)
);
const insuranceStatusOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.INSURANCE_STATUS)
);
const salesAdvisorOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.SALES_ADVISOR)
);

// 数据定义
const loading = ref(false);
const tableData = ref<VehicleRegistrationItem[]>([]);

// 用户角色 (实际项目中应该从用户状态管理中获取)
const userRole = ref<UserRole>('vehicle_registration_officer');

// 搜索参数（修正为MyBatisPlus标准）
const searchParams = reactive<VehicleRegistrationSearchParams>({
  orderNumber: '',
  customerName: '',
  customerPhone: '',
  registrationStatus: '',
  vin: '',
  insuranceStatus: '',
  salesAdvisor: ''
});

// 推送时间范围
const pushTimeRange = ref<[string, string] | null>(null);

// 分页参数（修正为MyBatisPlus标准）
const pagination = reactive({
  pageNum: 1,      // ✅ 修正为pageNum
  pageSize: 20,
  total: 0
});

// 弹窗状态
const pushDialogVisible = ref(false);
const retryDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const pushLoading = ref(false);
const retryLoading = ref(false);

// 选中的订单
const selectedOrder = ref<VehicleRegistrationItem | null>(null);
const selectedOrderDetail = ref<VehicleRegistrationDetail | null>(null);

// 状态标签样式映射
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS]: {
    'pending': 'primary',
    'processing': 'warning',
    'success': 'success',
    'failed': 'danger'
  },
  [DICTIONARY_TYPES.INSURANCE_STATUS]: {
    'insured': 'success',
    'not_insured': 'info'
  }
};

// 字典转义函数
const formatRegistrationStatus = (status: string) =>
  getNameByCode(DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS, status) || status;

const formatInsuranceStatus = (status: string) =>
  getNameByCode(DICTIONARY_TYPES.INSURANCE_STATUS, status) || status;

const getStatusTagType = (status: string, dictionaryType: string) =>
  STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info';

// 页面初始化
onMounted(() => {
  loadData();
});

// 构建搜索参数
const buildSearchParams = (): VehicleRegistrationSearchParams => {
  const params: VehicleRegistrationSearchParams = {
    pageNum: pagination.pageNum,      // ✅ 使用标准参数名
    pageSize: pagination.pageSize,
    ...searchParams
  };

  // 处理时间范围
  if (pushTimeRange.value) {
    params.pushTimeStart = pushTimeRange.value[0];
    params.pushTimeEnd = pushTimeRange.value[1];
  }

  return filterEmptyParams(params);
};

// 过滤空值参数
const filterEmptyParams = (params: VehicleRegistrationSearchParams): VehicleRegistrationSearchParams => {
  const filteredParams: VehicleRegistrationSearchParams = {
    pageNum: params.pageNum,
    pageSize: params.pageSize
  };

  // 只保留非空值
  Object.keys(params).forEach(key => {
    const value = params[key as keyof VehicleRegistrationSearchParams];
    if (value && String(value).trim() !== '' && key !== 'pageNum' && key !== 'pageSize') {
      (filteredParams as any)[key] = value;
    }
  });

  return filteredParams;
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;

    const params = buildSearchParams();
    const response = await getVehicleRegistrationList(params);

    // 标准API响应处理
    if (response.code === '200' || response.code === 200) {
      tableData.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pageNum = response.result.pageNum || pagination.pageNum;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取车辆登记列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    orderNumber: '',
    customerName: '',
    customerPhone: '',
    registrationStatus: '',
    vin: '',
    insuranceStatus: '',
    salesAdvisor: ''
  });
  pushTimeRange.value = null;
  pagination.pageNum = 1;
  loadData();
};

// 分页处理（修正为MyBatisPlus标准）
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;  // ✅ 修正为pageNum
  loadData();
};

// 推送登记
const handlePushRegistration = (row: VehicleRegistrationItem) => {
  selectedOrder.value = row;
  pushDialogVisible.value = true;
};

// 确认推送登记
const confirmPushRegistration = async () => {
  if (!selectedOrder.value) return;

  try {
    pushLoading.value = true;
    const response = await pushVehicleRegistration(selectedOrder.value.id);

    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('pushSuccess'));
      pushDialogVisible.value = false;
      loadData(); // 重新加载数据
    } else {
      throw new Error(response.message || t('pushFailed'));
    }
  } catch (error) {
    console.error('推送失败:', error);
    ElMessage.error(t('pushFailed'));
  } finally {
    pushLoading.value = false;
  }
};

// 重新推送
const handleRetryPush = (row: VehicleRegistrationItem) => {
  selectedOrder.value = row;
  retryDialogVisible.value = true;
};

// 确认重新推送
const confirmRetryPush = async () => {
  if (!selectedOrder.value) return;

  try {
    retryLoading.value = true;
    const response = await retryPushVehicleRegistration(selectedOrder.value.id);

    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('retryPushSuccess'));
      retryDialogVisible.value = false;
      detailDialogVisible.value = false; // 关闭详情弹窗
      loadData(); // 重新加载数据
    } else {
      throw new Error(response.message || t('retryPushFailed'));
    }
  } catch (error) {
    console.error('重新推送失败:', error);
    ElMessage.error(t('retryPushFailed'));
  } finally {
    retryLoading.value = false;
  }
};

// 查看详情
const handleViewDetails = (row: VehicleRegistrationItem) => {
  selectedOrder.value = row;
  detailDialogVisible.value = true;
};

// 导出数据
const handleExport = async () => {
  try {
    const params = buildSearchParams();
    const response = await exportVehicleRegistrationData(params);

    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('exportSuccess'));
    } else {
      throw new Error(response.message || t('exportFailed'));
    }
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('exportFailed'));
  }
};

// 工具方法
const maskCustomerName = (name: string): string => {
  if (!name) return '';
  return name.charAt(0) + '*'.repeat(name.length - 1);
};

const maskPhoneNumber = (phone: string): string => {
  if (!phone || phone.length < 7) return phone;
  return phone.slice(0, 3) + '****' + phone.slice(-4);
};

const formatAmount = (amount: number): string => {
  return amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card, .operation-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.fee-amount {
  color: #67c23a;
  font-weight: bold;
}
</style>
```

### 10.3 第三阶段：配置更新

#### 步骤7：更新路由配置
```typescript
// 更新 src/router/modules/sales.ts
// 添加车辆登记路由配置
```

#### 步骤8：更新国际化文件
```json
// 更新 src/locales/modules/sales/zh.json
// 添加车辆登记相关翻译
```

#### 步骤9：更新字典常量
```typescript
// 更新 src/constants/dictionary.ts
// 添加车辆登记相关字典类型
```

### 10.4 第四阶段：测试验证

#### 步骤10：功能测试
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作（使用pageNum参数）
- [ ] 字典数据正常显示和转义
- [ ] 推送和重新推送功能正常
- [ ] 详情弹窗正常显示
- [ ] 导出功能正常
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

#### 步骤11：代码质量检查
- [ ] TypeScript类型安全，无编译错误
- [ ] ESLint检查通过
- [ ] 代码格式化正确
- [ ] 组件拆分合理
- [ ] API响应处理符合规范

#### 步骤12：性能优化验证
- [ ] 字典数据缓存正常
- [ ] 分页加载性能良好
- [ ] 组件懒加载正常
- [ ] Mock数据切换正常

---

**本设计文档为车辆登记页面重构提供完整的技术方案和实施指导，确保重构后的代码符合项目规范并具备良好的可维护性。**
