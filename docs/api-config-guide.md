# API配置和Mock切换指南

## 🎯 问题描述

当从Mock API切换到真实API时，可能遇到token存储失败等问题。本文档提供完整的解决方案。

## 🔧 环境变量配置

请在项目根目录创建以下环境变量文件：

### 1. `.env` (基础配置)
```bash
# 应用标题
VITE_APP_TITLE=DMS 经销商管理系统

# 是否使用Mock API (true/false)
# 设置为true时使用Mock数据，设置为false时调用真实API
VITE_APP_USE_MOCK_API=true

# 真实API基础地址
# 当VITE_APP_USE_MOCK_API=false时，会调用这个地址的API
VITE_APP_BASE_API=http://localhost:8080/api/v1

# 开发环境配置
NODE_ENV=development
```

### 2. `.env.development` (开发环境)
```bash
# 开发环境配置
VITE_APP_TITLE=DMS 经销商管理系统 (开发版)

# 开发环境默认使用Mock API
VITE_APP_USE_MOCK_API=true

# 开发环境API地址
VITE_APP_BASE_API=http://localhost:8080/api/v1
```

### 3. `.env.production` (生产环境)
```bash
# 生产环境配置
VITE_APP_TITLE=DMS 经销商管理系统

# 生产环境禁用Mock API
VITE_APP_USE_MOCK_API=false

# 生产环境API地址 (请根据实际情况修改)
VITE_APP_BASE_API=https://your-api-domain.com/api/v1
```

## 🚀 快速切换方法

### 方法1：修改环境变量
```bash
# 使用Mock API
VITE_APP_USE_MOCK_API=true

# 使用真实API
VITE_APP_USE_MOCK_API=false
```

### 方法2：命令行指定
```bash
# 强制使用Mock API启动
VITE_APP_USE_MOCK_API=true npm run dev

# 强制使用真实API启动
VITE_APP_USE_MOCK_API=false npm run dev
```

## 🔍 问题诊断和解决

### 1. Token存储失败问题

**现象：** 登录显示成功，但localStorage中token为"undefined"

**原因：** 真实API和Mock API的响应数据格式不一致

**解决方案：** 已经修复了`src/api/modules/auth.ts`，添加了数据格式处理

### 2. API响应格式问题

**Mock API格式：**
```javascript
{
  code: '200',
  message: '登录成功',
  result: {
    token: 'xxx',
    refreshToken: 'xxx',
    userInfo: { ... }
  },
  timestamp: 1234567890
}
```

**真实API可能的格式：**
```javascript
// 格式1：直接返回数据
{
  token: 'xxx',
  refreshToken: 'xxx',
  userInfo: { ... }
}

// 格式2：包装格式（推荐）
{
  code: 200,
  message: 'success',
  data: {
    token: 'xxx',
    refreshToken: 'xxx',
    userInfo: { ... }
  }
}
```

### 3. 后端API接口要求

为了保证前端正常工作，后端API应该返回以下格式：

#### 登录接口 `POST /auth/login`

**请求：**
```javascript
{
  "username": "admin",
  "password": "123456"
}
```

**响应：**
```javascript
{
  "code": 200,           // 或 "200"
  "message": "登录成功",
  "result": {            // 或使用 "data"
    "token": "jwt_token_here",
    "refreshToken": "refresh_token_here",
    "userInfo": {
      "id": "1",
      "username": "admin",
      "realName": "管理员",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "avatar": "",
      "roles": ["admin"],
      "permissions": ["*:*:*"]
    }
  },
  "timestamp": 1234567890
}
```

#### 获取用户信息接口 `POST /auth/user-info`

**请求头：**
```
Authorization: Bearer {token}
```

**响应：**
```javascript
{
  "code": 200,
  "message": "获取用户信息成功",
  "result": {
    "id": "1",
    "username": "admin",
    "realName": "管理员",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "",
    "roles": ["admin"],
    "permissions": ["*:*:*"]
  },
  "timestamp": 1234567890
}
```

## 🛠️ 调试技巧

### 1. 开启详细日志
已经在代码中添加了详细的调试日志：

```bash
# 在浏览器控制台查看以下日志
🔐 Mock登录API被调用         # Mock模式日志
🌐 调用真实登录API           # 真实API模式日志
📩 API Response             # HTTP响应日志
🚀 API Request             # HTTP请求日志
```

### 2. 检查网络请求
1. 打开浏览器开发者工具
2. 切换到Network面板
3. 执行登录操作
4. 查看HTTP请求和响应详情

### 3. 检查localStorage
在浏览器控制台执行：
```javascript
// 查看存储的token
console.log('Token:', localStorage.getItem('token'))
console.log('RefreshToken:', localStorage.getItem('refreshToken'))

// 清除token（如果需要）
localStorage.removeItem('token')
localStorage.removeItem('refreshToken')
```

## 📋 故障排除清单

- [ ] 确认环境变量配置正确
- [ ] 检查API服务器是否运行
- [ ] 验证API接口地址是否正确
- [ ] 确认API响应数据格式
- [ ] 查看浏览器控制台错误信息
- [ ] 检查Network面板HTTP状态码
- [ ] 验证token存储是否成功

## 🎉 最佳实践

1. **开发阶段**：使用Mock API进行前端开发
2. **联调阶段**：切换到真实API进行前后端联调
3. **测试阶段**：使用真实API进行完整测试
4. **生产部署**：确保使用真实API且配置正确

通过以上配置和解决方案，您可以顺利在Mock API和真实API之间切换，确保登录功能正常工作。 