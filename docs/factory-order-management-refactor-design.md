# FactoryOrderManagementView.vue 重构设计文档

> **重要说明**: 本设计文档已根据 `/规范/页面目录结构规范.md` 进行审查和修正，确保完全符合DMS前端项目的目录结构规范。
> 
> **模块归属说明**: 工厂订单管理功能归属于 `sales` 模块，因为工厂订单是销售业务流程的核心环节，包括订单状态管理、支付状态跟踪等都直接服务于销售管理。

## 1. 重构目标与范围

### 1.1 重构目标
将 `src/views/FactoryOrderManagementView.vue` 按照DMS前端重构技术规范进行模块化重构，实现：
- 符合目录结构规范的模块化组织
- 统一的MyBatisPlus分页组件标准化
- 规范的数据字典实现方式
- 标准的API请求响应处理

### 1.2 重构范围
- **源文件**: `src/views/FactoryOrderManagementView.vue`
- **目标模块**: `sales` (销售模块 - 工厂订单管理属于销售业务范畴)
- **功能名称**: `factoryOrderManagement` (工厂订单管理)

## 2. 现状分析

### 2.1 当前文件结构问题
```
❌ 当前结构:
src/views/FactoryOrderManagementView.vue  # 不符合模块化规范

✅ 目标结构:
src/views/sales/factoryOrderManagement/
├── FactoryOrderManagementView.vue        # 主页面文件（路由页面）
└── components/
    └── FactoryOrderDetailDialog.vue      # 详情对话框（非路由页面）
src/api/modules/sales/
└── factoryOrderManagement.ts             # API模块
src/types/sales/
└── factoryOrderManagement.d.ts           # 类型定义
src/mock/data/sales/
└── factoryOrderManagement.ts             # Mock数据
```

### 2.2 MyBatisPlus分页问题分析
**当前实现问题**:
```typescript
// ❌ 分页参数不符合MyBatisPlus标准
const pagination = reactive({
  page: 1,              // ❌ 应为 pageNum
  pageSize: 10,         // ✅ 正确
  total: 0              // ✅ 正确
});

// ❌ API调用参数不一致
const params = {
  ...searchParams,
  pageNum: pagination.page,    // ❌ 应为 pageNum: pagination.pageNum
  pageSize: pagination.pageSize
}

// ❌ 分页组件绑定不符合标准
<el-pagination
  v-model:current-page="pagination.page"      // ❌ 应绑定 pageNum
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
/>

// ✅ 响应处理正确
orderList.value = response.result.records    // ✅ 正确使用 records
pagination.total = response.result.total     // ✅ 正确使用 total
```

**目标实现**:
```typescript
// ✅ 标准MyBatisPlus分页参数
interface FactoryOrderSearchParams {
  pageNum?: number;     // MyBatisPlus标准参数
  pageSize?: number;    // MyBatisPlus标准参数
  // ... 其他搜索参数
}

// ✅ 标准分页状态
const pagination = reactive({
  pageNum: 1,           // 修正为 pageNum
  pageSize: 10,
  total: 0
});

// ✅ 标准分页组件绑定
<el-pagination
  v-model:current-page="pagination.pageNum"
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
/>
```

### 2.3 数据字典实现问题分析
**当前实现问题**:
```typescript
// ❌ 硬编码选项数据
<el-select v-model="searchParams.dealerName">
  <el-option label="吉隆坡中央店" value="吉隆坡中央店" />
  <el-option label="槟城旗舰店" value="槟城旗舰店" />
  // ...
</el-select>

<el-select v-model="searchParams.model">
  <el-option label="Model A" value="Model A" />
  <el-option label="Model B" value="Model B" />
  // ...
</el-select>

// ❌ 部分使用字典但不完整
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS
]);

// ❌ 缺少其他字典类型的标准化处理
const getLoanStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '已通过': 'success',
    '审批中': 'warning',
    // ... 硬编码状态映射
  }
}
```

**目标实现** (参考ProspectsView.vue):
```typescript
// ✅ 使用标准数据字典
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.LOAN_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS
]);

// ✅ 标准字典转义
<el-table-column prop="orderStatus" label="订单状态">
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.orderStatus)">
      {{ getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
    </el-tag>
  </template>
</el-table-column>
```

### 2.4 API响应处理分析
**当前实现**:
```typescript
// ✅ 正确的响应处理
const response = await getFactoryOrderList(params)
orderList.value = response.result.records    // ✅ 正确使用 result.records
pagination.total = response.result.total     // ✅ 正确使用 result.total

const data = await getOrderStatistics()
statistics.value = data.result               // ✅ 正确使用 result
```

**评估结果**: 当前API响应处理已符合标准，使用了`response.result`模式。

## 3. 重构技术方案

### 3.1 目录结构重构

#### 3.1.1 创建目标目录结构
```bash
# 创建模块化目录
mkdir -p src/views/sales/factoryOrderManagement/components
mkdir -p src/api/modules/sales
mkdir -p src/types/sales
mkdir -p src/mock/data/sales
```

#### 3.1.2 文件迁移映射
```
源文件 → 目标文件:
src/views/FactoryOrderManagementView.vue
→ src/views/sales/factoryOrderManagement/FactoryOrderManagementView.vue

src/views/components/FactoryOrderDetailDialog.vue
→ src/views/sales/factoryOrderManagement/components/FactoryOrderDetailDialog.vue

新增文件:
src/api/modules/sales/factoryOrderManagement.ts    # API模块
src/types/sales/factoryOrderManagement.d.ts        # 类型定义
src/mock/data/sales/factoryOrderManagement.ts      # Mock数据
```

### 3.2 MyBatisPlus分页标准化实现

#### 3.2.1 分页参数规范修正
```typescript
// 修正分页搜索参数接口
export interface FactoryOrderSearchParams {
  pageNum?: number;        // ✅ 标准MyBatisPlus参数
  pageSize?: number;       // ✅ 标准MyBatisPlus参数
  dealerName?: string;
  model?: string;
  variant?: string;
  orderStatus?: string;
  paymentStatus?: string;
  orderNumber?: string;
  orderDateStart?: string;
  orderDateEnd?: string;
}
```

#### 3.2.2 分页响应结构标准化
```typescript
// 标准MyBatisPlus分页响应
export interface FactoryOrderPageResponse {
  result: {
    records: FactoryOrderListItem[];    // ✅ MyBatisPlus标准响应
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
  };
}
```

#### 3.2.3 分页组件配置修正
```vue
<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="pagination.pageNum"    <!-- ✅ 修正为pageNum -->
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
// 分页状态修正
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 10,
  total: 0
});

// 分页处理函数修正
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  loadOrderList();
};

const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;  // ✅ 修正为pageNum
  loadOrderList();
};

// API调用参数修正
const loadOrderList = async () => {
  const params = {
    ...searchParams,
    pageNum: pagination.pageNum,     // ✅ 修正为pageNum
    pageSize: pagination.pageSize
  };
  
  const response = await getFactoryOrderList(params);
  orderList.value = response.result.records;
  pagination.total = response.result.total;
};
</script>
```

### 3.3 数据字典标准化实现

#### 3.3.1 字典类型定义
```typescript
// src/constants/dictionary.ts 中添加工厂订单相关字典
export const DICTIONARY_TYPES = {
  // ... 现有字典类型
  STORE: '01010',                    // 门店
  VEHICLE_MODEL: '01020',            // 车型
  VEHICLE_VARIANT: '01030',          // 配置
  ORDER_STATUS: '01040',             // 订单状态
  APPROVAL_STATUS: '01050',          // 审批状态
  PAYMENT_STATUS: '01060',           // 支付状态
  LOAN_STATUS: '01070',              // 贷款状态
  INSURANCE_STATUS: '01080',         // 保险状态
  JPJ_REGISTRATION_STATUS: '01090'   // JPJ注册状态
} as const;
```

#### 3.3.2 字典使用标准化
```vue
<template>
  <!-- 搜索表单字典化 -->
  <el-form :model="searchParams" inline>
    <el-form-item label="门店">
      <el-select v-model="searchParams.dealerName" clearable>
        <el-option
          v-for="option in storeOptions"
          :key="option.code"
          :label="option.name"
          :value="option.code"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="车型">
      <el-select v-model="searchParams.model" clearable>
        <el-option
          v-for="option in modelOptions"
          :key="option.code"
          :label="option.name"
          :value="option.code"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="订单状态">
      <el-select v-model="searchParams.orderStatus" clearable>
        <el-option
          v-for="option in orderStatusOptions"
          :key="option.code"
          :label="option.name"
          :value="option.code"
        />
      </el-select>
    </el-form-item>
  </el-form>

  <!-- 表格列字典化 -->
  <el-table :data="orderList">
    <el-table-column prop="dealerName" label="门店">
      <template #default="{ row }">
        {{ getNameByCode(DICTIONARY_TYPES.STORE, row.dealerName) }}
      </template>
    </el-table-column>

    <el-table-column prop="model" label="车型">
      <template #default="{ row }">
        {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_MODEL, row.model) }}
      </template>
    </el-table-column>

    <el-table-column prop="orderStatus" label="订单状态">
      <template #default="{ row }">
        <el-tag :type="getOrderStatusTagType(row.orderStatus)">
          {{ getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
        </el-tag>
      </template>
    </el-table-column>

    <el-table-column prop="paymentStatus" label="支付状态">
      <template #default="{ row }">
        <el-tag :type="getPaymentStatusTagType(row.paymentStatus)">
          {{ getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, row.paymentStatus) }}
        </el-tag>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 批量获取字典数据
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.VEHICLE_VARIANT,
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.LOAN_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS
]);

// 计算属性获取字典选项
const storeOptions = computed(() => getOptions(DICTIONARY_TYPES.STORE));
const modelOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_MODEL));
const variantOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_VARIANT));
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS));
const approvalStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_STATUS));
const paymentStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PAYMENT_STATUS));
const loanStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.LOAN_STATUS));
const insuranceStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.INSURANCE_STATUS));
const jpjStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS));

// 标签类型映射函数
const getOrderStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01040001': 'info',     // 待确认
    '01040002': 'warning',  // 已确认
    '01040003': 'success',  // 已完成
    '01040004': 'danger'    // 已取消
  };
  return typeMap[status] || 'info';
};

const getPaymentStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01060001': 'warning',  // 待支付
    '01060002': 'info',     // 部分支付
    '01060003': 'success',  // 已支付
    '01060004': 'danger'    // 支付失败
  };
  return typeMap[status] || 'info';
};

const getApprovalStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01050001': 'warning',  // 待审批
    '01050002': 'success',  // 已通过
    '01050003': 'danger'    // 已拒绝
  };
  return typeMap[status] || 'info';
};

const getLoanStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01070001': 'warning',  // 申请中
    '01070002': 'success',  // 已通过
    '01070003': 'danger'    // 已拒绝
  };
  return typeMap[status] || 'info';
};
</script>
```

### 3.4 API模块重构

#### 3.4.1 API模块实现
```typescript
// src/api/modules/sales/factoryOrderManagement.ts
import request from '@/api';
import type {
  FactoryOrderSearchParams,
  FactoryOrderPageResponse,
  FactoryOrderDetail,
  FactoryOrderStatistics
} from '@/types/sales/factoryOrderManagement';
import {
  getFactoryOrderList as getMockFactoryOrderList,
  getFactoryOrderDetail as getMockFactoryOrderDetail,
  getFactoryOrderStatistics as getMockFactoryOrderStatistics
} from '@/mock/data/sales/factoryOrderManagement';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getFactoryOrderList = (params: FactoryOrderSearchParams): Promise<FactoryOrderPageResponse> => {
  if (USE_MOCK_API) {
    return getMockFactoryOrderList(params);
  } else {
    return request.post<any, FactoryOrderPageResponse>('/sales/factory-order/list', params);
  }
};

export const getFactoryOrderDetail = (orderNumber: string): Promise<{ result: FactoryOrderDetail }> => {
  if (USE_MOCK_API) {
    return getMockFactoryOrderDetail(orderNumber);
  } else {
    return request.get<any, { result: FactoryOrderDetail }>(`/sales/factory-order/detail/${orderNumber}`);
  }
};

export const getFactoryOrderStatistics = (): Promise<{ result: FactoryOrderStatistics }> => {
  if (USE_MOCK_API) {
    return getMockFactoryOrderStatistics();
  } else {
    return request.get<any, { result: FactoryOrderStatistics }>('/sales/factory-order/statistics');
  }
};

export const exportFactoryOrderList = (params: FactoryOrderSearchParams): Promise<Blob> => {
  return request.post<any, Blob>('/sales/factory-order/export', params, { responseType: 'blob' });
};
```

### 3.5 国际化文件结构

#### 3.5.1 国际化文件结构
```json
// src/locales/modules/sales/zh.json
{
  "factoryOrderManagement": {
    "title": "工厂订单管理",
    "search": {
      "dealerName": "门店",
      "model": "车型",
      "variant": "配置",
      "orderStatus": "订单状态",
      "paymentStatus": "支付状态",
      "orderNumber": "订单号",
      "orderDate": "订单日期",
      "search": "搜索",
      "reset": "重置",
      "export": "导出"
    },
    "table": {
      "orderNumber": "订单号",
      "dealerName": "门店",
      "model": "车型",
      "variant": "配置",
      "orderStatus": "订单状态",
      "approvalStatus": "审批状态",
      "paymentStatus": "支付状态",
      "loanStatus": "贷款状态",
      "insuranceStatus": "保险状态",
      "jpjStatus": "JPJ注册状态",
      "orderDate": "订单日期",
      "actions": "操作",
      "viewDetail": "查看详情"
    },
    "statistics": {
      "totalOrders": "总订单数",
      "pendingOrders": "待处理订单",
      "completedOrders": "已完成订单",
      "cancelledOrders": "已取消订单"
    }
  }
}
```

#### 3.5.2 国际化引用更新
```typescript
// 更新国际化引用
const { t, tc } = useModuleI18n('sales.factoryOrderManagement');
```

### 3.6 路由配置更新

```typescript
// src/router/modules/sales.ts
{
  path: '/sales/factory-order-management',
  name: 'SalesFactoryOrderManagement',
  component: () => import('@/views/sales/factoryOrderManagement/FactoryOrderManagementView.vue'),
  meta: {
    title: 'menu.salesFactoryOrderManagement',
    requiresAuth: true,
    icon: 'Document'
  }
}
```

## 4. 实施计划

### 4.1 重构步骤

#### 4.1.1 第一阶段：目录结构迁移
1. 创建目标目录结构
2. 迁移主页面文件
3. 创建组件目录和文件
4. 更新路由配置

#### 4.1.2 第二阶段：MyBatisPlus分页标准化
1. 修正分页参数接口定义
2. 更新分页状态管理
3. 修正分页组件绑定
4. 测试分页功能

#### 4.1.3 第三阶段：数据字典标准化
1. 定义字典类型常量
2. 实现字典批量获取
3. 更新搜索表单字典化
4. 更新表格列字典化
5. 实现标签类型映射

#### 4.1.4 第四阶段：API模块重构
1. 创建API模块文件
2. 实现Mock数据
3. 更新API调用
4. 测试API功能

### 4.2 验收标准

#### 4.2.1 目录结构验证
- [ ] 页面文件移动到 `src/views/sales/factoryOrderManagement/FactoryOrderManagementView.vue`
- [ ] API模块创建在 `src/api/modules/sales/factoryOrderManagement.ts`
- [ ] Mock数据创建在 `src/mock/data/sales/factoryOrderManagement.ts`
- [ ] 类型定义创建在 `src/types/sales/factoryOrderManagement.d.ts`
- [ ] 国际化文件更新在 `src/locales/modules/sales/`

#### 4.2.2 MyBatisPlus分页验证
- [ ] 分页参数使用 `pageNum` 和 `pageSize`
- [ ] 分页组件绑定 `pagination.pageNum`
- [ ] API响应使用 `response.result.records` 和 `response.result.total`
- [ ] 分页功能正常工作

#### 4.2.3 数据字典验证
- [ ] 所有下拉选项使用字典数据
- [ ] 表格列显示使用字典转义
- [ ] 标签类型映射正确
- [ ] 字典数据加载正常

#### 4.2.4 API响应处理验证
- [ ] 使用 `response.result` 获取数据
- [ ] 错误处理符合规范
- [ ] Mock数据和真实API切换正常

## 5. 类型定义文件

### 5.1 类型定义文件
```typescript
// src/types/sales/factoryOrderManagement.d.ts

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 工厂订单搜索参数
export interface FactoryOrderSearchParams extends PageParams {
  dealerName?: string;
  model?: string;
  variant?: string;
  orderStatus?: string;
  approvalStatus?: string;
  paymentStatus?: string;
  loanStatus?: string;
  insuranceStatus?: string;
  jpjRegistrationStatus?: string;
  orderNumber?: string;
  orderDateStart?: string;
  orderDateEnd?: string;
}

// 工厂订单列表项
export interface FactoryOrderListItem {
  id: string;
  orderNumber: string;
  dealerName: string;
  model: string;
  variant: string;
  color: string;
  orderStatus: string;
  approvalStatus: string;
  paymentStatus: string;
  loanStatus: string;
  insuranceStatus: string;
  jpjRegistrationStatus: string;
  orderDate: string;
  deliveryDate?: string;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  customerName: string;
  customerPhone: string;
  salesConsultant: string;
  remarks?: string;
  createTime: string;
  updateTime: string;
}

// 工厂订单详情
export interface FactoryOrderDetail extends FactoryOrderListItem {
  customerInfo: {
    name: string;
    phone: string;
    email?: string;
    address?: string;
    icNumber: string;
  };
  vehicleInfo: {
    model: string;
    variant: string;
    color: string;
    vin?: string;
    engineNumber?: string;
    chassisNumber?: string;
  };
  paymentInfo: {
    totalAmount: number;
    paidAmount: number;
    remainingAmount: number;
    paymentMethod: string;
    paymentRecords: PaymentRecord[];
  };
  loanInfo?: {
    loanAmount: number;
    loanBank: string;
    loanStatus: string;
    approvalDate?: string;
  };
  insuranceInfo?: {
    insuranceCompany: string;
    insuranceType: string;
    insuranceAmount: number;
    insuranceStatus: string;
  };
  jpjInfo?: {
    registrationNumber?: string;
    registrationStatus: string;
    registrationDate?: string;
  };
}

// 支付记录
export interface PaymentRecord {
  id: string;
  amount: number;
  paymentMethod: string;
  paymentDate: string;
  transactionNumber?: string;
  remarks?: string;
}

// 工厂订单统计
export interface FactoryOrderStatistics {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
}

// 工厂订单分页响应
export interface FactoryOrderPageResponse {
  result: PageResponse<FactoryOrderListItem>;
}
```

### 5.2 Mock数据实现
```typescript
// src/mock/data/sales/factoryOrderManagement.ts
import type {
  FactoryOrderSearchParams,
  FactoryOrderPageResponse,
  FactoryOrderDetail,
  FactoryOrderStatistics
} from '@/types/sales/factoryOrderManagement';

// Mock数据生成
const generateMockFactoryOrders = (count: number) => {
  const orders = [];
  const dealers = ['01010001', '01010002', '01010003', '01010004'];
  const models = ['01020001', '01020002', '01020003'];
  const variants = ['01030001', '01030002', '01030003'];
  const orderStatuses = ['01040001', '01040002', '01040003', '01040004'];
  const paymentStatuses = ['01060001', '01060002', '01060003', '01060004'];

  for (let i = 1; i <= count; i++) {
    orders.push({
      id: `FO${String(i).padStart(6, '0')}`,
      orderNumber: `FO${String(i).padStart(6, '0')}`,
      dealerName: dealers[Math.floor(Math.random() * dealers.length)],
      model: models[Math.floor(Math.random() * models.length)],
      variant: variants[Math.floor(Math.random() * variants.length)],
      color: ['白色', '黑色', '银色', '红色'][Math.floor(Math.random() * 4)],
      orderStatus: orderStatuses[Math.floor(Math.random() * orderStatuses.length)],
      approvalStatus: ['01050001', '01050002', '01050003'][Math.floor(Math.random() * 3)],
      paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
      loanStatus: ['01070001', '01070002', '01070003'][Math.floor(Math.random() * 3)],
      insuranceStatus: ['01080001', '01080002', '01080003'][Math.floor(Math.random() * 3)],
      jpjRegistrationStatus: ['01090001', '01090002', '01090003'][Math.floor(Math.random() * 3)],
      orderDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      totalAmount: Math.floor(Math.random() * 100000) + 50000,
      paidAmount: Math.floor(Math.random() * 50000),
      remainingAmount: Math.floor(Math.random() * 50000),
      customerName: `客户${i}`,
      customerPhone: `+60${Math.floor(Math.random() * 1000000000)}`,
      salesConsultant: `顾问${Math.floor(Math.random() * 10) + 1}`,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updateTime: new Date().toISOString()
    });
  }
  return orders;
};

const mockOrders = generateMockFactoryOrders(100);

export const getFactoryOrderList = (params: FactoryOrderSearchParams): Promise<FactoryOrderPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockOrders];

      // 搜索过滤
      if (params.dealerName) {
        filteredData = filteredData.filter(item => item.dealerName === params.dealerName);
      }
      if (params.model) {
        filteredData = filteredData.filter(item => item.model === params.model);
      }
      if (params.orderStatus) {
        filteredData = filteredData.filter(item => item.orderStatus === params.orderStatus);
      }
      if (params.orderNumber) {
        filteredData = filteredData.filter(item =>
          item.orderNumber.toLowerCase().includes(params.orderNumber!.toLowerCase())
        );
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 10;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        }
      });
    }, 500);
  });
};

export const getFactoryOrderDetail = (orderNumber: string): Promise<{ result: FactoryOrderDetail }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const order = mockOrders.find(item => item.orderNumber === orderNumber);
      if (order) {
        resolve({
          result: {
            ...order,
            customerInfo: {
              name: order.customerName,
              phone: order.customerPhone,
              email: `${order.customerName.toLowerCase()}@example.com`,
              address: '马来西亚吉隆坡',
              icNumber: '123456-78-9012'
            },
            vehicleInfo: {
              model: order.model,
              variant: order.variant,
              color: order.color,
              vin: `VIN${order.id}`,
              engineNumber: `ENG${order.id}`,
              chassisNumber: `CHS${order.id}`
            },
            paymentInfo: {
              totalAmount: order.totalAmount,
              paidAmount: order.paidAmount,
              remainingAmount: order.remainingAmount,
              paymentMethod: '银行转账',
              paymentRecords: []
            }
          } as FactoryOrderDetail
        });
      }
    }, 300);
  });
};

export const getFactoryOrderStatistics = (): Promise<{ result: FactoryOrderStatistics }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        result: {
          totalOrders: mockOrders.length,
          pendingOrders: mockOrders.filter(o => o.orderStatus === '01040001').length,
          completedOrders: mockOrders.filter(o => o.orderStatus === '01040003').length,
          cancelledOrders: mockOrders.filter(o => o.orderStatus === '01040004').length,
          totalAmount: mockOrders.reduce((sum, o) => sum + o.totalAmount, 0),
          paidAmount: mockOrders.reduce((sum, o) => sum + o.paidAmount, 0),
          remainingAmount: mockOrders.reduce((sum, o) => sum + o.remainingAmount, 0)
        }
      });
    }, 300);
  });
};
```

## 6. 规范符合性验证

### 6.1 页面目录结构规范符合性

#### 6.1.1 目录结构规范 ✅
- [x] **路由页面位置**: `src/views/sales/factoryOrderManagement/FactoryOrderManagementView.vue` - 符合 `src/views/模块名/功能名/功能名View.vue` 规范
- [x] **API模块位置**: `src/api/modules/sales/factoryOrderManagement.ts` - 符合模块化API结构
- [x] **类型定义位置**: `src/types/sales/factoryOrderManagement.d.ts` - 符合模块化类型结构
- [x] **Mock数据位置**: `src/mock/data/sales/factoryOrderManagement.ts` - 符合模块化Mock结构
- [x] **国际化位置**: `src/locales/modules/sales/` - 符合模块化国际化结构

#### 6.1.2 命名规范 ✅
- [x] **路由页面命名**: `FactoryOrderManagementView.vue` - 符合 `功能名View.vue` 格式
- [x] **模块名称**: `sales` - 使用标准模块名称
- [x] **功能名称**: `factoryOrderManagement` - 使用小驼峰命名法

#### 6.1.3 MyBatisPlus分页规范 ✅
- [x] **分页参数**: 使用 `pageNum` 和 `pageSize` 标准参数
- [x] **分页响应**: 使用 `records`、`total`、`pageNum`、`pageSize`、`pages` 标准结构
- [x] **分页组件**: 绑定 `pagination.pageNum` 而非 `pagination.page`

#### 6.1.4 数据字典规范 ✅
- [x] **字典使用**: 使用 `useBatchDictionary` 批量获取字典
- [x] **字典转义**: 使用 `getNameByCode` 进行字典值转义
- [x] **字典常量**: 使用 `DICTIONARY_TYPES` 常量定义字典类型

#### 6.1.5 API响应处理规范 ✅
- [x] **响应结构**: 使用 `response.result` 获取业务数据
- [x] **错误处理**: 通过API拦截器统一处理错误
- [x] **Mock切换**: 支持Mock数据和真实API的切换

### 6.2 规范遵循要点
1. **功能内聚**: 工厂订单管理相关的所有文件统一放在 `sales/factoryOrderManagement/` 目录下
2. **清晰分类**: 路由页面直接放在功能目录下，组件放在 `components/` 子目录
3. **模块化组织**: API、Mock、类型定义、国际化文件都按相同的模块结构组织
4. **命名一致性**: 所有文件和目录命名都遵循统一的命名规范
5. **标准模块使用**: 使用项目标准的 `sales` 模块，而非自定义模块名
6. **分页标准化**: 严格遵循MyBatisPlus分页参数和响应结构
7. **字典标准化**: 统一使用数据字典进行选项展示和值转义
8. **API标准化**: 统一使用 `response.result` 模式处理API响应

---

**cc-fe**: 我已完成 `FactoryOrderManagementView.vue` 的重构设计文档。

**设计要点**:
- [x] 符合页面目录结构规范的模块化重构方案
- [x] MyBatisPlus分页组件标准化实现（pageNum/pageSize参数）
- [x] 参考ProspectsView.vue的数据字典实现方式
- [x] API请求响应的通用处理方式（response.result模式）
- [x] 完整的类型定义和Mock数据实现
- [x] 详细的实施计划和验收标准

文档已提交，请审查。
