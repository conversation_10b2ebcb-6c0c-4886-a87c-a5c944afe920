# Token传输实现指南

## 📋 概述

本文档详细说明了DMS系统中Token在HTTP请求头中的自动传输机制，确保所有需要认证的API请求都能正确携带Authorization头信息。

## 🔧 实现架构

### 1. 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户登录      │───▶│   Token存储     │───▶│   请求拦截器    │
│  (LoginView)    │    │ (localStorage)  │    │ (HTTP Interceptor)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                              ┌─────────────────┐
                                              │   API请求       │
                                              │ (Authorization) │
                                              └─────────────────┘
```

### 2. 文件结构

- `src/utils/http.ts` - 统一HTTP客户端和拦截器
- `src/stores/auth.ts` - 用户认证状态管理
- `src/api/modules/auth.ts` - 认证相关API
- `src/config/index.ts` - 配置管理
- `src/views/TokenTestView.vue` - Token传输测试页面

## 🚀 核心实现

### 1. HTTP请求拦截器

在 `src/utils/http.ts` 中实现：

```typescript
// 请求拦截器 - 自动添加Token
service.interceptors.request.use(
  (requestConfig) => {
    // 添加token到请求头
    const token = localStorage.getItem(config.tokenKey);
    if (token && requestConfig.headers) {
      requestConfig.headers.Authorization = `Bearer ${token}`;
    }
    
    // 开发环境打印请求信息
    if (import.meta.env.DEV) {
      console.log('🚀 API Request:', {
        url: requestConfig.url,
        method: requestConfig.method?.toUpperCase(),
        headers: requestConfig.headers,
        data: requestConfig.data
      });
    }
    
    return requestConfig;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);
```

### 2. Token存储管理

在 `src/stores/auth.ts` 中管理：

```typescript
// 设置Token
const setToken = (newToken: string, newRefreshToken?: string) => {
  token.value = newToken
  localStorage.setItem(config.tokenKey, newToken)
  
  if (newRefreshToken) {
    refreshToken.value = newRefreshToken
    localStorage.setItem(config.refreshTokenKey, newRefreshToken)
  }
}

// 清除Token
const clearToken = () => {
  token.value = null
  refreshToken.value = null
  userInfo.value = null
  localStorage.removeItem(config.tokenKey)
  localStorage.removeItem(config.refreshTokenKey)
}
```

### 3. 响应拦截器 - Token过期处理

```typescript
// 响应拦截器 - 处理Token过期
service.interceptors.response.use(
  (response) => {
    // 检查业务状态码
    if (res.code === 401 || res.code === '401') {
      handleTokenExpired();
      return Promise.reject(new Error('登录已过期，请重新登录'));
    }
    return res;
  },
  (error) => {
    // 检查HTTP状态码
    if (error.response?.status === 401) {
      handleTokenExpired();
    }
    return Promise.reject(error);
  }
);

// 处理Token过期
const handleTokenExpired = () => {
  localStorage.removeItem(config.tokenKey);
  localStorage.removeItem(config.refreshTokenKey);
  
  ElMessageBox.confirm('登录已过期，请重新登录', '提示', {
    confirmButtonText: '重新登录',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    window.location.href = '/login';
  }).catch(() => {
    window.location.href = '/login';
  });
};
```

## 📝 使用方法

### 1. API模块中使用

所有API模块都通过统一的HTTP客户端发送请求：

```typescript
import { http } from '@/utils/http'

// 示例：获取用户信息
export const getUserInfo = (): Promise<ApiResponse<UserInfo>> => {
  // Token会自动添加到请求头中
  return http.post('/auth/user-info')
}

// 示例：获取门店列表
export const getStoreList = (params: StoreQueryParams) => {
  // Token会自动添加到请求头中
  return http.post('/stores/page', params)
}
```

### 2. 组件中使用

在Vue组件中直接调用API方法：

```typescript
import { getUserInfo } from '@/api/modules/auth'
import { getStoreList } from '@/api/modules/permission'

// 调用API - Token会自动传输
const fetchUserInfo = async () => {
  try {
    const response = await getUserInfo()
    console.log('用户信息:', response.result)
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}
```

## 🧪 测试验证

### 1. 访问测试页面

在开发环境中，访问 `/test/token` 路径可以打开Token传输测试页面。

### 2. 测试功能

- **认证状态检查**: 显示当前登录状态和Token信息
- **API请求测试**: 测试不同类型的API请求
- **请求日志**: 实时显示请求信息和Token传输状态
- **错误处理**: 验证Token过期时的处理机制

### 3. 浏览器验证

1. **Network面板验证**:
   - 打开浏览器开发者工具
   - 切换到Network面板
   - 执行任意API请求
   - 检查Request Headers中是否包含 `Authorization: Bearer xxx`

2. **Console日志验证**:
   - 开发环境下会自动打印请求信息
   - 查看控制台输出的请求详情

## 📋 配置选项

### 1. Token配置

在 `src/config/index.ts` 中配置：

```typescript
export const config = {
  // Token键名
  tokenKey: 'token',
  refreshTokenKey: 'refreshToken',
  
  // API基础地址
  apiBaseUrl: 'http://localhost:8080/api/v1',
  
  // 请求超时时间
  timeout: 30000
}
```

### 2. Mock API配置

```typescript
export const config = {
  // 是否使用Mock API
  useMockApi: import.meta.env.VITE_APP_USE_MOCK_API === 'true' || import.meta.env.DEV
}
```

## ✅ 实现特点

### 1. 自动化
- ✅ 所有API请求自动添加Authorization头
- ✅ Token存储和清除自动管理
- ✅ Token过期自动处理和跳转

### 2. 统一性
- ✅ 所有API模块使用统一的HTTP客户端
- ✅ 统一的错误处理机制
- ✅ 统一的配置管理

### 3. 安全性
- ✅ Token过期自动清除
- ✅ 未授权访问自动重定向
- ✅ 安全的Token存储机制

### 4. 开发友好
- ✅ 开发环境请求日志
- ✅ 完整的TypeScript支持
- ✅ 详细的错误信息

## 🔍 调试指南

### 1. 常见问题

**问题**: API请求没有携带Token
- **检查**: 确认使用的是 `import { http } from '@/utils/http'`
- **解决**: 统一使用项目的HTTP客户端

**问题**: Token过期处理不生效
- **检查**: 确认后端返回的错误码格式
- **解决**: 调整响应拦截器中的状态码判断

**问题**: Mock API不返回Token
- **检查**: 确认Mock配置开关
- **解决**: 检查 `config.useMockApi` 配置

### 2. 调试步骤

1. **检查Token存储**:
   ```javascript
   console.log('Token:', localStorage.getItem('token'))
   ```

2. **检查请求头**:
   - 打开Network面板
   - 查看Request Headers
   - 确认Authorization字段

3. **检查拦截器**:
   - 在请求拦截器中添加断点
   - 验证Token添加逻辑

## 🚀 最佳实践

### 1. API开发规范

```typescript
// ✅ 推荐：使用统一的HTTP客户端
import { http } from '@/utils/http'

export const getUser = (id: string) => {
  return http.get(`/users/${id}`)
}

// ❌ 不推荐：直接使用axios
import axios from 'axios'

export const getUser = (id: string) => {
  return axios.get(`/users/${id}`) // 不会自动添加Token
}
```

### 2. 错误处理

```typescript
// ✅ 推荐：使用try-catch处理
const fetchData = async () => {
  try {
    const response = await getUser('123')
    // 处理成功响应
  } catch (error) {
    // 错误会被拦截器自动处理
    console.error('请求失败:', error)
  }
}
```

### 3. 类型安全

```typescript
// ✅ 推荐：使用类型定义
interface UserResponse {
  id: string
  name: string
}

export const getUser = (id: string): Promise<ApiResponse<UserResponse>> => {
  return http.get(`/users/${id}`)
}
```

## 📚 参考资料

- [Axios拦截器文档](https://axios-http.com/docs/interceptors)
- [Vue3 Composition API](https://vuejs.org/guide/extras/composition-api-faq.html)
- [Pinia状态管理](https://pinia.vuejs.org/)
- [TypeScript最佳实践](https://www.typescriptlang.org/docs/) 