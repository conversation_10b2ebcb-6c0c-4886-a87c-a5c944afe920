# 零件档案页面设计文档

## 1. 页面概述

### 1.1 页面定位

- **页面名称**：零件档案
- **功能定位**：零件主数据展示与同步管理
- **数据来源**：ERP主数据同步
- **操作权限**：只读查询，同步操作需要特定权限

### 1.2 核心功能

- 零件信息查询展示
- 多维度筛选搜索
- 主数据同步
- Excel数据导出
- 零件详情查看

## 2. 页面布局设计

### 2.1 整体布局结构

```
┌──────────────────────────────────────────────────────────────────────────────┐
│  页面标题栏                                                                    │
├──────────────────────────────────────────────────────────────────────────────┤
│  搜索筛选区                                                                    │
├──────────────────────────────────────────────────────────────────────────────┤
│  操作栏 & 状态栏                                                               │
├──────────────────────────────────────────────────────────────────────────────┤
│  数据列表区                                                                    │
├──────────────────────────────────────────────────────────────────────────────┤
│  分页控件                                                                      │
└──────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 搜索筛选区设计

#### 筛选字段布局（2行3列）

**第一行**：
- 零件编号（输入框）
- 零件名称（输入框）
- 品牌（下拉选择）

**第二行**：
- 类别（下拉选择）
- 条形码（输入框）
- 状态（下拉选择）

#### 操作按钮

- 查询按钮（主要）
- 重置按钮（次要）

### 2.3 操作栏设计

#### 左侧功能按钮

- 导出Excel（需要导出权限）

#### 右侧状态信息

- 最后同步时间：YYYY-MM-DD HH:mm:ss
- 同步状态图标：✅成功 / ❌失败 / 🔄同步中
- 同步数据按钮（需要同步权限）

### 2.4 数据列表设计

#### 列表字段

| 序号 | 字段名   | 宽度   | 对齐   | 说明       |
|------|----------|--------|--------|------------|
| 1    | 序号     | 60px   | 居中   | 自动编号   |
| 2    | 零件编号 | 120px  | 左对齐 | 主要标识   |
| 3    | 零件名称 | 200px  | 左对齐 | 支持tooltip |
| 4    | 品牌     | 100px  | 左对齐 | -          |
| 5    | 类别     | 100px  | 左对齐 | -          |
| 6    | 单位     | 60px   | 居中   | -          |
| 7    | 规格型号 | 150px  | 左对齐 | 支持tooltip |
| 8    | 状态     | 80px   | 居中   | 标签样式   |
| 9    | 操作     | 80px   | 居中   | 查看按钮   |

#### 状态标签样式

- 启用：绿色标签
- 停用：灰色标签

### 2.5 零件详情弹窗设计

#### 弹窗布局

```
┌─────────────────────────────────────────────────────────────────┐
│  零件详情 - {零件编号}                                      [X]  │
├─────────────────────────────────────────────────────────────────┤
│  基础信息模块                                                    │
├─────────────────────────────────────────────────────────────────┤
│  价格信息模块                                                    │
├─────────────────────────────────────────────────────────────────┤
│  系统信息模块                                                    │
├─────────────────────────────────────────────────────────────────┤
│                                              [关闭]              │
└─────────────────────────────────────────────────────────────────┘
```

#### 信息展示布局

- 采用2列布局展示信息
- 标签右对齐，值左对齐
- 重要信息突出显示

## 3. 交互设计

### 3.1 页面加载流程

1. 页面初始化
2. 并行请求：
   - 获取零件列表（默认第一页）
   - 获取同步状态信息
   - 获取筛选下拉选项（如需动态加载）
3. 渲染页面

### 3.2 搜索交互

- 输入框支持回车触发搜索
- 搜索时显示loading状态
- 无结果时显示友好提示

### 3.3 同步交互流程

1. 点击"同步数据"按钮
2. 二次确认弹窗
3. 调用同步接口
4. 显示进度提示
5. 轮询同步状态
6. 完成后刷新列表和状态

### 3.4 导出交互

1. 点击"导出Excel"
2. 根据当前筛选条件导出
3. 显示导出进度
4. 完成后自动下载

## 4. 接口设计

### 4.1 接口清单

| 接口名称     | 方法 | 路径                    | 说明               |
|--------------|------|------------------------|--------------------|
| 零件列表查询 | GET  | `/api/parts/list`       | 支持分页和筛选     |
| 零件详情     | GET  | `/api/parts/{id}`       | 获取单个零件详情   |
| 同步主数据   | POST | `/api/parts/sync`       | 触发同步任务       |
| 导出Excel    | POST | `/api/parts/export`     | 导出零件数据       |
| 同步状态查询 | GET  | `/api/parts/sync-status`| 获取同步状态       |
| 下拉选项     | GET  | `/api/parts/options`    | 获取品牌、类别选项 |

### 4.2 接口详细定义

#### 4.2.1 零件列表查询接口

```typescript
// 请求
GET /api/parts/list

interface PartListRequest {
  // 筛选条件
  partCode?: string;        // 零件编号（模糊）
  partName?: string;        // 零件名称（模糊）
  brand?: string;           // 品牌
  category?: string;        // 类别
  barcode?: string;         // 条形码
  status?: 'ACTIVE' | 'INACTIVE';  // 状态
  
  // 分页
  pageNo: number;           // 页码（从1开始）
  pageSize: number;         // 每页条数（默认10）
}

// 响应
interface PartListResponse {
  code: number;
  message: string;
  data: {
    list: PartItem[];
    total: number;
    pageNo: number;
    pageSize: number;
  };
}

interface PartItem {
  id: string;
  partCode: string;
  partName: string;
  brand: string;
  category: string;
  unit: string;
  specification: string;
  status: 'ACTIVE' | 'INACTIVE';
}
```

#### 4.2.2 零件详情接口

```typescript
// 请求
GET /api/parts/{id}

// 响应
interface PartDetailResponse {
  code: number;
  message: string;
  data: PartDetail;
}

interface PartDetail {
  id: string;
  partCode: string;
  partName: string;
  brand: string;
  category: string;
  unit: string;
  specification: string;
  barcode: string;
  status: 'ACTIVE' | 'INACTIVE';
  retailPrice: number;
  purchasePrice: number;
  minimumPrice: number;
  sourceSystem: string;
  createTime: string;
  updateTime: string;
  lastSyncTime: string;
  syncStatus: 'SUCCESS' | 'FAILED';
}
```

#### 4.2.3 同步主数据接口

```typescript
// 请求
POST /api/parts/sync

// 响应
interface SyncResponse {
  code: number;
  message: string;
  data: {
    success: boolean;
    syncTaskId: string;
    startTime: string;
  };
}
```

#### 4.2.4 导出Excel接口

```typescript
// 请求
POST /api/parts/export

interface ExportRequest {
  // 与列表查询相同的筛选条件
  partCode?: string;
  partName?: string;
  brand?: string;
  category?: string;
  barcode?: string;
  status?: string;
}

// 响应
interface ExportResponse {
  code: number;
  message: string;
  data: {
    fileUrl: string;      // 文件下载地址
    fileName: string;     // 文件名
    expireTime: string;   // 过期时间
  };
}
```

#### 4.2.5 同步状态查询接口

```typescript
// 请求
GET /api/parts/sync-status

// 响应
interface SyncStatusResponse {
  code: number;
  message: string;
  data: {
    lastSyncTime: string;
    syncStatus: 'SUCCESS' | 'FAILED' | 'SYNCING';
    syncMessage?: string;     // 失败原因
    nextSyncTime?: string;    // 下次同步时间
    syncProgress?: number;    // 同步进度（0-100）
  };
}
```

#### 4.2.6 下拉选项接口

```typescript
// 请求
GET /api/parts/options

// 响应
interface OptionsResponse {
  code: number;
  message: string;
  data: {
    brands: string[];
    categories: string[];
  };
}
```

## 5. 状态管理

### 5.1 页面状态定义

```typescript
interface PartArchivePageState {
  // 列表数据
  partList: PartItem[];
  total: number;
  loading: boolean;
  
  // 分页
  pageNo: number;
  pageSize: number;
  
  // 筛选条件
  filters: {
    partCode?: string;
    partName?: string;
    brand?: string;
    category?: string;
    barcode?: string;
    status?: string;
  };
  
  // 同步状态
  syncStatus: {
    lastSyncTime: string;
    status: 'SUCCESS' | 'FAILED' | 'SYNCING';
    message?: string;
  };
  
  // 下拉选项
  options: {
    brands: string[];
    categories: string[];
  };
  
  // 详情弹窗
  detailDialog: {
    visible: boolean;
    loading: boolean;
    data: PartDetail | null;
  };
}
```

### 5.2 状态更新时机

- **搜索操作**：更新filters和partList
- **分页操作**：更新pageNo和partList
- **同步操作**：更新syncStatus
- **查看详情**：更新detailDialog

## 6. 权限控制

### 6.1 页面权限定义

- **查看权限**：`parts:archive:view`
- **导出权限**：`parts:archive:export`
- **同步权限**：`parts:archive:sync`

### 6.2 权限控制点

- **页面入口**：需要查看权限
- **导出按钮**：需要导出权限
- **同步按钮**：需要同步权限

## 7. 性能优化

### 7.1 列表优化

- 虚拟滚动（数据量大时）
- 分页加载
- 搜索防抖（300ms）

### 7.2 缓存策略

- 下拉选项数据缓存
- 详情数据短时缓存（5分钟）

### 7.3 加载优化

- 骨架屏显示
- 分步加载
- 懒加载详情数据

## 8. 错误处理

### 8.1 接口错误处理

- **网络错误**：提示网络异常
- **业务错误**：显示具体错误信息
- **超时错误**：提示请求超时

### 8.2 同步错误处理

- **同步失败**：显示失败原因
- **同步超时**：提示联系管理员

## 9. 国际化支持

### 9.1 需要国际化的内容

- 页面标题
- 表格列头
- 按钮文本
- 提示信息
- 状态文本

### 9.2 国际化key设计

```javascript
{
  "parts": {
    "archive": {
      "title": "零件档案",
      "search": {
        "partCode": "零件编号",
        "partName": "零件名称",
        "brand": "品牌",
        "category": "类别",
        "barcode": "条形码",
        "status": "状态"
      },
      "table": {
        "no": "序号",
        "partCode": "零件编号",
        "partName": "零件名称",
        "brand": "品牌",
        "category": "类别",
        "unit": "单位",
        "specification": "规格型号",
        "status": "状态",
        "action": "操作"
      },
      "action": {
        "search": "查询",
        "reset": "重置",
        "export": "导出Excel",
        "sync": "同步数据",
        "view": "查看"
      },
      "status": {
        "active": "启用",
        "inactive": "停用",
        "syncing": "同步中",
        "syncSuccess": "同步成功",
        "syncFailed": "同步失败"
      },
      "message": {
        "syncConfirm": "确定要同步主数据吗？",
        "syncSuccess": "数据同步成功",
        "syncFailed": "数据同步失败",
        "exportSuccess": "导出成功",
        "noData": "暂无数据"
      }
    }
  }
}
```

## 10. 技术实现要点

### 10.1 前端交互流程

1. **页面加载**：
   - 调用列表查询接口
   - 调用同步状态接口
   - （可选）调用下拉选项接口

2. **搜索/筛选**：
   - 调用列表查询接口（带参数）

3. **查看详情**：
   - 调用详情接口

4. **同步数据**：
   - 调用同步接口
   - 轮询同步状态接口直到完成

5. **导出操作**：
   - 调用导出接口
   - 下载文件

### 10.2 接口汇总

| 功能     | 接口数量 | 说明                     |
|----------|----------|--------------------------|
| 核心功能 | 5个      | 列表、详情、同步、导出、同步状态 |
| 辅助功能 | 1个      | 下拉选项（可选）         |
| **总计** | **5-6个** | **取决于下拉数据是否需要动态加载** |

### 10.3 开发注意事项

- 确保所有文本支持国际化
- 做好权限控制和异常处理
- 优化大数据量的展示性能
- 保持UI交互的一致性
- 确保数据同步的可靠性

---

**文档版本**：v1.0  
**最后更新**：2024年1月  
**维护人员**：前端开发团队
