# 安全库存设置页面技术方案

## 1. 技术方案概述

### 1.1 技术栈选择

- **前端框架：** Vue 3 + TypeScript
- **UI组件库：** Element Plus
- **状态管理：** Pinia
- **路由管理：** Vue Router 4
- **HTTP客户端：** Axios
- **构建工具：** Vite
- **代码规范：** ESLint + Prettier

### 1.2 架构设计原则

- **组件化：** 高度模块化的组件设计，提高复用性
- **响应式：** 支持多种设备屏幕适配
- **性能优化：** 懒加载、虚拟滚动、防抖节流
- **可维护性：** 清晰的代码结构和规范的命名
- **可扩展性：** 预留扩展接口，便于后续功能增加

## 2. 系统架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────┐
│           安全库存设置页面              │
├─────────────────────────────────────────┤
│  View Layer (页面视图层)                │
│  ├── SafetyStockSettingView.vue         │
│  ├── Components (组件层)                │
│  │   ├── SafetyStockStatsCards.vue     │
│  │   ├── SafetyStockSearchForm.vue     │
│  │   ├── SafetyStockListTable.vue     │
│  │   ├── SafetyStockSettingModal.vue  │
│  │   └── BatchSettingModal.vue        │
├─────────────────────────────────────────┤
│  Store Layer (状态管理层)               │
│  ├── safetyStockStore.ts               │
│  └── partCategoryStore.ts              │
├─────────────────────────────────────────┤
│  API Layer (接口层)                     │
│  ├── safetyStock.ts                    │
│  └── partCategory.ts                   │
├─────────────────────────────────────────┤
│  Utils Layer (工具层)                   │
│  ├── safetyStockCalculator.ts          │
│  ├── dataValidator.ts                  │
│  └── excelHandler.ts                   │
└─────────────────────────────────────────┘
```

### 2.2 目录结构设计

```
src/views/parts/safety-stock/
├── index.vue                          # 主页面入口
├── components/                         # 页面组件
│   ├── SafetyStockStatsCards.vue      # 统计卡片组件
│   ├── SafetyStockSearchForm.vue      # 搜索筛选组件
│   ├── SafetyStockListTable.vue       # 数据列表组件
│   ├── SafetyStockSettingModal.vue    # 单个设置弹窗
│   ├── BatchSettingModal.vue          # 批量设置弹窗
│   ├── SmartRecommendModal.vue        # 智能推荐弹窗
│   └── ImportExportModal.vue          # 导入导出弹窗
├── composables/                        # 组合式函数
│   ├── useSafetyStockList.ts          # 列表数据管理
│   ├── useSafetyStockStats.ts         # 统计数据管理
│   ├── useBatchOperation.ts           # 批量操作逻辑
│   └── useSmartRecommendation.ts      # 智能推荐逻辑
└── types/                             # 类型定义
    └── safetyStock.d.ts               # 安全库存相关类型

src/api/modules/parts/
└── safetyStock.ts                     # 安全库存相关API

src/stores/modules/parts/
└── safetyStock.ts                     # 安全库存状态管理

src/utils/
├── safetyStockCalculator.ts           # 安全库存计算工具
├── dataValidator.ts                   # 数据验证工具
└── excelHandler.ts                    # Excel处理工具
```

## 3. 组件设计方案

### 3.1 主页面组件 (SafetyStockSettingView.vue)

#### 3.1.1 组件职责
- 页面布局和路由管理
- 子组件的数据传递和事件处理
- 全局状态管理

#### 3.1.2 技术实现要点
```typescript
// 主要功能实现
const { 
  tableData, 
  pagination, 
  loading, 
  selectedItems,
  fetchList,
  handleSearch,
  handleReset
} = useSafetyStockList();

const {
  statsData,
  fetchStats
} = useSafetyStockStats();

// 弹窗控制
const settingModalVisible = ref(false);
const batchModalVisible = ref(false);
const currentPartId = ref<number | null>(null);
```

### 3.2 统计卡片组件 (SafetyStockStatsCards.vue)

#### 3.2.1 组件功能
- 展示四个关键统计指标
- 支持点击卡片快速筛选
- 实时数据更新

#### 3.2.2 数据结构设计
```typescript
interface SafetyStockStats {
  configuredParts: {
    count: number;
    percentage: number;
    trend: number;
  };
  unconfiguredParts: {
    count: number;
    percentage: number;
  };
  belowSafetyStock: {
    count: number;
    urgentCount: number;
  };
  averageSafetyDays: {
    value: number;
    trend: number;
  };
}
```

### 3.3 搜索筛选组件 (SafetyStockSearchForm.vue)

#### 3.3.1 筛选条件设计
```typescript
interface SearchFormData {
  category?: string;           // 零件类别
  partCode?: string;          // 零件编号
  partName?: string;          // 零件名称
  settingStatus?: string;     // 设置状态
  stockStatus?: string;       // 库存状态
  consumptionLevel?: string;  // 消耗频率
  supplierType?: string;      // 供应商类型
  partImportance?: string;    // 零件重要性
}
```

#### 3.3.2 防抖搜索实现
```typescript
import { debounce } from 'lodash-es';

const debouncedSearch = debounce((searchForm: SearchFormData) => {
  emit('search', searchForm);
}, 300);
```

### 3.4 数据列表组件 (SafetyStockListTable.vue)

#### 3.4.1 列配置设计
```typescript
const columns = [
  { prop: 'partCode', label: '零件编号', width: 120, fixed: 'left' },
  { prop: 'partName', label: '零件名称', width: 200 },
  { prop: 'currentStock', label: '当前库存', width: 100, align: 'center' },
  { prop: 'safetyStock', label: '安全库存', width: 100, align: 'center' },
  { prop: 'recommendedSafetyStock', label: '推荐安全值', width: 120, align: 'center' },
  { prop: 'monthlyConsumption', label: '月均消耗', width: 100, align: 'center' },
  { prop: 'settingStatus', label: '设置状态', width: 100, align: 'center' },
  { prop: 'actions', label: '操作', width: 120, fixed: 'right' }
];
```

#### 3.4.2 虚拟滚动实现
```typescript
// 使用 Element Plus 的虚拟化表格
<el-table-v2
  :columns="columns"
  :data="tableData"
  :width="tableWidth"
  :height="tableHeight"
  :row-height="50"
  v-loading="loading"
/>
```

### 3.5 设置弹窗组件 (SafetyStockSettingModal.vue)

#### 3.5.1 设置方式枚举
```typescript
enum SettingMode {
  MANUAL = 'manual',        // 手动设置
  SMART = 'smart',          // 智能推荐
  BY_DAYS = 'by_days'       // 按天数设置
}
```

#### 3.5.2 智能推荐算法
```typescript
interface RecommendationResult {
  recommendedValue: number;
  calculationBasis: {
    averageDailyConsumption: number;
    supplyLeadTime: number;
    safetyDays: number;
    seasonalFactor: number;
  };
  confidence: number; // 推荐置信度
}
```

## 4. 状态管理设计

### 4.1 Store 结构设计

```typescript
// stores/modules/parts/safetyStock.ts
export const useSafetyStockStore = defineStore('safetyStock', {
  state: (): SafetyStockState => ({
    // 列表数据
    list: [],
    total: 0,
    loading: false,
    
    // 搜索条件
    searchForm: {
      category: '',
      partCode: '',
      partName: '',
      settingStatus: 'all'
    },
    
    // 分页信息
    pagination: {
      page: 1,
      pageSize: 20
    },
    
    // 统计数据
    stats: {
      configuredParts: { count: 0, percentage: 0, trend: 0 },
      unconfiguredParts: { count: 0, percentage: 0 },
      belowSafetyStock: { count: 0, urgentCount: 0 },
      averageSafetyDays: { value: 0, trend: 0 }
    },
    
    // 选中项
    selectedItems: []
  }),
  
  getters: {
    // 过滤后的列表
    filteredList: (state) => {
      // 根据搜索条件过滤数据
    },
    
    // 选中项的统计
    selectedStats: (state) => {
      // 计算选中项的统计信息
    }
  },
  
  actions: {
    // 获取列表数据
    async fetchList(params?: SearchParams) {},
    
    // 获取统计数据
    async fetchStats() {},
    
    // 设置安全库存
    async setSafetyStock(params: SetSafetyStockParams) {},
    
    // 批量设置
    async batchSetSafetyStock(params: BatchSetParams) {},
    
    // 智能推荐
    async getSmartRecommendation(partIds: number[]) {},
    
    // 重置搜索条件
    resetSearch() {},
    
    // 设置选中项
    setSelectedItems(items: SafetyStockItem[]) {}
  }
});
```

## 5. API 接口设计

### 5.1 接口列表

```typescript
// api/modules/parts/safetyStock.ts

// 获取安全库存列表
export interface GetSafetyStockListParams {
  page: number;
  pageSize: number;
  category?: string;
  partCode?: string;
  partName?: string;
  settingStatus?: string;
  stockStatus?: string;
}

export const getSafetyStockList = (params: GetSafetyStockListParams) => {
  return request.get<ApiResponse<SafetyStockListResponse>>('/api/parts/safety-stock/list', { params });
};

// 获取统计数据
export const getSafetyStockStats = () => {
  return request.get<ApiResponse<SafetyStockStats>>('/api/parts/safety-stock/stats');
};

// 设置安全库存
export interface SetSafetyStockParams {
  partId: number;
  safetyStock: number;
  warningDays: number;
  settingMode: SettingMode;
  remark?: string;
}

export const setSafetyStock = (params: SetSafetyStockParams) => {
  return request.post<ApiResponse<void>>('/api/parts/safety-stock/set', params);
};

// 批量设置安全库存
export interface BatchSetSafetyStockParams {
  partIds: number[];
  settingMode: 'percentage' | 'uniform' | 'by_days' | 'recommended';
  value: number;
  remark?: string;
}

export const batchSetSafetyStock = (params: BatchSetSafetyStockParams) => {
  return request.post<ApiResponse<BatchOperationResult>>('/api/parts/safety-stock/batch-set', params);
};

// 获取智能推荐
export const getSmartRecommendation = (partIds: number[]) => {
  return request.post<ApiResponse<RecommendationResult[]>>('/api/parts/safety-stock/smart-recommend', { partIds });
};

// 导入安全库存设置
export const importSafetyStockSettings = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  return request.post<ApiResponse<ImportResult>>('/api/parts/safety-stock/import', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  });
};

// 导出安全库存设置
export const exportSafetyStockSettings = (params: GetSafetyStockListParams) => {
  return request.get('/api/parts/safety-stock/export', { 
    params, 
    responseType: 'blob' 
  });
};
```

## 6. 工具函数设计

### 6.1 安全库存计算工具

```typescript
// utils/safetyStockCalculator.ts

export interface CalculationInput {
  partId: number;
  averageDailyConsumption: number;
  supplyLeadTime: number;
  historicalVariance: number;
  seasonalFactor?: number;
  supplierReliability?: number;
  partImportance?: 'critical' | 'important' | 'normal';
}

export interface CalculationResult {
  recommendedSafetyStock: number;
  confidence: number;
  calculationDetails: {
    baseStock: number;
    varianceBuffer: number;
    seasonalAdjustment: number;
    reliabilityAdjustment: number;
    importanceAdjustment: number;
  };
}

export class SafetyStockCalculator {
  // 智能推荐计算
  static calculateRecommendation(input: CalculationInput): CalculationResult {
    const { 
      averageDailyConsumption, 
      supplyLeadTime, 
      historicalVariance,
      seasonalFactor = 1,
      supplierReliability = 0.95,
      partImportance = 'normal'
    } = input;

    // 基础安全库存 = 平均日消耗 × 供应周期
    const baseStock = averageDailyConsumption * supplyLeadTime;

    // 方差缓冲
    const varianceBuffer = Math.sqrt(supplyLeadTime) * historicalVariance;

    // 季节性调整
    const seasonalAdjustment = baseStock * (seasonalFactor - 1);

    // 供应商可靠性调整
    const reliabilityAdjustment = baseStock * (1 - supplierReliability);

    // 零件重要性调整
    const importanceMultiplier = this.getImportanceMultiplier(partImportance);
    const importanceAdjustment = baseStock * (importanceMultiplier - 1);

    // 最终推荐值
    const recommendedSafetyStock = Math.ceil(
      baseStock + 
      varianceBuffer + 
      seasonalAdjustment + 
      reliabilityAdjustment + 
      importanceAdjustment
    );

    // 置信度计算
    const confidence = this.calculateConfidence(input);

    return {
      recommendedSafetyStock,
      confidence,
      calculationDetails: {
        baseStock,
        varianceBuffer,
        seasonalAdjustment,
        reliabilityAdjustment,
        importanceAdjustment
      }
    };
  }

  private static getImportanceMultiplier(importance: string): number {
    const multipliers = {
      'critical': 1.5,
      'important': 1.2,
      'normal': 1.0
    };
    return multipliers[importance] || 1.0;
  }

  private static calculateConfidence(input: CalculationInput): number {
    // 根据数据完整性和历史准确性计算置信度
    let confidence = 1.0;

    // 历史数据充足性影响
    if (input.historicalVariance > 0.5) confidence -= 0.2;

    // 供应商可靠性影响
    if (input.supplierReliability < 0.9) confidence -= 0.1;

    return Math.max(0.5, confidence);
  }

  // 按天数计算
  static calculateByDays(dailyConsumption: number, targetDays: number): number {
    return Math.ceil(dailyConsumption * targetDays);
  }

  // 按比例计算
  static calculateByPercentage(currentStock: number, percentage: number): number {
    return Math.ceil(currentStock * percentage / 100);
  }
}
```

### 6.2 数据验证工具

```typescript
// utils/dataValidator.ts

export interface ValidationRule {
  field: string;
  required?: boolean;
  type?: 'number' | 'string' | 'array';
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export class DataValidator {
  static validateSafetyStockSetting(data: SetSafetyStockParams): ValidationResult {
    const rules: ValidationRule[] = [
      { field: 'partId', required: true, type: 'number', min: 1 },
      { field: 'safetyStock', required: true, type: 'number', min: 0, max: 99999 },
      { field: 'warningDays', required: true, type: 'number', min: 1, max: 30 }
    ];

    return this.validate(data, rules);
  }

  static validateBatchSetting(data: BatchSetSafetyStockParams): ValidationResult {
    const rules: ValidationRule[] = [
      { field: 'partIds', required: true, type: 'array' },
      { field: 'settingMode', required: true, type: 'string' },
      { field: 'value', required: true, type: 'number', min: 0 }
    ];

    return this.validate(data, rules);
  }

  private static validate(data: any, rules: ValidationRule[]): ValidationResult {
    const errors: string[] = [];

    for (const rule of rules) {
      const value = data[rule.field];

      // 必填验证
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.push(`${rule.field} 是必填项`);
        continue;
      }

      // 类型验证
      if (value !== undefined && rule.type) {
        if (!this.validateType(value, rule.type)) {
          errors.push(`${rule.field} 类型不正确`);
          continue;
        }
      }

      // 数值范围验证
      if (rule.type === 'number' && typeof value === 'number') {
        if (rule.min !== undefined && value < rule.min) {
          errors.push(`${rule.field} 不能小于 ${rule.min}`);
        }
        if (rule.max !== undefined && value > rule.max) {
          errors.push(`${rule.field} 不能大于 ${rule.max}`);
        }
      }

      // 正则验证
      if (rule.pattern && typeof value === 'string') {
        if (!rule.pattern.test(value)) {
          errors.push(`${rule.field} 格式不正确`);
        }
      }

      // 自定义验证
      if (rule.custom) {
        const result = rule.custom(value);
        if (typeof result === 'string') {
          errors.push(result);
        } else if (!result) {
          errors.push(`${rule.field} 验证失败`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private static validateType(value: any, type: string): boolean {
    switch (type) {
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'string':
        return typeof value === 'string';
      case 'array':
        return Array.isArray(value);
      default:
        return true;
    }
  }
}

interface ValidationResult {
  valid: boolean;
  errors: string[];
}
```

### 6.3 Excel处理工具

```typescript
// utils/excelHandler.ts
import * as XLSX from 'xlsx';

export class ExcelHandler {
  // 导出安全库存设置
  static exportSafetyStockSettings(data: SafetyStockItem[], filename = '安全库存设置') {
    const worksheet = XLSX.utils.json_to_sheet(
      data.map(item => ({
        '零件编号': item.partCode,
        '零件名称': item.partName,
        '规格型号': item.specification,
        '零件类别': item.category,
        '当前库存': item.currentStock,
        '安全库存': item.safetyStock || '未设置',
        '推荐安全值': item.recommendedSafetyStock,
        '月均消耗': item.monthlyConsumption,
        '设置状态': this.getSettingStatusText(item.settingStatus),
        '最后更新时间': item.updatedAt
      }))
    );

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '安全库存设置');

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 零件编号
      { wch: 25 }, // 零件名称
      { wch: 20 }, // 规格型号
      { wch: 15 }, // 零件类别
      { wch: 12 }, // 当前库存
      { wch: 12 }, // 安全库存
      { wch: 15 }, // 推荐安全值
      { wch: 12 }, // 月均消耗
      { wch: 12 }, // 设置状态
      { wch: 20 }  // 最后更新时间
    ];
    worksheet['!cols'] = colWidths;

    // 下载文件
    XLSX.writeFile(workbook, `${filename}.xlsx`);
  }

  // 解析导入的Excel文件
  static parseImportFile(file: File): Promise<ImportData[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          const importData = jsonData.map((row: any) => ({
            partCode: row['零件编号'],
            safetyStock: row['安全库存'],
            warningDays: row['预警天数'] || 7,
            remark: row['备注']
          }));

          resolve(importData);
        } catch (error) {
          reject(new Error('文件解析失败'));
        }
      };

      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsArrayBuffer(file);
    });
  }

  private static getSettingStatusText(status: string): string {
    const statusMap = {
      'configured': '已设置',
      'unconfigured': '未设置',
      'below_safety': '低于安全值'
    };
    return statusMap[status] || status;
  }
}

interface ImportData {
  partCode: string;
  safetyStock: number;
  warningDays?: number;
  remark?: string;
}
```

## 7. 路由配置

```typescript
// router/modules/parts.ts
export const safetyStockRoutes: RouteRecordRaw[] = [
  {
    path: '/parts/safety-stock',
    name: 'parts-safety-stock',
    component: () => import('@/views/parts/safety-stock/index.vue'),
    meta: {
      title: 'menu.safetyStockSetting',
      icon: 'Setting',
      requiresAuth: true,
      permissions: ['parts:safety-stock:view'],
      breadcrumb: [
        { title: 'menu.partsManagement' },
        { title: 'menu.safetyStockSetting' }
      ]
    }
  }
];
```

## 8. 国际化配置

```typescript
// locales/modules/parts/zh.json
{
  "safetyStock": {
    "title": "安全库存设置",
    "stats": {
      "configuredParts": "已设置零件",
      "unconfiguredParts": "待设置零件",
      "belowSafetyStock": "低于安全库存",
      "averageSafetyDays": "平均安全库存"
    },
    "search": {
      "category": "零件类别",
      "partCode": "零件编号",
      "partName": "零件名称",
      "settingStatus": "设置状态",
      "all": "全部",
      "configured": "已设置",
      "unconfigured": "未设置",
      "belowSafety": "低于安全值"
    },
    "table": {
      "partCode": "零件编号",
      "partName": "零件名称",
      "currentStock": "当前库存",
      "safetyStock": "安全库存",
      "recommendedValue": "推荐安全值",
      "monthlyConsumption": "月均消耗",
      "settingStatus": "设置状态",
      "actions": "操作"
    },
    "actions": {
      "smartRecommend": "智能推荐设置",
      "batchSet": "批量设置安全库存",
      "setCategoryRules": "根据类别设置",
      "import": "导入设置",
      "export": "导出当前设置",
      "set": "设置",
      "modify": "修改"
    },
    "modal": {
      "settingTitle": "安全库存设置",
      "batchTitle": "批量设置安全库存",
      "partInfo": "零件信息",
      "consumptionAnalysis": "消耗分析",
      "settingOptions": "安全库存设置",
      "selectedParts": "选中零件",
      "batchOptions": "批量设置选项",
      "previewResult": "预览结果"
    }
  }
}
```

## 9. 性能优化方案

### 9.1 列表性能优化

```typescript
// 虚拟滚动配置
const virtualScrollConfig = {
  height: 400,
  itemHeight: 50,
  buffer: 10
};

// 防抖搜索
const debouncedSearch = debounce(search, 300);

// 分页加载
const usePagination = () => {
  const page = ref(1);
  const pageSize = ref(20);
  const total = ref(0);
  
  const loadMore = async () => {
    if (loading.value || page.value * pageSize.value >= total.value) return;
    
    page.value++;
    const newData = await fetchData({ page: page.value, pageSize: pageSize.value });
    data.value.push(...newData.list);
  };
  
  return { page, pageSize, total, loadMore };
};
```

### 9.2 数据缓存策略

```typescript
// 使用 Pinia 持久化
export const useSafetyStockStore = defineStore('safetyStock', {
  state: () => ({}),
  persist: {
    key: 'safety-stock-cache',
    storage: sessionStorage,
    paths: ['searchForm', 'pagination']
  }
});

// API 响应缓存
const apiCache = new Map();
const cacheTimeout = 5 * 60 * 1000; // 5分钟

export const getCachedData = async (key: string, fetcher: () => Promise<any>) => {
  const cached = apiCache.get(key);
  if (cached && Date.now() - cached.timestamp < cacheTimeout) {
    return cached.data;
  }
  
  const data = await fetcher();
  apiCache.set(key, { data, timestamp: Date.now() });
  return data;
};
```

## 10. 测试方案

### 10.1 单元测试

```typescript
// tests/components/SafetyStockListTable.spec.ts
import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import SafetyStockListTable from '@/views/parts/safety-stock/components/SafetyStockListTable.vue';

describe('SafetyStockListTable', () => {
  it('renders table with correct data', () => {
    const wrapper = mount(SafetyStockListTable, {
      props: {
        data: mockTableData,
        loading: false
      }
    });
    
    expect(wrapper.find('.el-table').exists()).toBe(true);
    expect(wrapper.findAll('.el-table__row')).toHaveLength(mockTableData.length);
  });
  
  it('emits selection-change event when rows are selected', async () => {
    const wrapper = mount(SafetyStockListTable, {
      props: { data: mockTableData }
    });
    
    await wrapper.find('.el-checkbox').trigger('click');
    expect(wrapper.emitted('selection-change')).toBeTruthy();
  });
});
```

### 10.2 集成测试

```typescript
// tests/integration/safetyStock.spec.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useSafetyStockStore } from '@/stores/modules/parts/safetyStock';

describe('Safety Stock Integration', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });
  
  it('should fetch and update list data', async () => {
    const store = useSafetyStockStore();
    
    await store.fetchList();
    
    expect(store.list.length).toBeGreaterThan(0);
    expect(store.loading).toBe(false);
  });
  
  it('should handle batch setting operation', async () => {
    const store = useSafetyStockStore();
    
    const result = await store.batchSetSafetyStock({
      partIds: [1, 2, 3],
      settingMode: 'percentage',
      value: 80
    });
    
    expect(result.success).toBe(true);
  });
});
```

## 11. 部署和监控

### 11.1 构建配置

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'safety-stock': [
            'src/views/parts/safety-stock',
            'src/stores/modules/parts/safetyStock'
          ]
        }
      }
    }
  }
});
```

### 11.2 错误监控

```typescript
// utils/errorHandler.ts
export const setupErrorHandling = () => {
  // 全局错误处理
  window.addEventListener('error', (event) => {
    console.error('Global Error:', event.error);
    // 发送错误日志到监控系统
  });
  
  // Vue 错误处理
  app.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err, info);
    // 发送错误日志到监控系统
  };
};
```

## 12. 开发时间估算

### 12.1 开发阶段划分

| 阶段 | 工作内容 | 预估时间 |
|------|----------|----------|
| 第一阶段 | 基础架构搭建、API接口定义 | 2天 |
| 第二阶段 | 主页面和基础组件开发 | 3天 |
| 第三阶段 | 核心功能实现（设置、批量操作） | 4天 |
| 第四阶段 | 智能推荐算法实现 | 2天 |
| 第五阶段 | 导入导出功能实现 | 2天 |
| 第六阶段 | 测试和优化 | 2天 |
| **总计** | **完整功能开发** | **15天** |

### 12.2 里程碑计划

- **Week 1：** 完成基础架构和主要页面框架
- **Week 2：** 完成核心功能和智能推荐
- **Week 3：** 完成导入导出、测试和优化

这个技术方案提供了完整的开发指导，包含了架构设计、组件设计、性能优化、测试方案等各个方面，可以确保安全库存设置页面的高质量交付。 