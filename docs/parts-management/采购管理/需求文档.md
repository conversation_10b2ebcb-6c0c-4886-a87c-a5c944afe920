# 采购订单管理需求文档

## 1. 页面概述

**页面名称:** 采购订单管理
**所属模块:** 配件管理 > 采购管理

**核心功能:** 提供一个集中管理采购订单的界面，允许用户创建、查看、编辑、审核和跟踪向供应商发出的配件采购订单。该页面是配件采购流程的起点，对于确保配件供应的及时性和准确性至关重要。

## 2. 用户故事

- **作为经销商配件管理员,** 我希望能快速创建采购申请单，无论是向主机厂申请原厂件，还是向其他供应商采购非原厂件，都能在一个流程中完成，并能分别跟踪这些订单的后续状态。
- **作为主机厂零件专员,** 我希望能集中审批所有经销商的采购申请，并方便地处理部分发货和全部发货的操作。

## 3. 功能需求 (v2.0 经销商-主机厂协同模式)

### 3.1 经销商端

- **仪表盘:**
    - 快速概览“待审核”、“在途订单”、“待收货”和“本月申请总额”四个核心指标。
- **采购订单列表:**
    - **查询:** 支持按订单号或物流单号进行模糊搜索。
    - **快速筛选:** 提供按订单全生命周期状态（草稿、待厂端审核、已驳回、在途、部分到货、已完成）的一键筛选功能。
    - **列表展示:** 清晰展示订单状态、单号、金额、创建及发货日期。
    - **操作:** 根据订单状态提供不同的操作按钮（新建、编辑、删除、查看、取消、收货）。
- **新建/编辑采购订单:**
    - 自动关联当前经销商，选择收货仓库。
    - 通过弹窗从配件主数据中添加申请配件，列表需展示当前库存和安全库存以供参考。
    - 可对申请数量进行编辑，并填写备注。
    - 一个订单草稿中可以同时添加原厂零件和非原厂零件。
    - 可将订单“保存为草稿”或直接“提交审核”。
    - **提交规则:** 提交时，若订单中同时包含原厂和非原厂零件，系统会自动将其拆分为两个独立的采购订单，分别进入后续的审批流程。
- **采购收货:**
    - 页面清晰展示关联的发货单信息（物流号、承运商等）。
    - 列表展示本次发货的配件、订购数和本次发货数。
    - 用户需填写“本次入库数”和指定的“库位”。
    - 点击“确认入库”完成收货，并更新库存。

### 3.2 主机厂端

- **仪表盘:**
    - 快速概览“待我审批”、“今日已审”、“待发货订单”和“7日内申请总额”四个核心指标。
- **采购审批列表:**
    - **查询:** 支持按订单号或经销商名称进行模糊搜索。
    - **快速筛选:** 提供按订单处理状态（待审核、已审核、待发货、部分发货等）的一键筛选。
    - **列表展示:** 清晰展示订单状态、单号、经销商名称、金额和申请日期。
    - **操作:** 提供“审核”或“发货”的入口。
- **审批/发货处理页:**
    - 一体化页面，根据订单状态显示不同操作区（审核区/发货区）。
    - **订单信息:** 展示经销商、申请人、申请备注等基础信息。
    - **明细列表:** 展示申请配件、申请数量，并实时拉取“厂区库存”作为参考。
    - **审核区:** 提供“通过”和“驳回”选项，并可填写审核说明。
    - **发货区:** 允许编辑“本次发货数量”（以处理部分发货），并需录入“承运商”和“物流单号”。

## 4. 核心交互点

- **点击统计卡片:** 可快速筛选出对应状态的订单列表。
- **列表状态标签:** 点击列表中的状态标签，可弹出该状态的流转历史记录。
- **新建订单时添加配件:** 应使用模态弹窗（Modal）形式，提供可搜索、可分页、可按零件类型（原厂/非原厂）筛选、可多选的配件列表。
- **提交混合订单时的提示:** 当用户提交一个包含两种类型零件的订单时，系统应有明确提示，如：“此操作将生成2个独立的采购单，是否继续？”
- **主机厂处理发货:** “本次发货”输入框的值应小于等于“申请数量”和“厂区库存”，并有前端校验。
- **经销商收货:** “本次入库”输入框的值应小于等于“本次发货”数量，并有前端校验。
