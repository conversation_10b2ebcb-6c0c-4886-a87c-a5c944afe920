# 零件采购管理页面开发任务清单

## 一、项目概述
本文档记录零件采购管理模块的开发任务，包括经销商端和主机厂端的完整功能实现。

## 二、新增文件说明

### 2.1 视图文件
| 文件路径 | 作用说明 |
|---------|---------|
| `src/views/parts/purchase/dealer/index.vue` | 经销商端订单列表页 |
| `src/views/parts/purchase/dealer/create.vue` | 经销商端新建订单页 |
| `src/views/parts/purchase/dealer/edit.vue` | 经销商端编辑订单页 |
| `src/views/parts/purchase/dealer/components/DealerDashboard.vue` | 经销商端统计看板 |
| `src/views/parts/purchase/dealer/components/DealerOrderTable.vue` | 经销商端订单列表表格 |
| `src/views/parts/purchase/dealer/components/DealerOrderForm.vue` | 经销商端订单表单 |
| `src/views/parts/purchase/dealer/components/DealerPartSelector.vue` | 经销商端配件选择器 |
| `src/views/parts/purchase/dealer/components/DealerOrderDetail.vue` | 经销商端订单详情 |
| `src/views/parts/purchase/dealer/components/DealerReceiptModal.vue` | 经销商端收货弹窗 |
| `src/views/parts/purchase/oem/index.vue` | 主机厂端审批列表页 |
| `src/views/parts/purchase/oem/components/OemDashboard.vue` | 主机厂端统计看板 |
| `src/views/parts/purchase/oem/components/OemApprovalTable.vue` | 主机厂端审批列表表格 |
| `src/views/parts/purchase/oem/components/OemOrderDetail.vue` | 主机厂端订单详情 |
| `src/views/parts/purchase/oem/components/OemApprovalModal.vue` | 主机厂端审批弹窗 |
| `src/views/parts/purchase/oem/components/OemShipmentModal.vue` | 主机厂端发货弹窗 |
| `src/views/parts/purchase/oem/components/OemBatchApprovalModal.vue` | 主机厂端批量审批弹窗 |
| `src/views/parts/purchase/oem/components/OemBatchShipmentModal.vue` | 主机厂端批量发货弹窗 |

### 2.2 API接口文件
| 文件路径 | 作用说明 |
|---------|---------|
| `src/api/modules/parts/purchase-dealer.ts` | 经销商端采购相关API接口 |
| `src/api/modules/parts/purchase-oem.ts` | 主机厂端采购相关API接口 |

### 2.3 类型定义文件
| 文件路径 | 作用说明 |
|---------|---------|
| `src/types/parts/purchase-dealer.ts` | 经销商端采购相关类型定义 |
| `src/types/parts/purchase-oem.ts` | 主机厂端采购相关类型定义 |

### 2.4 其他配置文件
| 文件路径 | 作用说明 |
|---------|---------|
| `src/stores/modules/parts/purchase-dealer.ts` | 经销商端状态管理 |
| `src/stores/modules/parts/purchase-oem.ts` | 主机厂端状态管理 |
| `src/mock/data/parts/purchase-dealer.ts` | 经销商端Mock数据 |
| `src/mock/data/parts/purchase-oem.ts` | 主机厂端Mock数据 |

## 三、开发任务清单

### 阶段一：基础架构搭建（优先级：P0）

#### 1.1 创建目录结构
- [ ] 创建 `src/views/parts/purchase/` 目录
- [ ] 创建 `src/views/parts/purchase/dealer/` 子目录
- [ ] 创建 `src/views/parts/purchase/dealer/components/` 子目录
- [ ] 创建 `src/views/parts/purchase/oem/` 子目录
- [ ] 创建 `src/views/parts/purchase/oem/components/` 子目录

#### 1.2 API接口开发
- [ ] 创建 `src/api/modules/parts/purchase-dealer.ts`
  - [ ] 实现获取订单列表接口
  - [ ] 实现创建订单接口
  - [ ] 实现更新订单接口
  - [ ] 实现删除订单接口
  - [ ] 实现提交审核接口
  - [ ] 实现确认收货接口
- [ ] 创建 `src/api/modules/parts/purchase-oem.ts`
  - [ ] 实现获取待审批列表接口
  - [ ] 实现审批订单接口
  - [ ] 实现批量审批接口
  - [ ] 实现发货接口
  - [ ] 实现批量发货接口
  - [ ] 实现获取发货历史接口

#### 1.3 类型定义
- [ ] 创建 `src/types/parts/purchase-dealer.ts`
  - [ ] 定义采购订单类型
  - [ ] 定义订单明细类型
  - [ ] 定义订单状态枚举
  - [ ] 定义查询参数类型
  - [ ] 定义收货记录类型
- [ ] 创建 `src/types/parts/purchase-oem.ts`
  - [ ] 定义审批请求类型
  - [ ] 定义发货请求类型
  - [ ] 定义审批记录类型
  - [ ] 定义发货记录类型

#### 1.4 Mock数据配置
- [ ] 创建 `src/mock/data/parts/purchase-dealer.ts`
  - [ ] 实现订单列表Mock数据
  - [ ] 实现配件列表Mock数据
  - [ ] 实现仓库列表Mock数据
- [ ] 创建 `src/mock/data/parts/purchase-oem.ts`
  - [ ] 实现待审批列表Mock数据
  - [ ] 实现审批历史Mock数据
  - [ ] 实现发货历史Mock数据

#### 1.5 路由配置
- [ ] 修改 `src/router/modules/parts.ts`
  - [ ] 添加经销商端采购管理路由
  - [ ] 添加主机厂端采购管理路由
  - [ ] 配置路由权限

#### 1.6 国际化配置
- [ ] 修改 `src/locales/modules/parts/zh.json`
  - [ ] 添加采购管理相关中文翻译
  - [ ] 添加订单状态翻译
  - [ ] 添加操作按钮翻译
- [ ] 修改 `src/locales/modules/parts/en.json`
  - [ ] 添加采购管理相关英文翻译
  - [ ] 添加订单状态翻译
  - [ ] 添加操作按钮翻译

### 阶段二：经销商端开发（优先级：P0）

#### 2.1 订单列表页开发
- [ ] 实现 `dealer/index.vue` 主页面
  - [ ] 页面布局实现
  - [ ] 组件引入和注册
  - [ ] 数据初始化
- [ ] 实现 `DealerDashboard.vue` 统计看板
  - [ ] 待提交订单数量统计
  - [ ] 待收货订单数量统计
  - [ ] 本月采购金额统计
  - [ ] 本月采购数量统计
- [ ] 实现 `DealerOrderTable.vue` 订单列表
  - [ ] 表格列配置
  - [ ] 状态标签展示
  - [ ] 操作按钮配置
  - [ ] 分页功能
  - [ ] 排序功能
  - [ ] 批量操作支持

#### 2.2 订单创建/编辑功能
- [ ] 实现 `dealer/create.vue` 新建订单页
  - [ ] 页面布局
  - [ ] 表单验证
  - [ ] 草稿保存
- [ ] 实现 `dealer/edit.vue` 编辑订单页
  - [ ] 数据回显
  - [ ] 编辑限制
  - [ ] 保存更新
- [ ] 实现 `DealerOrderForm.vue` 订单表单
  - [ ] 基本信息表单
  - [ ] 收货仓库选择
  - [ ] 备注信息
  - [ ] 表单验证规则
- [ ] 实现 `DealerPartSelector.vue` 配件选择器
  - [ ] 配件搜索功能
  - [ ] 配件列表展示
  - [ ] 数量输入控制
  - [ ] 金额自动计算
  - [ ] 批量添加支持

#### 2.3 订单详情和收货功能
- [ ] 实现 `DealerOrderDetail.vue` 订单详情
  - [ ] 订单基本信息展示
  - [ ] 明细列表展示
  - [ ] 审批历史展示
  - [ ] 收货记录展示
- [ ] 实现 `DealerReceiptModal.vue` 收货弹窗
  - [ ] 收货数量输入
  - [ ] 部分收货支持
  - [ ] 库位分配
  - [ ] 收货确认

### 阶段三：主机厂端开发（优先级：P0）

#### 3.1 审批列表页开发
- [ ] 实现 `oem/index.vue` 主页面
  - [ ] 页面布局实现
  - [ ] 权限控制
  - [ ] 数据加载
- [ ] 实现 `OemDashboard.vue` 统计看板
  - [ ] 待审批订单数量
  - [ ] 待发货订单数量
  - [ ] 本月审批通过率
  - [ ] 本月发货完成率
- [ ] 实现 `OemApprovalTable.vue` 审批列表
  - [ ] 多经销商数据展示
  - [ ] 高级筛选功能
  - [ ] 批量选择支持
  - [ ] 状态颜色区分

#### 3.2 审批功能开发
- [ ] 实现 `OemOrderDetail.vue` 订单详情
  - [ ] 订单信息展示
  - [ ] 经销商信息展示
  - [ ] 明细审批调整
- [ ] 实现 `OemApprovalModal.vue` 审批弹窗
  - [ ] 审批意见输入
  - [ ] 数量调整功能
  - [ ] 驳回原因选择
- [ ] 实现 `OemBatchApprovalModal.vue` 批量审批
  - [ ] 批量选择展示
  - [ ] 统一审批意见
  - [ ] 批量处理进度

#### 3.3 发货功能开发
- [ ] 实现 `OemShipmentModal.vue` 发货弹窗
  - [ ] 承运商选择
  - [ ] 物流单号输入
  - [ ] 发货数量控制
  - [ ] 部分发货支持
- [ ] 实现 `OemBatchShipmentModal.vue` 批量发货
  - [ ] 批量订单展示
  - [ ] 统一物流信息
  - [ ] 分批发货支持

### 阶段四：优化与测试（优先级：P1）

#### 4.1 性能优化
- [ ] 实现列表虚拟滚动
- [ ] 添加数据缓存机制
- [ ] 优化大数据查询
- [ ] 实现懒加载

#### 4.2 用户体验优化
- [ ] 添加加载状态提示
- [ ] 实现操作确认对话框
- [ ] 优化错误提示
- [ ] 添加操作成功反馈
- [ ] 实现快捷键支持

#### 4.3 权限和安全
- [ ] 实现按钮级权限控制
- [ ] 添加数据权限过滤
- [ ] 实现操作日志记录
- [ ] 添加敏感操作二次确认

## 四、技术要点备忘

1. **状态管理**：使用Pinia管理列表数据和筛选条件
2. **组件通信**：优先使用Props/Emit，复杂场景使用Store
3. **错误处理**：统一使用ElMessage展示错误信息
4. **加载状态**：使用v-loading指令处理加载状态
5. **表单验证**：使用Element Plus的Form组件内置验证
6. **国际化**：所有文本使用i18n，不要硬编码
7. **权限控制**：使用v-permission指令控制按钮显示

## 五、开发顺序建议

1. 先完成基础架构（API、类型、Mock）
2. 再开发经销商端列表页面
3. 然后开发创建/编辑功能
4. 接着开发主机厂端功能
5. 最后进行整体优化和测试

## 六、注意事项

1. 严格遵循项目技术规范
2. 保持代码风格一致性
3. 注重代码可维护性
4. 做好错误处理
5. 保证国际化完整性
6. 注意性能优化
7. 确保权限控制到位