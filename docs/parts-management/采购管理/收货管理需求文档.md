# 收货管理需求文档

## 🎯 业务背景

### 现状分析
当前系统的叫料单管理缺少专门的收货管理功能，用户需要：
- 查看已发货叫料单的收货进度
- 管理收货单的处理状态
- 核对收货明细并确认收货
- 跟踪整体收货完成情况

### 业务场景
1. **厂家直接配送**: 主机厂直接向经销商门店配送零件，无第三方物流
2. **多批次发货**: 一个叫料单可能分多次发货，产生多个收货单
3. **收货状态管理**: 需要区分待处理和已处理的收货单
4. **收货异常处理**: 处理短缺、破损等收货异常情况

## 🚀 功能需求

### 核心功能

#### 1. 收货管理页面入口
- **触发条件**: 叫料单状态为"在途"、"部分收货"、"完成"时
- **入口位置**: 叫料单列表页的"查看"按钮
- **路由设计**: `/parts/purchase/dealer/:id/receipt`

#### 2. 收货管理主页面
- **叫料基本信息展示**: 集中显示叫料单的所有基本信息
- **当前入库情况**: 显示收货进度和统计信息
- **收货单列表**: 分类显示待处理和已处理收货单
- **操作功能**: 刷新数据、导出报表

#### 3. 收货单明细管理
- **收货单详情查看**: 显示收货单基本信息和零件明细


### 页面功能清单

| 功能模块 | 功能点 | 优先级 | 说明 |
|---------|-------|--------|------|
| 页面导航 | 返回按钮 | P0 | 返回叫料单列表 |
| 叫料信息 | 基本信息展示 | P0 | 叫料单号、门店、状态等 |
| 收货进度 | 进度条显示 | P0 | 可视化收货完成度 |
| 收货进度 | 统计信息 | P0 | 数量统计、时间信息 |
| 收货单列表 | 待处理收货单 | P0 | 显示待收货的收货单 |
| 收货单列表 | 已处理收货单 | P0 | 显示已完成的收货单 |
| 收货单详情 | 基本信息 | P0 | 收货单号、发货时间等 |
| 收货单详情 | 零件明细 | P0 | 零件列表、数量、状态 |



## 🎨 界面设计

### 收货管理页面线框图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│  ← 返回    叫料单收货管理 - CL202401150001                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─── 叫料基本信息 ──────────────────────────────────────────────────────┐      │
│  │ 叫料单号: CL202401150001        创建时间: 2024-01-15 10:30:00           │      │
│  │ 门店名称: A经销商-北京总店        期望到货: 2024-01-20                    │      │
│  │ 叫料状态: 部分收货              零件品种: 25种                         │      │
│  │ 零件总数: 200件                叫料总额: ¥50,000.00                   │      │
│  │ 创建人: 张经理                  仓库: 主仓库                           │      │
│  │ 备注: 紧急叫料，请优先处理                                              │      │
│  └─────────────────────────────────────────────────────────────────────┘      │
│                                                                             │
│  ┌─── 当前入库情况 ──────────────────────────────────────────────────────┐      │
│  │ 📈 收货进度总览                                                        │      │
│  │ ┌─────────────────────────────────────────────────────────────────┐   │      │
│  │ │ 已收货: 48件 ████████████████████░░░░░░░░░░░░░░░░░░░░              │   │      │
│  │ │ 在途中: 152件 ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░              │   │      │
│  │ └─────────────────────────────────────────────────────────────────┘   │      │
│  │ • 收货完成数量: 48件 / 200件      • 最近收货时间: 2024-01-15 16:30    │      │
│  │ • 待收货数量: 152件              • 预计完成时间: 2024-01-20            │      │
│  │ • 收货异常: 5件 (短缺3件，破损2件)                                   │      │
│  └─────────────────────────────────────────────────────────────────────┘      │
│                                                                             │
│  ┌─── 收货单列表 ──────────────────────────────────────────────────────┐        │
│  │                                                                     │        │
│  │  📋 待处理收货单 (2)                                                 │        │
│  │  ┌─────────────────────────────────────────────────────────────┐     │        │
│  │  │ 📦 RV202401150001  │ 发货时间: 01-16 09:00   │ 待收货  │ [查看] │     │        │
│  │  │    零件数量: 80件   │ 预计到达: 01-18 18:00   │        │       │     │        │
│  │  │    配送状态: 运输中  │                        │        │       │     │        │
│  │  └─────────────────────────────────────────────────────────────┘     │        │
│  │  ┌─────────────────────────────────────────────────────────────┐     │        │
│  │  │ 📦 RV202401160001  │ 发货时间: 01-17 14:00   │ 待收货  │ [查看] │     │        │
│  │  │    零件数量: 70件   │ 预计到达: 01-19 16:00   │        │       │     │        │
│  │  │    配送状态: 运输中  │                        │        │       │     │        │
│  │  └─────────────────────────────────────────────────────────────┘     │        │
│  │                                                                     │        │
│  │  ✅ 已处理收货单 (1)                                                 │        │
│  │  ┌─────────────────────────────────────────────────────────────┐     │        │
│  │  │ 📦 RV202401140001  │ 收货时间: 01-15 16:30   │ 已完成  │ [查看] │     │        │
│  │  │    零件数量: 50件   │ 实收数量: 48件          │        │       │     │        │
│  │  │    异常情况: 短缺2件 │                        │        │       │     │        │
│  │  └─────────────────────────────────────────────────────────────┘     │        │
│  │                                                                     │        │
│  └─────────────────────────────────────────────────────────────────┘        │
│                                                                             │
│                              [🔄 刷新数据] [📊 导出报表]                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 收货单明细弹窗线框图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│  收货单详情 - RV202401150001                                        ✕ 关闭    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─── 收货单基本信息 ──────────────────────────────────────────────────┐      │
│  │ 收货单号: RV202401150001        发货时间: 2024-01-16 09:00:00       │      │
│  │ 零件总数: 80件                 预计到达: 2024-01-18 18:00          │      │
│  │ 发货仓库: 主机厂中央仓库         配送状态: 运输中                    │      │
│  │ 收货状态: 待收货                                                   │      │
│  └─────────────────────────────────────────────────────────────────────┘      │
│                                                                             │
│  ┌─── 收货零件明细 ──────────────────────────────────────────────────────┐    │
│  │ ┌─┬────────┬──────────────┬────┬────┬────┬────┬──────────┐           │    │
│  │ │#│零件编码  │零件名称        │应收│实收│差异│状态│备注      │           │    │
│  │ ├─┼────────┼──────────────┼────┼────┼────┼────┼──────────┤           │    │
│  │ │1│P001001 │前刹车片(原厂)   │ 20 │[20]│ 0  │✅ │        │           │    │
│  │ │2│P001002 │机油滤清器       │ 30 │[28]│-2  │⚠️ │破损2个   │           │    │
│  │ │3│P001003 │空气滤芯         │ 15 │[15]│ 0  │✅ │        │           │    │
│  │ │4│P001004 │火花塞           │ 15 │[12]│-3  │❌ │短缺3个   │           │    │
│  │ └─┴────────┴──────────────┴────┴────┴────┴────┴──────────┘           │    │
│  │                                                                     │    │
│  │ 💡 提示: 实收数量可以手动调整，差异将自动计算                            │    │
│  │ 📊 汇总: 应收80件，实收75件，差异-5件                                   │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                                                             │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 页面交互流程图

```
叫料单列表页
       │
       ▼ (点击查看)
   检查叫料单状态
       │
       ├─ 草稿/驳回 ────────────► 编辑详情页
       │
       ├─ 已提交/已审批 ──────────► 只读详情页  
       │
       └─ 在途/部分收货/完成 ─────► 收货管理页
                                      │
                                      ▼
                              ┌─────────────────┐
                              │  获取收货单列表   │
                              │  获取统计信息    │
                              └─────────────────┘
                                      │
                                      ▼
                              ┌─────────────────┐
                              │  渲染页面       │
                              │  • 待处理收货单  │
                              │  • 已处理收货单  │  
                              │  • 底部统计区域  │
                              └─────────────────┘
                                      │
                                      ▼ (点击查看详情)
                              ┌─────────────────┐
                              │ 收货单明细弹窗   │
                              │ • 基本信息      │
                              │ • 零件明细表格   │
                              │   │
                              └─────────────────┘
                                      
#### 优化后的收货弹窗线框图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│  收货单详情 - RV202401150001                                        ✕ 关闭    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─── 选择发货单 ──────────────────────────────────────────────────────┐      │
│  │ ○ SH20250728005  │ 发货时间: 2024-01-16 09:00   │ 待收货  │ 顺丰速运  │      │
│  │ ● SH20250729001  │ 发货时间: 2024-01-17 14:00   │ 待收货  │ 圆通快递  │      │
│  └─────────────────────────────────────────────────────────────────────┘      │
│                                                                             │
│  ┌─── 核对入库明细 (发货单: SH20250729001) ─────────────────────────────┐      │
│  │ ┌─┬────────┬──────────────┬────┬────┬────┬────────┬──────────┐         │      │
│  │ │#│零件编码  │零件名称        │应收│实收│差异│状态     │备注      │         │      │
│  │ ├─┼────────┼──────────────┼────┼────┼────┼────────┼──────────┤         │      │
│  │ │1│P10001  │火花塞          │ 10 │[10]│ 0  │[正常▼] │          │         │      │
│  │ │2│P10003  │刹车片前        │ 20 │[ 8]│-12 │[短缺▼] │缺货12个   │         │      │
│  │ │3│P10008  │雨刮片          │ 30 │[28]│-2  │[破损▼] │破损2个   │         │      │
│  │ └─┴────────┴──────────────┴────┴────┴────┴────────┴──────────┘         │      │
│  │                                                                       │      │
│  │ 💡 说明: 实收数量可手动调整，差异自动计算，异常情况请选择状态并填写备注        │      │
│  │ 📊 汇总: 应收60件，实收46件，差异-14件                                   │      │
│  │                                                                       │      │
│  │ [🔄 一键全收]  [✅ 批量设为正常]                                          │      │
│  └─────────────────────────────────────────────────────────────────────┘      │
│                                                                             │
│  ┌─── 入库信息 ──────────────────────────────────────────────────────┐      │
│  │ 收货日期: [2024-01-20 ▼]     经手人: [张三        ]                  │      │
│  └─────────────────────────────────────────────────────────────────────┘      │
│                                                                             │
│                     [取消]  [确认收货]                                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 状态选择器设计

```
收货状态下拉菜单:
┌─────────────────┐
│ ✅ 正常         │ (绿色，差异=0时默认)
│ ⚠️ 短缺         │ (橙色，差异<0时提示)  
│ 💔 破损         │ (红色，需填写备注)
│ ❌ 拒收         │ (灰色，需填写备注)
└─────────────────┘
```
