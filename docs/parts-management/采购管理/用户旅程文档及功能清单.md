# 采购管理-用户旅程与功能清单 (v2.1)

**说明:** 此版本根据产品经理最新需求进行调整，统一了业务术语，优化了经销商端操作流程。

---

## 1. 经销商端 - 用户旅程

**角色:** 经销商配件管理员 (小王)
**目标:** 快速为门店申请一批缺货配件，并跟踪到货情况。

| 步骤 | 行为 (小王的操作) | 触点 (系统界面) | 想法和感受 | 改进机会 |
| :--- | :--- | :--- | :--- | :--- |
| 1 | 登录DMS，进入叫料单管理。 | 登录页、菜单 | "看看上次叫的货到哪了。" | 在首页提供叫料单状态的快捷入口。 |
| 2 | 发现有配件需要补货，点击"新建叫料单"。 | 列表页 | "库存快不够了，得赶紧申请。" | 系统根据库存预警自动生成建议叫料单。 |
| 3 | 在新建页面，点击"添加配件"，在一个模态框内，先筛选"原厂零件"并添加，然后切换筛选至"非原厂零件"并添加。 | 新建叫料单页 | "太方便了，不用创建两个单子，在一个地方就能把所有要申请的配件都选好。" | 支持批量选择配件，提高效率。 |
| 4 | 填写期望到货日期和备注，点击提交。系统提示“将生成2个独立订单”，小王确认。 | 新建叫料单页 | "提示很清晰，我知道这会是两个单，一个发给厂里，一个发给供应商。" | 提交后清晰告知生成的两个叫料单号及其分别对应的供应商。 |
| 5 | 几天后，在列表页看到叫料单状态变为"已发货"。 | 列表页 | "太好了，货已经在路上了。" | 点击状态可直接查看物流信息。 |
| 6 | 点击"查看"，进入详情，看到了厂端填写的物流单号。 | 叫料单详情页 | "我查查物流，明天应该能到。" | 物流信息内嵌显示，无需跳转。 |
| 7 | 收到货物后，找到对应叫料单，点击"收货"。 | 列表页 | "货到了，点一下看对不对。" | - |
| 8 | 在收货页面，选择对应的发货单，核对本次到货数量与发货单是否一致，确认入库。 | 收货页面 | "数量都对，可以入库了。" | 支持扫码收货，提高效率。 |
| 9 | 叫料单状态变为"部分收货"，等待剩余配件发货。 | 列表页 | "还差几个，等下一批发货了。" | - |

---

## 2. 主机厂端 - 用户旅程

**角色:** 主机厂零件专员 (陈经理)
**目标:** 高效处理来自全国各经销商的配件采购申请。

| 步骤 | 行为 (陈经理的操作) | 触点 (系统界面) | 想法和感受 | 改进机会 |
| :--- | :--- | :--- | :--- | :--- |
| 1 | 登录DMS，进入采购审批模块。 | 登录页、菜单 | "看看今天有多少新订单。" | 桌面提供待办事项提醒。 |
| 2 | 在待审核列表中，选择一个叫料单进行审批。 | 审批列表页 | "这个经销商的申请量还挺大。" | 列表页直接显示叫料单的关键信息（如总金额、配件种类数）。 |
| 3 | 在审批页面，查看叫料单详情，确认无误后点击"通过"。 | 审批详情页 | "申请合理，通过。" | - |
| 4 | 叫料单进入待发货列表。点击"处理发货"。 | 待发货列表 | "让我看看库存够不够。" | 系统自动进行库存预检查，并给出建议。 |
| 5 | 系统提示某个配件库存不足，只能部分发货。 | 发货处理页 | "只能先发一部分了。" | 界面清晰展示可发货数量和缺货数量。 |
| 6 | 填写本次发货数量、选择承运商、录入物流单号，确认发货。 | 发货处理页 | "操作完成，通知仓库备货。" | 与WMS（仓库管理系统）打通，自动生成出库任务。 |
| 7 | 叫料单状态变为"部分发货"，等待库存补充后再处理剩余部分。 | 列表页 | "等新库存到了再发剩下的。" | - |

---

## 3. 功能清单 (Feature Checklist)

### 经销商端

| 模块 | 功能点 | 优先级 | 状态 |
| :--- | :--- | :--- | :--- |
| **列表页** | 查看所有叫料单及其状态 | P0 | To Do |
| | 按多种条件筛选和搜索叫料单 | P0 | To Do |
| | 新建叫料单 | P0 | To Do |
| | 对草稿状态的叫料单进行编辑/删除 | P0 | To Do |
| | 对已提交状态的叫料单进行作废操作 | P0 | To Do |
| | 跟踪已发货叫料单的物流信息 | P1 | To Do |
| **新建/编辑页** | 展示门店名称（只读） | P0 | To Do |
| | 设置期望到货日期 | P0 | To Do |
| | 通过模态框添加配件，支持原厂/非原厂筛选 | P0 | To Do |
| | 在一个订单草稿中支持混合添加原厂和非原厂零件 | P0 | To Do |
| | 显示配件库存详细信息（当前、可用、占用、安全库存） | P0 | To Do |
| | 智能计算建议叫料数量（安全库存-当前库存） | P0 | To Do |
| | 提交叫料单，系统根据零件类型自动拆分为多个订单 | P0 | To Do |
| **收货页** | 选择对应的发货单 | P0 | To Do |
| | 核对收货数量与发货单，显示已收货数量 | P0 | To Do |
| | 支持部分收货和全部收货 | P0 | To Do |
| | 确认入库，更新本地库存 | P0 | To Do |

### 主机厂端

| 模块 | 功能点 | 优先级 | 状态 |
| :--- | :--- | :--- | :--- |
| **审批列表页** | 查看所有待审核的叫料单 | P0 | To Do |
| | 按经销商、日期等条件筛选 | P1 | To Do |
| **审批详情页** | 查看叫料单完整信息 | P0 | To Do |
| | 通过或驳回叫料单 | P0 | To Do |
| **发货列表页** | 查看所有待发货的叫料单 | P0 | To Do |
| **发货处理页** | 系统提示库存情况 | P1 | To Do |
| | 填写本次发货数量 | P0 | To Do |
| | 录入物流信息（承运商、单号） | P0 | To Do |
| | 确认发货，生成发货单 | P0 | To Do |