# 收货管理开发设计文档

## 📋 项目概述

### 业务背景
收货管理是采购管理模块的重要环节，负责处理叫料单(采购订单)的收货确认流程。当叫料单状态为"在途"、"部分收货"、"完成"时，用户可以通过收货管理功能查看和处理相关的收货单。

### 技术架构
- **框架**: Vue 3 + TypeScript + Element Plus
- **状态管理**: Pinia
- **国际化**: Vue I18n (模块化)
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 🎯 核心设计原则

### 1. 遵循现有技术规范
- 使用 `useModuleI18n` 进行国际化管理
- 遵循现有的组件开发模式和文件结构
- API设计符合RESTful规范和现有模式
- 路由设计符合模块化规范

### 2. 用户体验优先
- 界面简洁直观，操作流程清晰
- 支持批量操作和快速操作
- 提供详细的状态反馈和错误提示
- 响应式设计，支持多设备访问

### 3. 数据一致性
- 确保收货数据与库存系统的一致性
- 提供完整的操作日志和追溯功能
- 支持异常情况的处理和回滚

## 🏗️ 系统架构设计

### 路由设计

```typescript
// 添加到 src/router/modules/parts.ts
{
  path: '/parts/purchase/dealer/:id/receipt',
  name: 'PartsPurchaseDealerReceipt',
  component: () => import('@/views/parts/purchase/dealer/receipt/index.vue'),
  meta: {
    title: 'menu.partsPurchaseDealerReceipt',
    requiresAuth: true,
    permissions: ['parts:purchase:dealer:receipt']
  }
}
```

### 目录结构

```
src/views/parts/purchase/dealer/receipt/
├── index.vue                      # 收货管理主页面
├── components/
│   ├── ReceiptHeader.vue          # 叫料单基本信息
│   ├── ReceiptProgress.vue        # 收货进度展示
│   ├── ReceiptList.vue           # 收货单列表
│   ├── ReceiptDetailModal.vue    # 收货单详情弹窗
│   └── ReceiptConfirmModal.vue   # 收货确认弹窗
└── composables/
    └── useReceipt.ts             # 收货逻辑复用
```

## 📊 数据模型设计

### 收货管理相关类型定义

```typescript
// 添加到 src/types/parts/purchase-dealer.ts

// 收货单状态
export type ReceiptStatus = 
  | 'PENDING'      // 待收货
  | 'PROCESSING'   // 收货中
  | 'COMPLETED'    // 已完成
  | 'ABNORMAL';    // 异常

// 收货异常类型
export type ReceiptAbnormalType =
  | 'SHORTAGE'     // 短缺
  | 'DAMAGE'       // 破损
  | 'WRONG_PART'   // 错发
  | 'QUALITY'      // 质量问题
  | 'OTHER';       // 其他

// 收货单基本信息
export interface ReceiptOrder {
  receiptId: string;
  receiptNo: string;
  purchaseOrderId: string;
  purchaseOrderNo: string;
  shipmentId: string;
  shipmentNo: string;
  shipmentDate: string;
  expectedArrivalDate: string;
  actualArrivalDate?: string;
  carrier: string;
  trackingNumber: string;
  status: ReceiptStatus;
  totalQuantity: number;
  receivedQuantity: number;
  abnormalQuantity: number;
  createTime: string;
  updateTime?: string;
  receiptTime?: string;
  handler?: string;
  remarks?: string;
  items: ReceiptOrderItem[];
}

// 收货单明细
export interface ReceiptOrderItem {
  id: string;
  receiptOrderId: string;
  partId: string;
  partCode: string;
  partName: string;
  partType: PartType;
  unit: string;
  orderedQuantity: number;
  shippedQuantity: number;
  receivedQuantity: number;
  abnormalQuantity: number;
  abnormalType?: ReceiptAbnormalType;
  abnormalReason?: string;
  locationId?: string;
  locationName?: string;
  remarks?: string;
}

// 收货统计信息
export interface ReceiptStatistics {
  totalOrdered: number;
  totalReceived: number;
  totalAbnormal: number;
  completionRate: number;
  pendingCount: number;
  completedCount: number;
  lastReceiptTime?: string;
  expectedCompletionTime?: string;
}

// 收货确认请求
export interface ReceiptConfirmRequest {
  receiptOrderId: string;
  receiptDate: string;
  handler: string;
  warehouseId: number;
  items: {
    id: string;
    receivedQuantity: number;
    abnormalQuantity?: number;
    abnormalType?: ReceiptAbnormalType;
    abnormalReason?: string;
    locationId?: string;
    remarks?: string;
  }[];
  remarks?: string;
}
```

### 叫料单扩展信息

```typescript
// 扩展现有的 PurchaseOrder 接口
export interface PurchaseOrderExtended extends PurchaseOrder {
  receiptStatistics?: ReceiptStatistics;
  receiptOrders?: ReceiptOrder[];
  canReceipt?: boolean;
}
```

## 🔧 API接口设计

### 收货管理API

```typescript
// 添加到 src/api/modules/parts/receipt.ts

export const receiptApi = {
  /**
   * 获取叫料单收货概览
   * @param orderId 叫料单ID
   * @returns 收货概览信息
   */
  async getReceiptOverview(orderId: string): Promise<{
    order: PurchaseOrderExtended;
    statistics: ReceiptStatistics;
    receiptOrders: ReceiptOrder[];
  }> {
    // Mock 或 实际API调用
  },

  /**
   * 获取收货单列表
   * @param orderId 叫料单ID
   * @param status 收货单状态
   * @returns 收货单列表
   */
  async getReceiptOrderList(orderId: string, status?: ReceiptStatus): Promise<ReceiptOrder[]> {
    // Mock 或 实际API调用
  },

  /**
   * 获取收货单详情
   * @param receiptOrderId 收货单ID
   * @returns 收货单详情
   */
  async getReceiptOrderDetail(receiptOrderId: string): Promise<ReceiptOrder> {
    // Mock 或 实际API调用
  },

  /**
   * 确认收货
   * @param data 收货确认数据
   * @returns 收货结果
   */
  async confirmReceipt(data: ReceiptConfirmRequest): Promise<{
    success: boolean;
    message: string;
    data: ReceiptOrder;
  }> {
    // Mock 或 实际API调用
  },

  /**
   * 获取收货异常选项
   * @returns 异常类型选项
   */
  async getAbnormalTypeOptions(): Promise<Array<{
    value: ReceiptAbnormalType;
    label: string;
  }>> {
    // Mock 或 实际API调用
  },

  /**
   * 导出收货报表
   * @param orderId 叫料单ID
   * @returns 导出文件信息
   */
  async exportReceiptReport(orderId: string): Promise<{
    downloadUrl: string;
    filename: string;
  }> {
    // Mock 或 实际API调用
  }
};
```

## 🎨 组件设计

### 1. 收货管理主页面 (index.vue)

```vue
<template>
  <div class="receipt-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button 
        type="text" 
        @click="handleBack"
        class="back-button"
      >
        <el-icon><ArrowLeft /></el-icon>
        {{ t('common.back') }}
      </el-button>
      <h1 class="page-title">
        {{ t('receipt.title') }} - {{ orderInfo?.orderNo }}
      </h1>
    </div>

    <!-- 叫料基本信息 -->
    <ReceiptHeader :order="orderInfo" />

    <!-- 收货进度 -->
    <ReceiptProgress :statistics="receiptStatistics" />

    <!-- 收货单列表 -->
    <ReceiptList 
      :pending-orders="pendingOrders"
      :completed-orders="completedOrders"
      @view-detail="handleViewDetail"
      @confirm-receipt="handleConfirmReceipt"
    />

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button 
        type="info" 
        @click="handleRefresh"
        :loading="loading"
      >
        <el-icon><Refresh /></el-icon>
        {{ t('receipt.actions.refresh') }}
      </el-button>
      <el-button 
        type="primary" 
        @click="handleExport"
      >
        <el-icon><Download /></el-icon>
        {{ t('receipt.actions.export') }}
      </el-button>
    </div>

    <!-- 收货单详情弹窗 -->
    <ReceiptDetailModal
      v-model="detailModalVisible"
      :receipt-order="selectedReceiptOrder"
    />

    <!-- 收货确认弹窗 -->
    <ReceiptConfirmModal
      v-model="confirmModalVisible"
      :receipt-order="selectedReceiptOrder"
      @success="handleReceiptSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { receiptApi } from '@/api/modules/parts/receipt'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { PurchaseOrderExtended, ReceiptOrder, ReceiptStatistics } from '@/types/parts/purchase-dealer'
import { ArrowLeft, Download, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ReceiptConfirmModal from './components/ReceiptConfirmModal.vue'
import ReceiptDetailModal from './components/ReceiptDetailModal.vue'
import ReceiptHeader from './components/ReceiptHeader.vue'
import ReceiptList from './components/ReceiptList.vue'
import ReceiptProgress from './components/ReceiptProgress.vue'

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer.receipt')

// 路由
const route = useRoute()
const router = useRouter()
const orderId = route.params.id as string

// 响应式数据
const loading = ref(false)
const orderInfo = ref<PurchaseOrderExtended>()
const receiptStatistics = ref<ReceiptStatistics>()
const receiptOrders = ref<ReceiptOrder[]>([])
const detailModalVisible = ref(false)
const confirmModalVisible = ref(false)
const selectedReceiptOrder = ref<ReceiptOrder>()

// 计算属性
const pendingOrders = computed(() => 
  receiptOrders.value.filter(order => order.status === 'PENDING' || order.status === 'PROCESSING')
)

const completedOrders = computed(() => 
  receiptOrders.value.filter(order => order.status === 'COMPLETED' || order.status === 'ABNORMAL')
)

// 方法
const fetchReceiptData = async () => {
  try {
    loading.value = true
    const result = await receiptApi.getReceiptOverview(orderId)
    orderInfo.value = result.order
    receiptStatistics.value = result.statistics
    receiptOrders.value = result.receiptOrders
  } catch (error) {
    console.error('获取收货数据失败:', error)
    ElMessage.error(t('messages.loadDataFailed'))
  } finally {
    loading.value = false
  }
}

const handleBack = () => {
  router.push('/parts/purchase/dealer')
}

const handleRefresh = () => {
  fetchReceiptData()
}

const handleExport = async () => {
  try {
    const result = await receiptApi.exportReceiptReport(orderId)
    // 触发下载
    const link = document.createElement('a')
    link.href = result.downloadUrl
    link.download = result.filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success(t('messages.exportSuccess'))
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error(t('messages.exportFailed'))
  }
}

const handleViewDetail = (receiptOrder: ReceiptOrder) => {
  selectedReceiptOrder.value = receiptOrder
  detailModalVisible.value = true
}

const handleConfirmReceipt = (receiptOrder: ReceiptOrder) => {
  selectedReceiptOrder.value = receiptOrder
  confirmModalVisible.value = true
}

const handleReceiptSuccess = () => {
  fetchReceiptData()
  ElMessage.success(t('messages.receiptSuccess'))
}

// 生命周期
onMounted(() => {
  fetchReceiptData()
})
</script>

<style scoped lang="scss">
.receipt-management-page {
  padding: 20px;
  background: var(--el-bg-color-page);
  min-height: 100vh;

  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
    .back-button {
      margin-right: 16px;
    }
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 24px;
  }
}
</style>
```

### 2. 叫料单基本信息组件 (ReceiptHeader.vue)

```vue
<template>
  <el-card class="receipt-header" shadow="hover">
    <template #header>
      <div class="card-header">
        <h3>{{ t('receipt.orderBasicInfo') }}</h3>
      </div>
    </template>
    
    <div class="order-info-grid" v-if="order">
      <div class="info-item">
        <label>{{ t('receipt.orderNo') }}:</label>
        <span class="value">{{ order.orderNo }}</span>
      </div>
      
      <div class="info-item">
        <label>{{ t('receipt.createTime') }}:</label>
        <span class="value">{{ formatDateTime(order.createTime) }}</span>
      </div>
      
      <div class="info-item">
        <label>{{ t('receipt.storeName') }}:</label>
        <span class="value">{{ order.dealerStoreInfo?.storeName }}</span>
      </div>
      
      <div class="info-item">
        <label>{{ t('receipt.expectedDelivery') }}:</label>
        <span class="value">{{ formatDate(order.expectedDeliveryDate) }}</span>
      </div>
      
      <div class="info-item">
        <label>{{ t('receipt.orderStatus') }}:</label>
        <el-tag :type="getStatusType(order.status)">
          {{ getStatusText(order.status) }}
        </el-tag>
      </div>
      
      <div class="info-item">
        <label>{{ t('receipt.partVarieties') }}:</label>
        <span class="value">{{ order.itemCount }}{{ t('receipt.varieties') }}</span>
      </div>
      
      <div class="info-item">
        <label>{{ t('receipt.totalQuantity') }}:</label>
        <span class="value">{{ getTotalQuantity(order) }}{{ t('receipt.pieces') }}</span>
      </div>
      
      <div class="info-item">
        <label>{{ t('receipt.totalAmount') }}:</label>
        <span class="value amount">¥{{ formatAmount(order.totalAmount) }}</span>
      </div>
      
      <div class="info-item">
        <label>{{ t('receipt.warehouse') }}:</label>
        <span class="value">{{ order.warehouseName }}</span>
      </div>
      
      <div class="info-item full-width" v-if="order.remark">
        <label>{{ t('receipt.remark') }}:</label>
        <span class="value">{{ order.remark }}</span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { PurchaseOrderExtended } from '@/types/parts/purchase-dealer'

// Props
interface Props {
  order?: PurchaseOrderExtended
}

defineProps<Props>()

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer.receipt')

// 工具方法
const formatDateTime = (dateStr: string): string => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const formatDate = (dateStr?: string): string => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

const getTotalQuantity = (order: PurchaseOrderExtended): number => {
  return order.items.reduce((sum, item) => sum + item.quantity, 0)
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'shipped': 'primary',
    'partiallyShipped': 'warning',
    'partiallyReceived': 'warning',
    'received': 'success'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  return t(`status.${status}`) || status
}
</script>

<style scoped lang="scss">
.receipt-header {
  margin-bottom: 20px;
  
  .card-header {
    h3 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .order-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    
    .info-item {
      display: flex;
      align-items: center;
      
      &.full-width {
        grid-column: 1 / -1;
        align-items: flex-start;
        
        label {
          margin-top: 4px;
        }
      }
      
      label {
        color: var(--el-text-color-regular);
        margin-right: 8px;
        min-width: 80px;
        font-weight: 500;
      }
      
      .value {
        color: var(--el-text-color-primary);
        font-weight: 600;
        
        &.amount {
          color: var(--el-color-primary);
          font-size: 16px;
        }
      }
    }
  }
}
</style>
```

### 3. 收货进度组件 (ReceiptProgress.vue)

```vue
<template>
  <el-card class="receipt-progress" shadow="hover">
    <template #header>
      <div class="card-header">
        <h3>{{ t('receipt.currentReceiptStatus') }}</h3>
      </div>
    </template>
    
    <div class="progress-content" v-if="statistics">
      <!-- 进度条 -->
      <div class="progress-section">
        <h4>{{ t('receipt.progressOverview') }}</h4>
        <div class="progress-bars">
          <div class="progress-item">
            <div class="progress-info">
              <span class="label">{{ t('receipt.received') }}: {{ statistics.totalReceived }}{{ t('receipt.pieces') }}</span>
              <span class="percentage">{{ (statistics.completionRate * 100).toFixed(1) }}%</span>
            </div>
            <el-progress 
              :percentage="statistics.completionRate * 100"
              color="#67C23A"
              :show-text="false"
            />
          </div>
          
          <div class="progress-item">
            <div class="progress-info">
              <span class="label">{{ t('receipt.inTransit') }}: {{ statistics.totalOrdered - statistics.totalReceived }}{{ t('receipt.pieces') }}</span>
            </div>
            <el-progress 
              :percentage="((statistics.totalOrdered - statistics.totalReceived) / statistics.totalOrdered) * 100"
              color="#E6A23C"
              :show-text="false"
            />
          </div>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">{{ t('receipt.completedQuantity') }}</div>
            <div class="stat-value">{{ statistics.totalReceived }} / {{ statistics.totalOrdered }}{{ t('receipt.pieces') }}</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">{{ t('receipt.lastReceiptTime') }}</div>
            <div class="stat-value">{{ formatDateTime(statistics.lastReceiptTime) }}</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">{{ t('receipt.pendingQuantity') }}</div>
            <div class="stat-value">{{ statistics.totalOrdered - statistics.totalReceived }}{{ t('receipt.pieces') }}</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">{{ t('receipt.expectedCompletion') }}</div>
            <div class="stat-value">{{ formatDate(statistics.expectedCompletionTime) }}</div>
          </div>
          
          <div class="stat-item abnormal" v-if="statistics.totalAbnormal > 0">
            <div class="stat-label">{{ t('receipt.abnormalQuantity') }}</div>
            <div class="stat-value">{{ statistics.totalAbnormal }}{{ t('receipt.pieces') }}</div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { ReceiptStatistics } from '@/types/parts/purchase-dealer'

// Props
interface Props {
  statistics?: ReceiptStatistics
}

defineProps<Props>()

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer.receipt')

// 工具方法
const formatDateTime = (dateStr?: string): string => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const formatDate = (dateStr?: string): string => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}
</script>

<style scoped lang="scss">
.receipt-progress {
  margin-bottom: 20px;
  
  .card-header {
    h3 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .progress-content {
    .progress-section {
      margin-bottom: 24px;
      
      h4 {
        margin: 0 0 16px 0;
        color: var(--el-text-color-primary);
        font-size: 16px;
      }
      
      .progress-bars {
        .progress-item {
          margin-bottom: 12px;
          
          .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            
            .label {
              color: var(--el-text-color-regular);
            }
            
            .percentage {
              color: var(--el-text-color-primary);
              font-weight: 600;
            }
          }
        }
      }
    }
    
    .stats-section {
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        
        .stat-item {
          padding: 12px;
          background: var(--el-bg-color-page);
          border-radius: 6px;
          border-left: 3px solid var(--el-color-primary);
          
          &.abnormal {
            border-left-color: var(--el-color-warning);
          }
          
          .stat-label {
            font-size: 12px;
            color: var(--el-text-color-regular);
            margin-bottom: 4px;
          }
          
          .stat-value {
            font-size: 14px;
            color: var(--el-text-color-primary);
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>
```

### 4. 收货单列表组件 (ReceiptList.vue)

```vue
<template>
  <el-card class="receipt-list" shadow="hover">
    <template #header>
      <div class="card-header">
        <h3>{{ t('receipt.receiptOrderList') }}</h3>
      </div>
    </template>
    
    <div class="receipt-sections">
      <!-- 待处理收货单 -->
      <div class="section">
        <div class="section-header">
          <h4>
            <el-icon class="pending-icon"><Clock /></el-icon>
            {{ t('receipt.pendingReceipt') }} ({{ pendingOrders.length }})
          </h4>
        </div>
        
        <div class="receipt-cards">
          <div 
            v-for="order in pendingOrders" 
            :key="order.receiptId"
            class="receipt-card pending"
          >
            <div class="card-header">
              <div class="receipt-info">
                <span class="receipt-no">📦 {{ order.receiptNo }}</span>
                <el-tag :type="getStatusTagType(order.status)" size="small">
                  {{ getStatusText(order.status) }}
                </el-tag>
              </div>
              <div class="card-actions">
                <el-button 
                  type="text" 
                  size="small" 
                  @click="$emit('viewDetail', order)"
                >
                  {{ t('receipt.actions.view') }}
                </el-button>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="$emit('confirmReceipt', order)"
                  v-if="order.status === 'PENDING'"
                >
                  {{ t('receipt.actions.confirm') }}
                </el-button>
              </div>
            </div>
            
            <div class="card-content">
              <div class="info-row">
                <span class="label">{{ t('receipt.shipmentDate') }}:</span>
                <span class="value">{{ formatDate(order.shipmentDate) }}</span>
              </div>
              <div class="info-row">
                <span class="label">{{ t('receipt.expectedArrival') }}:</span>
                <span class="value">{{ formatDate(order.expectedArrivalDate) }}</span>
              </div>
              <div class="info-row">
                <span class="label">{{ t('receipt.partQuantity') }}:</span>
                <span class="value">{{ order.totalQuantity }}{{ t('receipt.pieces') }}</span>
              </div>
              <div class="info-row">
                <span class="label">{{ t('receipt.carrier') }}:</span>
                <span class="value">{{ order.carrier }}</span>
              </div>
            </div>
          </div>
          
          <div v-if="pendingOrders.length === 0" class="empty-state">
            <el-empty :description="t('receipt.noPendingOrders')" />
          </div>
        </div>
      </div>
      
      <!-- 已处理收货单 -->
      <div class="section">
        <div class="section-header">
          <h4>
            <el-icon class="completed-icon"><Check /></el-icon>
            {{ t('receipt.completedReceipt') }} ({{ completedOrders.length }})
          </h4>
        </div>
        
        <div class="receipt-cards">
          <div 
            v-for="order in completedOrders" 
            :key="order.receiptId"
            class="receipt-card completed"
          >
            <div class="card-header">
              <div class="receipt-info">
                <span class="receipt-no">📦 {{ order.receiptNo }}</span>
                <el-tag :type="getStatusTagType(order.status)" size="small">
                  {{ getStatusText(order.status) }}
                </el-tag>
              </div>
              <div class="card-actions">
                <el-button 
                  type="text" 
                  size="small" 
                  @click="$emit('viewDetail', order)"
                >
                  {{ t('receipt.actions.view') }}
                </el-button>
              </div>
            </div>
            
            <div class="card-content">
              <div class="info-row">
                <span class="label">{{ t('receipt.receiptTime') }}:</span>
                <span class="value">{{ formatDateTime(order.receiptTime) }}</span>
              </div>
              <div class="info-row">
                <span class="label">{{ t('receipt.actualReceived') }}:</span>
                <span class="value">{{ order.receivedQuantity }}{{ t('receipt.pieces') }}</span>
              </div>
              <div class="info-row" v-if="order.abnormalQuantity > 0">
                <span class="label">{{ t('receipt.abnormalSituation') }}:</span>
                <span class="value abnormal">{{ getAbnormalText(order) }}</span>
              </div>
              <div class="info-row" v-if="order.handler">
                <span class="label">{{ t('receipt.handler') }}:</span>
                <span class="value">{{ order.handler }}</span>
              </div>
            </div>
          </div>
          
          <div v-if="completedOrders.length === 0" class="empty-state">
            <el-empty :description="t('receipt.noCompletedOrders')" />
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { ReceiptOrder } from '@/types/parts/purchase-dealer'
import { Check, Clock } from '@element-plus/icons-vue'

// Props
interface Props {
  pendingOrders: ReceiptOrder[]
  completedOrders: ReceiptOrder[]
}

defineProps<Props>()

// Emits
defineEmits<{
  viewDetail: [order: ReceiptOrder]
  confirmReceipt: [order: ReceiptOrder]
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer.receipt')

// 工具方法
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateStr?: string): string => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING': 'warning',
    'PROCESSING': 'primary',
    'COMPLETED': 'success',
    'ABNORMAL': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  return t(`receiptStatus.${status}`) || status
}

const getAbnormalText = (order: ReceiptOrder): string => {
  if (order.abnormalQuantity === 0) return ''
  
  // 统计异常类型
  const abnormalTypes = order.items
    .filter(item => item.abnormalQuantity > 0)
    .map(item => item.abnormalType)
    .filter((type, index, arr) => arr.indexOf(type) === index)
  
  const typeTexts = abnormalTypes
    .map(type => t(`abnormalType.${type}`))
    .join('、')
  
  return `${typeTexts}${order.abnormalQuantity}${t('receipt.pieces')}`
}
</script>

<style scoped lang="scss">
.receipt-list {
  .card-header {
    h3 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .receipt-sections {
    .section {
      margin-bottom: 32px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-header {
        margin-bottom: 16px;
        
        h4 {
          margin: 0;
          display: flex;
          align-items: center;
          gap: 8px;
          color: var(--el-text-color-primary);
          font-size: 16px;
          
          .pending-icon {
            color: var(--el-color-warning);
          }
          
          .completed-icon {
            color: var(--el-color-success);
          }
        }
      }
      
      .receipt-cards {
        display: grid;
        gap: 16px;
        
        .receipt-card {
          border: 1px solid var(--el-border-color-light);
          border-radius: 8px;
          padding: 16px;
          background: var(--el-bg-color);
          transition: all 0.3s ease;
          
          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
          }
          
          &.pending {
            border-left: 4px solid var(--el-color-warning);
          }
          
          &.completed {
            border-left: 4px solid var(--el-color-success);
          }
          
          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            .receipt-info {
              display: flex;
              align-items: center;
              gap: 12px;
              
              .receipt-no {
                font-weight: 600;
                color: var(--el-text-color-primary);
              }
            }
            
            .card-actions {
              display: flex;
              gap: 8px;
            }
          }
          
          .card-content {
            .info-row {
              display: flex;
              margin-bottom: 8px;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              .label {
                color: var(--el-text-color-regular);
                min-width: 100px;
                font-size: 13px;
              }
              
              .value {
                color: var(--el-text-color-primary);
                font-size: 13px;
                
                &.abnormal {
                  color: var(--el-color-warning);
                  font-weight: 600;
                }
              }
            }
          }
        }
        
        .empty-state {
          text-align: center;
          padding: 40px 0;
        }
      }
    }
  }
}
</style>
```

### 5. 收货确认弹窗组件 (ReceiptConfirmModal.vue)

```vue
<template>
  <el-dialog
    v-model="visible"
    :title="t('receipt.confirmTitle')"
    width="90%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="receipt-confirm-content" v-if="receiptOrder">
      <!-- 发货单选择 -->
      <div class="section">
        <h4>{{ t('receipt.selectShipment') }}</h4>
        <div class="shipment-selection">
          <el-radio-group v-model="selectedShipmentId" @change="handleShipmentChange">
            <div v-for="shipment in availableShipments" :key="shipment.shipmentId" class="shipment-option">
              <el-radio :value="shipment.shipmentId">
                <div class="shipment-info">
                  <span class="shipment-no">{{ shipment.shipmentNo }}</span>
                  <span class="shipment-date">{{ t('receipt.shipmentDate') }}: {{ formatDate(shipment.shipmentDate) }}</span>
                  <span class="shipment-status">{{ getShipmentStatusText(shipment.status) }}</span>
                  <span class="shipment-carrier">{{ shipment.carrier }}</span>
                </div>
              </el-radio>
            </div>
          </el-radio-group>
        </div>
      </div>
      
      <!-- 收货明细 -->
      <div class="section" v-if="selectedShipmentId">
        <h4>{{ t('receipt.receiptDetails', { shipmentNo: selectedShipment?.shipmentNo }) }}</h4>
        
        <el-table :data="receiptItems" border>
          <el-table-column type="index" :label="t('receipt.index')" width="60" align="center" />
          
          <el-table-column :label="t('receipt.partCode')" prop="partCode" min-width="120" />
          
          <el-table-column :label="t('receipt.partName')" prop="partName" min-width="150" />
          
          <el-table-column :label="t('receipt.orderedQty')" prop="orderedQuantity" width="100" align="center" />
          
          <el-table-column :label="t('receipt.receivedQty')" width="120" align="center">
            <template #default="{ row, $index }">
              <el-input-number
                v-model="row.receivedQuantity"
                :min="0"
                :max="row.shippedQuantity"
                size="small"
                @change="handleQuantityChange(row, $index)"
              />
            </template>
          </el-table-column>
          
          <el-table-column :label="t('receipt.difference')" width="80" align="center">
            <template #default="{ row }">
              <span :class="getDifferenceClass(row.shippedQuantity - row.receivedQuantity)">
                {{ getDifferenceText(row.shippedQuantity - row.receivedQuantity) }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column :label="t('receipt.status')" width="120" align="center">
            <template #default="{ row }">
              <DictionarySelect
                v-model="row.abnormalType"
                dictionary-type="RECEIPT_ABNORMAL_TYPE"
                :placeholder="t('receipt.statusPlaceholder')"
                size="small"
                @change="handleStatusChange(row)"
              />
            </template>
          </el-table-column>
          
          <el-table-column :label="t('receipt.remarks')" min-width="150">
            <template #default="{ row }">
              <el-input
                v-model="row.remarks"
                :placeholder="t('receipt.remarksPlaceholder')"
                size="small"
                type="textarea"
                :rows="1"
              />
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 批量操作 -->
        <div class="batch-actions">
          <el-button size="small" @click="handleBatchReceiveAll">
            <el-icon><Check /></el-icon>
            {{ t('receipt.batchReceiveAll') }}
          </el-button>
          
          <el-button size="small" @click="handleBatchSetNormal">
            <el-icon><CircleCheck /></el-icon>
            {{ t('receipt.batchSetNormal') }}
          </el-button>
        </div>
        
        <!-- 汇总信息 -->
        <div class="summary-info">
          <el-alert
            :title="getSummaryText()"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>
      
      <!-- 入库信息 -->
      <div class="section" v-if="selectedShipmentId">
        <h4>{{ t('receipt.receiptInfo') }}</h4>
        <el-form :model="receiptForm" :rules="receiptRules" ref="receiptFormRef" label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('receipt.receiptDate')" prop="receiptDate">
                <el-date-picker
                  v-model="receiptForm.receiptDate"
                  type="date"
                  :placeholder="t('receipt.receiptDatePlaceholder')"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item :label="t('receipt.handler')" prop="handler">
                <el-input
                  v-model="receiptForm.handler"
                  :placeholder="t('receipt.handlerPlaceholder')"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleCancel">{{ t('common.cancel') }}</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm"
        :loading="confirmLoading"
        :disabled="!canConfirm"
      >
        {{ t('receipt.confirmReceipt') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { receiptApi } from '@/api/modules/parts/receipt'
import DictionarySelect from '@/components/common/DictionarySelect.vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { ReceiptConfirmRequest, ReceiptOrder, ReceiptOrderItem, ShipmentOrder } from '@/types/parts/purchase-dealer'
import { Check, CircleCheck } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { computed, nextTick, reactive, ref, watch } from 'vue'

// Props
interface Props {
  modelValue: boolean
  receiptOrder?: ReceiptOrder
}

const props = defineProps<Props>()

// Emits
const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer.receipt')

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emits('update:modelValue', value)
})

const confirmLoading = ref(false)
const availableShipments = ref<ShipmentOrder[]>([])
const selectedShipmentId = ref<string>('')
const receiptItems = ref<ReceiptOrderItem[]>([])
const receiptFormRef = ref<FormInstance>()

// 表单数据
const receiptForm = reactive({
  receiptDate: new Date().toISOString().split('T')[0],
  handler: ''
})

// 表单验证规则
const receiptRules: FormRules = {
  receiptDate: [{ required: true, message: t('validation.receiptDateRequired'), trigger: 'blur' }],
  handler: [{ required: true, message: t('validation.handlerRequired'), trigger: 'blur' }]
}

// 计算属性
const selectedShipment = computed(() => 
  availableShipments.value.find(s => s.shipmentId.toString() === selectedShipmentId.value)
)

const canConfirm = computed(() => 
  selectedShipmentId.value && receiptItems.value.length > 0 && 
  receiptForm.receiptDate && receiptForm.handler
)

// 监听
watch(() => props.receiptOrder, async (order) => {
  if (order) {
    await loadShipmentData()
  }
}, { immediate: true })

// 方法
const loadShipmentData = async () => {
  if (!props.receiptOrder) return
  
  try {
    // 这里应该调用API获取相关的发货单列表
    // 暂时使用模拟数据
    availableShipments.value = [
      {
        shipmentId: 1001,
        shipmentNo: 'SH20250728005',
        purchaseOrderId: parseInt(props.receiptOrder.purchaseOrderId),
        shippingDate: '2024-01-16T09:00:00',
        carrier: '顺丰速运',
        trackingNumber: 'SF123456789',
        status: 'DELIVERED',
        items: props.receiptOrder.items.map(item => ({
          itemId: parseInt(item.id),
          shipmentOrderId: 1001,
          purchaseOrderItemId: parseInt(item.id),
          partId: item.partId,
          partCode: item.partCode,
          partName: item.partName,
          unit: item.unit,
          orderedQuantity: item.orderedQuantity,
          shippedQuantity: item.shippedQuantity,
          receivedQuantity: item.receivedQuantity
        }))
      }
    ]
  } catch (error) {
    console.error('加载发货单数据失败:', error)
    ElMessage.error(t('messages.loadShipmentFailed'))
  }
}

const handleShipmentChange = () => {
  if (!selectedShipment.value) return
  
  // 初始化收货明细
  receiptItems.value = props.receiptOrder?.items.map(item => ({
    ...item,
    receivedQuantity: item.shippedQuantity, // 默认按发货数量收货
    abnormalType: item.shippedQuantity === item.orderedQuantity ? 'NORMAL' : 'SHORTAGE',
    remarks: ''
  })) || []
}

const handleQuantityChange = (row: ReceiptOrderItem, index: number) => {
  // 自动设置状态
  if (row.receivedQuantity === row.shippedQuantity) {
    row.abnormalType = 'NORMAL'
  } else if (row.receivedQuantity < row.shippedQuantity) {
    row.abnormalType = 'SHORTAGE'
  }
  
  // 更新异常数量
  row.abnormalQuantity = row.shippedQuantity - row.receivedQuantity
}

const handleStatusChange = (row: ReceiptOrderItem) => {
  // 根据状态调整收货数量
  if (row.abnormalType === 'NORMAL') {
    row.receivedQuantity = row.shippedQuantity
    row.abnormalQuantity = 0
  }
}

const handleBatchReceiveAll = () => {
  receiptItems.value.forEach(item => {
    item.receivedQuantity = item.shippedQuantity
    item.abnormalType = 'NORMAL'
    item.abnormalQuantity = 0
    item.remarks = ''
  })
}

const handleBatchSetNormal = () => {
  receiptItems.value.forEach(item => {
    item.abnormalType = 'NORMAL'
    if (item.receivedQuantity !== item.shippedQuantity) {
      item.receivedQuantity = item.shippedQuantity
      item.abnormalQuantity = 0
    }
  })
}

const getDifferenceClass = (difference: number): string => {
  if (difference === 0) return 'normal'
  if (difference > 0) return 'shortage'
  return 'excess'
}

const getDifferenceText = (difference: number): string => {
  if (difference === 0) return '0'
  return difference > 0 ? `-${difference}` : `+${Math.abs(difference)}`
}

const getShipmentStatusText = (status: string): string => {
  return t(`shipmentStatus.${status}`) || status
}

const getSummaryText = (): string => {
  const totalOrdered = receiptItems.value.reduce((sum, item) => sum + item.orderedQuantity, 0)
  const totalReceived = receiptItems.value.reduce((sum, item) => sum + item.receivedQuantity, 0)
  const totalDifference = receiptItems.value.reduce((sum, item) => sum + item.abnormalQuantity, 0)
  
  return t('receipt.summary', {
    ordered: totalOrdered,
    received: totalReceived,
    difference: totalDifference
  })
}

const handleConfirm = async () => {
  if (!receiptFormRef.value) return
  
  try {
    await receiptFormRef.value.validate()
    
    await ElMessageBox.confirm(
      t('receipt.confirmReceiptMessage'),
      t('receipt.confirmTitle'),
      {
        type: 'warning'
      }
    )
    
    confirmLoading.value = true
    
    const requestData: ReceiptConfirmRequest = {
      receiptOrderId: props.receiptOrder!.receiptId,
      receiptDate: receiptForm.receiptDate,
      handler: receiptForm.handler,
      warehouseId: 1, // 从叫料单获取
      items: receiptItems.value.map(item => ({
        id: item.id,
        receivedQuantity: item.receivedQuantity,
        abnormalQuantity: item.abnormalQuantity,
        abnormalType: item.abnormalType,
        abnormalReason: item.remarks,
        locationId: item.locationId || '',
        remarks: item.remarks
      })),
      remarks: ''
    }
    
    await receiptApi.confirmReceipt(requestData)
    
    ElMessage.success(t('messages.receiptSuccess'))
    emits('success')
    handleCancel()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('收货确认失败:', error)
      ElMessage.error(t('messages.receiptFailed'))
    }
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
  // 重置数据
  nextTick(() => {
    selectedShipmentId.value = ''
    receiptItems.value = []
    receiptForm.receiptDate = new Date().toISOString().split('T')[0]
    receiptForm.handler = ''
  })
}

// 工具方法
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}
</script>

<style scoped lang="scss">
.receipt-confirm-content {
  .section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: var(--el-text-color-primary);
      font-size: 16px;
      font-weight: 600;
    }
    
    .shipment-selection {
      .shipment-option {
        margin-bottom: 12px;
        
        .shipment-info {
          display: flex;
          gap: 16px;
          align-items: center;
          
          .shipment-no {
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
          
          .shipment-date, .shipment-carrier {
            color: var(--el-text-color-regular);
            font-size: 13px;
          }
          
          .shipment-status {
            color: var(--el-color-primary);
            font-size: 13px;
          }
        }
      }
    }
    
    .batch-actions {
      margin: 16px 0;
      display: flex;
      gap: 12px;
    }
    
    .summary-info {
      margin-top: 16px;
    }
  }
}

:deep(.el-table) {
  .normal {
    color: var(--el-color-success);
  }
  
  .shortage {
    color: var(--el-color-warning);
  }
  
  .excess {
    color: var(--el-color-primary);
  }
}
</style>
```

## 🌐 国际化配置

### 中文配置 (src/locales/modules/parts/zh.json)

```json
{
  "purchase": {
    "dealer": {
      "receipt": {
        "title": "收货管理",
        "orderBasicInfo": "叫料基本信息",
        "currentReceiptStatus": "当前入库情况",
        "receiptOrderList": "收货单列表",
        "pendingReceipt": "待处理收货单",
        "completedReceipt": "已处理收货单",
        "progressOverview": "收货进度总览",
        "received": "已收货",
        "inTransit": "在途中",
        "pieces": "件",
        "varieties": "种",
        "completedQuantity": "收货完成数量",
        "lastReceiptTime": "最近收货时间",
        "pendingQuantity": "待收货数量",
        "expectedCompletion": "预计完成时间",
        "abnormalQuantity": "收货异常",
        "noPendingOrders": "暂无待处理收货单",
        "noCompletedOrders": "暂无已处理收货单",
        "orderNo": "叫料单号",
        "createTime": "创建时间",
        "storeName": "门店名称",
        "expectedDelivery": "期望到货",
        "orderStatus": "叫料状态",
        "partVarieties": "零件品种",
        "totalQuantity": "零件总数",
        "totalAmount": "叫料总额",
        "warehouse": "仓库",
        "remark": "备注",
        "shipmentDate": "发货时间",
        "expectedArrival": "预计到达",
        "partQuantity": "零件数量",
        "carrier": "承运商",
        "receiptTime": "收货时间",
        "actualReceived": "实收数量",
        "abnormalSituation": "异常情况",
        "handler": "经手人",
        "actions": {
          "view": "查看",
          "confirm": "收货",
          "refresh": "刷新数据",
          "export": "导出报表"
        },
        "confirmTitle": "收货确认",
        "selectShipment": "选择发货单",
        "receiptDetails": "核对入库明细 (发货单: {shipmentNo})",
        "index": "序号",
        "partCode": "配件编码",
        "partName": "配件名称",
        "orderedQty": "应收",
        "receivedQty": "实收",
        "difference": "差异",
        "status": "状态",
        "remarks": "备注",
        "statusPlaceholder": "请选择状态",
        "remarksPlaceholder": "请填写备注",
        "batchReceiveAll": "一键全收",
        "batchSetNormal": "批量设为正常",
        "summary": "汇总: 应收{ordered}件，实收{received}件，差异{difference}件",
        "receiptInfo": "入库信息",
        "receiptDate": "收货日期",
        "receiptDatePlaceholder": "请选择收货日期",
        "handlerPlaceholder": "请输入签收人",
        "confirmReceipt": "确认收货",
        "confirmReceiptMessage": "确认按当前明细进行收货操作？",
        "receiptStatus": {
          "PENDING": "待收货",
          "PROCESSING": "收货中",
          "COMPLETED": "已完成",
          "ABNORMAL": "异常"
        },
        "abnormalType": {
          "NORMAL": "正常",
          "SHORTAGE": "短缺",
          "DAMAGE": "破损",
          "WRONG_PART": "错发",
          "QUALITY": "质量问题",
          "OTHER": "其他"
        },
        "shipmentStatus": {
          "SHIPPED": "已发货",
          "IN_TRANSIT": "运输中",
          "DELIVERED": "已送达",
          "RECEIVED": "已收货"
        },
        "validation": {
          "receiptDateRequired": "请选择收货日期",
          "handlerRequired": "请输入签收人"
        },
        "messages": {
          "loadDataFailed": "获取收货数据失败",
          "loadShipmentFailed": "获取发货单数据失败",
          "receiptSuccess": "收货确认成功",
          "receiptFailed": "收货确认失败",
          "exportSuccess": "导出成功",
          "exportFailed": "导出失败"
        }
      }
    }
  }
}
```

### 英文配置 (src/locales/modules/parts/en.json)

```json
{
  "purchase": {
    "dealer": {
      "receipt": {
        "title": "Receipt Management",
        "orderBasicInfo": "Purchase Order Basic Information",
        "currentReceiptStatus": "Current Receipt Status",
        "receiptOrderList": "Receipt Order List",
        "pendingReceipt": "Pending Receipt Orders",
        "completedReceipt": "Completed Receipt Orders",
        "progressOverview": "Receipt Progress Overview",
        "received": "Received",
        "inTransit": "In Transit",
        "pieces": "pcs",
        "varieties": "varieties",
        "completedQuantity": "Completed Quantity",
        "lastReceiptTime": "Last Receipt Time",
        "pendingQuantity": "Pending Quantity",
        "expectedCompletion": "Expected Completion",
        "abnormalQuantity": "Abnormal Quantity",
        "noPendingOrders": "No pending receipt orders",
        "noCompletedOrders": "No completed receipt orders",
        "actions": {
          "view": "View",
          "confirm": "Confirm",
          "refresh": "Refresh",
          "export": "Export"
        },
        "validation": {
          "receiptDateRequired": "Please select receipt date",
          "handlerRequired": "Please enter handler name"
        },
        "messages": {
          "loadDataFailed": "Failed to load receipt data",
          "receiptSuccess": "Receipt confirmed successfully",
          "receiptFailed": "Receipt confirmation failed",
          "exportSuccess": "Export successful",
          "exportFailed": "Export failed"
        }
      }
    }
  }
}
```

## 🧩 Mock数据设计

### 收货管理Mock数据

```typescript
// src/mock/data/parts/receipt.ts

import type { ReceiptOrder, ReceiptStatistics, PurchaseOrderExtended } from '@/types/parts/purchase-dealer'

// Mock收货统计数据
export const mockReceiptStatistics: ReceiptStatistics = {
  totalOrdered: 200,
  totalReceived: 48,
  totalAbnormal: 5,
  completionRate: 0.24,
  pendingCount: 2,
  completedCount: 1,
  lastReceiptTime: '2024-01-15T16:30:00',
  expectedCompletionTime: '2024-01-20'
}

// Mock收货单数据
export const mockReceiptOrders: ReceiptOrder[] = [
  {
    receiptId: 'receipt_001',
    receiptNo: 'RV202401150001',
    purchaseOrderId: 'order_1',
    purchaseOrderNo: 'CL202401150001',
    shipmentId: 'ship_001',
    shipmentNo: 'SH20250728005',
    shipmentDate: '2024-01-16T09:00:00',
    expectedArrivalDate: '2024-01-18T18:00:00',
    carrier: '顺丰速运',
    trackingNumber: 'SF123456789',
    status: 'PENDING',
    totalQuantity: 80,
    receivedQuantity: 0,
    abnormalQuantity: 0,
    createTime: '2024-01-16T09:00:00',
    items: [
      {
        id: 'item_001',
        receiptOrderId: 'receipt_001',
        partId: 'P001001',
        partCode: 'P001001',
        partName: '前刹车片(原厂)',
        partType: 'ORIGINAL',
        unit: '个',
        orderedQuantity: 20,
        shippedQuantity: 20,
        receivedQuantity: 0,
        abnormalQuantity: 0
      },
      {
        id: 'item_002',
        receiptOrderId: 'receipt_001',
        partId: 'P001002',
        partCode: 'P001002',
        partName: '机油滤清器',
        partType: 'ORIGINAL',
        unit: '个',
        orderedQuantity: 30,
        shippedQuantity: 30,
        receivedQuantity: 0,
        abnormalQuantity: 0
      }
    ]
  },
  {
    receiptId: 'receipt_002',
    receiptNo: 'RV202401140001',
    purchaseOrderId: 'order_1',
    purchaseOrderNo: 'CL202401150001',
    shipmentId: 'ship_002',
    shipmentNo: 'SH20250727003',
    shipmentDate: '2024-01-14T14:00:00',
    expectedArrivalDate: '2024-01-16T18:00:00',
    actualArrivalDate: '2024-01-15T16:30:00',
    carrier: '圆通快递',
    trackingNumber: 'YT987654321',
    status: 'COMPLETED',
    totalQuantity: 50,
    receivedQuantity: 48,
    abnormalQuantity: 2,
    createTime: '2024-01-14T14:00:00',
    receiptTime: '2024-01-15T16:30:00',
    handler: '张三',
    items: [
      {
        id: 'item_003',
        receiptOrderId: 'receipt_002',
        partId: 'P001003',
        partCode: 'P001003',
        partName: '空气滤芯',
        partType: 'ORIGINAL',
        unit: '个',
        orderedQuantity: 25,
        shippedQuantity: 25,
        receivedQuantity: 25,
        abnormalQuantity: 0,
        abnormalType: 'NORMAL'
      },
      {
        id: 'item_004',
        receiptOrderId: 'receipt_002',
        partId: 'P001004',
        partCode: 'P001004',
        partName: '火花塞',
        partType: 'ORIGINAL',
        unit: '个',
        orderedQuantity: 25,
        shippedQuantity: 25,
        receivedQuantity: 23,
        abnormalQuantity: 2,
        abnormalType: 'SHORTAGE',
        abnormalReason: '短缺2个',
        remarks: '包装破损导致短缺'
      }
    ]
  }
]

// Mock API方法
export const getMockReceiptOverview = async (orderId: string) => {
  await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟
  
  return {
    order: mockPurchaseOrderExtended,
    statistics: mockReceiptStatistics,
    receiptOrders: mockReceiptOrders
  }
}

export const getMockReceiptOrderDetail = async (receiptOrderId: string) => {
  await new Promise(resolve => setTimeout(resolve, 300))
  
  const receiptOrder = mockReceiptOrders.find(order => order.receiptId === receiptOrderId)
  if (!receiptOrder) {
    throw new Error('收货单不存在')
  }
  
  return receiptOrder
}

export const getMockConfirmReceipt = async (data: any) => {
  await new Promise(resolve => setTimeout(resolve, 800))
  
  return {
    success: true,
    message: '收货确认成功',
    data: mockReceiptOrders[0]
  }
}
```

## 🔧 开发配置

### 路由配置更新

```typescript
// src/router/modules/parts.ts 添加收货管理路由

{
  path: 'dealer/:id/receipt',
  name: 'PartsPurchaseDealerReceipt',
  component: () => import('@/views/parts/purchase/dealer/receipt/index.vue'),
  meta: {
    title: 'menu.partsPurchaseDealerReceipt',
    requiresAuth: true,
    permissions: ['parts:purchase:dealer:receipt'],
    activeMenu: '/parts/purchase/dealer'
  }
}
```

### API配置更新

```typescript
// 更新 src/api/modules/parts/purchase-dealer.ts
// 添加跳转到收货管理的方法

/**
 * 跳转到收货管理页面的判断
 * @param order 采购订单
 * @returns 是否可以跳转到收货管理
 */
export const canGotoReceiptManagement = (order: PurchaseOrder): boolean => {
  return ['shipped', 'partiallyShipped', 'partiallyReceived', 'received'].includes(order.status)
}
```

### 组件注册

```typescript
// src/main.ts 或相关组件注册文件
// 确保收货管理相关组件能够正确加载和使用
```

## 📝 开发注意事项

### 1. 数据一致性
- 收货数据变更需要同步更新库存系统
- 异常情况处理需要完整的日志记录
- 确保收货状态与叫料单状态的一致性

### 2. 用户体验
- 提供清晰的操作流程指引
- 异常情况的明确提示和处理建议
- 支持批量操作提高效率

### 3. 错误处理
- 网络异常的友好提示
- 数据验证失败的明确说明
- 操作失败的回滚机制

### 4. 性能优化
- 大数据量的分页处理
- 图表组件的按需加载
- 适当的数据缓存策略

### 5. 安全考虑
- 收货权限的严格控制
- 敏感操作的二次确认
- 操作日志的完整记录

## 🧪 测试策略

### 1. 单元测试
- 组件逻辑测试
- 工具方法测试
- API调用测试

### 2. 集成测试
- 页面交互流程测试
- 数据流转测试
- 错误边界测试

### 3. 端到端测试
- 完整收货流程测试
- 异常情况处理测试
- 多设备兼容性测试

## 📈 后续优化方向

### 1. 功能增强
- 收货照片上传功能
- 收货质检流程
- 自动收货建议

### 2. 用户体验
- 移动端优化
- 离线收货支持
- 语音收货确认

### 3. 数据分析
- 收货效率分析
- 异常情况统计
- 供应商评估

这个设计文档提供了收货管理功能的完整技术实现方案，确保与现有系统架构的一致性，同时满足业务需求和用户体验要求。