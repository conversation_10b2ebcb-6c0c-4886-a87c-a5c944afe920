 # 安全库存设置页面功能需求文档 (PRD)

## 1. 文档概述

### 1.1 文档信息

- **文档名称：** 安全库存设置页面功能需求文档
- **版本号：** V1.0
- **创建日期：** 2025-01-25
- **目标读者：** 产品经理、前端开发工程师、后端开发工程师、测试工程师
- **需求优先级：** P1（重要功能）

### 1.2 业务背景

安全库存设置是库存管理系统的重要组成部分，用于确保关键零件在供应链中断或需求激增时能够维持正常业务运营。通过科学设置安全库存阈值，可以有效避免缺货风险，同时控制库存成本。该页面主要服务于仓库管理员和库存主管，帮助他们建立合理的库存预警机制。

### 1.3 核心价值

- **风险控制：** 通过合理的安全库存设置，降低缺货风险
- **成本优化：** 避免过度库存，减少资金占用和仓储成本
- **智能决策：** 基于历史数据提供科学的安全库存建议
- **批量管理：** 支持批量设置，提升管理效率

## 2. 业务需求

### 2.1 用户角色

#### 2.1.1 仓库管理员
- **权限：** 查看、设置、修改所有零件的安全库存
- **职责：** 日常库存管理，安全库存维护
- **使用场景：** 定期检查和调整安全库存设置

#### 2.1.2 库存主管
- **权限：** 查看、设置、修改、审批安全库存设置
- **职责：** 制定库存策略，审核重要零件的安全库存设置
- **使用场景：** 制定库存策略，审核关键零件的安全库存

### 2.2 业务流程

#### 2.2.1 单个零件安全库存设置流程
```
查看零件信息 → 分析历史消耗 → 确定设置方式 → 输入安全库存值 → 保存设置
```

#### 2.2.2 批量安全库存设置流程
```
选择零件 → 选择批量设置方式 → 预览设置结果 → 确认并应用设置
```

#### 2.2.3 智能推荐流程
```
系统分析历史数据 → 计算推荐值 → 用户确认或调整 → 保存设置
```

## 3. 功能需求

### 3.1 页面概览功能

#### 3.1.1 统计看板
- **已设置零件：** 显示已配置安全库存的零件数量和占比
- **待设置零件：** 显示未配置安全库存的零件数量
- **低于安全库存：** 显示当前库存低于安全库存的零件数量
- **平均安全库存：** 显示所有零件的平均安全库存天数

#### 3.1.2 数据统计说明
- 统计数据实时更新，反映当前库存状态
- 支持点击统计卡片快速筛选对应状态的零件
- 提供环比变化趋势，便于了解库存管理改善情况

### 3.2 查询筛选功能

#### 3.2.1 基础筛选条件
- **零件类别筛选：** 支持按零件类别进行筛选
- **零件编号搜索：** 支持模糊搜索零件编号
- **零件名称搜索：** 支持模糊搜索零件名称
- **设置状态筛选：** 全部、已设置、未设置、低于安全值

#### 3.2.2 高级筛选条件
- **库存状态：** 正常、预警、缺货
- **消耗频率：** 高频、中频、低频、无消耗
- **供应商类型：** 国内供应商、国外供应商
- **零件重要性：** 关键零件、一般零件

### 3.3 批量操作功能

#### 3.3.1 智能推荐设置
- **触发条件：** 选择未设置安全库存的零件
- **推荐逻辑：** 基于历史消耗、供应周期、季节性波动计算
- **推荐展示：** 显示推荐值和推荐理由
- **批量应用：** 支持一键应用所有推荐值

#### 3.3.2 批量设置安全库存
- **按比例设置：** 根据当前库存的固定比例设置
- **统一设置：** 为选中零件设置相同的安全库存值
- **按天数设置：** 根据日均消耗和指定天数计算
- **按类别设置：** 针对不同零件类别设置不同的安全库存策略

#### 3.3.3 根据类别设置
- **类别策略：** 为不同零件类别定义不同的安全库存策略
- **策略模板：** 预设常用的类别策略模板
- **批量应用：** 将类别策略应用到对应类别的所有零件

### 3.4 数据导入导出功能

#### 3.4.1 导入设置
- **文件格式：** 支持Excel格式的安全库存设置文件
- **模板下载：** 提供标准的导入模板下载
- **数据验证：** 导入前验证数据格式和逻辑正确性
- **错误处理：** 显示导入错误详情，支持部分成功导入

#### 3.4.2 导出当前设置
- **导出范围：** 支持导出当前筛选条件下的所有零件设置
- **导出格式：** Excel格式，包含完整的零件信息和安全库存设置
- **自定义字段：** 支持选择导出的字段内容

### 3.5 单项操作功能

#### 3.5.1 设置安全库存
- **设置方式：** 手动设置、智能推荐、按天数设置
- **数据分析：** 显示零件的历史消耗数据和趋势
- **推荐计算：** 基于历史数据自动计算推荐的安全库存值
- **预警设置：** 设置库存预警的天数阈值

#### 3.5.2 修改安全库存
- **修改权限：** 仅允许有权限的用户修改
- **修改记录：** 记录修改历史，包括修改人、修改时间、修改原因
- **影响分析：** 显示修改后对补货需求的影响

## 4. 页面线框图设计

### 4.1 主页面线框图

```
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│  安全库存设置                                                    [返回库存管理]         │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐    │
│  │   已设置零件    │  │   待设置零件    │  │   低于安全库存  │  │   平均安全库存  │    │
│  │      2,180      │  │       278       │  │       127       │  │       18        │    │
│  │   ────────      │  │   ────────      │  │   ────────      │  │   ────────      │    │
│  │   已配置比例    │  │   需要配置      │  │   需要补货      │  │   平均天数      │    │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘    │
│                                                                                         │
│ 🔍 筛选条件 ─────────────────────────────────────────────────────────────────────────── │
│  零件类别: [请选择零件类别 ▼]  零件编号: [请输入零件编号]  零件名称: [请输入零件名称]      │
│  设置状态: [ 全部 ] [ 已设置 ] [ 未设置 ] [ 低于安全值 ]  [搜索] [重置]                  │
│                                                                                         │
│ ⚙️ 批量操作 ─────────────────────────────────────────────────────────────────────────── │
│  [智能推荐设置] [批量设置安全库存] [根据类别设置] [导入设置] [导出当前设置]              │
│                                                                                         │
│ 📋 零件列表 ─────────────────────────────────────────────────────────────────────────── │
│ ┌─┬─────────┬─────────────┬────────┬────────┬──────────┬────────┬────────┬──────┐      │
│ │☐│零件编号 │零件名称     │当前库存│安全库存│推荐安全值│月均消耗│设置状态│操作  │      │
│ ├─┼─────────┼─────────────┼────────┼────────┼──────────┼────────┼────────┼──────┤      │
│ │☐│P10001   │前制动盘     │   15   │   10   │    12    │    8   │已设置  │修改  │      │
│ │☐│P10002   │后制动盘     │   20   │   15   │    15    │   12   │已设置  │修改  │      │
│ │☐│P10003   │刹车片前     │   8    │   15   │    18    │   15   │低于安全│修改  │      │
│ │☐│P10004   │刹车片后     │   25   │   --   │    20    │   16   │未设置  │设置  │      │
│ │☐│P10005   │机油滤清器   │   50   │   30   │    25    │   20   │已设置  │修改  │      │
│ └─┴─────────┴─────────────┴────────┴────────┴──────────┴────────┴────────┴──────┘      │
│                                                                                         │
│                                                           << < 1 2 3 4 5 > >>  10/页  │
└─────────────────────────────────────────────────────────────────────────────────────────┘
```

### 4.2 安全库存设置弹窗线框图

```
┌─────────────────────────────────────────────────────────────────┐
│  安全库存设置 - P10004 刹车片后                        [×]      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 📦 零件信息 ──────────────────────────────────────────────────── │
│  零件编号：P10004                    零件名称：刹车片后         │
│  规格型号：通用型                    所属类别：刹车系统         │
│  当前库存：25 个                     单位：个                   │
│                                                                 │
│ 📊 消耗分析 ──────────────────────────────────────────────────── │
│  近30天消耗：16 个                   日均消耗：0.53 个/天       │
│  近90天消耗：48 个                   月均消耗：16 个/月         │
│  供应周期：7 天                      最大需求：2 个/天          │
│                                                                 │
│ ⚙️ 安全库存设置 ────────────────────────────────────────────── │
│  设置方式：○ 手动设置  ● 智能推荐  ○ 按天数设置                 │
│                                                                 │
│  推荐安全库存：20 个 (基于历史消耗和供应周期计算)               │
│  设置安全库存：[     20     ] 个                               │
│                                                                 │
│  预警天数：[  7  ] 天 (当库存可维持天数低于此值时预警)          │
│                                                                 │
│ 💡 设置说明 ──────────────────────────────────────────────────── │
│  • 智能推荐基于历史消耗、供应周期和季节性波动计算                 │
│  • 建议设置为供应周期 + 安全缓冲的库存量                         │
│  • 可根据零件重要性和供应稳定性适当调整                           │
│                                                                 │
│                                    [取消]  [确认设置]           │
└─────────────────────────────────────────────────────────────────┘
```

### 4.3 批量设置弹窗线框图

```
┌─────────────────────────────────────────────────────────────────┐
│  批量设置安全库存                                      [×]      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 📋 选中零件 (5 个) ──────────────────────────────────────────── │
│ ┌───────────┬─────────────┬────────┬──────────┬────────┐        │
│ │零件编号   │零件名称     │当前库存│推荐安全值│设置值  │        │
│ ├───────────┼─────────────┼────────┼──────────┼────────┤        │
│ │P10001     │前制动盘     │   15   │    12    │ [  12] │        │
│ │P10002     │后制动盘     │   20   │    15    │ [  15] │        │
│ │P10004     │刹车片后     │   25   │    20    │ [  20] │        │
│ │P10006     │空气滤清器   │   30   │    18    │ [  18] │        │
│ │P10007     │燃油滤清器   │   40   │    22    │ [  22] │        │
│ └───────────┴─────────────┴────────┴──────────┴────────┘        │
│                                                                 │
│ ⚙️ 批量设置选项 ────────────────────────────────────────────── │
│  ○ 使用推荐值    ● 按比例设置    ○ 统一设置    ○ 按天数设置     │
│                                                                 │
│  设置比例：当前库存的 [  80  ] %                               │
│  (将根据各零件当前库存按比例计算安全库存)                       │
│                                                                 │
│ 🔄 预览结果 ──────────────────────────────────────────────────── │
│  将设置 5 个零件的安全库存，平均安全库存：17.4 个               │
│  预计影响 3 个零件需要补货，2 个零件高于安全值                  │
│                                                                 │
│                                    [取消]  [批量应用]           │
└─────────────────────────────────────────────────────────────────┘
```

## 5. 业务规则

### 5.1 安全库存计算规则

#### 5.1.1 智能推荐算法
- **基础公式：** 安全库存 = 平均日消耗 × (供应周期 + 安全天数)
- **季节性调整：** 根据历史数据识别季节性波动，调整推荐值
- **供应商可靠性：** 根据供应商历史表现调整安全缓冲天数
- **零件重要性：** 关键零件适当提高安全库存比例

#### 5.1.2 最小值限制
- **最小安全库存：** 不得低于1个或1天的消耗量
- **最大安全库存：** 不得超过90天的消耗量
- **零消耗零件：** 设置最小安全库存为1个

### 5.2 权限控制规则

#### 5.2.1 查看权限
- 所有有库存管理权限的用户可查看安全库存设置
- 可查看零件的消耗历史和推荐计算过程

#### 5.2.2 设置权限
- 仓库管理员可设置一般零件的安全库存
- 库存主管可设置所有零件的安全库存
- 关键零件的安全库存设置需要主管审批

#### 5.2.3 批量操作权限
- 批量设置操作需要特殊权限
- 单次批量操作不得超过100个零件
- 批量操作需要记录操作日志

### 5.3 数据验证规则

#### 5.3.1 输入验证
- 安全库存必须为正整数
- 预警天数必须为1-30之间的整数
- 导入数据必须包含必要的零件标识信息

#### 5.3.2 逻辑验证
- 安全库存不得超过仓库最大容量
- 新设置的安全库存应与历史消耗合理匹配
- 批量设置时检查是否存在异常的设置值

## 6. 异常处理

### 6.1 数据异常

#### 6.1.1 无历史消耗数据
- **处理方式：** 提示用户手动设置或使用默认值
- **默认策略：** 设置为类别平均值或最小安全库存

#### 6.1.2 供应周期数据缺失
- **处理方式：** 使用行业标准周期或类别平均周期
- **提示信息：** 明确告知用户使用的是估算值

### 6.2 操作异常

#### 6.2.1 批量操作失败
- **部分失败：** 显示成功和失败的零件清单
- **全部失败：** 显示失败原因和建议操作

#### 6.2.2 网络异常
- **自动重试：** 网络异常时自动重试3次
- **本地缓存：** 保存用户已输入的设置值

## 7. 性能要求

### 7.1 响应时间
- **页面加载：** 首次加载不超过3秒
- **查询筛选：** 查询响应时间不超过2秒
- **批量操作：** 100个零件的批量设置不超过10秒

### 7.2 数据处理能力
- **最大零件数：** 支持同时显示1000个零件
- **并发用户：** 支持50个用户同时操作
- **数据同步：** 实时同步安全库存设置变更

## 8. 验收标准

### 8.1 功能验收
- ✅ 所有筛选条件正常工作
- ✅ 智能推荐计算准确
- ✅ 批量操作功能完整
- ✅ 数据导入导出正常
- ✅ 权限控制有效

### 8.2 界面验收
- ✅ 页面布局符合设计规范
- ✅ 响应式设计适配不同屏幕
- ✅ 交互反馈及时准确
- ✅ 错误提示清晰明确

### 8.3 性能验收
- ✅ 满足响应时间要求
- ✅ 大数据量处理稳定
- ✅ 内存使用合理
- ✅ 无明显性能瓶颈