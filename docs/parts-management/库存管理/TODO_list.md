# 零件库存管理页面开发任务清单 (TODO List)

**负责人**: `cc-fe`
**审查官**: `cai-fe`

---

## 阶段一：基础建设 (Setup)

1.  **[ ] 任务1：创建文件结构**
    *   **描述**: 根据架构设计，在项目中创建所有必需的空文件和目录。
    *   **文件列表**:
        - `src/views/parts/inventory/InventoryManagementView.vue`
        - `src/views/parts/inventory/components/InventoryDashboard.vue`
        - `src/views/parts/inventory/components/InventorySearchForm.vue`
        - `src/views/parts/inventory/components/InventoryListTable.vue`
        - `src/views/parts/inventory/components/InventoryDetailModal.vue`
        - `src/views/parts/inventory/components/InventoryAdjustModal.vue`
        - `src/views/parts/inventory/components/BatchReplenishmentModal.vue`
        - `src/api/modules/parts/inventory.ts`
        - `src/mock/data/parts/inventory.ts`
        - `src/types/parts/inventory.ts`
        - `src/stores/modules/parts/inventory.ts`
        - `src/locales/modules/parts/zh.json`
        - `src/locales/modules/parts/en.json`

2.  **[ ] 任务2：定义TypeScript类型**
    *   **描述**: 在 `src/types/parts/inventory.ts` 中，根据API接口文档定义所有请求和响应的数据结构。
    *   **关键类型**: `DashboardData`, `InventoryItem`, `InventorySearchParams`, `InventoryDetail`, `InventoryAdjustPayload`, `ReplenishmentItem` 等。

3.  **[ ] 任务3：实现API服务与Mock数据**
    *   **描述**: 在 `src/api/modules/parts/inventory.ts` 中实现所有API函数。同时，在 `src/mock/data/parts/inventory.ts` 中为每个API提供对应的Mock实现。
    *   **接口列表**:
        - `getDashboardStatistics`
        - `getInventoryList`
        - `getInventoryDetail`
        - `getInventoryTrend`
        - `adjustInventory`
        - `createBatchReplenishmentOrder`

4.  **[ ] 任务4：设置Pinia状态管理**
    *   **描述**: 在 `src/stores/modules/parts/inventory.ts` 中创建Pinia store。
    *   **实现内容**:
        - **State**: `dashboardData`, `searchParams`, `tableData`, `pagination`, `loading`。
        - **Actions**: `fetchDashboardData`, `fetchInventoryList` 等异步操作。
        - **Getters**: 计算派生状态（如 `isTableEmpty`）。

5.  **[ ] 任务5：填充国际化文件**
    *   **描述**: 在 `zh.json` 和 `en.json` 中，以 `inventoryManagement` 为顶级键，添加所有页面需要的翻译文本。
    *   **内容**: 页面标题、字段标签、按钮文字、提示信息、状态文本等。

---

## 阶段二：组件开发 (Component Development)

6.  **[ ] 任务6：开发 `InventoryDashboard.vue` 组件**
    *   **描述**: 实现顶部统计卡片，从Pinia store获取数据并展示。

7.  **[ ] 任务7：开发 `InventorySearchForm.vue` 组件**
    *   **描述**: 实现搜索表单，与Pinia store中的 `searchParams` 双向绑定，并提供搜索和重置功能。

8.  **[ ] 任务8：开发 `InventoryListTable.vue` 组件**
    *   **描述**: 实现数据表格，展示库存列表。处理分页逻辑，并 `emit` 查看详情、调整库存等行内操作事件。

9.  **[ ] 任务9：开发 `InventoryDetailModal.vue` 组件**
    *   **描述**: 实现库存详情弹窗。接收 `inventoryId` 作为prop，内部调用API获取并展示详情数据和趋势图。

10. **[ ] 任务10：开发 `InventoryAdjustModal.vue` 组件**
    *   **描述**: 实现库存调整弹窗。包含一个表单用于提交调整信息，并在成功后 `emit` 事件通知父组件刷新列表。

11. **[ ] 任务11：开发 `BatchReplenishmentModal.vue` 组件**
    *   **描述**: 实现批量补货弹窗。接收选中的库存项列表，处理批量补货逻辑。

---

## 阶段三：整合与收尾 (Integration & Finalization)

12. **[ ] 任务12：开发 `InventoryManagementView.vue` 主页面**
    *   **描述**: 整合所有子组件，构建完整的页面布局。处理所有弹窗的显示/隐藏逻辑，并响应子组件发出的事件。

13. **[ ] 任务13：添加路由**
    *   **描述**: 在 `src/router/modules/parts.ts` (或相应文件) 中添加 `/parts/inventory` 路由，指向 `InventoryManagementView`。

14. **[ ] 任务14：最终审查与自检**
    *   **描述**: 遵循 `06-常见问题和解决方案.md` 中的自检清单，全面审查代码，确保所有功能符合规范和需求。
