库存管理页面功能需求文档 (PRD)

  1. 文档概述

  1.1 文档信息

  - 文档名称：库存管理页面功能需求文档
  - 版本号：V1.0
  - 创建日期：2025-01-23
  - 目标读者：前端开发工程师、后端开发工程师、测试工程师
  - 需求优先级：P0（核心功能）

  1.2 业务背景

  库存管理是汽车DMS系统的核心模块，负责实时监控零件库存状态，支持库存预警、补货申请、库存调整等关键业务操作。该页面是仓库管理
  员的主要工作界面，需要提供高效的数据展示和操作功能。

  1.3 核心价值

  - 实时监控：可视化展示库存状态，及时发现缺货和预警
  - 高效操作：支持批量操作，提升仓库管理效率
  - 精准管理：提供多维度数据查询和分析功能
  - 决策支持：通过趋势分析辅助库存管理决策

⏺ Update Todos
  ⎿  ☒ 编写库存管理页面功能需求文档-概述部分    
     ☒ 编写库存管理页面功能需求文档-页面结构
     ☒ 编写库存管理页面功能需求文档-功能详细说明
     ☒ 编写库存管理页面功能需求文档-交互设计
     ☒ 编写库存管理页面功能需求文档-数据字段映射
     ☐ 编写库存管理页面功能需求文档-技术要求

⏺ 2. 页面结构

  2.1 页面层级

  库存管理（一级菜单）
  ├── 主页面
  │   ├── 顶部统计区域
  │   ├── 搜索筛选区域
  │   ├── 功能按钮区域
  │   ├── 数据表格区域
  │   └── 分页控制区域
  ├── 弹窗页面
  │   ├── 库存详情弹窗
  │   └── 库存调整弹窗
  └── 其他功能页面（通过功能按钮跳转）
      ├── 库存盘点页面
      ├── 批量补货页面
      └── 安全库存设置页面

  2.2 页面布局结构

  2.2.1 顶部统计区域

  - 位置：页面顶部
  - 占比：高度约120px
  - 内容：4个统计卡片横向排列
  - 响应式：移动端改为2×2网格布局

  2.2.2 搜索筛选区域

  - 位置：统计区域下方
  - 占比：高度约80px
  - 内容：搜索框 + 条件筛选 + 状态快捷筛选
  - 响应式：移动端垂直排列

  2.2.3 功能按钮区域

  - 位置：搜索区域下方
  - 占比：高度约50px
  - 内容：批量选择控制 + 功能操作按钮
  - 响应式：按钮自适应折叠

  2.2.4 数据表格区域

  - 位置：页面主体区域
  - 占比：自适应剩余高度
  - 内容：库存数据表格
  - 特性：固定表头，左右固定列，中间滚动

  2.2.5 分页控制区域

  - 位置：页面底部
  - 占比：高度约50px
  - 内容：分页信息 + 页码控制 + 每页条数设置
3. 功能详细说明

  3.1 统计展示功能

  3.1.1 总库存SKU统计

  - 功能描述：显示当前门店管理的零件种类总数
  - 数据来源：调用 /api/inventory/dashboard 接口 (当前实现中 `storeId` 为硬编码 `1001`)
  - 展示内容：
    - 主数字：SKU总数（如：2,458）
    - 趋势指标：增长率（如：+5.2% ↑）
    - 趋势说明：对比上月同期
  - 刷新机制：页面加载时获取，每5分钟自动刷新
  - 点击行为：无

  3.1.2 缺货警报统计

  - 功能描述：显示当前库存为0的零件数量
  - 业务规则：current_stock = 0
  - 展示内容：
    - 图标：🔴（红色圆点）
    - 主数字：缺货数量（如：23）
    - 辅助文字：需紧急补货
  - 视觉提示：红色背景，高亮显示
  - 点击行为：快速筛选缺货商品

  3.1.3 库存预警统计

  - 功能描述：显示低于安全库存的零件数量
  - 业务规则：current_stock > 0 AND current_stock <= safety_stock
  - 展示内容：
    - 图标：⚠️（警告图标）
    - 主数字：预警数量（如：127）
    - 辅助文字：低于安全库存
  - 视觉提示：橙色背景，醒目提示
  - 点击行为：快速筛选预警商品

  3.1.4 占用价值统计

  - 功能描述：显示被占用库存的总金额
  - 计算公式：SUM(occupied_stock * purchase_price)
  - 展示内容：
    - 图标：💰（金钱图标）
    - 主数字：金额（如：¥125,000）
    - 辅助文字：待出库金额
  - 格式化：千分位分隔，保留2位小数
  - 点击行为：无

  3.2 搜索筛选功能

  3.2.1 条件搜索

  - 搜索字段：
    - 零件类别（下拉选择）
    - 零件编号（文本输入）
    - 零件名称（文本输入）
  - 搜索方式：模糊匹配
  - 搜索触发：点击"搜索"按钮
  - 重置功能：点击"重置"清空所有条件

  3.2.2 状态快速筛选

  - 筛选标签：
    - 全部状态（默认选中）
    - 🔴 缺货(23) - 显示实时数量
    - ⚠️ 预警(127) - 显示实时数量
    - ✅ 正常(2,308) - 显示实时数量
    - 📦 超储(15) - 显示实时数量
  - 交互方式：单击切换，支持多选
  - 联动效果：立即刷新表格数据

  3.3 数据列表功能

  3.3.1 列表字段说明

  | 字段名称 | 字段类型     | 展示说明               | 对齐方式 |
  |------|----------|--------------------|------|
  | 选择框  | Checkbox | 批量选择控制             | 居中   |
  | 状态   | 图标+文字    | 🔴缺货/⚠️预警/✅正常/📦超储 | 居中   |
  | 零件编号 | 文本       | 如：P10001           | 左对齐  |
  | 零件名称 | 文本       | 如：火花塞              | 左对齐  |
  | 品牌   | 文本       | 如：博世               | 左对齐  |
  | 型号   | 文本       | 如：FR7DC+           | 左对齐  |
  | 当前库存 | 数字       | 整数显示               | 右对齐  |
  | 安全库存 | 数字       | 整数显示               | 右对齐  |
  | 可用库存 | 数字       | 整数显示               | 右对齐  |
  | 占用库存 | 数字       | 整数显示               | 右对齐  |
  | 损坏库存 | 数字       | 整数显示               | 右对齐  |
  | 仓库   | 文本       | 如：主仓               | 左对齐  |
  | 盘点时间 | 日期       | 格式：MM-DD           | 居中   |
  | 操作   | 按钮组      | 详情、调整              | 居中   |

  3.3.2 表格特性

  - 固定列：选择框、状态、操作列固定
  - 横向滚动：中间数据列支持横向滚动
  - 排序功能：库存数量字段支持排序
  - 行高亮：鼠标悬停行高亮显示
  - 空数据：无数据时显示"暂无数据"提示

  3.4 批量操作功能

  3.4.1 批量选择

  - 全选控制：表头checkbox控制当页全选
  - 选中计数：实时显示"已选 N 项"
  - 选择限制：最多选择50项

  3.4.2 批量补货

  - 触发条件：至少选择1个库存项
  - 操作流程：
    a. 点击"批量补货"按钮
    b. 弹出批量补货申请弹窗
    c. 自动填充选中的库存项，并预设申请数量（如：安全库存-当前库存，最低为1）
  - 权限控制：需要补货申请权限
  (当前实现中storeId为硬编码1001)

  3.5 单项操作功能

  3.5.1 查看详情

  - 触发方式：点击"详情"按钮
  - 展示内容：
    - 零件基础信息(不含品牌、类别)
    - 库存详细信息(增加占用库存，去掉仓库、库位)
    - 30天库存变化趋势图
  - 弹窗尺寸：宽度800px，高度600px

  3.5.2 库存调整

  - 触发方式：点击"调整"按钮
  - 零件信息展示：零件编号、名称、规格型号、当前库存、可用库存、占用库存、损坏库存
  - 调整类型：
    - 增加库存
    - 减少库存
    - 设置为指定数量
  - 必填信息：
    - 调整数量
    - 调整原因(不含损坏调整)
    - 备注说明
  - 权限控制：需要库存调整权限

  3.6 其他功能入口

  3.6.1 库存盘点

  - 功能说明：启动库存盘点流程
  - 跳转页面：库存盘点页面
  - 携带参数：当前筛选条件

  3.6.2 安全库存设置

  - 功能说明：批量设置安全库存阈值
  - 跳转页面：安全库存设置页面
  - 权限要求：库存管理权限
4.1 页面加载交互

  4.1.1 初始加载

  1. 显示页面骨架屏（Skeleton）
  2. 并行请求：
     - 统计数据API
     - 列表数据API（第1页）
  3. 数据返回后渲染对应区域
  4. 加载完成移除骨架屏

  4.1.2 加载状态

  - 统计卡片：显示加载动画
  - 表格区域：显示表格骨架屏
  - 加载失败：显示重试按钮

  4.2 搜索交互设计

  4.2.1 搜索流程

  用户输入 → 点击搜索 → 显示加载 → 更新列表 → 更新统计

  4.2.2 搜索优化

  - 防抖处理：输入停止500ms后触发
  - 空结果：显示"未找到相关库存"
  - 搜索历史：记录最近5次搜索

  4.3 状态筛选交互

  4.3.1 单选模式

  - 点击效果：选中高亮，其他取消
  - 数据更新：立即刷新列表
  - URL同步：更新URL参数

  4.3.2 筛选联动

  - 筛选后更新表格数据
  - 保持其他搜索条件
  - 重置页码到第1页

  4.4 表格交互设计

  4.4.1 行操作

  - 悬停效果：整行背景色变化
  - 点击选择：点击checkbox选中
  - 双击查看：双击行查看详情

  4.4.2 列操作

  - 固定列拖拽：不可拖动
  - 数据列排序：点击表头排序
  - 列宽调整：拖拽调整列宽

  4.5 弹窗交互设计

  4.5.1 库存详情弹窗

  - 打开动画：从中心放大淡入
  - 关闭方式：
    - 点击右上角X
    - 点击遮罩层
    - ESC键关闭
  - 内容加载：
    - 基础信息立即显示
    - 趋势图异步加载

  4.5.2 库存调整弹窗

  - 表单交互：
    - 实时计算调整后库存
    - 数量输入限制（正整数）
    - 必填项实时校验
  - 提交流程：
  填写表单 → 点击确认 → 二次确认 → 提交请求 →
  成功提示 → 关闭弹窗 → 刷新列表

  4.6 批量操作交互

  4.6.1 批量选择

  - 全选逻辑：
    - 未选中：全选当页
    - 部分选中：全选当页
    - 全部选中：取消全选
  - 选择限制：
    - 超过50项提示
    - 禁止继续选择

  4.6.2 批量操作确认

  - 操作前置检查：
    - 检查选择数量
    - 检查操作权限
    - 显示确认对话框
  - 批量补货跳转：
    - 传递选中ID列表
    - 新页面预填充数据

  4.7 分页交互设计

  4.7.1 分页控制

  - 页码显示：1 2 3 ... 98 [>]
  - 快捷跳转：输入页码回车跳转
  - 每页条数：10/20/50/100可选

  4.7.2 分页状态保持

  - 切换页码保持筛选条件
  - URL参数同步更新
  - 支持浏览器前进后退

  4.8 响应式交互

  4.8.1 移动端适配

  - 统计卡片：2×2网格排列
  - 搜索区域：垂直布局
  - 表格滚动：支持左右滑动
  - 操作按钮：底部悬浮

  4.8.2 平板适配

  - 统计卡片：2×2或1×4
  - 表格列数：自适应隐藏
  - 弹窗尺寸：90%屏幕宽度

  4.9 快捷键支持

  | 快捷键    | 功能说明  |
  |--------|-------|
  | Ctrl+F | 聚焦搜索框 |
  | ESC    | 关闭弹窗  |
  | Enter  | 确认操作  |
  | Ctrl+A | 全选当页  |

  4.10 异常交互处理

  4.10.1 网络异常

  - 请求超时：显示"网络请求超时，请重试"
  - 断网提示：显示"网络连接已断开"
  - 重试机制：提供重试按钮
  (当前代码中主要通过ElMessage进行成功提示，更详细的错误处理需进一步完善)

  4.10.2 数据异常

  - 空数据：友好的空状态图示
  - 加载失败：显示错误信息和重试
  - 权限不足：提示"您没有权限执行此操作"
  
5.1 统计数据字段映射

  | 页面展示字段 | API返回字段        | 数据类型       | 说明                 |
  |--------|----------------|------------|--------------------|
  | 总库存SKU | totalSkuCount  | Integer    | 零件种类总数             |
  | SKU增长率 | skuGrowthRate  | BigDecimal | 百分比，保留1位小数         |
  | 趋势方向   | trendDirection | String     | UP/DOWN/STABLE     |
  | 缺货警报数  | shortageCount  | Integer    | current_stock=0的数量 |
  | 库存预警数  | warningCount   | Integer    | 低于安全库存的数量          |
  | 占用价值   | occupiedValue  | BigDecimal | 金额，保留2位小数          |

  5.2 列表数据字段映射

  | 页面展示字段 | API返回字段        | 数据库字段                     | 数据类型    | 备注        |
  |--------|----------------|---------------------------|---------|-----------|
  | 库存ID   | inventoryId    | inventory.id              | Long    | 主键，不展示    |
  | 状态     | stockStatus    | 计算字段                      | String  | 前端转换为图标   |
  | 零件编号   | partCode       | parts.part_code           | String  | -         |
  | 零件名称   | partName       | parts.part_name           | String  | -         |
  | 品牌     | brand          | parts.brand               | String  | -         |
  | 型号     | specification  | parts.specification       | String  | -         |
  | 当前库存   | currentStock   | inventory.current_stock   | Integer | -         |
  | 安全库存   | safetyStock    | inventory.safety_stock    | Integer | -         |
  | 可用库存   | availableStock | inventory.available_stock | Integer | -         |
  | 占用库存   | occupiedStock  | inventory.occupied_stock  | Integer | -         |
  | 损坏库存   | damagedStock   | inventory.damaged_stock   | Integer | -         |
  | 占用库存   | occupiedStock  | inventory.occupied_stock  | Integer | -         |
  | 仓库     | warehouseName  | warehouses.warehouse_name | String  | -         |
  | 盘点时间   | lastCheckTime  | inventory.last_check_time | Date    | 格式化为MM-DD |

  5.3 库存详情字段映射

  5.3.1 零件基础信息

  | 页面展示字段 | API返回字段       | 数据库字段                | 数据类型       |
  |--------|---------------|----------------------|------------|
  | 零件编号   | partCode      | parts.part_code      | String     |
  | 零件名称   | partName      | parts.part_name      | String     |
  | 规格型号   | specification | parts.specification  | String     |
  | 计量单位   | unit          | parts.unit           | String     |
  | 建议零售价  | retailPrice   | parts.retail_price   | BigDecimal |
  | 进货参考价  | purchasePrice | parts.purchase_price | BigDecimal |

  5.3.2 库存详细信息

  | 页面展示字段 | API返回字段        | 数据库字段                     | 数据类型     |
  |--------|----------------|---------------------------|----------|
  | 当前库存   | currentStock   | inventory.current_stock   | Integer  |
  | 可用库存   | availableStock | inventory.available_stock | Integer  |
  | 占用库存   | occupiedStock  | inventory.occupied_stock  | Integer  |
  | 损坏库存   | damagedStock   | inventory.damaged_stock   | Integer  |
  | 安全库存   | safetyStock    | inventory.safety_stock    | Integer  |
  | 最大库存   | maximumStock   | inventory.maximum_stock   | Integer  |
  | 库存状态   | stockStatus    | 计算字段                      | String   |
  | 货架号    | shelfNumber    | inventory.shelf_number    | String   |
  | 最后盘点时间 | lastCheckTime  | inventory.last_check_time | DateTime |
  | 盘点人    | checkPerson    | 子查询获取                     | String   |

  5.4 库存调整字段映射

  5.4.1 请求参数

  | 表单字段 | 请求参数       | 数据类型    | 校验规则       |
  |------|------------|---------|------------|
  | 调整类型 | adjustType | String  | 必填，枚举值     |
  | 调整数量 | quantity   | Integer | 必填，>0      |
  | 调整原因 | reason     | String  | 必填，枚举值     |
  | 备注   | remark     | String  | 选填，最大500字符 |

  5.4.2 调整类型枚举

  | 枚举值      | 显示文本 | 说明      |
  |----------|------|---------|
  | INCREASE | 增加库存 | 增加指定数量  |
  | DECREASE | 减少库存 | 减少指定数量  |
  | SET_TO   | 设置为  | 设置为指定数量 |

  5.4.3 调整原因枚举

  | 枚举值               | 显示文本 | 说明     |
  |-------------------|------|--------|
  | INVENTORY_CHECK   | 盘点调整 | 盘点发现差异 |
  | SYSTEM_CORRECTION | 系统纠错 | 系统错误修正 |
  | OTHER             | 其他   | 其他原因   |

  5.5 库存状态计算规则

  // 库存状态计算逻辑
  function calculateStockStatus(inventory) {
    if (inventory.currentStock === 0) {
      return 'SHORTAGE'; // 缺货
    } else if (inventory.currentStock > 0 &&
               inventory.currentStock <= inventory.safetyStock) {
      return 'WARNING'; // 预警
    } else if (inventory.currentStock > inventory.maximumStock) {
      return 'OVERSTOCKED'; // 超储
    } else {
      return 'NORMAL'; // 正常
    }
  }

  5.6 状态展示映射

  | 状态值         | 显示图标 | 显示文本 | 背景色     |
  |-------------|------|------|---------|
  | SHORTAGE    | 🔴   | 缺货   | #ff4d4f |
  | WARNING     | ⚠️   | 预警   | #faad14 |
  | NORMAL      | ✅    | 正常   | #52c41a |
  | OVERSTOCKED | 📦   | 超储   | #1890ff |

