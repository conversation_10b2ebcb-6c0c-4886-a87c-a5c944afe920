# 金融管理模块重构设计文档

## 1. 重构概述

### 1.1 重构目标
将 `financeManagement` 模块从 `src/views/financeManagement/` 重构到符合项目规范的模块化结构，实现：
- 符合页面目录结构规范的模块化组织
- 标准化MyBatisPlus分页组件使用
- 规范化数据字典实现
- 统一API响应处理方式

### 1.2 当前模块分析

#### 1.2.1 目录结构问题
```
❌ 当前结构:
src/views/financeManagement/
├── WholeVehicleCollectionManagementView.vue    # 整车收款管理
└── components/
    ├── OrderDetailDialog.vue                   # 订单详情弹窗
    └── PaymentOperationDialog.vue              # 收退款操作弹窗

✅ 目标结构:
src/views/finance/
├── wholeVehicleCollection/
│   ├── WholeVehicleCollectionView.vue          # 主页面文件（路由页面）
│   └── components/
│       ├── OrderDetailDialog.vue               # 订单详情弹窗（非路由页面）
│       ├── PaymentOperationDialog.vue          # 收退款操作弹窗
│       ├── PaymentRecordForm.vue               # 收退款记录表单
│       ├── PaymentHistoryTable.vue             # 收退款历史表格
│       └── OrderInfoSection.vue                # 订单信息区块
├── loanManagement/                             # 贷款管理（预留）
├── insuranceManagement/                        # 保险管理（预留）
└── financialReports/                           # 财务报表（预留）
src/api/modules/finance/
├── wholeVehicleCollection.ts                   # 整车收款API
├── loanManagement.ts                           # 贷款管理API（预留）
└── insuranceManagement.ts                      # 保险管理API（预留）
src/types/finance/
├── wholeVehicleCollection.d.ts                 # 整车收款类型定义
├── loanManagement.d.ts                         # 贷款管理类型定义（预留）
└── insuranceManagement.d.ts                    # 保险管理类型定义（预留）
src/mock/data/finance/
├── wholeVehicleCollection.ts                   # 整车收款Mock数据
├── loanManagement.ts                           # 贷款管理Mock数据（预留）
└── insuranceManagement.ts                      # 保险管理Mock数据（预留）
```

#### 1.2.2 MyBatisPlus分页问题分析
**当前实现问题**:
```typescript
// ❌ 分页参数不符合MyBatisPlus标准
const pagination = reactive({
  page: 1,              // ❌ 应为 pageNum
  pageSize: 20,         // ✅ 正确
  total: 0              // ✅ 正确
});

// ❌ 分页组件绑定不符合标准
<el-pagination
  v-model:current-page="pagination.page"       // ❌ 应绑定 pageNum
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
/>

// ❌ API调用参数不符合标准
const params: Partial<OrderPaymentListParams> = {
  page: pagination.page,                        // ❌ 应为 pageNum
  pageSize: pagination.pageSize
};

// ✅ 响应处理正确
tableData.value = response.result.records;     // ✅ 正确使用 result.records
pagination.total = response.result.total;      // ✅ 正确使用 result.total
```

**目标实现**:
```typescript
// ✅ 标准MyBatisPlus分页参数
interface WholeVehicleCollectionSearchParams {
  pageNum?: number;     // MyBatisPlus标准参数
  pageSize?: number;    // MyBatisPlus标准参数
  // ... 其他搜索参数
}

// ✅ 标准分页状态
const pagination = reactive({
  pageNum: 1,           // 修正为 pageNum
  pageSize: 20,
  total: 0
});

// ✅ 标准分页组件绑定
<el-pagination
  v-model:current-page="pagination.pageNum"    // 修正为 pageNum
  v-model:page-size="pagination.pageSize"
  :total="pagination.total"
  layout="total, sizes, prev, pager, next, jumper"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
/>
```

#### 1.2.3 数据字典问题分析
**当前实现问题**:
```typescript
// ❌ 硬编码字典值和转义逻辑
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': t('orderStatusSubmitted'),
    '取消审核中': t('orderStatusCancelPending'),
    '取消审核通过': t('orderStatusCancelApproved'),
    // ... 硬编码映射
  };
  return statusMap[status] || status;
};

// ❌ 下拉选项硬编码
<el-option :label="t('orderStatusSubmitted')" value="已提交" />
<el-option :label="t('orderStatusCancelPending')" value="取消审核中" />
```

**目标实现**:
```typescript
// ✅ 使用标准数据字典
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.BUSINESS_TYPE,
  DICTIONARY_TYPES.PAYMENT_CHANNEL
]);

// ✅ 标准字典转义
const formatOrderStatus = (status: string) => 
  getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;

// ✅ 标准下拉选项
<el-option
  v-for="option in getOptions(DICTIONARY_TYPES.ORDER_STATUS)"
  :key="option.code"
  :label="option.name"
  :value="option.code"
/>
```

#### 1.2.4 API响应处理问题分析
**当前实现**:
```typescript
// ✅ 响应处理基本正确
const response = await getOrderPaymentList(params as OrderPaymentListParams);
console.log(response);
tableData.value = response.result.records;
pagination.total = response.result.total;
```

**需要优化的地方**:
```typescript
// ✅ 标准API响应处理模式
try {
  const response = await getOrderPaymentList(params);
  
  // 标准响应结构: { code, message, result: { records, total, pageNum, pageSize, pages } }
  if (response.code === '200' || response.code === 200) {
    tableData.value = response.result.records || [];
    pagination.total = response.result.total || 0;
    pagination.pageNum = response.result.pageNum || pagination.pageNum;
  } else {
    throw new Error(response.message || '获取数据失败');
  }
} catch (error) {
  console.error('获取整车收款列表失败:', error);
  ElMessage.error(tc('loadFailed'));
  tableData.value = [];
  pagination.total = 0;
}
```

## 2. 重构实施方案

### 2.1 目录结构重构

#### 2.1.1 创建新的目录结构
```bash
# 创建页面目录
mkdir -p src/views/finance/wholeVehicleCollection/components

# 创建API模块目录
mkdir -p src/api/modules/finance

# 创建类型定义目录
mkdir -p src/types/finance

# 创建Mock数据目录
mkdir -p src/mock/data/finance
```

#### 2.1.2 文件迁移计划
1. **主页面文件**: `WholeVehicleCollectionManagementView.vue` → `src/views/finance/wholeVehicleCollection/WholeVehicleCollectionView.vue`
2. **弹窗组件**: 保持现有组件结构，移动到新目录
3. **API模块**: 重构 `src/api/modules/payment.ts` → `src/api/modules/finance/wholeVehicleCollection.ts`
4. **类型定义**: 重构相关类型定义到 `src/types/finance/wholeVehicleCollection.d.ts`
5. **Mock数据**: 重构Mock数据到 `src/mock/data/finance/wholeVehicleCollection.ts`

### 2.2 MyBatisPlus分页标准化

#### 2.2.1 分页参数标准化
```typescript
// src/types/finance/wholeVehicleCollection.d.ts

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 整车收款搜索参数（继承分页参数）
export interface WholeVehicleCollectionSearchParams extends PageParams {
  orderNumber?: string;
  buyerName?: string;
  buyerPhone?: string;
  orderStatus?: string;
  paymentStatus?: string;
  startDate?: string;
  endDate?: string;
  canInvoice?: boolean;
}
```

#### 2.2.2 分页组件标准化
```vue
<!-- 标准MyBatisPlus分页组件 -->
<div class="pagination-container">
  <el-pagination
    v-model:current-page="pagination.pageNum"
    v-model:page-size="pagination.pageSize"
    :page-sizes="[10, 20, 50, 100]"
    :total="pagination.total"
    layout="total, sizes, prev, pager, next, jumper"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</div>
```

#### 2.2.3 分页处理函数标准化
```typescript
// 分页处理（修正为MyBatisPlus标准）
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;  // ✅ 修正为pageNum
  loadData();
};

// API调用参数构建
const buildSearchParams = (): WholeVehicleCollectionSearchParams => {
  const params: WholeVehicleCollectionSearchParams = {
    pageNum: pagination.pageNum,      // ✅ 使用标准参数名
    pageSize: pagination.pageSize,
    ...searchParams
  };

  // 处理时间范围
  if (dateRange.value) {
    params.startDate = dateRange.value[0];
    params.endDate = dateRange.value[1];
  }

  return filterEmptyParams(params);
};
```

### 2.3 数据字典标准化

#### 2.3.1 字典类型定义
```typescript
// src/constants/dictionary.ts 中添加
export const DICTIONARY_TYPES = {
  // ... 现有字典类型
  ORDER_STATUS: 'ORDER_STATUS',                   // 订单状态
  PAYMENT_STATUS: 'PAYMENT_STATUS',               // 支付状态
  PAYMENT_METHOD: 'PAYMENT_METHOD',               // 支付方式
  BUSINESS_TYPE: 'BUSINESS_TYPE',                 // 业务类型（收款/退款）
  PAYMENT_CHANNEL: 'PAYMENT_CHANNEL',             // 支付渠道
  PAYMENT_TYPE: 'PAYMENT_TYPE',                   // 收款类型
  DEALER_STORE: 'DEALER_STORE',                   // 经销商门店
  SALES_CONSULTANT: 'SALES_CONSULTANT',           // 销售顾问
} as const;
```

#### 2.3.2 字典使用标准化
```typescript
// 使用批量字典获取
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.BUSINESS_TYPE,
  DICTIONARY_TYPES.PAYMENT_CHANNEL,
  DICTIONARY_TYPES.PAYMENT_TYPE,
  DICTIONARY_TYPES.DEALER_STORE,
  DICTIONARY_TYPES.SALES_CONSULTANT
]);

// 计算属性获取选项
const orderStatusOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.ORDER_STATUS)
);
const paymentStatusOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.PAYMENT_STATUS)
);
const businessTypeOptions = computed(() => 
  getOptions(DICTIONARY_TYPES.BUSINESS_TYPE)
);

// 标准转义函数
const formatOrderStatus = (status: string) => 
  getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;

const formatPaymentStatus = (status: string) => 
  getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, status) || status;

const formatPaymentChannel = (channel: string) => 
  getNameByCode(DICTIONARY_TYPES.PAYMENT_CHANNEL, channel) || channel;
```

#### 2.3.3 标签样式映射标准化
```typescript
// 状态标签样式映射
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.ORDER_STATUS]: {
    'submitted': 'info',
    'confirmed': 'success',
    'pending_review': 'warning',
    'cancelled': 'danger',
    'delivered': 'success'
  },
  [DICTIONARY_TYPES.PAYMENT_STATUS]: {
    'pending_deposit': 'warning',
    'deposit_paid': 'primary',
    'pending_final': 'warning',
    'fully_paid': 'success',
    'refunding': 'danger',
    'refunded': 'info'
  }
};

const getStatusTagType = (status: string, dictionaryType: DictionaryType) =>
  STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info';
```

### 2.4 API响应处理标准化

#### 2.4.1 API函数标准化
```typescript
// src/api/modules/finance/wholeVehicleCollection.ts

import request from '@/api';
import type {
  WholeVehicleCollectionSearchParams,
  WholeVehicleCollectionPageResponse,
  OrderDetailInfo,
  PaymentRecord,
  AddPaymentRecordForm,
  ApiResponse
} from '@/types/finance/wholeVehicleCollection';
import {
  getWholeVehicleCollectionListMock,
  getOrderDetailMock,
  addPaymentRecordMock,
  deletePaymentRecordMock,
  checkTransactionNumberMock
} from '@/mock/data/finance/wholeVehicleCollection';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getWholeVehicleCollectionList = (
  params: WholeVehicleCollectionSearchParams
): Promise<ApiResponse<WholeVehicleCollectionPageResponse>> => {
  if (USE_MOCK_API) {
    return getWholeVehicleCollectionListMock(params);
  } else {
    return request.get<any, ApiResponse<WholeVehicleCollectionPageResponse>>(
      '/finance/whole-vehicle-collection/list',
      { params }
    );
  }
};

export const getOrderDetail = (
  orderId: string
): Promise<ApiResponse<OrderDetailInfo>> => {
  if (USE_MOCK_API) {
    return getOrderDetailMock(orderId);
  } else {
    return request.get<any, ApiResponse<OrderDetailInfo>>(
      `/finance/order/detail/${orderId}`
    );
  }
};

export const addPaymentRecord = (
  orderId: string,
  data: AddPaymentRecordForm
): Promise<ApiResponse<PaymentRecord>> => {
  if (USE_MOCK_API) {
    return addPaymentRecordMock(orderId, data);
  } else {
    return request.post<any, ApiResponse<PaymentRecord>>(
      `/finance/payment-record/${orderId}/create`,
      data
    );
  }
};

export const deletePaymentRecord = (
  orderId: string,
  recordId: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return deletePaymentRecordMock(orderId, recordId);
  } else {
    return request.delete<any, ApiResponse<boolean>>(
      `/finance/payment-record/${orderId}/delete/${recordId}`
    );
  }
};

export const checkTransactionNumber = (
  transactionNumber: string
): Promise<ApiResponse<boolean>> => {
  if (USE_MOCK_API) {
    return checkTransactionNumberMock(transactionNumber);
  } else {
    return request.get<any, ApiResponse<boolean>>(
      `/finance/check-transaction/${transactionNumber}`
    );
  }
};
```

#### 2.4.2 响应处理标准化
```typescript
// 标准API调用和响应处理
const loadData = async () => {
  try {
    loading.value = true;

    const params = buildSearchParams();
    const response = await getWholeVehicleCollectionList(params);

    // 标准响应处理
    if (response.code === '200' || response.code === 200) {
      tableData.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pageNum = response.result.pageNum || pagination.pageNum;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取整车收款列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};
```

## 3. 类型定义文件

### 3.1 完整类型定义
```typescript
// src/types/finance/wholeVehicleCollection.d.ts

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 整车收款列表项
export interface WholeVehicleCollectionItem {
  id: string;
  orderId: string;
  orderNumber: string;
  buyerName: string;
  buyerPhone: string;
  dealerStoreName: string;
  salesConsultantName: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  createTime: string;
  orderStatus: string;
  paymentStatus: string;
  vehicleSalesPrice: number;
  insuranceAmount: number;
  otrAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  unpaidAmount: number;
  loanAmount: number;
  canInvoice: boolean;
  invoiceTime?: string;
  invoiceNumber?: string;
  updateTime: string;
}

// 整车收款搜索参数
export interface WholeVehicleCollectionSearchParams extends PageParams {
  orderNumber?: string;
  buyerName?: string;
  buyerPhone?: string;
  orderStatus?: string;
  paymentStatus?: string;
  startDate?: string;
  endDate?: string;
  canInvoice?: boolean;
}

// 整车收款分页响应
export interface WholeVehicleCollectionPageResponse extends PageResponse<WholeVehicleCollectionItem> {}

// 订单详情信息
export interface OrderDetailInfo {
  // 订单基本信息
  orderId: string;
  orderNumber: string;
  orderCreateTime: string;
  orderStatus: string;
  paymentStatus: string;
  paymentMethod: string;
  loanAmount: number;

  // 下单人信息
  ordererName: string;
  ordererPhone: string;

  // 购车人信息
  buyerName: string;
  buyerPhone: string;
  buyerIdType: string;
  buyerIdNumber: string;
  buyerEmail: string;
  buyerState: string;
  buyerCity: string;
  buyerPostcode: string;
  buyerAddress: string;

  // 经销商信息
  dealerRegion: string;
  dealerCity: string;
  dealerStoreName: string;

  // 销售顾问信息
  salesConsultantName: string;

  // 车辆信息
  model: string;
  variant: string;
  color: string;
  vin: string;
  warehouseName: string;
  productionDate: string;
  options?: Array<{
    name: string;
    price: number;
  }>;

  // 价格信息
  salesSubtotal: number;
  consumptionTax: number;
  salesTax: number;
  numberPlatesFee: number;
  accessoriesTotalAmount: number;
  vehicleSalesPrice: number;
  insuranceAmount: number;
  otrAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  unpaidAmount: number;

  // 收退款记录
  paymentRecords: PaymentRecord[];
}

// 收退款记录
export interface PaymentRecord {
  paymentRecordId: string;
  paymentRecordNumber: string;
  orderId: string;
  businessType: '收款' | '退款';
  transactionNumber: string;
  channel: string;
  amount: number;
  paymentType: string;
  arrivalTime: string;
  remark?: string;
  dataSource: string;
  isDeletable?: boolean;
  createTime: string;
  creator: string;
}

// 添加收退款记录表单
export interface AddPaymentRecordForm {
  businessType: '收款' | '退款';
  transactionNumber: string;
  channel: string;
  amount: number;
  paymentType: string;
  arrivalTime: string;
  remark?: string;
}

// API响应类型
export interface ApiResponse<T> {
  code: string | number;
  message: string;
  result: T;
  timestamp: number;
}

// 用户角色类型
export type UserRole = 'finance_manager' | 'payment_operator' | 'sales_manager';
```

## 4. Mock数据设计

### 4.1 Mock数据结构
```typescript
// src/mock/data/finance/wholeVehicleCollection.ts

import type {
  WholeVehicleCollectionSearchParams,
  WholeVehicleCollectionPageResponse,
  WholeVehicleCollectionItem,
  OrderDetailInfo,
  PaymentRecord,
  AddPaymentRecordForm,
  ApiResponse
} from '@/types/finance/wholeVehicleCollection';

// 动态生成模拟数据（25-30条，便于测试分页）
function generateMockData(): WholeVehicleCollectionItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData: WholeVehicleCollectionItem[] = [];

  const orderStatuses = ['submitted', 'confirmed', 'pending_review', 'cancelled', 'delivered'];
  const paymentStatuses = ['pending_deposit', 'deposit_paid', 'pending_final', 'fully_paid', 'refunding', 'refunded'];
  const vehicleModels = ['MYVI 1.5L', 'ALZA 1.5L', 'AXIA 1.0L', 'BEZZA 1.3L'];
  const colors = ['白色', '黑色', '银色', '红色', '蓝色'];
  const stores = ['吉隆坡总店', '槟城分店', '新山分店', '怡保分店'];
  const consultants = ['张三', '李四', '王五', '赵六'];

  for (let i = 0; i < dataCount; i++) {
    const vehicleSalesPrice = Math.floor(Math.random() * 50000) + 30000;
    const insuranceAmount = Math.floor(Math.random() * 3000) + 1000;
    const otrAmount = Math.floor(Math.random() * 2000) + 500;
    const discountAmount = Math.floor(Math.random() * 5000);
    const totalAmount = vehicleSalesPrice + insuranceAmount + otrAmount - discountAmount;
    const paidAmount = Math.floor(Math.random() * totalAmount);
    const unpaidAmount = totalAmount - paidAmount;

    mockData.push({
      id: `WVC${String(i + 1).padStart(6, '0')}`,
      orderId: `ORD${String(i + 1).padStart(6, '0')}`,
      orderNumber: `ON${String(Math.floor(Math.random() * 900000) + 100000)}`,
      buyerName: `客户${i + 1}`,
      buyerPhone: `1${String(Math.floor(Math.random() * 900000000) + 100000000)}`,
      dealerStoreName: stores[Math.floor(Math.random() * stores.length)],
      salesConsultantName: consultants[Math.floor(Math.random() * consultants.length)],
      vin: `WVWZZZ1JZ3W${String(Math.floor(Math.random() * 900000) + 100000)}`,
      model: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      variant: 'Standard',
      color: colors[Math.floor(Math.random() * colors.length)],
      createTime: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000).toISOString(),
      orderStatus: orderStatuses[Math.floor(Math.random() * orderStatuses.length)],
      paymentStatus: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)],
      vehicleSalesPrice,
      insuranceAmount,
      otrAmount,
      discountAmount,
      totalAmount,
      paidAmount,
      unpaidAmount,
      loanAmount: Math.random() > 0.5 ? Math.floor(Math.random() * 30000) + 10000 : 0,
      canInvoice: Math.random() > 0.3,
      invoiceTime: Math.random() > 0.5 ? new Date().toISOString() : undefined,
      invoiceNumber: Math.random() > 0.5 ? `INV${String(Math.floor(Math.random() * 900000) + 100000)}` : undefined,
      updateTime: new Date().toISOString()
    });
  }

  return mockData;
}

const mockData = generateMockData();

export const getWholeVehicleCollectionListMock = (
  params: WholeVehicleCollectionSearchParams
): Promise<ApiResponse<WholeVehicleCollectionPageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.orderNumber) {
        filteredData = filteredData.filter(item =>
          item.orderNumber.toLowerCase().includes(params.orderNumber!.toLowerCase())
        );
      }

      if (params.buyerName) {
        filteredData = filteredData.filter(item =>
          item.buyerName.includes(params.buyerName!)
        );
      }

      if (params.orderStatus) {
        filteredData = filteredData.filter(item =>
          item.orderStatus === params.orderStatus
        );
      }

      if (params.paymentStatus) {
        filteredData = filteredData.filter(item =>
          item.paymentStatus === params.paymentStatus
        );
      }

      if (params.canInvoice !== undefined) {
        filteredData = filteredData.filter(item =>
          item.canInvoice === params.canInvoice
        );
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        code: '200',
        message: '获取成功',
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        },
        timestamp: Date.now()
      });
    }, 500);
  });
};

// 获取订单详情Mock
export const getOrderDetailMock = (
  orderId: string
): Promise<ApiResponse<OrderDetailInfo>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockData.find(item => item.orderId === orderId);
      if (!item) {
        resolve({
          code: '404',
          message: '订单不存在',
          result: null as any,
          timestamp: Date.now()
        });
        return;
      }

      const detail: OrderDetailInfo = {
        orderId: item.orderId,
        orderNumber: item.orderNumber,
        orderCreateTime: item.createTime,
        orderStatus: item.orderStatus,
        paymentStatus: item.paymentStatus,
        paymentMethod: '银行转账',
        loanAmount: item.loanAmount,

        ordererName: item.buyerName,
        ordererPhone: item.buyerPhone,

        buyerName: item.buyerName,
        buyerPhone: item.buyerPhone,
        buyerIdType: 'IC',
        buyerIdNumber: `${Math.floor(Math.random() * 900000) + 100000}-${Math.floor(Math.random() * 90) + 10}-${Math.floor(Math.random() * 9000) + 1000}`,
        buyerEmail: `customer${Math.floor(Math.random() * 1000)}@example.com`,
        buyerState: '雪兰莪',
        buyerCity: '吉隆坡',
        buyerPostcode: `${Math.floor(Math.random() * 90000) + 10000}`,
        buyerAddress: `地址${Math.floor(Math.random() * 100) + 1}号`,

        dealerRegion: '中央区',
        dealerCity: '吉隆坡',
        dealerStoreName: item.dealerStoreName,

        salesConsultantName: item.salesConsultantName,

        model: item.model,
        variant: item.variant,
        color: item.color,
        vin: item.vin,
        warehouseName: '总仓库',
        productionDate: '2024-01-15',
        options: [
          { name: '天窗', price: 2000 },
          { name: '真皮座椅', price: 3000 }
        ],

        salesSubtotal: item.vehicleSalesPrice,
        consumptionTax: Math.floor(item.vehicleSalesPrice * 0.1),
        salesTax: Math.floor(item.vehicleSalesPrice * 0.06),
        numberPlatesFee: 500,
        accessoriesTotalAmount: 5000,
        vehicleSalesPrice: item.vehicleSalesPrice,
        insuranceAmount: item.insuranceAmount,
        otrAmount: item.otrAmount,
        discountAmount: item.discountAmount,
        totalAmount: item.totalAmount,
        paidAmount: item.paidAmount,
        unpaidAmount: item.unpaidAmount,

        paymentRecords: [
          {
            paymentRecordId: 'PR001',
            paymentRecordNumber: 'PRN001',
            orderId: item.orderId,
            businessType: '收款',
            transactionNumber: 'TXN001',
            channel: '银行卡',
            amount: 5000,
            paymentType: 'Book Fee',
            arrivalTime: '2024-01-15',
            remark: '定金',
            dataSource: 'APP推送',
            isDeletable: false,
            createTime: '2024-01-15T10:00:00Z',
            creator: '系统'
          }
        ]
      };

      resolve({
        code: '200',
        message: '获取成功',
        result: detail,
        timestamp: Date.now()
      });
    }, 300);
  });
};

// 添加收退款记录Mock
export const addPaymentRecordMock = (
  orderId: string,
  data: AddPaymentRecordForm
): Promise<ApiResponse<PaymentRecord>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newRecord: PaymentRecord = {
        paymentRecordId: `PR${Date.now()}`,
        paymentRecordNumber: `PRN${Date.now()}`,
        orderId,
        businessType: data.businessType,
        transactionNumber: data.transactionNumber,
        channel: data.channel,
        amount: data.amount,
        paymentType: data.paymentType,
        arrivalTime: data.arrivalTime,
        remark: data.remark,
        dataSource: '手动录入',
        isDeletable: true,
        createTime: new Date().toISOString(),
        creator: '当前用户'
      };

      resolve({
        code: '200',
        message: '添加成功',
        result: newRecord,
        timestamp: Date.now()
      });
    }, 1000);
  });
};

// 删除收退款记录Mock
export const deletePaymentRecordMock = (
  orderId: string,
  recordId: string
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '200',
        message: '删除成功',
        result: true,
        timestamp: Date.now()
      });
    }, 500);
  });
};

// 检查流水号是否存在Mock
export const checkTransactionNumberMock = (
  transactionNumber: string
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟检查逻辑，随机返回是否存在
      const exists = Math.random() > 0.8;

      resolve({
        code: '200',
        message: '检查完成',
        result: exists,
        timestamp: Date.now()
      });
    }, 300);
  });
};
```

## 5. 组件重构设计

### 5.1 主页面重构
```vue
<!-- src/views/finance/wholeVehicleCollection/WholeVehicleCollectionView.vue -->
<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <!-- 第一行筛选字段 -->
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('enterOrderNumber')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('buyerName')">
              <el-input
                v-model="searchParams.buyerName"
                :placeholder="t('enterBuyerName')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('buyerPhone')">
              <el-input
                v-model="searchParams.buyerPhone"
                :placeholder="t('enterBuyerPhone')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderStatus')">
              <el-select
                v-model="searchParams.orderStatus"
                :placeholder="tc('all')"
                clearable
                style="width: 100%"
                :loading="dictionaryLoading"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in orderStatusOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行筛选字段 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('paymentStatus')">
              <el-select
                v-model="searchParams.paymentStatus"
                :placeholder="tc('all')"
                clearable
                style="width: 100%"
                :loading="dictionaryLoading"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in paymentStatusOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderCreateTime')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('canInvoice')">
              <el-select
                v-model="searchParams.canInvoice"
                :placeholder="tc('all')"
                clearable
                style="width: 100%"
              >
                <el-option :label="tc('all')" :value="undefined" />
                <el-option :label="tc('yes')" :value="true" />
                <el-option :label="tc('no')" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button @click="resetSearch">
                {{ tc('reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#fafafa', fontWeight: 'bold' }"
      >
        <el-table-column type="index" :label="tc('index')" width="80" fixed="left" />
        <el-table-column prop="orderNumber" :label="t('orderNumber')" width="120" sortable />
        <el-table-column prop="buyerName" :label="t('buyerName')" width="100" />
        <el-table-column prop="buyerPhone" :label="t('buyerPhone')" width="120">
          <template #default="{ row }">
            {{ maskPhoneNumber(row.buyerPhone) }}
          </template>
        </el-table-column>
        <el-table-column prop="dealerStoreName" :label="t('dealerStore')" width="100">
          <template #default="{ row }">
            {{ formatDealerStore(row.dealerStoreName) }}
          </template>
        </el-table-column>
        <el-table-column prop="salesConsultantName" :label="t('salesConsultant')" width="100">
          <template #default="{ row }">
            {{ formatSalesConsultant(row.salesConsultantName) }}
          </template>
        </el-table-column>
        <el-table-column prop="vin" :label="t('vin')" width="120" />
        <el-table-column prop="model" :label="t('model')" width="100" />
        <el-table-column prop="variant" :label="t('variant')" width="100" />
        <el-table-column prop="color" :label="t('color')" width="80" />
        <el-table-column prop="createTime" :label="t('orderCreateTime')" width="150" sortable />
        <el-table-column prop="orderStatus" :label="t('orderStatus')" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.orderStatus, DICTIONARY_TYPES.ORDER_STATUS)">
              {{ formatOrderStatus(row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" :label="t('paymentStatus')" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.paymentStatus, DICTIONARY_TYPES.PAYMENT_STATUS)">
              {{ formatPaymentStatus(row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="vehicleSalesPrice" :label="t('vehicleSalesPrice')" width="100" align="right">
          <template #default="{ row }">
            {{ formatAmount(row.vehicleSalesPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" :label="t('totalAmount')" width="100" align="right">
          <template #default="{ row }">
            <strong>{{ formatAmount(row.totalAmount) }}</strong>
          </template>
        </el-table-column>
        <el-table-column prop="paidAmount" :label="t('paidAmount')" width="100" align="right">
          <template #default="{ row }">
            <span class="paid-amount">{{ formatAmount(row.paidAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unpaidAmount" :label="t('unpaidAmount')" width="100" align="right">
          <template #default="{ row }">
            <span class="unpaid-amount">{{ formatAmount(row.unpaidAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="canInvoice" :label="t('canInvoice')" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.canInvoice ? 'success' : 'info'">
              {{ row.canInvoice ? tc('yes') : tc('no') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="tc('operations')" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="View" link @click="handleViewDetail(row)">
              {{ t('orderDetail') }}
            </el-button>
            <el-button
              type="primary"
              :icon="CreditCard"
              link
              @click="handlePaymentOperation(row)"
              :disabled="!canOperatePayment(row.paymentStatus)"
            >
              {{ t('paymentOperation') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 弹窗组件 -->
    <PaymentOperationDialog
      v-model:visible="paymentDialogVisible"
      :order-id="currentOrderId"
      @refresh="loadData"
    />

    <OrderDetailDialog
      v-model:visible="detailDialogVisible"
      :order-id="currentOrderId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { ElMessage } from 'element-plus';
import { Search, View, CreditCard } from '@element-plus/icons-vue';
import type {
  WholeVehicleCollectionSearchParams,
  WholeVehicleCollectionItem
} from '@/types/finance/wholeVehicleCollection';
import { getWholeVehicleCollectionList } from '@/api/modules/finance/wholeVehicleCollection';
import PaymentOperationDialog from './components/PaymentOperationDialog.vue';
import OrderDetailDialog from './components/OrderDetailDialog.vue';

// 使用国际化
const { t } = useModuleI18n('finance.wholeVehicleCollection');
const { t: tc } = useModuleI18n('common');

// 使用字典数据
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.DEALER_STORE,
  DICTIONARY_TYPES.SALES_CONSULTANT
]);

// 计算属性获取字典选项
const orderStatusOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.ORDER_STATUS)
);
const paymentStatusOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.PAYMENT_STATUS)
);

// 数据定义
const loading = ref(false);
const tableData = ref<WholeVehicleCollectionItem[]>([]);
const paymentDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const currentOrderId = ref('');
const dateRange = ref<[string, string] | null>(null);

// 搜索参数（修正为MyBatisPlus标准）
const searchParams = reactive<WholeVehicleCollectionSearchParams>({
  orderNumber: '',
  buyerName: '',
  buyerPhone: '',
  orderStatus: '',
  paymentStatus: '',
  startDate: '',
  endDate: '',
  canInvoice: undefined
});

// 分页参数（修正为MyBatisPlus标准）
const pagination = reactive({
  pageNum: 1,      // ✅ 修正为pageNum
  pageSize: 20,
  total: 0
});

// 状态标签样式映射
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.ORDER_STATUS]: {
    'submitted': 'info',
    'confirmed': 'success',
    'pending_review': 'warning',
    'cancelled': 'danger',
    'delivered': 'success'
  },
  [DICTIONARY_TYPES.PAYMENT_STATUS]: {
    'pending_deposit': 'warning',
    'deposit_paid': 'primary',
    'pending_final': 'warning',
    'fully_paid': 'success',
    'refunding': 'danger',
    'refunded': 'info'
  }
};

// 字典转义函数
const formatOrderStatus = (status: string) =>
  getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;

const formatPaymentStatus = (status: string) =>
  getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, status) || status;

const formatDealerStore = (store: string) =>
  getNameByCode(DICTIONARY_TYPES.DEALER_STORE, store) || store;

const formatSalesConsultant = (consultant: string) =>
  getNameByCode(DICTIONARY_TYPES.SALES_CONSULTANT, consultant) || consultant;

const getStatusTagType = (status: string, dictionaryType: string) =>
  STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info';

// 页面初始化
onMounted(() => {
  initDefaultDateRange();
  loadData();
});

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal) {
    searchParams.startDate = newVal[0];
    searchParams.endDate = newVal[1];
  } else {
    searchParams.startDate = '';
    searchParams.endDate = '';
  }
});

// 初始化默认时间范围（最近一个月）
const initDefaultDateRange = () => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - 1);

  dateRange.value = [
    `${startDate.toISOString().split('T')[0]} 00:00:00`,
    `${endDate.toISOString().split('T')[0]} 23:59:59`
  ];
};

// 构建搜索参数
const buildSearchParams = (): WholeVehicleCollectionSearchParams => {
  const params: WholeVehicleCollectionSearchParams = {
    pageNum: pagination.pageNum,      // ✅ 使用标准参数名
    pageSize: pagination.pageSize,
    ...searchParams
  };

  // 处理时间范围
  if (dateRange.value) {
    params.startDate = dateRange.value[0];
    params.endDate = dateRange.value[1];
  }

  return filterEmptyParams(params);
};

// 过滤空值参数
const filterEmptyParams = (params: WholeVehicleCollectionSearchParams): WholeVehicleCollectionSearchParams => {
  const filteredParams: WholeVehicleCollectionSearchParams = {
    pageNum: params.pageNum,
    pageSize: params.pageSize
  };

  // 只保留非空值
  Object.keys(params).forEach(key => {
    const value = params[key as keyof WholeVehicleCollectionSearchParams];
    if (value !== undefined && value !== null && String(value).trim() !== '' && key !== 'pageNum' && key !== 'pageSize') {
      (filteredParams as any)[key] = value;
    }
  });

  return filteredParams;
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;

    const params = buildSearchParams();
    const response = await getWholeVehicleCollectionList(params);

    // 标准API响应处理
    if (response.code === '200' || response.code === 200) {
      tableData.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pageNum = response.result.pageNum || pagination.pageNum;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取整车收款列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    orderNumber: '',
    buyerName: '',
    buyerPhone: '',
    orderStatus: '',
    paymentStatus: '',
    startDate: '',
    endDate: '',
    canInvoice: undefined
  });
  dateRange.value = null;
  initDefaultDateRange();
  pagination.pageNum = 1;
  loadData();
};

// 分页处理（修正为MyBatisPlus标准）
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;  // ✅ 修正为pageNum
  loadData();
};

// 查看详情
const handleViewDetail = (row: WholeVehicleCollectionItem) => {
  currentOrderId.value = row.orderId;
  detailDialogVisible.value = true;
};

// 收退款操作
const handlePaymentOperation = (row: WholeVehicleCollectionItem) => {
  currentOrderId.value = row.orderId;
  paymentDialogVisible.value = true;
};

// 判断是否可以收退款操作
const canOperatePayment = (paymentStatus: string) => {
  return ['deposit_paid', 'pending_final', 'fully_paid'].includes(paymentStatus);
};

// 工具方法
const maskPhoneNumber = (phone: string): string => {
  if (!phone || phone.length < 7) return phone;
  return phone.slice(0, 3) + '****' + phone.slice(-4);
};

const formatAmount = (amount: number): string => {
  return `RM ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.paid-amount {
  color: #67c23a;
  font-weight: bold;
}

.unpaid-amount {
  color: #f56c6c;
  font-weight: bold;
}
</style>
```

## 6. 路由配置更新

### 6.1 路由路径调整
```typescript
// src/router/modules/finance.ts

export default {
  path: '/finance',
  name: 'Finance',
  component: () => import('@/layouts/default.vue'),
  meta: {
    title: 'menu.finance',
    requiresAuth: true,
    icon: 'Money',
    roles: ['finance_manager', 'payment_operator', 'sales_manager']
  },
  children: [
    {
      path: '/finance/whole-vehicle-collection',
      name: 'WholeVehicleCollection',
      component: () => import('@/views/finance/wholeVehicleCollection/WholeVehicleCollectionView.vue'),
      meta: {
        title: 'menu.wholeVehicleCollection',
        requiresAuth: true,
        icon: 'CreditCard',
        roles: ['finance_manager', 'payment_operator']
      }
    },
    {
      path: '/finance/loan-management',
      name: 'LoanManagement',
      component: () => import('@/views/finance/loanManagement/LoanManagementView.vue'),
      meta: {
        title: 'menu.loanManagement',
        requiresAuth: true,
        icon: 'Wallet',
        roles: ['finance_manager', 'loan_officer']
      }
    },
    {
      path: '/finance/insurance-management',
      name: 'InsuranceManagement',
      component: () => import('@/views/finance/insuranceManagement/InsuranceManagementView.vue'),
      meta: {
        title: 'menu.insuranceManagement',
        requiresAuth: true,
        icon: 'Shield',
        roles: ['finance_manager', 'insurance_officer']
      }
    },
    {
      path: '/finance/financial-reports',
      name: 'FinancialReports',
      component: () => import('@/views/finance/financialReports/FinancialReportsView.vue'),
      meta: {
        title: 'menu.financialReports',
        requiresAuth: true,
        icon: 'DataAnalysis',
        roles: ['finance_manager', 'report_viewer']
      }
    }
  ]
};
```

### 6.2 菜单配置更新
```json
// src/locales/modules/common/zh.json
{
  "menu": {
    "finance": "财务管理",
    "wholeVehicleCollection": "整车收款管理",
    "loanManagement": "贷款管理",
    "insuranceManagement": "保险管理",
    "financialReports": "财务报表"
  }
}
```

## 7. 国际化文件更新

### 7.1 模块化国际化结构
```json
// src/locales/modules/finance/zh.json
{
  "wholeVehicleCollection": {
    "title": "整车收款管理",
    "orderNumber": "订单编号",
    "buyerName": "购车人姓名",
    "buyerPhone": "购车人手机号",
    "orderStatus": "订单状态",
    "paymentStatus": "支付状态",
    "orderCreateTime": "下单时间",
    "canInvoice": "可开票",
    "dealerStore": "经销商门店",
    "salesConsultant": "销售顾问",
    "vin": "VIN码",
    "model": "车型",
    "variant": "变型",
    "color": "颜色",
    "vehicleSalesPrice": "车辆销售价",
    "totalAmount": "订单总额",
    "paidAmount": "已付金额",
    "unpaidAmount": "未付金额",
    "orderDetail": "订单详情",
    "paymentOperation": "收退款操作",
    "enterOrderNumber": "请输入订单编号",
    "enterBuyerName": "请输入购车人姓名",
    "enterBuyerPhone": "请输入购车人手机号",
    "paymentRecord": "收退款记录",
    "addPaymentRecord": "添加收退款记录",
    "businessType": "业务类型",
    "transactionNumber": "流水号",
    "channel": "渠道",
    "amount": "金额",
    "paymentType": "收款类型",
    "arrivalTime": "到账时间",
    "remark": "备注",
    "collection": "收款",
    "refund": "退款",
    "creator": "创建人",
    "createTime": "创建时间",
    "dataSource": "数据来源",
    "manualEntry": "手动录入",
    "appPush": "APP推送",
    "systemSync": "系统同步",
    "addSuccess": "添加成功",
    "addFailed": "添加失败",
    "deleteSuccess": "删除成功",
    "deleteFailed": "删除失败",
    "confirmDelete": "确认删除",
    "deleteConfirmMessage": "确定要删除这条收退款记录吗？",
    "transactionNumberExists": "流水号已存在",
    "transactionNumberRequired": "请输入流水号",
    "amountRequired": "请输入金额",
    "channelRequired": "请选择渠道",
    "paymentTypeRequired": "请选择收款类型",
    "arrivalTimeRequired": "请选择到账时间"
  }
}
```

### 7.2 页面引用方式
```typescript
// 使用模块化引用
const { t, tc } = useModuleI18n('finance.wholeVehicleCollection');

// t() 访问当前模块翻译: finance.wholeVehicleCollection.title
// tc() 访问通用模块翻译: common.search
```

## 8. 完整实施步骤

### 8.1 第一阶段：基础结构创建

#### 步骤1：创建目录结构
```bash
# 创建页面目录
mkdir -p src/views/finance/wholeVehicleCollection/components

# 创建API模块目录
mkdir -p src/api/modules/finance

# 创建类型定义目录
mkdir -p src/types/finance

# 创建Mock数据目录
mkdir -p src/mock/data/finance
```

#### 步骤2：创建类型定义文件
```typescript
// 创建 src/types/finance/wholeVehicleCollection.d.ts
// 内容参考第3节的完整类型定义
```

#### 步骤3：创建Mock数据文件
```typescript
// 创建 src/mock/data/finance/wholeVehicleCollection.ts
// 内容参考第4节的Mock数据实现
```

#### 步骤4：创建API模块文件
```typescript
// 创建 src/api/modules/finance/wholeVehicleCollection.ts
// 内容参考第2.4.1节的API函数标准化
```

### 8.2 第二阶段：组件重构

#### 步骤5：重构主页面文件
```vue
// 重构 src/views/finance/wholeVehicleCollection/WholeVehicleCollectionView.vue
// 内容参考第5.1节的主页面重构
```

#### 步骤6：重构弹窗组件
```vue
// 重构 src/views/finance/wholeVehicleCollection/components/PaymentOperationDialog.vue
// 重构 src/views/finance/wholeVehicleCollection/components/OrderDetailDialog.vue
// 保持现有功能，修正分页参数和字典使用
```

#### 步骤7：创建新的子组件
```vue
// 创建 src/views/finance/wholeVehicleCollection/components/PaymentRecordForm.vue
// 创建 src/views/finance/wholeVehicleCollection/components/PaymentHistoryTable.vue
// 创建 src/views/finance/wholeVehicleCollection/components/OrderInfoSection.vue
```

### 8.3 第三阶段：配置更新

#### 步骤8：更新路由配置
```typescript
// 创建 src/router/modules/finance.ts
// 添加金融管理模块路由配置
```

#### 步骤9：更新国际化文件
```json
// 创建 src/locales/modules/finance/zh.json
// 添加金融管理相关翻译
```

#### 步骤10：更新字典常量
```typescript
// 更新 src/constants/dictionary.ts
// 添加金融管理相关字典类型
```

### 8.4 第四阶段：测试验证

#### 步骤11：功能测试
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作（使用pageNum参数）
- [ ] 字典数据正常显示和转义
- [ ] 收退款操作功能正常
- [ ] 订单详情弹窗正常显示
- [ ] 收退款记录增删功能正常
- [ ] 流水号重复检查功能正常
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

#### 步骤12：代码质量检查
- [ ] TypeScript类型安全，无编译错误
- [ ] ESLint检查通过
- [ ] 代码格式化正确
- [ ] 组件拆分合理
- [ ] API响应处理符合规范

#### 步骤13：性能优化验证
- [ ] 字典数据缓存正常
- [ ] 分页加载性能良好
- [ ] 组件懒加载正常
- [ ] Mock数据切换正常

## 9. 重构验证清单

### 9.1 目录结构验证
- [ ] 页面文件移动到 `src/views/finance/wholeVehicleCollection/WholeVehicleCollectionView.vue`
- [ ] 弹窗组件移动到 `components/` 目录
- [ ] API模块创建在 `src/api/modules/finance/wholeVehicleCollection.ts`
- [ ] Mock数据创建在 `src/mock/data/finance/wholeVehicleCollection.ts`
- [ ] 类型定义创建在 `src/types/finance/wholeVehicleCollection.d.ts`
- [ ] 国际化文件更新在 `src/locales/modules/finance/`

### 9.2 MyBatisPlus分页验证
- [ ] 分页参数使用 `pageNum` 和 `pageSize`
- [ ] 分页组件绑定 `pagination.pageNum`
- [ ] API响应使用 `response.result.records` 和 `response.result.total`
- [ ] 分页功能正常工作

### 9.3 数据字典验证
- [ ] 所有下拉选项使用字典数据
- [ ] 表格列显示使用字典转义
- [ ] 标签类型映射正确
- [ ] 字典数据加载正常

### 9.4 API响应处理验证
- [ ] 使用 `response.result` 获取数据
- [ ] 错误处理符合规范
- [ ] Mock数据和真实API切换正常

---

**本设计文档为金融管理模块重构提供完整的技术方案和实施指导，确保重构后的代码符合项目规范并具备良好的可维护性。**
```
