# 登录系统实现说明

## 功能概述

本项目已成功实现了完整的登录功能，包括用户认证、路由守卫、状态管理等核心功能。

## 已实现的功能

### 1. 登录页面 (LoginView.vue)
- 现代化的登录界面设计
- 用户名和密码输入框
- 表单验证
- 测试账户快速填充
- 响应式设计，支持移动端

### 2. 认证API (auth.ts)
- 用户登录接口
- 用户信息获取接口
- 退出登录接口
- Token刷新接口
- Mock数据支持，方便开发测试

### 3. 用户状态管理 (auth store)
- Pinia状态管理
- Token管理（本地存储）
- 用户信息存储
- 权限检查方法
- 自动初始化认证状态

### 4. 路由守卫
- 自动检查用户登录状态
- 未登录用户重定向到登录页
- 登录成功后重定向到目标页面
- 已登录用户访问登录页自动跳转到首页

### 5. 应用布局优化
- 区分登录页面和主应用布局
- 登录页面全屏显示
- 主应用包含侧边栏、头部导航
- 用户信息显示和退出按钮

## 测试账户

系统提供了以下测试账户（Mock数据）：

### 管理员账户
- 用户名: `admin`
- 密码: `123456`
- 权限: 超级管理员权限 (`*:*:*`)

### 普通用户账户
- 用户名: `user`
- 密码: `123456`
- 权限: 基础用户权限 (`system:user:view`)

## 使用方法

### 1. 启动项目
```bash
npm run dev
```

### 2. 访问登录页面
访问 `http://localhost:5173/login` 或访问任何需要认证的页面都会自动重定向到登录页面。

### 3. 登录系统
- 使用上述测试账户登录
- 或点击页面上的测试账户标签快速填充
- 登录成功后会自动跳转到首页或之前访问的页面

### 4. 退出登录
- 点击右上角的用户头像
- 选择"退出登录"
- 确认后会清除登录状态并跳转到登录页面

## 技术实现细节

### 1. Mock API配置
项目使用配置文件 `src/config/index.ts` 管理Mock API开关：
```typescript
useMockApi: import.meta.env.VITE_APP_USE_MOCK_API === 'true' || import.meta.env.DEV
```

### 2. Token管理
- Token存储在localStorage中
- 请求拦截器自动添加Authorization头
- Token过期时自动清除并重定向到登录页

### 3. 权限验证
```typescript
// 检查权限
authStore.hasPermission('system:user:view')

// 检查角色
authStore.hasRole('admin')
```

### 4. 路由守卫逻辑
```typescript
// 检查认证状态
if (to.meta.requiresAuth !== false && !authStore.isLoggedIn) {
  // 重定向到登录页并保存目标路由
  next({ 
    name: 'login',
    query: { redirect: to.fullPath }
  })
}
```

## 国际化支持

登录页面支持中英文切换：
- 中文：`src/locales/modules/login/zh.json`
- 英文：`src/locales/modules/login/en.json`

## 后续扩展

### 1. 集成真实API
当后端API就绪时，只需要：
1. 设置 `useMockApi: false`
2. 配置正确的API地址
3. 调整API响应格式（如需要）

### 2. 增强安全性
- 添加验证码功能
- 实现记住密码功能
- 添加登录失败次数限制
- 实现SSO单点登录

### 3. 用户体验优化
- 添加登录页面动画
- 实现自动登录功能
- 添加密码找回功能

## 注意事项

1. **Mock数据**: 当前使用Mock数据进行开发，生产环境需要连接真实API
2. **权限系统**: 已实现基础权限检查，可根据业务需求进一步扩展
3. **安全性**: 生产环境需要实现更严格的安全措施
4. **性能**: Token刷新机制可根据实际需求优化

## 故障排除

### 1. 登录失败
- 检查用户名和密码是否正确
- 确认Mock API配置是否正确
- 查看浏览器控制台错误信息

### 2. 路由跳转问题
- 检查路由配置是否正确
- 确认路由守卫逻辑
- 验证用户权限设置

### 3. 状态管理问题
- 检查Pinia store是否正确初始化
- 确认localStorage中的token
- 验证用户信息是否正确存储 