# API迁移规则文件

## 概述

本文档总结了在Factory Prospect页面重构过程中遇到的API迁移问题，并制定了标准化的API迁移规则，以避免后续迁移中的类似问题。

## 遇到的主要问题

### 1. 请求库不一致问题

**问题描述**:
- 原始API使用 `http` 从 `@/utils/http` 导入
- 重构后的API错误使用了 `request` 从 `@/api` 导入

**影响**:
- 导致请求方式和响应处理不一致
- 类型定义不匹配
- 可能导致运行时错误

**解决方案**:
```typescript
// ❌ 错误的导入
import request from '@/api';

// ✅ 正确的导入
import { http } from '@/utils/http';
```

### 2. URL路径不一致问题

**问题描述**:
- 原始API使用 `/factory/prospects/` 前缀
- 重构后的API错误使用了 `/sales/factory-prospect/` 前缀

**影响**:
- 后端接口无法正确匹配
- 404错误或接口调用失败

**解决方案**:
```typescript
// ❌ 错误的URL
'/sales/factory-prospect/list'

// ✅ 正确的URL
'/factory/prospects/list'
```

### 3. 请求方法不一致问题

**问题描述**:
- 原始API全部使用 `POST` 方法
- 重构过程中错误地使用了 `GET` 方法

**影响**:
- 后端接口无法正确处理请求
- 参数传递方式错误

### 4. 参数命名不一致问题

**问题描述**:
- 原始API定义中所有详情接口都使用 `CustomerDetailParams` (包含 `globalCustomerId`)
- 实际使用中部分接口使用了 `storeProspectId`

**影响**:
- 前后端参数不匹配
- 类型定义与实际使用不一致

### 5. 返回类型格式不一致问题

**问题描述**:
- 原始API使用 `http.post<unknown, ResponseType>` 格式
- 重构后错误使用了 `request.post<any, ResponseType>` 格式

**影响**:
- TypeScript类型检查不准确
- 响应数据结构处理错误

## API迁移标准规则

### 规则1: 请求库标准化

**强制要求**:
- 所有API模块必须使用 `import { http } from '@/utils/http'`
- 禁止使用其他请求库，除非有特殊说明

**检查清单**:
- [ ] 导入语句正确
- [ ] 所有请求使用 `http.get/post/put/delete`
- [ ] 类型定义使用 `http.post<unknown, ResponseType>` 格式

### 规则2: URL路径标准化

**强制要求**:
- 必须与原始API的URL路径完全一致
- URL前缀、路径结构、参数格式都不能改变

**检查步骤**:
1. 找到原始API文件
2. 逐个对比URL路径
3. 确保每个字符都完全匹配

**示例对比表**:
| 功能 | 原始URL | 迁移后URL | 状态 |
|------|---------|-----------|------|
| 列表 | `/factory/prospects/list` | `/factory/prospects/list` | ✅ |
| 统计 | `/factory/prospects/overview/stats` | `/factory/prospects/overview/stats` | ✅ |

### 规则3: 请求方法标准化

**强制要求**:
- 必须与原始API的请求方法完全一致
- 不允许随意更改 GET/POST/PUT/DELETE

**验证方法**:
```typescript
// 检查原始API
export function getFactoryProspectList(params: ProspectListParams) {
  return http.post<unknown, { result: FactoryProspectListResponse }>('/factory/prospects/list', params);
}

// 迁移后API必须保持一致
export const getFactoryProspectList = (params: FactoryProspectSearchParams) => {
  return http.post<unknown, { result: FactoryProspectPageResponse }>('/factory/prospects/list', params);
}
```

### 规则4: 参数类型标准化

**强制要求**:
- 参数接口名称必须与原始API一致
- 参数字段名称和类型必须完全匹配
- 如果原始API有不一致，必须保持相同的不一致

**处理原则**:
```typescript
// 如果原始API定义与实际使用不一致，优先保持与原始API定义一致
interface CustomerDetailParams {
  globalCustomerId: string; // 保持与原始定义一致
}

// 即使实际使用中有不同的参数名，也要保持API定义层面的一致性
```

### 规则5: 返回类型标准化

**强制要求**:
- 返回类型必须与原始API完全一致
- 泛型参数格式必须匹配
- 响应数据结构必须保持一致

**标准格式**:
```typescript
// 标准的返回类型格式
return http.post<unknown, ResponseType>('/api/path', params);

// 如果原始API有特殊的响应包装，必须保持一致
return http.post<unknown, { result: ResponseType }>('/api/path', params);
```

## 迁移检查清单

### 迁移前准备
- [ ] 找到并分析原始API文件
- [ ] 记录所有API的URL、方法、参数、返回类型
- [ ] 检查原始API中是否有不一致的地方
- [ ] 确认Mock数据是否需要同步更新

### 迁移过程检查
- [ ] 导入语句正确 (`import { http } from '@/utils/http'`)
- [ ] URL路径与原始API完全一致
- [ ] 请求方法与原始API完全一致
- [ ] 参数类型与原始API完全一致
- [ ] 返回类型与原始API完全一致
- [ ] Mock数据函数名称和参数匹配

### 迁移后验证
- [ ] TypeScript编译无错误
- [ ] 所有API接口类型定义正确
- [ ] Mock API正常工作
- [ ] 实际API调用参数正确
- [ ] 响应数据处理正确

## 常见错误和解决方案

### 错误1: 导入库错误
```typescript
// ❌ 错误
import request from '@/api';

// ✅ 正确
import { http } from '@/utils/http';
```

### 错误2: URL前缀错误
```typescript
// ❌ 错误
'/sales/factory-prospect/list'

// ✅ 正确
'/factory/prospects/list'
```

### 错误3: 请求方法错误
```typescript
// ❌ 错误 - 原始API是POST但改成了GET
return http.get('/factory/prospects/list');

// ✅ 正确 - 保持与原始API一致
return http.post('/factory/prospects/list', params);
```

### 错误4: 参数类型不匹配
```typescript
// ❌ 错误 - 参数名称不一致
{ storeProspectId: string }

// ✅ 正确 - 与原始API定义一致
{ globalCustomerId: string }
```

### 错误5: 返回类型格式错误
```typescript
// ❌ 错误
return http.post<any, ResponseType>('/api/path', params);

// ✅ 正确
return http.post<unknown, ResponseType>('/api/path', params);
```

## 工具和自动化

### 建议的验证工具
1. **TypeScript编译器** - 检查类型错误
2. **ESLint规则** - 检查导入和使用规范
3. **API对比脚本** - 自动对比原始API和迁移后API
4. **Mock数据验证** - 确保Mock数据与API接口匹配

### 自动化检查脚本示例
```bash
# 检查API导入是否正确
grep -r "import.*@/api" src/api/modules/ && echo "发现错误的导入"

# 检查URL前缀是否一致
grep -r "/sales/factory-prospect" src/api/modules/ && echo "发现错误的URL前缀"
```

## 总结

API迁移是一个需要极其谨慎的过程，任何细微的差异都可能导致功能异常。通过遵循本规则文件，可以确保：

1. **一致性** - 与原始API保持完全一致
2. **可靠性** - 避免运行时错误和类型错误
3. **可维护性** - 保持代码结构的清晰和规范
4. **可扩展性** - 为后续的API迁移提供标准模板

**核心原则**: 在没有明确需求变更的情况下，迁移后的API必须与原始API在所有方面保持100%一致。

---

**文档版本**: v1.0  
**创建时间**: 2025-07-24  
**维护人**: cc-fe (Frontend Specialist)  
**适用范围**: 所有前端API迁移项目
