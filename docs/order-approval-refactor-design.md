# 订单审批管理页面重构设计文档

## 1. 项目基本信息

- **重构目标文件**: `src/views/customerOrderManagement/OrderApprovalManagementView.vue`
- **目标模块**: `sales` (销售模块)
- **功能名称**: `orderApproval` (订单审批)
- **重构类型**: 页面模块化重构 + 数据字典标准化

## 2. 现状分析

### 2.1 当前文件结构问题
- ❌ 页面位置不符合模块化规范：`src/views/customerOrderManagement/`
- ❌ 使用非标准分页参数：`currentPage` 而非 `pageNum`
- ❌ 分页响应结构不统一：`response.result.records` 和 `response.result.total`
- ❌ 数据字典实现方式不规范：直接使用硬编码映射
- ❌ 缺少独立的API模块、类型定义和Mock数据

### 2.2 MyBatisPlus分页参数分析

基于代码分析，项目中存在**两种分页参数规范**：

#### 2.2.1 标准MyBatisPlus规范（推荐）
```typescript
// 请求参数
interface PageParams {
  pageNum: number;    // 当前页码，从1开始
  pageSize: number;   // 每页条数
}

// 响应结构
interface PageResponse<T> {
  records: T[];       // 数据列表
  total: number;      // 总条数
  pageNum: number;    // 当前页码
  pageSize: number;   // 每页条数
  pages: number;      // 总页数
}
```

#### 2.2.2 当前页面使用的非标准方式
```typescript
// 当前使用（需要修正）
const pagination = reactive({
  currentPage: 1,     // ❌ 应为 pageNum
  pageSize: 20
})

// API调用
const params = {
  page: pagination.currentPage,  // ❌ 应为 pageNum
  pageSize: pagination.pageSize
}
```

### 2.3 数据字典实现对比分析

#### 2.3.1 参考页面（ProspectsView.vue）的标准实现
```vue
// 字典数据获取
const { getOptions,getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.PROSPECT_STATUS
]);

// 字段转义方法
const getName = (dictionaryType: DictionaryType,code: string) => {
  return getNameByCode(dictionaryType, code) || code;
};

// 表格列中的使用
<el-table-column :label="t('prospects.sourceChannel')" prop="sourceChannel" min-width="120">
  <template #default="{ row }">
    {{ getName(DICTIONARY_TYPES.CUSTOMER_SOURCE,row.sourceChannel) }}
  </template>
</el-table-column>
```

#### 2.3.2 当前页面的非标准实现
```vue
// 硬编码的标签类型映射
const getApprovalTypeTagType = (type: ApprovalType) => {
  const typeMap = {
    cancel_order: 'danger',
    modify_info: 'primary'
  }
  return typeMap[type] || 'info'
}

// 字段转义方法
const getApprovalTypeText = (type: ApprovalType) => {
  return getNameByCode(DICTIONARY_TYPES.APPROVAL_TYPE, type) || type
}
```

## 3. 重构目标结构

### 3.1 目标目录结构
```
src/
├── views/sales/orderApproval/
│   ├── OrderApprovalView.vue           # 重构后的主页面
│   └── components/                     # 组件目录
│       ├── ApprovalDetailDialog.vue
│       ├── ApprovalActionDialog.vue
│       ├── ApprovalHistoryDialog.vue
│       └── ApprovalExportDialog.vue
├── api/modules/sales/
│   └── orderApproval.ts               # API模块
├── types/sales/
│   └── orderApproval.d.ts             # 类型定义
├── mock/data/sales/
│   └── orderApproval.ts               # Mock数据
└── locales/modules/sales/
    ├── zh.json                        # 中文翻译（包含orderApproval）
    └── en.json                        # 英文翻译（包含orderApproval）
```

## 4. MyBatisPlus分页组件标准化实现

### 4.1 分页参数标准化

#### 4.1.1 类型定义修正
```typescript
// src/types/sales/orderApproval.d.ts
export interface OrderApprovalSearchParams {
  pageNum?: number;        // ✅ 标准MyBatisPlus参数
  pageSize?: number;       // ✅ 标准MyBatisPlus参数
  approvalType?: string;
  orderNo?: string;
  submitterName?: string;
  submitTimeStart?: string;
  submitTimeEnd?: string;
  approvalResult?: string;
  dealerId?: string;
  status?: 'pending' | 'approved';
}

export interface OrderApprovalPageResponse {
  records: OrderApprovalListItem[];  // ✅ 标准MyBatisPlus响应
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}
```

#### 4.1.2 分页组件配置修正
```vue
<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="pagination.pageNum"    <!-- ✅ 修正为pageNum -->
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
// 分页状态修正
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 20,
  total: 0
})

// API调用参数修正
const fetchData = async () => {
  const params: OrderApprovalSearchParams = {
    ...searchParams,
    status: activeTab.value,
    pageNum: pagination.pageNum,    // ✅ 修正为pageNum
    pageSize: pagination.pageSize
  }
  
  const response = await getOrderApprovalList(params)
  tableData.value = response.records    // ✅ 标准响应结构
  pagination.total = response.total
}

// 分页事件处理修正
const handleCurrentChange = (page: number) => {
  pagination.pageNum = page    // ✅ 修正为pageNum
  fetchData()
}
</script>
```

### 4.2 API模块标准化实现

```typescript
// src/api/modules/sales/orderApproval.ts
import request from '@/api';
import type { 
  OrderApprovalSearchParams, 
  OrderApprovalPageResponse 
} from '@/types/sales/orderApproval';
import { getOrderApprovalListMock } from '@/mock/data/sales/orderApproval';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getOrderApprovalList = (
  params: OrderApprovalSearchParams
): Promise<OrderApprovalPageResponse> => {
  if (USE_MOCK_API) {
    return getOrderApprovalListMock(params);
  } else {
    // 标准MyBatisPlus分页接口
    return request.post<any, OrderApprovalPageResponse>(
      '/sales/order-approval/page', 
      params
    );
  }
};
```

## 5. 数据字典标准化实现

### 5.1 字典类型定义
```typescript
// src/constants/dictionary.ts (新增)
export const DICTIONARY_TYPES = {
  // 现有字典类型...
  APPROVAL_TYPE: 'APPROVAL_TYPE',           // 审批类型
  APPROVAL_STATUS: 'APPROVAL_STATUS',       // 审批状态
  ORDER_STATUS: 'ORDER_STATUS',             // 订单状态
} as const;
```

### 5.2 标准化字典使用方式

#### 5.2.1 组合式函数引入
```typescript
// 参考ProspectsView.vue的标准实现
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.APPROVAL_TYPE,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.ORDER_STATUS
]);
```

#### 5.2.2 字段转义函数标准化
```typescript
// 统一的字段转义函数（参考ProspectsView.vue）
const getName = (dictionaryType: DictionaryType, code: string) => {
  return getNameByCode(dictionaryType, code) || code;
};

// 获取字典选项
const approvalTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_TYPE));
const approvalStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_STATUS));
```

#### 5.2.3 标签样式映射标准化
```typescript
// 标准化的状态标签映射配置
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.APPROVAL_TYPE]: {
    'cancel_order': 'danger',
    'modify_info': 'primary',
    'price_adjustment': 'warning'
  },
  [DICTIONARY_TYPES.APPROVAL_STATUS]: {
    'approved': 'success',
    'rejected': 'danger',
    'pending': 'warning'
  }
} as const;

// 统一的标签类型获取函数
const getStatusTagType = (status: string, dictionaryType: DictionaryType) => {
  return STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info';
};
```

#### 5.2.4 表格列实现标准化
```vue
<!-- 审批类型列 -->
<el-table-column prop="approvalType" :label="t('approvalType')" width="140">
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.approvalType, DICTIONARY_TYPES.APPROVAL_TYPE)">
      {{ getName(DICTIONARY_TYPES.APPROVAL_TYPE, row.approvalType) }}
    </el-tag>
  </template>
</el-table-column>

<!-- 审批结果列 -->
<el-table-column
  v-if="activeTab === 'approved'"
  prop="approvalResult"
  :label="t('approvalResult')"
  width="120"
>
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.approvalResult, DICTIONARY_TYPES.APPROVAL_STATUS)">
      {{ getName(DICTIONARY_TYPES.APPROVAL_STATUS, row.approvalResult) }}
    </el-tag>
  </template>
</el-table-column>
```

#### 5.2.5 搜索表单下拉选择标准化
```vue
<!-- 审批类型下拉 -->
<el-form-item :label="t('approvalType')">
  <el-select
    v-model="searchParams.approvalType"
    :placeholder="t('approvalTypePlaceholder')"
    clearable
    :loading="dictionaryLoading"
  >
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in approvalTypeOptions"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>
```

## 6. Mock数据实现规范

### 6.1 Mock数据结构
```typescript
// src/mock/data/sales/orderApproval.ts
import type {
  OrderApprovalSearchParams,
  OrderApprovalPageResponse,
  OrderApprovalListItem
} from '@/types/sales/orderApproval';

// 动态生成Mock数据（25-30条）
function generateMockOrderApprovalData(): OrderApprovalListItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData: OrderApprovalListItem[] = [];

  const approvalTypes = ['cancel_order', 'modify_info', 'price_adjustment'];
  const approvalResults = ['approved', 'rejected', 'pending'];

  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      id: `approval_${i + 1}`,
      approvalNo: `AP${String(i + 1).padStart(6, '0')}`,
      approvalType: approvalTypes[Math.floor(Math.random() * approvalTypes.length)],
      orderNo: `ORD${String(i + 1).padStart(8, '0')}`,
      submitterName: `提交人${i + 1}`,
      submitTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      reason: `审批原因${i + 1}`,
      approvalResult: approvalResults[Math.floor(Math.random() * approvalResults.length)],
      approvalTime: new Date().toISOString(),
      approvalUserName: `审批人${i + 1}`,
      dealerId: `dealer_${i + 1}`
    });
  }

  return mockData;
}

const mockData = generateMockOrderApprovalData();

export const getOrderApprovalListMock = (
  params: OrderApprovalSearchParams
): Promise<OrderApprovalPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.approvalType) {
        filteredData = filteredData.filter(item =>
          item.approvalType === params.approvalType
        );
      }

      if (params.orderNo) {
        filteredData = filteredData.filter(item =>
          item.orderNo.includes(params.orderNo!)
        );
      }

      if (params.status) {
        if (params.status === 'pending') {
          filteredData = filteredData.filter(item =>
            item.approvalResult === 'pending'
          );
        } else {
          filteredData = filteredData.filter(item =>
            item.approvalResult !== 'pending'
          );
        }
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        records: filteredData.slice(start, end),
        total,
        pageNum,
        pageSize,
        pages
      });
    }, 500);
  });
};
```

## 7. 国际化文件更新

### 7.1 中文翻译文件
```json
// src/locales/modules/sales/zh.json
{
  "orderApproval": {
    "pageTitle": "订单审批管理",
    "pendingTab": "待审批",
    "approvedTab": "已审批",
    "searchTitle": "筛选条件",
    "approvalType": "审批类型",
    "approvalTypePlaceholder": "请选择审批类型",
    "orderNumber": "订单编号",
    "orderNumberPlaceholder": "请输入订单编号",
    "submittedBy": "提交人",
    "submittedByPlaceholder": "请输入提交人",
    "submissionTime": "提交时间",
    "startDate": "开始日期",
    "endDate": "结束日期",
    "approvalResult": "审批结果",
    "approvalResultPlaceholder": "请选择审批结果",
    "store": "门店",
    "storePlaceholder": "请选择门店",
    "serialNumber": "序号",
    "approvalNumber": "审批单号",
    "applicationReason": "申请原因",
    "approvalTime": "审批时间",
    "approvedBy": "审批人",
    "review": "审核",
    "pendingApprovalList": "待审批列表",
    "approvedList": "已审批列表",
    "totalCount": "共 {count} 条记录",
    "fetchDataFailed": "获取数据失败",
    "orderDetailNotImplemented": "订单详情功能待实现",
    "operationSuccess": "操作成功",
    "exportSuccess": "导出成功"
  }
}
```

## 8. 路由配置更新

```typescript
// src/router/index.ts
{
  path: '/sales/order-approval',
  name: 'OrderApproval',
  component: () => import('@/views/sales/orderApproval/OrderApprovalView.vue'),
  meta: {
    title: 'menu.orderApproval',
    requiresAuth: true,
    icon: 'DocumentChecked'
  }
}
```

## 9. 重构执行计划

### 9.1 第一阶段：基础结构重构
1. 创建目标目录结构
2. 创建类型定义文件
3. 创建Mock数据模块
4. 创建API模块

### 9.2 第二阶段：分页标准化
1. 修正分页参数命名（currentPage → pageNum）
2. 统一分页响应结构处理
3. 更新API调用方式

### 9.3 第三阶段：数据字典标准化
1. 引入标准字典组合式函数
2. 实现统一的字段转义方法
3. 标准化标签样式映射
4. 更新表格列和搜索表单实现

### 9.4 第四阶段：页面迁移和测试
1. 迁移页面文件到新位置
2. 更新国际化文件
3. 更新路由配置
4. 功能测试和验证

## 10. 验证清单

### 10.1 分页功能验证
- [ ] 分页参数使用标准的 `pageNum` 和 `pageSize`
- [ ] 分页响应结构符合MyBatisPlus标准
- [ ] 分页组件正常工作（页码切换、页大小切换）
- [ ] Mock数据支持分页功能

### 10.2 数据字典验证
- [ ] 使用 `useBatchDictionary` 获取字典数据
- [ ] 字段转义使用统一的 `getName` 方法
- [ ] 标签样式映射配置标准化
- [ ] 搜索下拉选择使用字典数据

### 10.3 功能完整性验证
- [ ] Tab切换功能正常
- [ ] 搜索功能正常工作
- [ ] 表格数据正确显示
- [ ] 国际化切换正常
- [ ] Mock数据功能完整

---

**本设计文档基于项目现有规范和最佳实践，确保重构后的页面完全符合项目标准，特别是MyBatisPlus分页规范和数据字典使用规范。**
```
