 # 系统管理-用户旅程文档及功能清单

## 用户角色定义

### 系统管理员 (System Administrator)
- **职责**：负责系统基础数据配置、用户权限管理、门店组织架构管理
- **权限**：系统管理模块完整权限（增删改查）
- **工作场景**：系统初始化、组织架构调整、用户入职离职、权限分配

### 超级管理员 (Super Administrator)
- **职责**：负责系统总体配置、跨门店数据管理、系统安全管理
- **权限**：全系统最高权限，包括Factory级别角色管理
- **工作场景**：系统维护、安全管理、跨门店业务协调

## 用户旅程分析

### 系统管理员用户旅程

#### 旅程1：新用户入职管理

**Given**: 新员工入职，需要在系统中创建用户账号并分配角色
**When**: 系统管理员收到新用户开户需求

**用户旅程步骤**：

1. **登录系统**
   - 用户操作：使用管理员账号登录DMS系统
   - 系统响应：验证身份，进入系统首页
   - 用户感受：快速进入管理界面

2. **进入用户管理页面**
   - 用户操作：点击"系统管理-用户管理"菜单
   - 系统响应：显示用户列表页面
   - 用户感受：界面清晰，功能明确

3. **查看现有用户**
   - 用户操作：浏览用户列表，可使用筛选功能
   - 系统响应：显示用户基本信息和状态
   - 用户感受：用户信息一目了然，便于管理

4. **创建新用户**
   - 用户操作：点击"新增用户"按钮
   - 系统响应：打开用户创建表单
   - 用户感受：表单简洁明了

5. **填写用户信息**
   - 用户操作：填写用户名、姓名、联系方式、用户类型等信息
   - 系统响应：实时验证数据格式和唯一性
   - 用户感受：输入体验流畅，错误提示清晰

6. **保存用户信息**
   - 用户操作：确认保存用户信息
   - 系统响应：创建用户账号，显示成功提示
   - 用户感受：操作反馈及时，结果明确

7. **分配用户角色**
   - 用户操作：点击"分配角色"按钮
   - 系统响应：打开角色分配界面
   - 用户感受：角色管理界面专业且易用

8. **配置门店角色**
   - 用户操作：选择门店、部门、角色，设置职位信息
   - 系统响应：动态加载相关选项，支持多角色配置
   - 用户感受：配置灵活，支持复杂业务场景

9. **确认角色分配**
   - 用户操作：设置主角色，确认保存
   - 系统响应：更新用户权限，显示分配结果
   - 用户感受：权限分配准确，用户可正常使用系统

**Then**: 新用户账号创建完成，权限配置生效，可以正常使用系统功能

#### 旅程2：组织架构管理

**Given**: 公司业务发展，需要调整门店和部门结构
**When**: 系统管理员需要维护组织架构

**用户旅程步骤**：

1. **门店结构管理**
   - 用户操作：进入门店管理页面，查看门店树形结构
   - 系统响应：显示完整的门店层级关系
   - 用户感受：门店关系清晰，便于理解

2. **新增门店**
   - 用户操作：点击"新增门店"，选择父门店，填写门店信息
   - 系统响应：验证门店编码唯一性，保存门店信息
   - 用户感受：门店创建流程简单，层级关系明确

3. **部门结构管理**
   - 用户操作：进入部门管理页面，查看部门树形结构
   - 系统响应：显示部门层级和相关信息
   - 用户感受：部门结构清晰，便于管理

4. **调整部门结构**
   - 用户操作：新增、编辑、删除部门，调整层级关系
   - 系统响应：实时更新部门树，检查数据完整性
   - 用户感受：操作灵活，变更实时生效

**Then**: 组织架构调整完成，相关用户权限自动更新

#### 旅程3：权限配置管理

**Given**: 业务需求变化，需要调整角色权限配置
**When**: 系统管理员需要维护角色权限

**用户旅程步骤**：

1. **角色权限分析**
   - 用户操作：进入角色管理页面，查看现有角色列表
   - 系统响应：显示角色详情、权限范围、关联用户数
   - 用户感受：角色信息全面，便于分析

2. **创建新角色**
   - 用户操作：点击"新增角色"，设置角色基本信息
   - 系统响应：验证角色编码，保存角色信息
   - 用户感受：角色创建简单，信息完整

3. **配置菜单权限**
   - 用户操作：点击"配置菜单权限"，选择菜单权限
   - 系统响应：显示菜单树，支持多选配置
   - 用户感受：权限配置直观，操作便捷

4. **配置数据权限**
   - 用户操作：点击"配置数据权限"，设置数据访问范围
   - 系统响应：根据权限范围显示相应配置选项
   - 用户感受：数据权限配置灵活，满足业务需求

5. **权限验证测试**
   - 用户操作：保存权限配置，通知相关用户测试
   - 系统响应：权限立即生效，用户可验证权限正确性
   - 用户感受：权限生效及时，验证方便

**Then**: 角色权限配置完成，用户可按新权限使用系统

#### 旅程4：菜单权限维护

**Given**: 系统功能更新，需要维护菜单权限配置
**When**: 系统管理员需要更新菜单权限

**用户旅程步骤**：

1. **菜单结构管理**
   - 用户操作：进入菜单管理页面，查看菜单树形结构
   - 系统响应：显示完整菜单层级和权限标识
   - 用户感受：菜单结构清晰，权限标识明确

2. **新增菜单项**
   - 用户操作：点击"新增菜单"，选择菜单类型和父菜单
   - 系统响应：显示相应表单，支持不同类型菜单配置
   - 用户感受：菜单类型明确，配置项清晰

3. **配置权限标识**
   - 用户操作：设置菜单权限标识，配置访问权限
   - 系统响应：验证权限标识格式，保存配置
   - 用户感受：权限标识规范，便于管理

4. **更新角色权限**
   - 用户操作：将新菜单权限分配给相应角色
   - 系统响应：更新角色权限，通知相关用户
   - 用户感受：权限分配及时，用户可正常访问新功能

**Then**: 菜单权限配置完成，用户可按权限访问相应功能

## 核心功能清单

### 1. 用户管理功能

#### 1.1 用户列表管理功能
- **功能描述**：展示系统中所有用户的基本信息和状态
- **用户故事**：作为系统管理员，我希望能够查看所有用户信息，以便全面了解用户状况
- **功能细节**：
  - 显示用户基本信息：用户名、姓名、手机号、邮箱
  - 显示用户类型：Factory用户/Store用户标识
  - 显示主门店信息：用户所属主要门店
  - 显示用户状态：正常/禁用状态切换
  - 支持多条件筛选：用户名、类型、门店
  - 支持分页显示，提升页面性能

#### 1.2 用户创建编辑功能
- **功能描述**：创建新用户或编辑现有用户信息
- **用户故事**：作为系统管理员，我希望能够创建和编辑用户，以便管理用户账号
- **功能细节**：
  - 用户基本信息录入：用户名、姓名、联系方式
  - 用户类型选择：Factory/Store类型
  - 用户状态设置：正常/禁用
  - 入职日期设置：记录用户入职时间
  - 备注信息录入：用户相关说明
  - 数据验证：用户名唯一性、邮箱格式验证

#### 1.3 用户角色分配功能
- **功能描述**：为用户分配门店角色和权限
- **用户故事**：作为系统管理员，我希望能够灵活分配用户角色，以便满足不同业务需求
- **功能细节**：
  - 支持多门店角色分配：一个用户可在多个门店担任角色
  - 部门角色配置：选择门店、部门、角色
  - 职位信息设置：设置用户在各门店的职位
  - 主角色设置：指定用户的主要角色
  - 权限动态加载：根据门店动态加载部门和角色选项
  - Factory用户限制：Factory用户最多只能分配一个门店角色

#### 1.4 用户状态管理功能
- **功能描述**：管理用户账号状态，支持启用/禁用操作
- **用户故事**：作为系统管理员，我希望能够管理用户状态，以便控制用户访问权限
- **功能细节**：
  - 用户状态切换：正常/禁用状态切换
  - 状态即时生效：状态变更立即影响用户登录
  - 批量状态管理：支持批量启用/禁用用户
  - 状态变更记录：记录状态变更历史

### 2. 门店管理功能

#### 2.1 门店层级管理功能
- **功能描述**：管理门店的层级结构和组织关系
- **用户故事**：作为系统管理员，我希望能够管理门店层级结构，以便反映真实的组织架构
- **功能细节**：
  - 树形结构显示：以树形结构展示门店层级关系
  - 层级关系管理：支持父子门店关系设置
  - 总部门店管理：总部作为根节点的特殊处理
  - 门店类型管理：总部、主店、分店、仓库等类型
  - 展开/折叠功能：支持树形结构的展开和折叠

#### 2.2 门店信息管理功能
- **功能描述**：管理门店的基本信息和属性配置
- **用户故事**：作为系统管理员，我希望能够管理门店信息，以便维护准确的门店数据
- **功能细节**：
  - 门店基本信息：门店编码、名称、类型
  - 联系信息管理：经理、联系人、联系电话
  - 地址信息管理：省市区、详细地址
  - 门店属性配置：销售属性、售后属性
  - 门店状态管理：正常/停用状态
  - 门店编码唯一性：确保门店编码全局唯一

#### 2.3 门店筛选查询功能
- **功能描述**：支持多条件筛选查询门店信息
- **用户故事**：作为系统管理员，我希望能够快速查询门店信息，以便提高工作效率
- **功能细节**：
  - 门店名称模糊匹配
  - 门店编码精确匹配
  - 门店状态筛选
  - 门店类型筛选
  - 实时筛选结果更新
  - 筛选条件重置功能

### 3. 部门管理功能

#### 3.1 部门层级管理功能
- **功能描述**：管理部门的层级结构和组织关系
- **用户故事**：作为系统管理员，我希望能够管理部门层级结构，以便反映组织架构
- **功能细节**：
  - 树形结构显示：以树形结构展示部门层级关系
  - 层级关系管理：支持父子部门关系设置
  - 部门类型管理：业务部门、支持部门、管理部门
  - 展开/折叠功能：支持树形结构的展开和折叠
  - 部门负责人设置：设置部门负责人信息

#### 3.2 部门信息管理功能
- **功能描述**：管理部门的基本信息和配置
- **用户故事**：作为系统管理员，我希望能够管理部门信息，以便维护准确的组织架构
- **功能细节**：
  - 部门基本信息：部门名称、部门编码、类型
  - 部门状态管理：正常/禁用状态
  - 部门负责人：设置部门负责人
  - 部门描述信息：详细描述部门职责
  - 部门编码唯一性：确保部门编码唯一

#### 3.3 部门查询筛选功能
- **功能描述**：支持多条件筛选查询部门信息
- **用户故事**：作为系统管理员，我希望能够快速查询部门信息，以便提高管理效率
- **功能细节**：
  - 部门名称模糊匹配
  - 部门状态筛选
  - 部门类型筛选
  - 实时筛选结果更新
  - 筛选条件重置功能

### 4. 角色管理功能

#### 4.1 角色列表管理功能
- **功能描述**：管理系统中的所有角色信息
- **用户故事**：作为系统管理员，我希望能够查看和管理所有角色，以便进行权限配置
- **功能细节**：
  - 角色基本信息展示：角色名称、编码、来源
  - 角色分类显示：Factory角色/Store角色区分
  - 所属门店信息：Store角色显示所属门店
  - 数据权限范围：显示角色的数据权限范围
  - 角色状态管理：正常/禁用状态切换
  - 支持多条件筛选：角色名称、来源、门店

#### 4.2 角色创建编辑功能
- **功能描述**：创建新角色或编辑现有角色信息
- **用户故事**：作为系统管理员，我希望能够创建和编辑角色，以便满足业务需求
- **功能细节**：
  - 角色基本信息：角色名称、编码、描述
  - 角色来源设置：Factory/Store角色选择
  - 所属门店设置：Store角色需要设置所属门店
  - 数据权限配置：全部、自定义、部门、仅个人等范围
  - 自定义部门权限：支持选择特定部门作为数据权限范围
  - 角色状态设置：正常/禁用状态

#### 4.3 角色权限配置功能
- **功能描述**：配置角色的菜单权限和数据权限
- **用户故事**：作为系统管理员，我希望能够灵活配置角色权限，以便实现精细化权限控制
- **功能细节**：
  - 菜单权限配置：通过菜单树选择角色可访问的菜单
  - 数据权限配置：设置角色的数据访问范围
  - 权限树形显示：以树形结构显示权限选项
  - 权限批量操作：支持批量选择/取消权限
  - 权限预览功能：预览权限配置效果
  - 权限即时生效：权限配置立即生效

### 5. 菜单管理功能

#### 5.1 菜单层级管理功能
- **功能描述**：管理菜单的层级结构和组织关系
- **用户故事**：作为系统管理员，我希望能够管理菜单层级结构，以便组织系统功能
- **功能细节**：
  - 树形结构显示：以树形结构展示菜单层级关系
  - 菜单类型管理：目录、菜单、按钮三种类型
  - 菜单归属管理：Factory端/Dealer端菜单区分
  - 展开/折叠功能：支持树形结构的展开和折叠
  - 菜单排序功能：支持菜单顺序调整

#### 5.2 菜单信息管理功能
- **功能描述**：管理菜单的基本信息和配置
- **用户故事**：作为系统管理员，我希望能够管理菜单信息，以便维护系统功能结构
- **功能细节**：
  - 菜单基本信息：菜单名称、编码、类型
  - 菜单路径配置：路由路径、组件路径
  - 菜单图标设置：菜单显示图标
  - 权限标识配置：菜单权限标识符
  - 菜单状态管理：正常/禁用状态
  - 显示控制：是否显示、是否缓存等属性

#### 5.3 菜单查询筛选功能
- **功能描述**：支持多条件筛选查询菜单信息
- **用户故事**：作为系统管理员，我希望能够快速查询菜单信息，以便提高管理效率
- **功能细节**：
  - 菜单名称模糊匹配
  - 菜单状态筛选
  - 菜单归属筛选（Factory/Dealer）
  - 菜单类型筛选
  - 实时筛选结果更新
  - 筛选条件重置功能

### 6. 权限控制功能

#### 6.1 基于角色的权限控制功能
- **功能描述**：实现基于角色的访问控制（RBAC）
- **用户故事**：作为系统管理员，我希望能够通过角色控制用户权限，以便实现安全的权限管理
- **功能细节**：
  - 角色权限继承：用户继承所有角色的权限
  - 权限合并策略：多角色权限取并集
  - 权限实时验证：访问时实时验证权限
  - 权限缓存机制：提高权限验证性能
  - 权限变更通知：权限变更时通知相关用户

#### 6.2 数据权限控制功能
- **功能描述**：实现细粒度的数据访问控制
- **用户故事**：作为系统管理员，我希望能够控制用户的数据访问范围，以便保护数据安全
- **功能细节**：
  - 全部数据权限：超级管理员权限
  - 自定义数据权限：指定部门数据访问
  - 部门数据权限：本部门数据访问
  - 部门及下级权限：本部门及下级部门数据
  - 仅个人权限：只能访问个人创建的数据
  - 数据过滤机制：查询时自动过滤数据

#### 6.3 多租户权限隔离功能
- **功能描述**：实现Factory和Store的权限隔离
- **用户故事**：作为系统管理员，我希望能够实现多租户权限隔离，以便保护各门店数据安全
- **功能细节**：
  - Factory权限：可访问所有门店数据
  - Store权限：只能访问所属门店数据
  - 跨门店权限：特殊角色可访问多门店数据
  - 权限边界控制：严格控制跨门店数据访问
  - 审计日志记录：记录跨门店数据访问

## 功能优先级排序

### 高优先级（P0）
1. 用户管理基础功能（创建、编辑、查询）
2. 角色管理基础功能（创建、编辑、权限配置）
3. 菜单权限管理
4. 基础权限控制功能

### 中优先级（P1）
1. 门店管理功能
2. 部门管理功能
3. 用户角色分配功能
4. 数据权限控制功能

### 低优先级（P2）
1. 高级筛选功能
2. 批量操作功能
3. 权限审计功能
4. 操作日志记录

## 用户体验关键指标

### 效率指标
- 用户创建时间：< 2分钟
- 角色权限配置时间：< 5分钟
- 权限验证响应时间：< 100ms
- 页面加载时间：< 3秒

### 准确性指标
- 权限配置准确率：100%
- 用户信息准确率：100%
- 权限验证准确率：100%

### 安全性指标
- 权限变更记录：100%
- 敏感操作审计：100%
- 数据访问控制有效性：100%

### 易用性指标
- 功能学习时间：< 1小时
- 操作错误率：< 2%
- 用户满意度：> 4.5/5

---
**文档维护**：本文档应根据业务发展和用户反馈持续优化更新。