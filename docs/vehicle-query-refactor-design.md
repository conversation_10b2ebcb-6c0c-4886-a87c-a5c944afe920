# VehicleQueryView.vue 重构设计文档

> **重要说明**: 本设计文档已根据 `/规范/页面目录结构规范.md` 进行审查和修正，确保完全符合DMS前端项目的目录结构规范。
>
> **模块归属说明**: 车辆查询功能归属于 `sales` 模块，因为车辆资源管理是销售业务的核心组成部分，包括车辆库存查询、车辆状态管理等都直接服务于销售流程。

## 1. 重构目标与范围

### 1.1 重构目标
将 `src/views/vehicleResourceManagement/VehicleQueryView.vue` 按照DMS前端重构技术规范进行模块化重构，实现：
- 符合目录结构规范的模块化组织
- 统一的MyBatisPlus分页组件标准化
- 规范的数据字典实现方式
- 标准的API请求响应处理

### 1.2 重构范围
- **源文件**: `src/views/vehicleResourceManagement/VehicleQueryView.vue`
- **目标模块**: `sales` (销售模块 - 车辆资源管理属于销售业务范畴)
- **功能名称**: `vehicleQuery` (车辆查询)

## 2. 现状分析

### 2.1 当前文件结构问题
```
❌ 当前结构:
src/views/vehicleResourceManagement/VehicleQueryView.vue  # 不符合模块化规范

✅ 目标结构:
src/views/sales/vehicleQuery/VehicleQueryView.vue  # 符合模块化规范
```

### 2.2 MyBatisPlus分页问题分析
**当前实现**:
```typescript
// ✅ 分页参数正确
const pagination = reactive({
  pageNum: 1,        // 正确使用pageNum
  pageSize: 10,      // 正确使用pageSize
  total: 0,
});

// ✅ 响应处理正确
vehicleList.value = response.result.records;  // 正确使用records
pagination.total = response.result.total;     // 正确使用total
```

**评估结果**: 当前分页实现已符合MyBatisPlus标准，无需修改。

### 2.3 数据字典实现问题分析
**当前实现问题**:
```typescript
// ❌ 硬编码字典值和国际化
<el-tag :type="getStockStatusTagType(row.stockStatus)">
  {{ t(`vehicleQuery.stockStatusOptions.${row.stockStatus}`) }}
</el-tag>

// ❌ 手动实现字典转义函数
const getStockStatusTagType = (status: VehicleListItem['stockStatus']) => {
  switch (status) {
    case 'inStock': return 'success';
    case 'allocated': return 'warning';
    // ...
  }
};
```

**目标实现** (参考ProspectsView.vue):
```typescript
// ✅ 使用标准数据字典
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_STOCK_STATUS,
  DICTIONARY_TYPES.VEHICLE_LOCK_STATUS,
  // ...
]);

// ✅ 标准字典转义
<el-tag :type="getStatusTagType(row.stockStatus)">
  {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_STOCK_STATUS, row.stockStatus) }}
</el-tag>
```

### 2.4 API响应处理分析
**当前实现**:
```typescript
// ✅ 正确的响应处理
const response = await getVehicleList(requestParams);
vehicleList.value = response.result.records;  // 正确使用result.records
pagination.total = response.result.total;     // 正确使用result.total
```

**评估结果**: 当前API响应处理已符合标准，使用了`response.result`模式。

## 3. 重构技术方案

### 3.1 目录结构重构

#### 3.1.1 创建目标目录结构
```bash
# 创建模块化目录
mkdir -p src/views/sales/vehicleQuery/components
mkdir -p src/api/modules/sales
mkdir -p src/types/sales
mkdir -p src/mock/data/sales
```

#### 3.1.2 文件迁移映射
```
源文件 → 目标文件:
src/views/vehicleResourceManagement/VehicleQueryView.vue
→ src/views/sales/vehicleQuery/VehicleQueryView.vue

新增文件:
src/api/modules/sales/vehicleQuery.ts             # API模块
src/types/sales/vehicleQuery.d.ts                 # 类型定义
src/mock/data/sales/vehicleQuery.ts               # Mock数据
```

### 3.2 MyBatisPlus分页标准化实现

#### 3.2.1 分页参数规范 (已符合标准)
```typescript
// ✅ 当前实现已正确，保持不变
interface VehicleQuerySearchParams {
  pageNum?: number;    // MyBatisPlus标准参数
  pageSize?: number;   // MyBatisPlus标准参数
  vin?: string;
  factoryOrderNo?: string;
  // ... 其他搜索参数
}
```

#### 3.2.2 分页响应结构 (已符合标准)
```typescript
// ✅ 当前实现已正确，保持不变
interface VehicleQueryPageResponse {
  result: {
    records: VehicleListItem[];  // MyBatisPlus标准响应
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
  };
}
```

#### 3.2.3 分页组件配置 (已符合标准)
```vue
<!-- ✅ 当前实现已正确，保持不变 -->
<el-pagination
  v-model:current-page="pagination.pageNum"
  v-model:page-size="pagination.pageSize"
  :page-sizes="[10, 20, 50, 100]"
  :total="pagination.total"
  layout="total, sizes, prev, pager, next, jumper"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
/>
```

### 3.3 数据字典标准化实现

#### 3.3.1 字典类型定义
```typescript
// src/constants/dictionary.ts (新增)
export const DICTIONARY_TYPES = {
  // 车辆相关字典
  VEHICLE_STOCK_STATUS: '01200001',      // 库存状态
  VEHICLE_LOCK_STATUS: '01200002',       // 锁定状态
  VEHICLE_INVOICE_STATUS: '01200003',    // 开票状态
  VEHICLE_DELIVERY_STATUS: '01200004',   // 交车状态
  VEHICLE_MODEL: '01200005',             // 车型
  VEHICLE_COLOR: '01200006',             // 颜色
  WAREHOUSE: '01200007',                 // 仓库
} as const;
```

#### 3.3.2 字典使用标准化
```typescript
// 引入标准字典组合式函数
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 批量获取字典数据
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_STOCK_STATUS,
  DICTIONARY_TYPES.VEHICLE_LOCK_STATUS,
  DICTIONARY_TYPES.VEHICLE_INVOICE_STATUS,
  DICTIONARY_TYPES.VEHICLE_DELIVERY_STATUS,
  DICTIONARY_TYPES.WAREHOUSE
]);

// 标准字典转义函数
const getVehicleStatusDisplay = (type: string, code: string) => {
  return getNameByCode(type, code) || code;
};
```

#### 3.3.3 表格字段标准化实现
```vue
<!-- 库存状态列 -->
<el-table-column prop="stockStatus" :label="t('query.stockStatus')" min-width="120">
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.stockStatus, STATUS_TYPE_MAPS.STOCK_STATUS)">
      {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_STOCK_STATUS, row.stockStatus) }}
    </el-tag>
  </template>
</el-table-column>

<!-- 锁定状态列 -->
<el-table-column prop="lockStatus" :label="t('query.lockStatus')" min-width="120">
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.lockStatus, STATUS_TYPE_MAPS.LOCK_STATUS)">
      {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_LOCK_STATUS, row.lockStatus) }}
    </el-tag>
  </template>
</el-table-column>
```

#### 3.3.4 搜索表单标准化实现
```vue
<!-- 仓库下拉选择 -->
<el-form-item :label="t('query.warehouseName')">
  <el-select
    v-model="searchParams.warehouseName"
    :placeholder="tc('all')"
    clearable
  >
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in getOptions(DICTIONARY_TYPES.WAREHOUSE)"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>

<!-- 库存状态下拉选择 -->
<el-form-item :label="t('query.stockStatus')">
  <el-select
    v-model="searchParams.stockStatus"
    :placeholder="tc('all')"
    clearable
  >
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in getOptions(DICTIONARY_TYPES.VEHICLE_STOCK_STATUS)"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>
```

#### 3.3.5 状态标签样式映射
```typescript
// 标准状态样式映射配置
const STATUS_TYPE_MAPS = {
  STOCK_STATUS: {
    '01200001001': 'success',    // 在库
    '01200001002': 'warning',    // 配车
    '01200001003': 'primary',    // 在途
    '01200001004': 'info'        // 调拨
  },
  LOCK_STATUS: {
    '01200002001': 'danger',     // 已锁定
    '01200002002': 'success'     // 未锁定
  },
  INVOICE_STATUS: {
    '01200003001': 'success',    // 已开票
    '01200003002': 'info'        // 未开票
  },
  DELIVERY_STATUS: {
    '01200004001': 'success',    // 已交车
    '01200004002': 'info'        // 未交车
  }
};

// 通用状态标签类型获取函数
const getStatusTagType = (status: string, typeMap: Record<string, string>) => {
  return typeMap[status] || 'info';
};
```

### 3.4 API请求响应处理标准化

#### 3.4.1 API模块重构
```typescript
// src/api/modules/sales/vehicleQuery.ts
import request from '@/api';
import type { VehicleQuerySearchParams, VehicleQueryPageResponse } from '@/types/sales/vehicleQuery';
import { getVehicleQueryList } from '@/mock/data/sales/vehicleQuery';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getVehicleQuery = (params: VehicleQuerySearchParams): Promise<VehicleQueryPageResponse> => {
  if (USE_MOCK_API) {
    return getVehicleQueryList(params);
  } else {
    return request.get<any, VehicleQueryPageResponse>('/sales/vehicle-query/list', { params });
  }
};
```

#### 3.4.2 响应处理标准化 (已符合标准)
```typescript
// ✅ 当前实现已正确，保持标准响应处理
const fetchVehicleList = async () => {
  loading.value = true;
  try {
    const response = await getVehicleQuery(requestParams);

    // 标准响应处理：使用response.result获取数据
    vehicleList.value = response.result.records;
    pagination.total = response.result.total;

  } catch (error) {
    console.error('获取车辆列表失败:', error);
    ElMessage.error(tc('fetchFailed'));
  } finally {
    loading.value = false;
  }
};
```

### 3.5 国际化文件更新

#### 3.5.1 国际化文件结构
```json
// src/locales/modules/sales/zh.json
{
  "vehicleQuery": {
    "title": "车辆查询",
    "vin": "车架号",
    "factoryOrderNo": "工厂订单号",
    "warehouseName": "仓库名称",
    "model": "车型",
    "variant": "配置",
    "color": "颜色",
    "fmrId": "FMR ID",
    "stockStatus": "库存状态",
    "lockStatus": "锁定状态",
    "invoiceStatus": "开票状态",
    "deliveryStatus": "交车状态",
    "invoiceDate": "开票日期",
    "deliveryDate": "交车日期",
    "storageDate": "入库日期",
    "productionDate": "生产日期"
  }
}
```

#### 3.5.2 国际化引用更新
```typescript
// 更新国际化引用
const { t, tc } = useModuleI18n('sales.vehicleQuery');
```

### 3.6 路由配置更新

```typescript
// src/router/modules/sales.ts
{
  path: '/sales/vehicle-query',
  name: 'SalesVehicleQuery',
  component: () => import('@/views/sales/vehicleQuery/VehicleQueryView.vue'),
  meta: {
    title: 'menu.salesVehicleQuery',
    requiresAuth: true,
    icon: 'Search'
  }
}
```

## 4. 重构实施计划

### 4.1 重构步骤
1. **创建目录结构** - 建立模块化目录
2. **类型定义重构** - 提取并标准化类型定义
3. **Mock数据重构** - 创建模块化Mock数据
4. **API模块重构** - 创建标准化API模块
5. **数据字典集成** - 替换硬编码为标准字典
6. **页面文件重构** - 重构主页面文件
7. **国际化更新** - 更新国际化文件结构
8. **路由配置更新** - 更新路由配置

### 4.2 重构检查清单

#### 4.2.1 目录结构验证
- [ ] 页面文件移动到 `src/views/sales/vehicleQuery/VehicleQueryView.vue`
- [ ] API模块创建在 `src/api/modules/sales/vehicleQuery.ts`
- [ ] Mock数据创建在 `src/mock/data/sales/vehicleQuery.ts`
- [ ] 类型定义创建在 `src/types/sales/vehicleQuery.d.ts`
- [ ] 国际化文件更新在 `src/locales/modules/sales/`

#### 4.2.2 MyBatisPlus分页验证
- [x] 分页参数使用 `pageNum` 和 `pageSize` (已符合)
- [x] 响应数据使用 `response.result.records` (已符合)
- [x] 分页组件绑定正确参数 (已符合)

#### 4.2.3 数据字典验证
- [ ] 使用 `useBatchDictionary` 获取字典数据
- [ ] 使用 `getNameByCode` 进行字段转义
- [ ] 使用 `getOptions` 获取下拉选项
- [ ] 移除硬编码字典值和国际化
- [ ] 统一状态标签样式映射

#### 4.2.4 API响应处理验证
- [x] 使用标准 `response.result` 获取数据 (已符合)
- [x] 统一错误处理机制 (已符合)
- [x] 正确的响应码处理 (已符合)

#### 4.2.5 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 字典转义正常显示
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

## 5. 风险评估与注意事项

### 5.1 低风险项
- **MyBatisPlus分页**: 当前实现已符合标准，风险极低
- **API响应处理**: 当前实现已符合标准，风险极低

### 5.2 中等风险项
- **数据字典集成**: 需要替换大量硬编码，需要仔细测试
- **国际化重构**: 需要更新翻译键路径，可能影响显示

### 5.3 注意事项
1. **保持功能不变**: 重构过程中不改变任何业务逻辑
2. **渐进式重构**: 按步骤逐一验证，确保每步正常
3. **充分测试**: 重点测试字典转义和搜索功能
4. **备份原文件**: 重构前备份原始文件

## 6. 详细技术实现

### 6.1 类型定义文件
```typescript
// src/types/sales/vehicleQuery.d.ts
export interface VehicleQueryItem {
  id: string;
  factoryOrderNo: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  fmrId: string;
  warehouseName: string;
  stockStatus: string;
  lockStatus: string;
  invoiceStatus: string;
  deliveryStatus: string;
  invoiceDate: string;
  deliveryDate: string;
  storageDate: string;
  productionDate: string;
}

export interface VehicleQuerySearchParams {
  pageNum?: number;
  pageSize?: number;
  vin?: string;
  factoryOrderNo?: string;
  warehouseName?: string;
  model?: string;
  variant?: string;
  color?: string;
  fmrId?: string;
  stockStatus?: string;
  lockStatus?: string;
  invoiceStatus?: string;
  invoiceDateStart?: string;
  invoiceDateEnd?: string;
  storageDateStart?: string;
  storageDateEnd?: string;
  productionDateStart?: string;
  productionDateEnd?: string;
}

export interface VehicleQueryPageResponse {
  result: {
    records: VehicleQueryItem[];
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
  };
}
```

### 6.2 Mock数据实现
```typescript
// src/mock/data/sales/vehicleQuery.ts
import type { VehicleQuerySearchParams, VehicleQueryPageResponse } from '@/types/sales/vehicleQuery';

// 动态生成模拟数据
function generateMockData() {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData = [];

  const stockStatusOptions = ['01200001001', '01200001002', '01200001003', '01200001004'];
  const lockStatusOptions = ['01200002001', '01200002002'];
  const invoiceStatusOptions = ['01200003001', '01200003002'];
  const deliveryStatusOptions = ['01200004001', '01200004002'];

  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      id: `VQ${String(i + 1).padStart(6, '0')}`,
      factoryOrderNo: `FO${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,
      vin: `VIN${String(Math.floor(Math.random() * 9999999999999999)).padStart(17, '0')}`,
      model: ['Axia', 'Bezza', 'Myvi', 'Alza'][Math.floor(Math.random() * 4)],
      variant: ['1.0 Standard', '1.3 Premium', '1.5 Advance'][Math.floor(Math.random() * 3)],
      color: ['白色', '黑色', '银色', '红色', '蓝色'][Math.floor(Math.random() * 5)],
      fmrId: `FMR${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,
      warehouseName: ['主仓库', '分仓库A', '分仓库B'][Math.floor(Math.random() * 3)],
      stockStatus: stockStatusOptions[Math.floor(Math.random() * stockStatusOptions.length)],
      lockStatus: lockStatusOptions[Math.floor(Math.random() * lockStatusOptions.length)],
      invoiceStatus: invoiceStatusOptions[Math.floor(Math.random() * invoiceStatusOptions.length)],
      deliveryStatus: deliveryStatusOptions[Math.floor(Math.random() * deliveryStatusOptions.length)],
      invoiceDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      deliveryDate: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      storageDate: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      productionDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    });
  }

  return mockData;
}

const mockData = generateMockData();

export const getVehicleQueryList = (params: VehicleQuerySearchParams): Promise<VehicleQueryPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.vin) {
        filteredData = filteredData.filter(item =>
          item.vin.toLowerCase().includes(params.vin!.toLowerCase())
        );
      }

      if (params.factoryOrderNo) {
        filteredData = filteredData.filter(item =>
          item.factoryOrderNo.toLowerCase().includes(params.factoryOrderNo!.toLowerCase())
        );
      }

      if (params.stockStatus) {
        filteredData = filteredData.filter(item =>
          item.stockStatus === params.stockStatus
        );
      }

      // 分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 10;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        }
      });
    }, 500);
  });
};
```

## 7. 页面目录结构规范符合性检查

### 7.1 规范符合性验证
根据 `/规范/页面目录结构规范.md` 进行的符合性检查：

#### 7.1.1 目录结构规范 ✅
- [x] **路由页面位置**: `src/views/sales/vehicleQuery/VehicleQueryView.vue` - 符合 `src/views/模块名/功能名/功能名View.vue` 规范
- [x] **API模块位置**: `src/api/modules/sales/vehicleQuery.ts` - 符合模块化API结构
- [x] **类型定义位置**: `src/types/sales/vehicleQuery.d.ts` - 符合模块化类型结构
- [x] **Mock数据位置**: `src/mock/data/sales/vehicleQuery.ts` - 符合模块化Mock结构
- [x] **国际化位置**: `src/locales/modules/sales/` - 符合模块化国际化结构

#### 7.1.2 命名规范 ✅
- [x] **路由页面命名**: `VehicleQueryView.vue` - 符合 `功能名View.vue` 格式
- [x] **模块名称**: `sales` - 使用标准模块名称
- [x] **功能名称**: `vehicleQuery` - 使用小驼峰命名法

#### 7.1.3 模块一致性 ✅
- [x] **API模块结构**: 与页面结构保持一致
- [x] **Mock数据结构**: 与API模块结构保持一致
- [x] **类型定义结构**: 与功能模块保持一致
- [x] **国际化文件**: 按模块正确组织
- [x] **路由配置**: 按模块正确组织

### 7.2 规范遵循要点
1. **功能内聚**: 车辆查询相关的所有文件统一放在 `sales/vehicleQuery/` 目录下
2. **清晰分类**: 路由页面直接放在功能目录下，组件放在 `components/` 子目录
3. **模块化组织**: API、Mock、类型定义、国际化文件都按相同的模块结构组织
4. **命名一致性**: 所有文件和目录命名都遵循统一的命名规范
5. **标准模块使用**: 使用项目标准的 `sales` 模块，而非自定义模块名

---

**本设计文档基于DMS前端重构技术规范制定，已完全符合页面目录结构规范，为VehicleQueryView.vue的标准化重构提供详细指导。重构完成后将实现完全的模块化组织和规范化实现。**
