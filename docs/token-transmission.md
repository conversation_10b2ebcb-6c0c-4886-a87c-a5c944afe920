# Token传输实现说明

## 功能概述

系统已成功实现了Token在HTTP请求头中的自动传输机制。所有通过统一HTTP客户端发送的API请求都会自动在请求头中添加 `Authorization: Bearer {token}` 参数。

## 核心实现

### 1. HTTP拦截器配置

在 `src/utils/http.ts` 中实现了请求拦截器：

```typescript
// 请求拦截器 - 自动添加Token
service.interceptors.request.use(
  (requestConfig) => {
    // 从localStorage获取token并添加到请求头
    const token = localStorage.getItem(config.tokenKey);
    if (token && requestConfig.headers) {
      requestConfig.headers.Authorization = `Bearer ${token}`;
    }
    return requestConfig;
  }
);
```

### 2. Token存储管理

在 `src/stores/auth.ts` 中管理Token的存储和清除：

```typescript
// 设置Token
const setToken = (newToken: string, newRefreshToken?: string) => {
  token.value = newToken
  localStorage.setItem(config.tokenKey, newToken)
}

// 清除Token  
const clearToken = () => {
  token.value = null
  localStorage.removeItem(config.tokenKey)
  localStorage.removeItem(config.refreshTokenKey)
}
```

### 3. Token过期处理

响应拦截器自动处理Token过期情况：

```typescript
// 处理401错误 - Token过期
if (res.code === 401 || res.code === '401') {
  handleTokenExpired();
  return Promise.reject(new Error('登录已过期，请重新登录'));
}
```

## 使用方法

### API模块中使用

所有API模块都使用统一的HTTP客户端：

```typescript
import { http } from '@/utils/http'

// Token会自动添加到请求头
export const getUserInfo = () => {
  return http.post('/auth/user-info')
}

export const getStoreList = (params) => {
  return http.post('/stores/page', params)
}
```

### 组件中调用

在Vue组件中直接调用API方法：

```typescript
import { getUserInfo } from '@/api/modules/auth'

const fetchUserInfo = async () => {
  try {
    const response = await getUserInfo() // Token自动传输
    console.log('用户信息:', response.result)
  } catch (error) {
    console.error('获取失败:', error)
  }
}
```

## 测试验证

### 1. 测试页面

访问 `/test/token` 可以打开Token传输测试页面，包含：
- 当前认证状态显示
- API请求测试按钮
- 实时请求日志
- Authorization头验证

### 2. 浏览器验证

1. **Network面板**：
   - 打开开发者工具Network面板
   - 执行API请求
   - 检查Request Headers中的Authorization字段

2. **Console日志**：
   - 开发环境会自动打印请求信息
   - 查看控制台输出验证Token传输

## 配置选项

在 `src/config/index.ts` 中配置：

```typescript
export const config = {
  tokenKey: 'token',
  refreshTokenKey: 'refreshToken',
  apiBaseUrl: 'http://localhost:8080/api/v1',
  timeout: 30000
}
```

## 实现特点

✅ **自动化**: 所有API请求自动添加Authorization头
✅ **统一性**: 所有API模块使用统一的HTTP客户端  
✅ **安全性**: Token过期自动清除和重定向
✅ **调试友好**: 开发环境请求日志和详细错误信息

## 注意事项

1. 确保所有API模块都使用 `import { http } from '@/utils/http'`
2. Token存储在localStorage中，页面刷新后会自动恢复
3. Token过期时会自动清除并跳转到登录页
4. 开发环境下可以在控制台查看详细的请求信息

## 故障排除

**问题**: API请求没有携带Token
- **解决**: 确认使用的是项目统一的HTTP客户端

**问题**: Token过期处理不生效  
- **解决**: 检查后端返回的错误码格式，调整拦截器判断逻辑

**问题**: 登录后Token未保存
- **解决**: 检查登录接口调用后是否调用了 `authStore.setToken()` 