# VehicleAllocationManagement.vue 重构设计文档

## 1. 现状分析

### 1.1 目标文件分析
**文件路径**: `/src/views/vehicleAllocation/VehicleAllocationManagement.vue`

**当前结构问题**:
- ❌ 页面直接放在 `src/views/vehicleAllocation/` 目录下，不符合模块化规范
- ❌ 未使用数据字典系统，硬编码了选项和状态映射
- ❌ 分页信息使用 `page/pageSize` 而非标准的 `pageNum/pageSize`
- ❌ API响应处理不规范，直接使用 `response.data` 而非 `response.result`
- ❌ 国际化使用方式不规范，未使用模块化i18n
- ❌ 缺少对应的API模块、Mock数据、类型定义

### 1.2 参考实现分析 (ProspectsView.vue)

**优秀实践**:
- ✅ 使用 `useBatchDictionary` 进行字典管理
- ✅ 规范的分页参数: `pageNum/pageSize/total`
- ✅ 统一的API响应处理: `response.result`
- ✅ 模块化国际化: `useModuleI18n('sales')`
- ✅ 完整的类型定义和Mock数据支持

## 2. 重构技术方案

### 2.1 目录结构重构

**重构前**:
```
src/views/vehicleAllocation/
└── VehicleAllocationManagement.vue
```

**重构后**:
```
src/
├── views/sales/vehicleAllocation/
│   ├── VehicleAllocationView.vue           # 重构后的主页面
│   └── components/                         # 后续模态框组件
├── api/modules/sales/
│   └── vehicleAllocation.ts               # API模块
├── types/sales/
│   └── vehicleAllocation.d.ts             # 类型定义
├── mock/data/sales/
│   └── vehicleAllocation.ts               # Mock数据
└── locales/modules/sales/
    ├── zh.json                            # 包含vehicleAllocation翻译
    └── en.json                            # 包含vehicleAllocation翻译
```

### 2.2 分页组件标准化

**当前实现问题**:
```typescript
// ❌ 当前使用 page/pageSize
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});
```

**规范实现**:
```typescript
// ✅ 标准MyBatis Plus分页参数
const pagination = reactive({
  pageNum: 1,    // 当前页码
  pageSize: 20,  // 每页大小
  total: 0       // 总记录数
});
```

**Element Plus分页组件配置**:
```vue
<el-pagination
  v-model:current-page="pagination.pageNum"
  v-model:page-size="pagination.pageSize"
  :page-sizes="[10, 20, 50, 100]"
  :total="pagination.total"
  layout="total, sizes, prev, pager, next, jumper"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
/>
```

### 2.3 数据字典系统集成

**当前硬编码问题**:
```vue
<!-- ❌ 硬编码选项 -->
<el-option :label="t('vehicleAllocation.allocated')" value="allocated" />
<el-option :label="t('vehicleAllocation.unallocated')" value="unallocated" />

<!-- ❌ 硬编码状态映射 -->
<el-tag :type="row.allocationStatus === 'allocated' ? 'success' : 'info'">
  {{ row.allocationStatus === 'allocated' ? t('vehicleAllocation.allocated') : t('vehicleAllocation.unallocated') }}
</el-tag>
```

**规范字典实现**:
```typescript
// 1. 定义字典类型常量
export const DICTIONARY_TYPES = {
  // 现有字典...
  ALLOCATION_STATUS: 'ALLOCATION_STATUS',     // 配车状态
  ORDER_STATUS: 'ORDER_STATUS',               // 订单状态  
  VEHICLE_MODEL: 'VEHICLE_MODEL',             // 车型
  VEHICLE_COLOR: 'VEHICLE_COLOR',             // 车辆颜色
  STORE: 'STORE',                             // 门店
  SALES_CONSULTANT: 'SALES_CONSULTANT'       // 销售顾问
} as const;

// 2. 使用批量字典
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.ALLOCATION_STATUS,
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.VEHICLE_COLOR,
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.SALES_CONSULTANT
]);

// 3. 状态标签样式映射
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.ALLOCATION_STATUS]: {
    'allocated': 'success',
    'unallocated': 'info'
  },
  [DICTIONARY_TYPES.ORDER_STATUS]: {
    'confirmed': 'success',
    'pending': 'warning',
    'cancelled': 'danger'
  }
} as const;
```

**模板中的使用**:
```vue
<!-- ✅ 使用字典数据 -->
<el-form-item :label="t('vehicleAllocation.allocationStatus')">
  <el-select v-model="searchParams.allocationStatus" clearable>
    <el-option :label="tc('all')" value="" />
    <el-option
      v-for="option in getOptions(DICTIONARY_TYPES.ALLOCATION_STATUS)"
      :key="option.code"
      :label="option.name"
      :value="option.code"
    />
  </el-select>
</el-form-item>

<!-- ✅ 表格中字典展示 -->
<el-table-column :label="t('vehicleAllocation.allocationStatus')" prop="allocationStatus" min-width="100">
  <template #default="{ row }">
    <el-tag :type="getStatusTagType(row.allocationStatus, DICTIONARY_TYPES.ALLOCATION_STATUS)">
      {{ getNameByCode(DICTIONARY_TYPES.ALLOCATION_STATUS, row.allocationStatus) }}
    </el-tag>
  </template>
</el-table-column>
```

### 2.4 API请求通用处理方式

**当前问题**:
```typescript
// ❌ 直接使用response.data
const response = await getVehicleAllocationOrderList(params);
tableData.value = response.data;
pagination.total = response.total;
```

**规范实现**:
```typescript
// 1. API接口定义 (src/api/modules/sales/vehicleAllocation.ts)
interface ApiResponse<T> {
  result: T;
  code: number;
  message: string;
}

export const getVehicleAllocationOrderList = async (
  params: VehicleAllocationOrderParams
): Promise<VehicleAllocationPageResponse> => {
  if (USE_MOCK_API) {
    return getMockVehicleAllocationList(params);
  } else {
    const response = await request.post<any, ApiResponse<VehicleAllocationPageResponse>>(
      '/sales/vehicle-allocation/list', 
      params
    );
    return response.result; // ✅ 返回result字段
  }
};

// 2. 页面中的使用
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getVehicleAllocationOrderList({
      ...searchParams,
      pageNum: pagination.pageNum,  // ✅ 使用pageNum
      pageSize: pagination.pageSize
    });
    
    tableData.value = response.records; // ✅ MyBatis Plus返回格式
    pagination.total = response.total;
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error(tc('loadDataFailed'));
  } finally {
    loading.value = false;
  }
};
```

**MyBatis Plus分页响应格式**:
```typescript
export interface VehicleAllocationPageResponse {
  records: VehicleAllocationOrderItem[];  // 数据列表
  total: number;                          // 总记录数
  size: number;                           // 每页大小
  current: number;                        // 当前页码
  pages: number;                          // 总页数
}
```

### 2.5 国际化模块化改造

**当前问题**:
```typescript
// ❌ 直接使用useI18n，不是模块化
const { t } = useI18n();
```

**规范实现**:
```typescript
// ✅ 使用模块化国际化
const { t, tc } = useModuleI18n('sales');

// 国际化文件结构 (src/locales/modules/sales/zh.json)
{
  "vehicleAllocation": {
    "title": "配车管理",
    "orderNumber": "订单号",
    "customerName": "客户姓名",
    "allocationStatus": "配车状态",
    "allocated": "已配车",
    "unallocated": "未配车",
    "orderStatus": {
      "confirmed": "已确认",
      "pending_delivery": "待交付",
      "delivered": "已交付",
      "cancelled": "已取消"
    }
  }
}
```

## 3. 完整重构实现方案

### 3.1 类型定义 (src/types/sales/vehicleAllocation.d.ts)

```typescript
export interface VehicleAllocationOrderItem {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  store: string;
  salesConsultant: string;
  model: string;
  variant: string;
  color: string;
  factoryOrderNumber?: string;
  vin?: string;
  allocationStatus: string;
  orderStatus: string;
  orderCreateTime: string;
  allocationTime?: string;
}

export interface VehicleAllocationOrderParams {
  orderNumber?: string;
  customerName?: string;
  customerPhone?: string;
  allocationStatus?: string;
  orderStatus?: string;
  vin?: string;
  factoryOrderNumber?: string;
  store?: string;
  salesConsultant?: string;
  allocationTimeStart?: string;
  allocationTimeEnd?: string;
  orderCreateTimeStart?: string;
  orderCreateTimeEnd?: string;
  pageNum: number;
  pageSize: number;
}

export interface VehicleAllocationPageResponse {
  records: VehicleAllocationOrderItem[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

export interface AvailableVehicle {
  id: string;
  vin: string;
  factoryOrderNumber: string;
  model: string;
  variant: string;
  color: string;
  warehouseName: string;
  inStockTime: string;
}

export interface AllocationParams {
  orderNumber: string;
  vin: string;
}

export interface OrderAllocationTimelineItem {
  id: string;
  operationType: string;
  processResult: string;
  operationTime: string;
  operator: string;
  vin?: string;
  warehouseName?: string;
  remarks?: string;
}
```

### 3.2 API模块 (src/api/modules/sales/vehicleAllocation.ts)

```typescript
import request from '@/api';
import type {
  VehicleAllocationOrderParams,
  VehicleAllocationPageResponse,
  AvailableVehicle,
  AllocationParams,
  OrderAllocationTimelineItem
} from '@/types/sales/vehicleAllocation';
import {
  getVehicleAllocationList as getMockList,
  getAvailableVehicles as getMockVehicles,
  confirmAllocation as mockConfirmAllocation,
  cancelAllocation as mockCancelAllocation,
  getOrderTimeline as getMockTimeline
} from '@/mock/data/sales/vehicleAllocation';

const USE_MOCK_API = import.meta.env.VITE_USE_MOCK === 'true';

interface ApiResponse<T> {
  result: T;
  code: number;
  message: string;
}

export const getVehicleAllocationOrderList = async (
  params: VehicleAllocationOrderParams
): Promise<VehicleAllocationPageResponse> => {
  if (USE_MOCK_API) {
    return getMockList(params);
  } else {
    const response = await request.post<any, ApiResponse<VehicleAllocationPageResponse>>(
      '/sales/vehicle-allocation/list', 
      params
    );
    return response.result;
  }
};

export const getAvailableVehicles = async (params: any): Promise<AvailableVehicle[]> => {
  if (USE_MOCK_API) {
    return getMockVehicles(params);
  } else {
    const response = await request.post<any, ApiResponse<AvailableVehicle[]>>(
      '/sales/vehicle-allocation/available-vehicles',
      params
    );
    return response.result;
  }
};

export const confirmAllocation = async (params: AllocationParams): Promise<{success: boolean, message: string}> => {
  if (USE_MOCK_API) {
    return mockConfirmAllocation(params);
  } else {
    const response = await request.post<any, ApiResponse<{success: boolean, message: string}>>(
      '/sales/vehicle-allocation/confirm',
      params
    );
    return response.result;
  }
};

export const cancelAllocation = async (params: {orderNumber: string, reason: string}): Promise<{success: boolean, message: string}> => {
  if (USE_MOCK_API) {
    return mockCancelAllocation(params);
  } else {
    const response = await request.post<any, ApiResponse<{success: boolean, message: string}>>(
      '/sales/vehicle-allocation/cancel',
      params
    );
    return response.result;
  }
};

export const getOrderAllocationTimeline = async (orderNumber: string): Promise<OrderAllocationTimelineItem[]> => {
  if (USE_MOCK_API) {
    return getMockTimeline(orderNumber);
  } else {
    const response = await request.get<any, ApiResponse<OrderAllocationTimelineItem[]>>(
      `/sales/vehicle-allocation/timeline/${orderNumber}`
    );
    return response.result;
  }
};
```

### 3.3 Mock数据 (src/mock/data/sales/vehicleAllocation.ts)

```typescript
import type {
  VehicleAllocationOrderParams,
  VehicleAllocationPageResponse,
  VehicleAllocationOrderItem,
  AvailableVehicle,
  AllocationParams,
  OrderAllocationTimelineItem
} from '@/types/sales/vehicleAllocation';

// 生成Mock数据
function generateMockOrders(): VehicleAllocationOrderItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData: VehicleAllocationOrderItem[] = [];
  
  const models = ['Axia', 'Bezza', 'Alza', 'Myvi', 'Viva'];
  const variants = ['1.0 Standard', '1.3 Premium', '1.5 Advance'];
  const colors = ['White', 'Black', 'Silver', 'Red', 'Blue'];
  const stores = ['Store_001', 'Store_002', 'Store_003'];
  const consultants = ['CONSULTANT_001', 'CONSULTANT_002', 'CONSULTANT_003'];
  const allocationStatuses = ['allocated', 'unallocated'];
  const orderStatuses = ['confirmed', 'pending_delivery', 'delivered'];

  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      id: `VA${String(i + 1).padStart(6, '0')}`,
      orderNumber: `ORD${String(i + 1).padStart(8, '0')}`,
      customerName: `客户${i + 1}`,
      customerPhone: `+60${Math.floor(Math.random() * 90000000) + 10000000}`,
      store: stores[Math.floor(Math.random() * stores.length)],
      salesConsultant: consultants[Math.floor(Math.random() * consultants.length)],
      model: models[Math.floor(Math.random() * models.length)],
      variant: variants[Math.floor(Math.random() * variants.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      factoryOrderNumber: Math.random() > 0.3 ? `FO${String(i + 1).padStart(8, '0')}` : undefined,
      vin: Math.random() > 0.4 ? `MBBVA51EXKP${String(100000 + i).padStart(6, '0')}` : undefined,
      allocationStatus: allocationStatuses[Math.floor(Math.random() * allocationStatuses.length)],
      orderStatus: orderStatuses[Math.floor(Math.random() * orderStatuses.length)],
      orderCreateTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      allocationTime: Math.random() > 0.5 ? new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString() : undefined
    });
  }
  
  return mockData;
}

const mockOrders = generateMockOrders();

export const getVehicleAllocationList = (
  params: VehicleAllocationOrderParams
): Promise<VehicleAllocationPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockOrders];
      
      // 搜索过滤逻辑
      if (params.orderNumber) {
        filteredData = filteredData.filter(item => 
          item.orderNumber.includes(params.orderNumber!)
        );
      }
      if (params.customerName) {
        filteredData = filteredData.filter(item => 
          item.customerName.includes(params.customerName!)
        );
      }
      if (params.allocationStatus) {
        filteredData = filteredData.filter(item => 
          item.allocationStatus === params.allocationStatus
        );
      }
      
      // 分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        records: filteredData.slice(start, end),
        total: filteredData.length,
        size: pageSize,
        current: pageNum,
        pages: Math.ceil(filteredData.length / pageSize)
      });
    }, 500);
  });
};

export const getAvailableVehicles = (params: any): Promise<AvailableVehicle[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const vehicles: AvailableVehicle[] = Array.from({length: 5}, (_, i) => ({
        id: `V${String(i + 1).padStart(6, '0')}`,
        vin: `MBBVA51EXKP${String(200000 + i).padStart(6, '0')}`,
        factoryOrderNumber: `FO${String(i + 1).padStart(8, '0')}`,
        model: params.model || 'Axia',
        variant: params.variant || '1.0 Standard',
        color: params.color || 'White',
        warehouseName: `仓库${i + 1}`,
        inStockTime: new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000).toISOString()
      }));
      resolve(vehicles);
    }, 300);
  });
};

export const confirmAllocation = (params: AllocationParams): Promise<{success: boolean, message: string}> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '配车成功'
      });
    }, 1000);
  });
};

export const cancelAllocation = (params: {orderNumber: string, reason: string}): Promise<{success: boolean, message: string}> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '取消配车成功'
      });
    }, 1000);
  });
};

export const getOrderTimeline = (orderNumber: string): Promise<OrderAllocationTimelineItem[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const timeline: OrderAllocationTimelineItem[] = [
        {
          id: '1',
          operationType: 'create_order',
          processResult: 'success',
          operationTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          operator: '系统',
          remarks: '订单创建成功'
        },
        {
          id: '2',
          operationType: 'allocate_vehicle',
          processResult: 'success',
          operationTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          operator: '管理员',
          vin: 'MBBVA51EXKP200001',
          warehouseName: '仓库1',
          remarks: '配车成功'
        }
      ];
      resolve(timeline);
    }, 300);
  });
};
```

### 3.4 重构后的页面组件 (src/views/sales/vehicleAllocation/VehicleAllocationView.vue)

```vue
<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('vehicleAllocation.title') }}</h1>

    <!-- 搜索筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('vehicleAllocation.orderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.allocationStatus')">
              <el-select v-model="searchParams.allocationStatus" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.ALLOCATION_STATUS)"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleAllocation.orderStatus')">
              <el-select v-model="searchParams.orderStatus" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.ORDER_STATUS)"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <div class="buttons-container">
              <el-space>
                <el-button type="primary" :icon="Search" @click="handleSearch">
                  {{ tc('search') }}
                </el-button>
                <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
              </el-space>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" stripe>
        <el-table-column type="index" :label="tc('serialNumber')" width="60" />
        <el-table-column :label="t('vehicleAllocation.orderNumber')" prop="orderNumber" min-width="120" />
        <el-table-column :label="t('vehicleAllocation.customerName')" prop="customerName" min-width="100" />
        
        <!-- ✅ 使用字典展示 -->
        <el-table-column :label="t('vehicleAllocation.allocationStatus')" prop="allocationStatus" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.allocationStatus, DICTIONARY_TYPES.ALLOCATION_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.ALLOCATION_STATUS, row.allocationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column :label="t('vehicleAllocation.orderStatus')" prop="orderStatus" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.orderStatus, DICTIONARY_TYPES.ORDER_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column :label="tc('operations')" width="200" fixed="right">
          <template #default="{ row }">
            <el-space>
              <el-button v-if="canAllocate(row)" type="primary" link @click="showAllocationModal(row)">
                {{ t('vehicleAllocation.allocate') }}
              </el-button>
              <el-button v-if="canCancelAllocate(row)" type="danger" link @click="showCancelModal(row)">
                {{ t('vehicleAllocation.cancelAllocate') }}
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>

      <!-- ✅ 标准分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getVehicleAllocationOrderList } from '@/api/modules/sales/vehicleAllocation';
import type { 
  VehicleAllocationOrderItem, 
  VehicleAllocationOrderParams 
} from '@/types/sales/vehicleAllocation';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// ✅ 使用模块化国际化
const { t, tc } = useModuleI18n('sales');

// ✅ 使用批量字典
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.ALLOCATION_STATUS,
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.SALES_CONSULTANT
]);

// ✅ 状态标签样式映射
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.ALLOCATION_STATUS]: {
    'allocated': 'success',
    'unallocated': 'info'
  },
  [DICTIONARY_TYPES.ORDER_STATUS]: {
    'confirmed': 'success',
    'pending_delivery': 'warning',
    'delivered': 'success',
    'cancelled': 'danger'
  }
} as const;

const getStatusTagType = (status: string, dictionaryType: string) => {
  return STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info';
};

// 响应式数据
const loading = ref(false);
const tableData = ref<VehicleAllocationOrderItem[]>([]);

// ✅ 标准分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
});

// 搜索参数
const searchParams = reactive<Partial<VehicleAllocationOrderParams>>({
  orderNumber: '',
  customerName: '',
  allocationStatus: '',
  orderStatus: ''
});

// ✅ 标准数据加载方法
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getVehicleAllocationOrderList({
      ...searchParams,
      pageNum: pagination.pageNum,  // ✅ 使用pageNum
      pageSize: pagination.pageSize
    });
    
    tableData.value = response.records; // ✅ MyBatis Plus返回格式
    pagination.total = response.total;
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error(tc('loadDataFailed'));
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.pageNum = 1;
  loadData();
};

const resetSearch = () => {
  Object.assign(searchParams, {
    orderNumber: '',
    customerName: '',
    allocationStatus: '',
    orderStatus: ''
  });
  handleSearch();
};

const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;
  loadData();
};

const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;
  loadData();
};

// 业务逻辑方法
const canAllocate = (row: VehicleAllocationOrderItem) => {
  return row.allocationStatus === 'unallocated' && row.orderStatus === 'confirmed';
};

const canCancelAllocate = (row: VehicleAllocationOrderItem) => {
  return row.allocationStatus === 'allocated' && !['delivered', 'cancelled'].includes(row.orderStatus);
};

const showAllocationModal = (row: VehicleAllocationOrderItem) => {
  // TODO: 实现配车模态框
  console.log('显示配车模态框', row);
};

const showCancelModal = (row: VehicleAllocationOrderItem) => {
  // TODO: 实现取消配车模态框
  console.log('显示取消配车模态框', row);
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 15px;
}

.buttons-container {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  height: 100%;
  padding-top: 30px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
```

## 4. 路由配置更新

```typescript
// src/router/modules/sales.ts
{
  path: '/sales/vehicle-allocation',
  name: 'VehicleAllocation',
  component: () => import('@/views/sales/vehicleAllocation/VehicleAllocationView.vue'),
  meta: {
    title: 'menu.vehicleAllocation',
    requiresAuth: true,
    icon: 'Car'
  }
}
```

## 5. 重构检查清单

### 5.1 目录结构验证
- [x] 页面移动到 `src/views/sales/vehicleAllocation/VehicleAllocationView.vue`
- [x] API模块创建在 `src/api/modules/sales/vehicleAllocation.ts`
- [x] Mock数据创建在 `src/mock/data/sales/vehicleAllocation.ts`
- [x] 类型定义创建在 `src/types/sales/vehicleAllocation.d.ts`
- [x] 国际化文件更新在 `src/locales/modules/sales/`

### 5.2 分页标准化验证
- [x] 分页参数使用 `pageNum/pageSize/total`
- [x] API请求传递正确的分页参数
- [x] 响应数据使用MyBatis Plus格式 `records/total`
- [x] Element Plus分页组件配置正确

### 5.3 数据字典验证
- [x] 使用 `useBatchDictionary` 获取字典数据
- [x] 所有硬编码选项替换为字典数据
- [x] 表格字段使用 `getNameByCode` 转义
- [x] 状态标签使用统一的样式映射

### 5.4 API处理验证
- [x] API接口返回 `response.result`
- [x] 统一的响应码处理
- [x] Mock数据支持完整功能
- [x] 错误处理规范

### 5.5 国际化验证
- [x] 使用 `useModuleI18n('sales')`
- [x] 翻译键按模块组织
- [x] 通用翻译使用 `tc()` 函数

**本设计文档提供了完整的重构方案，确保VehicleAllocationManagement.vue符合项目技术规范和最佳实践。**