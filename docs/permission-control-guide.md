# 权限控制系统实现指南

## 概述

本系统基于用户信息返回值中的权限和菜单数据，实现了完整的权限控制功能。根据 `menu.md` 文件中的用户信息结构，系统支持基于权限标识和菜单编码的细粒度权限控制。

## 用户信息结构

根据 `menu.md` 文件，用户信息包含以下关键字段：

```json
{
  "permissions": [
    "system:role:delete",
    "system:user:list", 
    "system:role:create",
    "system:main",
    "system:user:create",
    "system:user:delete",
    "factory:main",
    "system:user:update",
    "system:role:update"
  ],
  "menus": [
    {
      "menuId": 1,
      "menuCode": "总部管理",
      "menuName": "总部管理",
      "permission": "factory:main"
    },
    {
      "menuId": 2,
      "menuCode": "FACTORY_SYSTEM",
      "menuName": "系统管理",
      "permission": "system:main"
    }
  ]
}
```

## 权限控制实现

### 1. 权限指令系统

系统实现了三个自定义指令：

#### v-permission 指令
- **用途**: 控制页面元素的显示/隐藏
- **用法**: 
  ```vue
  <!-- 单个权限 -->
  <el-button v-permission="'system:user:create'">新增用户</el-button>
  
  <!-- 多个权限（满足其一即可） -->
  <el-button v-permission="['system:user:create', 'system:user:update']">操作</el-button>
  ```

#### v-role 指令
- **用途**: 基于角色的权限控制
- **用法**:
  ```vue
  <el-button v-role="'admin'">管理员功能</el-button>
  <el-button v-role="['admin', 'manager']">管理功能</el-button>
  ```

#### v-menu 指令
- **用途**: 基于菜单的权限控制
- **用法**:
  ```vue
  <el-button v-menu="'FACTORY_SYSTEM'">系统管理</el-button>
  ```

### 2. 认证状态管理

在 `src/stores/auth.ts` 中实现了权限检查方法：

```typescript
// 检查权限
const hasPermission = (permission: string) => {
  if (!userInfo.value) return false
  if (userPermissions.value.includes('*:*:*')) return true
  return userPermissions.value.includes(permission)
}

// 检查角色
const hasRole = (role: string) => {
  if (!userInfo.value) return false
  return userRoles.value.includes(role)
}

// 检查菜单权限
const hasMenu = (menuCode: string) => {
  if (!userInfo.value || !userInfo.value.menus) return false
  return userInfo.value.menus.some(menu => menu.menuCode === menuCode)
}
```

## 系统管理模块权限控制

### 门店管理权限
- `system:store:create` - 新增门店
- `system:store:update` - 编辑门店  
- `system:store:delete` - 删除门店

### 部门管理权限
- `system:department:create` - 新增部门
- `system:department:update` - 编辑部门
- `system:department:delete` - 删除部门

### 菜单管理权限
- `system:menu:create` - 新增菜单
- `system:menu:update` - 编辑菜单
- `system:menu:delete` - 删除菜单

### 角色管理权限
- `system:role:create` - 新增角色
- `system:role:update` - 编辑角色
- `system:role:delete` - 删除角色

### 用户管理权限
- `system:user:create` - 新增用户
- `system:user:update` - 编辑用户
- `system:user:delete` - 删除用户

## 实际应用示例

### 门店管理页面
```vue
<template>
  <!-- 新增按钮 - 需要 system:store:create 权限 -->
  <el-button v-permission="'system:store:create'" type="primary">
    新增门店
  </el-button>
  
  <!-- 操作列 -->
  <el-table-column label="操作">
    <template #default="{ row }">
      <!-- 编辑按钮 - 需要 system:store:update 权限 -->
      <el-button v-permission="'system:store:update'" type="primary">
        编辑
      </el-button>
      
      <!-- 删除按钮 - 需要 system:store:delete 权限 -->
      <el-button v-permission="'system:store:delete'" type="danger">
        删除
      </el-button>
    </template>
  </el-table-column>
</template>
```

### 角色管理页面
```vue
<template>
  <!-- 新增角色按钮 -->
  <el-button v-permission="'system:role:create'" type="primary">
    新增角色
  </el-button>
  
  <!-- 操作列 -->
  <el-table-column label="操作">
    <template #default="{ row }">
      <!-- 编辑角色 -->
      <el-button v-permission="'system:role:update'" type="primary">
        编辑
      </el-button>
      
      <!-- 配置权限 -->
      <el-button v-permission="'system:role:update'" type="primary">
        配置权限
      </el-button>
      
      <!-- 删除角色 -->
      <el-button v-permission="'system:role:delete'" type="danger">
        删除
      </el-button>
    </template>
  </el-table-column>
</template>
```

## 权限测试

系统提供了权限测试页面 `/permission-test`，用于验证权限控制功能：

1. **用户信息展示**: 显示当前用户的用户名、角色、权限数量等信息
2. **权限按钮测试**: 展示各种权限控制按钮，无权限的按钮会自动隐藏
3. **权限列表**: 显示所有系统权限及其当前用户的拥有状态
4. **菜单权限测试**: 显示菜单权限的检查结果

## 权限控制最佳实践

### 1. 权限粒度控制
- 使用细粒度的权限标识，如 `system:user:create`
- 避免使用过于宽泛的权限，如 `system:user:*`

### 2. 权限组合使用
- 对于复杂操作，可以使用多个权限的组合
- 使用数组形式：`v-permission="['perm1', 'perm2']"`

### 3. 权限与业务逻辑结合
- 权限控制应与业务逻辑保持一致
- 在关键操作前进行权限验证

### 4. 权限缓存优化
- 用户权限信息在登录后缓存到本地存储
- 页面刷新时自动恢复权限状态

## 故障排除

### 1. 权限指令不生效
- 检查权限指令是否正确注册
- 确认用户信息是否已加载
- 验证权限标识是否正确

### 2. 权限检查失败
- 检查用户信息中的权限数组
- 确认权限标识格式是否正确
- 查看控制台错误信息

### 3. 菜单权限问题
- 确认菜单编码是否正确
- 检查用户信息中的菜单数组
- 验证菜单权限检查逻辑

## 扩展说明

### 添加新权限
1. 在用户信息中添加新的权限标识
2. 在相关页面使用 `v-permission` 指令
3. 在权限测试页面添加测试用例

### 添加新菜单权限
1. 在用户信息中添加新的菜单项
2. 使用 `v-menu` 指令进行菜单权限控制
3. 更新菜单权限测试

### 自定义权限逻辑
可以在 `authStore` 中添加自定义的权限检查方法：

```typescript
// 检查多个权限（需要全部满足）
const hasAllPermissions = (permissions: string[]) => {
  return permissions.every(permission => hasPermission(permission))
}

// 检查数据权限
const hasDataPermission = (dataType: string, action: string) => {
  return hasPermission(`${dataType}:${action}`)
}
```

## 总结

本权限控制系统基于用户信息返回值实现了完整的权限管理功能，支持：

1. **细粒度权限控制**: 基于权限标识的精确控制
2. **菜单权限控制**: 基于菜单编码的访问控制  
3. **角色权限控制**: 基于用户角色的权限管理
4. **指令化权限**: 使用自定义指令简化权限控制
5. **权限测试**: 提供完整的权限测试功能

系统具有良好的扩展性和维护性，可以根据业务需求灵活调整权限控制策略。 