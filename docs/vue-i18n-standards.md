 # Vue.js 项目国际化标准规范文档

## 概述

本文档定义了Vue.js项目中使用模块化国际化的标准流程和规则，确保所有页面和组件的国际化处理保持一致性。此文档专为Cursor AI和开发人员提供明确的国际化处理指导。

## 1. 基础架构

### 1.1 国际化Hook使用规范

**✅ 正确方式：**
```typescript
import { useModuleI18n } from '@/composables/useModuleI18n'

// 模块翻译 - 用于业务相关翻译
const { t } = useModuleI18n('payment')  
// 通用翻译 - 用于通用UI元素
const { t: tc } = useModuleI18n('common')
```

**❌ 错误方式：**
```typescript
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
```

### 1.2 翻译文件目录结构
```
src/locales/modules/
├── common/           # 通用翻译
│   ├── zh.json      # 中文
│   └── en.json      # 英文
├── payment/         # 支付模块
│   ├── zh.json
│   └── en.json
├── delivery/        # 交付模块
│   ├── zh.json
│   └── en.json
└── invoice/         # 发票模块
    ├── zh.json
    └── en.json
```

## 2. 翻译键命名规范

### 2.1 基础字段命名
```json
{
  "title": "页面标题",
  "orderNumber": "订单编号",
  "buyerName": "购车人名称",
  "buyerPhone": "购车人手机号",
  "createTime": "创建时间",
  "updateTime": "更新时间",
  "vin": "VIN码",
  "model": "车型",
  "variant": "配置",
  "color": "颜色"
}
```

### 2.2 表单交互命名
```json
{
  "enterOrderNumber": "请输入订单编号",
  "enterBuyerName": "请输入购车人姓名",
  "selectOrderStatus": "请选择订单状态",
  "selectPaymentStatus": "请选择支付状态",
  "selectDateRange": "选择日期范围",
  "selectCanInvoice": "选择是否可开票"
}
```

### 2.3 状态值命名
```json
{
  "orderStatusSubmitted": "已提交",
  "orderStatusConfirmed": "已确认",
  "orderStatusCancelled": "已取消",
  "orderStatusDelivered": "已交车",
  "paymentStatusPending": "待支付",
  "paymentStatusPaid": "已支付",
  "paymentStatusRefunding": "退款中",
  "paymentStatusRefunded": "已退款"
}
```

### 2.4 操作按钮命名
```json
{
  "paymentOperation": "收退款操作",
  "orderDetail": "订单详情",
  "addPaymentRecord": "添加收退款记录",
  "viewDetail": "查看详情",
  "exportData": "导出数据"
}
```

### 2.5 验证消息命名
```json
{
  "orderNumberRequired": "请输入订单编号",
  "amountRequired": "请输入金额",
  "amountMustBePositive": "金额必须大于0",
  "paymentMethodRequired": "请选择支付方式",
  "remarkTooLong": "备注不能超过200个字符",
  "refundAmountExceedsPaid": "退款金额不能超过已支付金额"
}
```

### 2.6 操作结果消息命名
```json
{
  "addSuccess": "添加成功",
  "addFailed": "添加失败",
  "deleteSuccess": "删除成功",
  "deleteFailed": "删除失败",
  "confirmDeleteRecord": "确认删除此记录？",
  "cannotDeleteAppRecord": "APP推送记录不能删除"
}
```

## 3. 代码实现标准

### 3.1 Script Setup部分模板

```vue
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'

// 国际化设置
const { t } = useModuleI18n('moduleName')  // 替换为实际模块名
const { t: tc } = useModuleI18n('common')

// 数据加载错误处理
const loadData = async () => {
  try {
    // API调用
  } catch (error: unknown) {
    console.error('加载数据失败:', error)
    ElMessage.error(tc('loadFailed'))  // 使用通用翻译
  }
}

// 状态文本映射
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': t('orderStatusSubmitted'),
    '已确认': t('orderStatusConfirmed'),
    '已取消': t('orderStatusCancelled')
  }
  return statusMap[status] || status
}

// 表单验证规则
const formRules = {
  orderNumber: [
    { required: true, message: t('orderNumberRequired'), trigger: 'blur' }
  ],
  amount: [
    { required: true, message: t('amountRequired'), trigger: 'blur' },
    { type: 'number', min: 0.01, message: t('amountMustBePositive'), trigger: 'blur' }
  ]
}

// 删除确认对话框
const handleDelete = async (record: any) => {
  try {
    await ElMessageBox.confirm(
      t('confirmDeleteRecord'),
      tc('confirm'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    )
    // 执行删除
    ElMessage.success(t('deleteSuccess'))
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(t('deleteFailed'))
    }
  }
}
</script>
```

### 3.2 Template部分模板

```vue
<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('title') }}</h1>
    
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchParams">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input 
                v-model="searchParams.orderNumber" 
                :placeholder="t('enterOrderNumber')" 
                clearable 
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderStatus')">
              <el-select 
                v-model="searchParams.orderStatus" 
                :placeholder="t('selectOrderStatus')" 
                clearable
              >
                <el-option :label="t('orderStatusSubmitted')" value="已提交" />
                <el-option :label="t('orderStatusConfirmed')" value="已确认" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('createTime')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading">
        <el-table-column type="index" :label="tc('index')" width="80" />
        <el-table-column prop="orderNumber" :label="t('orderNumber')" />
        <el-table-column prop="buyerName" :label="t('buyerName')" />
        <el-table-column prop="orderStatus" :label="t('orderStatus')">
          <template #default="scope">
            <el-tag>{{ getOrderStatusText(scope.row.orderStatus) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="tc('operations')" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleView(scope.row)">
              {{ t('orderDetail') }}
            </el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">
              {{ tc('delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
```

## 4. 通用翻译键标准 (common模块)

```json
{
  "search": "搜索",
  "reset": "重置",
  "confirm": "确认",
  "cancel": "取消",
  "save": "保存",
  "edit": "编辑",
  "delete": "删除",
  "add": "添加",
  "view": "查看",
  "close": "关闭",
  "submit": "提交",
  "back": "返回",
  "next": "下一步",
  "previous": "上一步",
  "yes": "是",
  "no": "否",
  "index": "序号",
  "operations": "操作",
  "remark": "备注",
  "description": "描述",
  "startDate": "开始日期",
  "endDate": "结束日期",
  "createTime": "创建时间",
  "updateTime": "更新时间",
  "loadFailed": "加载失败",
  "saveFailed": "保存失败",
  "saveSuccess": "保存成功",
  "copied": "已复制",
  "copyFailed": "复制失败",
  "noData": "暂无数据",
  "loading": "加载中...",
  "pleaseSelect": "请选择",
  "pleaseInput": "请输入"
}
```

## 5. 翻译文件管理规则

### 5.1 核心原则
1. **🚫 严禁删除现有键** - 其他文件可能正在使用
2. **✅ 只能新增或修改值** - 保持向后兼容性
3. **✅ 中英文必须同步** - 确保键完全一致
4. **✅ 键名使用驼峰命名** - 保持一致性

### 5.2 新增翻译键流程
1. 在中文翻译文件中添加新键
2. 在对应的英文翻译文件中添加相同键
3. 确保键名符合命名规范
4. 验证中英文键的一致性

### 5.3 翻译键分类结构示例
```json
{
  "title": "整车收款管理",
  "orderNumber": "订单编号",
  "buyerName": "购车人名称",
  "enterOrderNumber": "请输入订单编号",
  "selectOrderStatus": "请选择订单状态",
  "orderStatusSubmitted": "已提交",
  "orderStatusConfirmed": "已确认",
  "paymentOperation": "收退款操作",
  "addPaymentRecord": "添加收退款记录",
  "orderNumberRequired": "请输入订单编号",
  "amountMustBePositive": "金额必须大于0",
  "addSuccess": "添加成功",
  "deleteFailed": "删除失败",
  "confirmDeleteRecord": "确认删除此记录？"
}
```

## 6. Cursor AI 处理指令

### 6.1 标准国际化指令
```
请对以下文件进行国际化处理，使用useModuleI18n替代useI18n：

文件路径：[具体文件路径]
模块名称：[模块名，如payment/delivery/invoice等]

处理要求：
1. 将 import { useI18n } from 'vue-i18n' 替换为 import { useModuleI18n } from '@/composables/useModuleI18n'
2. 将 const { t } = useI18n() 替换为：
   const { t } = useModuleI18n('[模块名]')
   const { t: tc } = useModuleI18n('common')
3. 更新所有模板中的翻译调用：
   - t('模块名.xxx') → t('xxx')
   - t('common.xxx') → tc('xxx')
4. 更新脚本中的翻译调用，移除模块前缀
5. 更新表单验证规则中的翻译调用
6. 更新错误处理中的翻译调用，使用tc()处理通用消息
7. 不允许删除en.json、zh.json中的现有key
8. 如需新增翻译键，请同时更新中英文文件
9. 确保所有硬编码的中文文本都替换为翻译调用

翻译键命名规范：
- 基础字段：orderNumber, buyerName, createTime
- 表单提示：enterXxx, selectXxx
- 状态值：xxxStatusPending, xxxStatusConfirmed  
- 操作：xxxOperation, addXxx, deleteXxx
- 验证：xxxRequired, xxxMustBePositive
- 消息：xxxSuccess, xxxFailed, confirmXxx
```

### 6.2 翻译文件更新指令
```
请更新翻译文件，添加以下缺失的翻译键：

模块：[模块名]
需要添加的翻译键：[列出具体的键]

要求：
1. 同时更新 src/locales/modules/[模块名]/zh.json 和 en.json
2. 确保中英文键名完全一致
3. 不删除现有的任何键
4. 翻译内容要准确、简洁
5. 遵循项目的翻译键命名规范
```

## 7. 质量检查清单

### 7.1 代码审查要点
- [ ] 已使用 `useModuleI18n` 替代 `useI18n`
- [ ] 模板中无硬编码的中文或英文文本
- [ ] 翻译函数调用正确：`t()` 用于模块翻译，`tc()` 用于通用翻译
- [ ] 表单验证消息已国际化
- [ ] 错误处理消息已国际化
- [ ] 状态文本映射使用翻译函数
- [ ] 确认对话框使用翻译文本

### 7.2 翻译文件检查
- [ ] 中英文翻译文件键名完全一致
- [ ] 新增键遵循命名规范
- [ ] 未删除现有翻译键
- [ ] 翻译内容准确无误
- [ ] JSON格式正确

### 7.3 功能测试
- [ ] 中英文切换功能正常
- [ ] 所有界面文本正确显示
- [ ] 表单验证消息正确显示
- [ ] 错误提示消息正确显示

## 8. 常见问题解决方案

### 8.1 翻译键冲突
**问题**: 不同模块使用相同的翻译键  
**解决**: 使用模块特定的前缀或更具体的命名

### 8.2 动态翻译
**问题**: 需要根据条件动态选择翻译  
**解决**: 使用状态映射对象或计算属性

### 8.3 嵌套翻译
**问题**: 复杂对象的翻译处理  
**解决**: 使用扁平化的键名结构

## 9. 示例：完整的页面国际化

### 9.1 翻译文件 (payment/zh.json)
```json
{
  "title": "整车收款管理",
  "orderNumber": "订单编号",
  "buyerName": "购车人名称",
  "enterOrderNumber": "请输入订单编号",
  "selectOrderStatus": "请选择订单状态",
  "orderStatusSubmitted": "已提交",
  "orderStatusConfirmed": "已确认",
  "paymentOperation": "收退款操作",
  "orderDetail": "订单详情",
  "orderNumberRequired": "请输入订单编号",
  "addSuccess": "添加成功",
  "deleteFailed": "删除失败"
}
```

### 9.2 翻译文件 (payment/en.json)
```json
{
  "title": "Vehicle Payment Management",
  "orderNumber": "Order Number",
  "buyerName": "Buyer Name",
  "enterOrderNumber": "Enter order number",
  "selectOrderStatus": "Select order status",
  "orderStatusSubmitted": "Submitted",
  "orderStatusConfirmed": "Confirmed",
  "paymentOperation": "Payment Operation",
  "orderDetail": "Order Detail",
  "orderNumberRequired": "Please enter order number",
  "addSuccess": "Add successful",
  "deleteFailed": "Delete failed"
}
```

---

**重要提醒**: 此文档是项目国际化的唯一标准，所有开发人员和AI助手必须严格遵循这些规范。任何偏离都可能导致系统不一致和维护困难。