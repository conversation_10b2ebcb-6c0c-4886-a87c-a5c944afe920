# API接口开发规范

> **📍 工作流程对应**：本文档主要对应标准工作流程的第2-4步
> - **第2步：创建类型定义** - 在`src/types/`下定义接口
> - **第3步：实现Mock数据** - 在`src/mock/data/`下创建测试数据  
> - **第4步：开发API接口** - 在`src/api/modules/`下实现接口

---

## 🔌 API开发核心规范

### 1. 文件组织和结构

#### 1.1 目录结构
```
src/
├── api/
│   ├── index.ts              # Axios配置和拦截器
│   └── modules/              # API模块目录
│       ├── sales/            # 销售模块API
│       ├── aftersales/       # 售后模块API
│       ├── parts/            # 零件模块API
│       └── base/             # 基础模块API
├── types/                    # TypeScript类型定义
│   ├── common/              # 通用类型
│   └── [module]/            # 各模块类型
└── mock/
    └── data/                # Mock数据
        └── [module]/        # 各模块Mock数据
```

#### 1.2 标准API文件模板
```typescript
// src/api/modules/[module]/[feature].ts

// 1. 导入核心依赖
import request from '@/api';

// 2. 导入类型定义（必须从 src/types/ 目录导入）
import type {
  // 查询参数类型
  SearchParams,
  // 响应数据类型
  PageResponse,
  DetailResponse,
  // 请求数据类型
  CreateRequest,
  UpdateRequest
} from '@/types/[module]/[feature]';

// 通用API类型
import type { ApiResult } from '@/types/common/api';

// 3. 导入Mock相关
import {
  getMockDataList,
  getMockDataDetail,
  createMockData,
  updateMockData,
  deleteMockData
} from '@/mock/data/[module]/[feature]';
import { USE_MOCK_API } from '@/utils/mock-config';

// 4. API函数定义
/**
 * 获取数据列表
 * @param params 查询参数
 * @returns 分页数据
 */
export const getDataList = async (params: SearchParams): Promise<PageResponse> => {
  if (USE_MOCK_API) {
    return getMockDataList(params);
  }
  const response = await request.get<any, ApiResult<PageResponse>>('/api/data', { params });
  return response.result;
};

// ... 其他API函数
```

### 2. 类型定义规范

**⚠️ 强制要求：** 所有API相关的类型定义必须统一放在 `src/types/` 目录下，**禁止在API文件同目录定义类型**。

#### 2.1 类型文件位置规范
```typescript
// ✅ 正确位置：按模块组织在src/types/目录下
src/types/
├── common/           # 通用类型
│   ├── api.ts       # API响应通用类型
│   └── pagination.ts # 分页相关类型
└── sales/           # 销售模块类型
    ├── order.ts     # 订单相关类型
    └── customer.ts  # 客户相关类型

// ❌ 禁止位置：API文件同目录
src/api/modules/sales/
├── order.ts         # ✅ API实现
└── types.ts         # ❌ 禁止在此处定义类型
```

#### 2.2 标准类型定义
```typescript
// ✅ 正确：标准命名模式
export interface OrderSearchParams {
  keyword?: string;
  status?: string;
  customerName?: string;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

export interface OrderListItem {
  id: string;
  orderNo: string;
  customerName: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  amount: number;
  createTime: string;
}

export interface OrderPageResponse {
  list: OrderListItem[];
  total: number;
}

export interface OrderDetail extends OrderListItem {
  customerPhone: string;
  customerEmail: string;
  vehicleInfo: VehicleInfo;
  orderItems: OrderItem[];
}

export interface CreateOrderRequest {
  customerName: string;
  customerPhone: string;
  vehicleId: string;
  orderItems: CreateOrderItem[];
}

export interface UpdateOrderRequest extends Partial<CreateOrderRequest> {
  id: string;
}
```

#### 2.3 通用类型定义
```typescript
// src/types/common/api.d.ts

// 统一的API响应结构
export interface ApiResult<T = any> {
  success: boolean;
  message: string;
  code: string;
  traceId: string;
  result: T;
  timestamp: number;
}

// 分页响应通用结构
export interface PaginationResponse<T = any> {
  list: T[];
  total: number;
  page?: number;
  pageSize?: number;
}

// 分页查询通用参数
export interface PaginationParams {
  page: number;
  pageSize: number;
}

// 操作结果通用类型
export interface OperationResult {
  success: boolean;
  message: string;
}

// 批量操作结果
export interface BatchOperationResult {
  successCount: number;
  failedCount: number;
  message: string;
  details?: Array<{
    id: string;
    success: boolean;
    message?: string;
  }>;
}
```

### 3. API函数开发模式

#### 3.1 查询列表API
```typescript
/**
 * 获取订单列表
 * @param params 查询参数
 * @returns 订单列表分页数据
 */
export const getOrdersList = async (params: OrderSearchParams): Promise<OrderPageResponse> => {
  if (USE_MOCK_API) {
    return getMockOrdersList(params);
  }
  
  // 处理查询参数
  const queryParams = {
    ...params,
    // 日期范围处理
    startDate: params.dateRange?.[0],
    endDate: params.dateRange?.[1],
    // 移除前端特有的字段
    dateRange: undefined
  };
  
  const response = await request.get<any, ApiResult<OrderPageResponse>>(
    '/sales/orders', 
    { params: queryParams }
  );
  return response.result;
};
```

#### 3.2 查询详情API
```typescript
/**
 * 获取订单详情
 * @param orderId 订单ID
 * @returns 订单详情数据
 */
export const getOrderDetail = async (orderId: string): Promise<OrderDetail> => {
  if (USE_MOCK_API) {
    return getMockOrderDetail(orderId);
  }
  
  if (!orderId) {
    throw new Error('Order ID is required');
  }
  
  const response = await request.get<any, ApiResult<OrderDetail>>(`/sales/orders/${orderId}`);
  return response.result;
};
```

#### 3.3 创建数据API
```typescript
/**
 * 创建新订单
 * @param data 订单数据
 * @returns 创建结果
 */
export const createOrder = async (data: CreateOrderRequest): Promise<{id: string}> => {
  if (USE_MOCK_API) {
    return createMockOrder(data);
  }
  
  // 数据验证
  if (!data.customerName || !data.customerPhone) {
    throw new Error('Customer name and phone are required');
  }
  
  const response = await request.post<any, ApiResult<{id: string}>>(
    '/sales/orders', 
    data
  );
  return response.result;
};
```

#### 3.4 更新数据API
```typescript
/**
 * 更新订单信息
 * @param orderId 订单ID
 * @param data 更新数据
 * @returns 更新结果
 */
export const updateOrder = async (orderId: string, data: UpdateOrderRequest): Promise<OperationResult> => {
  if (USE_MOCK_API) {
    return updateMockOrder(orderId, data);
  }
  
  const response = await request.put<any, ApiResult<OperationResult>>(
    `/sales/orders/${orderId}`, 
    data
  );
  return response.result;
};
```

#### 3.5 删除数据API
```typescript
/**
 * 删除订单
 * @param orderId 订单ID
 * @returns 删除结果
 */
export const deleteOrder = async (orderId: string): Promise<OperationResult> => {
  if (USE_MOCK_API) {
    return deleteMockOrder(orderId);
  }
  
  const response = await request.delete<any, ApiResult<OperationResult>>(
    `/sales/orders/${orderId}`
  );
  return response.result;
};
```

#### 3.6 批量操作API
```typescript
/**
 * 批量删除订单
 * @param orderIds 订单ID数组
 * @returns 批量操作结果
 */
export const batchDeleteOrders = async (orderIds: string[]): Promise<BatchOperationResult> => {
  if (USE_MOCK_API) {
    return batchDeleteMockOrders(orderIds);
  }
  
  if (!orderIds || orderIds.length === 0) {
    throw new Error('Order IDs are required');
  }
  
  const response = await request.post<any, ApiResult<BatchOperationResult>>(
    '/sales/orders/batch-delete',
    { orderIds }
  );
  return response.result;
};
```

#### 3.7 文件导出API
```typescript
/**
 * 导出订单数据
 * @param params 导出参数
 * @returns Blob数据
 */
export const exportOrders = async (params: OrderExportParams): Promise<Blob> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 生成Mock CSV数据
        const csvData = [
          'Order No,Customer,Amount,Status,Create Time',
          'ORD001,张三,10000,已完成,2024-01-01',
          'ORD002,李四,20000,处理中,2024-01-02'
        ].join('\n');
        
        const blob = new Blob([csvData], { 
          type: 'text/csv;charset=utf-8;' 
        });
        resolve(blob);
      }, 1000);
    });
  }
  
  // 注意：导出API需要特殊的responseType配置
  const response = await request.post('/sales/orders/export', params, {
    responseType: 'blob'
  });
  
  // 对于blob响应，直接返回response
  return response as unknown as Blob;
};
```

#### 3.8 文件上传API
```typescript
/**
 * 上传订单附件
 * @param orderId 订单ID
 * @param file 文件对象
 * @returns 上传结果
 */
export const uploadOrderAttachment = async (
  orderId: string, 
  file: File
): Promise<{url: string; filename: string}> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          url: `https://mock.cdn.com/${file.name}`,
          filename: file.name
        });
      }, 2000);
    });
  }
  
  const formData = new FormData();
  formData.append('file', file);
  formData.append('orderId', orderId);
  
  const response = await request.post<any, ApiResult<{url: string; filename: string}>>(
    '/sales/orders/upload',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  );
  
  return response.result;
};
```

### 4. Mock数据开发规范

#### 4.1 Mock文件结构
```typescript
// src/mock/data/sales/orders.ts

import type {
  OrderSearchParams,
  OrderPageResponse,
  OrderDetail,
  CreateOrderRequest,
  OperationResult
} from '@/types/sales/orders';

// Mock数据生成工具
const generateOrderId = () => `ORD${Date.now()}${Math.floor(Math.random() * 1000)}`;

const generateMockOrder = (index: number) => ({
  id: generateOrderId(),
  orderNo: `ORDER-${String(index).padStart(4, '0')}`,
  customerName: `客户${index}`,
  status: ['pending', 'confirmed', 'completed', 'cancelled'][index % 4] as any,
  amount: Math.floor(Math.random() * 100000) + 10000,
  createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
});

// Mock数据存储
let mockOrdersData: OrderListItem[] = Array.from({ length: 50 }, (_, index) => 
  generateMockOrder(index + 1)
);

/**
 * Mock: 获取订单列表
 */
export const getMockOrdersList = async (params: OrderSearchParams): Promise<OrderPageResponse> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  let filteredData = [...mockOrdersData];
  
  // 应用筛选条件
  if (params.keyword) {
    filteredData = filteredData.filter(item => 
      item.orderNo.includes(params.keyword!) ||
      item.customerName.includes(params.keyword!)
    );
  }
  
  if (params.status) {
    filteredData = filteredData.filter(item => item.status === params.status);
  }
  
  // 分页处理
  const start = (params.page - 1) * params.pageSize;
  const end = start + params.pageSize;
  const list = filteredData.slice(start, end);
  
  return {
    list,
    total: filteredData.length
  };
};

/**
 * Mock: 获取订单详情
 */
export const getMockOrderDetail = async (orderId: string): Promise<OrderDetail> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const order = mockOrdersData.find(item => item.id === orderId);
  if (!order) {
    throw new Error('Order not found');
  }
  
  return {
    ...order,
    customerPhone: '13800138000',
    customerEmail: '<EMAIL>',
    vehicleInfo: {
      id: 'VEH001',
      model: 'Model X',
      brand: 'Tesla',
      color: '白色'
    },
    orderItems: [
      {
        id: 'ITEM001',
        productName: '产品1',
        quantity: 2,
        price: 5000
      }
    ]
  };
};

/**
 * Mock: 创建订单
 */
export const createMockOrder = async (data: CreateOrderRequest): Promise<{id: string}> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const newOrder = {
    ...generateMockOrder(mockOrdersData.length + 1),
    customerName: data.customerName,
    // 新创建的订单状态为pending
    status: 'pending' as const
  };
  
  mockOrdersData.unshift(newOrder);
  
  return { id: newOrder.id };
};

/**
 * Mock: 更新订单
 */
export const updateMockOrder = async (orderId: string, data: UpdateOrderRequest): Promise<OperationResult> => {
  await new Promise(resolve => setTimeout(resolve, 400));
  
  const index = mockOrdersData.findIndex(item => item.id === orderId);
  if (index === -1) {
    throw new Error('Order not found');
  }
  
  // 更新数据
  mockOrdersData[index] = {
    ...mockOrdersData[index],
    ...data
  };
  
  return {
    success: true,
    message: '更新成功'
  };
};

/**
 * Mock: 删除订单
 */
export const deleteMockOrder = async (orderId: string): Promise<OperationResult> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const index = mockOrdersData.findIndex(item => item.id === orderId);
  if (index === -1) {
    throw new Error('Order not found');
  }
  
  mockOrdersData.splice(index, 1);
  
  return {
    success: true,
    message: '删除成功'
  };
};
```

### 5. 特殊场景处理

#### 5.1 POST方法查询
```typescript
/**
 * 复杂查询（使用POST方法）
 * @param params 查询参数
 * @returns 查询结果
 */
export const searchOrdersAdvanced = async (params: AdvancedSearchParams): Promise<OrderPageResponse> => {
  if (USE_MOCK_API) {
    return getMockAdvancedSearch(params);
  }
  
  // 某些复杂查询后端要求使用POST方法
  const response = await request.post<any, ApiResult<OrderPageResponse>>(
    '/sales/orders/advanced-search',
    params
  );
  return response.result;
};
```

#### 5.2 流式下载
```typescript
/**
 * 下载大文件（流式下载）
 * @param fileId 文件ID
 * @returns 文件流
 */
export const downloadLargeFile = async (fileId: string): Promise<Blob> => {
  if (USE_MOCK_API) {
    // Mock大文件下载
    return new Promise((resolve) => {
      setTimeout(() => {
        const data = new Array(1000000).fill('mock data').join('\n');
        resolve(new Blob([data], { type: 'text/plain' }));
      }, 2000);
    });
  }
  
  const response = await request.get(`/files/download/${fileId}`, {
    responseType: 'blob',
    timeout: 30000 // 增加超时时间
  });
  
  return response as unknown as Blob;
};
```

### 6. 错误处理和重试机制

#### 6.1 API重试逻辑
```typescript
/**
 * 带重试机制的API调用
 * @param apiCall API调用函数
 * @param maxRetries 最大重试次数
 * @param delay 重试延迟
 * @returns API结果
 */
export const withRetry = async <T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      // 某些错误不应该重试
      if (error.response?.status === 400 || error.response?.status === 401) {
        break;
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt)));
    }
  }
  
  throw lastError!;
};

// 使用示例
export const getOrdersListWithRetry = async (params: OrderSearchParams): Promise<OrderPageResponse> => {
  return withRetry(() => getOrdersList(params), 3, 1000);
};
```

### 7. API开发检查清单

#### 7.1 开发前检查
- [ ] 确定业务模块和功能类型
- [ ] 设计完整的TypeScript类型定义
- [ ] 规划Mock数据结构和行为
- [ ] 了解后端API接口规范

#### 7.2 开发中检查
- [ ] API函数有完整的JSDoc注释
- [ ] 支持Mock和真实API切换
- [ ] 参数验证和错误处理完善
- [ ] 返回response.result而非整个response
- [ ] 特殊场景（如文件操作）处理正确

#### 7.3 开发后检查
- [ ] 类型定义准确且完整
- [ ] Mock数据逻辑正确
- [ ] API调用测试通过
- [ ] 错误场景测试通过
- [ ] 代码符合项目规范

### 8. 常见错误避免

#### 8.1 错误示例
```typescript
// ❌ 错误：返回整个response
export const getBadData = async () => {
  const response = await request.get('/api/data');
  return response; // 错误：应该返回response.result
};

// ❌ 错误：缺少类型定义
export const getBadTypedData = async (params: any) => {
  // 错误：参数和返回值都缺少类型
  const response = await request.get('/api/data', { params });
  return response.result;
};

// ❌ 错误：缺少Mock处理
export const getDataWithoutMock = async () => {
  // 错误：没有Mock处理逻辑
  const response = await request.get('/api/data');
  return response.result;
};
```

#### 8.2 正确示例
```typescript
// ✅ 正确：完整的API函数
export const getCorrectData = async (params: DataSearchParams): Promise<DataPageResponse> => {
  if (USE_MOCK_API) {
    return getMockDataList(params);
  }
  
  const response = await request.get<any, ApiResult<DataPageResponse>>('/api/data', { params });
  return response.result;
};
```

---

通过遵循这些规范，AI程序员可以开发出高质量、可维护的API接口代码，确保项目的稳定性和一致性。 