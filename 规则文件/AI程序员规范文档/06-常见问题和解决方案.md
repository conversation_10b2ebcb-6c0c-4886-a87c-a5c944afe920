# 常见问题和解决方案

## 🚀 快速启动配置

### 基础项目创建
```bash
# 创建项目并安装依赖
npm create vue@latest my-project
cd my-project && npm install

# 安装必要依赖
npm install element-plus axios vue-i18n@9
npm install -D sass
```

### 标准目录结构
```bash
mkdir -p src/{api/modules,types,mock/data,locales/modules,views}/{sales,parts,aftersales,base}
```

---

## 🛠️ 核心问题解决方案

### 1. 环境启动问题

#### npm install 报错
```bash
# 清理缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

#### 开发服务器启动失败  
```bash
# 检查端口占用
lsof -i :3000
kill -9 <PID>

# 清理缓存
rm -rf node_modules/.vite
npm run dev
```

### 2. TypeScript 类型问题

#### 找不到模块声明
```typescript
// 检查tsconfig.json路径映射
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": { "@/*": ["src/*"] }
  }
}

// 正确的组件导入
import MyComponent from '@/components/MyComponent.vue'  // ✅
```

#### Element Plus类型错误
```typescript
// 正确导入类型
import type { FormInstance, FormRules } from 'element-plus'

const formRef = ref<FormInstance>()
const rules = reactive<FormRules>({
  // 规则定义
})
```

#### API响应类型错误
```typescript
// 正确定义API类型
interface ApiResult<T> {
  success: boolean;
  result: T;
}

// API函数返回result部分
export const getOrdersList = async (params: SearchParams) => {
  const response = await request.get<any, ApiResult<OrderPageResponse>>('/api/orders', { params });
  return response.result;
};
```

### 3. 国际化问题

#### 翻译键找不到
```typescript
// 检查模块名和键路径
const { t, tc } = useModuleI18n('sales.orders')

// 正确的键路径
t('orders.title')              // ✅ 正确
t('orders.fields.customerName') // ✅ 正确
t('title')                     // ❌ 错误，缺少前缀
```

#### t() 和 tc() 使用规则
```typescript
// t() - 业务特定内容
t('orders.title')                    // 页面标题
t('orders.fields.customerName')      // 业务字段

// tc() - 通用内容  
tc('save')                          // 通用按钮
tc('operations')                    // 操作列
tc('saveSuccess')                   // 系统消息
```

#### 参数化翻译
```typescript
// JSON格式
{
  "deleteConfirm": "确定要删除 {name} 吗？"
}

// 传递参数
const message = t('deleteConfirm', { name: selectedItem.customerName })
```

### 4. Element Plus 组件问题

#### 表单验证不生效
```vue
<template>
  <el-form ref="formRef" :model="form" :rules="rules">
    <!-- prop属性必须和rules中的键名一致 -->
    <el-form-item label="用户名" prop="username">
      <el-input v-model="form.username" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'

const formRef = ref<FormInstance>()
const form = reactive({ username: '' })  // 必须存在对应属性
const rules = reactive<FormRules>({
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }]
})

const handleSubmit = async () => {
  await formRef.value?.validate()
}
</script>
```

#### 表格选择功能异常
```vue
<template>
  <el-table :data="tableData" @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="50" />
    <el-table-column prop="name" label="姓名" />
  </el-table>
</template>

<script setup lang="ts">
const selectedRows = ref<DataItem[]>([])

const handleSelectionChange = (selection: DataItem[]) => {
  selectedRows.value = selection
}
</script>
```

#### 分页器数据不同步
```vue
<template>
  <el-pagination
    v-model:current-page="pagination.page"
    v-model:page-size="pagination.size"
    :total="pagination.total"
    @current-change="loadData"
  />
</template>

<script setup lang="ts">
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const loadData = async () => {
  const result = await getDataList(pagination)
  tableData.value = result.list
  pagination.total = result.total  // 重要：设置总数
}
</script>
```

### 5. API 和数据问题

#### Mock数据不生效
```typescript
// 检查Mock配置
export const USE_MOCK_API = true

// API函数中的Mock判断
export const getOrdersList = async (params: SearchParams) => {
  if (USE_MOCK_API) {
    return getMockOrdersList(params);
  }
  const response = await request.get('/api/orders', { params });
  return response.result;
};
```

#### API请求总是失败
```typescript
// 检查axios配置
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
})

// 响应拦截器返回data部分
request.interceptors.response.use(
  (response) => response.data,
  (error) => Promise.reject(error)
)
```

#### 数据更新后界面不刷新
```typescript
// 使用响应式数据
const tableData = ref<DataItem[]>([])

// 数据操作后重新加载
const handleSave = async (data: FormData) => {
  await saveData(data)
  await loadData()  // 重新加载数据
}
```

### 6. 其他常见问题

#### 路由跳转失败
```typescript
import { useRouter } from 'vue-router'
const router = useRouter()

// 正确的跳转方式
router.push('/orders')
router.push({ name: 'Orders' })
router.push({ path: '/orders', query: { id: '123' } })
```

#### 构建失败
```bash
# 检查错误
npm run type-check
npm run lint

# 清理缓存重新构建
rm -rf node_modules/.vite dist
npm run build
```

#### 页面加载缓慢
```typescript
// 使用路由懒加载
const routes = [{
  path: '/orders',
  component: () => import('@/views/sales/OrdersView.vue')
}]

// 使用shallowRef优化大数据
const tableData = shallowRef<DataItem[]>([])
```

#### 内存泄漏
```typescript
// 清理定时器和事件监听
onBeforeUnmount(() => {
  if (timer.value) clearInterval(timer.value)
  window.removeEventListener('resize', handleResize)
})
```

---

## 🔍 快速诊断检查清单

### 错误关键词
- **"Cannot find module"** → 检查路径映射和导入
- **"Missing translation"** → 检查国际化键路径  
- **"Validation failed"** → 检查表单prop和rules
- **"Network Error"** → 检查API配置
- **"Build failed"** → 检查TypeScript错误

### 自检清单
- [ ] **代码规范**：命名规范、类型安全、导入规范
- [ ] **功能完整**：API实现、类型定义、错误处理、加载状态
- [ ] **国际化**：文本国际化、模块选择、翻译文件
- [ ] **UI样式**：响应式设计、组件使用、样式规范
- [ ] **性能**：构建无错误、控制台清洁、内存管理
- [ ] **测试**：功能测试、边界测试、Mock测试 