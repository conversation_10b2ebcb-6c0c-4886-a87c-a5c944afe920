# AI程序员总体规范文档

## 📋 项目概述

### 技术栈
- **前端框架**: Vue 3 + Composition API
- **开发语言**: TypeScript（强制类型安全）
- **构建工具**: Vite
- **状态管理**: Pinia
- **UI组件库**: Element Plus
- **国际化**: Vue I18n（模块化架构）
- **HTTP请求**: Axios（统一封装）

### 业务模块
- **sales**: 销售管理（车辆、订单、客户等）
- **aftersales**: 售后服务（维修、保修、预约等）
- **parts**: 零件管理（采购、库存、供应商等）
- **base**: 基础系统（用户、角色、权限等）

---

## 🎯 核心原则（AI必须严格遵循）

### 1. 🚫 绝对禁止事项
- **禁止硬编码**: 任何用户可见的中文/英文文本
- **禁止使用any类型**: 除非在request泛型参数中
- **禁止直接修改props**: 必须通过emit通知父组件
- **禁止在组件内创建假数据**: 必须通过API或Mock获取
- **禁止跳过类型定义**: 所有数据结构必须有TypeScript类型

### 2. ✅ 强制要求事项
- **类型安全**: 所有变量、函数、API都必须有明确类型
- **国际化**: 所有文本使用`useModuleI18n`进行国际化
- **模块化**: 严格按照业务模块组织代码
- **统一风格**: 遵循项目既定的代码风格
- **Mock支持**: 所有API必须支持Mock/真实数据切换

### 3. 🔄 标准工作流程
1. **分析需求** → 确定业务模块和功能类型
2. **创建类型定义** → 在`src/types/`下定义接口
3. **实现Mock数据** → 在`src/mock/data/`下创建测试数据
4. **开发API接口** → 在`src/api/modules/`下实现接口
5. **开发页面组件** → 使用统一模板和规范
6. **添加国际化** → 在对应模块下添加翻译
7. **配置路由** → 如需要，在路由文件中添加配置
8. **自检验证** → 确保符合所有规范要求

---

## 📁 项目目录结构

```
src/
├── api/                    # API接口层
│   ├── index.ts           # Axios配置和拦截器
│   └── modules/           # 按业务模块组织的API
│       ├── sales/         # 销售模块API
│       ├── aftersales/    # 售后模块API
│       ├── parts/         # 零件模块API
│       └── base/          # 基础模块API
├── components/            # 通用组件
│   ├── common/           # 通用业务组件
│   └── layout/           # 布局组件
├── views/                # 页面组件
│   ├── sales/            # 销售模块页面
│   ├── aftersales/       # 售后模块页面
│   ├── parts/            # 零件模块页面
│   └── base/             # 基础模块页面
├── stores/               # Pinia状态管理
│   └── modules/          # 按业务模块组织
├── types/                # TypeScript类型定义
│   ├── common/           # 通用类型
│   ├── sales/            # 销售模块类型
│   ├── aftersales/       # 售后模块类型
│   ├── parts/            # 零件模块类型
│   └── base/             # 基础模块类型
├── mock/                 # Mock数据
│   └── data/             # 按模块组织的Mock数据
├── locales/              # 国际化文件
│   └── modules/          # 模块化国际化
│       ├── common/       # 通用翻译
│       ├── sales/        # 销售模块翻译
│       ├── aftersales/   # 售后模块翻译
│       ├── parts/        # 零件模块翻译
│       └── base/         # 基础模块翻译
├── utils/                # 工具函数
│   ├── mock-config.ts    # Mock开关配置 ⭐️
│   └── ...               # 其他工具函数
├── router/               # 路由配置
└── plugins/              # 插件配置
```

---

## ⚙️ 核心配置文件

### Mock配置 (`src/utils/mock-config.ts`)
```typescript
// Mock开关配置
export const USE_MOCK_API = process.env.NODE_ENV === 'development' && 
  (process.env.VITE_USE_MOCK === 'true' || !process.env.VITE_API_BASE_URL);

// Mock延迟配置（模拟网络延迟）
export const MOCK_DELAY = {
  min: 300,   // 最小延迟(ms)
  max: 800    // 最大延迟(ms)
};
```

### 环境变量配置 (`.env.development`)
```bash
# API配置
VITE_API_BASE_URL=http://localhost:3000
VITE_USE_MOCK=true
```

### TypeScript配置关键点
```json
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
```

---

## 🏗️ 命名规范（强制执行）

### 文件命名规范
| 文件类型 | 命名规则 | 示例 |
|---------|---------|------|
| Vue页面组件 | **PascalCase** | `OrderManagementView.vue` |
| Vue业务组件 | **PascalCase** | `OrderFormDialog.vue` |
| TypeScript文件 | **kebab-case** | `order-management.ts` |
| API接口文件 | **kebab-case** | `order-api.ts` |

### 变量命名规范
```typescript
// ✅ 常量：SCREAMING_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_COUNT = 3;

// ✅ 变量/函数：camelCase
const orderList = ref([]);
const isLoading = ref(false);
const handleSubmit = () => {};

// ✅ 类型/接口：PascalCase
interface OrderInfo {
  id: string;
  customerName: string;
}

// ✅ 枚举：PascalCase + SCREAMING_SNAKE_CASE值
enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  COMPLETED = 'COMPLETED'
}
```

---

## 📁 类型定义组织原则（强制执行）

### 1. 类型文件位置规范
```typescript
// ✅ 通用类型：src/types/common/
src/types/common/
├── api.ts          # API响应通用类型
├── pagination.ts   # 分页相关类型
└── form.ts         # 表单通用类型

// ✅ 模块特定类型：src/types/[module]/
src/types/sales/
├── order.ts        # 订单相关类型
├── customer.ts     # 客户相关类型
└── product.ts      # 产品相关类型

// ❌ 禁止：API文件同目录定义类型
src/api/modules/sales/order.ts  // API实现
src/api/modules/sales/types.ts  // ❌ 禁止在此处定义类型
```

### 2. 类型导入规范
```typescript
// ✅ 正确的类型导入方式
import type { 
  OrderInfo,           // 从模块类型导入
  CreateOrderRequest,
  UpdateOrderRequest 
} from '@/types/sales/order';

import type { 
  ApiResult,          // 从通用类型导入
  PageResponse 
} from '@/types/common/api';
```

### 3. 标准类型定义
```typescript
// src/types/sales/order.ts

// 基础数据类型
export interface OrderInfo {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  status: OrderStatus;
  totalAmount: number;
  createdAt: string;
  updatedAt: string;
}

// 请求参数类型
export interface CreateOrderRequest {
  customerId: string;
  items: OrderItem[];
  notes?: string;
}

// 查询参数类型
export interface OrderSearchParams {
  keyword?: string;
  status?: OrderStatus;
  startDate?: string;
  endDate?: string;
  page: number;
  pageSize: number;
}

// 响应数据类型
export interface OrderListResponse {
  list: OrderInfo[];
  total: number;
  page: number;
  pageSize: number;
}
```

---

## 🎨 页面开发模式

### 页面类型识别
- **路由页面**: 可通过URL直接访问，放在功能目录根部
- **非路由页面**: 通过弹窗/跳转访问，放在`components/`目录

### 标准页面结构
```vue
<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('title') }}</h1>
    
    <!-- 搜索区域 -->
    <el-card class="search-card mb-20">
      <!-- 搜索表单 -->
    </el-card>
    
    <!-- 操作按钮区域 -->
    <div class="action-buttons mb-20">
      <!-- 操作按钮 -->
    </div>
    
    <!-- 数据展示区域 -->
    <el-card class="table-card">
      <!-- 表格和分页 -->
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'

// 根据页面功能选择正确模块
const { t, tc } = useModuleI18n('模块名')

// 响应式数据
// 方法定义
// 生命周期
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.mb-20 {
  margin-bottom: 20px;
}
</style>

---

## 🌐 国际化开发规范

### 模块选择决策表
| 页面功能关键词 | 模块选择 | 示例 |
|------------|----------|------|
| 零件、库存、采购、供应商 | `parts.*` | `parts.management` |
| 销售、订单、客户、车辆 | `sales.*` | `sales.orders` |
| 维修、售后、工单、预约 | `aftersales.*` | `aftersales.repairs` |
| 用户、角色、权限、菜单 | `base.*` | `base.users` |
| 通用组件或跨模块 | `common` | `common` |

### 使用规范
```typescript
// 导入国际化函数
const { t, tc } = useModuleI18n('模块名')

// 业务特定内容使用 t()
t('title')              // 页面标题
t('fields.partName')    // 字段标签
t('actions.create')     // 业务按钮

// 通用操作使用 tc()
tc('save')              // 保存按钮
tc('cancel')            // 取消按钮
tc('operations')        // 操作列
```

---

## 📊 数据管理规范

### API开发模式
```typescript
// 标准API函数结构
export const getFunctionName = async (params: ParamsType): Promise<ResponseType> => {
  if (USE_MOCK_API) {
    return getMockFunction(params);
  }
  const response = await request.get<any, ApiResult<ResponseType>>('/api/path', { params });
  return response.result;  // 重要：返回result，不是整个response
};
```

### Mock数据处理
- 所有API必须有对应的Mock实现
- Mock数据应模拟真实业务场景
- 支持分页、筛选、排序等功能
- 模拟适当的网络延迟（200-500ms）

---

## ✅ AI开发检查清单

### 代码生成前检查
- [ ] 确定正确的业务模块
- [ ] 选择合适的页面类型（路由/非路由）
- [ ] 规划需要的API接口
- [ ] 设计数据结构和类型

### 代码生成后检查
- [ ] 所有文本都已国际化
- [ ] 类型定义完整且准确
- [ ] API支持Mock和真实数据切换
- [ ] 页面结构符合标准模板
- [ ] 按钮和操作符合设计规范
- [ ] 没有任何硬编码数据
- [ ] 代码通过TypeScript类型检查
- [ ] 遵循命名规范

---

## 🚨 常见错误避免

### ❌ 错误示例
```vue
<!-- 错误：硬编码文本 -->
<h1>用户管理</h1>
<el-button>保存</el-button>

<!-- 错误：混用t和tc -->
<el-button>{{ t('save') }}</el-button>

<!-- 错误：硬编码数据 -->
const tableData = ref([{ id: 1, name: '测试' }])
```

### ✅ 正确示例
```vue
<!-- 正确：使用国际化 -->
<h1>{{ t('title') }}</h1>
<el-button>{{ tc('save') }}</el-button>

<!-- 正确：通过API获取数据 -->
const tableData = ref([])
const loadData = async () => {
  tableData.value = await getDataList()
}
```

---

## 📞 获取帮助

遇到问题时的解决顺序：
1. 查看本规范文档的相关章节
2. 参考同模块下已有的实现
3. 检查项目配置文件（如`vite.config.ts`）
4. 查看Element Plus官方文档
5. 请求人工协助

---

**记住：作为AI程序员，严格遵循规范比创新更重要。保持代码风格的一致性是我们的首要目标。** 