### API 设计规范 (RESTful) (V2.0 - 公司标准版)

本规范定义了项目对外提供RESTful API的标准，旨在确保接口的规范性、一致性和易用性。**本文档根据公司后端代码相关规范进行编写。**

#### 1. URL 设计
- **版本化**：API **必须**包含版本号，置于路径开头。例如：`/api/v1/...`。
- **模块化**：API **必须**体现业务模块层级，格式：`/api/v1/{module}/{resource}`。
  - **销售模块**: `/api/v1/sales/...`
  - **售后模块**: `/api/v1/after-sales/...`
  - **配件模块**: `/api/v1/parts/...`
  - **基础模块**: `/api/v1/basic/...`
- **资源导向**：URL代表一种资源，**必须**使用名词复数形式，并用小写字母和破折号。
  - **推荐**: `/api/v1/sales/users`, `/api/v1/after-sales/checkins`, `/api/v1/sales/purchase-orders`
  - **禁止**: `/api/v1/getAllUsers`, `/api/v1/createOrder`
- **路径参数**：使用路径参数定位特定资源：`/api/v1/after-sales/checkins/{checkinId}`。

#### 2. HTTP 方法 (动词)
严格按照HTTP方法的语义使用动词来操作资源：
- **`GET`**：读取/检索资源（查询）。
- **`POST`**：创建新资源。
- **`PUT`**：**完整更新**一个已存在的资源。
- **`DELETE`**：删除一个资源（逻辑删除）。

#### 3. 统一响应结构 (`Result`)
所有API的响应体**必须**使用公司基础架构中定义的`Result<T>`进行包装。

```java
// 公司通用响应对象 Result<T>
public class Result<T> {
    private boolean success;      // 成功标志
    private String message;       // 返回消息
    private String code;          // 返回代码
    private String traceId;       // 链路追踪ID
    private T result;             // 返回数据对象
    private long timestamp;       // 时间戳
}
```
- **`success` (是否成功)**：`true` 或 `false`。
- **`message` (响应消息)**：对操作结果的文本描述。
- **`code` (响应码)**：业务响应码，成功时为"10000000"，其他值代表各类业务异常或系统错误。
- **`traceId` (链路追踪ID)**：用于分布式链路追踪的唯一标识。
- **`result` (数据体)**：存放返回的业务数据，可以是VO对象或`PageResult`。
- **`timestamp` (时间戳)**：响应生成的时间戳。

#### 4. 数据对象 (DTO / VO)
- **分层明确**：严格区分不同场景的数据对象。
  - **DTO (Data Transfer Object)**: 用于服务层之间的数据传输。
  - **VO (View Object)**: 用于Controller层返回给前端的视图对象。
- **强制使用VO**：Controller的响应体**必须**是VO对象，严禁直接返回数据库实体(`Entity/PO`)。
- **命名规范**：

| 对象类型 | 命名规范 | 示例 | 说明 |
|---------|----------|------|------|
| **请求参数对象** | `...Query`, `...Form` | `UserPageQuery`, `UserCreateForm` | 用于接收请求参数 |
| **数据传输对象** | **必须**以`DTO`结尾 | `UserCreateDTO`, `OrderUpdateDTO` | 用于服务层之间数据传输 |
| **视图对象** | **必须**以`VO`结尾 | `UserVO`, `OrderDetailVO` | 用于Controller返回给前端 |
| **数据库实体** | **必须**以`PO`结尾 | `UserPO`, `OrderPO` | 数据库实体类，必须继承BasePO |
- **字段规范**：JSON字段名**必须**使用小驼峰命名法 (`camelCase`)。
- **实体类规范**：
  - 数据库实体类必须以`PO`结尾，如`UserPO`、`OrderPO`。
  - 所有实体类必须继承`BasePO`基类。
  - 时间字段必须使用`java.util.Date`类型。
- **对象转换规范**：
  - **强制使用**：`cn.hutool.core.bean.BeanUtils.copyProperties(source, target)`
  - **禁止使用**：Spring BeanUtils、Apache BeanUtils、MapStruct或手动逐字段赋值
  - **转换场景**：DTO→PO、PO→VO、DTO→DTO、VO→VO等所有转换场景

#### 5. 过滤、排序与分页
- **请求**：使用统一的mybatis-plus的`IPage`对象作为参数。
  ```java
    public class QueryPage<T> extends IPage {
        private Integer pageNum; // 当前页码，从1开始，可选，默认1
        private Integer pageSize; // 每页数量，可选，默认20
    }
  ```
- **响应**：响应体`result`中**必须**使用mybatis-plus的`Page<T>`对象。
  ```java
    public class Page<T> {
        private List<T> records; // 当前页数据列表
        private Long total;      // 总记录数
        private Long pageNum;    // 当前页码
        private Long pageSize;   // 每页条数
        private Long pages;      // 总页数
    }
  ```
- **其他查询**：
  - **列表查询**：使用`...ListQuery`对象。
  - **详情查询**：使用ID作为路径参数。

#### 6. API 文档 (Swagger)
- 所有API接口**必须**使用 `springdoc-openapi` (Swagger) 提供实时、准确的文档。
- **必须**使用注解 (`@Operation`, `@Parameter`, `@Tag` 等)清晰地描述接口功能、参数、标签和响应模型。
- Controller类应使用`@Tag`注解进行分组。

#### 7. HTTP 状态码
- **业务成功**: 统一返回HTTP `200 OK`，具体的业务成功或失败通过`Result`中的`code`和`success`字段来体现。
- **客户端/服务端技术错误**:
  - `401 Unauthorized`: 用户未认证。
  - `403 Forbidden`: 用户无权访问。
  - `404 Not Found`: 请求的API路径不存在。
  - `500 Internal Server Error`: 服务器内部发生未被捕获的未知异常。

#### 8. 基础设施集成规范
- **命名规范强制要求**：
  - DTO对象必须以`DTO`结尾
  - VO对象必须以`VO`结尾
  - PO对象必须以`PO`结尾
- **分布式锁**：涉及并发访问共享资源的接口必须使用`@DistributedLock`注解。
- **幂等性**：所有可能重复提交的业务操作必须使用`@Idempotent`注解。
- **业务编号生成**：所有业务单号必须通过`BusinessCodeService.getCode()`方法生成。
- **异常处理**：业务异常必须使用`BusinessException`和`ExceptionEnum`。
- **响应格式**：所有接口响应必须使用`Result<T>`格式，由`ResponseBodyAdvice`自动包装。
