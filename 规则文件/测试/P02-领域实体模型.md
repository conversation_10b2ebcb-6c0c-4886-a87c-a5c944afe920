# P02-领域实体模型

## 文档概述
本文档基于P01页面元素与数据分析清单，结合现有数据库表结构，构建到店登记功能的领域实体模型和数据库表结构设计。遵循数据库设计规范，强调复用现有模型，并明确新增和变更的实体关系。

---

## 1. 现有模型映射分析

### 1.1 现有表复用情况

根据现有数据库结构分析，以下表可以直接复用：

**无直接复用表**
- 现有数据库主要包含销售、订单、库存等模块
- 到店登记作为售后模块的新功能，需要创建专门的业务表
- 但可以关联现有的用户、门店等基础数据

### 1.2 关联现有表的字段

**用户信息关联**
- 关联现有用户表（具体表名待确认）
- 存储用户ID和用户名称（冗余设计）

**门店信息关联**
- 关联现有门店表
- 用于权限控制和数据隔离

---

## 2. 新增实体设计

### 2.1 核心实体：到店登记单 (tt_after_sales_checkin)

#### 实体描述
到店登记单是售后模块的核心实体，记录客户车辆到店登记的完整信息，包括车辆信息、客户信息、服务信息等。

#### 字段设计

**主键字段**
- `id`: BIGINT - 主键ID，自增

**业务标识字段**
- `checkin_id`: VARCHAR(50) - 登记单号，唯一标识，遵循门店简称+日期+流水号规则
- `store_id`: BIGINT - 门店ID，关联门店表

**车辆信息字段（适当冗余）**
- `license_plate`: VARCHAR(20) - 车牌号，必填
- `vin`: VARCHAR(17) - VIN码，必填
- `vehicle_model`: VARCHAR(100) - 车型
- `vehicle_configuration`: VARCHAR(150) - 车辆配置
- `color`: VARCHAR(50) - 颜色
- `mileage`: INT - 里程数，单位：公里
- `delivery_time`: TIMESTAMP - 交车时间，用于车龄计算

**客户信息字段**
- `repair_person_name`: VARCHAR(100) - 送修人姓名，必填
- `repair_person_phone`: VARCHAR(20) - 送修人电话，必填

**服务信息字段**
- `service_advisor_id`: BIGINT - 服务顾问ID，关联用户表
- `service_advisor_name`: VARCHAR(100) - 服务顾问姓名，冗余字段
- `service_type`: VARCHAR(50) - 服务类型，关联数据字典
- `notes`: TEXT - 备注信息，可选

**关联信息字段**
- `related_repair_order_id`: BIGINT - 关联环检单转工单ID，默认NULL

**状态字段**
- `status`: VARCHAR(20) - 登记单状态，关联数据字典

**标准字段**
- `is_deleted`: TINYINT(1) - 是否删除，默认0
- `created_by`: VARCHAR(32) - 创建人ID
- `created_at`: TIMESTAMP - 创建时间
- `updated_by`: VARCHAR(32) - 更新人ID
- `updated_at`: TIMESTAMP - 更新时间

#### 索引设计
- 主键索引：`PRIMARY KEY (id)`
- 唯一索引：`UNIQUE KEY uk_checkin_id (checkin_id)`
- 普通索引：
  - `KEY idx_store_id (store_id)`
  - `KEY idx_license_plate (license_plate)`
  - `KEY idx_vin (vin)`
  - `KEY idx_service_advisor_id (service_advisor_id)`
  - `KEY idx_status (status)`
  - `KEY idx_created_at (created_at)`
  - `KEY idx_repair_person_phone (repair_person_phone)`

---

## 3. 数据字典设计

### 3.1 使用统一字典表 (tc_dic_data)

#### 字典类型定义
到店登记功能使用现有的统一字典表 `tc_dic_data` 来管理所有字典值，不单独创建字典表。

#### 字典类型清单

**服务类型字典**
- 字典类别编码：`0107` (工单类型)
- 用途：定义到店登记中可选择的服务类型
- 预设字典值：
  - `01070001` - 维修
  - `01070002` - 保养
  - `01070003` - 检查

**登记单状态字典**
- 字典类别编码：`0105` (工单状态)
- 用途：定义到店登记单的状态类型
- 预设字典值：
  - `01050001` - 正常
  - `01050007` - 已取消

---

## 4. 实体关系设计

### 4.1 关系图

```
tt_after_sales_checkin (到店登记单)
├── store_id → 门店表 (一对多)
├── service_advisor_id → 用户表 (一对多)
├── service_type → tc_dict_data (SERVICE_TYPE) (一对多)
├── status → tc_dict_data (CHECKIN_STATUS) (一对多)
└── related_repair_order_id → 工单表 (一对一，可空)
```

### 4.2 关系说明

**一对多关系**
- 一个门店可以有多个登记单
- 一个服务顾问可以处理多个登记单
- 一个服务类型字典值可以被多个登记单使用
- 一个状态字典值可以被多个登记单使用

**一对一关系**
- 一个登记单最多关联一个工单（环检单转工单后）

**字典关联**
- service_type 字段关联 tc_dic_data 表中 dic_category_code='0107' 的 dic_code 记录
- status 字段关联 tc_dic_data 表中 dic_category_code='0105' 的 dic_code 记录

---

## 5. 数据库DDL语句

### 5.1 到店登记单表

```sql
-- 到店登记单表
DROP TABLE IF EXISTS `tt_after_sales_checkin`;
CREATE TABLE `tt_after_sales_checkin` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `checkin_id` VARCHAR(50) NOT NULL COMMENT '登记单号，格式：门店简称+日期+流水号',
  `store_id` BIGINT NOT NULL COMMENT '门店ID，关联门店表',
  
  -- 车辆信息字段（适当冗余）
  `license_plate` VARCHAR(20) NOT NULL COMMENT '车牌号',
  `vin` VARCHAR(17) NOT NULL COMMENT 'VIN码，车辆识别代号',
  `vehicle_model` VARCHAR(100) DEFAULT NULL COMMENT '车型',
  `vehicle_configuration` VARCHAR(150) DEFAULT NULL COMMENT '车辆配置',
  `color` VARCHAR(50) DEFAULT NULL COMMENT '颜色',
  `mileage` INT DEFAULT NULL COMMENT '里程数，单位：公里',
  `delivery_time` TIMESTAMP NULL DEFAULT NULL COMMENT '交车时间，用于车龄计算',
  
  -- 客户信息字段
  `repair_person_name` VARCHAR(100) NOT NULL COMMENT '送修人姓名',
  `repair_person_phone` VARCHAR(20) NOT NULL COMMENT '送修人电话',
  
  -- 服务信息字段
  `service_advisor_id` BIGINT NOT NULL COMMENT '服务顾问ID，关联用户表',
  `service_advisor_name` VARCHAR(100) NOT NULL COMMENT '服务顾问姓名',
  `service_type` VARCHAR(50) NOT NULL COMMENT '服务类型，关联tc_dic_data表中dic_category_code=0107的dic_code',
  `notes` TEXT DEFAULT NULL COMMENT '备注信息',
  
  -- 关联信息字段
  `related_repair_order_id` BIGINT DEFAULT NULL COMMENT '关联环检单转工单ID，默认为空',
  
  -- 状态字段
  `status` VARCHAR(20) NOT NULL DEFAULT '01050001' COMMENT '登记单状态，关联tc_dic_data表中dic_category_code=0105的dic_code',

  -- 标准字段
  `is_deleted` TINYINT(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_by` VARCHAR(32) DEFAULT NULL COMMENT '创建人ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` VARCHAR(32) DEFAULT NULL COMMENT '更新人ID',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_checkin_id` (`checkin_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_license_plate` (`license_plate`),
  KEY `idx_vin` (`vin`),
  KEY `idx_service_advisor_id` (`service_advisor_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_repair_person_phone` (`repair_person_phone`),
  KEY `idx_related_repair_order_id` (`related_repair_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='售后到店登记单表';
```

### 5.2 字典数据初始化

```sql
-- 新增字典类别
INSERT INTO `tc_dic_category` (`category_code`, `category_zh_name`, `category_en_name`, `category_ms_name`, `category_desc`, `category_sort`, `created_by`) VALUES
('0301', '服务类型', 'Service Type', 'Jenis Perkhidmatan', '到店登记服务类型分类', 1, 'SYSTEM'),
('0302', '登记单状态', 'Checkin Status', 'Status Daftar Masuk', '到店登记单状态分类', 2, 'SYSTEM');

-- 服务类型字典数据（0301）
INSERT INTO `tc_dic_data` (`dic_category_code`, `dic_code`, `dic_zh_name`, `dic_en_name`, `dic_ms_name`, `dic_sort`, `created_by`) VALUES
('0301', '03010001', '维修', 'Repair', 'Pembaikan', 1, 'SYSTEM'),
('0301', '03010002', '保养', 'Maintenance', 'Penyelenggaraan', 2, 'SYSTEM'),
('0301', '03010003', '检查', 'Inspection', 'Pemeriksaan', 3, 'SYSTEM');

-- 登记单状态字典数据（0302）
INSERT INTO `tc_dic_data` (`dic_category_code`, `dic_code`, `dic_zh_name`, `dic_en_name`, `dic_ms_name`, `dic_sort`, `created_by`) VALUES
('0302', '03020001', '正常', 'Normal', 'Normal', 1, 'SYSTEM'),
('0302', '03020002', '已取消', 'Cancelled', 'Dibatalkan', 2, 'SYSTEM');
```

---

## 6. 数据完整性约束

### 6.1 业务约束规则

**唯一性约束**
- 登记单号全局唯一
- 服务类型编码唯一
- 状态编码唯一

**必填字段约束**
- 登记单号、门店ID、车牌号、VIN码为必填
- 送修人姓名、送修人电话为必填
- 服务顾问ID、服务顾问姓名为必填
- 服务类型、状态为必填

**数据格式约束**
- 车牌号长度限制20字符
- VIN码长度限制17字符
- 手机号长度限制20字符
- 登记单号长度限制50字符

### 6.2 关联完整性

**软关联约束**
- service_advisor_id 必须存在于用户表中
- store_id 必须存在于门店表中
- service_type 必须存在于 tc_dic_data 表中 dic_category_code='0107' 的 dic_code 中
- status 必须存在于 tc_dic_data 表中 dic_category_code='0105' 的 dic_code 中

**可空关联**
- related_repair_order_id 可以为空（未关联工单时）
- delivery_time 可以为空（无交车记录时）

---

## 7. 向后兼容性评估

### 7.1 新增表影响分析

**无向后兼容性问题**
- 所有表都是新增的业务表
- 不影响现有系统功能
- 不修改现有表结构

### 7.2 数据迁移策略

**无需数据迁移**
- 全新功能模块，无历史数据
- 字典数据通过初始化脚本添加到现有 tc_dic_data 表中
- 业务表从功能上线后开始积累数据

---

## 8. 性能优化设计

### 8.1 索引优化

**查询优化索引**
- 按门店查询：`idx_store_id`
- 按服务顾问查询：`idx_service_advisor_id`
- 按车牌号查询：`idx_license_plate`
- 按VIN码查询：`idx_vin`
- 按状态查询：`idx_status`
- 按创建时间查询：`idx_created_at`
- 按送修人电话查询：`idx_repair_person_phone`

**联合索引考虑**
- 如果经常按门店+状态查询，可考虑添加联合索引：`idx_store_status (store_id, status)`
- 如果经常按服务顾问+创建时间查询，可考虑添加联合索引：`idx_advisor_time (service_advisor_id, created_at)`

### 8.2 存储优化

**字段长度优化**
- 根据实际业务需求设置合理的字段长度
- 避免过长的VARCHAR字段影响性能

**数据类型优化**
- 使用合适的数据类型减少存储空间
- 里程数使用INT类型，支持到2,147,483,647公里

---

## 9. 扩展性设计

### 9.1 字段扩展

**预留扩展能力**
- notes字段使用TEXT类型，支持大量备注信息
- 字典表支持动态配置，便于业务扩展

### 9.2 关联扩展

**未来可能的关联**
- 与客户表的关联（如果需要客户档案管理）
- 与车辆档案表的关联（如果需要车辆历史记录）
- 与工单表的关联（环检单转工单功能）

### 9.3 字典扩展

**字典管理优势**
- 使用统一的 tc_dic_data 表管理所有字典值
- 支持动态配置，无需修改表结构
- 便于系统管理和维护

---

## 自检清单确认

- [x] 已最大限度地复用现有数据库模型（关联用户表、门店表）
- [x] 新增模型完全覆盖P01中的核心数据对象
- [x] 实体间的关系已正确定义（一对多、一对一关系）
- [x] 每个字段的命名、数据类型和约束符合数据库设计规范
- [x] 已评估数据库变更的向后兼容性（无影响）
- [x] 已设计合理的索引支持查询性能
- [x] 已考虑数据完整性约束和业务规则
- [x] 已提供完整的DDL语句和初始化数据
- [x] 已澄清所有疑问点并获得确认
```
