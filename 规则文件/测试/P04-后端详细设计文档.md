# P04-后端详细设计文档

## 文档概述
本文档基于P01-P03的设计成果，严格遵循后端代码规范，详细设计到店登记功能的后端实现方案。包含完整的Controller、Service、Entity、Mapper等各层代码设计，确保代码质量和架构一致性。

---

## 1. 项目结构设计

### 1.1 包结构规范

```
src/main/java/com/perodua/aftersales/
├── controller/
│   └── checkin/
│       └── CheckinController.java
├── service/
│   └── checkin/
│       ├── CheckinService.java
│       └── impl/
│           └── CheckinServiceImpl.java
├── entity/
│   └── CheckinPO.java
├── mapper/
│   └── CheckinMapper.java
├── dto/
│   └── checkin/
│       ├── CheckinPageQueryDTO.java
│       ├── CheckinCreateDTO.java
│       ├── CheckinUpdateDTO.java
│       └── CheckinDetailQueryDTO.java
├── vo/
│   └── checkin/
│       ├── CheckinVO.java
│       ├── CheckinDetailVO.java
│       └── CheckinCreateResponseVO.java
├── convert/
│   └── CheckinConverter.java
└── constants/
    └── CheckinConstants.java
```

### 1.2 模块依赖关系

```
CheckinController → CheckinService → CheckinMapper → CheckinPO
                ↓                 ↓
            CheckinDTO        CheckinVO
                ↓                 ↑
            CheckinConverter ------
```

---

## 2. 实体层设计 (Entity)

### 2.1 CheckinPO 实体类

```java
package com.perodua.aftersales.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.perodua.common.entity.base.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 到店登记单实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tt_after_sales_checkin")
@ApiModel(value = "CheckinPO", description = "到店登记单实体")
public class CheckinPO extends BasePO {

    @ApiModelProperty("登记单号，格式：门店简称+日期+流水号")
    @TableField("checkin_id")
    private String checkinId;

    @ApiModelProperty("门店ID，关联门店表")
    @TableField("store_id")
    private Long storeId;

    @ApiModelProperty("车牌号")
    @TableField("license_plate")
    private String licensePlate;

    @ApiModelProperty("VIN码，车辆识别代号")
    @TableField("vin")
    private String vin;

    @ApiModelProperty("车型")
    @TableField("vehicle_model")
    private String vehicleModel;

    @ApiModelProperty("车辆配置")
    @TableField("vehicle_configuration")
    private String vehicleConfiguration;

    @ApiModelProperty("颜色")
    @TableField("color")
    private String color;

    @ApiModelProperty("里程数，单位：公里")
    @TableField("mileage")
    private Integer mileage;

    @ApiModelProperty("交车时间，用于车龄计算")
    @TableField("delivery_time")
    private Date deliveryTime;

    @ApiModelProperty("送修人姓名")
    @TableField("repair_person_name")
    private String repairPersonName;

    @ApiModelProperty("送修人电话")
    @TableField("repair_person_phone")
    private String repairPersonPhone;

    @ApiModelProperty("服务顾问ID，关联用户表")
    @TableField("service_advisor_id")
    private Long serviceAdvisorId;

    @ApiModelProperty("服务顾问姓名")
    @TableField("service_advisor_name")
    private String serviceAdvisorName;

    @ApiModelProperty("服务类型，关联tc_dict_data表SERVICE_TYPE类型")
    @TableField("service_type")
    private String serviceType;

    @ApiModelProperty("备注信息")
    @TableField("notes")
    private String notes;

    @ApiModelProperty("关联环检单转工单ID，默认为空")
    @TableField("related_repair_order_id")
    private Long relatedRepairOrderId;

    @ApiModelProperty("登记单状态，关联tc_dict_data表CHECKIN_STATUS类型")
    @TableField("status")
    private String status;
}
```

---

## 3. 数据传输对象设计 (DTO)

### 3.1 分页查询DTO

```java
package com.perodua.aftersales.dto.checkin;

import com.perodua.common.entity.base.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 到店登记分页查询DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CheckinPageQueryDTO", description = "到店登记分页查询参数")
public class CheckinPageQueryDTO extends BaseQueryDTO {

    @ApiModelProperty("登记编号")
    @Size(max = 50, message = "登记编号长度不能超过50个字符")
    private String checkinId;

    @ApiModelProperty("车牌号")
    @Size(max = 20, message = "车牌号长度不能超过20个字符")
    private String licensePlate;

    @ApiModelProperty("送修人姓名")
    @Size(max = 100, message = "送修人姓名长度不能超过100个字符")
    private String repairPersonName;

    @ApiModelProperty("送修人电话")
    @Pattern(regexp = "^[0-9-+()\\s]*$", message = "送修人电话格式不正确")
    @Size(max = 20, message = "送修人电话长度不能超过20个字符")
    private String repairPersonPhone;

    @ApiModelProperty("开始时间 (YYYY-MM-DD)")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "开始时间格式必须为YYYY-MM-DD")
    private String createdAtStart;

    @ApiModelProperty("结束时间 (YYYY-MM-DD)")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "结束时间格式必须为YYYY-MM-DD")
    private String createdAtEnd;
}
```

### 3.2 新增DTO

```java
package com.perodua.aftersales.dto.checkin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * 到店登记新增DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@ApiModel(value = "CheckinCreateDTO", description = "到店登记新增参数")
public class CheckinCreateDTO {

    @ApiModelProperty(value = "车牌号", required = true)
    @NotBlank(message = "车牌号不能为空")
    @Size(max = 20, message = "车牌号长度不能超过20个字符")
    private String licensePlate;

    @ApiModelProperty(value = "VIN码", required = true)
    @NotBlank(message = "VIN码不能为空")
    @Size(min = 17, max = 17, message = "VIN码长度必须为17位")
    private String vin;

    @ApiModelProperty("车型")
    @Size(max = 100, message = "车型长度不能超过100个字符")
    private String vehicleModel;

    @ApiModelProperty("车辆配置")
    @Size(max = 150, message = "车辆配置长度不能超过150个字符")
    private String vehicleConfiguration;

    @ApiModelProperty("颜色")
    @Size(max = 50, message = "颜色长度不能超过50个字符")
    private String color;

    @ApiModelProperty("里程数，单位：公里")
    @Min(value = 0, message = "里程数不能为负数")
    @Max(value = 9999999, message = "里程数不能超过9999999公里")
    private Integer mileage;

    @ApiModelProperty("交车时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "送修人姓名", required = true)
    @NotBlank(message = "送修人姓名不能为空")
    @Size(max = 100, message = "送修人姓名长度不能超过100个字符")
    private String repairPersonName;

    @ApiModelProperty(value = "送修人电话", required = true)
    @NotBlank(message = "送修人电话不能为空")
    @Pattern(regexp = "^[0-9-+()\\s]+$", message = "送修人电话格式不正确")
    @Size(max = 20, message = "送修人电话长度不能超过20个字符")
    private String repairPersonPhone;

    @ApiModelProperty(value = "服务类型", required = true)
    @NotBlank(message = "服务类型不能为空")
    @Pattern(regexp = "^\\d{8}$", message = "服务类型编码格式不正确")
    private String serviceType;

    @ApiModelProperty("备注信息")
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    private String notes;
}
```

### 3.3 编辑DTO

```java
package com.perodua.aftersales.dto.checkin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 到店登记编辑DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@ApiModel(value = "CheckinUpdateDTO", description = "到店登记编辑参数")
public class CheckinUpdateDTO {

    @ApiModelProperty("里程数，单位：公里")
    @Min(value = 0, message = "里程数不能为负数")
    @Max(value = 9999999, message = "里程数不能超过9999999公里")
    private Integer mileage;

    @ApiModelProperty(value = "送修人姓名", required = true)
    @NotBlank(message = "送修人姓名不能为空")
    @Size(max = 100, message = "送修人姓名长度不能超过100个字符")
    private String repairPersonName;

    @ApiModelProperty(value = "送修人电话", required = true)
    @NotBlank(message = "送修人电话不能为空")
    @Pattern(regexp = "^[0-9-+()\\s]+$", message = "送修人电话格式不正确")
    @Size(max = 20, message = "送修人电话长度不能超过20个字符")
    private String repairPersonPhone;

    @ApiModelProperty(value = "服务类型", required = true)
    @NotBlank(message = "服务类型不能为空")
    @Pattern(regexp = "^\\d{8}$", message = "服务类型编码格式不正确")
    private String serviceType;

    @ApiModelProperty("备注信息")
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    private String notes;
}
```

### 3.4 详情查询DTO

```java
package com.perodua.aftersales.dto.checkin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 到店登记详情查询DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@ApiModel(value = "CheckinDetailQueryDTO", description = "到店登记详情查询参数")
public class CheckinDetailQueryDTO {

    @ApiModelProperty(value = "登记单ID", required = true)
    @NotNull(message = "登记单ID不能为空")
    @Min(value = 1, message = "登记单ID必须大于0")
    private Long id;
}
```

---

## 4. 视图对象设计 (VO)

### 4.1 列表VO

```java
package com.perodua.aftersales.vo.checkin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 到店登记列表VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@ApiModel(value = "CheckinVO", description = "到店登记列表视图对象")
public class CheckinVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("登记编号")
    private String checkinId;

    @ApiModelProperty("车牌号")
    private String licensePlate;

    @ApiModelProperty("VIN码")
    private String vin;

    @ApiModelProperty("车型")
    private String vehicleModel;

    @ApiModelProperty("车辆配置")
    private String vehicleConfiguration;

    @ApiModelProperty("颜色")
    private String color;

    @ApiModelProperty("里程数")
    private Integer mileage;

    @ApiModelProperty("车龄（月）")
    private Integer vehicleAge;

    @ApiModelProperty("送修人姓名")
    private String repairPersonName;

    @ApiModelProperty("送修人电话")
    private String repairPersonPhone;

    @ApiModelProperty("服务顾问姓名")
    private String serviceAdvisorName;

    @ApiModelProperty("关联工单ID")
    private Long relatedRepairOrderId;

    @ApiModelProperty("服务类型编码")
    private String serviceType;

    @ApiModelProperty("状态编码")
    private String status;

    @ApiModelProperty("备注")
    private String notes;

    @ApiModelProperty("创建时间")
    private Date createdAt;

    @ApiModelProperty("更新时间")
    private Date updatedAt;
}
```

### 4.2 详情VO

```java
package com.perodua.aftersales.vo.checkin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 到店登记详情VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CheckinDetailVO", description = "到店登记详情视图对象")
public class CheckinDetailVO extends CheckinVO {
    // 继承CheckinVO的所有字段，用于详情展示
    // 如果需要额外字段，可以在此添加
}
```

### 4.3 创建响应VO

```java
package com.perodua.aftersales.vo.checkin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 到店登记创建响应VO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@ApiModel(value = "CheckinCreateResponseVO", description = "到店登记创建响应视图对象")
public class CheckinCreateResponseVO {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("登记编号")
    private String checkinId;
}
```

---

## 5. 数据访问层设计 (Mapper)

### 5.1 CheckinMapper 接口

```java
package com.perodua.aftersales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.perodua.aftersales.entity.CheckinPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 到店登记Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface CheckinMapper extends BaseMapper<CheckinPO> {

    // 继承BaseMapper，获得基础CRUD能力
    // 复杂查询需要时可在此添加自定义方法

    // 注意：根据MyBatis Plus规范，单表操作不需要编写XML文件
    // 只有涉及多表关联、复杂统计等场景才需要编写XML
}
```

---

## 6. 业务逻辑层设计 (Service)

### 6.1 CheckinService 接口

```java
package com.perodua.aftersales.service.checkin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.perodua.aftersales.dto.checkin.*;
import com.perodua.aftersales.entity.CheckinPO;
import com.perodua.aftersales.vo.checkin.*;

/**
 * 到店登记业务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface CheckinService extends IService<CheckinPO> {

    /**
     * 分页查询登记单列表
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    IPage<CheckinVO> pageCheckins(CheckinPageQueryDTO queryDTO);

    /**
     * 新增登记单
     *
     * @param createDTO 新增参数
     * @return 创建结果
     */
    CheckinCreateResponseVO createCheckin(CheckinCreateDTO createDTO);

    /**
     * 编辑登记单
     *
     * @param id 登记单ID
     * @param updateDTO 编辑参数
     */
    void updateCheckin(Long id, CheckinUpdateDTO updateDTO);

    /**
     * 删除登记单（取消）
     *
     * @param id 登记单ID
     */
    void deleteCheckin(Long id);

    /**
     * 查看登记单详情
     *
     * @param id 登记单ID
     * @return 详情信息
     */
    CheckinDetailVO getCheckinDetail(Long id);

    /**
     * 创建环检单
     * TODO: 等环检单功能开发设计时补上
     *
     * @param checkinId 登记单ID
     * @return 环检单信息
     */
    // InspectionOrderVO createInspectionOrder(Long checkinId);

    /**
     * 导出登记单列表
     * TODO: 暂不实现，留待后续开发
     *
     * @param queryDTO 查询参数
     * @return 导出文件
     */
    // byte[] exportCheckins(CheckinPageQueryDTO queryDTO);
}
```

### 6.2 CheckinServiceImpl 实现类

```java
package com.perodua.aftersales.service.checkin.impl;

import cn.hutool.core.bean.BeanUtils;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.perodua.aftersales.convert.CheckinConverter;
import com.perodua.aftersales.dto.checkin.*;
import com.perodua.aftersales.entity.CheckinPO;
import com.perodua.aftersales.mapper.CheckinMapper;
import com.perodua.aftersales.service.checkin.CheckinService;
import com.perodua.aftersales.vo.checkin.*;
import com.perodua.common.constants.enums.ExceptionEnum;
import com.perodua.common.exception.BusinessException;
import com.perodua.common.utils.UserContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 到店登记业务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class CheckinServiceImpl extends ServiceImpl<CheckinMapper, CheckinPO> implements CheckinService {

    @Autowired
    private CheckinConverter checkinConverter;

    @Override
    public IPage<CheckinVO> pageCheckins(CheckinPageQueryDTO queryDTO) {
        // 构建分页对象
        Page<CheckinPO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 构建查询条件（包含权限控制）
        IPage<CheckinPO> poPage = super.lambdaQuery()
            .like(StrUtil.isNotBlank(queryDTO.getCheckinId()), CheckinPO::getCheckinId, queryDTO.getCheckinId())
            .like(StrUtil.isNotBlank(queryDTO.getLicensePlate()), CheckinPO::getLicensePlate, queryDTO.getLicensePlate())
            .like(StrUtil.isNotBlank(queryDTO.getRepairPersonName()), CheckinPO::getRepairPersonName, queryDTO.getRepairPersonName())
            .like(StrUtil.isNotBlank(queryDTO.getRepairPersonPhone()), CheckinPO::getRepairPersonPhone, queryDTO.getRepairPersonPhone())
            .ge(StrUtil.isNotBlank(queryDTO.getCreatedAtStart()), CheckinPO::getCreatedAt, queryDTO.getCreatedAtStart())
            .le(StrUtil.isNotBlank(queryDTO.getCreatedAtEnd()), CheckinPO::getCreatedAt, queryDTO.getCreatedAtEnd())
            // 权限控制：服务顾问只能查看自己创建的登记单
            .eq(isServiceAdvisor(), CheckinPO::getServiceAdvisorId, getCurrentUserId())
            .orderByDesc(CheckinPO::getCreatedAt)
            .page(page);

        // PO转VO
        return poPage.convert(checkinConverter::toVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckinCreateResponseVO createCheckin(CheckinCreateDTO createDTO) {
        // 业务校验
        validateCreateData(createDTO);

        // DTO转PO
        CheckinPO checkinPO = checkinConverter.toPO(createDTO);

        // 设置系统字段
        checkinPO.setCheckinId(generateCheckinId());
        checkinPO.setStoreId(getCurrentUserStoreId());
        checkinPO.setServiceAdvisorId(getCurrentUserId());
        checkinPO.setServiceAdvisorName(getCurrentUserName());
        checkinPO.setStatus("03020001"); // 正常状态

        // 保存到数据库
        boolean saved = super.save(checkinPO);
        if (!saved) {
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR, "保存登记单失败");
        }

        log.info("创建登记单成功，ID: {}, 登记编号: {}", checkinPO.getId(), checkinPO.getCheckinId());

        // 返回创建结果
        CheckinCreateResponseVO responseVO = new CheckinCreateResponseVO();
        responseVO.setId(checkinPO.getId());
        responseVO.setCheckinId(checkinPO.getCheckinId());
        return responseVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCheckin(Long id, CheckinUpdateDTO updateDTO) {
        // 查询现有记录
        CheckinPO existingPO = super.getById(id);
        if (existingPO == null) {
            throw new BusinessException(ExceptionEnum.DATA_NOT_FOUND_ERROR, "登记单不存在");
        }

        // 权限校验
        validateUpdatePermission(existingPO);

        // 业务校验
        validateUpdateData(existingPO, updateDTO);

        // DTO内容拷贝到PO（只拷贝非空值）
        BeanUtils.copyProperties(updateDTO, existingPO,
            CopyOptions.create().setIgnoreNullValue(true));

        // 更新到数据库
        boolean updated = super.updateById(existingPO);
        if (!updated) {
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR, "更新登记单失败");
        }

        log.info("更新登记单成功，ID: {}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCheckin(Long id) {
        // 查询现有记录
        CheckinPO existingPO = super.getById(id);
        if (existingPO == null) {
            throw new BusinessException(ExceptionEnum.DATA_NOT_FOUND_ERROR, "登记单不存在");
        }

        // 权限校验
        validateDeletePermission(existingPO);

        // 业务校验
        validateDeleteData(existingPO);

        // 修改状态为已取消（软删除）
        existingPO.setStatus("03020002"); // 已取消状态

        boolean updated = super.updateById(existingPO);
        if (!updated) {
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR, "取消登记单失败");
        }

        log.info("取消登记单成功，ID: {}", id);
    }

    @Override
    public CheckinDetailVO getCheckinDetail(Long id) {
        // 查询记录
        CheckinPO checkinPO = super.getById(id);
        if (checkinPO == null) {
            throw new BusinessException(ExceptionEnum.DATA_NOT_FOUND_ERROR, "登记单不存在");
        }

        // 权限校验
        validateViewPermission(checkinPO);

        // PO转VO
        return checkinConverter.toDetailVO(checkinPO);
    }

    /**
     * 校验新增数据
     */
    private void validateCreateData(CheckinCreateDTO createDTO) {
        // 校验同一车牌号当日是否已有未完成的登记单
        long count = super.lambdaQuery()
            .eq(CheckinPO::getLicensePlate, createDTO.getLicensePlate())
            .eq(CheckinPO::getStatus, "03020001") // 正常状态
            .ge(CheckinPO::getCreatedAt, getTodayStart())
            .le(CheckinPO::getCreatedAt, getTodayEnd())
            .count();

        if (count > 0) {
            throw new BusinessException("30010008", "同一车牌号当日已有未完成登记单");
        }
    }

    /**
     * 校验编辑权限
     */
    private void validateUpdatePermission(CheckinPO checkinPO) {
        // 权限控制：服务顾问只能编辑自己创建的登记单
        if (isServiceAdvisor() && !checkinPO.getServiceAdvisorId().equals(getCurrentUserId())) {
            throw new BusinessException("30010009", "无权限访问该登记单");
        }
    }

    /**
     * 校验编辑数据
     */
    private void validateUpdateData(CheckinPO existingPO, CheckinUpdateDTO updateDTO) {
        // 校验是否已关联工单
        if (existingPO.getRelatedRepairOrderId() != null) {
            throw new BusinessException("30010002", "登记单已关联工单，无法编辑");
        }
    }

    /**
     * 校验删除权限
     */
    private void validateDeletePermission(CheckinPO checkinPO) {
        // 权限控制：服务顾问只能删除自己创建的登记单
        if (isServiceAdvisor() && !checkinPO.getServiceAdvisorId().equals(getCurrentUserId())) {
            throw new BusinessException("30010009", "无权限访问该登记单");
        }
    }

    /**
     * 校验删除数据
     */
    private void validateDeleteData(CheckinPO existingPO) {
        // 校验是否已关联工单
        if (existingPO.getRelatedRepairOrderId() != null) {
            throw new BusinessException("30010003", "登记单已关联工单，无法删除");
        }
    }

    /**
     * 校验查看权限
     */
    private void validateViewPermission(CheckinPO checkinPO) {
        // 权限控制：服务顾问只能查看自己创建的登记单
        if (isServiceAdvisor() && !checkinPO.getServiceAdvisorId().equals(getCurrentUserId())) {
            throw new BusinessException("30010009", "无权限访问该登记单");
        }
    }

    /**
     * 生成登记单号
     * TODO: 实现具体的编号生成逻辑
     */
    private String generateCheckinId() {
        // 临时实现，实际应该根据门店简称+日期+流水号规则生成
        return "TEMP" + System.currentTimeMillis();
    }

    /**
     * 判断当前用户是否为服务顾问
     */
    private boolean isServiceAdvisor() {
        // TODO: 实现具体的角色判断逻辑
        return true; // 临时返回true
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        // TODO: 从用户上下文获取当前用户ID
        return 1L; // 临时返回
    }

    /**
     * 获取当前用户姓名
     */
    private String getCurrentUserName() {
        // TODO: 从用户上下文获取当前用户姓名
        return "临时用户"; // 临时返回
    }

    /**
     * 获取当前用户门店ID
     */
    private Long getCurrentUserStoreId() {
        // TODO: 从用户上下文获取当前用户门店ID
        return 1L; // 临时返回
    }

    /**
     * 获取今日开始时间
     */
    private Date getTodayStart() {
        // TODO: 实现获取今日开始时间的逻辑
        return new Date();
    }

    /**
     * 获取今日结束时间
     */
    private Date getTodayEnd() {
        // TODO: 实现获取今日结束时间的逻辑
        return new Date();
    }
}
```

---

## 7. 对象转换器设计 (Converter)

### 7.1 CheckinConverter 转换器

```java
package com.perodua.aftersales.convert;

import cn.hutool.core.bean.BeanUtils;
import cn.hutool.core.date.DateUtil;
import com.perodua.aftersales.dto.checkin.CheckinCreateDTO;
import com.perodua.aftersales.entity.CheckinPO;
import com.perodua.aftersales.vo.checkin.CheckinDetailVO;
import com.perodua.aftersales.vo.checkin.CheckinVO;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 到店登记转换器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
public class CheckinConverter {

    /**
     * DTO转PO
     */
    public CheckinPO toPO(CheckinCreateDTO dto) {
        CheckinPO po = new CheckinPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * PO转VO
     */
    public CheckinVO toVO(CheckinPO po) {
        CheckinVO vo = new CheckinVO();
        BeanUtils.copyProperties(po, vo);

        // 设置计算属性
        vo.setVehicleAge(calculateVehicleAge(po.getDeliveryTime()));

        return vo;
    }

    /**
     * PO转详情VO
     */
    public CheckinDetailVO toDetailVO(CheckinPO po) {
        CheckinDetailVO vo = new CheckinDetailVO();
        BeanUtils.copyProperties(po, vo);

        // 设置计算属性
        vo.setVehicleAge(calculateVehicleAge(po.getDeliveryTime()));

        return vo;
    }

    /**
     * 批量PO转VO
     */
    public List<CheckinVO> toVOList(List<CheckinPO> poList) {
        return poList.stream()
            .map(this::toVO)
            .collect(Collectors.toList());
    }

    /**
     * 计算车龄（月）
     */
    private Integer calculateVehicleAge(Date deliveryTime) {
        if (deliveryTime == null) {
            return null;
        }

        // 计算当前时间与交车时间的月份差
        Date now = new Date();
        long diffInMillis = now.getTime() - deliveryTime.getTime();
        long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);
        return (int) (diffInDays / 30); // 简化计算，实际应该更精确
    }


}
```

---

## 8. 控制器层设计 (Controller)

### 8.1 CheckinController 控制器

```java
package com.perodua.aftersales.controller.checkin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.perodua.aftersales.dto.checkin.*;
import com.perodua.aftersales.service.checkin.CheckinService;
import com.perodua.aftersales.vo.checkin.*;
import com.perodua.common.entity.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 到店登记控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/after-sales/checkins")
@Api(tags = "到店登记管理")
@Validated
public class CheckinController {

    @Autowired
    private CheckinService checkinService;

    @PostMapping("/page")
    @ApiOperation("分页查询登记单列表")
    public Result<IPage<CheckinVO>> pageCheckins(@Valid @RequestBody CheckinPageQueryDTO queryDTO) {
        log.info("分页查询登记单列表，参数: {}", queryDTO);
        IPage<CheckinVO> result = checkinService.pageCheckins(queryDTO);
        return Result.ok("查询成功", result);
    }

    @PostMapping
    @ApiOperation("新增登记单")
    public Result<CheckinCreateResponseVO> createCheckin(@Valid @RequestBody CheckinCreateDTO createDTO) {
        log.info("新增登记单，参数: {}", createDTO);
        CheckinCreateResponseVO result = checkinService.createCheckin(createDTO);
        return Result.ok("新增成功", result);
    }

    @PutMapping("/{id}")
    @ApiOperation("编辑登记单")
    public Result<Void> updateCheckin(
            @PathVariable @NotNull(message = "ID不能为空") @Min(value = 1, message = "ID必须大于0") Long id,
            @Valid @RequestBody CheckinUpdateDTO updateDTO) {
        log.info("编辑登记单，ID: {}, 参数: {}", id, updateDTO);
        checkinService.updateCheckin(id, updateDTO);
        return Result.ok("编辑成功");
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除登记单（取消）")
    public Result<Void> deleteCheckin(
            @PathVariable @NotNull(message = "ID不能为空") @Min(value = 1, message = "ID必须大于0") Long id) {
        log.info("删除登记单，ID: {}", id);
        checkinService.deleteCheckin(id);
        return Result.ok("取消成功");
    }

    @GetMapping("/{id}")
    @ApiOperation("查看登记单详情")
    public Result<CheckinDetailVO> getCheckinDetail(
            @PathVariable @NotNull(message = "ID不能为空") @Min(value = 1, message = "ID必须大于0") Long id) {
        log.info("查看登记单详情，ID: {}", id);
        CheckinDetailVO result = checkinService.getCheckinDetail(id);
        return Result.ok("查询成功", result);
    }

    @PostMapping("/{checkinId}/inspection-orders")
    @ApiOperation("创建环检单")
    public Result<Void> createInspectionOrder(
            @PathVariable @NotNull(message = "登记单ID不能为空") @Min(value = 1, message = "登记单ID必须大于0") Long checkinId) {
        log.info("创建环检单，登记单ID: {}", checkinId);

        // TODO: 等环检单功能开发设计时补上
        // InspectionOrderVO result = checkinService.createInspectionOrder(checkinId);
        // return Result.ok("环检单创建成功", result);

        return Result.ok("功能开发中，敬请期待");
    }

    @PostMapping("/export")
    @ApiOperation("导出登记单列表")
    public Result<Void> exportCheckins(@Valid @RequestBody CheckinPageQueryDTO queryDTO) {
        log.info("导出登记单列表，参数: {}", queryDTO);

        // TODO: 暂不实现，留待后续开发
        // byte[] excelData = checkinService.exportCheckins(queryDTO);
        // return ResponseEntity.ok()
        //     .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=checkins.xlsx")
        //     .contentType(MediaType.APPLICATION_OCTET_STREAM)
        //     .body(excelData);

        return Result.ok("导出功能开发中，敬请期待");
    }
}
```

---

## 9. 常量定义 (Constants)

### 9.1 CheckinConstants 常量类

```java
package com.perodua.aftersales.constants;

/**
 * 到店登记常量类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public class CheckinConstants {

    /**
     * 服务类型字典编码
     */
    public static final class ServiceType {
        /** 维修 */
        public static final String REPAIR = "03010001";
        /** 保养 */
        public static final String MAINTENANCE = "03010002";
        /** 检查 */
        public static final String INSPECTION = "03010003";
    }

    /**
     * 登记单状态字典编码
     */
    public static final class Status {
        /** 正常 */
        public static final String NORMAL = "03020001";
        /** 已取消 */
        public static final String CANCELLED = "03020002";
    }

    /**
     * 业务错误码
     */
    public static final class ErrorCode {
        /** 登记单不存在 */
        public static final String CHECKIN_NOT_FOUND = "30010001";
        /** 登记单已关联工单，无法编辑 */
        public static final String CHECKIN_LINKED_CANNOT_EDIT = "30010002";
        /** 登记单已关联工单，无法删除 */
        public static final String CHECKIN_LINKED_CANNOT_DELETE = "30010003";
        /** 车牌号格式错误 */
        public static final String LICENSE_PLATE_FORMAT_ERROR = "30010004";
        /** VIN码格式错误 */
        public static final String VIN_FORMAT_ERROR = "30010005";
        /** 手机号格式错误 */
        public static final String PHONE_FORMAT_ERROR = "30010006";
        /** 车辆信息不存在 */
        public static final String VEHICLE_NOT_FOUND = "30010007";
        /** 同一车牌号当日已有未完成登记单 */
        public static final String DUPLICATE_CHECKIN_TODAY = "30010008";
        /** 无权限访问该登记单 */
        public static final String NO_PERMISSION = "30010009";
        /** 服务类型不存在 */
        public static final String SERVICE_TYPE_NOT_FOUND = "30010010";
    }

    /**
     * 业务规则常量
     */
    public static final class BusinessRule {
        /** VIN码长度 */
        public static final int VIN_LENGTH = 17;
        /** 车牌号最大长度 */
        public static final int LICENSE_PLATE_MAX_LENGTH = 20;
        /** 手机号最大长度 */
        public static final int PHONE_MAX_LENGTH = 20;
        /** 备注最大长度 */
        public static final int NOTES_MAX_LENGTH = 1000;
        /** 里程数最大值 */
        public static final int MILEAGE_MAX_VALUE = 9999999;
    }
}
```

---

## 10. 异常处理设计

### 10.1 业务异常定义

根据后端代码通用组件规则，所有业务异常都使用 `BusinessException` 和 `ExceptionEnum`。

```java
// 在ExceptionEnum中新增到店登记相关的异常枚举
// 错误码格式：前4位(3001) + 后4位(业务自定义)

CHECKIN_NOT_FOUND("30010001", "登记单不存在"),
CHECKIN_LINKED_CANNOT_EDIT("30010002", "登记单已关联工单，无法编辑"),
CHECKIN_LINKED_CANNOT_DELETE("30010003", "登记单已关联工单，无法删除"),
LICENSE_PLATE_FORMAT_ERROR("30010004", "车牌号格式错误"),
VIN_FORMAT_ERROR("30010005", "VIN码格式错误"),
PHONE_FORMAT_ERROR("30010006", "手机号格式错误"),
VEHICLE_NOT_FOUND("30010007", "车辆信息不存在"),
DUPLICATE_CHECKIN_TODAY("30010008", "同一车牌号当日已有未完成登记单"),
NO_PERMISSION("30010009", "无权限访问该登记单"),
SERVICE_TYPE_NOT_FOUND("30010010", "服务类型不存在");
```

### 10.2 异常使用示例

```java
// 在Service中使用业务异常
if (checkinPO == null) {
    throw new BusinessException(ExceptionEnum.CHECKIN_NOT_FOUND);
}

// 使用自定义错误码和消息
if (existingPO.getRelatedRepairOrderId() != null) {
    throw new BusinessException("30010002", "登记单已关联工单，无法编辑");
}
```

---

## 11. 数据库操作规范

### 11.1 MyBatis Plus使用规范

严格遵循后端代码生成规则中的MyBatis Plus规范：

1. **强制使用MyBatis Plus基础能力**：所有单表操作使用继承的方法
2. **禁止手动编写单表SQL**：不为单表CRUD编写XML文件
3. **使用Lambda查询**：使用 `super.lambdaQuery()` 和 `super.lambdaUpdate()`
4. **自动处理逻辑删除**：不手动处理 `isDeleted` 字段

### 11.2 查询示例

```java
// 分页查询示例
IPage<CheckinPO> poPage = super.lambdaQuery()
    .like(StrUtil.isNotBlank(queryDTO.getCheckinId()), CheckinPO::getCheckinId, queryDTO.getCheckinId())
    .eq(CheckinPO::getStatus, "03020001")
    .orderByDesc(CheckinPO::getCreatedAt)
    .page(page);

// 条件更新示例
super.lambdaUpdate()
    .set(CheckinPO::getStatus, "03020002")
    .eq(CheckinPO::getId, id)
    .update();

// 条件查询示例
CheckinPO checkinPO = super.lambdaQuery()
    .eq(CheckinPO::getCheckinId, checkinId)
    .one();
```

---

## 12. 事务管理规范

### 12.1 事务注解使用

严格遵循后端代码生成规则中的事务管理规范：

```java
@Transactional(rollbackFor = Exception.class)
public CheckinCreateResponseVO createCheckin(CheckinCreateDTO createDTO) {
    // 事务方法实现
}
```

### 12.2 事务设计原则

1. **事务粒度控制**：事务足够小，足够快
2. **异常回滚配置**：使用 `rollbackFor = Exception.class`
3. **避免长事务**：不在事务内调用缓慢的外部服务
4. **单操作判断**：单条数据操作要判断执行结果

---

## 13. 性能优化设计

### 13.1 查询优化

1. **索引使用**：充分利用P02中设计的索引
2. **分页查询**：使用MyBatis Plus的Page对象
3. **字段选择**：列表查询只返回必要字段

### 13.2 缓存策略

1. **用户信息缓存**：当前用户信息缓存
2. **查询结果缓存**：热点数据查询结果缓存

---

## 14. 日志记录规范

### 14.1 日志级别使用

```java
// 业务操作日志
log.info("创建登记单成功，ID: {}, 登记编号: {}", checkinPO.getId(), checkinPO.getCheckinId());

// 异常日志
log.error("创建登记单失败，参数: {}", createDTO, e);

// 调试日志
log.debug("查询条件: {}", queryDTO);
```

### 14.2 日志内容规范

1. **操作日志**：记录关键业务操作的成功和失败
2. **参数日志**：记录重要方法的输入参数
3. **异常日志**：记录异常信息和上下文
4. **性能日志**：记录耗时较长的操作

---

## 自检清单确认

- [x] 严格遵循后端代码规范和架构设计原则
- [x] 实体类使用PO后缀，继承BasePO，使用MyBatis Plus注解
- [x] DTO/VO设计符合对象约束规范，使用Hutool BeanUtils转换
- [x] Service层继承IService，使用Lambda查询，包含完整的业务逻辑和校验
- [x] Controller层使用统一响应格式，包含完整的参数校验和Swagger注解
- [x] 异常处理使用BusinessException和ExceptionEnum
- [x] 事务管理遵循规范，使用正确的注解配置
- [x] 包结构符合项目规范，命名遵循标准
- [x] 权限控制通过Service层查询条件实现
- [x] 预留TODO项目，为后续功能扩展做准备
- [x] 包含完整的常量定义和错误码设计
- [x] 代码注释完整，符合JavaDoc规范
```
```
