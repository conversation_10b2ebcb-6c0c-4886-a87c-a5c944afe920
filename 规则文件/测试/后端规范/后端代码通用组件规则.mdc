---
description: 
globs: 
alwaysApply: true
---
# 后端代码通用组件规则

## 概述
本规则基于 `dms-service-common` 项目分析，定义了后端开发中必须遵循的通用组件使用规范。该通用组件包提供了完整的基础设施支持，确保项目的一致性、可维护性和标准化。

## 技术栈依赖
- **Spring Boot**: 2.2.2.RELEASE
- **Spring Cloud**: Hoxton.SR1
- **MyBatis Plus**: ********
- **MySQL**: 8.0.19
- **Java**: 1.8
- **Lombok**: 自动生成样板代码
- **Swagger**: API文档生成

## 核心组件能力

### 1. 统一响应处理体系

#### 1.1 Result<T> 统一响应类
**位置**: `com.perodua.common.entity.base.Result`

**核心特性**:
- 标准化API响应格式
- 自动链路追踪ID注入
- 时间戳自动生成
- 支持泛型数据类型

**字段结构**:
```java
public class Result<T> {
    private boolean success;      // 成功标志
    private String message;       // 返回消息
    private String code;          // 返回代码
    private String traceId;       // 链路追踪ID
    private T result;             // 返回数据对象
    private long timestamp;       // 时间戳
}
```

**使用规范**:
```java
// 成功响应（无数据）
return Result.ok();

// 成功响应（带数据）
return Result.ok(data);

// 成功响应（带消息和数据）
return Result.ok("操作成功", data);

// 失败响应（使用异常枚举）
return Result.fail(ExceptionEnum.USER_NOT_FOUND);

// 失败响应（自定义错误码和消息）
return Result.fail("11001001", "用户不存在");
```

#### 1.2 ResponseBodyAdvice 响应拦截器
**位置**: `com.perodua.common.handler.ResponseBodyAdvice`

**功能**:
- 自动包装Controller返回值为Result格式
- 跳过已经是Result类型的返回值
- 支持多语言消息处理
- 自动注入链路追踪ID

**生效条件**:
- 返回类型不是Result
- 返回类型不是ResponseEntity
- 返回类型不是Spring框架类型

### 2. 异常处理体系

#### 2.1 ExceptionEnum 异常枚举
**位置**: `com.perodua.common.constants.enums.ExceptionEnum`

**错误码体系（4+4模式）**：
- **总长度**：8位数字字符串
- **前4位**：通用+业务系统标识（平台统一分配）
- **后4位**：业务系统自定义异常码（业务系统自行维护）

**前4位分配建议**：
| 系统名称         | 标识（前4位） |
|------------------|:------------:|
| 通用             | 1000         |
| 销售系统         | 2000         |
| 售后工单系统     | 2100         |
| 售后零件系统     | 2200         |
| 主数据系统       | 2300         |
| 主记录系统       | 2400         |
| 认证中心         | 2500         |
| 基础服务系统     | 2600         |

**常用异常枚举示例**：
```java
// 通用
SUCCESS("10000000", "成功", 200),
SYSTEM_ERROR("10000001", "系统异常", 500),
PARAM_INVALID_ERROR("10000002", "参数无效", 400),
UNAUTHORIZED_ERROR("10000003", "未授权", 403),
DATA_NOT_FOUND_ERROR("10000004", "数据不存在", 404),

// 销售系统
ORDER_NOT_FOUND("********", "订单不存在", 404),
ORDER_CANCELLED("20000002", "订单已取消", 400),
ORDER_AMOUNT_ERROR("20000003", "订单金额错误", 400),

// 售后工单系统
WORK_ORDER_NOT_FOUND("21000001", "工单不存在", 404),
WORK_ORDER_CLOSED("21000002", "工单已关闭", 400),
WORK_ORDER_STATUS_INVALID("21000003", "工单状态无效", 400),

// 售后零件系统
PART_NOT_FOUND("22000001", "零件不存在", 404),
PART_STOCK_LACK("********", "零件库存不足", 400),
PART_ALREADY_USED("********", "零件已被使用", 400),

// 主数据系统
MASTER_DATA_NOT_FOUND("********", "主数据不存在", 404),
MASTER_DATA_CONFLICT("********", "主数据冲突", 400),

// 主记录系统
RECORD_NOT_FOUND("********", "主记录不存在", 404),
RECORD_STATUS_INVALID("********", "主记录状态无效", 400),

// 认证中心
TOKEN_EXPIRED("********", "token失效", 401),
USER_NOT_LOGIN("********", "用户未登录", 401),
USER_ACCOUNT_LOCKED("********", "用户账号被锁定", 403),

// 基础服务系统
SERVICE_UNAVAILABLE("********", "服务不可用", 503),
SERVICE_TIMEOUT("********", "服务超时", 504);
```

#### 2.2 BusinessException 业务异常类
**位置**: `com.perodua.common.exception.BusinessException`

**使用规范**:
```java
// 使用异常枚举
throw new BusinessException(ExceptionEnum.DATA_NOT_FOUND_ERROR);

// 使用自定义错误码和消息
throw new BusinessException("********", "订单不存在");

// 使用静态工厂方法
throw BusinessException.of(ExceptionEnum.PARAM_INVALID_ERROR);

// 带原因异常
throw new BusinessException(ExceptionEnum.SYSTEM_ERROR, originalException);
```

#### 2.3 GlobalExceptionHandler 全局异常处理器
**位置**: `com.perodua.common.handler.GlobalExceptionHandler`

**处理异常类型**:
- BusinessException: 业务异常
- MethodArgumentNotValidException: 参数校验异常
- BindException: 参数绑定异常
- ConstraintViolationException: 约束违反异常
- SQLException: 数据库异常
- NullPointerException: 空指针异常
- IllegalArgumentException: 非法参数异常
- Exception: 其他未知异常

**特性**:
- 自动多语言错误消息处理
- 链路追踪ID自动注入
- HTTP状态码自动映射
- 详细错误日志记录

### 3. 多语言支持体系

#### 3.1 I18nUtils 多语言工具类
**位置**: `com.perodua.common.util.I18nUtils`

**功能**:
- 自动检测当前请求语言
- 支持参数化消息
- 错误消息多语言处理
- 字段名称多语言处理

**语言支持**:
- zh_CN: 中文（默认）
- en_US: 英文
- ms_MY: 马来文

**使用方法**:
```java
// 获取当前语言的消息
String message = I18nUtils.getMessage("user.not.found");

// 获取指定语言的消息
String message = I18nUtils.getMessage("user.not.found", "en_US");

// 获取错误消息
String errorMsg = I18nUtils.getErrorMessage("11001001");

// 获取字段消息
String fieldMsg = I18nUtils.getFieldMessage("username");
```

#### 3.2 I18nService 多语言服务
**位置**: `com.perodua.common.service.I18nService`

**功能**:
- 消息键值对管理
- 参数化消息处理
- 错误消息处理
- 字段消息处理

### 4. 数据库基础设施

#### 4.1 BasePO 基础实体类
**位置**: `com.perodua.common.entity.po.base.BasePO`

**标准字段**:
```java
public abstract class BasePO<T extends Model<T>> extends Model<T> {
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;                    // 主键ID（自增）
    
    @TableLogic
    @TableField(value = "is_deleted", select = false)
    protected Boolean isDeleted;          // 逻辑删除标识（false-未删除，true-已删除）
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    protected Date createdAt;             // 创建时间
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    protected Date updatedAt;             // 更新时间
    
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    protected String createdBy;           // 创建人
    
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    protected String updatedBy;           // 更新人
}
```

**使用规范**:
```java
@Data
@TableName("user")
@EqualsAndHashCode(callSuper = true)
public class User extends BasePO<User> {
    
    @TableField("username")
    private String username;
    
    @TableField("email")
    private String email;
    
    // 注意：不需要重复定义id、createdAt、updatedAt等字段
}
```

#### 4.2 MyMetaObjectHandler 自动填充处理器
**位置**: `com.perodua.common.handler.MyMetaObjectHandler`

**自动填充规则**:
- **新增时自动填充**:
  - createdAt: 当前时间（Date类型）
  - updatedAt: 当前时间（Date类型）
  - createdBy: "-1"（String类型，默认值）
  - updatedBy: "-1"（String类型，默认值）
  - isDeleted: 0（Integer类型，未删除）

- **更新时自动填充**:
  - updatedAt: 当前时间（Date类型）
  - updatedBy: "-1"（String类型，默认值）

**注意**: 虽然BasePO中isDeleted定义为Boolean类型，但MyMetaObjectHandler实际填充的是Integer类型的0值，这可能需要在实际使用中注意类型转换。

### 5. 链路追踪支持

#### 5.1 TraceIdResponseFilter 链路追踪过滤器
**位置**: `com.perodua.common.interceptor.TraceIdResponseFilter`

**功能**:
- 自动从MDC获取traceId和spanId
- 将追踪ID添加到响应头
- 支持分布式链路追踪

### 6. 工具类支持

#### 6.1 ApplicationContextHelper Spring上下文助手
**位置**: `com.perodua.common.util.ApplicationContextHelper`

**功能**:
- 静态方法获取Spring Bean
- 支持按名称和类型获取Bean
- 提供Bean类型和别名查询

**使用方法**:
```java
// 按类型获取Bean
UserService userService = ApplicationContextHelper.getBean(UserService.class);

// 按名称获取Bean
Object bean = ApplicationContextHelper.getBean("userService");

// 按名称和类型获取Bean
UserService userService = ApplicationContextHelper.getBean("userService", UserService.class);
```

### 7. 分布式锁支持

#### 7.1 @DistributedLock 分布式锁注解
**位置**: `com.perodua.common.annotation.DistributedLock`
**切面处理器**: `com.perodua.common.aop.DistributedLockAspect`

**功能特性**:
- 基于Redisson实现的分布式锁
- 支持SPEL表达式动态构建锁KEY
- 自动锁获取与释放
- 可配置等待时间和租期
- 防止锁泄露，确保线程安全

**注解参数**:
```java
public @interface DistributedLock {
    String[] key();                          // 锁KEY，支持SPEL表达式
    String prefix();                         // 业务前缀
    String delimiter() default "";           // 分隔符
    int waitTime() default 3000;             // 获取锁等待时间（毫秒）
    int timeout() default 10000;             // 锁超时时间（毫秒）
    TimeUnit timeUnit() default TimeUnit.MILLISECONDS; // 时间单位
}
```

**使用规范**:
```java
@Service
public class OrderServiceImpl implements OrderService {
    
    // 基础用法：单一参数锁
    @DistributedLock(prefix = "order", key = {"#orderId"})
    public void processOrder(Long orderId) {
        // 业务逻辑，同一订单ID同时只能有一个线程处理
    }
    
    // 复合锁：多参数组合
    @DistributedLock(
        prefix = "user:order", 
        key = {"#userId", "#orderId"}, 
        delimiter = ":"
    )
    public void processUserOrder(Long userId, Long orderId) {
        // 锁KEY格式：user:order:userId:orderId
    }
    
    // 复杂对象属性锁
    @DistributedLock(
        prefix = "order:payment", 
        key = {"#orderDTO.orderId", "#orderDTO.paymentType"},
        waitTime = 5000,
        timeout = 30000
    )
    public void payOrder(OrderPaymentDTO orderDTO) {
        // 锁KEY格式：order:payment:orderId:paymentType
    }
    
    // 用户级别操作锁
    @DistributedLock(
        prefix = "user:profile", 
        key = {"#user.id"},
        waitTime = 1000,
        timeout = 5000,
        timeUnit = TimeUnit.MILLISECONDS
    )
    public void updateUserProfile(User user) {
        // 同一用户的资料修改操作互斥
    }
}
```

**SPEL表达式支持**:
```java
// 直接参数
@DistributedLock(prefix = "simple", key = {"#id"})
public void method(Long id) {}

// 对象属性
@DistributedLock(prefix = "object", key = {"#user.id", "#user.type"})
public void method(User user) {}

// 集合元素
@DistributedLock(prefix = "list", key = {"#userList[0].id"})
public void method(List<User> userList) {}

// 方法调用
@DistributedLock(prefix = "method", key = {"#user.getId()", "#user.getType()"})
public void method(User user) {}
```

**异常处理**:
- 获取锁失败时抛出`DistributedLockException`异常
- 异常消息："数据处理中."
- 建议在Controller层捕获并返回友好提示

### 8. 幂等性支持

#### 8.1 @Idempotent 幂等注解
**位置**: `com.perodua.common.annotation.Idempotent`
**切面处理器**: `com.perodua.common.aop.IdempotentAspect`

**功能特性**:
- 防止重复提交和重复处理
- 支持Redis和自定义两种策略
- 支持SPEL表达式动态构建幂等KEY
- 业务异常时自动清理幂等标识
- 支持自定义幂等逻辑实现

**注解参数**:
```java
public @interface Idempotent {
    String[] key();                          // 幂等KEY，支持SPEL表达式
    String prefix();                         // 业务前缀
    String delimiter() default "";           // 分隔符
    String strategy() default "redis";       // 策略：redis/custom
    int timeout() default 3600;             // 超时时间（秒）
    TimeUnit timeUnit() default TimeUnit.SECONDS; // 时间单位
    String method() default "";              // 自定义方法名
    Class<?> classz() default Object.class; // 自定义实现类
}
```

**Redis策略使用**:
```java
@Service
public class PaymentServiceImpl implements PaymentService {
    
    // 基础幂等：防止重复支付
    @Idempotent(
        prefix = "payment", 
        key = {"#paymentRequest.orderId", "#paymentRequest.amount"},
        timeout = 300
    )
    public PaymentResult processPayment(PaymentRequest paymentRequest) {
        // 同一订单相同金额的支付请求在5分钟内只能处理一次
        return paymentGateway.pay(paymentRequest);
    }
    
    // 用户操作幂等
    @Idempotent(
        prefix = "user:action", 
        key = {"#userId", "#actionType"},
        delimiter = ":",
        timeout = 60
    )
    public void userAction(Long userId, String actionType) {
        // 同一用户相同操作1分钟内只能执行一次
    }
    
    // 复杂对象幂等
    @Idempotent(
        prefix = "order:create", 
        key = {"#orderCreateDTO.userId", "#orderCreateDTO.productId", "#orderCreateDTO.quantity"},
        timeout = 1800
    )
    public OrderVO createOrder(OrderCreateDTO orderCreateDTO) {
        // 防止用户重复创建相同订单
        return orderService.create(orderCreateDTO);
    }
}
```

**自定义策略使用**:
```java
// 1. 实现自定义幂等逻辑
@Component
public class CustomIdempotentService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    // 方法签名必须与原方法一致
    public Boolean checkOrderIdempotent(OrderCreateDTO orderCreateDTO) {
        // 检查数据库中是否已存在相同订单
        QueryWrapper<Order> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", orderCreateDTO.getUserId())
               .eq("product_id", orderCreateDTO.getProductId())
               .eq("status", "PENDING");
        
        Order existingOrder = orderMapper.selectOne(wrapper);
        return existingOrder == null; // 返回true表示可以执行，false表示已存在
    }
}

// 2. 使用自定义策略
@Service
public class OrderServiceImpl implements OrderService {
    
    @Idempotent(
        prefix = "order:custom", 
        key = {"#orderCreateDTO.userId", "#orderCreateDTO.productId"},
        strategy = "custom",
        method = "checkOrderIdempotent",
        classz = CustomIdempotentService.class
    )
    public OrderVO createOrder(OrderCreateDTO orderCreateDTO) {
        // 使用自定义数据库检查逻辑
        return this.doCreateOrder(orderCreateDTO);
    }
}
```

**异常处理**:
- 幂等检查失败时抛出`IdempotentException`异常
- 异常消息："数据已处理."
- 业务异常时自动清理Redis中的幂等标识

### 9. 业务编号生成

#### 9.1 BusinessCodeService 业务编号生成服务
**位置**: `com.perodua.common.ruleService.impl.BusinessCodeService`
**核心接口**: `com.perodua.common.ruleService.rule.ICodeService`
**参数类型**: `com.perodua.common.entity.dto.rule.RuleCodeVo`

**功能特性**:
- 基于规则链模式的编号生成框架
- 支持可配置的规则类型和参数
- 统一的业务单号生成入口
- 支持动态规则配置和刷新
- 确保编号的唯一性和可追溯性

**核心类结构**:
```java
// RuleCodeVo 参数对象
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RuleCodeVo {
    private Map<String, Object> paramMap;  // 参数映射
    private String ruleKey;                // 规则键值
}

// BusinessCodeService 业务编号服务
@Service
public class BusinessCodeService extends AbsRuleChainService {
    public String getCode(RuleCodeVo ruleCodeVo) {
        // 基于规则链获取对应的编码服务
        ICodeService<RuleCodeVo, String> codeService = 
            this.getRuleService(this.getRuleTypeEnum(this.getRuleTypeByKey(ruleCodeVo.getRuleKey())));
        return codeService.getCode(ruleCodeVo);
    }
}
```

**规则常量定义**:
```java
public final class RuleContant {
    public static final String DMS_CODE = "dms_code";    // DMS系统编号规则
    public static final String FIX = "FIX";              // 固定开头
    public static final String TIMES = "TIMES";          // 时间
    public static final String RANDOM = "RANDOM";        // 随机数
    public static final String AUTOINCR = "AUTOINCR";    // 自增序号
    // ... 其他常量
}
```

**标准使用方式**:
```java
@Service
public class OrderServiceImpl implements OrderService {
    
    @Autowired
    private BusinessCodeService businessCodeService;
    
    public OrderVO createOrder(OrderCreateDTO dto) {
        // 构建编号生成参数
        RuleCodeVo vo = RuleCodeVo.builder()
            .ruleKey(RuleContant.DMS_CODE)
            .paramMap(new HashMap<String, Object>() {
                {
                    put("prefix", "ORD");           // 前缀
                    put("dealer", dealerCode);      // 经销商代码
                    put("businessType", "ONLINE");  // 业务类型
                }
            }).build();
        
        // 生成业务编号
        String orderCode = businessCodeService.getCode(vo);
        
        // 创建订单...
        Order order = new Order();
        order.setOrderCode(orderCode);
        // ... 其他业务逻辑
        
        return convertToVO(order);
    }
}
```

**常用业务场景示例**:
```java
@Service
public class BusinessCodeServiceImpl implements BusinessCodeService {
    
    @Autowired
    private BusinessCodeService businessCodeService;
    
    // 1. 订单编号生成
    public String generateOrderCode(String dealerCode, String businessType) {
        RuleCodeVo vo = RuleCodeVo.builder()
            .ruleKey(RuleContant.DMS_CODE)
            .paramMap(new HashMap<String, Object>() {
                {
                    put("prefix", "ORD");
                    put("dealer", dealerCode);
                    put("businessType", businessType);
                }
            }).build();
        return businessCodeService.getCode(vo);
    }
    
    // 2. 工单编号生成
    public String generateWorkOrderCode(String dealerCode, String workType) {
        RuleCodeVo vo = RuleCodeVo.builder()
            .ruleKey(RuleContant.DMS_CODE)
            .paramMap(new HashMap<String, Object>() {
                {
                    put("prefix", "WO");
                    put("dealer", dealerCode);
                    put("workType", workType);
                }
            }).build();
        return businessCodeService.getCode(vo);
    }
    
    // 3. 支付流水号生成
    public String generatePaymentCode(String dealerCode, String paymentMethod) {
        RuleCodeVo vo = RuleCodeVo.builder()
            .ruleKey(RuleContant.DMS_CODE)
            .paramMap(new HashMap<String, Object>() {
                {
                    put("prefix", "PAY");
                    put("dealer", dealerCode);
                    put("paymentMethod", paymentMethod);
                }
            }).build();
        return businessCodeService.getCode(vo);
    }
    
    // 4. 客户编号生成
    public String generateCustomerCode(String dealerCode, String customerType) {
        RuleCodeVo vo = RuleCodeVo.builder()
            .ruleKey(RuleContant.DMS_CODE)
            .paramMap(new HashMap<String, Object>() {
                {
                    put("prefix", "CUS");
                    put("dealer", dealerCode);
                    put("customerType", customerType);
                }
            }).build();
        return businessCodeService.getCode(vo);
    }
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| ruleKey | String | ✅ | 规则键值，指定使用哪个编码规则 | RuleContant.DMS_CODE |
| prefix | String | ✅ | 业务前缀，标识业务类型 | "ORD", "WO", "PAY" |
| dealer | String | ✅ | 经销商代码，用于区分不同经销商 | "D001", "DEALER_KL" |
| businessType | String | ⭕ | 业务类型，细分业务场景 | "ONLINE", "OFFLINE" |
| workType | String | ⭕ | 工作类型，用于工单等场景 | "REPAIR", "MAINTENANCE" |
| paymentMethod | String | ⭕ | 支付方式，用于支付场景 | "CASH", "CARD", "TRANSFER" |
| customerType | String | ⭕ | 客户类型，用于客户编号 | "INDIVIDUAL", "CORPORATE" |

**与注解结合使用**:
```java
@Service
public class OrderServiceImpl implements OrderService {
    
    @Autowired
    private BusinessCodeService businessCodeService;
    
    // 结合幂等注解使用
    @Idempotent(
        prefix = "order:create", 
        key = {"#dto.userId", "#dto.productId"},
        timeout = 600
    )
    public OrderVO createOrder(OrderCreateDTO dto) {
        // 生成订单编号
        String orderCode = generateOrderCode(dto.getDealerCode(), "ONLINE");
        
        // 创建订单业务逻辑...
        return orderVO;
    }
    
    // 结合分布式锁使用
    @DistributedLock(
        prefix = "code:generate", 
        key = {"#dealerCode", "#businessType"}
    )
    public String generateOrderCode(String dealerCode, String businessType) {
        RuleCodeVo vo = RuleCodeVo.builder()
            .ruleKey(RuleContant.DMS_CODE)
            .paramMap(new HashMap<String, Object>() {
                {
                    put("prefix", "ORD");
                    put("dealer", dealerCode);
                    put("businessType", businessType);
                }
            }).build();
        return businessCodeService.getCode(vo);
    }
}
```

**规则配置示例**:
```java
// 编号规则配置（示例）
// 通常通过数据库配置，此处仅展示概念
public class CodeRuleConfig {
    // 规则：ORD + 经销商代码 + YYYYMMDD + 4位序号
    // 示例结果：ORDD00120231120001
    
    public static final String ORDER_RULE = "ORDER_CODE_RULE";
    
    // 规则组件：
    // 1. 固定前缀：ORD
    // 2. 参数引用：dealer
    // 3. 时间组件：YYYYMMDD
    // 4. 自增序号：4位数字
}
```

**最佳实践**:
```java
@Service
public class ComplexBusinessServiceImpl implements ComplexBusinessService {
    
    @Autowired
    private BusinessCodeService businessCodeService;
    
    // 1. 标准编号生成方法
    private String generateBusinessCode(String prefix, String dealerCode, Map<String, Object> extraParams) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("prefix", prefix);
        paramMap.put("dealer", dealerCode);
        if (extraParams != null) {
            paramMap.putAll(extraParams);
        }
        
        RuleCodeVo vo = RuleCodeVo.builder()
            .ruleKey(RuleContant.DMS_CODE)
            .paramMap(paramMap)
            .build();
            
        return businessCodeService.getCode(vo);
    }
    
    // 2. 业务方法使用
    @Idempotent(prefix = "payment:create", key = {"#request.orderId"})
    public PaymentVO createPayment(PaymentCreateDTO request) {
        // 生成支付编号
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("paymentMethod", request.getPaymentMethod());
        
        String paymentCode = generateBusinessCode("PAY", request.getDealerCode(), extraParams);
        
        // 支付业务逻辑...
        Payment payment = new Payment();
        payment.setPaymentCode(paymentCode);
        // ...
        
        return convertToVO(payment);
    }
    
    // 3. 批量编号生成
    @DistributedLock(prefix = "batch:code", key = {"#prefix", "#dealerCode"})
    public List<String> generateBatchCodes(String prefix, String dealerCode, int count) {
        List<String> codes = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            String code = generateBusinessCode(prefix, dealerCode, null);
            codes.add(code);
        }
        return codes;
    }
}
```

**异常处理**:
```java
@Service
public class CodeServiceImpl implements CodeService {
    
    @Autowired
    private BusinessCodeService businessCodeService;
    
    public String generateCodeSafely(String prefix, String dealerCode) {
        try {
            RuleCodeVo vo = RuleCodeVo.builder()
                .ruleKey(RuleContant.DMS_CODE)
                .paramMap(new HashMap<String, Object>() {
                    {
                        put("prefix", prefix);
                        put("dealer", dealerCode);
                    }
                }).build();
            
            return businessCodeService.getCode(vo);
        } catch (RuntimeException e) {
            log.error("生成业务编号失败, prefix: {}, dealer: {}", prefix, dealerCode, e);
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR, "编号生成失败");
        }
    }
}
```

**规则类型说明**:
- **静态类型 (FINAL_TYPE = 1)**: 固定字符串
- **自增类型 (AUTOINCR_TYPE = 2)**: 自动递增序号
- **时间类型 (DATE_TYPE = 3)**: 日期时间格式
- **随机类型 (RANDOM_TYPE = 4)**: 随机数字或字符
- **引用类型 (CITE_TYPE = 5)**: 引用其他规则结果
- **传参类型 (PARAM_TYPE = 6)**: 使用传入的参数值

## 强制使用规范

### 1. 响应格式规范
- ✅ **强制使用**: 所有Controller方法必须返回Result<T>格式
- ✅ **自动包装**: 普通返回值会被ResponseBodyAdvice自动包装
- 🚫 **禁止**: 直接返回原始数据类型或Map

### 2. 异常处理规范
- ✅ **强制使用**: 所有业务异常必须使用BusinessException
- ✅ **强制使用**: 错误码必须使用ExceptionEnum中定义的枚举
- 🚫 **禁止**: 抛出原始Exception或自定义异常类
- 🚫 **禁止**: 重复实现GlobalExceptionHandler

### 3. 实体类规范
- ✅ **强制继承**: 所有数据库实体类必须继承BasePO
- ✅ **自动填充**: 依赖MyMetaObjectHandler自动填充基础字段
- 🚫 **禁止**: 手动处理isDeleted字段（MyBatis Plus自动处理）
- 🚫 **禁止**: 重复定义基础字段（id、createdAt等）

### 4. 多语言规范
- ✅ **强制使用**: 所有用户可见消息必须支持多语言
- ✅ **强制使用**: 使用I18nUtils获取多语言消息
- 🚫 **禁止**: 硬编码中文消息

### 5. 常量使用规范
- ✅ **强制使用**: 所有状态值、类型值必须使用常量或枚举
- 🚫 **禁止**: 出现魔法数字或硬编码字符串

### 6. 分布式锁使用规范
- ✅ **强制使用**: 涉及并发访问共享资源的方法必须使用@DistributedLock
- ✅ **强制使用**: 锁的KEY必须具有业务意义，使用有意义的前缀
- ✅ **强制配置**: 必须配置合理的waitTime和timeout参数
- 🚫 **禁止**: 使用过长的锁超时时间（建议不超过30秒）
- 🚫 **禁止**: 在锁内执行耗时的外部调用（如HTTP请求）

### 7. 幂等性使用规范
- ✅ **强制使用**: 所有可能重复提交的业务操作必须使用@Idempotent
- ✅ **强制使用**: 支付、订单创建等关键业务必须实现幂等
- ✅ **推荐使用**: 优先使用Redis策略，复杂场景使用自定义策略
- 🚫 **禁止**: 在幂等方法内执行非幂等的外部操作
- 🚫 **禁止**: 使用过短的超时时间（建议最少60秒）

### 8. 业务编号生成规范
- ✅ **强制使用**: 所有业务单号必须通过BusinessCodeService.getCode()方法生成
- ✅ **强制使用**: 必须使用RuleCodeVo作为参数对象
- ✅ **强制配置**: ruleKey必须使用RuleContant.DMS_CODE或其他预定义常量
- ✅ **强制规范**: paramMap中必须包含prefix（业务前缀）和dealer（经销商代码）参数
- ✅ **强制要求**: 必须确保编号的唯一性和可追溯性
- 🚫 **禁止**: 使用数据库自增ID作为业务编号
- 🚫 **禁止**: 在应用层拼接随机字符串作为业务编号
- 🚫 **禁止**: 直接实现ICodeService接口而不使用规则链框架

## 依赖引入规范

### Maven依赖配置
```xml
<dependency>
    <groupId>com.perodua</groupId>
    <artifactId>dms-service-common</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 包扫描配置
```java
@SpringBootApplication(scanBasePackages = {
    "com.perodua.common",  // 扫描通用组件包
    "com.example.project"  // 扫描项目包
})
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

## 配置要求

### 1. MyBatis Plus配置
```yaml
mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: isDeleted  # 逻辑删除字段
      logic-delete-value: 1          # 删除值
      logic-not-delete-value: 0      # 未删除值
  configuration:
    map-underscore-to-camel-case: true
```

### 2. 多语言配置
```yaml
spring:
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600
```

### 3. Redis配置（分布式锁和幂等性必需）
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# Redisson配置
redisson:
  codec: org.redisson.codec.JsonJacksonCodec
  threads: 16
  nettyThreads: 32
  singleServerConfig:
    address: "redis://localhost:6379"
    password: 
    database: 0
    connectionMinimumIdleSize: 8
    connectionPoolSize: 32
    dnsMonitoringInterval: 5000
```

## 最佳实践

### 1. 错误处理最佳实践
```java
@Service
public class UserServiceImpl implements UserService {
    
    public UserVO getUserById(Long id) {
        // 参数校验
        if (id == null || id <= 0) {
            throw new BusinessException(ExceptionEnum.PARAM_INVALID_ERROR, "用户ID不能为空");
        }
        
        // 查询用户
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException(ExceptionEnum.DATA_NOT_FOUND_ERROR, "用户不存在");
        }
        
        // 转换并返回
        return UserConvert.INSTANCE.toVO(user);
    }
}
```

### 2. 多语言使用最佳实践
```java
@RestController
public class UserController {
    
    @PostMapping("/user/create")
    public Result<UserVO> createUser(@RequestBody @Valid UserCreateDTO dto) {
        UserVO userVO = userService.createUser(dto);
        
        // 使用多语言成功消息
        String successMsg = I18nUtils.getMessage("user.create.success");
        return Result.ok(successMsg, userVO);
    }
}
```

### 3. 实体类定义最佳实践
```java
@Data
@TableName("sys_user")
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "用户实体")
public class User extends BasePO<User> {
    
    @ApiModelProperty("用户名")
    @TableField("username")
    private String username;
    
    @ApiModelProperty("邮箱")
    @TableField("email")
    private String email;
    
    @ApiModelProperty("状态")
    @TableField("status")
    private Integer status;
    
    // 状态常量
    public static final Integer STATUS_ACTIVE = 1;
    public static final Integer STATUS_INACTIVE = 0;
}
```

### 4. 分布式锁最佳实践
```java
@Service
public class OrderServiceImpl implements OrderService {
    
    // 正确示例：细粒度锁
    @DistributedLock(
        prefix = "order:process", 
        key = {"#orderId"}, 
        waitTime = 5000, 
        timeout = 10000
    )
    public void processOrder(Long orderId) {
        // 业务逻辑：订单处理
    }
    
    // 错误示例：锁粒度过粗
    // @DistributedLock(prefix = "order", key = {}) // 不要这样做
    
    // 正确示例：复合业务锁
    @DistributedLock(
        prefix = "inventory:deduct", 
        key = {"#productId", "#warehouseId"}, 
        delimiter = ":",
        waitTime = 3000,
        timeout = 8000
    )
    public void deductInventory(Long productId, Long warehouseId, Integer quantity) {
        // 库存扣减业务
    }
}
```

### 5. 幂等性最佳实践
```java
@Service
public class PaymentServiceImpl implements PaymentService {
    
    // 正确示例：关键业务幂等
    @Idempotent(
        prefix = "payment:process", 
        key = {"#request.orderId", "#request.amount", "#request.paymentMethod"},
        delimiter = ":",
        timeout = 1800  // 30分钟
    )
    public PaymentResult processPayment(PaymentRequest request) {
        // 支付处理逻辑
        return doPayment(request);
    }
    
    // 正确示例：自定义幂等逻辑
    @Idempotent(
        prefix = "refund:custom", 
        key = {"#refundRequest.orderId"},
        strategy = "custom",
        method = "checkRefundIdempotent",
        classz = RefundIdempotentService.class
    )
    public RefundResult processRefund(RefundRequest refundRequest) {
        // 退款处理逻辑
        return doRefund(refundRequest);
    }
}
```

### 6. 业务编号生成最佳实践
```java
@Service
public class BusinessCodeServiceImpl implements BusinessCodeService {
    
    @Autowired
    private BusinessCodeService businessCodeService;
    
    // 正确示例：标准编号生成方法
    private String generateBusinessCode(String prefix, String dealerCode, Map<String, Object> extraParams) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("prefix", prefix);
        paramMap.put("dealer", dealerCode);
        if (extraParams != null) {
            paramMap.putAll(extraParams);
        }
        
        RuleCodeVo vo = RuleCodeVo.builder()
            .ruleKey(RuleContant.DMS_CODE)
            .paramMap(paramMap)
            .build();
            
        return businessCodeService.getCode(vo);
    }
    
    // 正确示例：订单编号生成
    public String generateOrderCode(String dealerCode, String businessType) {
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("businessType", businessType);
        return generateBusinessCode("ORD", dealerCode, extraParams);
    }
    
    // 正确示例：结合幂等的编号生成
    @Idempotent(
        prefix = "order:code:generate", 
        key = {"#dealerCode", "#businessType"},
        timeout = 300
    )
    public String generateOrderCodeIdempotent(String dealerCode, String businessType) {
        return generateOrderCode(dealerCode, businessType);
    }
}
```

### 7. 综合使用最佳实践
```java
@Service
public class ComplexBusinessServiceImpl implements ComplexBusinessService {
    
    @Autowired
    private BusinessCodeService businessCodeService;
    
    // 综合示例：创建订单（幂等 + 分布式锁 + 编号生成）
    @Idempotent(
        prefix = "order:create", 
        key = {"#dto.userId", "#dto.productId", "#dto.quantity"},
        timeout = 600
    )
    @DistributedLock(
        prefix = "inventory:check", 
        key = {"#dto.productId"}, 
        waitTime = 3000,
        timeout = 10000
    )
    public OrderVO createOrder(OrderCreateDTO dto) {
        // 1. 生成订单编号
        RuleCodeVo vo = RuleCodeVo.builder()
            .ruleKey(RuleContant.DMS_CODE)
            .paramMap(new HashMap<String, Object>() {
                {
                    put("prefix", "ORD");
                    put("dealer", dto.getDealerCode());
                    put("businessType", "ONLINE");
                }
            }).build();
        String orderCode = businessCodeService.getCode(vo);
        
        // 2. 检查库存
        checkInventory(dto.getProductId(), dto.getQuantity());
        
        // 3. 创建订单
        Order order = new Order();
        order.setOrderCode(orderCode);
        order.setUserId(dto.getUserId());
        order.setProductId(dto.getProductId());
        order.setQuantity(dto.getQuantity());
        
        orderMapper.insert(order);
        
        // 4. 扣减库存
        deductInventory(dto.getProductId(), dto.getQuantity());
        
        return convertToVO(order);
    }
}
```