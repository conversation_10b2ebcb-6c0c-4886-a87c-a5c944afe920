---
description: 
globs: 
alwaysApply: false
---
# 后端代码生成规则 - 质量检查

> **规则说明：** 本文件定义代码生成的质量检查标准和检查清单，确保生成代码的质量和规范性。
> 
> **引用关系：** 本规则汇总其他所有规则文件的检查要点，提供完整的质量检查清单。

## 🎯 质量检查概述

### 检查目的

- **确保规范一致性**：所有生成代码遵循统一规范
- **保证代码质量**：减少错误和潜在问题
- **提升可维护性**：代码结构清晰，易于维护
- **强化安全性**：避免安全隐患和数据泄露

### 检查层次

1. **基础架构检查** - 项目结构和基础配置
2. **代码模板检查** - 各层代码模板规范
3. **DTO/VO设计检查** - 数据传输对象设计质量
4. **数据库操作检查** - MyBatis Plus和事务使用
5. **安全性检查** - 权限控制和数据安全
6. **性能检查** - 性能优化和资源使用

## 📋 基础架构检查清单

### Controller层检查

- [ ] **基础注解配置**：
  - [ ] 使用@RestController注解
  - [ ] 配置@RequestMapping路径
  - [ ] 使用@Slf4j日志注解
  - [ ] 使用@Validated参数校验注解

- [ ] **接口设计规范**：
  - [ ] 所有接口遵循RESTful API的标准
  - [ ] 返回统一Result<T>格式
  - [ ] 入参使用具体DTO对象，禁止Map
  - [ ] 禁用@PathVariable和@RequestParam

### Service层检查

- [ ] **继承关系**：
  - [ ] Service接口继承IService<T>
  - [ ] ServiceImpl继承ServiceImpl<Mapper, Entity>
  - [ ] 正确注入Mapper依赖

- [ ] **方法实现**：
  - [ ] 使用lambda查询：super.lambdaQuery()
  - [ ] 使用lambda更新：super.lambdaUpdate()
  - [ ] 不重复实现IService已有方法
  - [ ] 正确使用分页：Page和IPage

- [ ] **注解使用**：
  - [ ] 事务注解：@Transactional(rollbackFor = Exception.class)
  - [ ] 分布式锁：@DistributedLock
  - [ ] 幂等控制：@Idempotent
  - [ ] 正确的SPEL表达式

### Mapper层检查

- [ ] **接口定义**：
  - [ ] 继承BaseMapper<T>
  - [ ] 不重复定义基础CRUD方法
  - [ ] 只定义复杂查询方法
  - [ ] 默认不生成XML文件

### Entity层检查

- [ ] **基础配置**：
  - [ ] 继承BasePO基类
  - [ ] 使用@TableName指定表名
  - [ ] 使用@TableField指定字段映射
  - [ ] 不重复定义BasePO已有字段

- [ ] **注解配置**：
  - [ ] @Data注解
  - [ ] @EqualsAndHashCode(callSuper = true)
  - [ ] 正确的字段映射

## 📝 DTO/VO设计检查清单 ⭐

### DTO职责单一性检查

- [ ] **场景专用性**：
  - [ ] 每个DTO只服务一个特定场景
  - [ ] CreateDTO、UpdateDTO、QueryDTO、DetailDTO功能明确
  - [ ] 不存在万能DTO（包含所有字段的DTO）
  - [ ] DTO名称明确体现使用场景

- [ ] **字段精确控制**：
  - [ ] 创建DTO不包含系统字段（id、createTime等）
  - [ ] 更新DTO不包含不可修改字段
  - [ ] 查询DTO只包含查询条件字段
  - [ ] 响应DTO按展示需求精确定义字段

### DTO安全性检查

- [ ] **权限安全分离**：
  - [ ] 不同权限级别使用不同DTO/VO
  - [ ] 敏感字段在对应DTO中隔离
  - [ ] 管理员DTO与普通用户DTO分开定义
  - [ ] 不暴露不必要的系统字段

- [ ] **校验完整性**：
  - [ ] 所有必填字段有@NotNull/@NotBlank校验
  - [ ] 字符串字段有长度限制@Length
  - [ ] 数值字段有范围限制@Min/@Max
  - [ ] 集合字段有大小限制@Size
  - [ ] 枚举字段有自定义校验方法

### VO设计检查

- [ ] **展示导向**：
  - [ ] 根据前端展示需求设计字段
  - [ ] 列表VO精简字段，详情VO包含完整信息
  - [ ] 提供计算属性（如statusName）
  - [ ] 不包含校验注解

- [ ] **权限分级**：
  - [ ] 不同权限级别有对应的VO
  - [ ] 避免敏感信息泄露
  - [ ] 字段暴露符合权限要求

### 复用与拆分策略检查

- [ ] **合理复用**：
  - [ ] 语义一致时才复用DTO
  - [ ] 字段需求不同时拆分DTO
  - [ ] 校验规则一致的才复用

- [ ] **公共抽取**：
  - [ ] 使用继承抽取公共字段（BaseQueryDTO等）
  - [ ] 使用组合复用功能模块（TimeRangeDTO等）
  - [ ] 避免重复代码

## 🗃️ 数据库操作检查清单

### MyBatis Plus使用检查

- [ ] **基础能力使用**：
  - [ ] 单表CRUD使用MyBatis Plus基础方法
  - [ ] 禁止为单表操作编写自定义SQL
  - [ ] 使用lambda查询构建条件
  - [ ] 禁止手动拼接SQL字符串

- [ ] **逻辑删除处理**：
  - [ ] 禁止显式处理isDeleted字段
  - [ ] 查询时不手动添加isDeleted条件
  - [ ] 删除时使用removeById等基础方法
  - [ ] 更新时不手动设置isDeleted

- [ ] **状态管理**：
  - [ ] 使用枚举或常量类定义状态值
  - [ ] 禁止魔法数字
  - [ ] 状态校验使用枚举方法
  - [ ] 提供状态转换说明

### 事务管理检查

- [ ] **事务注解配置**：
  - [ ] 使用@Transactional(rollbackFor = Exception.class)
  - [ ] 合理设置超时时间
  - [ ] 只读事务使用readOnly = true
  - [ ] 事务粒度足够小

- [ ] **事务使用规范**：
  - [ ] 先调用外部服务，再执行本地事务
  - [ ] 避免类内部直接调用事务方法
  - [ ] 单条操作不使用事务，直接判断结果
  - [ ] 批量操作合理使用事务

### 分布式锁和幂等检查

- [ ] **注解使用**：
  - [ ] 使用@DistributedLock实现分布式锁
  - [ ] 使用@Idempotent实现幂等控制
  - [ ] 禁止手动实现Redis锁逻辑
  - [ ] SPEL表达式正确有效

- [ ] **参数配置**：
  - [ ] 锁key具有唯一性
  - [ ] 超时时间设置合理
  - [ ] 幂等策略选择正确
  - [ ] 支持注解组合使用

## 🔒 安全性检查清单

### 数据安全检查

- [ ] **SQL注入防护**：
  - [ ] 使用MyBatis Plus预防SQL注入
  - [ ] 禁止拼接SQL字符串
  - [ ] 参数化查询使用正确
  - [ ] 输入数据经过校验

- [ ] **权限控制**：
  - [ ] 不同权限级别数据隔离
  - [ ] 敏感字段访问控制
  - [ ] 操作权限验证完整
  - [ ] 数据范围权限控制

### 输入验证检查

- [ ] **参数校验**：
  - [ ] 所有输入参数有效性校验
  - [ ] 字符串长度限制检查
  - [ ] 数值范围合理性检查
  - [ ] 特殊字符过滤处理

- [ ] **业务校验**：
  - [ ] 枚举值有效性校验
  - [ ] 业务规则逻辑校验
  - [ ] 数据一致性校验
  - [ ] 关联数据存在性校验

## ⚡ 性能检查清单

### 查询性能检查

- [ ] **分页查询**：
  - [ ] 分页大小限制（最大500条）
  - [ ] 分页参数校验
  - [ ] 排序字段安全性检查
  - [ ] 避免深度分页问题

- [ ] **查询优化**：
  - [ ] 使用索引覆盖查询
  - [ ] 避免全表扫描
  - [ ] 合理使用查询条件
  - [ ] 避免N+1查询问题

### 缓存使用检查

- [ ] **Redis缓存**：
  - [ ] 缓存key设计合理
  - [ ] 缓存过期时间设置
  - [ ] 缓存更新策略正确
  - [ ] 避免缓存雪崩

### 批量操作检查

- [ ] **批量处理**：
  - [ ] 批量大小限制控制
  - [ ] 批量操作事务处理
  - [ ] 内存使用优化
  - [ ] 错误处理机制

## 📊 代码质量检查清单

### 代码规范检查

- [ ] **命名规范**：
  - [ ] 类名、方法名符合命名规范
  - [ ] 变量名具有明确含义
  - [ ] 常量使用全大写命名
  - [ ] 包名遵循约定

- [ ] **注释规范**：
  - [ ] 类和方法有完整注释
  - [ ] 复杂逻辑有解释说明
  - [ ] 注释内容准确有效
  - [ ] 避免无用注释

### 异常处理检查

- [ ] **异常使用**：
  - [ ] 使用BusinessException处理业务异常
  - [ ] 使用ExceptionEnum统一错误码
  - [ ] 异常信息具有指导意义
  - [ ] 避免吞噬异常

- [ ] **日志记录**：
  - [ ] 关键操作有日志记录
  - [ ] 异常信息完整记录
  - [ ] 日志级别使用正确
  - [ ] 敏感信息不记录日志

## ✅ 检查通过标准

### 强制检查项

以下检查项必须100%通过：

1. **架构规范**：继承关系、注解使用、接口设计
2. **安全规范**：权限控制、数据安全、输入验证
3. **DTO设计**：职责单一、字段精确、校验完整
4. **数据库操作**：MyBatis Plus使用、逻辑删除处理

### 建议检查项

以下检查项建议通过率达到90%以上：

1. **性能优化**：查询优化、缓存使用、批量处理
2. **代码质量**：命名规范、注释完整、异常处理
3. **可维护性**：代码结构、复用设计、扩展性

## 🔧 检查工具和方法

### 自动化检查

- **静态代码分析**：使用SonarQube等工具
- **单元测试**：覆盖率达到80%以上
- **集成测试**：接口测试完整
- **代码审查**：人工代码审查

### 手动检查

- **架构审查**：架构师审查设计
- **安全审查**：安全专家审查
- **性能测试**：压力测试验证
- **用户验收**：产品验收测试

## 🚫 常见问题和解决方案

### 设计问题

| 问题 | 表现 | 解决方案 |
|------|------|----------|
| 万能DTO | 一个DTO包含所有字段 | 按场景拆分专用DTO |
| 权限混乱 | 不同权限看到相同数据 | 设计权限分级DTO/VO |
| 校验不完整 | 缺少必要的数据校验 | 补充校验注解和业务校验 |
| 性能问题 | 查询数据过多或过慢 | 优化查询条件和分页设计 |

### 实现问题

| 问题 | 表现 | 解决方案 |
|------|------|----------|
| 重复代码 | 相似逻辑重复实现 | 抽取公共方法或基类 |
| 事务滥用 | 不必要的事务使用 | 单操作不用事务，复杂操作才用 |
| 异常处理不当 | 异常信息不明确 | 使用BusinessException和明确错误信息 |
| 缓存问题 | 缓存更新不及时 | 设计合理的缓存更新策略 |

