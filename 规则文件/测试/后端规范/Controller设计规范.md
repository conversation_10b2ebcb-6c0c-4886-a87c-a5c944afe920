### Controller 设计规范 (V2.0 - 公司标准版)

本规范定义了Controller层的设计标准，旨在确保该层职责单一、轻量，并成为一个高规范性的HTTP请求处理入口。

#### 1. 职责定位
- **唯一的HTTP入口**：Controller是处理外部HTTP请求的唯一入口。
- **"薄层"定位**：Controller应保持为一个"薄层"，其核心职责是**请求的路由、校验和委派**。
- **严禁包含业务逻辑**：任何业务逻辑、数据计算、流程编排都**严禁**出现在Controller层。

#### 2. 核心职责清单
一个Controller方法的核心工作流应严格遵守以下步骤：
1.  **接收HTTP请求**：解析URL、路径参数、查询参数和请求体(`Query`或`Form`对象)。
2.  **参数校验**：对请求对象使用`@Valid`或`@Validated`进行自动化校验。
3.  **对象转换**: 调用`Converter`将`Query/Form`对象转换为`DTO`。
4.  **调用Service**：调用一个`Service`方法来完成业务处理。
5.  **结果转换**: 调用`Converter`将Service返回的`DTO`转换为`VO`。
6.  **封装响应**：将`VO`或`PageResult<VO>`使用`ResultData<T>`进行包装，然后返回给客户端。

#### 3. 对象使用规范
- **请求对象**：
  - 分页查询: `...PageQuery`
  - 列表查询: `...ListQuery`
  - 表单提交(增/改): `...Form`
- **响应对象 (VO)**：
  - **强制使用VO**：Controller的返回值**必须**是`VO` (View Object)对象，严禁出现`DTO`或数据库实体(`PO`)。
- **转换器 (`Converter`)**：
  - `Query/Form`与`DTO`的互相转换、`DTO`与`VO`的互相转换，**必须**在Controller层通过调用相应的`Converter`完成。

#### 4. 编码规范
- **注解使用**：
  - **类级别**：使用`@RestController`, `@RequestMapping`和`@Tag` (用于Swagger分组)。
  - **方法级别**：使用`@GetMapping`, `@PostMapping`等，并用`@Operation`描述接口用途。
- **方法返回值**：**必须**是`ResultData<T>`的泛型类型，例如 `ResultData<UserVO>` 或 `ResultData<PageResult<UserVO>>`。
- **异常处理**：
  - Controller自身**不捕获**任何业务异常(`BizException`)。
  - 所有异常都应交由全局异常处理器统一捕获，并转换为`ResultData`的失败格式返回。

#### 5. API文档
- **强制要求**：每个对外暴露的Controller方法都**必须**有完整的Swagger/OpenAPI注解。
- **核心注解**：
  - `@Operation`：清晰描述接口的业务用途。
  - `@Parameter` / `@Parameters`：描述路径参数、查询参数的含义。
  - `@ApiResponses` / `@ApiResponse`：明确列出可能的成功和失败响应，包括不同的HTTP状态码和业务错误码。
- **目标**：做到API文档即代码，代码即文档，无需额外维护一份静态的接口文档。 