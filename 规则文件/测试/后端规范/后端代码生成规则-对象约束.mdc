---
description: 
globs: 
alwaysApply: true
---
# 后端代码生成规则 - 对象约束

> **规则说明：** 本文件定义后端开发中DTO、VO、PO对象的统一约束规范和使用标准。
> 
> **引用关系：** 本规则引用 [基础架构规则](mdc:dms/dms/dms/dms/dms/dms/dms/dms/dms/后端代码生成规则-基础架构.mdc) 和 [后端代码通用组件规则.mdc](mdc:dms/dms/dms/dms/dms/dms/dms/dms/dms/后端代码通用组件规则.mdc)

## 🎯 核心约束总览

### 强制执行约束

- ✅ **强制PO命名约束**：所有数据库持久化对象必须以`PO`结尾，如`UserPO`、`OrderPO`、`ProspectiveCustomerPO`
- ✅ **强制对象转换约束**：VO/DTO/PO之间的对象拷贝必须使用`cn.hutool.core.bean.BeanUtils`
- 🚫 **禁止手动字段拷贝**：禁止使用Spring BeanUtils、Apache BeanUtils或手动赋值
- ✅ **适用所有转换场景**：DTO→PO、PO→VO、DTO→DTO、VO→VO等所有转换场景
- 🚫 **禁止VO包含响应词汇**：接口出参VO不允许包含Resp、Response等响应相关词汇
- 🚫 **禁止DTO包含请求词汇**：接口入参DTO不允许包含Req、Request等请求相关词汇
- ✅ **强制枚举独立定义**：所有对象中的枚举必须在独立的枚举包中创建，禁止在对象内部创建枚举子类
- 🚫 **禁止DTO/VO/PO继承**：DTO/VO/PO不允许有子类，复合对象必须采用多对象引用的方式

## 🏗️ 对象约束定义

### 📦 PO (Persistent Object) - 持久化对象约束

- **使用范围**：**数据库实体专用**
- **职责**：与数据库表结构一一对应的持久化对象
- **约束**：必须以PO结尾，继承BasePO，使用MyBatis Plus注解
- **命名规范**：`{业务名}PO`，如`UserPO`、`OrderPO`

### 📥 DTO (Data Transfer Object) - 数据传输对象约束

- **使用范围**：**接口入参专用**
- **职责**：承载客户端向服务端传输的数据
- **约束**：包含数据校验注解、业务逻辑校验方法，不含系统字段
- **命名规范**：`{业务名}{操作}DTO`，如`UserCreateDTO`、`UserUpdateDTO`
- **禁止词汇**：不允许包含Req、Request等请求相关词汇

### 📤 VO (Value Object) - 值对象约束

- **使用范围**：**接口出参专用**  
- **职责**：承载服务端向客户端返回的数据
- **约束**：只包含展示数据，无校验注解，可包含计算属性
- **命名规范**：`{业务名}VO`、`{业务名}DetailVO`、`{业务名}ListItemVO`
- **禁止词汇**：不允许包含Resp、Response等响应相关词汇

## 🏷️ PO命名约束规范

### 命名约束规则

| 实体类型 | 命名约束 | 示例 | 约束说明 |
|---------|----------|------|------|
| **数据库实体类** | `{业务名}PO` | `UserPO`、`OrderPO` | 所有数据库实体类必须以PO结尾 |
| **关联表实体** | `{主表名}{关联表名}PO` | `UserRolePO`、`OrderProductPO` | 多对多关联表 |
| **业务复杂实体** | `{完整业务名}PO` | `ProspectiveCustomerPO`、`ProductCategoryPO` | 业务名较长时保持完整性 |
| **历史表实体** | `{业务名}HistoryPO` | `UserHistoryPO`、`OrderHistoryPO` | 历史记录表 |
| **日志表实体** | `{业务名}LogPO` | `UserOperationLogPO`、`SystemLogPO` | 日志记录表 |

### PO约束正确示例

```java
// ✅ 正确：用户表实体约束
@Data
@TableName("user")
public class UserPO extends BasePO {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String username;
    private String email;
    private Integer status;
    
    // ... 其他字段
}

// ✅ 正确：订单表实体约束
@Data
@TableName("order")
public class OrderPO extends BasePO {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String orderNo;
    private Long userId;
    private BigDecimal amount;
    
    // ... 其他字段
}

// ✅ 正确：潜在客户表实体约束
@Data
@TableName("prospective_customer")
public class ProspectiveCustomerPO extends BasePO {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String companyName;
    private String contactPerson;
    private String phone;
    
    // ... 其他字段
}
```

### PO约束违规示例

```java
// 🚫 违规：缺少PO后缀
@Data
@TableName("user")
public class User extends BasePO {  // 违规：应该是UserPO
    // ...
}

// 🚫 违规：使用Entity后缀
@Data
@TableName("order")
public class OrderEntity extends BasePO {  // 违规：应该是OrderPO
    // ...
}

// 🚫 违规：使用DO后缀
@Data
@TableName("product")
public class ProductDO extends BasePO {  // 违规：应该是ProductPO
    // ...
}
```

## 🔄 对象转换约束

### 强制使用Hutool BeanUtils约束

所有VO/DTO/PO之间的对象拷贝必须使用`cn.hutool.core.bean.BeanUtils`，这是强制性约束。

### 基础拷贝操作约束

```java
import cn.hutool.core.bean.BeanUtils;

// ✅ 约束合规：DTO转PO
public UserPO convertToPO(UserCreateDTO dto) {
    UserPO userPO = new UserPO();
    BeanUtils.copyProperties(dto, userPO);
    return userPO;
}

// ✅ 约束合规：PO转VO
public UserVO convertToVO(UserPO po) {
    UserVO userVO = new UserVO();
    BeanUtils.copyProperties(po, userVO);
    return userVO;
}

// ✅ 约束合规：DTO转DTO
public UserUpdateDTO convertToUpdateDTO(UserCreateDTO createDTO) {
    UserUpdateDTO updateDTO = new UserUpdateDTO();
    BeanUtils.copyProperties(createDTO, updateDTO);
    return updateDTO;
}

// ✅ 约束合规：VO转VO
public UserListItemVO convertToListItemVO(UserDetailVO detailVO) {
    UserListItemVO listItemVO = new UserListItemVO();
    BeanUtils.copyProperties(detailVO, listItemVO);
    return listItemVO;
}
```

### 批量拷贝操作约束

```java
import cn.hutool.core.bean.BeanUtils;

// ✅ 约束合规：批量PO转VO
public List<UserVO> convertToVOList(List<UserPO> poList) {
    return poList.stream()
        .map(po -> {
            UserVO vo = new UserVO();
            BeanUtils.copyProperties(po, vo);
            return vo;
        })
        .collect(Collectors.toList());
}

// ✅ 约束合规：批量DTO转PO
public List<UserPO> convertToPOList(List<UserCreateDTO> dtoList) {
    return dtoList.stream()
        .map(dto -> {
            UserPO po = new UserPO();
            BeanUtils.copyProperties(dto, po);
            return po;
        })
        .collect(Collectors.toList());
}
```

### 带忽略字段的拷贝约束

```java
import cn.hutool.core.bean.BeanUtils;
import cn.hutool.core.bean.copier.CopyOptions;

// ✅ 约束合规：忽略特定字段的拷贝
public UserPO convertToPOWithIgnore(UserUpdateDTO dto) {
    UserPO userPO = new UserPO();
    BeanUtils.copyProperties(dto, userPO, 
        CopyOptions.create().setIgnoreNullValue(true)
                           .setIgnoreProperties("id", "createTime", "updateTime"));
    return userPO;
}

// ✅ 约束合规：只拷贝非空值
public void updatePOFromDTO(UserUpdateDTO dto, UserPO po) {
    BeanUtils.copyProperties(dto, po, 
        CopyOptions.create().setIgnoreNullValue(true));
}
```

## 🏗️ DTO约束原则

### 📥 DTO约束原则（入参）

- **精确性约束**：每个DTO只包含特定操作需要的字段，避免冗余
- **校验完整性约束**：包含完整的数据校验注解和业务逻辑校验
- **语义一致性约束**：可复用DTO，但必须确保在不同场景下语义完全一致
- **安全性约束**：不包含敏感的系统字段（如id、createTime等）

### 📤 VO约束原则（出参）

- **展示导向约束**：根据前端展示需求设计字段，支持UI渲染
- **计算属性约束**：可包含基于基础数据计算的衍生字段（如statusName）
- **权限分级约束**：不同权限级别使用不同VO，控制数据暴露范围
- **性能优化约束**：列表VO精简字段，详情VO包含完整信息

## 📋 DTO/VO复用与拆分约束策略

### ✅ 推荐复用约束场景

- 字段完全一致且语义相同
- 校验规则完全相同
- 业务含义完全一致

### 🚫 不推荐复用约束场景

- 不同场景字段需求不同
- 校验规则存在差异
- 权限控制要求不同
- 业务语义存在差异

### 📊 复用与拆分约束决策表

| 情况 | 约束建议 | 约束原因 |
|---|---|---|
| DTO/VO 复用 | 可以复用，但需确保语义一致 | 避免字段语义混乱 |
| 不同场景字段不同 | 建议拆分定义 | 提高精确性和安全性 |
| 字段重复较多 | 使用组合或继承抽出公共部分 | 减少重复代码 |
| 控制精度、易维护 | 拆分更有利于维护、校验和安全控制 | 长期可维护性 |

## 🏗️ 公共DTO约束设计

### 基础查询DTO约束

```java
// 基础查询DTO约束 - 可被继承
@Data
public abstract class BaseQueryDTO {
    
    @Min(value = 1, message = "当前页必须大于0")
    private Integer current = 1;

    @Min(value = 1, message = "页大小必须大于0") 
    @Max(value = 500, message = "页大小不能超过500")
    private Integer size = 10;

    private String sort;
    private String order = "desc";
} 
```

### 时间范围查询DTO约束

```java
// 时间范围查询DTO约束 - 可被组合
@Data
public class TimeRangeDTO {
    
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") 
    private LocalDateTime endTime;
    
    @AssertTrue(message = "结束时间必须大于开始时间")
    public boolean isValidTimeRange() {
        if (startTime == null || endTime == null) {
            return true;
        }
        return endTime.isAfter(startTime);
    }
}
```

### 通用请求DTO约束

```java
// ID请求DTO约束 - 高频复用
@Data
public class IdRequestDTO {
    
    @NotNull(message = "ID不能为空")
    @Min(value = 1, message = "ID必须大于0")
    private Long id;
}

// 批量ID请求DTO约束
@Data  
public class BatchIdRequestDTO {
    
    @NotEmpty(message = "ID列表不能为空")
    @Size(max = 100, message = "批量操作最多支持100个ID")
    private List<@NotNull(message = "ID不能为空") @Min(value = 1, message = "ID必须大于0") Long> ids;
}
```

## 📝 DTO约束模板（入参专用）

### 分页查询DTO约束

```java
// 分页查询DTO约束 - 继承BaseQueryDTO
@Data
@EqualsAndHashCode(callSuper = true)
public class {业务名}PageQueryDTO extends BaseQueryDTO {

    // 业务特有查询字段
    @Length(max = 50, message = "名称长度不能超过50个字符")
    private String name;

    private Integer status;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    // 业务校验方法
    @AssertTrue(message = "状态值无效")
    public boolean isValidStatus() {
        return status == null || {业务名}StatusEnum.isValidStatus(status);
    }

    @AssertTrue(message = "结束时间必须大于开始时间")
    public boolean isValidTimeRange() {
        if (startTime == null || endTime == null) {
            return true;
        }
        return endTime.isAfter(startTime);
    }
}
```

### 详情查询DTO约束

```java
// 详情查询DTO约束 - 简单ID查询
@Data
public class {业务名}DetailQueryDTO {
    
    @NotNull(message = "ID不能为空")
    @Min(value = 1, message = "ID必须大于0")
    private Long id;
}
```

### 创建DTO约束

```java
// 创建DTO约束 - 不包含系统字段
@Data
public class {业务名}CreateDTO {

    @NotBlank(message = "名称不能为空")
    @Length(max = 50, message = "名称长度不能超过50个字符")
    private String name;

    @NotNull(message = "状态不能为空")
    private Integer status;

    @Length(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    // 幂等性相关字段
    @NotBlank(message = "幂等键不能为空")
    private String idempotentKey;

    // 业务唯一性字段
    @NotBlank(message = "唯一键不能为空")
    private String uniqueKey;

    // 业务校验方法
    @AssertTrue(message = "状态值无效")
    public boolean isValidStatus() {
        return {业务名}StatusEnum.isValidStatus(status);
    }

    // 约束：不包含系统字段（id、createTime、updateTime等）
}
```

### 更新DTO约束

```java
// 全量更新DTO约束
@Data
public class {业务名}UpdateDTO {

    @NotBlank(message = "名称不能为空")
    @Length(max = 50, message = "名称长度不能超过50个字符")
    private String name;

    @NotNull(message = "状态不能为空")
    private Integer status;

    @Length(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    // 业务校验方法
    @AssertTrue(message = "状态值无效")
    public boolean isValidStatus() {
        return {业务名}StatusEnum.isValidStatus(status);
    }
}

// 部分更新DTO约束
@Data
public class {业务名}PatchDTO {

    @Length(max = 50, message = "名称长度不能超过50个字符")
    private String name;

    private Integer status;

    @Length(max = 200, message = "描述长度不能超过200个字符")
    private String description;

    // 业务校验方法
    @AssertTrue(message = "状态值无效")
    public boolean isValidStatus() {
        return status == null || {业务名}StatusEnum.isValidStatus(status);
    }
}
```

### 复合请求DTO约束

```java
// 更新请求DTO约束 - 包含ID和数据
@Data
public class {业务名}UpdateRequestDTO {
    
    @NotNull(message = "ID不能为空")
    @Min(value = 1, message = "ID必须大于0")
    private Long id;
    
    @NotNull(message = "更新数据不能为空")
    @Valid
    private {业务名}UpdateDTO updateData;
}

// 删除请求DTO约束 - 可包含删除原因
@Data
public class {业务名}DeleteRequestDTO {
    
    @NotNull(message = "ID不能为空")
    @Min(value = 1, message = "ID必须大于0")
    private Long id;
    
    @Length(max = 100, message = "删除原因长度不能超过100个字符")
    private String reason;
}

// 批量删除请求DTO约束
@Data
public class {业务名}BatchDeleteRequestDTO {
    
    @NotEmpty(message = "ID列表不能为空")
    @Size(max = 100, message = "批量操作最多支持100个ID")
    private List<@NotNull @Min(1) Long> ids;
    
    @NotBlank(message = "删除原因不能为空")
    @Length(max = 100, message = "删除原因长度不能超过100个字符")
    private String reason;
}
```

## 📄 VO约束模板（出参专用）

### 创建响应VO约束

```java
// 创建响应VO约束 - 只返回必要信息
@Data
public class {业务名}CreateResponseVO {

    private Long id;
    private LocalDateTime createTime;
    // 创建成功后只返回关键信息
}
```

### 列表项VO约束

```java
// 简单列表VO约束 - 列表展示时的精简信息
@Data
public class {业务名}ListItemVO {

    private Long id;
    private String name;
    private Integer status;
    private String statusName;  // 计算属性
    private LocalDateTime createTime;
    
    /**
     * 获取状态名称（计算属性，基于枚举）
     */
    public String getStatusName() {
        {业务名}StatusEnum statusEnum = {业务名}StatusEnum.getByCode(this.status);
        return statusEnum != null ? statusEnum.getDesc() : "未知状态";
    }
}
```

### 详情VO约束

```java
// 详情VO约束 - 详情页面的完整信息
@Data
public class {业务名}DetailVO {

    private Long id;
    private String name;
    private Integer status;
    private String statusName;  // 计算属性
    private String description;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createByName;
    private String updateByName;
    
    // 可能包含关联数据
    private List<RelatedDataVO> relatedData;
    
    /**
     * 获取状态名称（计算属性，基于枚举）
     */
    public String getStatusName() {
        {业务名}StatusEnum statusEnum = {业务名}StatusEnum.getByCode(this.status);
        return statusEnum != null ? statusEnum.getDesc() : "未知状态";
    }
}
```

### 权限分级VO约束

```java
// 管理员用户详情VO约束 - 包含完整权限信息
@Data
public class Admin{业务名}DetailVO {

    private Long id;
    private String name;
    private Integer status;
    private String statusName;
    private String description;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createByName;
    private String updateByName;
    
    // 管理员可见的额外信息
    private String internalNotes;      // 内部备注
    private List<String> operationLogs; // 操作日志
    private Map<String, Object> metadata; // 元数据信息
    
    /**
     * 获取状态名称（计算属性）
     */
    public String getStatusName() {
        {业务名}StatusEnum statusEnum = {业务名}StatusEnum.getByCode(this.status);
        return statusEnum != null ? statusEnum.getDesc() : "未知状态";
    }
}

// 普通用户详情VO约束 - 基础信息
@Data  
public class Regular{业务名}DetailVO {

    private Long id;
    private String name;
    private Integer status;
    private String statusName;
    private String description;
    private LocalDateTime createTime;
    
    // 普通用户不能看到敏感信息
    // 不包含：内部备注、操作日志、创建人等
    
    /**
     * 获取状态名称（计算属性）
     */
    public String getStatusName() {
        {业务名}StatusEnum statusEnum = {业务名}StatusEnum.getByCode(this.status);
        return statusEnum != null ? statusEnum.getDesc() : "未知状态";
    }
}
```

### 导出VO约束

```java
// 导出VO约束 - 导出时需要的字段格式
@Data
public class {业务名}ExportVO {

    @ExcelProperty("编号")
    private Long id;
    
    @ExcelProperty("名称")
    private String name;
    
    @ExcelProperty("状态")
    private String statusName;
    
    @ExcelProperty("描述")
    private String description;
    
    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 获取状态名称（用于导出显示）
     */
    public String getStatusName() {
        {业务名}StatusEnum statusEnum = {业务名}StatusEnum.getByCode(this.status);
        return statusEnum != null ? statusEnum.getDesc() : "未知状态";
    }
}
```

## 🔄 Service层转换实战约束示例

### 完整Service实现（遵循Hutool BeanUtils约束）

```java
import cn.hutool.core.bean.BeanUtils;
import cn.hutool.core.bean.copier.CopyOptions;

@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, UserPO> implements UserService {

    @Override
    public Long createUser(UserCreateDTO dto) {
        // DTO转PO（遵循约束）
        UserPO userPO = new UserPO();
        BeanUtils.copyProperties(dto, userPO);
        
        // 保存到数据库
        this.save(userPO);
        
        return userPO.getId();
    }

    @Override
    public UserVO getUserById(Long id) {
        // 查询PO
        UserPO userPO = this.getById(id);
        if (userPO == null) {
            throw new BusinessException(ExceptionEnum.DATA_NOT_FOUND_ERROR);
        }
        
        // PO转VO（遵循约束）
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(userPO, userVO);
        
        return userVO;
    }

    @Override
    public void updateUser(Long id, UserUpdateDTO dto) {
        // 查询现有PO
        UserPO userPO = this.getById(id);
        if (userPO == null) {
            throw new BusinessException(ExceptionEnum.DATA_NOT_FOUND_ERROR);
        }
        
        // DTO内容拷贝到PO（只拷贝非空值，遵循约束）
        BeanUtils.copyProperties(dto, userPO, 
            CopyOptions.create().setIgnoreNullValue(true));
        
        // 更新到数据库
        this.updateById(userPO);
    }

    @Override
    public IPage<UserVO> pageUsers(UserPageQueryDTO queryDTO) {
        // 分页查询PO
        Page<UserPO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<UserPO> poPage = this.lambdaQuery()
            .like(StringUtils.isNotBlank(queryDTO.getName()), UserPO::getUsername, queryDTO.getName())
            .eq(queryDTO.getStatus() != null, UserPO::getStatus, queryDTO.getStatus())
            .page(page);
        
        // 批量PO转VO（遵循约束）
        return poPage.convert(po -> {
            UserVO vo = new UserVO();
            BeanUtils.copyProperties(po, vo);
            return vo;
        });
    }

    @Override
    public List<UserVO> batchConvertToVO(List<UserPO> poList) {
        // 批量转换（遵循约束）
        return poList.stream()
            .map(po -> {
                UserVO vo = new UserVO();
                BeanUtils.copyProperties(po, vo);
                return vo;
            })
            .collect(Collectors.toList());
    }
}
```

### 转换器工具类约束设计

```java
/**
 * 用户转换器工具类
 * 统一管理User相关的对象转换逻辑（遵循约束）
 */
@Component
public class UserConverter {

    /**
     * DTO转PO（遵循约束）
     */
    public UserPO toPO(UserCreateDTO dto) {
        UserPO po = new UserPO();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    /**
     * PO转VO（遵循约束）
     */
    public UserVO toVO(UserPO po) {
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    /**
     * 批量PO转VO（遵循约束）
     */
    public List<UserVO> toVOList(List<UserPO> poList) {
        return poList.stream()
            .map(this::toVO)
            .collect(Collectors.toList());
    }

    /**
     * 更新PO（忽略空值，遵循约束）
     */
    public void updatePO(UserUpdateDTO dto, UserPO po) {
        BeanUtils.copyProperties(dto, po, 
            CopyOptions.create().setIgnoreNullValue(true));
    }
}
```

### 通用转换工具类约束

```java
/**
 * 通用对象拷贝工具类
 * 基于Hutool BeanUtils封装（遵循约束）
 */
@Component
public class ConvertUtils {

    /**
     * 对象拷贝（遵循约束）
     */
    public static <T> T convert(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("对象转换失败", e);
        }
    }

    /**
     * 列表对象拷贝（遵循约束）
     */
    public static <T> List<T> convertList(List<?> sourceList, Class<T> targetClass) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }
        return sourceList.stream()
            .map(source -> convert(source, targetClass))
            .collect(Collectors.toList());
    }

    /**
     * 对象拷贝（忽略空值，遵循约束）
     */
    public static void copyPropertiesIgnoreNull(Object source, Object target) {
        BeanUtils.copyProperties(source, target, 
            CopyOptions.create().setIgnoreNullValue(true));
    }
}
```

## 🔄 数据处理约束

### 📡 数据脱敏约束

- 🚫 **禁止服务端脱敏**：禁止在服务端（dms-sales-service等核心服务层）进行数据脱敏处理
- ✅ **BFF层脱敏**：所有敏感数据（手机号、身份证号、姓名等）的脱敏操作必须在BFF层（dms-pc-bff等）进行
- ✅ **统一脱敏工具**：必须使用统一的脱敏工具类进行处理，避免重复实现脱敏逻辑
- 🔍 **脱敏日志**：在进行脱敏操作时，必须在日志中标记哪些字段已被脱敏

```java
// 🚫 违规：服务层进行脱敏
@Service
public class UserServiceImpl implements UserService {
    public UserVO getUserDetail(Long id) {
        UserPO userPO = userMapper.selectById(id);
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(userPO, userVO);
        
        // 违规：服务层不应进行脱敏
        userVO.setPhone(maskPhoneNumber(userVO.getPhone()));
        userVO.setIdNumber(maskIdNumber(userVO.getIdNumber()));
        
        return userVO;
    }
}

// ✅ 正确：BFF层进行脱敏
@Service
public class UserBffServiceImpl implements UserBffService {
    @Autowired
    private UserServiceFeign userServiceFeign;
    
    public UserBffVO getUserDetail(Long id) {
        UserVO userVO = userServiceFeign.getUserDetail(id);
        UserBffVO userBffVO = new UserBffVO();
        BeanUtils.copyProperties(userVO, userBffVO);
        
        // 正确：在BFF层进行脱敏
        userBffVO.setPhone(DataMaskUtil.maskPhone(userBffVO.getPhone()));
        userBffVO.setIdNumber(DataMaskUtil.maskIdNumber(userBffVO.getIdNumber()));
        
        return userBffVO;
    }
}
```

### ⏰ 时间格式处理约束

- 🚫 **禁止服务端时间格式化**：禁止在服务端（dms-sales-service等核心服务层）进行时间格式化处理
- ✅ **服务端统一时间类型**：服务端所有时间字段必须使用`java.util.Date`类型，禁止使用String类型传递时间
- ✅ **BFF层时间格式化**：时间格式的处理（如Date转String）必须在BFF层进行
- ✅ **统一日期工具**：必须使用统一的日期工具类（如DateUtil）进行处理，避免重复实现日期格式化逻辑

```java
// 🚫 违规：服务层进行时间格式化
@Service
public class OrderServiceImpl implements OrderService {
    public OrderVO getOrderDetail(Long id) {
        OrderPO orderPO = orderMapper.selectById(id);
        OrderVO orderVO = new OrderVO();
        BeanUtils.copyProperties(orderPO, orderVO);
        
        // 违规：服务层不应处理时间格式
        orderVO.setCreateTimeStr(DateUtil.format(orderPO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        orderVO.setUpdateTimeStr(DateUtil.format(orderPO.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
        
        return orderVO;
    }
}

// ✅ 正确：BFF层进行时间格式化
@Service
public class OrderBffServiceImpl implements OrderBffService {
    @Autowired
    private OrderServiceFeign orderServiceFeign;
    
    public OrderBffVO getOrderDetail(Long id) {
        OrderVO orderVO = orderServiceFeign.getOrderDetail(id);
        OrderBffVO orderBffVO = new OrderBffVO();
        BeanUtils.copyProperties(orderVO, orderBffVO);
        
        // 正确：在BFF层进行时间格式化
        orderBffVO.setCreateTimeStr(DateUtil.format(orderVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        orderBffVO.setUpdateTimeStr(DateUtil.format(orderVO.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
        
        return orderBffVO;
    }
}
```

### 📋 服务层与BFF层职责约束

| 处理类型 | 服务层（dms-sales-service） | BFF层（dms-pc-bff） |
|---------|---------------------------|-------------------|
| 数据脱敏 | 🚫 禁止 | ✅ 必须 |
| 时间格式化 | 🚫 禁止 | ✅ 必须 |
| 数据转换 | ✅ PO→VO | ✅ VO→前端VO |
| 业务逻辑 | ✅ 核心逻辑 | ✅ 简单适配 |
| 权限校验 | ✅ 细粒度校验 | ✅ 粗粒度校验 |
| 参数校验 | ✅ 业务规则校验 | ✅ 格式校验 |

### 🔐 实现约束理由

1. **职责分离**：确保服务层专注于核心业务逻辑，BFF层专注于前端适配
2. **性能优化**：避免服务层进行额外的字符串处理，减少性能开销
3. **维护简化**：脱敏和格式化规则可能根据UI需求变化，在BFF层处理便于维护
4. **数据安全**：原始数据在服务层之间传递，仅在与前端交互的BFF层进行脱敏

## 🚫 强制禁止约束事项

### 禁止使用的拷贝工具约束

```java
// 🚫 违规：禁止使用Spring BeanUtils
import org.springframework.beans.BeanUtils;
BeanUtils.copyProperties(source, target);  // 约束禁止

// 🚫 违规：禁止使用Apache BeanUtils
import org.apache.commons.beanutils.BeanUtils;
BeanUtils.copyProperties(target, source);  // 约束禁止

// 🚫 违规：禁止手动逐字段赋值
public UserVO convertToVO(UserPO po) {
    UserVO vo = new UserVO();
    vo.setId(po.getId());                    // 约束禁止手动赋值
    vo.setUsername(po.getUsername());        // 约束禁止手动赋值
    vo.setEmail(po.getEmail());              // 约束禁止手动赋值
    // ... 更多手动赋值
    return vo;
}

// 🚫 违规：禁止使用MapStruct等其他转换工具
@Mapper
public interface UserConvert {             // 约束禁止使用MapStruct
    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);
    UserVO toVO(UserPO po);
}
```

### 禁止的PO命名方式约束

```java
// 🚫 违规：实体类不使用PO后缀
public class User extends BasePO { }         // 应该是UserPO
public class OrderEntity extends BasePO { }  // 应该是OrderPO
public class ProductDO extends BasePO { }    // 应该是ProductPO
public class CustomerModel extends BasePO { } // 应该是CustomerPO
```

### DTO禁止约束事项

- 🚫 **禁止包含系统字段**：不要在CreateDTO中包含id、createTime、updateTime等系统字段
- 🚫 **禁止万能DTO**：不要创建包含所有字段的通用DTO
- 🚫 **禁止忽略校验**：所有输入字段都必须有适当的校验注解
- 🚫 **禁止复杂嵌套**：避免过深的对象嵌套，影响性能和可维护性

### VO禁止约束事项

- 🚫 **禁止包含校验注解**：VO是输出对象，不需要校验注解
- 🚫 **禁止暴露敏感信息**：根据权限控制返回字段，避免敏感信息泄露
- 🚫 **禁止过于臃肿**：列表VO应该精简，详情VO才包含完整信息
- 🚫 **禁止忽略计算属性**：状态等枚举字段应提供对应的名称字段

## ✅ 最佳实践约束建议

### 对象设计最佳约束实践

1. **PO约束设计**：
   - 所有实体类必须以PO结尾
   - 继承BasePO获得基础字段
   - 使用@TableName指定表名
   - 字段映射清晰明确

2. **DTO约束设计**：
   - 每个操作设计专用DTO
   - 使用继承抽取公共字段
   - 校验规则要完整和准确
   - 命名要清晰体现用途

3. **VO约束设计**：
   - 根据展示需求设计字段
   - 提供计算属性增强展示
   - 按权限级别分别设计
   - 考虑性能优化需求

4. **对象转换约束**：
   - 统一使用Hutool BeanUtils
   - 提供转换器工具类
   - 批量转换使用Stream API
   - 更新时忽略空值

### 常见问题约束解决

| 问题 | 约束解决方案 |
|------|----------|
| 字段重复过多 | 使用继承或组合抽取公共DTO |
| 校验规则复杂 | 使用@AssertTrue自定义校验方法 |
| 权限控制复杂 | 设计不同权限级别的VO |
| 转换逻辑复杂 | 使用转换器工具类统一管理 |
| 性能问题 | 列表查询使用精简VO，避免过度查询 |
| PO命名不规范 | 严格执行PO后缀命名约束 |
| 转换工具混用 | 统一使用Hutool BeanUtils约束 |

## 📋 使用检查约束清单

### PO命名约束检查

- [ ] 所有数据库实体类是否以`PO`结尾？
- [ ] 命名是否清晰表达业务含义？
- [ ] 是否继承了`BasePO`基类？
- [ ] 表名映射是否使用`@TableName`注解？

### 对象拷贝约束检查

- [ ] 是否使用`cn.hutool.core.bean.BeanUtils`进行对象拷贝？
- [ ] 是否避免了手动逐字段赋值？
- [ ] 批量转换是否使用Stream API？
- [ ] 更新操作是否忽略了空值？
- [ ] 是否避免了Spring BeanUtils和Apache BeanUtils？
- [ ] 是否避免了MapStruct等其他转换工具？

### DTO/VO设计约束检查

- [ ] DTO是否只用于入参，VO是否只用于出参？
- [ ] 校验注解是否完整和准确？
- [ ] 计算属性是否正确实现？
- [ ] 权限控制是否合理？

### 性能优化约束检查

- [ ] 大批量转换是否考虑了性能影响？
- [ ] 是否合理使用了忽略字段功能？
- [ ] 转换逻辑是否可以复用？

---

