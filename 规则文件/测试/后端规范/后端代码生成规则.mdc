---
description: 
globs: 
alwaysApply: false
---
# 后端代码生成规则 - 主规则

## 🎯 规则体系概览

本规则体系为后端代码生成提供完整的指导规范，已按照功能和职责拆分为以下专项规则文件：

### 📋 规则文件导航

| 规则文件 | 主要内容 | 适用场景 |
|---------|----------|----------|
| **[基础架构规则](mdc:dms/dms/dms/dms/dms/后端代码生成规则-基础架构.mdc)** | 项目结构、命名规范、MyBatis Plus配置、事务管理 | 项目初始化、架构设计、数据库配置 |
| **[代码模板规则](mdc:dms/dms/dms/dms/dms/后端代码生成规则-代码模板.mdc)** | Controller、Service、Entity模板和具体实现示例 | 代码生成、开发规范、具体实现 |
| **[对象约束规范](mdc:dms/dms/dms/dms/dms/后端代码生成规则-对象约束.mdc)** | DTO/VO/PO约束、命名规范、Hutool BeanUtils使用约束 | 接口设计、数据传输、对象转换 |
| **[质量检查规则](mdc:dms/dms/dms/dms/dms/后端代码生成规则-质量检查.mdc)** | 代码检查清单、质量标准 | 代码审查、质量保证 |

### 🔗 强制引用规范

**所有后端代码生成必须强制引用并遵循以下规则：**

1. **[后端代码通用组件规则.mdc](mdc:dms/dms/dms/dms/dms/dms/dms/后端代码通用组件规则.mdc)** - 统一响应、异常处理、多语言支持等基础设施
2. **[对象约束规范.mdc](mdc:dms/dms/dms/dms/dms/dms/dms/后端代码生成规则-对象约束.mdc)** - 强制PO命名约束和Hutool BeanUtils使用约束
3. **本规则体系中的所有专项规则** - 按需查阅对应的专项规则文件

### 🚀 重要更新说明

**PO命名和对象拷贝强制规范：**
- ✅ **强制PO命名规范**：所有数据库实体类必须以`PO`结尾，如`UserPO`、`OrderPO`、`ProspectiveCustomerPO`
- ✅ **强制使用Hutool BeanUtils**：VO/DTO/PO之间的对象拷贝必须使用`cn.hutool.core.bean.BeanUtils`
- 🚫 **禁止手动逐字段赋值**：禁止使用Spring BeanUtils、Apache BeanUtils或手动赋值
- ✅ **适用所有转换场景**：DTO→PO、PO→VO、DTO→DTO、VO→VO等所有转换场景

**统一POST方法和具体对象参数规范：**
- 🚫 **禁止使用GET/PUT/PATCH/DELETE方法**，所有接口统一使用POST
- 🚫 **禁止使用Map<String, Object>**作为入参或出参
- ✅ **强制使用具体DTO对象**作为所有接口参数
- ✅ **路径设计**：POST /xxx/list、POST /xxx/detail、POST /xxx/create等
- ✅ **参数封装**：分页参数、ID参数、更新参数都封装在专用DTO中

**逻辑删除和状态管理规范：**
- 🚫 **禁止显式处理isDeleted字段**：由MyBatis Plus自动处理
- ✅ **强制使用常量或枚举**：所有状态字段必须使用常量类或枚举类
- ✅ **统一异常处理**：使用BusinessException和ExceptionEnum

### 🛠️ 技术栈

- Java 8
- Spring Boot 2.2
- MyBatis Plus
- Redis
- MySQL
- dms-service-common (通用组件包)

### 📖 使用指南

1. **新项目开发**：按顺序阅读基础架构 → 数据库操作 → 代码模板规则
2. **接口设计**：重点查看对象约束规则
3. **代码审查**：使用质量检查规则进行验证
4. **问题排查**：在对应的专项规则文件中查找解决方案

### 🔄 规则更新

- 各专项规则文件独立维护和更新
- 主规则文件保持概览和导航功能
- 通过引用关系确保规则一致性

---

**💡 提示：** 建议将此主规则文件加入收藏，作为后端开发的快速导航入口。
