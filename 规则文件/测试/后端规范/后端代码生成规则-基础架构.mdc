---
description: 
globs: 
alwaysApply: true
---
# 后端代码生成规则 - 基础架构

> **规则说明：** 本文件定义后端项目的基础架构规范，包括项目结构、命名规范、组件引用等。
> 
> **引用关系：** 本规则强制引用 [后端代码通用组件规则.mdc](mdc:dms/dms/dms/dms/dms/dms/dms/dms/后端代码通用组件规则.mdc)

## 🔗 强制引用规范

**本规则强制引用并遵循 `后端代码通用组件规则.mdc` 中定义的所有规范**，包括但不限于：
- 统一响应处理体系（Result<T>、ResponseBodyAdvice）
- 异常处理体系（ExceptionEnum、BusinessException、GlobalExceptionHandler）
- 多语言支持体系（I18nUtils、I18nService、多语言配置、数据库表结构等）
- 数据库基础设施（BasePO、MyMetaObjectHandler）
- 链路追踪支持（TraceIdResponseFilter）
- 工具类支持（ApplicationContextHelper）

**所有生成的代码必须严格遵循通用组件规则中的强制使用规范和禁止事项。**

## 🛠️ 技术栈

- **Java 8** - 基础开发语言
- **Spring Boot 2.2** - 应用框架
- **MyBatis Plus** - 数据库操作框架
- **Redis** - 缓存和分布式锁
- **MySQL** - 关系型数据库
- **EasyPOI** - Excel文件导入导出
- **dms-service-common** - 通用组件包，提供完整的基础设施支持

## 📁 项目结构规范

### 包结构标准

```
src/main/java/com/example/project/
├── constants/       # 通用模块
│   ├── CommonConstants.java    # 通用常量
│   ├── ErrorCode.java         # 错误码枚举
│   ├── ResponseCode.java      # 响应码枚举
│   └── BusinessType.java      # 业务类型枚举
├── config/         # 配置类
│   ├── CorsConfig.java        # 跨域配置
│   ├── RedisConfig.java       # Redis配置
│   ├── MybatisPlusConfig.java # MyBatis Plus配置
│   ├── I18nConfig.java        # 多语言配置（由通用组件提供）
│   └── WebMvcConfig.java      # Web配置
├── controller/     # 控制器层
├── dto/           # 数据传输对象
├── entity/        # 实体类
├── mapper/        # Mapper接口
├── service/       # 业务逻辑层
│   ├── impl/      # 实现类
├── vo/            # 视图对象
├── convert/       # 转换器
└── exception/     # 异常处理
    ├── GlobalExceptionHandler.java
    └── BusinessException.java
```
### 包结构设计原则
- **职责单一**：每个包专注特定功能领域
- **分层清晰**：严格按照三层架构组织代码
- **便于维护**：相关功能聚合，降低耦合度
- **扩展友好**：支持业务模块的独立扩展
## 📝 命名规范
### 类命名规范
| 类型 | 命名规范 | 示例 | 说明 |
|------|----------|------|------|
| **Controller** | `{业务名}Controller` | `UserController` | 控制器类 |
| **Service接口** | `{业务名}Service` | `UserService` | 业务接口 |
| **Service实现** | `{业务名}ServiceImpl` | `UserServiceImpl` | 业务实现类 |
| **Entity** | `{表名对应的实体名}` | `User` | 实体类 |
| **DTO** | `{业务名}{操作}DTO` | `UserCreateDTO`、`UserUpdateDTO` | 数据传输对象 |
| **VO** | `{业务名}VO` | `UserVO`、`UserDetailVO` | 视图对象 |
| **Mapper** | `{实体名}Mapper` | `UserMapper` | 数据访问接口 |
| **Convert** | `{业务名}Convert` | `UserConvert` | 转换器 |
| **Enum** | `{业务名}{含义}Enum` | `UserStatusEnum` | 枚举类 |
| **Constants** | `{业务名}Constants` | `UserConstants` | 常量类 |
### 方法命名规范
| 操作类型 | 命名规范 | 示例 | 说明 |
|----------|----------|------|------|
| **查询** | `get{实体名}`、`list{实体名}`、`page{实体名}` | `getUserById`、`listUsers`、`pageUsers` | 查询操作 |
| **创建** | `create{实体名}` | `createUser` | 创建操作 |
| **更新** | `update{实体名}` | `updateUser` | 全量更新 |
| **部分更新** | `patch{实体名}` | `patchUser` | 部分更新 |
| **删除** | `delete{实体名}`、`batchDelete{实体名}` | `deleteUser`、`batchDeleteUsers` | 删除操作 |
| **校验** | `validate{含义}`、`check{含义}` | `validateUserData`、`checkUserExists` | 校验操作 |
| **转换** | `to{目标类型}` | `toVO`、`toEntity` | 类型转换 |
### 接口路径命名规范
| 操作 | 路径规范 | 示例 | 说明 |
|------|----------|------|------|
| **分页查询** | `POST /{业务}/list` | `POST /user/list` | 分页列表 |
| **详情查询** | `POST /{业务}/detail` | `POST /user/detail` | 查询详情 |
| **创建** | `POST /{业务}/create` | `POST /user/create` | 创建操作 |
| **更新** | `POST /{业务}/update` | `POST /user/update` | 全量更新 |
| **部分更新** | `POST /{业务}/patch` | `POST /user/patch` | 部分更新 |
| **删除** | `POST /{业务}/delete` | `POST /user/delete` | 单个删除 |
| **批量删除** | `POST /{业务}/batch-delete` | `POST /user/batch-delete` | 批量删除 |
| **导出** | `POST /{业务}/export` | `POST /user/export` | 数据导出 |
## 🏗️ dms-service-common 组件能力
> **详细组件能力说明请参考：** [后端代码通用组件规则.mdc](mdc:dms/dms/dms/dms/dms/dms/dms/dms/后端代码通用组件规则.mdc)
### 核心能力概述
| 组件类型 | 主要功能 | 使用方式 |
|----------|----------|----------|
| **统一响应处理** | Result<T>、ResponseBodyAdvice | 自动包装返回结果 |
| **异常处理体系** | ExceptionEnum、BusinessException、GlobalExceptionHandler | 统一异常处理 |
| **多语言支持** | I18nUtils、I18nService | 国际化消息处理 |
| **数据库基础设施** | BasePO、MyMetaObjectHandler | 实体基类和字段自动填充 |
| **链路追踪支持** | TraceIdResponseFilter | 请求链路跟踪 |
| **工具类支持** | ApplicationContextHelper | Spring上下文工具 |
### 关键使用要点
1. **实体类必须继承BasePO**：不要重复定义基础字段（id、createTime、updateTime等）
2. **异常处理使用BusinessException和ExceptionEnum**：统一异常抛出和处理
3. **响应格式统一使用Result<T>**：自动包装返回结果
4. **多语言消息使用I18nUtils**：支持国际化消息处理
5. **不要手动处理isDeleted字段**：MyBatis Plus自动处理逻辑删除
6. **单表CRUD使用MyBatis Plus基础能力**：不编写自定义SQL
7. **Mapper继承BaseMapper，Service继承IService**：充分利用框架能力
## 🎯 MyBatis Plus 配置规范
### 强制使用规范
- ✅ **强制使用MyBatis Plus基础能力**：所有单表的增删改查必须使用MyBatis Plus提供的基础方法实现
- ✅ **禁止手动编写单表SQL**：不允许为单表操作编写自定义SQL语句  
- ✅ **默认不生成XML文件**：没有关联查询需求的Mapper接口，默认不生成对应的XML映射文件
- ✅ **复杂查询才使用XML**：只有涉及多表关联、复杂条件查询、统计分析等场景才编写XML
- 🚫 **禁止显式处理isDeleted字段**：逻辑删除字段由MyBatis Plus自动处理，代码中不需要显式添加该字段条件
### 允许使用XML的场景
- ✅ **多表关联查询**：涉及JOIN操作的复杂查询
- ✅ **复杂统计分析**：GROUP BY、聚合函数等统计查询  
- ✅ **性能优化查询**：需要手动优化的特殊SQL
- ✅ **存储过程调用**：调用数据库存储过程
- ✅ **批量操作优化**：特殊的批量插入、更新逻辑
### MyBatis Plus禁止事项
- 🚫 **禁止为单表CRUD编写XML**：如 selectById、insert、updateById、deleteById等
- 🚫 **禁止重复实现基础方法**：BaseMapper和IService已提供完整的基础能力
- 🚫 **禁止在简单查询中使用@Select注解**：应使用Lambda查询构建条件
- 🚫 **禁止手动拼接SQL字符串**：使用Lambda查询保证SQL安全性
- 🚫 **禁止使用传统Wrapper方式**：推荐使用super.lambdaQuery()和super.lambdaUpdate()
- 🚫 **禁止显式处理isDeleted字段**：逻辑删除由框架自动处理
## 🔄 事务管理规范
### 强制规范
- ✅ **事务粒度控制**：事务的粒度要足够小，足够快。避免长时间占用数据库连接，减少锁竞争和死锁风险
- ✅ **嵌套事务处理**：嵌套事务需要包装所有的执行都成功。使用`@Transactional(rollbackFor = Exception.class)`确保所有异常都会回滚
- ✅ **三方调用顺序**：存在三方调用时，应当等三方调用完毕后再执行本地事务。先调用外部服务，成功后再进行本地数据操作
- ✅ **避免内部调用**：避免在类内部调用事务方法，这种不会生效，除非调用当前类的代理类
- ✅ **单条数据操作**：方法中只有仅一条数据的更新/修改/删除（查询不算），无需使用事务，需对直接结果进行判断是否执行成功
- 🚫 **禁止在类上加事务注解**：类上加注解导致类上的所有方法都会启动事务，事务被滥用，且无限拉长
### 事务禁止事项
- 🚫 **禁止事务过大**：单个事务不要包含过多业务逻辑，避免长时间锁定资源
- 🚫 **禁止在事务内调用缓慢的外部服务**：先完成外部调用，再开启事务
- 🚫 **禁止类内部直接调用事务方法**：必须通过Spring代理对象调用
- 🚫 **禁止忽略单操作的返回结果**：即使不用事务，也要检查操作是否成功
- 🚫 **禁止忽略异常类型配置**：必须配置rollbackFor = Exception.class
### 注解使用规范
- ✅ **强制使用注解方式**：分布式锁和幂等必须使用@DistributedLock和@Idempotent注解
- ✅ **支持注解组合**：同一方法可以同时使用多个注解
- ✅ **自动管理**：锁的获取、释放和异常处理由AOP切面自动管理
- 🚫 **禁止手动实现**：禁止手动使用RedisTemplate、Redisson等实现相关逻辑
## 🚫 架构禁止事项
### 强制禁止
1. **禁止绕过统一响应处理**：所有Controller方法必须返回Result<T>类型
2. **禁止绕过异常处理体系**：业务异常必须使用BusinessException
3. **禁止重复实现基础设施**：不要重复实现已有的通用组件功能
4. **禁止违反包结构规范**：严格按照定义的包结构组织代码
5. **禁止使用非标准命名**：必须遵循命名规范，保持代码风格一致
### 架构约束
1. **依赖方向约束**：Controller → Service → Mapper，禁止反向依赖
2. **层级职责约束**：每层只处理对应职责，不得越界
3. **组件引用约束**：必须使用dms-service-common提供的组件
4. **注解使用约束**：优先使用注解方式，减少手动编码
## 📝 Swagger文档规范
### 强制使用规范
- ✅ **所有对象都需要添加Swagger注解**：所有DTO、VO、Entity等数据对象必须添加@ApiModel和@ApiModelProperty注解
- ✅ **所有Controller需要添加Swagger注解**：所有控制器类和方法必须添加完整的Swagger文档注解
- ✅ **接口文档完整性**：每个接口必须包含完整的请求参数、响应结果、错误码说明

## ⏰ 时间格式规范
### 强制使用规范
- ✅ **所有时间格式使用java.util.Date类型**：禁止使用LocalDateTime、LocalDate等Java 8时间类型
- ✅ **统一时间格式化**：所有时间字段必须使用统一的格式化注解
- ✅ **时区统一处理**：所有时间处理必须考虑时区问题，统一使用GMT+8
### 时间字段规范
### 时间格式禁止事项
- 🚫 **禁止使用LocalDateTime**：统一使用java.util.Date类型
- 🚫 **禁止使用LocalDate**：日期字段也使用java.util.Date类型
- 🚫 **禁止使用Timestamp**：避免使用java.sql.Timestamp
- 🚫 **禁止不同的时间格式**：必须使用统一的时间格式化模式
- 🚫 **禁止忽略时区设置**：所有时间处理必须考虑时区问题
## 📊 文件导入导出规范
### 强制使用EasyPOI
所有Excel文件的导入导出操作必须使用EasyPOI实现，禁止使用Apache POI、阿里EasyExcel等其他Excel处理框架。
### 依赖配置
```xml
<dependency>
    <groupId>cn.afterturn</groupId>
    <artifactId>easypoi-spring-boot-starter</artifactId>
    <version>3.3.0</version>
</dependency>
```
### EasyPOI使用规范
#### 强制规范
- ✅ **强制使用EasyPOI**：所有Excel操作必须使用EasyPOI，禁止使用其他Excel框架
- ✅ **注解配置完整**：Excel实体类必须使用@Excel注解完整配置字段属性
- ✅ **文件大小限制**：导入文件大小不超过10MB，导出数据量不超过10万条
- ✅ **异常处理统一**：Excel操作异常统一使用BusinessException包装
- ✅ **数据校验完整**：导入数据必须进行完整的业务校验和去重处理
#### 禁止事项
- 🚫 **禁止使用Apache POI**：不允许直接使用Apache POI进行Excel操作
- 🚫 **禁止使用阿里EasyExcel**：统一使用EasyPOI框架
- 🚫 **禁止忽略数据校验**：导入数据必须进行完整校验
- 🚫 **禁止大数据量同步导出**：超过1万条数据必须使用异步导出
- 🚫 **禁止忽略文件格式校验**：必须校验文件格式和大小
#### 最佳实践

