### Service 设计规范 (V2.0 - 公司标准版)

本规范定义了Service层的设计标准。采用Controller → Service → Mapper三层架构，Service层负责业务逻辑处理。

#### 1. 职责与定位
- **Service 层 (业务逻辑层)**
  - **定位**: 核心业务逻辑的处理者。负责处理业务规则、数据校验、事务管理等。
  - **职责**:
    - 处理业务逻辑和业务规则。
    - 负责事务管理和数据一致性。
    - 直接调用`Mapper`层进行数据操作。
    - 使用`Hutool BeanUtils`进行对象转换。
    - 集成分布式锁、幂等性等基础设施。

#### 2. 接口化编程
- **强制接口先行**：所有Service**必须**定义接口及其实现类。
- **面向接口注入**：Controller层**必须**通过依赖注入的方式注入Service接口，而不是具体实现类。
- **继承MyBatis Plus能力**：Service接口必须继承`IService<T>`，实现类必须继承`ServiceImpl<Mapper, Entity>`。

#### 3. 命名规范
- **Service层**:
  - **接口**: `{业务领域}Service` (例如: `UserService`, `OrderService`)。
  - **实现**: `{业务领域}ServiceImpl` (例如: `UserServiceImpl`, `OrderServiceImpl`)。
- **实体类**:
  - **数据库实体**: 必须以`PO`结尾 (例如: `UserPO`, `OrderPO`)。
  - **必须继承**: 所有实体类必须继承`BasePO`。
- **数据传输对象**:
  - **DTO对象**: 必须以`DTO`结尾 (例如: `UserCreateDTO`, `OrderUpdateDTO`)。
  - **VO对象**: 必须以`VO`结尾 (例如: `UserVO`, `OrderDetailVO`)。
- **方法命名**: 使用动宾结构，清晰表达其业务动作 (例如: `createOrder`, `getUserById`)。

#### 4. 事务管理
- **事务边界**: 事务管理**必须**在Service层的`public`方法上通过`@Transactional`注解进行。
- **强制配置**: 必须配置`rollbackFor = Exception.class`确保所有异常都会回滚。
- **只读事务**: 对于纯查询操作，应明确指定为只读事务 (`@Transactional(readOnly = true)`)。
- **禁止事项**: 禁止在类上加事务注解，禁止事务过大，禁止在事务内调用缓慢的外部服务。

#### 5. 对象转换
- **强制使用Hutool BeanUtils**: `PO` (持久化对象) 与 `DTO` (数据传输对象) 之间的转换**必须**使用`cn.hutool.core.bean.BeanUtils`。
- **数据流向**:
  1. Service从Mapper获取`PO`对象。
  2. 使用`BeanUtils.copyProperties(po, dto)`将`PO`转换为`DTO`。
  3. Service处理业务逻辑后返回给Controller。
  4. 反之，Service接收上层`DTO`，使用`BeanUtils.copyProperties(dto, po)`转为`PO`后保存。
- **禁止使用**: 禁止使用Spring BeanUtils、Apache BeanUtils、MapStruct或手动逐字段赋值。
- **适用场景**: DTO→PO、PO→VO、DTO→DTO、VO→VO等所有转换场景。

#### 6. 异常处理
- **抛出业务异常**: 当业务规则不满足时，应抛出公司基础架构中定义的`BusinessException`。
- **使用异常枚举**: 错误码必须使用`ExceptionEnum`中定义的枚举值。
- **异常位置**: `BusinessException`通常在Service层中，根据业务校验逻辑的位置被抛出。
- **禁止吞并异常**: 严禁在方法内部`try-catch`一个异常后不做任何处理。异常应统一向上抛出，最终由全局异常处理器捕获。

#### 7. 基础设施集成
- **分布式锁**: 涉及并发访问共享资源的方法必须使用`@DistributedLock`注解。
- **幂等性**: 所有可能重复提交的业务操作必须使用`@Idempotent`注解。
- **业务编号**: 所有业务单号必须通过`BusinessCodeService.getCode()`方法生成。
- **时间类型**: 所有时间字段必须使用`java.util.Date`类型，禁止使用LocalDateTime。

#### 8. 服务复用与交互
- **逻辑复用**：可复用的业务逻辑应抽取为`private`方法或独立的工具类。
- **服务调用**：允许一个Service调用另一个Service的方法来组合更复杂的业务流程。注意避免循环依赖。