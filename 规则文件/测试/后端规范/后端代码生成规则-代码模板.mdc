---
description: 
globs: 
alwaysApply: true
---
# 后端代码生成规则 - 代码模板

> **规则说明：** 本文件定义各层代码生成模板，包括Controller、Service、Entity、Mapper等。
> 
> **引用关系：** 本规则引用 [基础架构规则](mdc:dms/dms/dms/dms/dms/后端代码生成规则-基础架构.mdc)、[数据库操作规则](mdc:dms/dms/dms/dms/dms/后端代码生成规则-数据库操作.mdc) 和 [后端代码通用组件规则.mdc](mdc:dms/dms/dms/dms/dms/后端代码通用组件规则.mdc)

## 🎯 模板使用说明

### 模板变量说明

| 变量 | 说明 | 示例 |
|------|------|------|
| `{业务名}` | 业务实体名称，首字母大写 | `User`、`Order` |
| `{业务名小写}` | 业务实体名称，首字母小写 | `user`、`order` |
| `{业务模块复数}` | 业务模块复数形式，用于URL | `users`、`orders` |

### 模板原则

- ✅ **统一RESTful API的方法**
- ✅ **具体对象参数**：入参和出参都使用具体的DTO/VO对象
- ✅ **充分利用MyBatis Plus**：继承BaseMapper和IService
- ✅ **异常统一处理**：使用BusinessException和ExceptionEnum
- ✅ **注解增强**：使用@DistributedLock和@Idempotent注解

## 📝 模板使用规范

### 强制规范

1. **必须继承基类**：
   - Entity必须继承BasePO
   - Mapper必须继承BaseMapper<T>
   - Service接口必须继承IService<T>
   - Service实现必须继承ServiceImpl<Mapper, Entity>

2. **必须使用注解增强**：
   - 分布式锁使用@DistributedLock注解
   - 幂等控制使用@Idempotent注解
   - 事务控制使用@Transactional注解

3. **必须使用MyBatis Plus能力**：
   - 查询使用super.lambdaQuery()
   - 更新使用super.lambdaUpdate()
   - 分页使用Page和IPage

4. **必须统一异常处理**：
   - 业务异常使用BusinessException
   - 错误码使用ExceptionEnum
   - 不允许直接throw RuntimeException

### 禁止事项

- 🚫 **禁止重复定义基础字段**：Entity中不要定义id、createTime等BasePO已有字段
- 🚫 **禁止重复实现基础方法**：Service中不要重复实现save、getById等IService已有方法
- 🚫 **禁止手动处理isDeleted**：逻辑删除由MyBatis Plus自动处理
- 🚫 **禁止使用Map作为参数**：必须使用具体的DTO/VO对象

---

