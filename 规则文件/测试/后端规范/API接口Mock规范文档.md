# 后端 Mock 服务开发规范 (V1.1)

## 1. 核心原则

1.  **非侵入性 (Non-Invasive)**: Mock 逻辑不能侵入或污染真实的业务逻辑。业务代码应该“不知道” Mock 的存在。
2.  **配置驱动 (Configuration-Driven)**: 所有 Mock 的启用/禁用必须通过外部配置文件（`.yml` 或 `.properties`）控制，严禁通过修改代码来切换。
3.  **契约一致 (Consistent-Contract)**: Mock 数据必须严格遵守与真实服务相同的服务契约，即共享相同的 **DTO (Data Transfer Object)** 和 **服务接口 (Service Interface)**。
4.  **逻辑分离 (Separation of Concerns)**: Mock 数据的生成逻辑必须与业务逻辑实现完全分离，存放在独立的、专门的包和类中。

## 2. 代码结构规范

所有 Mock 相关的代码必须统一存放在 `src/main/java/{...}/mock` 包路径下。

```
src/main/java/com/perodua/bff/pc/
└── mock/
    ├── provider/
    │   └── MockDataProvider.java       // 核心：Mock数据提供者
    └── util/
        └── MockDataGenerator.java      // (可选) 复杂或随机数据生成工具
```

*   **`mock.provider.MockDataProvider`**:
    *   **职责**: 作为所有 Mock 数据的统一出口。它是一个 Spring 组件 (`@Component`)。
    *   **方法命名**: 方法名应清晰地反映其用途，建议使用 `getMock{FeatureName}` 格式。例如：`getMockOrderDetail()`、`getMockCustomerList()`。
    *   **实现**: 方法内部负责组装并返回一个填充了假数据的、符合接口定义的 **响应 DTO**。对于简单场景，可以直接 `new Dto()` 并 `set` 值。对于复杂场景，可以调用 `MockDataGenerator`。

*   **`mock.util.MockDataGenerator`**:
    *   **职责**: (可选) 用于创建复杂的、随机的或可复用的假数据。例如，生成随机姓名、日期、地址，或根据输入参数动态创建列表。
    *   **定位**: 这是一个工具类，其方法通常是 `static` 的，不作为 Spring Bean 管理。

## 3. 服务实现层规范

在服务实现类中（如 `OrderServiceImpl.java`），集成 Mock 逻辑必须遵循以下模式：

1.  **注入依赖**: 同时注入真实的下游服务客户端（如 `SalesServiceClient`）和 `MockDataProvider`。

2.  **定义开关**:
    *   使用 `@Value` 注解从配置文件中读取一个布尔类型的 Mock 开关。
    *   开关命名必须有清晰的层级结构，格式为 `mock.feature.{feature-group}.{feature-name}.enabled`。
    *   必须提供默认值 `false`，以保证在配置缺失时，默认执行真实逻辑。
    *   **示例**: `@Value("${mock.feature.order.detail.enabled:false}") private boolean mockOrderDetailEnabled;`

3.  **实现切换逻辑**:
    *   在业务方法的入口处，使用 `if` 语句检查 Mock 开关。
    *   如果开关为 `true`，则调用 `MockDataProvider` 的对应方法并立即 `return`。
    *   `if` 代码块之后的部分，是且仅是真实的业务逻辑。

## 4. 配置文件规范

所有 Mock 开关必须在开发环境的配置文件（如 `bootstrap-dev.yml`）中进行统一管理。

1.  **层级结构**: 使用清晰的 YAML 层级来组织开关，便于查找和管理。

    ```yaml
    mock:
      feature:
        order: # 按业务模块/功能域分组
          detail:
            enabled: true # 控制订单详情接口
          list:
            enabled: false # 控制订单列表接口
    ```

2.  **注释清晰**: 对于复杂的 Mock 场景，应在配置文件中添加注释，说明该开关控制的具体接口和 Mock 场景。

## 5. Mock 数据质量规范

1.  **数据完整性**: Mock DTO 对象的所有字段都应被赋值，除非业务上明确允许为 `null`。
2.  **数据多样性**: 鼓励提供多种 Mock 场景，例如正常情况、返回空列表的边界情况等。
3.  **数据真实性**: Mock 数据应尽可能模拟真实世界的数据格式和范围，避免使用无意义的占位符（如 `"test"`, `"123"`）。

## 6. Mock 数据生成时机

*   **实时生成 (Real-time Generation)**: Mock 数据是在 **API 被调用的那一刻，按需实时生成**的。它不是在应用启动时预先创建并缓存的。
*   **请求驱动 (Request-Driven)**: 每当一个被 Mock 的 API 端点被请求时，服务层 (`Service Impl`) 的判断逻辑会生效，并调用 `MockDataProvider`。此时，`MockDataProvider` 才会创建一个新的 DTO 对象，填充假数据，并将其返回。每一次 API 调用都会触发一次全新的 Mock 数据生成过程。

## 7. 工作流程

1.  **开发新接口**:
    1.  在 `Service Interface` 中定义新方法。
    2.  在 `MockDataProvider` 中创建对应的 Mock 数据生成方法。
    3.  在 `Service Impl` 中添加 Mock 开关和切换逻辑。
    4.  在 `.yml` 文件中添加并开启该接口的 Mock 开关 (`enabled: true`)。
2.  **完成真实接口**:
    1.  后端开发者在 `Service Impl` 中完成真实业务逻辑。
    2.  在 `.yml` 文件中将对应开关关闭 (`enabled: false`) 并重启应用。
3.  **清理 Mock (可选)**:
    1.  项目稳定后，可统一移除 `Service Impl` 中的 Mock 判断逻辑、`MockDataProvider` 的注入和相关方法，以及 `.yml` 中的配置项。
