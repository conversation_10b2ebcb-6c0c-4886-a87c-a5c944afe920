# 质量评审工作流设计文档（Review Workflow）

## 工作流概述

本评审工作流专门用于对基于线框图的前后端设计流程中每个阶段的产出物进行质量评审，确保每个环节的输出符合标准，为后续步骤提供高质量的输入。

---

## 评审工作流架构

### **RW01 - 文档预处理与输入验证**

**目标**：验证输入文档的完整性和可读性，准备评审环境

**输入要求**：
- 待评审的阶段产出物（P01-P06或C01-C02）
- 对应的输入依赖文档
- 相关的规范文件

**处理流程**：
1. **文档完整性检查**：
   - 验证所有必需的输入文档是否存在
   - 检查文档格式是否符合markdown规范
   - 确认文档内容是否可读取和解析

2. **版本一致性验证**：
   - 确认输入文档版本的时间戳
   - 验证文档间的引用关系是否正确
   - 检查是否存在循环依赖

3. **评审环境准备**：
   - 加载相关的规范文件到评审上下文
   - 初始化评审评分系统
   - 准备评审报告模板

**产出**：
- 输入验证报告
- 评审环境配置确认
- 准备就绪状态确认

### **RW02 - 多维度质量分析**

**目标**：从五个核心维度对产出物进行深度分析和评分

**输入**：
- 经过预处理的待评审文档
- 评审上下文环境
- 阶段特定的评审标准

**处理流程**：

#### **2.1 完整性分析（Completeness Analysis）**
```
输入检查项：
- 是否覆盖了所有必需的输入要求
- 是否遗漏关键业务场景或技术点
- 是否满足后续步骤的输入需求
- 边界条件和异常情况是否考虑完整

评分权重：根据阶段类型动态调整（20-40%）
```

#### **2.2 准确性分析（Accuracy Analysis）**
```
检查重点：
- 业务逻辑理解是否正确
- 技术实现方案是否可行
- 数据结构设计是否合理
- 与需求文档的匹配度

评分权重：25-35%
```

#### **2.3 一致性分析（Consistency Analysis）**
```
对比检查：
- 与前序文档的对齐度
- 内部逻辑的自洽性
- 术语使用的统一性
- 数据流的连贯性

评分权重：15-25%
```

#### **2.4 规范性分析（Compliance Analysis）**
```
规范检查：
- 是否遵循项目规范文件
- 文档格式是否标准
- 命名规则是否统一
- API设计是否符合RESTful原则

评分权重：15-25%
```

#### **2.5 可实现性分析（Feasibility Analysis）**
```
可行性评估：
- 技术方案是否可行
- 复杂度是否合理
- 是否考虑了技术约束
- 性能和扩展性考虑

评分权重：15-25%
```

**产出**：
- 五维度详细分析报告
- 各维度量化评分
- 问题点识别和分类

### **RW03 - 阶段特定评审**

**目标**：根据不同阶段的特点进行专项评审

#### **阶段特定评审标准**

**P01评审 - 页面元素与数据分析**
```yaml
专项检查点:
  UI元素遍历: 
    - 线框图中每个UI元素是否都已识别
    - 交互元素的行为定义是否明确
  数据溯源:
    - 每个数据项的来源是否明确
    - 展示数据与提交数据是否区分
  状态覆盖:
    - 是否考虑空数据、加载中、错误等状态
    - 边界条件处理是否完整
  术语一致性:
    - 是否使用PI04术语表中的标准命名
```

**P02评审 - 领域实体模型**
```yaml
专项检查点:
  复用优先:
    - 是否最大化复用现有数据库表结构
    - 新增实体的必要性论证
  模型设计:
    - 实体属性定义是否合理
    - 数据类型和约束是否符合规范
  关系正确性:
    - 实体间关系是否准确定义
    - 外键关联是否合理
  兼容性:
    - 数据库变更对现有系统的影响评估
```

**P03评审 - API接口契约**
```yaml
专项检查点:
  RESTful规范:
    - HTTP方法使用是否正确
    - URL路径设计是否符合REST原则
  DTO设计:
    - 请求响应结构是否精确匹配前端需求
    - 字段命名是否与数据库字段一致
  数据字典:
    - 枚举字段是否完整定义
    - 字典编码是否按业务域分类
  分页统一:
    - 是否使用pageNum/pageSize标准参数
    - 响应是否使用MyBatis Plus的Page格式
```

**P04评审 - 后端详细设计**
```yaml
专项检查点:
  架构设计:
    - Service层划分是否符合业务边界
    - Controller层是否保持薄层原则
  设计模式:
    - 设计模式应用是否恰当
    - 是否存在过度设计问题
  事务和并发:
    - 事务边界定义是否合理
    - 并发控制策略是否充分
  API对齐:
    - 与P03接口契约是否完全一致
    - HTTP方法和参数传递方式是否匹配
```

**P05评审 - 前端设计文档**
```yaml
专项检查点:
  组件设计:
    - 组件划分是否合理
    - 目录结构是否符合规范
  类型定义:
    - TypeScript类型定义是否完整
    - 是否与API契约的DTO对应
  状态管理:
    - 状态流转是否与后端业务逻辑匹配
    - 数据流向是否清晰
  国际化:
    - i18n实现是否完整
    - 是否遵循国际化规范
```

**P06评审 - Mock文档**
```yaml
专项检查点:
  业务逻辑:
    - Mock是否反映真实业务逻辑
    - 状态流转是否与P04设计一致
  场景覆盖:
    - 是否覆盖P01分析的所有前端场景
    - 边界和异常情况是否充分模拟
  数据质量:
    - Mock数据是否具有业务真实性
    - 数据间关联关系是否正确
```

### **RW04 - 问题分级与改进建议**

**目标**：对发现的问题进行分级，并提供具体的改进建议

**处理流程**：

#### **4.1 问题分级**
```
严重问题（Critical）- 影响等级：高
- 阻塞后续步骤执行的问题
- 违反核心业务逻辑的问题  
- 严重不符合规范的问题

重要问题（Major）- 影响等级：中
- 影响产出物质量的问题
- 可能导致后续返工的问题
- 部分不符合规范的问题

一般问题（Minor）- 影响等级：低
- 优化建议类问题
- 文档格式或描述问题
- 可改进但不影响功能的问题
```

#### **4.2 改进建议生成**
```
针对每个问题提供：
- 问题具体描述
- 问题产生原因分析
- 具体改进建议
- 修改优先级
- 预期改进效果
```

### **RW05 - 综合评分与通过判定**

**目标**：基于多维度分析结果生成综合评分和通过建议

**评分计算公式**：
```
综合评分 = Σ(维度评分 × 维度权重) × 阶段调整系数

通过标准：
- 90-100分：优秀，可直接进入下一阶段
- 80-89分：良好，建议优化后进入下一阶段  
- 70-79分：合格，需要针对性改进
- 60-69分：需要重大修改后重新评审
- 60分以下：不合格，建议重新制作
```

**判定逻辑**：
```
自动通过: 综合评分≥80分 且 无严重问题
条件通过: 综合评分≥70分 且 严重问题≤1个
需要修改: 综合评分≥60分 或 严重问题≤3个
重新制作: 综合评分<60分 或 严重问题>3个
```

### **RW06 - 评审报告生成**

**目标**：生成标准化的评审报告，为决策提供依据

## 评审报告模板

```markdown
# 质量评审报告

## 基本信息
- **评审阶段**: [P01/P02/P03/P04/P05/P06/C01/C02]
- **评审时间**: [YYYY-MM-DD HH:mm:ss]
- **评审版本**: [文档版本号]
- **评审者**: [Agent名称/版本]

## 综合评分
**总分**: XX/100分
**评审结果**: [优秀/良好/合格/需要修改/不合格]
**通过建议**: [自动通过/条件通过/需要修改/重新制作]

## 维度评分详情
| 评审维度 | 得分 | 权重 | 加权得分 | 评级 |
|---------|------|------|----------|------|
| 完整性 | XX/100 | XX% | XX | [优秀/良好/合格/不合格] |
| 准确性 | XX/100 | XX% | XX | [优秀/良好/合格/不合格] |
| 一致性 | XX/100 | XX% | XX | [优秀/良好/合格/不合格] |
| 规范性 | XX/100 | XX% | XX | [优秀/良好/合格/不合格] |
| 可实现性 | XX/100 | XX% | XX | [优秀/良好/合格/不合格] |

## 优点总结
- ✅ [具体优点1]
- ✅ [具体优点2]
- ✅ [具体优点3]

## 问题清单

### 🔴 严重问题 (Critical)
1. **[问题标题]** - 影响: 高
   - **描述**: [问题具体描述]
   - **位置**: [文档中的具体位置]
   - **影响**: [对后续步骤的影响]
   - **建议**: [具体改进建议]

### 🟡 重要问题 (Major)
1. **[问题标题]** - 影响: 中
   - **描述**: [问题具体描述]
   - **建议**: [改进建议]

### 🔵 一般问题 (Minor)
1. **[问题标题]** - 影响: 低
   - **描述**: [问题描述]
   - **建议**: [优化建议]

## 改进建议优先级
### 🚨 必须修改 (24小时内)
- [ ] [严重问题1的改进建议]
- [ ] [严重问题2的改进建议]

### ⚠️ 建议修改 (72小时内)
- [ ] [重要问题1的改进建议]
- [ ] [重要问题2的改进建议]

### 💡 优化建议 (可选)
- [ ] [一般问题1的改进建议]
- [ ] [一般问题2的改进建议]

## 阶段特定检查结果

### [根据评审阶段显示对应的专项检查结果]
**示例 - P03 API接口契约评审**:
- ✅ RESTful规范: 符合标准
- ⚠️ DTO设计: 部分字段命名不一致
- ✅ 数据字典: 完整定义
- ❌ 分页参数: 未使用标准参数

## 后续建议
**如果通过评审**:
- [进入下一阶段的建议]
- [需要注意的事项]

**如果未通过评审**:
- [修改重点]
- [重新评审的条件]
- [预期修改时间]

## 影响分析
**对后续步骤的影响**:
- [如果不修改问题，对后续步骤可能产生的影响]

**修改成本评估**:
- 预计修改时间: X小时
- 影响范围: [仅当前文档/涉及相关文档]
- 修改复杂度: [低/中/高]

---
**评审完成**: [YYYY-MM-DD HH:mm:ss]
**下次评审**: [如需重审的时间建议]
```

## 评审工作流使用指南

### **输入要求总览**

| 评审阶段 | 主要输入 | 依赖文档 | 规范文件 |
|---------|----------|----------|----------|
| **P01评审** | P01-页面元素与数据分析清单.md | PI01,PI02,PI03,PI04 | 术语表规范 |
| **P02评审** | P02-领域实体模型.md | P01,PI04数据库表结构 | PR01,PR08 |
| **P03评审** | P03-API接口契约.md | P01,P02 | PR02,PR08 |
| **P04评审** | P04-后端详细设计.md | P01-P03,PI01,PI02 | 后端规范文件组 |
| **P05评审** | P05-前端设计文档.md | P03,P04,PI03 | PR03-PR08 |
| **P06评审** | P06-后端接口mock文档.md | P01,P04 | PR09 |

### **评审触发方式**

#### **1. 手动触发评审**
```bash
# 示例命令格式
review --stage P01 --input "P01-页面元素与数据分析清单.md" --deps "PI01,PI02,PI03,PI04"
```

#### **2. 自动触发评审**
- 每个P阶段完成后自动启动评审
- 前一步骤未通过时，修改后自动重新评审
- 支持批量评审多个阶段

#### **3. 连续评审模式**
- 当某个阶段修改时，自动评审其对后续阶段的影响
- 提供影响链分析和建议

### **评审决策矩阵**

| 综合评分 | 严重问题数 | 决策结果 | 后续行动 |
|----------|------------|----------|----------|
| ≥90分 | 0 | 优秀通过 | 直接进入下一阶段 |
| 80-89分 | 0 | 良好通过 | 建议优化后进入下一阶段 |
| 70-79分 | ≤1 | 条件通过 | 修改严重问题后进入下一阶段 |
| 60-69分 | ≤3 | 需要修改 | 重大修改后重新评审 |
| <60分 | >3 | 不合格 | 建议重新制作 |

### **评审工作流的优势**

1. **标准化**: 统一的评审标准和流程
2. **可量化**: 明确的评分体系和通过标准  
3. **可追溯**: 完整的评审记录和问题跟踪
4. **可改进**: 基于评审结果持续优化流程
5. **高效性**: 自动化评审减少人工成本

这个评审工作流确保了每个阶段产出物的高质量，为整个开发流程提供了可靠的质量保障机制。

---

## 质量评审框架设计

### 一、核心评审维度

#### 1. **完整性维度 (Completeness)**
- 是否覆盖了所有输入要求
- 是否遗漏关键业务场景
- 是否满足后续步骤的输入需求

#### 2. **准确性维度 (Accuracy)**
- 业务逻辑理解是否正确
- 技术实现方案是否可行
- 数据结构设计是否合理

#### 3. **一致性维度 (Consistency)**
- 与前序文档的对齐度
- 内部逻辑的自洽性
- 术语使用的统一性

#### 4. **规范性维度 (Compliance)**
- 是否遵循项目规范文件
- 文档格式是否标准
- 命名规则是否统一

#### 5. **可实现性维度 (Feasibility)**
- 技术方案是否可行
- 复杂度是否合理
- 是否考虑了技术约束

### 二、分阶段评审标准

#### **P01 - 页面元素与数据分析清单**

**关键评审点：**
- **完整性**：是否遍历了线框图中的每个UI元素
- **准确性**：元素功能定义是否正确
- **边界覆盖**：是否考虑了所有状态（空数据、加载中、错误等）
- **数据溯源**：每个数据项是否明确了来源和用途

**评审权重：** 完整性40% + 准确性35% + 边界覆盖25%

#### **P02 - 领域实体模型**

**关键评审点：**
- **数据复用**：是否最大化复用现有表结构
- **模型合理性**：新增实体是否符合业务领域
- **关系正确性**：实体间关系定义是否准确
- **规范遵循**：是否符合数据库设计规范
- **向后兼容**：变更是否影响现有系统

**评审权重：** 数据复用30% + 模型合理性25% + 关系正确性25% + 规范遵循20%

#### **P03 - API接口契约**

**关键评审点：**
- **RESTful规范**：接口设计是否符合REST原则
- **DTO设计**：是否精确匹配前端需求
- **数据字典**：枚举字段是否完整定义
- **向后兼容**：API变更策略是否合理
- **分页统一**：是否使用标准分页参数

**评审权重：** RESTful规范25% + DTO设计30% + 数据字典20% + 兼容性25%

#### **P04 - 后端详细设计**

**关键评审点：**
- **架构合理性**：Service层划分是否符合业务边界
- **设计模式**：是否恰当应用设计模式（避免过度设计）
- **事务设计**：事务边界是否合理
- **并发处理**：并发控制策略是否充分
- **API一致性**：是否与P03接口契约完全对齐

**评审权重：** 架构合理性30% + API一致性25% + 事务设计20% + 并发处理25%

#### **P05 - 前端设计文档**

**关键评审点：**
- **组件设计**：组件划分是否合理
- **类型完整性**：TypeScript类型定义是否完整
- **状态管理**：状态流转是否与后端业务逻辑匹配
- **规范遵循**：是否遵循前端各项规范
- **国际化支持**：i18n实现是否完整

**评审权重：** 组件设计25% + 类型完整性20% + 状态管理30% + 规范遵循25%

#### **P06 - 后端接口Mock文档**

**关键评审点：**
- **业务逻辑一致性**：Mock是否反映真实业务逻辑
- **场景覆盖度**：是否覆盖所有前端场景需求
- **数据真实性**：Mock数据是否足够真实
- **边界处理**：异常和边界情况是否充分模拟

**评审权重：** 业务逻辑一致性35% + 场景覆盖度30% + 数据真实性35%

### 三、评审Agent的具体评分标准

#### **评分体系（0-100分）**

**90-100分：优秀**
- 所有关键点完全满足
- 超出基本要求，有创新性思考
- 文档质量高，逻辑清晰

**80-89分：良好**
- 关键点基本满足
- 个别细节需要完善
- 整体质量达标

**70-79分：合格**
- 核心要求满足
- 存在明显改进空间
- 需要针对性优化

**60-69分：需要修改**
- 关键缺陷较多
- 影响后续步骤执行
- 必须修改后才能继续

**60分以下：不合格**
- 严重缺陷，需要重做

### 四、评审触发机制

1. **自动触发**：每个P步骤完成后自动进行评审
2. **手动触发**：用户要求时进行专项评审
3. **连续评审**：前一步骤未通过时，修改后重新评审
4. **关联评审**：当某步骤修改时，评审其对后续步骤的影响

## 总结建议

**建议建立的评审Agent应具备以下能力：**

1. **深度理解能力**：能够理解业务需求和技术规范
2. **对比分析能力**：能够对比前后文档的一致性
3. **规范检查能力**：熟悉项目的各种规范文件
4. **质量评估能力**：能够进行量化的质量评分
5. **改进建议能力**：能够提供具体可执行的改进建议

**实施方式：**
- 可以训练专门的评审Agent，或者使用现有AI配合详细的评审提示词
- 建议将评审标准制作成结构化的检查清单
- 每个步骤完成后强制进行评审，评审通过才能进入下一步骤

这样的评审体系能确保工作流每个环节的输出质量，避免错误在后续步骤中放大，提升整体交付质量。