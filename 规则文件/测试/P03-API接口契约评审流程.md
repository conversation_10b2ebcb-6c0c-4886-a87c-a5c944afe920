# P03 - API接口契约评审流程

## 评审目标
验证API接口契约设计是否规范、完整，是否精确匹配前端需求，确保前后端交互的准确性和一致性。

## 输入要求

### 必需输入文档
- **主要输入**: `P03-API接口契约.md`
- **依赖文档**: 
  - `P01-页面元素与数据分析清单.md`
  - `P02-领域实体模型.md`
  - `PR02-API接口规范.md`
  - `PR08-数据字典规范.md`

### 输入验证清单
- [ ] 所有依赖文档是否存在且为最新版本
- [ ] P03文档是否包含完整的API定义
- [ ] 是否包含请求/响应DTO定义
- [ ] 数据字典是否完整定义

## 评审维度与权重

| 评审维度 | 权重 | 评分标准 |
|---------|------|----------|
| **RESTful规范** | 25% | HTTP方法使用、URL设计规范性 |
| **DTO设计** | 30% | 请求响应结构合理性、字段完整性 |
| **数据字典** | 20% | 枚举字段定义完整性、编码规范性 |
| **兼容性** | 25% | 向后兼容性、版本控制策略 |

## 详细评审清单

### 1. RESTful规范检查

#### 1.1 HTTP方法使用检查
**检查标准**: 对比PR02-API接口规范
- [ ] **GET**: 用于查询操作，无副作用
- [ ] **POST**: 用于创建资源
- [ ] **PUT**: 用于完整更新资源
- [ ] **PATCH**: 用于部分更新资源
- [ ] **DELETE**: 用于删除资源

**常见错误检查**:
- ❌ 使用GET进行数据修改
- ❌ 使用POST进行查询（除非参数过长）
- ❌ 混用PUT和PATCH
- ❌ 删除操作使用GET方法

**评分标准**:
- 90-100分: HTTP方法使用完全正确
- 80-89分: 基本正确，个别不当使用
- 70-79分: 部分方法使用不当
- <70分: HTTP方法使用混乱

#### 1.2 URL路径设计检查
**RESTful URL设计原则**:
- [ ] 使用名词而非动词
- [ ] 资源层级关系清晰
- [ ] 使用复数形式表示集合
- [ ] 避免深层嵌套（一般不超过3层）

**URL格式检查**:
```
✅ 正确示例:
GET /api/v1/users/{id}
POST /api/v1/users
PUT /api/v1/users/{id}
DELETE /api/v1/users/{id}
GET /api/v1/users/{userId}/orders

❌ 错误示例:
GET /api/v1/getUser/{id}
POST /api/v1/createUser
GET /api/v1/user/{id}/order/{orderId}/item/{itemId}/detail
```

#### 1.3 状态码使用检查
**必须正确使用的状态码**:
- [ ] **200**: 成功响应
- [ ] **201**: 创建成功
- [ ] **204**: 删除成功或无内容返回
- [ ] **400**: 客户端请求错误
- [ ] **401**: 未授权
- [ ] **403**: 禁止访问
- [ ] **404**: 资源不存在
- [ ] **500**: 服务器内部错误

### 2. DTO设计检查

#### 2.1 请求DTO检查
**检查重点**: 请求参数的完整性和合理性
- [ ] 是否覆盖P01中识别的所有输入需求
- [ ] 字段命名是否与P02实体模型一致
- [ ] 数据类型是否合适
- [ ] 必填/可选字段设置是否合理
- [ ] 参数验证规则是否明确

**参数类型检查**:
```yaml
路径参数 (Path Parameters):
  - 用于资源标识
  - 必须是简单类型 (string, number)
  - 示例: /users/{userId}

查询参数 (Query Parameters):
  - 用于过滤、排序、分页
  - 示例: ?page=1&size=10&status=active

请求体 (Request Body):
  - 用于创建/更新资源
  - 支持复杂数据结构
  - 必须定义JSON Schema
```

#### 2.2 响应DTO检查
**检查重点**: 响应数据的完整性
- [ ] 是否满足P01中识别的所有展示需求
- [ ] 字段命名是否与数据库字段保持一致
- [ ] 是否包含必要的元数据（如分页信息）
- [ ] 错误响应格式是否统一
- [ ] 是否考虑了性能优化（避免返回不必要字段）

**标准响应格式检查**:
```json
// 单个资源响应
{
  "code": 200,
  "message": "success", 
  "data": {
    // 实体数据
  }
}

// 列表响应（分页）
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [...],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}

// 错误响应
{
  "code": 400,
  "message": "参数错误",
  "data": null
}
```

#### 2.3 字段命名一致性检查
**检查方法**: 对比P02领域实体模型
- [ ] DTO字段名是否与数据库字段名一致
- [ ] 驼峰命名和下划线命名的使用是否统一
- [ ] 枚举字段的值是否与数据字典一致
- [ ] 时间字段的格式是否统一（ISO 8601）

### 3. 分页参数标准化检查

#### 3.1 分页参数检查
**标准分页参数**:
- [ ] 使用`pageNum`表示页码（从1开始）
- [ ] 使用`pageSize`表示每页大小
- [ ] 默认值设置是否合理
- [ ] 最大页面大小限制是否设置

**分页响应检查**:
- [ ] 是否使用MyBatis Plus的Page响应格式
- [ ] 是否包含`total`（总记录数）
- [ ] 是否包含`pages`（总页数）
- [ ] 是否包含`current`（当前页）

#### 3.2 排序和过滤参数
**排序参数**:
```
标准格式: ?orderBy=createTime&order=desc
或组合格式: ?sort=createTime,desc&sort=id,asc
```

**过滤参数**:
```
简单过滤: ?status=active&type=user
范围过滤: ?startDate=2023-01-01&endDate=2023-12-31
模糊搜索: ?keyword=张三
```

### 4. 数据字典检查

#### 4.1 枚举字段定义检查
**检查重点**: 所有枚举类型字段的字典定义
- [ ] 状态字段是否都定义了数据字典
- [ ] 类型字段是否都定义了数据字典
- [ ] 字典编码是否按业务域正确分类
- [ ] 字典值的含义是否明确无歧义

**字典编码规范检查**:
```
格式: [业务域]_[字段名]
示例:
- USER_STATUS: 用户状态
- ORDER_TYPE: 订单类型
- PAYMENT_STATUS: 支付状态
```

#### 4.2 通用字典接口检查
**检查内容**: 是否复用通用字典接口
- [ ] 是否使用`/api/v1/dict/{dictType}`获取字典数据
- [ ] 是否避免为每个字典类型创建专门接口
- [ ] 批量获取字典的接口是否提供
- [ ] 字典缓存策略是否考虑

### 5. 向后兼容性检查

#### 5.1 API版本控制
**版本控制策略检查**:
- [ ] 是否使用URL路径版本控制（如`/api/v1/`）
- [ ] 新版本API是否保持向后兼容
- [ ] 废弃API是否有明确的生命周期
- [ ] 版本升级策略是否清晰

#### 5.2 字段变更兼容性
**兼容性检查**:
- [ ] 新增字段是否可选
- [ ] 删除字段是否有过渡期
- [ ] 字段类型变更是否兼容
- [ ] 枚举值变更是否向后兼容

### 6. 安全性检查

#### 6.1 认证和授权
**安全机制检查**:
- [ ] 需要认证的接口是否明确标识
- [ ] 权限控制是否细化到接口级别
- [ ] 敏感数据是否进行脱敏处理
- [ ] 接口访问频率是否有限制

#### 6.2 输入验证
**验证规则检查**:
- [ ] 参数长度限制是否设置
- [ ] 数据格式验证是否完整
- [ ] SQL注入防护是否考虑
- [ ] XSS攻击防护是否考虑

## 问题分级标准

### 🔴 严重问题 (Critical) - 自动不通过
- HTTP方法使用严重错误
- URL设计不符合RESTful原则
- 关键DTO字段缺失
- 分页参数不符合标准

### 🟡 重要问题 (Major) - 需要修改
- 部分HTTP方法使用不当
- DTO字段命名不一致
- 数据字典定义不完整
- 缺少必要的错误处理

### 🔵 一般问题 (Minor) - 建议优化
- 接口文档描述不够清晰
- 参数验证规则可以更完善
- 响应格式可以优化
- 注释不够详细

## 评审报告模板

```markdown
# P03评审报告 - API接口契约

## 基本信息
- **评审时间**: [YYYY-MM-DD HH:mm:ss]
- **文档版本**: [版本号]
- **评审者**: [评审者信息]

## 综合评分
**总分**: XX/100分
**通过状态**: [通过/需要修改/不通过]

## 维度评分
| 维度 | 得分 | 权重 | 加权得分 | 说明 |
|------|------|------|----------|------|
| RESTful规范 | XX/100 | 25% | XX | HTTP方法和URL设计规范性 |
| DTO设计 | XX/100 | 30% | XX | 请求响应结构合理性 |
| 数据字典 | XX/100 | 20% | XX | 枚举字段定义完整性 |
| 兼容性 | XX/100 | 25% | XX | 向后兼容性和版本控制 |

## API统计
- 📊 总接口数: XX个
- 🔍 查询接口: XX个 (GET)
- ➕ 创建接口: XX个 (POST)
- 🔄 更新接口: XX个 (PUT/PATCH)
- 🗑️ 删除接口: XX个 (DELETE)
- 📝 定义DTO: XX个
- 🏷️ 数据字典: XX个

## 优点
- [具体优点描述]

## 问题清单
### 🔴 严重问题
1. **[问题标题]**
   - 描述: [具体问题]
   - 涉及接口: [接口路径]
   - 影响: [对前后端的影响]
   - 建议: [改进建议]

### 🟡 重要问题
[问题列表]

### 🔵 一般问题  
[问题列表]

## 接口规范检查结果

### RESTful规范检查
| 检查项 | 通过率 | 说明 |
|--------|--------|------|
| HTTP方法使用 | XX% | [具体说明] |
| URL设计规范 | XX% | [具体说明] |
| 状态码使用 | XX% | [具体说明] |
| 资源命名 | XX% | [具体说明] |

### 具体接口评审
| 接口路径 | 方法 | 规范性 | DTO完整性 | 评级 |
|----------|------|--------|-----------|------|
| /api/v1/users | GET | ✅ | ✅ | 优秀 |
| /api/v1/users/{id} | GET | ✅ | ⚠️ | 良好 |
| /api/v1/users | POST | ❌ | ✅ | 需改进 |

## 数据字典检查结果
### 已定义字典
- ✅ USER_STATUS: 用户状态
- ✅ ORDER_TYPE: 订单类型
- ✅ PAYMENT_STATUS: 支付状态

### 缺失字典
- ❌ AUDIT_STATUS: 审核状态
- ❌ PRIORITY_LEVEL: 优先级

### 字典规范性
- 编码格式符合度: XX%
- 业务域分类准确性: XX%

## 分页参数检查
- ✅ 使用标准pageNum/pageSize参数
- ✅ 响应使用MyBatis Plus格式
- ⚠️ 部分接口缺少排序参数
- ❌ 最大页面大小未限制

## 向后兼容性分析
### 新增接口
- [新增接口列表和影响分析]

### 修改接口
- [修改接口列表和兼容性分析]

### 版本控制
- API版本策略: [评估结果]
- 向后兼容程度: [百分比]

## 改进建议
### 必须修改
- [ ] [严重问题的改进建议]

### 建议修改
- [ ] [重要问题的改进建议]

### 优化建议
- [ ] [一般问题的改进建议]

## 后续影响分析
**对P04的影响**: [对后端详细设计的影响]
**对P05的影响**: [对前端设计文档的影响]

## 评审结论
**通过建议**: [通过/修改后重审/重新制作]
**预计修改时间**: [X小时]
**重点关注**: [需要特别注意的方面]

## OpenAPI文档检查
### 文档完整性
- [ ] 接口描述是否完整
- [ ] 参数说明是否清晰
- [ ] 示例数据是否提供
- [ ] 错误码说明是否完整

### 文档质量
- 接口描述清晰度: XX%
- 参数文档完整度: XX%
- 示例数据覆盖度: XX%
```

## 自动化检查工具建议

### API规范检查
```python
def check_restful_compliance(api_definitions):
    """检查RESTful规范符合度"""
    violations = []
    
    for api in api_definitions:
        # 检查HTTP方法使用
        if api.method == 'GET' and api.has_side_effects:
            violations.append(f"GET method should not have side effects: {api.path}")
        
        # 检查URL命名
        if has_verb_in_path(api.path):
            violations.append(f"URL should use nouns: {api.path}")
            
        # 检查状态码
        if not has_appropriate_status_codes(api):
            violations.append(f"Inappropriate status codes: {api.path}")
    
    return violations

def check_dto_completeness(api_definitions, p01_requirements):
    """检查DTO完整性"""
    missing_fields = []
    
    for requirement in p01_requirements:
        if not is_covered_by_apis(requirement, api_definitions):
            missing_fields.append(requirement)
    
    return missing_fields
```

### 分页参数检查
```python
def check_pagination_standards(api_definitions):
    """检查分页参数标准化"""
    non_standard_apis = []
    
    for api in api_definitions:
        if is_list_api(api):
            if not uses_standard_pagination(api):
                non_standard_apis.append(api.path)
    
    return non_standard_apis
```

## 评审时间估算
- **文档预读**: 45分钟
- **RESTful规范检查**: 2小时
- **DTO设计审查**: 2.5小时
- **数据字典检查**: 1小时
- **兼容性分析**: 1小时
- **报告撰写**: 45分钟
- **总计**: 7-8小时

此评审流程确保P03 API接口契约的设计质量，为前后端开发提供清晰、规范的交互契约。