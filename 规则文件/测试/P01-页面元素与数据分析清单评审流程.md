# P01 - 页面元素与数据分析清单评审流程

## 评审目标
验证页面元素与数据分析清单是否完整、准确地解析了需求和设计稿，确保为后续步骤提供可靠的基础数据。

## 输入要求

### 必需输入文档
- **主要输入**: `P01-页面元素与数据分析清单.md`
- **依赖文档**: 
  - `PI01-用户旅程文档及功能清单.md`
  - `PI02-业务流程.md`
  - `PI03-页面线框图.md`  
  - `PI04-术语表.md`

### 输入验证清单
- [ ] 所有依赖文档是否存在且可读
- [ ] P01文档格式是否符合Markdown规范
- [ ] 文档版本是否与依赖文档匹配
- [ ] 术语表是否最新版本

## 评审维度与权重

| 评审维度 | 权重 | 评分标准 |
|---------|------|----------|
| **完整性** | 40% | UI元素遍历完整性、数据项覆盖度 |
| **准确性** | 35% | 元素功能定义准确性、数据来源正确性 |
| **边界覆盖** | 25% | 异常状态考虑完整性、边界条件处理 |

## 详细评审清单

### 1. UI元素遍历完整性检查

#### 1.1 线框图对比检查
**检查方法**: 逐页对比PI03线框图与P01清单
- [ ] 每个页面的所有UI组件是否都已识别
- [ ] 按钮、表单、表格、标签、图表等是否遗漏
- [ ] 导航元素、菜单项是否完整记录
- [ ] 弹窗、提示框、模态框是否包含

**评分标准**:
- 95-100%: 完全遍历，无遗漏
- 85-94%: 基本完整，个别细节遗漏
- 70-84%: 主要元素已识别，部分遗漏
- <70%: 严重遗漏，需要重做

#### 1.2 交互元素行为定义
**检查重点**: 每个交互元素的行为是否明确定义
- [ ] 按钮点击后的行为是否明确
- [ ] 表单提交的处理流程是否清晰
- [ ] 链接跳转的目标是否明确
- [ ] 下拉菜单、选项卡等交互逻辑是否完整

**评分标准**:
- 90-100分: 所有交互行为定义明确
- 80-89分: 主要交互已定义，个别不清晰
- 70-79分: 部分交互定义模糊
- <70分: 交互定义不完整或错误

### 2. 数据项分析准确性检查

#### 2.1 数据来源识别
**检查方法**: 验证每个数据项的来源分析
- [ ] 展示数据的来源是否明确（API、本地计算、用户输入等）
- [ ] 提交数据的目标是否清晰（后端接口、本地存储等）
- [ ] 数据流向是否合理（输入→处理→输出）
- [ ] 数据依赖关系是否正确

**评分标准**:
- 90-100分: 数据来源分析完全准确
- 80-89分: 主要数据来源正确，个别不明确
- 70-79分: 部分数据来源分析有误
- <70分: 数据来源分析错误较多

#### 2.2 数据类型与格式
**检查内容**: 数据类型定义的准确性
- [ ] 文本、数字、日期等基础类型是否正确
- [ ] 列表、对象等复杂类型是否合理
- [ ] 枚举值、选择项是否完整
- [ ] 数据格式要求是否明确

### 3. 状态覆盖完整性检查

#### 3.1 页面状态分析
**必须考虑的状态**:
- [ ] **初始加载状态**: 页面首次加载时的状态
- [ ] **数据加载中**: 异步数据获取时的loading状态
- [ ] **数据加载成功**: 正常数据展示状态
- [ ] **数据加载失败**: 网络错误、服务器错误等异常状态
- [ ] **空数据状态**: 列表为空、搜索无结果等情况
- [ ] **权限不足状态**: 用户无权限访问时的处理

**评分标准**:
- 90-100分: 所有关键状态都已考虑
- 80-89分: 主要状态已覆盖，个别遗漏
- 70-79分: 基本状态覆盖，但不够全面
- <70分: 状态考虑不完整

#### 3.2 边界条件处理
**检查重点**: 极端情况的处理考虑
- [ ] 数据量极大时的处理（如长列表）
- [ ] 数据量极小时的处理（如空列表）
- [ ] 输入数据超出范围时的处理
- [ ] 网络异常、超时等情况的处理
- [ ] 并发操作的冲突处理

### 4. 术语一致性检查

#### 4.1 命名规范检查
**检查方法**: 对比PI04术语表
- [ ] 业务术语是否使用术语表中的标准名称
- [ ] 字段名称是否与术语表一致
- [ ] 状态名称、操作名称是否规范
- [ ] 是否存在同义词混用情况

**评分标准**:
- 95-100分: 完全符合术语表规范
- 85-94分: 基本符合，个别不一致
- 70-84分: 部分不符合规范
- <70分: 术语使用混乱

### 5. 业务逻辑正确性检查

#### 5.1 与需求文档对齐
**检查方法**: 对比PI01和PI02文档
- [ ] 功能描述是否与PI01用户旅程一致
- [ ] 业务流程是否与PI02业务流程匹配
- [ ] 用户角色和权限是否正确理解
- [ ] 业务规则是否准确体现

#### 5.2 数据关联关系
**检查内容**: 数据间的逻辑关系
- [ ] 主从数据关系是否正确
- [ ] 数据联动逻辑是否合理
- [ ] 计算字段的依赖关系是否明确
- [ ] 数据验证规则是否完整

## 问题分级标准

### 🔴 严重问题 (Critical) - 自动不通过
- 大量UI元素遗漏（>20%）
- 核心业务逻辑理解错误
- 关键数据来源分析错误
- 与需求文档严重不符

### 🟡 重要问题 (Major) - 需要修改
- 部分UI元素遗漏（5-20%）
- 重要状态未考虑
- 数据类型定义不准确
- 术语使用不规范

### 🔵 一般问题 (Minor) - 建议优化
- 个别细节遗漏（<5%）
- 描述不够清晰
- 格式不够规范
- 可以进一步完善的地方

## 评审报告模板

```markdown
# P01评审报告 - 页面元素与数据分析清单

## 基本信息
- **评审时间**: [YYYY-MM-DD HH:mm:ss]
- **文档版本**: [版本号]
- **评审者**: [评审者信息]

## 综合评分
**总分**: XX/100分
**通过状态**: [通过/需要修改/不通过]

## 维度评分
| 维度 | 得分 | 权重 | 加权得分 | 说明 |
|------|------|------|----------|------|
| 完整性 | XX/100 | 40% | XX | UI元素遍历情况 |
| 准确性 | XX/100 | 35% | XX | 数据分析准确性 |
| 边界覆盖 | XX/100 | 25% | XX | 状态和边界条件 |

## 检查结果摘要
- ✅ 已识别UI元素: XX个
- ✅ 已分析数据项: XX个  
- ✅ 已考虑状态: XX种
- ⚠️ 发现问题: XX个

## 优点
- [具体优点描述]

## 问题清单
### 🔴 严重问题
1. **[问题标题]**
   - 描述: [具体问题]
   - 位置: [文档位置]
   - 影响: [对后续步骤的影响]
   - 建议: [改进建议]

### 🟡 重要问题
[问题列表]

### 🔵 一般问题  
[问题列表]

## 改进建议
### 必须修改
- [ ] [严重问题的改进建议]

### 建议修改
- [ ] [重要问题的改进建议]

### 优化建议
- [ ] [一般问题的改进建议]

## 后续影响分析
**对P02的影响**: [如果不修改问题，对领域实体模型设计的影响]
**对P03的影响**: [对API接口契约设计的影响]

## 评审结论
**通过建议**: [通过/修改后重审/重新制作]
**预计修改时间**: [X小时]
**重点关注**: [需要特别注意的方面]
```

## 自动化检查脚本建议

### 基础检查项
```python
# 示例检查逻辑
def check_p01_completeness(p01_doc, pi03_wireframes):
    """检查UI元素完整性"""
    # 提取线框图中的UI元素
    wireframe_elements = extract_ui_elements(pi03_wireframes)
    # 提取P01中已识别的元素
    identified_elements = extract_identified_elements(p01_doc)
    # 计算覆盖率
    coverage = len(identified_elements) / len(wireframe_elements)
    return coverage

def check_terminology_consistency(p01_doc, pi04_terms):
    """检查术语一致性"""
    # 提取P01中使用的术语
    used_terms = extract_terms(p01_doc)
    # 检查是否符合术语表
    inconsistent_terms = []
    for term in used_terms:
        if not is_term_valid(term, pi04_terms):
            inconsistent_terms.append(term)
    return inconsistent_terms
```

## 评审时间估算
- **文档预读**: 30分钟
- **详细评审**: 2-3小时
- **报告撰写**: 30分钟
- **总计**: 3-4小时

此评审流程确保P01文档的高质量输出，为后续的领域建模和API设计提供可靠基础。