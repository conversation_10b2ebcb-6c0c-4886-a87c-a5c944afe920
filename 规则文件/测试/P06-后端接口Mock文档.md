# P06-后端接口Mock文档

## 文档概述
本文档基于P01页面元素与数据分析清单和P04后端详细设计文档，严格遵循API接口Mock规范，为到店登记功能提供完整的Spring Boot Mock实现方案。支撑前端开发和测试的所有场景需求。

---

## 1. Mock服务架构设计

### 1.1 Mock实现原则

严格遵循PR09-API接口Mock规范文档的核心原则：
- **非侵入性**：Mock逻辑不污染真实业务逻辑
- **配置驱动**：通过yml配置文件控制Mock开关
- **契约一致**：Mock数据严格遵守与真实服务相同的DTO和接口
- **逻辑分离**：Mock数据生成逻辑完全独立

### 1.2 代码结构设计

```
src/main/java/com/perodua/aftersales/
├── mock/
│   ├── provider/
│   │   └── CheckinMockDataProvider.java     # 到店登记Mock数据提供者
│   └── util/
│       └── CheckinMockDataGenerator.java    # 到店登记Mock数据生成工具
├── service/checkin/impl/
│   └── CheckinServiceImpl.java              # 集成Mock逻辑的服务实现
└── controller/checkin/
    └── CheckinController.java               # 控制器层（无需修改）
```

---

## 2. Mock数据提供者实现

### 2.1 CheckinMockDataProvider.java

```java
package com.perodua.aftersales.mock.provider;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.perodua.aftersales.dto.checkin.*;
import com.perodua.aftersales.vo.checkin.*;
import com.perodua.aftersales.mock.util.CheckinMockDataGenerator;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 到店登记Mock数据提供者
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
public class CheckinMockDataProvider {

    /**
     * Mock: 分页查询登记单列表
     */
    public IPage<CheckinVO> getMockCheckinPage(CheckinPageQueryDTO queryDTO) {
        // 生成Mock数据列表
        List<CheckinVO> mockData = CheckinMockDataGenerator.generateCheckinList(50);
        
        // 应用筛选条件
        List<CheckinVO> filteredData = mockData.stream()
            .filter(item -> queryDTO.getCheckinId() == null || 
                item.getCheckinId().contains(queryDTO.getCheckinId()))
            .filter(item -> queryDTO.getLicensePlate() == null || 
                item.getLicensePlate().contains(queryDTO.getLicensePlate()))
            .filter(item -> queryDTO.getRepairPersonName() == null || 
                item.getRepairPersonName().contains(queryDTO.getRepairPersonName()))
            .filter(item -> queryDTO.getRepairPersonPhone() == null || 
                item.getRepairPersonPhone().contains(queryDTO.getRepairPersonPhone()))
            .collect(Collectors.toList());
        
        // 分页处理
        int start = (queryDTO.getPageNum() - 1) * queryDTO.getPageSize();
        int end = Math.min(start + queryDTO.getPageSize(), filteredData.size());
        List<CheckinVO> pageData = filteredData.subList(start, end);
        
        // 构建分页结果
        Page<CheckinVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        page.setRecords(pageData);
        page.setTotal(filteredData.size());
        
        return page;
    }

    /**
     * Mock: 获取登记单详情
     */
    public CheckinDetailVO getMockCheckinDetail(Long id) {
        return CheckinMockDataGenerator.generateCheckinDetail(id);
    }

    /**
     * Mock: 新增登记单
     */
    public CheckinCreateResponseVO getMockCreateCheckin(CheckinCreateDTO createDTO) {
        CheckinCreateResponseVO response = new CheckinCreateResponseVO();
        response.setId(System.currentTimeMillis());
        response.setCheckinId(CheckinMockDataGenerator.generateCheckinId());
        return response;
    }

    /**
     * Mock: 编辑登记单（无返回值）
     */
    public void getMockUpdateCheckin(Long id, CheckinUpdateDTO updateDTO) {
        // Mock编辑操作，无需返回值
        // 实际场景中可以记录日志或进行其他处理
    }

    /**
     * Mock: 删除登记单（无返回值）
     */
    public void getMockDeleteCheckin(Long id) {
        // Mock删除操作，无需返回值
        // 实际场景中可以记录日志或进行其他处理
    }

    /**
     * Mock: 根据车牌号查询车辆信息
     */
    public VehicleVO getMockVehicleInfo(String licensePlate) {
        return CheckinMockDataGenerator.generateVehicleInfo(licensePlate);
    }

    /**
     * Mock: 创建环检单（预留功能）
     */
    public InspectionOrderVO getMockCreateInspectionOrder(Long checkinId) {
        InspectionOrderVO response = new InspectionOrderVO();
        response.setInspectionOrderId("HJ" + System.currentTimeMillis());
        response.setCheckinId(checkinId);
        response.setStatus("CREATED");
        response.setMessage("环检单创建成功");
        return response;
    }

    /**
     * Mock: 导出登记单列表（预留功能）
     */
    public byte[] getMockExportCheckins(CheckinPageQueryDTO queryDTO) {
        // 生成简单的CSV格式数据
        String csvData = "登记编号,车牌号,送修人姓名,服务类型,创建时间\n" +
                        "SH20240101001,沪A12345,张三,维修,2024-01-01 10:00:00\n" +
                        "SH20240101002,沪B67890,李四,保养,2024-01-01 11:00:00";
        return csvData.getBytes();
    }
}
```

---

## 3. Mock数据生成工具实现

### 3.1 CheckinMockDataGenerator.java

```java
package com.perodua.aftersales.mock.util;

import com.perodua.aftersales.vo.checkin.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 到店登记Mock数据生成工具
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class CheckinMockDataGenerator {

    private static final Random RANDOM = new Random();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // 静态数据数组
    private static final String[] VEHICLE_MODELS = {"AXIA", "BEZZA", "MYVI", "ALZA", "ARUZ"};
    private static final String[] COLORS = {"白色", "黑色", "银色", "红色", "蓝色"};
    private static final String[] SERVICE_TYPES = {"03010001", "03010002", "03010003"};
    private static final String[] STATUSES = {"03020001", "03020002"};
    private static final String[] CUSTOMER_NAMES = {"张三", "李四", "王五", "赵六", "钱七"};

    /**
     * 生成登记单列表
     */
    public static List<CheckinVO> generateCheckinList(int count) {
        List<CheckinVO> list = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            CheckinVO vo = new CheckinVO();
            vo.setId((long) i);
            vo.setCheckinId(generateCheckinId());
            vo.setLicensePlate(generateLicensePlate(i));
            vo.setVin(generateVin(i));
            vo.setVehicleModel(VEHICLE_MODELS[i % VEHICLE_MODELS.length]);
            vo.setVehicleConfiguration("1.0L 手动标准版");
            vo.setColor(COLORS[i % COLORS.length]);
            vo.setMileage(RANDOM.nextInt(100000) + 5000);
            vo.setVehicleAge(RANDOM.nextInt(60) + 6);
            vo.setRepairPersonName(CUSTOMER_NAMES[i % CUSTOMER_NAMES.length]);
            vo.setRepairPersonPhone(generatePhone());
            vo.setServiceAdvisorName("服务顾问" + (i % 3 + 1));
            vo.setRelatedRepairOrderId(i % 5 == 0 ? (long) (1000 + i) : null);
            vo.setServiceType(SERVICE_TYPES[i % SERVICE_TYPES.length]);
            vo.setStatus(i % 10 == 0 ? STATUSES[1] : STATUSES[0]); // 10%概率为已取消
            vo.setNotes(i % 3 == 0 ? "客户反映问题" + i : "");
            vo.setCreatedAt(generateRandomDate());
            vo.setUpdatedAt(generateRandomDate());
            
            list.add(vo);
        }
        
        return list;
    }

    /**
     * 生成登记单详情
     */
    public static CheckinDetailVO generateCheckinDetail(Long id) {
        CheckinDetailVO detail = new CheckinDetailVO();
        detail.setId(id);
        detail.setCheckinId(generateCheckinId());
        detail.setLicensePlate("沪A" + String.format("%05d", id.intValue()));
        detail.setVin("1234567890123456" + id);
        detail.setVehicleModel(VEHICLE_MODELS[id.intValue() % VEHICLE_MODELS.length]);
        detail.setVehicleConfiguration("1.0L 手动标准版");
        detail.setColor(COLORS[id.intValue() % COLORS.length]);
        detail.setMileage(RANDOM.nextInt(100000) + 5000);
        detail.setVehicleAge(RANDOM.nextInt(60) + 6);
        detail.setRepairPersonName(CUSTOMER_NAMES[id.intValue() % CUSTOMER_NAMES.length]);
        detail.setRepairPersonPhone(generatePhone());
        detail.setServiceAdvisorName("服务顾问" + (id % 3 + 1));
        detail.setRelatedRepairOrderId(id % 5 == 0 ? (1000L + id) : null);
        detail.setServiceType(SERVICE_TYPES[id.intValue() % SERVICE_TYPES.length]);
        detail.setStatus(id % 10 == 0 ? STATUSES[1] : STATUSES[0]);
        detail.setNotes("详细备注信息" + id);
        detail.setCreatedAt(generateRandomDate());
        detail.setUpdatedAt(generateRandomDate());
        
        return detail;
    }

    /**
     * 生成车辆信息
     */
    public static VehicleVO generateVehicleInfo(String licensePlate) {
        VehicleVO vehicle = new VehicleVO();
        vehicle.setLicensePlate(licensePlate);
        vehicle.setVin("1234567890123456X");
        vehicle.setVehicleModel("AXIA");
        vehicle.setVehicleConfiguration("1.0L 手动标准版");
        vehicle.setColor("白色");
        vehicle.setMileage(15000);
        vehicle.setDeliveryTime("2022-01-01");
        vehicle.setVehicleAge(24);
        
        return vehicle;
    }

    /**
     * 生成登记单号
     */
    public static String generateCheckinId() {
        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String sequence = String.format("%03d", RANDOM.nextInt(1000));
        return "SH" + date + sequence;
    }

    /**
     * 生成车牌号
     */
    private static String generateLicensePlate(int index) {
        String[] prefixes = {"沪A", "沪B", "沪C", "京A", "粤A"};
        String prefix = prefixes[index % prefixes.length];
        String number = String.format("%05d", 12345 + index);
        return prefix + number;
    }

    /**
     * 生成VIN码
     */
    private static String generateVin(int index) {
        return "1234567890123456" + (index % 10);
    }

    /**
     * 生成手机号
     */
    private static String generatePhone() {
        return "138" + String.format("%08d", RANDOM.nextInt(100000000));
    }

    /**
     * 生成随机日期
     */
    private static String generateRandomDate() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime randomDate = now.minusDays(RANDOM.nextInt(30));
        return randomDate.format(DATE_FORMATTER);
    }
}
```

---

## 4. 服务层Mock集成实现

### 4.1 CheckinServiceImpl.java Mock集成

```java
package com.perodua.aftersales.service.checkin.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.perodua.aftersales.dto.checkin.*;
import com.perodua.aftersales.entity.CheckinPO;
import com.perodua.aftersales.mapper.CheckinMapper;
import com.perodua.aftersales.service.checkin.CheckinService;
import com.perodua.aftersales.vo.checkin.*;
import com.perodua.aftersales.mock.provider.CheckinMockDataProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 到店登记业务实现类（集成Mock）
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class CheckinServiceImpl extends ServiceImpl<CheckinMapper, CheckinPO> implements CheckinService {

    @Autowired
    private CheckinMockDataProvider mockDataProvider;

    // Mock总开关配置
    @Value("${mock.feature.checkin.enabled:false}")
    private boolean mockCheckinEnabled;

    @Override
    public IPage<CheckinVO> pageCheckins(CheckinPageQueryDTO queryDTO) {
        // Mock开关判断
        if (mockCheckinEnabled) {
            log.info("使用Mock数据 - 分页查询登记单列表");
            return mockDataProvider.getMockCheckinPage(queryDTO);
        }

        // 真实业务逻辑
        // TODO: 实现真实的分页查询逻辑
        throw new UnsupportedOperationException("真实接口尚未实现");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckinCreateResponseVO createCheckin(CheckinCreateDTO createDTO) {
        // Mock开关判断
        if (mockCheckinEnabled) {
            log.info("使用Mock数据 - 新增登记单");
            return mockDataProvider.getMockCreateCheckin(createDTO);
        }

        // 真实业务逻辑
        // TODO: 实现真实的新增逻辑
        throw new UnsupportedOperationException("真实接口尚未实现");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCheckin(Long id, CheckinUpdateDTO updateDTO) {
        // Mock开关判断
        if (mockCheckinEnabled) {
            log.info("使用Mock数据 - 编辑登记单，ID: {}", id);
            mockDataProvider.getMockUpdateCheckin(id, updateDTO);
            return;
        }

        // 真实业务逻辑
        // TODO: 实现真实的编辑逻辑
        throw new UnsupportedOperationException("真实接口尚未实现");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCheckin(Long id) {
        // Mock开关判断
        if (mockCheckinEnabled) {
            log.info("使用Mock数据 - 删除登记单，ID: {}", id);
            mockDataProvider.getMockDeleteCheckin(id);
            return;
        }

        // 真实业务逻辑
        // TODO: 实现真实的删除逻辑
        throw new UnsupportedOperationException("真实接口尚未实现");
    }

    @Override
    public CheckinDetailVO getCheckinDetail(Long id) {
        // Mock开关判断
        if (mockCheckinEnabled) {
            log.info("使用Mock数据 - 查看登记单详情，ID: {}", id);
            return mockDataProvider.getMockCheckinDetail(id);
        }

        // 真实业务逻辑
        // TODO: 实现真实的详情查询逻辑
        throw new UnsupportedOperationException("真实接口尚未实现");
    }

    @Override
    public VehicleVO getVehicleInfo(String licensePlate) {
        // Mock开关判断
        if (mockCheckinEnabled) {
            log.info("使用Mock数据 - 查询车辆信息，车牌号: {}", licensePlate);
            return mockDataProvider.getMockVehicleInfo(licensePlate);
        }

        // 真实业务逻辑
        // TODO: 实现真实的车辆信息查询逻辑
        throw new UnsupportedOperationException("真实接口尚未实现");
    }

    @Override
    public InspectionOrderVO createInspectionOrder(Long checkinId) {
        // Mock开关判断
        if (mockCheckinEnabled) {
            log.info("使用Mock数据 - 创建环检单，登记单ID: {}", checkinId);
            return mockDataProvider.getMockCreateInspectionOrder(checkinId);
        }

        // 真实业务逻辑
        // TODO: 等环检单功能开发设计时补上
        throw new UnsupportedOperationException("环检单功能尚未实现");
    }

    @Override
    public byte[] exportCheckins(CheckinPageQueryDTO queryDTO) {
        // Mock开关判断
        if (mockCheckinEnabled) {
            log.info("使用Mock数据 - 导出登记单列表");
            return mockDataProvider.getMockExportCheckins(queryDTO);
        }

        // 真实业务逻辑
        // TODO: 实现真实的导出逻辑
        throw new UnsupportedOperationException("导出功能尚未实现");
    }
}
```

---

## 5. 新增VO类定义

### 5.1 VehicleVO.java

```java
package com.perodua.aftersales.vo.checkin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆信息视图对象
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@ApiModel(value = "VehicleVO", description = "车辆信息视图对象")
public class VehicleVO {

    @ApiModelProperty("车牌号")
    private String licensePlate;

    @ApiModelProperty("VIN码")
    private String vin;

    @ApiModelProperty("车型")
    private String vehicleModel;

    @ApiModelProperty("车辆配置")
    private String vehicleConfiguration;

    @ApiModelProperty("颜色")
    private String color;

    @ApiModelProperty("里程数")
    private Integer mileage;

    @ApiModelProperty("交车时间")
    private String deliveryTime;

    @ApiModelProperty("车龄（月）")
    private Integer vehicleAge;
}
```

### 5.2 InspectionOrderVO.java

```java
package com.perodua.aftersales.vo.checkin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 环检单视图对象（预留）
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@ApiModel(value = "InspectionOrderVO", description = "环检单视图对象")
public class InspectionOrderVO {

    @ApiModelProperty("环检单ID")
    private String inspectionOrderId;

    @ApiModelProperty("关联登记单ID")
    private Long checkinId;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("消息")
    private String message;
}
```

---

## 6. 配置文件设置

### 6.1 bootstrap-dev.yml

```yaml
# 开发环境配置
mock:
  feature:
    checkin:
      enabled: true  # 到店登记功能Mock总开关
```

### 6.2 bootstrap-prod.yml

```yaml
# 生产环境配置
mock:
  feature:
    checkin:
      enabled: false  # 生产环境关闭Mock
```

---

## 7. Mock数据场景覆盖

### 7.1 正常业务场景

**分页查询场景**
- 生成50条静态Mock数据
- 支持按登记编号、车牌号、送修人姓名、送修人电话筛选
- 支持分页查询，每页最多100条记录

**CRUD操作场景**
- 新增：返回生成的登记单ID和编号
- 编辑：Mock成功响应，无返回值
- 删除：Mock成功响应，无返回值
- 详情：返回完整的登记单信息

**关联查询场景**
- 车辆信息查询：根据车牌号返回固定的车辆信息
- 环检单创建：返回预设的成功响应

### 7.2 数据状态场景

**登记单状态分布**
- 90%的记录状态为"正常"（03020001）
- 10%的记录状态为"已取消"（03020002）

**工单关联状态**
- 80%的记录未关联工单（relatedRepairOrderId为null）
- 20%的记录已关联工单（有具体的工单ID）

**服务类型分布**
- 均匀分布三种服务类型：维修、保养、检查

---

## 8. Mock服务使用说明

### 8.1 启用Mock服务

1. **修改配置文件**
   ```yaml
   mock:
     feature:
       checkin:
         enabled: true
   ```

2. **重启应用**
   ```bash
   mvn spring-boot:run
   ```

3. **验证Mock状态**
   - 查看应用日志，确认Mock开关已启用
   - 调用任意接口，日志中应显示"使用Mock数据"

### 8.2 切换到真实接口

1. **关闭Mock开关**
   ```yaml
   mock:
     feature:
       checkin:
         enabled: false
   ```

2. **实现真实业务逻辑**
   - 在CheckinServiceImpl中完成真实的业务逻辑实现
   - 移除或注释掉UnsupportedOperationException

3. **重启应用并测试**

### 8.3 清理Mock代码（可选）

项目稳定后，可以清理Mock相关代码：
1. 删除mock包下的所有类
2. 移除CheckinServiceImpl中的Mock相关代码
3. 删除配置文件中的Mock配置项

---

## 9. 测试验证

### 9.1 接口测试用例

```bash
# 1. 分页查询登记单列表
curl -X POST http://localhost:8080/api/v1/after-sales/checkins/page \
  -H "Content-Type: application/json" \
  -d '{"pageNum":1,"pageSize":20}'

# 2. 获取登记单详情
curl -X GET http://localhost:8080/api/v1/after-sales/checkins/1

# 3. 新增登记单
curl -X POST http://localhost:8080/api/v1/after-sales/checkins \
  -H "Content-Type: application/json" \
  -d '{"licensePlate":"沪A12345","vin":"12345678901234567","repairPersonName":"张三","repairPersonPhone":"13800138000","serviceType":"03010001"}'

# 4. 查询车辆信息
curl -X GET "http://localhost:8080/api/v1/after-sales/vehicles/by-license-plate?licensePlate=沪A12345"

# 5. 创建环检单（预留）
curl -X POST http://localhost:8080/api/v1/after-sales/checkins/1/inspection-orders
```

### 9.2 预期响应验证

所有接口应返回符合P04设计的标准响应格式：
```json
{
  "success": true,
  "message": "操作成功",
  "code": "10000000",
  "traceId": "trace-123456",
  "result": { ... },
  "timestamp": 1704067200000
}
```

---

## 自检清单确认

- [x] Mock数据是否与P04的业务逻辑设计一致？
- [x] 是否覆盖了P01页面元素与数据分析清单分析的所有前端场景？
- [x] Mock的业务状态流转是否符合P04后端详细设计文档的设计？
- [x] 异常场景的Mock是否与P04后端详细设计文档的异常体系匹配？
- [x] 并发和边界条件的Mock是否充分？
- [x] Mock数据是否能真正支撑前端开发和测试？
- [x] 是否严格遵循PR09-API接口Mock规范文档的实现要求？
- [x] 配置驱动的Mock开关是否正确实现？
- [x] Mock代码结构是否符合规范要求？
- [x] 是否为环检单等TODO功能预留了Mock接口？
```
