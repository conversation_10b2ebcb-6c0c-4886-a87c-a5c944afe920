# 标准化工作流快速启动提示词模板

本文档包含从P01到P06每一步的标准化提示词模板。当需要执行流程中的某一步时，请复制对应的模板，将`'''...'''`中的占位符替换为实际的文档内容，然后将填充好的完整模板发送给我即可。


## 新会话启动模板

如果这是一个新的对话会话，请先使用此模板：

**指令：**
请加载并学习以下工作流文件：
/Users/<USER>/Git/perodua/auto/new-frontend/dms_frontend/规则文件/测试/基于线框图的前后端设计流程.md

然后确认您已理解整个工作流程，我们开始执行具体步骤。

---

## 第一步：生成 P01-页面元素与数据分析清单

```markdown
# 快速执行工作流：第一步：生成 P01-页面元素与数据分析清单

**指令：**
请根据以下输入内容，严格遵循《需求到实现端到端标准化工作流》中为“第一步”定义的处理流程，生成并输出 `P01-页面元素与数据分析清单.md` 的完整Markdown内容。

---
## 输入内容

### `PI01-用户旅程文档及功能清单.md`
'''
[在这里粘贴PI01的完整内容]
'''

### `PI02-业务流程.md`
'''
[在这里粘贴PI02的完整内容]
'''

### `PI03-页面线框图.md`
'''
[在这里粘贴PI03的完整内容，可以是文字描述或图片链接]
'''

### `PI04-术语表.md`
'''
[在这里粘贴PI04的完整内容]
'''
```

---

## 第二步：生成 P02-领域实体模型

```markdown
# 快速执行工作流：第二步：生成 P02-领域实体模型

**指令：**
请根据以下输入内容，严格遵循《需求到实现端到端标准化工作流》中为“第二步”定义的处理流程，生成并输出 `P02-领域实体模型.md` 的完整Markdown内容。

---
## 输入内容

### `P01-页面元素与数据分析清单.md`
'''
[在这里粘贴第一步生成的P01的完整内容]
'''

### `PI04-数据库表结构（最新）.md`
'''
[在这里粘贴PI04的完整内容]
'''

### `PR01-数据库设计规范.md`
'''
[在这里粘贴PR01的完整内容]
'''

### `PR08-数据字典规范.md`
'''
[在这里粘贴PR08的完整内容]
'''
```

---

## 第三步：生成 P03-API接口契约

```markdown
# 快速执行工作流：第三步：生成 P03-API接口契约

**指令：**
请根据以下输入内容，严格遵循《需求到实现端到端标准化工作流》中为“第三步”定义的处理流程，生成并输出 `P03-API接口契约.md` 的完整Markdown内容。

---
## 输入内容

### `P01-页面元素与数据分析清单.md`
'''
[在这里粘贴P01的完整内容]
'''

### `P02-领域实体模型.md`
'''
[在这里粘贴第二步生成的P02的完整内容]
'''

### `PR02-API接口规范.md`
'''
[在这里粘贴PR02的完整内容]
'''

### `PR08-数据字典规范.md`
'''
[在这里粘贴PR08的完整内容]
'''
```

---

## 第四步：生成 P04-后端详细设计

```markdown
# 快速执行工作流：第四步：生成 P04-后端详细设计

**指令：**
请根据以下输入内容，严格遵循《需求到实现端到端标准化工作流》中为"第四步"定义的处理流程，生成并输出 `P04-后端详细设计文档.md` 的完整Markdown内容。

---
## 输入内容

### `P01-页面元素与数据分析清单.md`
'''
[在这里粘贴P01的完整内容]
'''

### `P02-领域实体模型.md`
'''
[在这里粘贴第二步生成的P02的完整内容]
'''

### `P03-API接口契约.md`
'''
[在这里粘贴第三步生成的P03的完整内容]
'''

### `PI01-用户旅程文档及功能清单.md`
'''
[在这里粘贴PI01的完整内容]
'''

### `PI02-业务流程.md`
'''
[在这里粘贴PI02的完整内容]
'''

### [其他规范文件名] 
'''
[根据需要添加更多规范文件]
注：只需要粘贴项目实际需要的规范文件内容
'''

'''
---

## 第五步：生成 P05-前端设计文档

```markdown
# 快速执行工作流：第五步：生成 P05-前端设计文档

**指令：**
请根据以下输入内容，严格遵循《需求到实现端到端标准化工作流》中为"第五步"定义的处理流程，生成并输出 `P05-前端设计文档.md` 的完整Markdown内容。

---
## 输入内容

### `P03-API接口契约.md`
'''
[在这里粘贴第三步生成的P03的完整内容]
'''

### `P04-后端详细设计文档.md`
'''
[在这里粘贴第四步生成的P04的完整内容]
'''

### `PR03-前端目录结构规范.md`
'''
[在这里粘贴PR03的完整内容]
'''

### `PR04-前端API调用规范.md`
'''
[在这里粘贴PR04的完整内容]
'''

### `PR05-前端类型定义规范.md`
'''
[在这里粘贴PR05的完整内容]
'''

### `PR06-前端路由规范.md`
'''
[在这里粘贴PR06的完整内容]
'''

### `PR07-前端国际化规范.md`
'''
[在这里粘贴PR07的完整内容]
'''

### `PR08-数据字典规范.md`
'''
[在这里粘贴PR08的完整内容]
'''

```

---

## 第六步：生成 P06-后端接口Mock文档

```markdown
# 快速执行工作流：第六步：生成 P06-后端接口Mock文档

**指令：**
请根据以下输入内容，严格遵循《需求到实现端到端标准化工作流》中为"第六步"定义的处理流程，生成并输出 `P06-后端接口mock文档.md` 的完整Markdown内容。

---
## 输入内容

### `P01-页面元素与数据分析清单.md`
'''
[在这里粘贴P01的完整内容]
'''
### `P04-后端详细设计文档.md`
'''
[在这里粘贴第四步生成的P04的完整内容]
'''
### `PR09-API接口Mock规范文档.md`
'''
[在这里粘贴第四步生成的P04的完整内容]
'''
```
