# P01-页面元素与数据分析清单

## 项目信息

- **项目名称**: 到店登记管理系统
- **业务领域**: 售后服务
- **功能模块**: 到店登记
- **文档版本**: 1.0
- **创建日期**: 2024-01-01

## 页面概览

基于需求文档和线框图分析，到店登记管理系统包含以下核心页面和组件：

1. **主页面**: 到店登记管理列表页 (Checkin.vue)
2. **搜索组件**: 筛选条件表单 (CheckinSearchForm.vue)
3. **数据表格**: 登记列表表格 (CheckinTable.vue)
4. **表单弹窗**: 新增/编辑登记弹窗 (CheckinForm.vue)
5. **详情弹窗**: 登记详情查看弹窗 (CheckinDetail.vue)

## 页面元素与数据分析

### 1. 到店登记管理主页面 (Checkin.vue)

#### 1.1 页面标题区域

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 |
|--------|----------|----------|----------|----------|
| 页面标题 | H1文本 | 静态展示 | 展示 | 固定文本："到店登记管理" |

#### 1.2 操作按钮区域

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 |
|--------|----------|----------|----------|----------|
| 新增登记按钮 | Primary按钮 | 点击打开新增弹窗 | 操作触发 | 无 |
| 导出按钮 | Default按钮 | 点击导出当前筛选数据 | 操作触发 | 当前列表数据 |

### 2. 筛选条件表单 (CheckinSearchForm.vue)

#### 2.1 搜索字段

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 | 数据字段 |
|--------|----------|----------|----------|----------|----------|
| 登记编号输入框 | 文本输入框 | 输入、清空 | 提交查询 | 用户输入 | checkinId |
| 车牌号输入框 | 文本输入框 | 输入、清空 | 提交查询 | 用户输入 | licensePlate |
| 送修人姓名输入框 | 文本输入框 | 输入、清空 | 提交查询 | 用户输入 | repairPersonName |
| 送修人电话输入框 | 文本输入框 | 输入、清空 | 提交查询 | 用户输入 | repairPersonPhone |
| 登记时间范围选择器 | 日期范围选择器 | 选择日期、清空 | 提交查询 | 用户选择 | createdAtStart, createdAtEnd |

#### 2.2 操作按钮

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 |
|--------|----------|----------|----------|----------|
| 搜索按钮 | Primary按钮 | 点击触发搜索 | 操作触发 | 表单数据 |
| 重置按钮 | Default按钮 | 点击清空条件 | 操作触发 | 无 |

### 3. 数据表格 (CheckinTable.vue)

#### 3.1 表格列定义

| 列序号 | 列标题 | 数据字段 | 数据类型 | 数据用途 | 特殊处理 |
|--------|--------|----------|----------|----------|----------|
| 1 | 序号 | index | Number | 展示 | 自动序号 |
| 2 | 登记编号 | checkinId | String | 展示 | 唯一标识 |
| 3 | 车牌号 | licensePlate | String | 展示 | 车辆标识 |
| 4 | VIN码 | vin | String | 展示 | 车辆唯一码 |
| 5 | 车型 | vehicleModel | String | 展示 | 车辆型号 |
| 6 | 车辆配置 | vehicleConfiguration | String | 展示 | 配置信息 |
| 7 | 颜色 | color | String | 展示 | 车辆颜色 |
| 8 | 里程数 | mileage | Number | 展示 | 格式化显示"km" |
| 9 | 送修人姓名 | repairPersonName | String | 展示 | 客户信息 |
| 10 | 联系电话 | repairPersonPhone | String | 展示 | 客户联系方式 |
| 11 | 服务顾问 | serviceAdvisor | String | 展示 | 服务人员 |
| 12 | 维修工单编号 | relatedRepairOrderId | String | 展示 | 空值显示"-" |
| 13 | 服务类型 | serviceType | String | 展示 | 服务分类 |
| 14 | 创建时间 | createdAt | DateTime | 展示 | 时间格式化 |
| 15 | 更新时间 | updatedAt | DateTime | 展示 | 时间格式化 |

#### 3.2 操作列按钮

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 显示条件 |
|--------|----------|----------|----------|----------|
| 查看详情按钮 | Link按钮 | 点击打开详情弹窗 | 操作触发 | 所有记录 |
| 编辑按钮 | Link按钮 | 点击打开编辑弹窗 | 操作触发 | 所有记录 |
| 删除按钮 | Link按钮 | 点击删除记录 | 操作触发 | 未关联维修工单 |
| 创建工单按钮 | Link按钮 | 点击创建维修工单 | 操作触发 | 未关联维修工单 |

#### 3.3 分页器

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 |
|--------|----------|----------|----------|----------|
| 总条数显示 | 文本 | 静态展示 | 展示 | pagination.total |
| 每页条数选择器 | 下拉选择 | 选择页面大小 | 提交查询 | [10, 20, 50, 100] |
| 页码按钮 | 按钮组 | 点击切换页面 | 提交查询 | pagination.page |
| 跳转输入框 | 数字输入框 | 输入页码跳转 | 提交查询 | 用户输入 |

### 4. 新增/编辑表单弹窗 (CheckinForm.vue)

#### 4.1 弹窗控制

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 |
|--------|----------|----------|----------|----------|
| 弹窗标题 | 文本 | 静态展示 | 展示 | 根据模式动态："新增到店登记"/"编辑到店登记" |
| 关闭按钮 | 图标按钮 | 点击关闭弹窗 | 操作触发 | 无 |

#### 4.2 车牌号查询区

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 | 数据字段 |
|--------|----------|----------|----------|----------|----------|
| 车牌号查询输入框 | 带按钮输入框 | 输入、点击搜索 | 提交查询 | 用户输入 | licensePlate |
| 查询按钮 | 图标按钮 | 点击查询车辆信息 | 操作触发 | 输入框内容 |
| 提示文字 | 静态文本 | 静态展示 | 展示 | 固定文本："车辆信息自动填充" |

#### 4.3 车辆信息区

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 | 数据字段 | 编辑状态 |
|--------|----------|----------|----------|----------|----------|----------|
| 车牌号输入框 | 文本输入框 | 输入 | 提交 | 查询结果/用户输入 | licensePlate | 条件可编辑 |
| VIN码输入框 | 文本输入框 | 输入 | 提交 | 查询结果/用户输入 | vin | 条件可编辑 |
| 车型输入框 | 文本输入框 | 输入 | 提交 | 查询结果/用户输入 | vehicleModel | 条件可编辑 |
| 车辆配置输入框 | 文本输入框 | 输入 | 提交 | 查询结果/用户输入 | vehicleConfiguration | 条件可编辑 |
| 颜色输入框 | 文本输入框 | 输入 | 提交 | 查询结果/用户输入 | color | 条件可编辑 |
| 里程数输入框 | 数字输入框 | 输入 | 提交 | 用户输入 | mileage | 可编辑 |
| 车龄显示 | 只读文本 | 静态展示 | 展示 | 自动计算 | vehicleAge | 只读 |

#### 4.4 客户信息区

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 | 数据字段 | 验证规则 |
|--------|----------|----------|----------|----------|----------|----------|
| 送修人姓名输入框 | 文本输入框 | 输入 | 提交 | 查询结果/用户输入 | repairPersonName | 必填 |
| 送修人电话输入框 | 文本输入框 | 输入 | 提交 | 查询结果/用户输入 | repairPersonPhone | 必填 |
| 服务顾问输入框 | 只读输入框 | 静态展示 | 提交 | 当前用户 | serviceAdvisor | 只读，必填 |

#### 4.5 服务信息区

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 | 数据字段 |
|--------|----------|----------|----------|----------|----------|
| 服务类型选择框 | 只读选择框 | 静态展示 | 提交 | 固定值 | serviceType |
| 备注文本框 | 多行文本框 | 输入 | 提交 | 用户输入 | notes |

#### 4.6 操作按钮

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 |
|--------|----------|----------|----------|----------|
| 取消按钮 | Default按钮 | 点击关闭弹窗 | 操作触发 | 无 |
| 保存按钮 | Primary按钮 | 点击提交表单 | 操作触发 | 表单数据 |

### 5. 详情查看弹窗 (CheckinDetail.vue)

#### 5.1 弹窗控制

| UI元素 | 元素类型 | 交互行为 | 数据用途 | 数据来源 |
|--------|----------|----------|----------|----------|
| 弹窗标题 | 文本 | 静态展示 | 展示 | 固定文本："到店登记详情" |
| 关闭按钮 | 按钮 | 点击关闭弹窗 | 操作触发 | 无 |

#### 5.2 详情字段展示

| 显示字段 | 数据字段 | 数据类型 | 数据用途 | 格式化规则 |
|----------|----------|----------|----------|------------|
| 登记编号 | checkinId | String | 展示 | 直接显示 |
| 车牌号 | licensePlate | String | 展示 | 直接显示 |
| VIN码 | vin | String | 展示 | 直接显示 |
| 车型 | vehicleModel | String | 展示 | 直接显示 |
| 车辆配置 | vehicleConfiguration | String | 展示 | 直接显示 |
| 颜色 | color | String | 展示 | 直接显示 |
| 里程数 | mileage | Number | 展示 | "${value} km" 或 "-" |
| 车龄 | vehicleAge | Number | 展示 | "${value} 个月" 或 "-" |
| 送修人姓名 | repairPersonName | String | 展示 | 直接显示 |
| 送修人电话 | repairPersonPhone | String | 展示 | 直接显示 |
| 服务顾问 | serviceAdvisor | String | 展示 | 直接显示 |
| 关联维修工单 | relatedRepairOrderId | String | 展示 | 直接显示或 "-" |
| 服务类型 | serviceType | String | 展示 | 直接显示 |
| 创建时间 | createdAt | DateTime | 展示 | "YYYY-MM-DD HH:mm" |
| 更新时间 | updatedAt | DateTime | 展示 | "YYYY-MM-DD HH:mm" |
| 备注 | notes | String | 展示 | 直接显示或 "-" |

## 数据对象定义

### 核心数据结构

#### CheckinListItem（到店登记列表项）
```typescript
interface CheckinListItem {
  id: number;                      // 记录ID
  checkinId: string;               // 登记编号
  licensePlate: string;            // 车牌号
  vin: string;                     // VIN码
  vehicleModel: string;            // 车型
  vehicleConfiguration: string;    // 车辆配置
  color: string;                   // 颜色
  mileage: number | null;          // 里程数
  vehicleAge: number | null;       // 车龄（月）
  repairPersonName: string;        // 送修人姓名
  repairPersonPhone: string;       // 送修人电话
  serviceAdvisor: string;          // 服务顾问
  relatedRepairOrderId: string | null; // 关联维修工单编号
  serviceType: string;             // 服务类型
  notes: string | null;            // 备注
  createdAt: string;               // 创建时间
  updatedAt: string;               // 更新时间
  isDeleted: boolean;              // 删除标记
}
```

#### CheckinListParams（搜索参数）
```typescript
interface CheckinListParams {
  checkinId: string;               // 登记编号
  licensePlate: string;            // 车牌号
  repairPersonName: string;        // 送修人姓名
  repairPersonPhone: string;       // 送修人电话
  createdAtStart: string;          // 开始时间
  createdAtEnd: string;            // 结束时间
  page: number;                    // 页码
  pageSize: number;                // 每页条数
}
```

#### CheckinFormData（表单数据）
```typescript
interface CheckinFormData {
  licensePlate: string;            // 车牌号
  vin: string;                     // VIN码
  vehicleModel: string;            // 车型
  vehicleConfiguration: string;    // 车辆配置
  color: string;                   // 颜色
  mileage: number | undefined;     // 里程数
  vehicleAge: number | undefined;  // 车龄（自动计算）
  repairPersonName: string;        // 送修人姓名
  repairPersonPhone: string;       // 送修人电话
  serviceAdvisor: string;          // 服务顾问
  serviceType: string;             // 服务类型
  notes: string;                   // 备注
}
```

## 状态定义

### 页面状态

| 状态名称 | 状态类型 | 触发条件 | 展示形式 | 相关UI元素 |
|----------|----------|----------|----------|------------|
| 数据加载中 | Loading | API调用中 | 加载遮罩 | 表格区域 |
| 数据为空 | Empty | 列表无数据 | 空状态提示 | 表格区域 |
| 搜索结果为空 | SearchEmpty | 搜索无结果 | "暂无数据" | 表格区域 |
| 网络错误 | Error | API调用失败 | 错误提示 | 全局提示 |

### 表单状态

| 状态名称 | 状态类型 | 触发条件 | 展示形式 | 相关UI元素 |
|----------|----------|----------|----------|------------|
| 新增模式 | FormMode | 点击新增按钮 | 空表单 | 表单弹窗 |
| 编辑模式 | FormMode | 点击编辑按钮 | 预填表单 | 表单弹窗 |
| 车辆查询中 | Loading | 车牌号查询中 | 加载状态 | 查询按钮 |
| 车辆查询成功 | Success | 查询到车辆信息 | 自动填充 | 车辆信息区 |
| 车辆查询失败 | Failed | 未查询到信息 | 手动录入 | 车辆信息区 |
| 表单验证错误 | ValidationError | 必填项为空 | 错误提示 | 对应字段 |

### 记录状态

| 状态名称 | 数据字段 | 状态值 | 业务含义 | UI表现 |
|----------|----------|---------|----------|---------|
| 登记状态 | isDeleted | false | 正常状态 | 显示所有操作 |
| 登记状态 | isDeleted | true | 已取消 | 隐藏部分操作 |
| 关联状态 | relatedRepairOrderId | null | 未关联工单 | 显示"创建工单"按钮 |
| 关联状态 | relatedRepairOrderId | 有值 | 已关联工单 | 隐藏"创建工单"按钮 |

### 边界状态处理

#### 数据边界状态

| 边界情况 | 触发条件 | 处理方式 | 用户提示 |
|----------|----------|----------|----------|
| 空数据列表 | 首次加载无数据 | 显示空状态图标 | "暂无数据" |
| 搜索无结果 | 搜索条件无匹配 | 显示空状态提示 | "暂无符合条件的数据" |
| 网络超时 | API请求超时 | 显示错误提示 | "网络请求超时，请重试" |
| 服务器错误 | API返回5xx | 显示错误提示 | "服务器异常，请稍后重试" |

#### 操作边界状态

| 边界情况 | 触发条件 | 处理方式 | 用户提示 |
|----------|----------|----------|----------|
| 重复车牌登记 | 同日期同车牌 | 阻止提交 | "该车牌今日已有登记记录" |
| 已关联记录删除 | 删除已关联工单记录 | 隐藏删除按钮 | 无提示 |
| 表单未保存关闭 | 弹窗关闭前有修改 | 二次确认 | "是否确认关闭？未保存的数据将丢失" |
| 车辆信息查询失败 | 查询API异常 | 允许手动录入 | "未查询到车辆信息，请手动录入" |

#### 权限边界状态

| 边界情况 | 触发条件 | 处理方式 | 用户提示 |
|----------|----------|----------|----------|
| 服务顾问权限 | 登录角色为服务顾问 | 只显示自建记录 | 无提示 |
| 服务经理权限 | 登录角色为服务经理 | 显示全店记录 | 无提示 |
| 无权限操作 | 操作他人记录 | 隐藏操作按钮 | 无提示 |

## 变更标识

本文档为新需求开发，所有功能元素均为**新增**。

## 自检清单

- [x] 是否已覆盖线框图中的**每一个**UI元素？
- [x] 每个交互元素是否都明确了其对应的**行为和目的**？
- [x] 每个需要动态展示的数据，是否都已明确其**数据来源**？
- [x] 是否已考虑所有**边界状态**？
- [x] 对于变更需求，是否已明确标识**新增/修改/删除**的元素？
- [x] 文档中使用的术语是否与`PI04-术语表.md`完全一致？