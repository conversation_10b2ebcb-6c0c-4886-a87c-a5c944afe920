---
description: 
globs: 
alwaysApply: false
---
# 数据库语句生成规则（MySQL）

## 1. 表结构命名规范

### 表名规范
- **格式**: 小写 + 下划线
- **命名**: 表类型前缀 + 模块名
- **业务表**: `tt_` 开头，如 `tt_user_info`, `tt_order_detail`, `tt_product_category`
- **字典表**: `tc_dic_data` 开头，如 `tc_dic_data`, `tc_status_config`, `tc_region_info`
- **关联表**: `tr_` 开头，如 `tr_user_role`, `tr_order_product`, `tr_menu_permission`
- **表名结构为**:`tt_<业务模块>_<实体/行为>`
- **禁止使用**: `*_info`, `*_record` 这类模糊词汇，必须明确说明业务对象，例如：
   - ❌ `tt_allocation_record`
   - ✅ `tt_order_allocation_record`（表示订单配车记录）

### 字段名规范
- **格式**: 小写 + 下划线
- **示例**: `created_time`, `user_name`, `order_status`

### 主键规范
- **名称**: 统一命名为 `id`
- **类型**: `BIGINT`
- **属性**: `AUTO_INCREMENT PRIMARY KEY`

## 2. 字段设计规范

### 字符类型
- **推荐**: 使用 `VARCHAR`
- **长度**: 建议小于 255
- **编码**: 使用 `utf8mb4` 字符集

## 字段命名规范 - 特定领域规则（必须遵守）

- **经销商字段**
   - 所有经销商标识字段统一命名为 `dealer_code`
   - 不允许使用 `store_code`、`location_code`、`shop_code` 等别名

- **用户类字段**
   - 所有唯一用户标识字段必须使用 `_code` 后缀，前缀需明确说明角色或业务
     - ✅ 示例：`user_code`, `technician_code`, `salesman_code`
     - ❌ 禁止：`code`, `login_code`, `id_code`（歧义大）

   - 所有用户名称字段必须使用 `_name` 后缀，前缀需匹配角色
     - ✅ 示例：`user_name`, `technician_name`
     - ❌ 禁止：`name`, `display_name`

- **歧义字段禁止清单**
   - 禁用字段名：`code`, `name`, `desc`, `title`, `store_code`
   - 必须使用带前缀、语义明确的字段名代替

### 主键设计
```sql
`id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID'
```

### 必需字段（所有表必须包含）
```sql
`is_deleted` TINYINT(1) DEFAULT '0' COMMENT '是否删除',
`created_by` VARCHAR(36) DEFAULT NULL COMMENT '创建人ID',
`created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_by` VARCHAR(36) DEFAULT NULL COMMENT '更新人ID',
`updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
```

## 3. 数据类型选择规范

### 整数类型
- **TINYINT**: 布尔值、状态值 (0-255)
- **INT**: 一般整数
- **BIGINT**: 主键、大数值

### 字符串类型
- **VARCHAR(n)**: 变长字符串，n < 255
- **TEXT**: 长文本内容
- **CHAR(n)**: 固定长度字符串（如状态码）

### 时间类型
- **TIMESTAMP**: 时间戳（推荐）
- **DATETIME**: 日期时间
- **DATE**: 仅日期

### 数值类型
- **DECIMAL(m,d)**: 精确小数（金额等）
- **DOUBLE**: 浮点数

## 4. 索引规范

### 主键索引
- 自动生成，无需手动创建

### 普通索引
```sql
-- 单字段索引
KEY `idx_字段名` (`字段名`)

-- 联合索引（遵循最左前缀原则）
KEY `idx_字段1_字段2` (`字段1`, `字段2`)
```

### 唯一索引
```sql
UNIQUE KEY `uk_字段名` (`字段名`)
```

### 索引创建原则
- 需要模糊搜索的字段
- 需要排序的字段
- 需要关联查询的字段
- WHERE 条件中经常使用的字段
- 索引数量建议不多余5个，特殊情况另外讨论

## 5. 约束设计规范

### 非空约束
- 必填字段使用 `NOT NULL`
- 可选字段允许 `NULL`

### 默认值
- 状态字段设置合理默认值
- 时间字段使用 `CURRENT_TIMESTAMP`

### 枚举值处理
- 不直接使用中文枚举
- 建立字典表进行关联
- 使用数字或英文代码

## 6. 表创建模板

### 基础表结构模板

#### 业务表模板（tt_开头）
```sql
CREATE TABLE `tt_表名` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  
  -- 业务字段
  `字段名` VARCHAR(100) NOT NULL COMMENT '字段说明',
  `状态字段` TINYINT(1) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  
  -- 必需字段
  `is_deleted` TINYINT(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` VARCHAR(36) DEFAULT NULL COMMENT '创建人ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` VARCHAR(36) DEFAULT NULL COMMENT '更新人ID',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引
  KEY `idx_字段名` (`字段名`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务表说明';
```

#### 字典表模板（tc_开头）
```sql
CREATE TABLE `tc_表名` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  `dict_code` VARCHAR(50) NOT NULL COMMENT '字典编码',
  `dict_name` VARCHAR(100) NOT NULL COMMENT '字典名称',
  `dict_value` VARCHAR(100) DEFAULT NULL COMMENT '字典值',
  `parent_id` BIGINT DEFAULT '0' COMMENT '父级ID',
  `sort_order` INT DEFAULT '0' COMMENT '排序',
  `status` TINYINT(1) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  
  -- 必需字段
  `is_deleted` TINYINT(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` VARCHAR(36) DEFAULT NULL COMMENT '创建人ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` VARCHAR(36) DEFAULT NULL COMMENT '更新人ID',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引
  UNIQUE KEY `uk_dict_code` (`dict_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典表说明';
```

#### 关联表模板（tr_开头）
```sql
CREATE TABLE `tr_表名` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  `主表_id` BIGINT NOT NULL COMMENT '主表ID',
  `关联表_id` BIGINT NOT NULL COMMENT '关联表ID',
  `relation_type` TINYINT(1) DEFAULT '1' COMMENT '关联类型',
  
  -- 必需字段
  `is_deleted` TINYINT(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` VARCHAR(36) DEFAULT NULL COMMENT '创建人ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` VARCHAR(36) DEFAULT NULL COMMENT '更新人ID',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引
  UNIQUE KEY `uk_主表_关联表` (`主表_id`, `关联表_id`),
  KEY `idx_主表_id` (`主表_id`),
  KEY `idx_关联表_id` (`关联表_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关联表说明';
```

## 7. DDL语句生成规则

### 表创建语句结构
1. **表名**: 使用表类型前缀 + 模块名
   - 业务表：`tt_` + 模块名
   - 字典表：`tc_` + 模块名  
   - 关联表：`tr_` + 模块名
2. **字段定义**: 按照字段类型规范
3. **索引定义**: 根据查询需求添加
4. **表选项**: 指定引擎、字符集、注释

### 字段添加语句
```sql
ALTER TABLE `表名` ADD COLUMN `字段名` 数据类型 [约束] COMMENT '字段说明';
```

### 索引添加语句
```sql
-- 添加普通索引
ALTER TABLE `表名` ADD INDEX `idx_字段名` (`字段名`);

-- 添加唯一索引
ALTER TABLE `表名` ADD UNIQUE INDEX `uk_字段名` (`字段名`);
```

## 8. 关联设计规范

### 外键关联
- **不使用物理外键约束**
- 使用关联字段进行数据关联
- 关联字段命名：`关联表名_id`

### 关联字段示例
```sql
`user_id` BIGINT NOT NULL COMMENT '用户ID，关联tt_user_info.id',
`order_id` BIGINT NOT NULL COMMENT '订单ID，关联tt_order_info.id',
`status_id` BIGINT NOT NULL COMMENT '状态ID，关联tc_status_config.id'
```

## 9. 注释规范

### 表注释
- 必须添加表注释说明表的用途
- 格式：`COMMENT='表的功能说明'`

### 字段注释
- 所有字段必须添加注释
- 枚举字段需说明各值含义
- 关联字段需说明关联关系

### 注释示例
```sql
`status` TINYINT(1) DEFAULT '1' COMMENT '状态：1-正常，2-禁用，3-删除',
`user_id` BIGINT NOT NULL COMMENT '用户ID，关联tt_user_info.id',
`dict_type_id` BIGINT NOT NULL COMMENT '字典类型ID，关联tc_dict_type.id'
```

## 10. 字典表设计规范

### 字典表结构
```sql
CREATE TABLE `tc_dict_type` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  `dict_code` VARCHAR(50) NOT NULL COMMENT '字典编码',
  `dict_name` VARCHAR(100) NOT NULL COMMENT '字典名称',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '描述',
  `sort_order` INT DEFAULT '0' COMMENT '排序',
  
  -- 必需字段
  `is_deleted` TINYINT(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` VARCHAR(36) DEFAULT NULL COMMENT '创建人ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` VARCHAR(36) DEFAULT NULL COMMENT '更新人ID',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY `uk_dict_code` (`dict_code`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典类型表';
```

## 11. 生成规则总结

### 代码生成时遵循原则
1. **命名规范**: 严格按照命名规范生成表名和字段名
   - 业务表使用 `tt_` 前缀
   - 字典表使用 `tc_` 前缀
   - 关联表使用 `tr_` 前缀
2. **字段完整**: 确保包含所有必需字段
3. **索引合理**: 根据业务查询需求添加索引
4. **注释完整**: 所有表和字段都要有清晰注释
5. **类型准确**: 根据数据特点选择合适的数据类型
6. **约束明确**: 合理设置非空、默认值等约束
7. **字符集统一**: 使用 utf8mb4 字符集
8. **引擎选择**: 使用 InnoDB 存储引擎

### 质量检查清单
- [ ] 表名符合命名规范
- [ ] 包含所有必需字段
- [ ] 字段类型选择合理
- [ ] 索引设计合理
- [ ] 注释完整清晰
- [ ] 约束设置正确
- [ ] 字符集和引擎正确
