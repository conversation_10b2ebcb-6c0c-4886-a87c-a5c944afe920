# P02 - 领域实体模型评审流程

## 评审目标
验证领域实体模型设计是否合理、完整，是否最大化复用现有数据结构，确保数据模型能够支撑业务需求且具有良好的扩展性。

## 输入要求

### 必需输入文档
- **主要输入**: `P02-领域实体模型.md`
- **依赖文档**: 
  - `P01-页面元素与数据分析清单.md`
  - `PI04-数据库表结构（最新）.md`
  - `PR01-数据库设计规范.md`
  - `PR08-数据字典规范.md`

### 输入验证清单
- [ ] 所有依赖文档是否存在且为最新版本
- [ ] P02文档是否包含完整的实体定义
- [ ] 是否包含新增实体和变更现有实体的说明
- [ ] DDL语句是否提供（如有数据库变更）

## 评审维度与权重

| 评审维度 | 权重 | 评分标准 |
|---------|------|----------|
| **复用优先性** | 30% | 现有表结构复用程度、新增必要性论证 |
| **模型合理性** | 25% | 实体设计合理性、属性定义准确性 |
| **关系正确性** | 25% | 实体间关系设计、外键约束合理性 |
| **规范遵循** | 20% | 数据库设计规范、字典规范符合度 |

## 详细评审清单

### 1. 复用优先性检查

#### 1.1 现有模型映射分析
**检查方法**: 对比P01数据需求与PI04现有表结构
- [ ] 是否完整分析了现有数据库表结构
- [ ] 每个P01中的数据需求是否都尝试映射到现有表
- [ ] 复用现有表的理由是否充分
- [ ] 未复用现有表的原因是否合理

**评分标准**:
- 90-100分: 最大化复用现有结构，新增必要性充分论证
- 80-89分: 大部分复用现有结构，个别新增合理
- 70-79分: 部分复用现有结构，存在可优化空间
- <70分: 复用程度低，存在不必要的新增

#### 1.2 新增实体必要性论证
**检查重点**: 每个新增实体的合理性
- [ ] 新增实体是否无法通过现有表满足
- [ ] 新增实体的业务价值是否明确
- [ ] 是否考虑了通过扩展现有表的可能性
- [ ] 新增实体对系统复杂度的影响是否评估

**必须提供的论证**:
- 业务需求描述
- 现有表无法满足的具体原因
- 新增后的业务价值
- 对现有系统的影响评估

### 2. 模型合理性检查

#### 2.1 实体定义检查
**检查内容**: 每个实体的设计合理性
- [ ] 实体名称是否符合业务语义
- [ ] 实体的职责边界是否清晰
- [ ] 实体是否符合单一职责原则
- [ ] 实体粒度是否合适（不过细也不过粗）

**评分标准**:
- 90-100分: 实体定义清晰、职责明确、粒度合适
- 80-89分: 实体定义基本合理，个别需要调整
- 70-79分: 部分实体定义不够清晰
- <70分: 实体定义存在明显问题

#### 2.2 属性设计检查
**检查重点**: 实体属性的合理性
- [ ] 属性名称是否符合数据库命名规范
- [ ] 数据类型选择是否合适（VARCHAR长度、数值精度等）
- [ ] 必填/可选设置是否合理
- [ ] 默认值设置是否恰当
- [ ] 索引设计是否考虑查询需求

**技术规范检查**:
```sql
-- 示例检查项
- 主键字段: 是否使用统一的主键策略（如BIGINT AUTO_INCREMENT）
- 时间字段: 是否使用DATETIME或TIMESTAMP
- 状态字段: 是否使用TINYINT配合数据字典
- 文本字段: 长度设置是否合理（VARCHAR vs TEXT）
- 金额字段: 是否使用DECIMAL指定精度
- 版本字段: 是否添加用于乐观锁控制
```

#### 2.3 数据字典集成检查
**检查方法**: 对比PR08数据字典规范
- [ ] 状态、类型等枚举字段是否定义了数据字典
- [ ] 数据字典编码是否按业务域正确分类
- [ ] 新增的字典类型和值是否更新到PR08规范
- [ ] 字典值的含义是否明确、无歧义

### 3. 关系正确性检查

#### 3.1 实体关系设计
**检查内容**: 实体间关系的准确性
- [ ] 一对一关系设计是否合理
- [ ] 一对多关系是否正确建立外键
- [ ] 多对多关系是否通过中间表实现
- [ ] 自引用关系是否处理得当

**关系设计评估**:
- 关系的业务语义是否正确
- 外键约束是否会影响性能
- 是否考虑了级联删除的影响
- 关系的维护成本是否可接受

#### 3.2 外键约束检查
**检查重点**: 外键设计的合理性
- [ ] 外键字段的数据类型是否与主键一致
- [ ] 外键约束是否会导致循环依赖
- [ ] 是否考虑了软删除对外键的影响
- [ ] 外键索引是否已创建

**性能影响评估**:
- 外键约束对插入性能的影响
- 关联查询的性能优化考虑
- 是否需要适当的冗余设计

### 4. 向后兼容性检查

#### 4.1 现有系统影响评估
**检查内容**: 数据库变更对现有系统的影响
- [ ] 新增表是否会影响现有功能
- [ ] 现有表结构变更是否向后兼容
- [ ] 是否需要数据迁移脚本
- [ ] 变更是否会影响现有API的响应结构

#### 4.2 迁移策略检查
**必须考虑的迁移场景**:
- [ ] 新增字段的默认值设置
- [ ] 数据类型变更的兼容性
- [ ] 外键约束的添加顺序
- [ ] 索引创建对性能的影响
- [ ] 回滚方案的可行性

### 5. 规范遵循检查

#### 5.1 数据库设计规范检查
**检查方法**: 对比PR01数据库设计规范
- [ ] 表名、字段名命名是否符合规范
- [ ] 数据类型使用是否符合规范
- [ ] 索引命名是否符合规范
- [ ] 注释是否完整且规范

#### 5.2 标准字段检查
**必须包含的标准字段**:
- [ ] `id`: 主键字段
- [ ] `create_time`: 创建时间
- [ ] `update_time`: 更新时间
- [ ] `create_by`: 创建人
- [ ] `update_by`: 更新人
- [ ] `deleted`: 逻辑删除标识（如需要）
- [ ] `version`: 版本号（如需要乐观锁）

## 问题分级标准

### 🔴 严重问题 (Critical) - 自动不通过
- 大量不必要的新增实体（忽略现有表结构）
- 实体关系设计错误（如循环依赖）
- 严重违反数据库设计规范
- 向后兼容性问题严重

### 🟡 重要问题 (Major) - 需要修改
- 部分新增实体必要性不足
- 属性设计不合理（数据类型、约束等）
- 外键设计存在性能风险
- 数据字典定义不完整

### 🔵 一般问题 (Minor) - 建议优化
- 命名不够规范
- 注释不够完整
- 索引设计可以优化
- 文档描述可以更清晰

## 评审报告模板

```markdown
# P02评审报告 - 领域实体模型

## 基本信息
- **评审时间**: [YYYY-MM-DD HH:mm:ss]
- **文档版本**: [版本号]
- **评审者**: [评审者信息]

## 综合评分
**总分**: XX/100分
**通过状态**: [通过/需要修改/不通过]

## 维度评分
| 维度 | 得分 | 权重 | 加权得分 | 说明 |
|------|------|------|----------|------|
| 复用优先性 | XX/100 | 30% | XX | 现有表结构复用情况 |
| 模型合理性 | XX/100 | 25% | XX | 实体和属性设计合理性 |
| 关系正确性 | XX/100 | 25% | XX | 实体关系设计准确性 |
| 规范遵循 | XX/100 | 20% | XX | 数据库规范符合度 |

## 模型统计
- ✅ 复用现有表: XX个
- 🆕 新增实体: XX个
- 🔄 修改现有表: XX个
- 📊 新增数据字典: XX个

## 优点
- [具体优点描述]

## 问题清单
### 🔴 严重问题
1. **[问题标题]**
   - 描述: [具体问题]
   - 涉及实体: [实体名称]
   - 影响: [对系统的影响]
   - 建议: [改进建议]

### 🟡 重要问题
[问题列表]

### 🔵 一般问题  
[问题列表]

## 具体实体评审结果

### 新增实体评审
| 实体名 | 必要性 | 设计合理性 | 规范符合度 | 评级 |
|--------|--------|------------|------------|------|
| [实体名] | ✅/⚠️/❌ | ✅/⚠️/❌ | ✅/⚠️/❌ | [优秀/良好/需改进] |

### 修改现有实体评审
| 实体名 | 变更类型 | 兼容性 | 风险评估 | 评级 |
|--------|----------|--------|----------|------|
| [实体名] | [新增字段/修改字段/删除字段] | ✅/⚠️/❌ | [低/中/高] | [可接受/需注意/高风险] |

## 数据字典检查结果
- ✅ 已定义字典: [字典类型列表]
- ⚠️ 缺失字典: [需要补充的字典]
- ❌ 错误字典: [需要修正的字典]

## 向后兼容性分析
### 影响评估
- **对现有API的影响**: [具体影响描述]
- **对现有数据的影响**: [数据迁移需求]
- **对系统性能的影响**: [性能影响评估]

### 迁移建议
- [ ] [迁移步骤1]
- [ ] [迁移步骤2]
- [ ] [回滚方案]

## 改进建议
### 必须修改
- [ ] [严重问题的改进建议]

### 建议修改
- [ ] [重要问题的改进建议]

### 优化建议
- [ ] [一般问题的改进建议]

## 后续影响分析
**对P03的影响**: [对API接口契约设计的影响]
**对P04的影响**: [对后端详细设计的影响]

## 评审结论
**通过建议**: [通过/修改后重审/重新制作]
**预计修改时间**: [X小时]
**重点关注**: [需要特别注意的方面]

## DDL审查结果
### 新建表DDL
```sql
-- 审查通过的DDL语句
-- [如果存在新建表]
```

### 修改表DDL
```sql
-- 审查通过的ALTER语句
-- [如果存在表结构修改]
```

### 风险提示
- [DDL执行的注意事项]
- [建议的执行顺序]
- [性能影响预估]
```

## 专项检查工具建议

### DDL语法检查
```python
def validate_ddl_syntax(ddl_statements):
    """验证DDL语句语法正确性"""
    # 检查语法正确性
    # 检查命名规范
    # 检查数据类型使用
    pass

def check_foreign_key_cycles(entities):
    """检查外键循环依赖"""
    # 构建依赖图
    # 检测循环
    pass
```

### 兼容性检查
```python
def check_backward_compatibility(old_schema, new_schema):
    """检查向后兼容性"""
    # 检查删除的字段
    # 检查数据类型变更
    # 检查约束变更
    pass
```

## 评审时间估算
- **文档预读**: 30分钟
- **详细评审**: 3-4小时
- **DDL审查**: 1小时
- **报告撰写**: 45分钟
- **总计**: 5-6小时

此评审流程确保P02领域实体模型的设计质量，为后续的API设计和后端实现提供稳固的数据基础。