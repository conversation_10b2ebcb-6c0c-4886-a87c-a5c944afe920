# P03-API接口契约

## 文档概述
本文档基于P01页面元素与数据分析清单和P02领域实体模型，严格遵循API设计规范，定义到店登记功能的完整API接口契约。包含请求/响应格式、数据模型、错误处理等详细规范。

---

## 1. 字典数据设计

### 1.1 新增字典类别

根据字典数据使用规范，需要新增以下字典类别：

**售后模块字典类别（03xx）**
- **0301** - 服务类型 (Service Type)
- **0302** - 登记单状态 (Checkin Status)

### 1.2 字典数据初始化

```sql
-- 新增字典类别
INSERT INTO `tc_dic_category` (`category_code`, `category_zh_name`, `category_en_name`, `category_ms_name`, `category_desc`, `category_sort`, `created_by`) VALUES
('0301', '服务类型', 'Service Type', '<PERSON><PERSON>', '到店登记服务类型分类', 1, 'SYSTEM'),
('0302', '登记单状态', 'Checkin Status', 'Status Daftar Masuk', '到店登记单状态分类', 2, 'SYSTEM');

-- 服务类型字典数据（0301）
INSERT INTO `tc_dic_data` (`dic_category_code`, `dic_code`, `dic_zh_name`, `dic_en_name`, `dic_ms_name`, `dic_sort`, `created_by`) VALUES
('0301', '03010001', '维修', 'Repair', 'Pembaikan', 1, 'SYSTEM'),
('0301', '03010002', '保养', 'Maintenance', 'Penyelenggaraan', 2, 'SYSTEM'),
('0301', '03010003', '检查', 'Inspection', 'Pemeriksaan', 3, 'SYSTEM');

-- 登记单状态字典数据（0302）
INSERT INTO `tc_dic_data` (`dic_category_code`, `dic_code`, `dic_zh_name`, `dic_en_name`, `dic_ms_name`, `dic_sort`, `created_by`) VALUES
('0302', '03020001', '正常', 'Normal', 'Normal', 1, 'SYSTEM'),
('0302', '03020002', '已取消', 'Cancelled', 'Dibatalkan', 2, 'SYSTEM');
```

---

## 2. 数据模型定义

### 2.1 请求参数对象

#### CheckinPageQuery (分页查询参数)
```java
public class CheckinPageQuery extends QueryPage<CheckinVO> {
    private String checkinId;           // 登记编号
    private String licensePlate;        // 车牌号
    private String repairPersonName;    // 送修人姓名
    private String repairPersonPhone;   // 送修人电话
    private String createdAtStart;      // 开始时间 (YYYY-MM-DD)
    private String createdAtEnd;        // 结束时间 (YYYY-MM-DD)
}
```

#### CheckinCreateForm (新增表单)
```java
public class CheckinCreateForm {
    @NotBlank(message = "车牌号不能为空")
    private String licensePlate;        // 车牌号
    
    @NotBlank(message = "VIN码不能为空")
    private String vin;                 // VIN码
    
    private String vehicleModel;        // 车型
    private String vehicleConfiguration; // 车辆配置
    private String color;               // 颜色
    private Integer mileage;            // 里程数
    private Date deliveryTime;          // 交车时间
    
    @NotBlank(message = "送修人姓名不能为空")
    private String repairPersonName;    // 送修人姓名
    
    @NotBlank(message = "送修人电话不能为空")
    private String repairPersonPhone;   // 送修人电话
    
    @NotBlank(message = "服务类型不能为空")
    private String serviceType;         // 服务类型（字典编码）
    
    private String notes;               // 备注
}
```

#### CheckinUpdateForm (编辑表单)
```java
public class CheckinUpdateForm {
    @NotNull(message = "ID不能为空")
    private Long id;                    // 主键ID
    
    private Integer mileage;            // 里程数（可编辑）
    
    @NotBlank(message = "送修人姓名不能为空")
    private String repairPersonName;    // 送修人姓名
    
    @NotBlank(message = "送修人电话不能为空")
    private String repairPersonPhone;   // 送修人电话
    
    @NotBlank(message = "服务类型不能为空")
    private String serviceType;         // 服务类型（字典编码）
    
    private String notes;               // 备注
}
```

#### VehicleQuery (车辆查询参数)
```java
public class VehicleQuery {
    @NotBlank(message = "车牌号不能为空")
    private String licensePlate;        // 车牌号
}
```

### 2.2 响应数据对象

#### CheckinVO (登记单视图对象)
```java
public class CheckinVO {
    private Long id;                    // 主键ID
    private String checkinId;           // 登记编号
    private String licensePlate;        // 车牌号
    private String vin;                 // VIN码
    private String vehicleModel;        // 车型
    private String vehicleConfiguration; // 车辆配置
    private String color;               // 颜色
    private Integer mileage;            // 里程数
    private Integer vehicleAge;         // 车龄（月）
    private String repairPersonName;    // 送修人姓名
    private String repairPersonPhone;   // 送修人电话
    private String serviceAdvisorName;  // 服务顾问姓名
    private Long relatedRepairOrderId;  // 关联工单ID
    private String serviceType;         // 服务类型编码
    private String serviceTypeName;     // 服务类型名称
    private String status;              // 状态编码
    private String statusName;          // 状态名称
    private String notes;               // 备注
    private Date createdAt;             // 创建时间
    private Date updatedAt;             // 更新时间
}
```

#### VehicleVO (车辆信息视图对象)
```java
public class VehicleVO {
    private String licensePlate;        // 车牌号
    private String vin;                 // VIN码
    private String vehicleModel;        // 车型
    private String vehicleConfiguration; // 车辆配置
    private String color;               // 颜色
    private Integer mileage;            // 里程数
    private Date deliveryTime;          // 交车时间
    private Integer vehicleAge;         // 车龄（月）
}
```

#### CheckinDetailVO (登记单详情视图对象)
```java
public class CheckinDetailVO extends CheckinVO {
    // 继承CheckinVO的所有字段，用于详情展示
}
```

### 2.3 数据传输对象

#### CheckinCreateDTO (新增数据传输对象)
```java
public class CheckinCreateDTO {
    private Long storeId;               // 门店ID（从当前用户获取）
    private String licensePlate;        // 车牌号
    private String vin;                 // VIN码
    private String vehicleModel;        // 车型
    private String vehicleConfiguration; // 车辆配置
    private String color;               // 颜色
    private Integer mileage;            // 里程数
    private Date deliveryTime;          // 交车时间
    private String repairPersonName;    // 送修人姓名
    private String repairPersonPhone;   // 送修人电话
    private Long serviceAdvisorId;      // 服务顾问ID（从当前用户获取）
    private String serviceAdvisorName;  // 服务顾问姓名（从当前用户获取）
    private String serviceType;         // 服务类型
    private String notes;               // 备注
}
```

#### CheckinUpdateDTO (编辑数据传输对象)
```java
public class CheckinUpdateDTO {
    private Long id;                    // 主键ID
    private Integer mileage;            // 里程数
    private String repairPersonName;    // 送修人姓名
    private String repairPersonPhone;   // 送修人电话
    private String serviceType;         // 服务类型
    private String notes;               // 备注
}
```

---

## 3. API接口定义

### 3.1 到店登记管理接口

#### 3.1.1 分页查询登记单列表

**接口信息**
- **URL**: `POST /api/v1/after-sales/checkins/page`
- **方法**: POST
- **描述**: 分页查询到店登记单列表，支持多条件筛选
- **权限**: 服务顾问（查看自己创建的）、服务经理（查看全店）

**请求参数**
```json
{
  "pageNum": 1,
  "pageSize": 20,
  "checkinId": "string",
  "licensePlate": "string", 
  "repairPersonName": "string",
  "repairPersonPhone": "string",
  "createdAtStart": "2024-01-01",
  "createdAtEnd": "2024-01-31"
}
```

**响应数据**
```json
{
  "success": true,
  "message": "查询成功",
  "code": "10000000",
  "traceId": "trace-123456",
  "result": {
    "records": [
      {
        "id": 1,
        "checkinId": "SH20240101001",
        "licensePlate": "沪A12345",
        "vin": "12345678901234567",
        "vehicleModel": "AXIA",
        "vehicleConfiguration": "1.0L 手动标准版",
        "color": "白色",
        "mileage": 15000,
        "vehicleAge": 24,
        "repairPersonName": "张三",
        "repairPersonPhone": "13800138000",
        "serviceAdvisorName": "李四",
        "relatedRepairOrderId": null,
        "serviceType": "03010001",
        "serviceTypeName": "维修",
        "status": "03020001",
        "statusName": "正常",
        "notes": "客户反映发动机异响",
        "createdAt": "2024-01-01T10:00:00",
        "updatedAt": "2024-01-01T10:00:00"
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20,
    "pages": 5
  },
  "timestamp": 1704067200000
}
```

#### 3.1.2 新增登记单

**接口信息**
- **URL**: `POST /api/v1/after-sales/checkins`
- **方法**: POST
- **描述**: 新增到店登记单
- **权限**: 服务顾问、服务经理

**请求参数**
```json
{
  "licensePlate": "沪A12345",
  "vin": "12345678901234567",
  "vehicleModel": "AXIA",
  "vehicleConfiguration": "1.0L 手动标准版",
  "color": "白色",
  "mileage": 15000,
  "deliveryTime": "2022-01-01T00:00:00",
  "repairPersonName": "张三",
  "repairPersonPhone": "13800138000",
  "serviceType": "03010001",
  "notes": "客户反映发动机异响"
}
```

**响应数据**
```json
{
  "success": true,
  "message": "新增成功",
  "code": "10000000",
  "traceId": "trace-123456",
  "result": {
    "id": 1,
    "checkinId": "SH20240101001"
  },
  "timestamp": 1704067200000
}
```

#### 3.1.3 编辑登记单

**接口信息**
- **URL**: `PUT /api/v1/after-sales/checkins/{id}`
- **方法**: PUT
- **描述**: 编辑到店登记单（仅未关联工单的记录可编辑）
- **权限**: 服务顾问（编辑自己创建的）、服务经理

**请求参数**
```json
{
  "mileage": 16000,
  "repairPersonName": "张三",
  "repairPersonPhone": "13800138000",
  "serviceType": "03010002",
  "notes": "客户反映发动机异响，需要保养"
}
```

**响应数据**
```json
{
  "success": true,
  "message": "编辑成功",
  "code": "10000000",
  "traceId": "trace-123456",
  "result": null,
  "timestamp": 1704067200000
}
```

#### 3.1.4 删除登记单（取消）

**接口信息**
- **URL**: `DELETE /api/v1/after-sales/checkins/{id}`
- **方法**: DELETE
- **描述**: 删除（取消）到店登记单，实际是修改状态为已取消
- **权限**: 服务顾问（删除自己创建的）、服务经理

**响应数据**
```json
{
  "success": true,
  "message": "取消成功",
  "code": "10000000",
  "traceId": "trace-123456",
  "result": null,
  "timestamp": 1704067200000
}
```

#### 3.1.5 查看登记单详情

**接口信息**
- **URL**: `GET /api/v1/after-sales/checkins/{id}`
- **方法**: GET
- **描述**: 查看到店登记单详情
- **权限**: 服务顾问（查看自己创建的）、服务经理

**响应数据**
```json
{
  "success": true,
  "message": "查询成功",
  "code": "10000000",
  "traceId": "trace-123456",
  "result": {
    "id": 1,
    "checkinId": "SH20240101001",
    "licensePlate": "沪A12345",
    "vin": "12345678901234567",
    "vehicleModel": "AXIA",
    "vehicleConfiguration": "1.0L 手动标准版",
    "color": "白色",
    "mileage": 15000,
    "vehicleAge": 24,
    "repairPersonName": "张三",
    "repairPersonPhone": "13800138000",
    "serviceAdvisorName": "李四",
    "relatedRepairOrderId": null,
    "serviceType": "03010001",
    "serviceTypeName": "维修",
    "status": "03020001",
    "statusName": "正常",
    "notes": "客户反映发动机异响",
    "createdAt": "2024-01-01T10:00:00",
    "updatedAt": "2024-01-01T10:00:00"
  },
  "timestamp": 1704067200000
}
```

#### 3.1.6 导出登记单列表

**接口信息**
- **URL**: `POST /api/v1/after-sales/checkins/export`
- **方法**: POST
- **描述**: 导出到店登记单列表为Excel文件
- **权限**: 服务顾问、服务经理

**请求参数**
```json
{
  "checkinId": "string",
  "licensePlate": "string",
  "repairPersonName": "string",
  "repairPersonPhone": "string",
  "createdAtStart": "2024-01-01",
  "createdAtEnd": "2024-01-31"
}
```

**响应数据**
- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **文件名**: `到店登记单列表_YYYYMMDD_HHmmss.xlsx`

### 3.2 车辆信息查询接口

#### 3.2.1 根据车牌号查询车辆信息

**接口信息**
- **URL**: `GET /api/v1/after-sales/vehicles/by-license-plate`
- **方法**: GET
- **描述**: 根据车牌号查询车辆信息（已有接口）
- **权限**: 服务顾问、服务经理

**请求参数**
```
?licensePlate=沪A12345
```

**响应数据**
```json
{
  "success": true,
  "message": "查询成功",
  "code": "10000000",
  "traceId": "trace-123456",
  "result": {
    "licensePlate": "沪A12345",
    "vin": "12345678901234567",
    "vehicleModel": "AXIA",
    "vehicleConfiguration": "1.0L 手动标准版",
    "color": "白色",
    "mileage": 15000,
    "deliveryTime": "2022-01-01T00:00:00",
    "vehicleAge": 24
  },
  "timestamp": 1704067200000
}
```

### 3.3 环检单管理接口

#### 3.3.1 创建环检单

**接口信息**
- **URL**: `POST /api/v1/after-sales/checkins/{checkinId}/inspection-orders`
- **方法**: POST
- **描述**: 基于登记单创建环检单（新增接口）
- **权限**: 服务顾问、服务经理

**响应数据**
```json
{
  "success": true,
  "message": "环检单创建成功",
  "code": "10000000",
  "traceId": "trace-123456",
  "result": {
    "inspectionOrderId": "HJ20240101001",
    "checkinId": "SH20240101001"
  },
  "timestamp": 1704067200000
}
```

---

## 4. 错误处理

### 4.1 业务错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 30010001 | 登记单不存在 | 查询的登记单ID不存在 |
| 30010002 | 登记单已关联工单，无法编辑 | 已关联工单的登记单不允许编辑 |
| 30010003 | 登记单已关联工单，无法删除 | 已关联工单的登记单不允许删除 |
| 30010004 | 车牌号格式错误 | 车牌号格式不符合规范 |
| 30010005 | VIN码格式错误 | VIN码长度或格式不正确 |
| 30010006 | 手机号格式错误 | 手机号格式不符合规范 |
| 30010007 | 车辆信息不存在 | 根据车牌号未查询到车辆信息 |
| 30010008 | 同一车牌号当日已有未完成登记单 | 重复登记验证失败 |
| 30010009 | 无权限访问该登记单 | 服务顾问只能访问自己创建的登记单 |
| 30010010 | 服务类型不存在 | 选择的服务类型字典值不存在 |

### 4.2 错误响应格式

```json
{
  "success": false,
  "message": "登记单不存在",
  "code": "30010001",
  "traceId": "trace-123456",
  "result": null,
  "timestamp": 1704067200000
}
```

---

## 5. 接口安全

### 5.1 认证授权
- 所有接口都需要JWT Token认证
- 通过Header传递：`Authorization: Bearer {token}`

### 5.2 权限控制
- **服务顾问**：只能操作自己创建的登记单
- **服务经理**：可以操作全店所有登记单
- 权限验证在后端API层面实现

### 5.3 数据权限
- 查询接口自动根据用户角色过滤数据
- 操作接口验证数据归属权限

---

## 6. 接口性能

### 6.1 分页查询优化
- 默认每页20条记录
- 最大每页100条记录
- 使用索引优化查询性能

### 6.2 缓存策略
- 车辆信息查询结果缓存30分钟

### 6.3 并发控制
- 新增/编辑操作使用分布式锁防止并发冲突
- 删除操作使用乐观锁防止重复删除

---

## 7. 接口测试

### 7.1 单元测试覆盖
- Controller层接口测试
- Service层业务逻辑测试
- 数据权限验证测试

### 7.2 集成测试场景
- 完整的CRUD操作流程
- 权限控制验证
- 错误处理验证

### 7.3 性能测试指标
- 分页查询响应时间 < 500ms
- 单条记录操作响应时间 < 200ms
- 并发支持100个用户同时操作

---

## 自检清单确认

- [x] 已完全覆盖P01中识别的所有前端数据交互需求
- [x] 严格遵循API设计规范（RESTful、模块化路径、统一响应格式）
- [x] 数据模型与P02中的实体模型完全对应
- [x] 已定义完整的请求/响应数据结构
- [x] 已考虑所有边界情况和错误处理
- [x] 已明确权限控制和安全要求
- [x] 已按照字典数据规范新增字典类别和数据
- [x] 已更新API设计规范文档以体现模块化路径
- [x] 接口设计支持前端所有交互场景
```
