# 完整评审工作流及调用指南

## 工作流概述

本文档提供了基于线框图的前后端设计流程中每个阶段产出物的完整评审工作流，包括统一的调用接口和标准化的评审流程。

---

## 评审工作流架构

### 工作流输入输出规范

```yaml
评审工作流规范:
  输入:
    - stage: 评审阶段 (P01|P02|P03|P04|P05|P06)
    - document: 待评审文档路径
    - dependencies: 依赖文档路径列表
    - reviewer_config: 评审配置参数
  
  输出:
    - review_report: 标准化评审报告
    - score: 综合评分 (0-100)
    - status: 通过状态 (PASS|CONDITIONAL_PASS|NEEDS_REVISION|FAIL)
    - issues: 问题清单
    - recommendations: 改进建议
```

### 统一评审流程

```mermaid
graph TD
    A[接收评审请求] --> B[验证输入文档]
    B --> C[加载评审标准]
    C --> D[执行阶段特定评审]
    D --> E[生成评审报告]
    E --> F[返回评审结果]
    
    B --> G[输入验证失败]
    G --> H[返回错误信息]
    
    D --> I[P01评审] 
    D --> J[P02评审]
    D --> K[P03评审] 
    D --> L[P04评审]
    D --> M[P05评审]
    D --> N[P06评审]
```

---

## 调用工作流的提示词模板

### 基础调用模板

```
你是一个专业的技术文档评审专家，请按照以下规范对指定阶段的产出物进行全面评审：

**评审任务**：
- 评审阶段：{STAGE}
- 主要文档：{DOCUMENT_PATH}
- 依赖文档：{DEPENDENCIES}

**评审要求**：
1. 严格按照对应阶段的评审流程执行
2. 使用量化评分标准进行评估
3. 识别所有严重、重要、一般问题
4. 提供具体可执行的改进建议
5. 生成标准化评审报告

**评审标准**：
请参考 `{STAGE}-评审流程.md` 中定义的：
- 评审维度和权重
- 详细检查清单
- 问题分级标准
- 评分计算方法

**输出格式**：
请使用标准评审报告模板，包含：
- 综合评分和通过状态
- 各维度详细评分
- 问题清单和改进建议
- 后续影响分析
- 明确的评审结论

开始评审：
```

### P01阶段评审调用

```
# P01 - 页面元素与数据分析清单评审

你是一个专业的前端需求分析专家，请对P01页面元素与数据分析清单进行全面评审。

**评审配置**：
- 评审阶段：P01
- 主要文档：P01-页面元素与数据分析清单.md
- 依赖文档：
  - PI01-用户旅程文档及功能清单.md
  - PI02-业务流程.md  
  - PI03-页面线框图.md
  - PI04-术语表.md

**评审重点**：
1. **完整性检查 (40%)**：
   - UI元素遍历是否完整
   - 数据项识别是否全面
   - 交互行为定义是否明确

2. **准确性检查 (35%)**：
   - 元素功能定义是否正确
   - 数据来源分析是否准确
   - 业务逻辑理解是否到位

3. **边界覆盖 (25%)**：
   - 异常状态是否考虑完整
   - 边界条件是否处理
   - 术语使用是否一致

**关键检查项**：
- [ ] 线框图中每个UI元素是否都已识别
- [ ] 每个数据项的来源和用途是否明确
- [ ] 加载中、空数据、错误等状态是否考虑
- [ ] 术语是否与PI04术语表一致

**评审标准**：
- 90-100分：优秀，可直接进入下一阶段
- 80-89分：良好，建议优化后进入下一阶段
- 70-79分：合格，需要针对性改进
- 60-69分：需要重大修改后重新评审
- <60分：不合格，建议重新制作

请开始评审，并提供详细的评审报告。
```

### P02阶段评审调用

```
# P02 - 领域实体模型评审

你是一个专业的数据架构师，请对P02领域实体模型进行全面评审。

**评审配置**：
- 评审阶段：P02
- 主要文档：P02-领域实体模型.md
- 依赖文档：
  - P01-页面元素与数据分析清单.md
  - PI04-数据库表结构（最新）.md
  - PR01-数据库设计规范.md
  - PR08-数据字典规范.md

**评审重点**：
1. **复用优先性 (30%)**：
   - 现有表结构复用程度
   - 新增实体必要性论证

2. **模型合理性 (25%)**：
   - 实体设计是否合理
   - 属性定义是否准确

3. **关系正确性 (25%)**：
   - 实体间关系设计
   - 外键约束合理性

4. **规范遵循 (20%)**：
   - 数据库设计规范符合度
   - 数据字典规范遵循

**关键检查项**：
- [ ] 是否最大化复用现有数据库表结构
- [ ] 新增实体是否符合业务领域
- [ ] 实体间关系是否准确定义
- [ ] 是否符合PR01数据库设计规范
- [ ] 数据字典是否按PR08规范定义

**特别关注**：
- 向后兼容性评估
- DDL语句审查
- 数据迁移策略

请开始评审，重点关注数据模型的复用性和扩展性。
```

### P03阶段评审调用

```
# P03 - API接口契约评审

你是一个专业的API架构师，请对P03 API接口契约进行全面评审。

**评审配置**：
- 评审阶段：P03
- 主要文档：P03-API接口契约.md
- 依赖文档：
  - P01-页面元素与数据分析清单.md
  - P02-领域实体模型.md
  - PR02-API接口规范.md
  - PR08-数据字典规范.md

**评审重点**：
1. **RESTful规范 (25%)**：
   - HTTP方法使用正确性
   - URL设计规范性

2. **DTO设计 (30%)**：
   - 请求响应结构合理性
   - 字段命名一致性

3. **数据字典 (20%)**：
   - 枚举字段定义完整性
   - 字典编码规范性

4. **兼容性 (25%)**：
   - 向后兼容性处理
   - API版本控制策略

**关键检查项**：
- [ ] HTTP方法使用是否符合RESTful原则
- [ ] DTO字段是否与P02实体模型一致
- [ ] 分页参数是否使用pageNum/pageSize标准
- [ ] 数据字典是否完整定义且编码规范
- [ ] API变更是否考虑向后兼容

**重点验证**：
- 分页响应格式是否使用MyBatis Plus标准
- 错误响应格式是否统一
- 接口文档是否完整

请开始评审，特别关注API的规范性和一致性。
```

### P04阶段评审调用

```
# P04 - 后端详细设计评审

你是一个专业的后端架构师，请对P04后端详细设计进行全面评审。

**评审配置**：
- 评审阶段：P04
- 主要文档：P04-后端详细设计文档.md
- 依赖文档：
  - P01-页面元素与数据分析清单.md
  - P02-领域实体模型.md
  - P03-API接口契约.md
  - PI01-用户旅程文档及功能清单.md
  - PI02-业务流程.md
  - 后端开发设计规范文件组

**评审重点**：
1. **架构合理性 (30%)**：
   - Service层划分是否符合业务边界
   - Controller层是否保持薄层原则

2. **API一致性 (25%)**：
   - 与P03接口契约的完全对齐度
   - HTTP方法和参数传递方式一致性

3. **事务设计 (20%)**：
   - 事务边界定义合理性
   - 传播行为设置正确性

4. **并发处理 (25%)**：
   - 并发控制策略完整性
   - 幂等性设计合理性

**关键检查项**：
- [ ] Service划分是否符合DDD业务边界
- [ ] 与P03接口契约是否100%一致
- [ ] 事务边界设置是否合理
- [ ] 并发控制和幂等性是否充分考虑
- [ ] 设计模式应用是否恰当（避免过度设计）

**强制检查**：
必须逐条对照P03接口契约：
- HTTP方法必须完全一致
- 接口路径必须完全匹配
- 参数传递方式必须一致
- 如发现冲突，以P03为准

请开始评审，重点关注架构设计的合理性和API一致性。
```

### P05阶段评审调用

```
# P05 - 前端设计文档评审

你是一个专业的前端架构师，请对P05前端设计文档进行全面评审。

**评审配置**：
- 评审阶段：P05
- 主要文档：P05-前端设计文档.md
- 依赖文档：
  - P03-API接口契约.md
  - P04-后端详细设计文档.md
  - PI03-页面线框图.md
  - PR03-前端目录结构规范.md
  - PR04-前端API调用规范.md
  - PR05-前端类型定义规范.md
  - PR06-前端路由规范.md
  - PR07-前端国际化规范.md
  - PR08-数据字典规范.md

**评审重点**：
1. **组件设计 (25%)**：
   - 组件划分合理性
   - 目录结构规范性

2. **类型完整性 (20%)**：
   - TypeScript类型定义完整性
   - 与API契约的对应关系

3. **状态管理 (30%)**：
   - 状态流转逻辑设计
   - 与后端业务逻辑匹配度

4. **规范遵循 (25%)**：
   - 前端各项规范符合度
   - Vue 3和Element Plus最佳实践

**关键检查项**：
- [ ] 组件划分是否遵循单一职责原则
- [ ] TypeScript类型是否与P03 API契约对应
- [ ] 状态管理是否与P04后端逻辑匹配
- [ ] 是否遵循PR03-PR08各项前端规范
- [ ] 国际化实现是否完整
- [ ] 路由权限配置是否合理

**技术重点**：
- Vue 3 Composition API使用规范
- Pinia状态管理设计
- Element Plus组件集成
- TypeScript类型系统应用

请开始评审，重点关注前端架构的完整性和规范性。
```

### P06阶段评审调用

```
# P06 - 后端接口Mock文档评审

你是一个专业的测试架构师，请对P06后端接口Mock文档进行全面评审。

**评审配置**：
- 评审阶段：P06
- 主要文档：P06-后端接口mock文档.md
- 依赖文档：
  - P01-页面元素与数据分析清单.md
  - P04-后端详细设计文档.md
  - PR09-API接口Mock规范文档.md

**评审重点**：
1. **业务逻辑一致性 (35%)**：
   - Mock逻辑与P04后端设计匹配度
   - 状态流转逻辑正确性

2. **场景覆盖度 (30%)**：
   - P01前端场景需求覆盖程度
   - 边界和异常情况处理

3. **数据真实性 (35%)**：
   - Mock数据业务合理性
   - 数据关联关系正确性

**关键检查项**：
- [ ] Mock业务逻辑是否与P04设计一致
- [ ] 是否覆盖P01分析的所有前端场景
- [ ] Mock数据是否具有业务真实性
- [ ] 状态流转是否正确模拟
- [ ] 错误和异常场景是否充分
- [ ] 动态响应逻辑是否完整

**特别关注**：
- 并发场景的Mock处理
- 数据一致性和关联性
- 性能测试数据支持
- 前端开发和测试支持度

请开始评审，重点关注Mock服务对前端开发的支撑能力。
```

---

## 批量评审调用模板

### 全流程评审

```
# 全流程批量评审

你是一个专业的技术评审专家，请对整个设计流程的所有阶段产出物进行系统性评审。

**批量评审配置**：
- 评审范围：P01-P06全部阶段
- 评审模式：依赖关系检查 + 独立阶段评审
- 输出要求：综合评审报告 + 各阶段详细报告

**评审文档清单**：
1. P01-页面元素与数据分析清单.md
2. P02-领域实体模型.md  
3. P03-API接口契约.md
4. P04-后端详细设计文档.md
5. P05-前端设计文档.md
6. P06-后端接口mock文档.md

**评审执行顺序**：
1. P01 → P02 → P03 → P04 (后端链路)
2. P03 → P05 (前端链路)  
3. P01 + P04 → P06 (Mock链路)
4. 全流程一致性检查

**特别要求**：
- 检查前后阶段的依赖关系和一致性
- 识别跨阶段的设计冲突
- 评估整体架构的合理性
- 提供全局优化建议

**输出格式**：
1. 全流程综合评审报告
2. 各阶段独立评审报告  
3. 跨阶段一致性分析
4. 整体改进建议清单

请开始全流程评审，确保各阶段产出物的质量和一致性。
```

### 依赖关系检查

```
# 跨阶段依赖关系验证

请验证各阶段产出物之间的依赖关系和一致性：

**依赖关系矩阵**：
- P02 依赖 P01：数据需求是否完全覆盖
- P03 依赖 P01+P02：API是否满足前端需求且匹配数据模型
- P04 依赖 P01+P02+P03：后端设计是否完全实现API契约
- P05 依赖 P03+P04：前端设计是否与后端完全匹配
- P06 依赖 P01+P04：Mock是否支撑前端需求且反映后端逻辑

**一致性检查重点**：
1. 数据流一致性：P01→P02→P03→P04→P05
2. 业务逻辑一致性：PI01/PI02→P04→P06
3. 接口契约一致性：P03→P04→P05
4. 类型定义一致性：P02→P03→P05

请提供详细的依赖关系验证报告。
```

---

## 评审结果处理规范

### 评审状态定义

```yaml
评审状态:
  PASS: 
    描述: 直接通过，可进入下一阶段
    条件: 综合评分≥80分 且 无严重问题
    
  CONDITIONAL_PASS:
    描述: 条件通过，修改后可进入下一阶段
    条件: 综合评分≥70分 且 严重问题≤1个
    
  NEEDS_REVISION:
    描述: 需要修改，修改后重新评审
    条件: 综合评分≥60分 或 严重问题≤3个
    
  FAIL:
    描述: 不合格，建议重新制作
    条件: 综合评分<60分 或 严重问题>3个
```

### 后续行动指南

```yaml
后续行动:
  PASS:
    - 归档当前阶段文档
    - 启动下一阶段工作
    - 监控后续阶段对当前阶段的依赖
    
  CONDITIONAL_PASS:
    - 优先修改严重问题
    - 48小时内完成修改
    - 无需重新评审，直接进入下一阶段
    
  NEEDS_REVISION:
    - 根据优先级修改问题
    - 1周内完成修改
    - 提交重新评审申请
    
  FAIL:
    - 停止当前阶段工作
    - 分析根本原因
    - 重新制作文档
    - 重新提交评审
```

---

## 评审工具集成建议

### 自动化评审工具

```python
class ReviewWorkflow:
    """统一评审工作流"""
    
    def __init__(self, stage: str, document_path: str, dependencies: List[str]):
        self.stage = stage
        self.document_path = document_path  
        self.dependencies = dependencies
        self.reviewer = self._get_stage_reviewer(stage)
    
    def execute_review(self) -> ReviewResult:
        """执行评审流程"""
        # 1. 验证输入
        self._validate_inputs()
        
        # 2. 加载评审标准
        standards = self._load_review_standards(self.stage)
        
        # 3. 执行阶段特定评审
        result = self.reviewer.review(
            document=self.document_path,
            dependencies=self.dependencies,
            standards=standards
        )
        
        # 4. 生成标准报告
        report = self._generate_report(result)
        
        return ReviewResult(
            stage=self.stage,
            score=result.score,
            status=result.status,
            report=report,
            issues=result.issues,
            recommendations=result.recommendations
        )
    
    def _get_stage_reviewer(self, stage: str):
        """获取阶段特定评审器"""
        reviewers = {
            'P01': P01Reviewer(),
            'P02': P02Reviewer(), 
            'P03': P03Reviewer(),
            'P04': P04Reviewer(),
            'P05': P05Reviewer(),
            'P06': P06Reviewer()
        }
        return reviewers.get(stage)

# 使用示例
workflow = ReviewWorkflow(
    stage='P03',
    document_path='P03-API接口契约.md',
    dependencies=['P01-页面元素与数据分析清单.md', 'P02-领域实体模型.md']
)

result = workflow.execute_review()
print(f"评审结果: {result.status}, 评分: {result.score}")
```

### CI/CD集成

```yaml
# .github/workflows/review.yml
name: Document Review Workflow

on:
  pull_request:
    paths: 
      - 'docs/design/**'

jobs:
  review:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Review Environment
        run: |
          pip install -r requirements-review.txt
          
      - name: Execute Review Workflow
        run: |
          python scripts/review_workflow.py \
            --stage=${{ matrix.stage }} \
            --document=${{ matrix.document }} \
            --dependencies=${{ matrix.dependencies }}
        strategy:
          matrix:
            include:
              - stage: P01
                document: docs/P01-页面元素与数据分析清单.md
                dependencies: docs/PI01*.md,docs/PI02*.md,docs/PI03*.md,docs/PI04*.md
              - stage: P02  
                document: docs/P02-领域实体模型.md
                dependencies: docs/P01*.md,docs/PI04*.md,docs/PR01*.md,docs/PR08*.md
                
      - name: Upload Review Reports
        uses: actions/upload-artifact@v3
        with:
          name: review-reports
          path: reports/
```

## 总结

这套完整的评审工作流提供了：

1. **标准化流程**：统一的评审接口和流程
2. **阶段特定评审**：每个阶段都有专门的评审标准和检查项  
3. **调用模板**：可直接使用的提示词模板
4. **批量处理**：支持全流程和依赖关系检查
5. **结果处理**：明确的状态定义和后续行动指南
6. **工具集成**：自动化评审和CI/CD集成建议

通过这套工作流，可以确保每个阶段的产出物都经过严格的质量把关，为整个开发流程的成功提供可靠保障。