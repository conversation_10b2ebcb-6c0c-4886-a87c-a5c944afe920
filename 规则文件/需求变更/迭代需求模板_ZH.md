# 迭代需求：[填写简短的功能或变更名称]

- **所属模块：** [例如：售后 -> 预约限量管理]
- **产品经理：** [您的姓名]
- **日期：** [YYYY-MM-DD]

---

## 1. 迭代目标

*用一句话简述本次迭代为业务或用户带来的核心价值。*

---

## 2. 详细变更需求

*这是一个包含具体变更点的编号列表。请尽可能清晰、明确地描述。*

1.  **变更点：** [描述第一个变更，例如：“移除‘新增/编辑’模态框中的【配置说明】区域。”]
2.  **变更点：** [描述第二个变更，例如：“对于日期为今天或过去的配置，禁用其‘编辑’按钮。”]
3.  ...

---

## 3. 上下文与文件路径

*请提供所有相关文件的绝对路径，以便AI进行分析。*

### 📄 代码文件
- `[主Vue组件的路径, 例如: /Users/<USER>/project/src/views/Module/Component.vue]`
- `[API定义文件路径, 例如: /Users/<USER>/project/src/api/modules/module.ts]`
- `[类型定义文件路径, 例如: /Users/<USER>/project/src/types/module.d.ts]`
- `[其他相关子组件的路径...]`

### 📚 现有文档
- `[当前或旧版的需求文档路径, 例如: /Users/<USER>/project/docs/module/spec_v1.md]`
- `[实体关系图、业务流程图等文件的路径]`

### 📐 规范与模板
- `[接口文档模板的路径, 例如: /Users/<USER>/project/rules/api_template.md]`
- `[UI/风格指南等规范文件的路径, 如果适用]`

---

## 4. 信息源优先级

*当不同信息源之间存在冲突时，请定义它们的权威顺序。*

1.  **本需求模板 (`迭代需求模板.md`)**: 本次提出的新需求是最高优先级的最终事实。
2.  **代码文件**: 项目的当前实现是理解“现状”的第二信息源。
3.  **现有文档**: 这些文档提供业务背景，但可能已过时，优先级最低。

---

## 5. 期望交付产出物

*请明确指定AI需要最终生成的交付文件。*

- [ ] **迭代需求文档(PRD):** 为本次迭代生成一份新的需求文档。
- [ ] **AI工程师开发指令:** 为AI前端工程师生成一份开发任务指令。
- [ ] **接口文档:** 为所有新增或修改的API生成一份详细的接口文档。
