### **需求迭代开发指令 (V1.0)**

`[需求迭代任务卡]`

## **1. 任务元信息**

*   **模块名称**: DMS售后管理系统 - 维修预约限量
*   **迭代版本**: (由需求方填写，例如：V1.1)
*   **需求提出人**: (由需求方填写)
*   **执行人**: AI 开发工程师
*   **日期**: (由需求方填写)

## **2. 输入信息 (由需求方填写)**

#### **2.1 迭代目标**
1.调整预约限量相关页面，和计算规则。 

#### **2.2 相关文档与代码路径**
*(请提供所有相关文件的准确路径，这将极大提升AI的分析效率)*

*   **需求文档目录**: `/Users/<USER>/Desktop/perdua_dms/dms_frontend/售后需求文档/预约限量管理`
*   **API 接口文件**: `/Users/<USER>/Desktop/perdua_dms/dms_frontend/src/api/modules/afterSales/quota.ts`
*   **前端视图文件**:
    主页面：`/Users/<USER>/Desktop/perdua_dms/dms_frontend/src/views/afterSales/quota/QuotaView.vue`
    模态框：`/Users/<USER>/Desktop/perdua_dms/dms_frontend/src/views/afterSales/quota/components/StoreInfoCard.vue`，
    `/Users/<USER>/Desktop/perdua_dms/dms_frontend/src/views/afterSales/quota/components/QuotaListTable.vue`，`/Users/<USER>/Desktop/perdua_dms/dms_frontend/src/views/afterSales/quota/components/QuotaConfigDialog.vue`
*   **类型定义文件**: `/Users/<USER>/Desktop/perdua_dms/dms_frontend/src/types/afterSales/quota.d.ts`
*   **国际化目录**: `/Users/<USER>/Desktop/perdua_dms/dms_frontend/src/locales/modules/afterSales`

#### **2.3 核心数据结构 (可选，强烈推荐)**
    实体信息及实体关系：/Users/<USER>/Desktop/perdua_dms/dms_frontend/售后需求文档/预约限量管理/预约限量管理-实体信息及实体关系.md  

#### **2.4 需求变更清单**
*(请按页面或功能模块，结构化地列出所有具体的变更点)*

1.‘新增预约限量’、‘编辑预约限量’模态框，去除【配置说明】信息
2.预约限量管理列表门店信息模块下，去除【您当前只能管理本门店的预约限量配置】提示文案
3.编辑功能只用操作明天与之后日期的预约限量数据
---

## **3. 输出信息 (由 AI 执行并生成)**

#### **3.1 变更分析与执行**
*   **[AI执行]** 你将首先阅读并分析你在 `2.2` 中提供的所有相关文档，并结合 `2.3` 和 `2.4` 的信息，在内存中构建完整的迭代任务图谱。
*   **[AI执行]** 你将严格按照 **UI/线框图 -> 接口/类型定义 -> 需求/规格文档 -> 国际化文件** 的顺序，分步对相关文件进行 `edit_file` 操作，并等待我的确认。

#### **3.2 生成迭代交付物**
*   **[AI执行]** 在所有文件修改得到我的确认后，你将为我生成一份完整的迭代总结文档。

*   **文件名**: `[模块名称]-迭代纪要-V[版本号].md`
*   **存放路径**: `docs/[模块名称]/[子模块]/`
*   **文档内容**:
    *   **1. 需求背景与目标**: 对本次迭代的简要总结。
    *   **2. 需求变更详情 (Changelog)**: 清晰列出所有具体的变更项。
    *   **3. 开发实施计划**:
        *   包含后端、前端、测试等角色的详细任务分解表。
        *   前端任务中应包含对 `视图`、`类型`、`API服务` 和 `国际化` 文件的修改。
    *   **4. 面向AI工程师的详细开发提示词**: (可选，若需要) 生成一份事无巨细、可直接用于后续开发的提示词。

#### **3.3 文件保存**
*   **[AI执行]** 完成 `3.2` 中迭代纪要的生成后，你将调用 `edit_file` 工具，将这份新生成的文档保存在我指定的 `存放路径` 下。 