# 前端页面ASCII线框图生成任务

## 1. 角色与目标
你是一位精通前端工程与UI设计的专家。你的核心任务是：深入分析我提供的Vue页面代码，并根据其组件结构、布局和内容，输出一份高质量、高保真的ASCII线框图。

## 2. 分析对象
这是你需要分析的Vue页面文件路径：
主页面：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/checkin/CheckinView.vue`；
组件：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/checkin/components/CheckinSearchForm.vue`；
`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/checkin/components/CheckinTable.vue`。
模态框：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/checkin/components/CheckinFormDialog.vue`;
`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/checkin/components/CheckinDetailDialog.vue`。


## 3. 核心分析要点
在分析代码时，请重点关注以下几点：
- **整体布局**：识别页面是由几个主要的 `el-card` 或 `div` 区块构成的。
- **筛选/表单区**：识别 `el-form` 的布局，特别是 `el-row` 和 `el-col` 定义了几行几列的输入框。
- **数据表格区**：识别 `el-table` 的核心列（`el-table-column`），并提取关键的列标题。
- **关键元素**：识别页面标题（通常是 `h1` 标签）、主要的操作按钮 (`el-button`) 和分页器 (`el-pagination`)。

## 4. 输出要求
- **格式**：最终输出**必须是**一个单一的、格式规整的ASCII字符块。
- **风格**：线框图的风格应模拟现代B端管理系统页面，清晰地展示信息层次和区域划分。
- **保真度**：线框图必须**忠实于**代码中的布局结构，准确反映组件的排布和关键文本。

---
请开始分析并生成线框图，并保存到 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/到店登记/线框图/CheckinView.md` 文件中。