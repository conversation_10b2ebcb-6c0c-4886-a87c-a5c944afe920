# Iteration Request: [Brief Name of Feature or Change]

- **Module:** [e.g., After-Sales -> Quota Management]
- **PM:** [Your Name]
- **Date:** [YYYY-MM-DD]

---

## 1. Iteration Goal

*A brief, one-sentence description of the business or user value of this iteration.*

---

## 2. Detailed Change Requests

*A numbered list of specific changes. Be as explicit as possible.*

1.  **Change:** [Description of the first change, e.g., "Remove the 'Configuration Help' section from the Add/Edit modal."]
2.  **Change:** [Description of the second change, e.g., "Disable the 'Edit' button for any configuration dated today or in the past."]
3.  ...

---

## 3. Context & File Paths

*Provide absolute paths to all relevant files for the AI to analyze.*

### 📄 Code Files
- `[Path to main Vue component, e.g., /Users/<USER>/project/src/views/Module/Component.vue]`
- `[Path to API definitions, e.g., /Users/<USER>/project/src/api/modules/module.ts]`
- `[Path to type definitions, e.g., /Users/<USER>/project/src/types/module.d.ts]`
- `[Path to relevant child components...]`

### 📚 Existing Documents
- `[Path to the current or old requirement document, e.g., /Users/<USER>/project/docs/module/spec_v1.md]`
- `[Path to entity relationship diagrams, business process flows, etc.]`

### 📐 Rules & Templates
- `[Path to the API documentation template, e.g., /Users/<USER>/project/rules/api_template.md]`
- `[Path to UI/style guide documents, if applicable]`

---

## 4. Information Source Priority

*Define the order of authority if conflicts are found between sources.*

1.  **This Document (`ITERATION_REQUEST_TEMPLATE.md`)**: The new requests here are the ultimate source of truth.
2.  **Code Files**: The actual implementation is the next source of truth for "how things currently work".
3.  **Existing Documents**: These provide business context but may be outdated.

---

## 5. Required Deliverables

*Specify the final output files the AI should generate.*

- [ ] **Iteration PRD:** A new requirement document for this iteration.
- [ ] **AI Engineer Prompt:** A set of instructions for an AI developer.
- [ ] **API Documentation:** A document detailing all new or modified APIs.
