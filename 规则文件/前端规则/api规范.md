# API开发规范

## 1. 导入规范
- 按顺序导入：基础API -> 类型定义 -> Mock数据 -> 工具配置
- 必须导入 `USE_MOCK_API` 从 `@/utils/mock-config`

## 2. 函数结构规范
- 每个API函数必须包含JSDoc注释说明用途和参数
- 函数体结构固定：Mock判断 -> 真实API调用 -> 返回结果
- 参数和返回值必须使用TypeScript类型约束

## 3. Mock切换规范
```typescript
if (USE_MOCK_API) {
  return getMockXxx(params);
}
```
- 每个API函数开头必须进行Mock判断
- Mock为true时直接返回Mock数据
- Mock为false时调用真实后端接口

## 4. 接口调用规范
```typescript
const response = await request.post<any, ApiResult<T>>('/api/xxx', params);
return response.result;
```
- 使用统一的request对象发起请求
- 明确指定泛型类型 `ApiResult<T>`
- 统一返回 `response.result` 数据部分

## 5. 命名规范
- API函数使用驼峰命名，以动词开头：`getXxx`、`adjustXxx`、`batchXxx`
- Mock函数对应添加 `Mock` 前缀或 `mock` 前缀
- 接口路径使用RESTful风格：`/api/模块/操作`