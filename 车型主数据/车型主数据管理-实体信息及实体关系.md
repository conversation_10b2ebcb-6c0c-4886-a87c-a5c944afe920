# 基础数据-车型主数据管理-实体信息及实体关系

## 1. 核心实体信息

### 1.1 车型主数据 (VehicleModelMaster)

*   **定义:** 从ERP系统同步的，用于DMS系统内部作为标准参照的车型详细信息。
*   **主要属性:**
    *   `车型主数据ID (VehicleModelMasterID)`: 唯一标识ID。
    *   `车型 (Model)`: 车辆型号，如 “MYVI”。
    *   `版本名称 (VariantName)`: 车辆配置版本名称，如 “1.5L H CVT”。
    *   `版本代码 (VariantCode)`: 车辆配置版本代码，如 “BD5HZ”。
    *   `颜色名称 (ColourName)`: 车辆颜色名称，如 “GRANITE GREY”。
    *   `颜色代码 (ColourCode)`: 车辆颜色代码，如 “S43(M)”。
    *   `FMRID (FMRID)`: 车型-版本-颜色组合的唯一标识，如 “C6505”。
    *   `创建时间 (CreateTime)`: 该条数据在DMS中被创建的时间。
    *   `更新时间 (UpdateTime)`: 该条数据在DMS中最后被更新的时间。

### 1.2 数据同步日志 (DataSyncLog)

*   **定义:** 记录每一次车型主数据同步任务的执行情况。
*   **主要属性:**
    *   `日志ID (LogID)`: 唯一标识ID。
    *   `同步时间 (SyncTime)`: 同步任务执行的时间。
    *   `同步类型 (SyncType)`: 同步任务的触发类型（手动 / 自动）。
    *   `同步状态 (SyncStatus)`: 同步任务的结果（成功 / 失败）。
    *   `成功记录数 (SuccessCount)`: 本次同步成功更新或创建的记录数量。
    *   `失败记录数 (FailedCount)`: 本次同步失败的记录数量。
    *   `错误信息 (ErrorMessage)`: 如果同步失败，记录具体的失败原因。

## 2. 实体关系图

```mermaid
ERD
    VehicleModelMaster ||--|{ DataSyncLog : updated_by

    VehicleModelMaster }|..|| ERP_System : synchronized_from
```

### 2.1 实体关系说明

*   **VehicleModelMaster (车型主数据) 与 DataSyncLog (数据同步日志):** 一对多关系。一次同步任务（一条日志）会更新多条车型主数据记录。
*   **VehicleModelMaster (车型主数据) 与 ERP_System (ERP系统):** 这是逻辑上的关联，表示车型主数据来源于外部的ERP系统，通过同步过程进入DMS。

## 3. 外部系统集成

### 3.1 ERP 系统 (Enterprise Resource Planning System)

*   **集成方式:** 数据同步（通过API接口或数据库中间表）。
*   **集成内容:** 定时或手动从ERP系统拉取全量或增量的车型主数据。
*   **数据流向:** ERP 系统 → DMS。
