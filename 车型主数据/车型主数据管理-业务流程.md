# 基础数据-车型主数据管理-业务流程

## 1. 后台管理员业务流程

### 1.1 车型数据查询流程

```mermaid
graph TD
    A[进入“车辆车型主数据管理”页面] --> B{系统加载并按创建时间倒序展示第一页数据};
    B --> C{是否需要筛选?};
    C -- 是 --> D[选择/输入筛选条件];
    D --> E{筛选条件是否为级联字段?};
    E -- 是 --> F[系统根据上一级选择动态加载下一级选项];
    E -- 否 --> G[直接输入查询值];
    F --> G;
    G --> H[点击“查询”按钮];
    H --> I[系统根据精确匹配或下拉选择的条件过滤数据];
    I --> J[在表格中展示筛选结果];
    C -- 否 --> K[直接浏览或翻页];

    D --> L[点击“重置”按钮];
    L --> M[清空所有筛选条件];
    M --> B;
```

### 1.2 数据导出流程

```mermaid
graph TD
    A[用户完成数据筛选或希望导出全部数据] --> B[点击“导出数据”按钮];
    B --> C[系统根据当前页面的筛选条件准备数据];
    C --> D[后台生成包含所有列表字段的Excel文件];
    D --> E[浏览器自动触发文件下载];
```

## 2. 系统自动化与手动流程

### 2.1 数据同步流程（手动与自动）

```mermaid
graph TD
    subgraph 手动触发
        A[后台管理员点击“同步数据”按钮] --> C{调用后台数据同步接口};
    end

    subgraph 自动触发
        B[系统定时器在每日凌晨2点触发] --> C;
    end

    C --> D[后台执行数据同步逻辑];
    D --> E[将执行结果（成功/失败/详情）写入“数据同步日志”表];
```

### 2.2 同步日志查看流程

```mermaid
graph TD
    A[后台管理员点击“同步日志”按钮] --> B[系统弹出“数据同步日志”窗口];
    B --> C[默认展示最近的同步日志];
    C --> D{是否需要筛选日志?};
    D -- 是 --> E[选择时间范围或同步状态];
    E --> F[点击“查询”按钮];
    F --> G[日志列表刷新，展示筛选结果];
    D -- 否 --> H[直接浏览日志];
    G --> I[关闭弹窗];
    H --> I;
```
