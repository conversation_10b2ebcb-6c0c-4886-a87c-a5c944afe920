# AI生成页面国际化技术规范

## 🤖 AI执行指令

**你是一个前端开发AI助手，必须严格遵循以下国际化规范生成Vue页面代码。**

### 🚨 强制性约束条件

1. **绝对禁止**：在任何Vue组件中使用硬编码的中文或英文文本
2. **强制要求**：所有用户可见文本必须使用国际化函数
3. **严格遵循**：本文档中的所有代码模式和命名规范
4. **必须验证**：生成的代码通过本文档第5节的检查清单

---

## 1. 🔧 基础配置（强制执行）

### 1.1 标准导入模板（每个Vue文件必需）

```vue
<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'

// ⚠️ AI注意：根据页面功能选择正确的模块名
const { t, tc } = useModuleI18n('模块名')
</script>
```

### 1.2 模块选择决策表（AI必须遵循）

| 页面功能关键词 | 使用模块 | 示例模块名 | AI选择逻辑 |
|------------|----------|-----------|-----------|
| 包含"零件", "库存", "采购", "供应商" | `parts.*` | `parts.management`, `parts.archives` | 检测到零件相关关键词 → 选择parts模块 |
| 包含"销售", "订单", "客户", "车辆" | `sales.*` | `sales.orders`, `sales.customers` | 检测到销售相关关键词 → 选择sales模块 |
| 包含"维修", "售后", "工单", "预约" | `aftersales.*` | `aftersales.repairs`, `aftersales.warranty` | 检测到售后相关关键词 → 选择aftersales模块 |
| 包含"用户", "角色", "权限", "菜单" | `base.*` | `base.users`, `base.roles` | 检测到系统管理关键词 → 选择base模块 |
| 通用组件或不确定 | `common` | `common` | 无法确定或跨模块 → 使用common模块 |

### 1.3 t() vs tc() 使用规则（AI严格执行）

```typescript
// ✅ 正确使用模式
const { t, tc } = useModuleI18n('parts.management')

// 业务特定内容 → 使用 t()
const pageTitle = t('title')                    // ✅ 页面标题
const fieldLabel = t('partName')               // ✅ 字段标签
const businessButton = t('createOrder')       // ✅ 业务按钮

// 通用操作内容 → 使用 tc()
const saveButton = tc('save')                  // ✅ 保存按钮
const cancelButton = tc('cancel')              // ✅ 取消按钮
const operationsColumn = tc('operations')      // ✅ 操作列
const searchButton = tc('search')              // ✅ 搜索按钮
```

---

## 2. 📝 代码生成模式（AI必须使用）

### 2.1 页面标题和标签模式

```vue
<!-- AI生成模式：页面标题 -->
<h1 class="page-title">{{ t('title') }}</h1>

<!-- AI生成模式：表单字段标签 -->
<el-form-item :label="t('partName')">
<el-form-item :label="t('partNumber')">
<el-form-item :label="t('supplierName')">
<el-form-item :label="t('createTime')">

<!-- AI生成模式：表格列标题 -->
<el-table-column :label="t('partName')" prop="partName" />
<el-table-column :label="t('partNumber')" prop="partNumber" />
<el-table-column :label="t('status')" prop="status" />
```

### 2.2 输入控件模式

```vue
<!-- AI生成模式：文本输入框 -->
<el-input 
  v-model="form.partName"
  :placeholder="t('partNamePlaceholder')"
  clearable
/>

<!-- AI生成模式：选择器 -->
<el-select v-model="form.status" :placeholder="t('selectStatus')" clearable>
  <el-option :label="t('statusActive')" value="active" />
  <el-option :label="t('statusInactive')" value="inactive" />
</el-select>

<!-- AI生成模式：日期选择器 -->
<el-date-picker
  v-model="form.dateRange"
  type="daterange"
  :range-separator="tc('to')"
  :start-placeholder="tc('startDate')"
  :end-placeholder="tc('endDate')"
/>
```

### 2.3 按钮操作模式

```vue
<!-- AI生成模式：通用按钮（使用tc） -->
<el-button type="primary" @click="handleSearch">{{ tc('search') }}</el-button>
<el-button @click="handleReset">{{ tc('reset') }}</el-button>
<el-button type="primary" @click="handleSave">{{ tc('save') }}</el-button>
<el-button @click="handleCancel">{{ tc('cancel') }}</el-button>

<!-- AI生成模式：业务按钮（使用t） -->
<el-button type="primary" @click="handleCreate">{{ t('createOrder') }}</el-button>
<el-button @click="handleExport">{{ t('exportReport') }}</el-button>

<!-- AI生成模式：表格操作列 -->
<el-table-column :label="tc('operations')" width="200" fixed="right">
  <template #default="scope">
    <el-button link type="primary" size="small" @click="handleDetail(scope.row)">
      {{ tc('detail') }}
    </el-button>
    <el-button link type="warning" size="small" @click="handleEdit(scope.row)">
      {{ tc('edit') }}
    </el-button>
    <el-button link type="danger" size="small" @click="handleDelete(scope.row)">
      {{ tc('delete') }}
    </el-button>
  </template>
</el-table-column>
```

### 2.4 JavaScript使用模式

```typescript
// AI生成模式：消息提示
ElMessage.success(tc('operationSuccessful'))    // ✅ 通用成功消息
ElMessage.error(tc('operationFailed'))          // ✅ 通用错误消息
ElMessage.success(t('orderCreatedSuccess'))     // ✅ 业务特定消息

// AI生成模式：确认对话框
await ElMessageBox.confirm(
  t('confirmDeleteMessage', { name: item.name }),
  tc('warning'),
  {
    confirmButtonText: tc('confirm'),
    cancelButtonText: tc('cancel'),
    type: 'warning'
  }
)
```

---

## 3. 🏗️ JSON键值结构规范（AI严格遵循）

### 3.1 强制使用嵌套结构

```json
{
  "页面功能": {
    "title": "页面标题",
    "fields": {
      "fieldName": "字段名称",
      "fieldNamePlaceholder": "请输入字段名称"
    },
    "actions": {
      "create": "创建",
      "edit": "编辑",
      "delete": "删除"
    },
    "status": {
      "active": "激活",
      "inactive": "禁用"
    },
    "messages": {
      "createSuccess": "创建成功",
      "deleteConfirm": "确定要删除吗？"
    }
  }
}
```

### 3.2 键名映射规则（AI必须理解）

| Vue代码中的写法 | 对应JSON路径 | AI生成规则 |
|-------------|------------|----------|
| `t('order.title')` | `sales.order.title` | 页面主标题固定使用title |
| `t('order.fields.customerName')` | `sales.order.fields.customerName` | 字段名放在fields下 |
| `t('order.fields.customerNamePlaceholder')` | `sales.order.fields.customerNamePlaceholder` | 占位符添加Placeholder后缀 |
| `t('order.actions.create')` | `sales.order.actions.create` | 操作按钮放在actions下 |
| `t('order.status.pending')` | `sales.order.status.pending` | 状态值放在status下 |
| `tc('save')` | `common.save` | 通用操作直接使用键名 |

---

## 4. 📋 AI生成页面模板

### 4.1 列表页面标准模板

```vue
<template>
  <div class="page-container">
    <!-- AI注意：页面标题固定使用 t('title') -->
    <h1 class="page-title">{{ t('title') }}</h1>
    
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('fields.name')">
              <el-input 
                v-model="searchForm.name"
                :placeholder="t('fields.namePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('fields.status')">
              <el-select v-model="searchForm.status" :placeholder="t('fields.statusPlaceholder')" clearable>
                <el-option :label="t('status.active')" value="active" />
                <el-option :label="t('status.inactive')" value="inactive" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="search-buttons">
              <!-- AI注意：搜索重置按钮使用tc -->
              <el-button type="primary" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button @click="handleReset">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    
    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <!-- AI注意：业务按钮使用t -->
      <el-button type="primary" @click="handleCreate">{{ t('actions.create') }}</el-button>
      <el-button @click="handleExport">{{ t('actions.export') }}</el-button>
    </div>
    
    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" border v-loading="loading">
        <el-table-column :label="t('fields.name')" prop="name" />
        <el-table-column :label="t('fields.status')" prop="status">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ t(`status.${row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- AI注意：操作列固定使用tc('operations') -->
        <el-table-column :label="tc('operations')" width="200" fixed="right">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="handleDetail(scope.row)">
              {{ tc('detail') }}
            </el-button>
            <el-button link type="warning" size="small" @click="handleEdit(scope.row)">
              {{ tc('edit') }}
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(scope.row)">
              {{ tc('delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
        style="margin-top: 20px; justify-content: center"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'

// AI注意：根据页面功能选择正确的模块
const { t, tc } = useModuleI18n('模块名')

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const searchForm = reactive({
  name: '',
  status: ''
})
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 方法
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, { name: '', status: '' })
  handleSearch()
}

const handleCreate = () => {
  // 创建逻辑
}

const handleEdit = (row: any) => {
  // 编辑逻辑
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t('messages.deleteConfirm', { name: row.name }),
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    )
    
    // 删除API调用
    await deleteApi(row.id)
    ElMessage.success(tc('deleteSuccess'))
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(tc('operationFailed'))
    }
  }
}

const loadData = async () => {
  loading.value = true
  try {
    // API调用
    const response = await fetchData({ ...searchForm, ...pagination })
    tableData.value = response.data
    pagination.total = response.total
  } catch (error) {
    ElMessage.error(tc('loadDataFailed'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>
```

### 4.2 表单页面标准模板

```vue
<template>
  <div class="form-container">
    <h1>{{ t('form.title') }}</h1>
    
    <el-form 
      :model="form" 
      :rules="rules" 
      ref="formRef"
      label-position="top"
      class="form-content"
    >
      <el-form-item :label="t('fields.name')" prop="name">
        <el-input 
          v-model="form.name"
          :placeholder="t('fields.namePlaceholder')"
        />
      </el-form-item>
      
      <el-form-item :label="t('fields.status')" prop="status">
        <el-select v-model="form.status" :placeholder="t('fields.statusPlaceholder')">
          <el-option :label="t('status.active')" value="active" />
          <el-option :label="t('status.inactive')" value="inactive" />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSave" :loading="loading">
          {{ tc('save') }}
        </el-button>
        <el-button @click="handleCancel">
          {{ tc('cancel') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('模块名')

const loading = ref(false)
const formRef = ref()
const form = reactive({
  name: '',
  status: 'active'
})

const rules = {
  name: [
    { required: true, message: t('validation.nameRequired'), trigger: 'blur' }
  ],
  status: [
    { required: true, message: t('validation.statusRequired'), trigger: 'change' }
  ]
}

const handleSave = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    // API调用
    await saveData(form)
    ElMessage.success(tc('saveSuccess'))
    
    // 处理保存后逻辑
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(tc('saveFailed'))
    }
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  // 取消逻辑
}
</script>
```

---

## 5. 🔍 AI执行检查清单

### 5.1 代码生成后必须检查（AI自检）

```typescript
// ✅ AI自检清单 - 基础检查
- [ ] 已导入 useModuleI18n
- [ ] 已正确解构 { t, tc }
- [ ] 模块名选择正确
- [ ] 页面标题使用 t('title')

// ✅ AI自检清单 - 模板检查  
- [ ] 所有表单标签使用 t() 函数
- [ ] 所有占位符使用 *Placeholder 格式
- [ ] 通用按钮使用 tc() 函数
- [ ] 业务按钮使用 t() 函数
- [ ] 操作列标题使用 tc('operations')

// ✅ AI自检清单 - JavaScript检查
- [ ] 消息提示使用正确函数
- [ ] 确认对话框按钮使用 tc()
- [ ] 没有任何硬编码文本

// ✅ AI自检清单 - 键值检查
- [ ] 使用嵌套结构
- [ ] 占位符添加 Placeholder 后缀
- [ ] 状态值使用合理键名
```

### 5.2 禁止操作清单（AI严格禁止）

```typescript
// ❌ AI绝对禁止的操作
❌ 在template中写任何硬编码中文: "用户管理"
❌ 在template中写任何硬编码英文: "User Management"  
❌ 使用字符串拼接: "删除" + item.name
❌ 混用t和tc函数: tc('partName') 或 t('save')
❌ 使用旧版i18n语法: $t('xxx')
❌ 忽略占位符命名规范: t('请输入名称')
❌ 创建扁平结构的JSON: { "userName": "用户名" }
```

---

## 6. ⚠️ 常见错误纠正（AI学习参考）

### 6.1 错误示例 vs 正确示例

```vue
<!-- ❌ 错误：硬编码文本 -->
<h1>用户管理</h1>
<el-button>保存</el-button>

<!-- ✅ 正确：使用国际化 -->
<h1>{{ t('title') }}</h1>
<el-button>{{ tc('save') }}</el-button>

<!-- ❌ 错误：混用t和tc -->
<el-button>{{ t('save') }}</el-button>
<h1>{{ tc('title') }}</h1>

<!-- ✅ 正确：正确区分使用 -->
<el-button>{{ tc('save') }}</el-button>
<h1>{{ t('title') }}</h1>

<!-- ❌ 错误：缺少占位符 -->
<el-input v-model="form.name" placeholder="请输入姓名" />

<!-- ✅ 正确：使用国际化占位符 -->
<el-input v-model="form.name" :placeholder="t('fields.namePlaceholder')" />
```

---

## 7. 🎯 AI生成决策树

```
开始生成Vue页面
    ↓
检查页面功能关键词
    ↓
选择对应模块 (parts/sales/aftersales/base/common)
    ↓
添加标准导入代码
    ↓
生成页面结构
    ↓
应用国际化模式：
    - 页面标题 → t('title')
    - 表单字段 → t('fields.*')
    - 通用按钮 → tc('操作名')
    - 业务按钮 → t('actions.*')
    - 占位符 → t('fields.*Placeholder')
    ↓
执行自检清单
    ↓
确认无硬编码文本
    ↓
生成完成
```

---

## 📚 AI快速参考

### 常用模式速查

```typescript
// 页面基础结构
const { t, tc } = useModuleI18n('模块名')

// 页面标题
{{ t('title') }}

// 表单字段
:label="t('字段名')"
:placeholder="t('字段名Placeholder')"

// 按钮
{{ tc('save') }}     // 通用操作
{{ t('创建业务') }}   // 业务操作

// 消息
ElMessage.success(tc('operationSuccessful'))
ElMessage.success(t('businessSuccess'))

// 状态显示
{{ t(`status.${row.status}`) }}
```

---

**🤖 AI执行准则：严格遵循以上所有规范，绝不违反任何约束条件，确保生成的代码100%符合国际化要求。**

**文档版本**: v4.0 - AI专用版本