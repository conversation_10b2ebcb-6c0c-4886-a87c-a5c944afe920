# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Parts Management System** (零件管理系统) designed for automotive DMS (Dealer Management System) environments. The system manages the complete lifecycle of automotive parts from master data synchronization to final consumption across dealership operations.

### Core Business Context

**Domain**: Automotive dealer parts management
**Architecture**: ERP ↔ DMS ↔ Store Operations
**Primary Users**: 
- **Factory Side**: Parts master data administrators, supply chain approvers, data analysts
- **Dealer Side**: Warehouse managers, pickers, maintenance advisors, store managers

### Key Business Flows
1. **Parts Master Data Management**: ERP sync → DMS validation → Archive updates
2. **Inventory Monitoring**: Real-time stock tracking → Safety stock alerts → Inventory analysis
3. **Replenishment Process**: Store requests → Factory approval → ERP integration
4. **Receiving Management**: Delivery confirmation → Damage handling → Stock updates
5. **Picking Operations**: Work order triggers → Pick list generation → Stock deduction
6. **Damage Management**: Damage reporting → Approval process → Stock adjustment

## Project Structure

### Core Documentation Files

- `零件管理背景.md` - Complete business analysis and user journey mapping
- `页面分析方法.md` - Standardized page design analysis framework (7-step methodology)
- `零件主档/` - Parts archive module with database design and wireframes
- `库存管理/` - Inventory management module with detailed wireframes

### Module Organization

```
零件管理/
├── 零件主档/                    # Parts Archive Module
│   ├── parts-module-database-design.md    # Complete database schema
│   ├── parts-archive-page-design.md       # Page analysis
│   └── parts-archive-wireframe.md         # Detailed wireframes
├── 库存管理/                    # Inventory Management Module
│   └── inventory-management-wireframe.md  # Inventory wireframes
├── 零件管理背景.md              # Business requirements & analysis
└── 页面分析方法.md              # Design methodology framework
```

## Database Architecture

### Core Tables Schema
- **parts**: Master parts data with ERP sync status
- **inventory**: Multi-warehouse stock tracking by store/part/warehouse
- **replenishment_orders** + **replenishment_order_details**: Replenishment workflow
- **receiving_orders** + **receiving_order_details**: Receiving workflow  
- **damage_orders** + **damage_order_details**: Damage reporting workflow
- **picking_orders** + **picking_order_details**: Picking workflow

### Key Relationships
- Parts (1) → Inventory (n): One part across multiple stores
- Orders (1) → Order Details (n): Header-detail pattern for all business documents
- Receiving Orders link to Replenishment Orders for traceability

### Performance Considerations
- Index strategy: Primary keys, unique constraints on document numbers, foreign key indexes
- Partitioning strategy: Consider time-based partitioning for high-volume tables
- Read replicas for reporting queries to reduce main database load

## Business Rules & Workflows

### Inventory Status Logic
- **🔴 缺货 (Out of Stock)**: `current_stock = 0`
- **⚠️ 预警 (Warning)**: `current_stock ≤ safety_stock AND current_stock > 0`
- **✅ 正常 (Normal)**: `current_stock > safety_stock AND current_stock ≤ maximum_stock`
- **📦 超储 (Overstocked)**: `current_stock > maximum_stock`

### Stock Calculations
- **Available Stock** = Current Stock - Occupied Stock - Damaged Stock
- **Occupied Stock**: Reserved for work orders but not yet picked
- **Critical Operations**: All stock changes require audit trails

### Approval Workflows
- **Replenishment**: Store Request → Factory Approval → ERP Integration
- **Damage Reports**: Store Report → Factory Review → Stock Adjustment
- **Emergency Rules**: Critical parts may have expedited approval paths

## Design Patterns & Standards

### Page Analysis Framework (7-Step Methodology)
1. **Business Requirements Analysis**: Core objectives + target users
2. **User Scenario Analysis**: Complete user journeys with flow chains
3. **Data Requirements Analysis**: Database-driven field specifications
4. **Information Architecture**: Hierarchical structure + priority levels (P0-P3)
5. **Core Function Design**: Business rules + operation dimensions
6. **Interaction Design**: Visual hierarchy + efficiency features
7. **Wireframe Design**: Complete UI specifications + responsive design

### UI/UX Patterns
- **Status Visualization**: Color-coded status indicators (🔴⚠️✅📦)
- **Information Density**: Tabular layouts with horizontal scrolling for detailed data
- **Responsive Design**: Desktop-first with mobile adaptations
- **Batch Operations**: Multi-select with bulk actions for efficiency
- **Real-time Updates**: Live data refresh with timestamp indicators

### Data Flow Patterns
- **ERP Synchronization**: Scheduled sync with manual trigger options
- **State Management**: Document-based state machines (DRAFT → PENDING → APPROVED → COMPLETED)
- **Audit Trails**: All critical operations logged with user/timestamp
- **Error Handling**: Graceful degradation with user-friendly error states

## Technical Considerations

### Integration Requirements
- **ERP Integration**: Bidirectional sync for master data and approved transactions
- **Work Order System**: Integration for picking demand generation
- **Notification System**: Multi-channel alerts (system/email/SMS) for critical events

### Performance Requirements  
- **Query Optimization**: Complex inventory queries with multiple joins
- **Caching Strategy**: Master data suitable for caching, inventory data requires real-time access
- **Scalability**: Multi-store deployment with potential for large datasets (500K+ parts)

### Security & Compliance
- **Role-Based Access**: Factory vs Store vs User-level permissions
- **Data Audit**: Complete audit trails for compliance requirements
- **Financial Controls**: Price information access controls

## Development Guidelines

### Data Modeling
- Follow the established database schema in `parts-module-database-design.md`
- Maintain referential integrity through application-layer constraints
- Implement soft deletes for audit trail preservation
- Use status enums consistently across all business documents

### User Interface Development
- Reference wireframes in respective module directories for exact specifications
- Implement responsive breakpoints for mobile/tablet/desktop
- Follow the color-coding standards for status indicators
- Ensure accessibility with proper ARIA labels and keyboard navigation

### Business Logic Implementation
- Implement inventory calculation formulas as documented
- Maintain state machine integrity for approval workflows  
- Validate business rules before database operations
- Implement proper error handling with user-friendly messages

### Testing Strategy
- Test all approval workflow state transitions
- Validate inventory calculation accuracy
- Test ERP sync error scenarios and recovery
- Verify responsive design across device types
- Test batch operations with large datasets

## Key Terminology

### Chinese-English Business Terms
- **零件档案** = Parts Archive
- **叫料** = Replenishment Request (Store requesting inventory from factory)
- **拣货** = Picking (Warehouse operations)
- **报损** = Damage Reporting
- **库位** = Storage Location
- **安全库存** = Safety Stock
- **占用库存** = Occupied/Reserved Stock

### Status Values
- **待审批** = PENDING (Awaiting Approval)  
- **已审批** = APPROVED (Approved)
- **已拒绝** = REJECTED (Rejected)
- **执行中** = IN_PROGRESS (In Progress)
- **已完成** = COMPLETED (Completed)

This system represents a comprehensive automotive parts management solution with well-defined business processes, database architecture, and user experience patterns. All development should align with the documented business requirements and follow the established design methodology.