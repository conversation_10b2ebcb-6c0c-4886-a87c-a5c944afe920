### 描述：[Service的业务含义和职责描述]
**名称：[IServiceName]**  
**对应接口个数：[N]个**

**接口[序号]定义:**
```java
/**
 * [接口业务含义和功能描述]
 */
[注解列表，如@Transactional、@Cacheable等]
[返回类型] [方法名]([参数列表]);
```

**入参名称：[ParameterClassName]**  
**入参描述：[入参对象的业务含义和用途]**  
**入参具体参数:**
```java
class [ParameterClassName] {
    /**
     * [字段业务含义]
     */
    [类型] [字段名];
    
    /**
     * [字段业务含义]
     */
    [类型] [字段名];
    
    // ... 其他字段
}
```

**返回参数名称：[ReturnClassName]**  
**返回参数描述：[返回对象的业务含义]**  
**返回参数字段：**
```java
class [ReturnClassName] {
    /**
     * [字段业务含义]
     */
    [类型] [字段名];
    
    /**
     * [字段业务含义]
     */
    [类型] [字段名];
    
    // ... 其他字段
}
```

**方法实现类：[ServiceImplClassName]**  

**数据操作：**
- **查询表**: [table1]（[表业务含义]）、[table2]（[表业务含义]）
- **更新表**: [table1]（[表业务含义]）、[table2]（[表业务含义]）  
- **新增表**: [table1]（[表业务含义]）、[table2]（[表业务含义]）
- **删除表**: [table1]（[表业务含义]）、[table2]（[表业务含义]）
- **RPC调用**: [ServiceName.methodName]()（[调用目的]）
- **缓存操作**: [缓存操作描述，如清除、读取、写入]
- **操作类型**: [纯查询操作/事务性写操作/等]
- **数据操作详细步骤**:
  1. [第一步数据操作描述]
  2. [第二步数据操作描述]
  3. [第三步数据操作描述]
  // ... 其他步骤

**方法实现逻辑：**
[第一步伪代码]
[第二步伪代码]
[第三步伪代码]
IF [条件] THEN [操作]
调用方法名，返回值赋给[变量]
遍历[集合]，对每个[元素]执行以下操作：
[子步骤1]
[子步骤2]
TRY块开始
[正常逻辑]
CATCH [异常类型] e时：
[异常处理逻辑]
// ... 其他逻辑步骤