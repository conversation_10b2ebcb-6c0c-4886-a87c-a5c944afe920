# 迭代需求文档：到店登记 (v1.1)

## 主页面线框图 (CheckinView)

```
+-----------------------------------------------------------------------------------------------------------------------------------+
| 到店登记列表                                                                                                                      |
+-----------------------------------------------------------------------------------------------------------------------------------+
|                                                                                                                                   |
|  +-[筛选条件]--------------------------------------------------------------------------------------------------------------------+  |
|  |                                                                                                                                 |
|  |  登记单号: [__________________]  车牌号: [__________________]  送修人姓名: [__________________]  送修人电话: [__________________]   |
|  |                                                                                                                                 |
|  |  登记日期: [____-__-__ 至 ____-__-__]  登记单状态: [ 全部 v]                                                        [ 查询 ] [ 重置 ]   |
|  |                                                                                                                                 |
|  +---------------------------------------------------------------------------------------------------------------------------------+  |
|                                                                                                                                   |
|  +-[操作]--------------------------------------------------------------------------------------------------------------------------+  |
|  |                                                                                                                  [+] 新建登记  [↓] 导出 |
|  +---------------------------------------------------------------------------------------------------------------------------------+  |
|                                                                                                                                   |
|  +-[登记列表]----------------------------------------------------------------------------------------------------------------------+  |
|  |                                                                                                                                 |
|  | | 序号 | 登记单号 | 车牌号 | 车龄 | 登记单状态 | 环检单号 | 送修人 | 服务顾问 | 登记时间 | 操作                                     | |
|  | |------|----------|--------|------|------------|----------|--------|----------|----------|------------------------------------------| |
|  | | 1    | KB...001 | 京A12345 | 24月 | 正常       | -        | 张三   | 李四     | 24-07-31 | [详情] [编辑] [取消] [生成环检单]      | |
|  | | 2    | KB...002 | 沪B67890 | 36月 | 正常       | EV001    | 王五   | 李四     | 24-07-31 | [详情] [编辑]                            | |
|  | | 3    | KB...003 | 粤C98765 | 12月 | 已取消     | -        | 赵六   | 李四     | 24-07-30 | [详情]                                   | |
|  | | ...  | ...      | ...    | ...  | ...        | ...      | ...    | ...      | ...      | ...                                      | |
|  |                                                                                                                                 |
|  |                                                                                                                                 |
|  |                                                                                    [ << < 1 2 3 ... > >>  共 100 条  10条/页 ]   |
|  +---------------------------------------------------------------------------------------------------------------------------------+  |
|                                                                                                                                   |
+-----------------------------------------------------------------------------------------------------------------------------------+
```

## 模态框 (Dialogs)

### 1. 新增 / 编辑登记 (CheckinFormDialog)

```
+----------------------------------------------------------+
| 新增登记                                        [ X ]    |
+----------------------------------------------------------+
|                                                          |
|  车牌号: [________________] 或 车主IC: [________________] |
|  [ 查询 ] (查询无结果时，可手动填写以下信息)             |
|                                                          |
|  +-[车辆信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  车牌号: [________________] 车架号: [________________]  |  |
|  |  车型:   [________________] 配置:   [________________]  |  |
|  |  颜色:   [________________] 里程:   [________] 公里   |  |
|  |  车龄:   [________] 个月 (自动计算)                  |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  +-[客户信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  *送修人姓名: [________________] *电话: [________________]  |  |
|  |  *服务顾问:   [________________] (自动填充)           |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  *服务类型: [ 维修 ] (不可编辑)                          |
|                                                          |
|  备注:                                                   |
|  [______________________________________________________]  |
|  [______________________________________________________]  |
|                                                          |
|                                     +----------+ +-------+  |
|                                     |  取 消   | |  保 存  |  |
|                                     +----------+ +-------+  |
+----------------------------------------------------------+
```

### 2. 查看详情 (CheckinDetailDialog) - *样式更新*

```
+----------------------------------------------------------+
| 查看详情 (KB20240731001)                        [ X ]    |
+----------------------------------------------------------+
|                                                          |
|  +-[车辆信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  车牌号: [京A12345]         车架号: [VIN001]          |  |
|  |  车型:   [X5]               配置:   [豪华版]          |  |
|  |  颜色:   [白色]             里程:   [50000] 公里      |  |
|  |  车龄:   [24] 个月                                   |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  +-[客户信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  送修人姓名: [张三]         电话: [13800138000]       |  |
|  |  服务顾问:   [李四]                                  |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  服务类型: [维修]                                        |
|                                                          |
|  备注:                                                   |
|  [客户反映刹车异响。]                                    |
|                                                          |
|  +-[状态信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  登记单状态: [正常]                                  |  |
|  |  取消原因:   [-]                                     |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|                                              +---------+   |
|                                              |  关 闭  |   |
|                                              +---------+   |
+----------------------------------------------------------+
```

### 3. 取消登记 (CancelDialog) - *新增*

```
+----------------------------------------------------------+
| 取消登记                                        [ X ]    |
+----------------------------------------------------------+
|                                                          |
|  您确定要取消登记单 KB20240731001 吗？                   |
|                                                          |
|  *取消原因:                                              |
|  [______________________________________________________]  |
|  [______________________________________________________]  |
|  (请填写取消原因)                                        |
|                                                          |
|                                                          |
|                                     +----------+ +-------+  |
|                                     |  关 闭   | |  确 认  |  |
|                                     +----------+ +-------+  |
+----------------------------------------------------------+
```

### 4. 创建环检单确认 (CreateInspectionDialog) - *新增*

```
+----------------------------------------------------------+
| 创建环检单 (基于登记单 KB20240731001)           [ X ]    |
+----------------------------------------------------------+
|                                                          |
|  +-[车辆信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  车牌号: [京A12345]         车架号: [VIN001]          |  |
|  |  ... (同详情页)                                      |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  +-[客户信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  送修人姓名: [张三]         电话: [13800138000]       |  |
|  |  ... (同详情页)                                      |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  您确认要基于以上信息创建环检单吗？                      |
|                                                          |
|                                     +----------+ +-------+  |
|                                     |  取 消   | |  确 认  |  |
|                                     +----------+ +-------+  |
+----------------------------------------------------------+
```