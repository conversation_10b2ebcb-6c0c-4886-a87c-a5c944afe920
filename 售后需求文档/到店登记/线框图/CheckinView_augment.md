# 到店登记页面线框图 (CheckinListView) - v1.1

## 主页面布局

```
+-----------------------------------------------------------------------------------------------------------------------------------+
| 到店登记列表                                                                                                                      |
+-----------------------------------------------------------------------------------------------------------------------------------+
|                                                                                                                                   |
|  +-[筛选条件]--------------------------------------------------------------------------------------------------------------------+  |
|  |                                                                                                                                 |
|  |  登记单号: [__________________]  车牌号: [__________________]  送修人姓名: [__________________]  送修人电话: [__________________]   |
|  |                                                                                                                                 |
|  |  车主IC: [__________________]  登记日期: [____-__-__ 至 ____-__-__]  登记单状态: [ 全部 ▼]              [ 查询 ] [ 重置 ]   |
|  |                                                                                                                                 |
|  +---------------------------------------------------------------------------------------------------------------------------------+  |
|                                                                                                                                   |
|  +-[操作]--------------------------------------------------------------------------------------------------------------------------+  |
|  |                                                                                                                  [+] 新建登记  [↓] 导出 |
|  +---------------------------------------------------------------------------------------------------------------------------------+  |
|                                                                                                                                   |
|  +-[登记列表]----------------------------------------------------------------------------------------------------------------------+  |
|  |                                                                                                                                 |
|  | | 序号 | 登记单号 | 车牌号 | VIN码 | 车型 | 配置 | 颜色 | 里程 | 车龄 | 送修人 | 电话 | 服务顾问 | 环检单号 | 服务类型 | 登记单状态 | 创建时间 | 更新时间 | 操作 | |
|  | |------|----------|--------|-------|------|------|------|------|------|--------|------|----------|----------|----------|------------|----------|----------|------| |
|  | | 1    | KB...001 | 京A12345| VIN001| X5   | 豪华版| 白色 | 5万km| 24月 | 张三   |138...|  李四    |    -     |   维修   |   [正常]   |24-07-31  |24-07-31  |[详情]| |
|  | |      |          |        |       |      |      |      |      |      |        |      |          |          |          |            |          |          |[编辑]| |
|  | |      |          |        |       |      |      |      |      |      |        |      |          |          |          |            |          |          |[取消]| |
|  | |      |          |        |       |      |      |      |      |      |        |      |          |          |          |            |          |          |[创建环检单]| |
|  | | 2    | KB...002 | 沪B67890| VIN002| A4   | 标准版| 黑色 | 3万km| 36月 | 王五   |139...|  李四    | EV001    |   维修   |   [正常]   |24-07-31  |24-07-31  |[详情]| |
|  | |      |          |        |       |      |      |      |      |      |        |      |          |          |          |            |          |          |[编辑]| |
|  | | 3    | KB...003 | 粤C98765| VIN003| Q5   | 舒适版| 银色 | 2万km| 12月 | 赵六   |137...|  李四    |    -     |   维修   |  [已取消]  |24-07-30  |24-07-30  |[详情]| |
|  | | ...  | ...      | ...    | ...   | ...  | ...  | ... | ...  | ... | ...    | ... |   ...    |   ...    |   ...    |    ...     |   ...    |   ...    | ... | |
|  |                                                                                                                                 |
|  |                                                                                    [ << < 1 2 3 ... > >>  共 100 条  10条/页 ]   |
|  +---------------------------------------------------------------------------------------------------------------------------------+  |
|                                                                                                                                   |
+-----------------------------------------------------------------------------------------------------------------------------------+
```

## 登记单状态下拉选项

```
登记单状态: [ 全部 ▼]
            ┌─────────┐
            │  全部   │
            │  正常   │
            │ 已取消  │
            └─────────┘
```

## 模态框线框图

### 1. 新增/编辑登记弹窗 (CheckinFormDialog)

```
+----------------------------------------------------------+
| 新增登记                                        [ X ]    |
+----------------------------------------------------------+
|                                                          |
|  车牌号: [________________] 或 车主IC: [________________] |
|  [ 查询 ] (查询无结果时，可手动填写以下信息)             |
|                                                          |
|  +-[车辆信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  车牌号: [________________] VIN码: [________________]  |  |
|  |  车型:   [________________] 配置:  [________________]  |  |
|  |  颜色:   [________________] 里程:  [________] 公里    |  |
|  |  车龄:   [________] 个月 (自动计算)                  |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  +-[客户信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  *送修人姓名: [________________] *电话: [________________]  |  |
|  |  *服务顾问:   [________________] (自动填充)           |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  *服务类型: [ 维修 ] (固定不可编辑)                      |
|                                                          |
|  备注:                                                   |
|  [______________________________________________________]  |
|  [______________________________________________________]  |
|                                                          |
|                                     +----------+ +-------+  |
|                                     |  取 消   | |  保 存  |  |
|                                     +----------+ +-------+  |
+----------------------------------------------------------+
```

### 2. 查看详情弹窗 (CheckinDetailDialog) - 样式更新

```
+----------------------------------------------------------+
| 查看详情 (KB20240731001)                        [ X ]    |
+----------------------------------------------------------+
|                                                          |
|  +-[车辆信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  车牌号: [京A12345]         VIN码: [VIN001]          |  |
|  |  车型:   [X5]               配置:  [豪华版]          |  |
|  |  颜色:   [白色]             里程:  [50000] 公里      |  |
|  |  车龄:   [24] 个月                                   |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  +-[客户信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  送修人姓名: [张三]         电话: [13800138000]       |  |
|  |  服务顾问:   [李四]                                  |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  服务类型: [维修]                                        |
|                                                          |
|  备注:                                                   |
|  [客户反映刹车异响]                                      |
|                                                          |
|  +-[状态信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  登记单状态: [正常]                                  |  |
|  |  环检单号:   [-]                                     |  |
|  |  取消原因:   [-]                                     |  |
|  |  创建时间:   [2024-07-31 10:30:00]                  |  |
|  |  更新时间:   [2024-07-31 10:30:00]                  |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|                                              +---------+   |
|                                              |  关 闭  |   |
|                                              +---------+   |
+----------------------------------------------------------+
```

### 3. 取消登记弹窗 (CancelDialog) - 新增

```
+----------------------------------------------------------+
| 取消登记                                        [ X ]    |
+----------------------------------------------------------+
|                                                          |
|  您确定要取消登记单 KB20240731001 吗？                   |
|                                                          |
|  *取消原因:                                              |
|  [______________________________________________________]  |
|  [______________________________________________________]  |
|  (请填写取消原因，此操作不可逆)                          |
|                                                          |
|                                                          |
|                                     +----------+ +-------+  |
|                                     |  关 闭   | |  确 认  |  |
|                                     +----------+ +-------+  |
+----------------------------------------------------------+
```

### 4. 创建环检单确认弹窗 (CreateInspectionDialog) - 样式更新

```
+----------------------------------------------------------+
| 创建环检单 (基于登记单 KB20240731001)           [ X ]    |
+----------------------------------------------------------+
|                                                          |
|  +-[车辆信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  车牌号: [京A12345]         VIN码: [VIN001]          |  |
|  |  车型:   [X5]               配置:  [豪华版]          |  |
|  |  颜色:   [白色]             里程:  [50000] 公里      |  |
|  |  车龄:   [24] 个月                                   |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  +-[客户信息]-----------------------------------------+  |
|  |                                                      |  |
|  |  送修人姓名: [张三]         电话: [13800138000]       |  |
|  |  服务顾问:   [李四]                                  |  |
|  |                                                      |  |
|  +------------------------------------------------------+  |
|                                                          |
|  服务类型: [维修]                                        |
|                                                          |
|  备注: [客户反映刹车异响]                                |
|                                                          |
|  您确认要基于以上信息创建环检单吗？                      |
|                                                          |
|                                     +----------+ +-------+  |
|                                     |  取 消   | |  确 认  |  |
|                                     +----------+ +-------+  |
+----------------------------------------------------------+
```

## 组件说明

### CheckinSearchForm 组件 - 更新
- 第一行包含4个输入框：登记单号、车牌号、送修人姓名、送修人电话
- 第二行包含：车主IC输入框、日期范围选择器、登记单状态下拉选择器、查询和重置按钮
- 登记单状态下拉选项：全部、正常、已取消

### CheckinTable 组件 - 更新
- 新增"车龄"列，显示车辆使用年限（月）
- 新增"登记单状态"列，显示正常/已取消状态（标签形式）
- "关联维修单号"列更名为"环检单号"列
- "服务类型"列固定显示"维修"
- 操作列调整：
  - "删除"按钮改为"取消"按钮（仅对正常状态且未生成环检单的记录显示）
  - "创建环检单"按钮（仅对正常状态且未关联环检单的记录显示）
  - 已取消状态的记录仅显示"详情"按钮

### 新增组件
- **CancelDialog**: 取消登记确认弹窗，包含必填的取消原因输入框
- **CreateInspectionDialog**: 创建环检单确认弹窗，展示完整登记信息供确认

### 交互逻辑更新
- 支持车主IC作为查询条件
- 取消登记需要填写必填的取消原因
- 登记单状态管理：正常 → 已取消（不可逆）
- 手动录入功能：查询无结果时允许手动填写车辆信息
- ID生成规则：门店简称 + 日期(YYYYMMDD) + 流水号
