# 到店登记-新增/编辑弹窗描述

## 1. 弹窗基本信息
-   **弹窗名称：** 新增/编辑登记单弹窗
-   **弹窗作用：** 用于服务顾问录入新到店登记信息或编辑现有登记信息。
-   **设计原则：** 布局清晰，引导用户按步骤填写信息；关键信息突出；提供明确的保存和取消操作；保存时进行二次确认。

## 2. 弹窗布局与主要元素

弹窗内部主要分为顶部查询框、信息录入区域（车辆信息、客户信息、服务类型、备注信息）和底部操作按钮区域。

### 2.1. 顶部查询框 (Top Query Box)
-   **位置：** 弹窗顶部。
-   **元素：**
    -   `车牌号` 输入框：用户在此输入车牌号进行车辆信息查询。
    -   `车主IC` 输入框：用户在此输入车主IC进行车辆信息查询。
-   **查询逻辑：**
    -   输入车牌号或车主IC后，系统（DMS）查询已销售车辆信息。
    -   如果查询到结果，车辆信息区域自动带入查询结果，且所有字段为只读状态，不可编辑。
    -   如果通过任何条件都未查询到结果，车辆信息区域为空或提示"未找到相关车辆信息"，并允许服务顾问手动填写。

### 2.2. 车辆信息区域 (Vehicle Information Area)
-   **位置：** 顶部查询框下方。
-   **元素：**
    -   `车牌号`：自动带入或手动填写。
    -   `车型配置`：自动带入或手动填写。
    -   `颜色`：自动带入或手动填写。
    -   `里程数`：手动填写。
    -   `车龄`：计算字段，根据当前时间减去交车时间（以月为单位）自动计算并显示。
    -   其他车辆相关信息。
-   **可编辑性：** 
    -   如果通过车牌号或车主IC查询带入的信息，则此区域字段不可编辑（只读）。
    -   如果未查询到信息，则`车牌号`、`车型`、`颜色`、`里程数`变为可手动编辑状态。

### 2.3. 客户信息区域 (Customer Information Area)
-   **位置：** 车辆信息区域下方。
-   **元素：**
    -   `送修人名称`：默认带入车主信息，但支持服务顾问手动编辑。
    -   `送修人手机号`：默认带入车主信息，但支持服务顾问手动编辑。
    -   其他客户相关信息。
-   **可编辑性：** 可手动编辑。

### 2.4. 服务类型 (Service Type)
-   **位置：** 客户信息区域下方。
-   **元素：**
    -   `服务类型` 下拉选择框：默认值为"维修"，可选择其他服务类型（如果有）。

### 2.5. 备注信息 (Notes)
-   **位置：** 服务类型下方。
-   **元素：**
    -   `备注` 多行文本框：用于填写额外说明或特殊要求。

### 2.6. 底部按钮 (Bottom Buttons)
-   **位置：** 弹窗底部。
-   **元素：**
    -   `保存` 按钮：点击后触发保存操作。
    -   `取消` 按钮：点击后关闭弹窗，不保存任何修改。

## 3. 关键交互

-   **车辆信息查询：**
    -   在顶部查询框输入车牌号或车主IC，回车或点击查询按钮触发查询。
    -   系统根据查询结果自动填充或清空车辆信息区域，并设置其可编辑状态。
-   **保存操作：**
    -   用户点击"保存"按钮。
    -   系统弹出二次确认弹框，询问用户是否确认保存。
    -   用户确认后，系统保存数据并关闭弹窗，返回到店登记列表页。
    -   用户取消则停留在当前弹窗。
-   **取消操作：**
    -   用户点击"取消"按钮，弹窗直接关闭，不保存任何修改。

## 4. 特殊业务逻辑

-   **车龄计算：** 车龄字段的计算逻辑为：`当前时间 - 交车时间`，以月为单位。
-   **客户信息编辑：** 即使车辆信息是只读的，客户信息（送修人名称、手机号）仍允许手动编辑，以应对非车主送修的情况。
-   **服务类型默认值：** 服务类型默认展示为"维修"。
-   **手动登记：** 当服务顾问无法通过任何条件查询到客户的车辆信息时，允许手动录入`车牌号`、`车型`、`颜色`、`里程数`来完成登记。 