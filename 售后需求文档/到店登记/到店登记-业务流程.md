# 到店登记-业务流程

```mermaid
sequenceDiagram
    participant Customer as 客户
    participant ServiceAdvisor as 服务顾问
    participant DMS as 系统
    participant RepairOrder as 环检单

    Customer->>ServiceAdvisor: 自然进店 (客户未预约到店)
    ServiceAdvisor->>ServiceAdvisor: 收集客户基本信息与送修信息 (口头询问)
    ServiceAdvisor->>DMS: 查询车辆信息 (通过车牌号或车主IC查询)
    alt 车辆信息存在
        DMS-->>ServiceAdvisor: 返回已销售车辆信息 (车型、车龄、车主信息等)
        ServiceAdvisor->>ServiceAdvisor: 自动填充车辆信息
    else 车辆信息不存在
        DMS-->>ServiceAdvisor: 提示未找到车辆
        ServiceAdvisor->>ServiceAdvisor: 手动录入车辆信息
    end
    ServiceAdvisor->>ServiceAdvisor: 填写/确认到店登记单信息 (客户、送修、车辆，并处理非车主送修情况)
    ServiceAdvisor->>DMS: 保存到店登记单 (提交数据)
    DMS-->>ServiceAdvisor: 登记单保存成功 (系统反馈)
    ServiceAdvisor->>DMS: 创建关联环检单 (基于已保存的登记单)
    DMS-->>RepairOrder: 生成环检单 (系统生成环检单)
    RepairOrder-->>ServiceAdvisor: 环检单创建成功并关联 (系统反馈)
```

## 业务流程描述

1.  **客户自然进店：** 客户未通过预约等方式，直接到达门店。
2.  **服务顾问收集信息：** 服务顾问接待客户，并收集客户的基本信息（如姓名、手机号）和车辆的送修信息（包含车牌号、车主IC等）。
3.  **DMS系统查询车辆信息：** 服务顾问在系统中输入车牌号或车主IC，系统自动查询并带出车辆的详细信息（如车型、车龄等）。
4.  **服务顾问填写登记单：** 如果系统查询到车辆信息，则自动填充且不可编辑。如果未查询到客户车辆信息，则允许服务顾问手动录入车牌号、车型、颜色、里程数等信息。服务顾问核对并完善登记单信息，包括客户信息（如送修人非车主时可手动编辑）、服务类型（默认为"维修"）以及备注。
5.  **保存到店登记单：** 服务顾问确认信息无误后，保存到店登记单。系统在保存前会进行二次确认。
6.  **创建关联环检单：** 登记单保存成功后，服务顾问可以基于该登记单创建对应的环检单。该操作会将到店登记单与新生成的环检单进行关联。
7.  **进入环检环节：** 环检单创建成功后，客户将进入后续的车辆环检流程。

## 登记单生命周期管理

-   **创建：** 登记单由服务顾问在系统中创建。
-   **编辑：** 在生成环检单之前，服务顾问可以对登记单信息进行编辑。
-   **取消：** 对于未生成环检单的登记单，服务顾问可以执行"取消"操作，将登记单状态置为"已取消"。登记单一旦被取消，将不能再进行编辑或创建环检单等操作。
-   **关联环检单：** 登记单成功关联环检单后，即完成了其在"到店登记"环节的使命，此时登记单本身不应再被编辑或取消。 