# 到店登记-列表页描述

## 1. 页面基本信息
-   **页面名称：** 到店登记列表页
-   **页面作用：** 用于查询、展示所有到店登记信息，并提供相关操作入口。
-   **设计原则：** 采用响应式布局，适配PC端和移动端；使用现代化UI设计，简洁清晰；关键操作突出显示，减少用户认知负担；数据展示结构化，便于快速查找和操作。

## 2. 页面布局与主要元素

页面采用经典的列表+操作区布局。顶部是筛选区域和功能区，主体区域是到店登记数据表格，底部是分页控件。

### 2.1. 筛选区域 (Filter Area)
-   **位置：** 页面顶部。
-   **元素：**
    -   `送修人名称` 输入框：支持模糊查询。
    -   `送修人手机号` 输入框：支持模糊查询。
    -   `创建时间` 日期范围选择器：支持选择起始和结束日期，用于按创建时间范围筛选。
    -   `查询` 按钮：点击后根据筛选条件刷新列表。

### 2.2. 功能区 (Action Area)
-   **位置：** 筛选区域下方，列表区域左侧。
-   **元素：**
    -   `导出` 按钮：点击后将当前查询结果导出为Excel格式文件。
    -   `创建登记单` 按钮：点击后弹出"新增/编辑登记单弹窗"。

### 2.3. 列表区域 (List Area)
-   **位置：** 页面主体中央。
-   **元素：** 以表格形式展示到店登记数据，包含以下列：
    -   `序号`
    -   `登记单号`
    -   `车牌号`
    -   `车型配置`
    -   `颜色`
    -   `送修人名称`
    -   `送修人手机号`
    -   `服务顾问`
    -   `关联环检单号`
    -   `服务类型`
    -   `创建时间`
    -   `更新时间`

### 2.4. 操作栏 (Action Column)
-   **位置：** 列表区域每行的最右侧。
-   **元素：** 每条登记单记录对应的操作按钮：
    -   `查看详情` 按钮：点击后显示登记单的详细信息。
    -   `编辑` 按钮：点击后弹出"新增/编辑登记单弹窗"，并加载当前登记单的信息进行编辑。
    -   `删除` 按钮：点击后弹出二次确认弹框。确认后，对该登记单进行逻辑删除。**注意：** 已生成环检单的登记单不显示此按钮。
    -   `创建环检单` 按钮：点击后基于当前登记单信息创建新的环检单。**注意：** 此操作不是更新，而是直接读取关联关系表来生成。

## 3. 关键交互

-   **数据导出：** 用户点击"导出"按钮，系统将当前筛选条件下的列表数据导出为Excel文件。
-   **新增登记单：** 用户点击"创建登记单"按钮，系统弹出用于新增信息的表单弹窗。
-   **查看详情：** 用户点击列表行中的"查看详情"按钮，系统展示该登记单的完整详细信息，通常以只读模式。
-   **编辑登记单：** 用户点击列表行中的"编辑"按钮，系统弹出与新增功能类似的表单弹窗，但预填充了当前登记单的数据，用户可修改。
-   **删除登记单：** 用户点击列表行中的"删除"按钮，系统弹出确认删除的对话框。用户确认后，该登记单在数据库中被标记为逻辑删除，但不在列表中显示。
-   **创建环检单：** 用户点击列表行中的"创建环检单"按钮，系统根据该登记单的信息发起创建环检单的流程。

## 4. 角色权限

-   **服务顾问：** 拥有查看、创建、编辑、删除（逻辑删除）、创建环检单的权限。
-   **服务经理：** 拥有查看列表和详情的权限，可以查看全店数据，但无新增、编辑、删除和创建环检单的权限。 