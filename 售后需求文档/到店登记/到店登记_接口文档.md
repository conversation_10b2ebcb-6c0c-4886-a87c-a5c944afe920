# 接口文档：到店登记模块

- **所属模块：** 售后 -> 到店登记
- **版本：** 1.1
- **更新日期：** 2025-07-31

---

## 1. 概述

本文档定义了“到店登记”模块迭代所涉及的后端API变更。主要包括查询接口的调整和新增的取消登记接口。

---

## 2. API 详情

### 2.1. 获取到店登记列表
此接口用于查询和筛选到店登记列表数据。

- **Endpoint:** `GET /api/aftersales/checkin`
- **权限:** 需要登录用户，并具有“到店登记查询”权限。

#### 请求参数 (Query Parameters)

| 参数名 | 类型 | 是否必须 | 描述 | 示例 |
| --- | --- | --- | --- | --- |
| `pageNum` | `integer` | 否 | 页码，默认为 1 | `1` |
| `pageSize` | `integer` | 否 | 每页数量，默认为 10 | `10` |
| `licensePlate` | `string` | 否 | 车牌号，支持模糊查询 | `京A888` |
| `ownerIc` | `string` | **否 (新增)** | 车主IC，支持精确查询 | `110101...` |
| `status` | `string` | **否 (新增)** | 登记单状态。枚举值: `NORMAL` (正常), `CANCELLED` (已取消)。不传表示查询全部。 | `NORMAL` |

#### 响应 Body (Success 200)

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "total": 1,
    "records": [
      {
        "checkinId": "KB20240723001", // [新增] 到店登记ID
        "status": "NORMAL", // [新增] 登记单状态
        "licensePlate": "京A88888",
        "vehicleModel": "奥迪 A6L",
        "vehicleColor": "黑色",
        "mileage": 50000,
        "vehicleAge": "3年", // [新增] 车龄
        "customerName": "张三",
        "customerPhone": "13800138000",
        "serviceType": "维修",
        "inspectionOrderNo": "IJ20240723005", // [变更] 环检单号 (原维修单号)
        "createdAt": "2024-07-23 10:00:00"
      }
    ]
  }
}
```

### 2.2. 取消到店登记
此接口用于将一个正常的到店登记单置为取消状态。

- **Endpoint:** `POST /api/aftersales/checkin/{id}/cancel`
- **权限:** 需要登录用户，并具有“到店登记取消”权限。

#### URL 参数

| 参数名 | 类型 | 描述 |
| --- | --- | --- |
| `id` | `string` | 目标到店登记记录的 `checkinId` |

#### 请求 Body (application/json)

```json
{
  "cancelReason": "客户决定今天不修了" // [新增] 取消原因，必填
}
```

#### 响应 Body (Success 200)

```json
{
  "code": 200,
  "message": "取消成功",
  "result": null
}
```

#### 错误响应 (Error 400/403)
- 如果登记单状态不为“正常”或已生成环检单，应返回错误。
- 如果 `cancelReason` 为空，应返回错误。
```json
{
  "code": 400,
  "message": "取消失败：该登记单无法被取消"
}
```
```json
{
  "code": 400,
  "message": "取消原因不能为空"
}
```
