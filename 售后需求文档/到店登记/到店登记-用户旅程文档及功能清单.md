# 售后业务线-到店登记-用户旅程文档及功能清单

## 1. 用户旅程

### 1.1 服务顾问 - 自然进店客户接待与登记旅程

1.  **客户接待与引导：** 客户自然进店到达门店，服务顾问主动接待并引导客户至服务台。
2.  **信息收集与登记：** 服务顾问口头询问客户基本信息（姓名、手机号）和送修信息（车牌号、车主IC、车辆问题），点击"新增登记"按钮打开"新增/编辑登记单弹窗"。
3.  **车辆信息查询：** 在弹窗中输入车牌号或车主IC，DMS系统自动查询已销售车辆信息。如果查询成功，系统自动带入车辆信息（车型、车龄、车主信息等），且车辆信息区域显示为不可编辑状态。
4.  **手动信息录入：** 如果系统未查询到车辆信息，服务顾问可手动录入车牌号、车型、颜色、里程数等信息。
5.  **登记信息确认：** 服务顾问核对自动带入或手动录入的车辆信息，如客户信息与车主信息不符则手动编辑客户信息，确认服务类型（默认"维修"），填写备注信息。
6.  **登记单保存：** 点击"保存"按钮，系统弹出二次确认对话框，确认后系统保存到店登记单记录，显示保存成功提示，弹窗自动关闭返回"到店登记列表页"。
7.  **环检单创建：** 在列表页找到对应登记单记录，点击"创建环检单"按钮，系统基于登记单信息创建环检单并建立关联关系，显示环检单创建成功提示。

### 1.2 服务经理 - 登记单监控与审查旅程

1.  **全店概览：** 登录DMS系统进入"到店登记列表页"，查看全店所有到店登记单的概览统计。
2.  **登记单审查：** 浏览登记单列表，点击"详情"按钮查看具体登记单的详细信息。
3.  **数据监控：** 通过筛选查询功能监控不同时间段、不同服务顾问的登记情况。

## 2. 核心功能点清单

### 2.1 到店登记列表页 (Checkin List-到店登记列表.html)

1.  **登记单列表展示：**
    *   **列字段：** 序号、登记单号、车牌号、车型配置、颜色、送修人名称、送修人手机号、服务顾问、关联环检单号、服务类型、状态、创建时间、更新时间、操作。
    *   **数据权限：** 服务顾问查看自己创建的登记单，服务经理查看全店所有登记单。
    *   **状态显示：**
        *   关联环检单号字段显示关联状态，已关联显示环检单号，未关联显示"-"。
        *   状态字段显示"正常"、"已取消"等。
    *   **操作按钮：** 详情、编辑、取消（条件性显示）、创建环检单（条件性显示）。

2.  **筛选查询功能：**
    *   **查询条件：** 送修人名称、送修人手机号、车主IC、状态（下拉选择：全部、正常、已取消）、创建时间范围（开始时间-结束时间）。
    *   **查询/重置：** "查询"按钮执行筛选，"重置"按钮清除所有筛选条件。

3.  **登记单操作功能：**
    *   **新增登记：** "新增登记"按钮打开"新增/编辑登记单弹窗"。
    *   **查看详情：** 操作栏"详情"按钮查看登记单完整信息（只读模式）。
    *   **编辑登记：** 操作栏"编辑"按钮打开"新增/编辑登记单弹窗"进行修改。
    *   **取消登记：** 操作栏"取消"按钮（仅对状态为"正常"且未生成环检单的登记单显示），执行操作需二次确认，成功后更新登记单状态为"已取消"。
    *   **创建环检单：** 操作栏"创建环检单"按钮（仅对状态为"正常"且未关联环检单的登记单显示），直接创建并关联环检单。

4.  **数据导出功能：**
    *   "导出Excel"按钮：导出当前筛选条件下的所有登记单数据。
    *   导出格式包含列表页所有展示字段。

### 2.2 新增/编辑登记单弹窗 (Checkin Form Dialog-登记单弹窗.html)

1.  **车辆信息区域：**
    *   **车牌号/车主IC输入：** 必填字段，支持手动输入，输入后自动触发车辆信息查询。
    *   **车辆信息展示：**车牌号、VIN、车型配置、颜色、交车时间、车龄、里程数等，查询成功后自动带入且不可编辑。
    *   **手动录入：** 未查询到信息时，`车牌号`、`车型`、`颜色`、`里程数`可手动编辑。
    *   **查询状态：** 显示车辆信息查询结果状态（查询中、查询成功、未找到车辆信息）。

2.  **客户信息区域：**
    *   **送修人信息：** 送修人姓名（必填）、送修人手机号（必填），支持手动编辑。
    *   **信息预填：** 车辆查询成功时自动使用车主信息预填，可手动修改。

3.  **服务信息区域：**
    *   **服务类型：** 下拉选择框，默认值"维修"，支持其他服务类型选择。
    -   **备注信息：** 多行文本框，非必填，支持详细问题描述。

4.  **弹窗操作：**
    *   **保存按钮：** 执行表单验证，验证通过后弹出二次确认对话框。
    *   **取消按钮：** 关闭弹窗，未保存数据将丢失（需用户确认）。
    *   **二次确认：** 保存操作需用户再次确认，确认后执行保存并关闭弹窗。

5.  **表单验证：**
    *   **必填字段验证：** 车牌号、送修人姓名、送修人手机号为必填字段。
    *   **格式验证：** 手机号格式验证，车牌号格式验证。
    *   **重复性验证：** 同一车牌号当日是否已有未完成的登记单。

### 2.3 登记单详情页 (Checkin Detail-登记单详情.html)

1.  **完整信息展示：**
    *   **车辆信息模块：** 展示车牌号、VIN、车型配置、颜色、交车时间、车龄、里程数等完整车辆信息。
    *   **客户信息模块：** 展示送修人姓名、送修人手机号等客户信息。
    *   **服务信息模块：** 展示服务类型、备注信息等服务相关信息。
    *   **系统信息模块：** 展示登记单号、状态、创建时间、更新时间、服务顾问、关联环检单号等系统信息。

2.  **关联信息展示：**
    *   **环检单关联：** 显示关联的环检单号，支持点击跳转到环检单详情。
    *   **关联状态：** 清晰标识登记单与环检单的关联状态。

3.  **操作限制：**
    *   **只读模式：** 详情页所有信息仅供查看，不提供编辑功能。
    *   **返回导航：** 提供返回列表页的导航按钮。

--- 