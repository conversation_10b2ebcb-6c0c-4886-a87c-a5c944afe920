# 迭代需求：[到店登记]

- **所属模块：** [售后 -> 到店登记]
- **产品经理：** [MaJie]
- **日期：** [2024-05-15]

---

## 1. 迭代目标

到店登记是指顾客自然进店到达门店，服务顾问主动接待并引导客户至服务台，并对顾客进行信息收集与登记，并创建环检单的过程。

---

## 2. 详细变更需求

*这是一个包含具体变更点的编号列表。请尽可能清晰、明确地描述。*

1.  **变更点：** **[变更]** 列表操作栏中删除功能调整为取消功能。
2.  **变更点：** **[新增]** 列表增加登记单状态，枚举值为：正常、已取消。
3.  **变更点：** **[新增]** 筛选条件增加登记单状态筛选，下拉选项包括：全部、正常、已取消。
4.  **变更点：** **[新增]** **增加取消登记功能**：对于状态为"正常"且未生成环检单的登记单，允许服务顾问将其状态修改为"已取消"。
5.  **变更点：** **[新增]** **增加取消原因必填项**：在取消登记单时，要求服务顾问填写取消原因，作为取消的审计依据。
6.  **变更点：** **[新增]** **增加车主IC查询**：在服务顾问为到店客户查询车辆信息时，新增 **车主IC (Owner IC)** 作为查询条件，提升查询准确率。
7.  **变更点：** **[新增]** **增加手动录入功能**：当服务顾问无法通过车牌号或车主IC查询到车辆信息时，允许其手动录入车牌号、车型、颜色、里程数等核心信息来完成登记。
8.  **变更点：** **[新增]** **到店登记ID生成规则**：
    *   **格式**：`门店简称` + `日期 (YYYYMMDD)` + `流水号` (例如: `KB12024072301`)。
    *   **依赖**：此规则的实现，需要在后台的 **门店管理** 模块中，为每个门店维护一个唯一的“门店简称”。
9.  **变更点：** **[变更]** 详情页面展示形式与新增/编辑页面保持一致。
10.  **变更点：** **[变更]** 创建环检单模态框，需要展示新增编辑时输入的车辆信息、客户信息、服务信息、备注信息，展示形式与新增/编辑页面保持一致，但都不可编辑。
11.  **变更点：** **[变更]** 列表关联维修单号字段调整为环检单号字段。
12.  **变更点：** **[新增]** 列表新增车龄字段信息
13.  **变更点：** **[变更]** 因为到店登记创建时只有维修一种类型，所以列表中服务类型只有维修。



---

## 3. 上下文与文件路径

*请提供所有相关文件的绝对路径，以便AI进行分析。*

### 📄 代码文件
- 主Vue组件的路径,：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/checkin/CheckinView.vue`
- API定义文件路径, `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/api/modules/afterSales/checkin.ts`
- 类型定义文件路径, `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/types/afterSales/checkin.d.ts`
- 其他相关子组件的路径， `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/checkin/components.`

### 📚 现有文档
- 当前或旧版的需求文档路径:
`/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/到店登记/到店登记-用户旅程文档及功能清单.md`
`/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/到店登记/到店登记-实体信息及实体关系.md`
`/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/到店登记/到店登记-术语.md`
`/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/到店登记/到店登记-业务流程.md]`
- ``

### 📐 规范与模板
- 接口文档模板路径: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/规则文件/前端规则/接口文档模版.md`
- UI/风格指南等规范文件的路径,：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/需求文档/parts/ 页面风格.md`

---

## 4. 信息源优先级

*当不同信息源之间存在冲突时，请定义它们的权威顺序。*

1.  **本需求模板 (`迭代需求模板.md`)**: 本次提出的新需求是最高优先级的最终事实。
2.  **代码文件**: 项目的当前实现是理解“现状”的第二信息源。
3.  **现有文档**: 这些文档提供业务背景，但可能已过时，优先级最低。

---

## 5. 期望交付产出物

*请明确指定AI需要最终生成的交付文件。*

- [ ] **迭代需求文档(PRD):** 为本次迭代生成一份新的需求文档。
- [ ] **AI工程师开发指令:** 为AI前端工程师生成一份开发任务指令。
- [ ] **接口文档:** 为所有新增或修改的API生成一份详细的接口文档。
