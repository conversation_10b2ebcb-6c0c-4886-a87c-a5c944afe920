# 迭代需求文档：到店登记

- **所属模块：** 售后 -> 到店登记
- **产品经理：** MaJie
- **更新日期：** 2025-07-31
- **原始需求日期：** 2024-05-15
- **版本：** 1.1

---

## 1. 迭代目标

本次迭代旨在优化“到店登记”流程，提升服务顾问的工作效率和数据准确性。主要目标是引入登记单的取消操作和状态管理，增强车辆查询方式，并允许在无法自动查询到车辆信息时进行手动录入，同时对列表和详情页面的信息展示进行标准化。

---

## 2. 功能需求详情

### 2.1. 列表页面变更

1.  **[变更] 操作调整：** 列表操作栏中的“删除”功能调整为“取消”功能。
2.  **[新增] 状态显示：** 列表新增“登记单状态”列，显示“正常”或“已取消”。
3.  **[新增] 状态筛选：** 筛选条件新增“登记单状态”下拉筛选，选项包括：全部、正常、已取消。
4.  **[变更] 字段调整：** 列表中的“关联维修单号”字段调整为“环检单号”字段。
5.  **[新增] 车龄显示：** 列表新增“车龄”字段。
6.  **[固定] 服务类型：** 列表的“服务类型”字段固定显示为“维修”。

### 2.2. 核心功能新增与变更

7.  **[新增] 取消登记功能：**
    *   **触发条件：** 用户点击状态为“正常”且未生成环检单的登记记录对应的“取消”按钮。
    *   **操作流程：** 弹出模态框，要求服务顾问必须填写“取消原因”。
    *   **结果：** 提交后，该登记单状态更新为“已取消”，操作不可逆。

8.  **[新增] 查询与录入功能：**
    *   **车主IC查询：** 在查询车辆信息时，增加“车主IC (Owner IC)”作为新的查询条件。
    *   **手动录入：** 当系统无法通过车牌号或车主IC查询到车辆信息时，允许服务顾问手动录入核心车辆信息（如车牌号、车型、颜色、里程数）以创建登记单。

### 2.3. ID与页面展示

9.  **[新增] ID生成规则：**
    *   **格式：** `门店简称` + `日期 (YYYYMMDD)` + `三位流水号` (例如: `KB20240723001`)。
    *   **后台依赖：** 需要“门店管理”模块支持“门店简称”的维护。

10. **[变更] 详情页面：** 详情页面的展示布局与字段，需与新增/编辑页面保持一致，所有字段为只读状态。

11. **[变更] 创建环检单模态框：**
    *   **内容：** 需要完整展示本次登记时输入的车辆信息、客户信息、服务信息及备注。
    *   **样式：** 展示形式与新增/编辑页面保持一致，但所有字段均为只读，不可编辑。

---

## 3. 相关模块与文件路径

### 代码文件
- **主视图:** `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/checkin/CheckinView.vue`
- **API定义:** `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/api/modules/afterSales/checkin.ts`
- **类型定义:** `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/types/afterSales/checkin.d.ts`
- **子组件目录:** `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/checkin/components`

### 参考文档
- **用户旅程:** `/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/到店登记/到店登记-用户旅程文档及功能清单.md`
- **实体关系:** `/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/到店登记/到店登记-实体信息及实体关系.md`
- **术语:** `/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/到店登记/到店登记-术语.md`
- **业务流程:** `/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/到店登记/到店登记-业务流程.md`
