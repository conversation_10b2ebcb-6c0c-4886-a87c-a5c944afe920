# 开发任务指令：到店登记功能迭代

- **模块:** `售后 -> 到店登记`
- **负责人:** AI前端工程师
- **关联PRD:** `到店登记_PRD.md`
- **关联接口文档:** `到店登记_接口文档.md`

---

## 任务概述

根据产品需求，本次迭代需要对“到店登记”模块进行功能增强和UI调整。请根据以下任务清单，并参考相关文档，完成开发工作。

**主要文件路径:**
- **主视图:** `src/views/afterSales/checkin/CheckinView.vue`
- **API定义:** `src/api/modules/afterSales/checkin.ts`
- **类型定义:** `src/types/afterSales/checkin.d.ts`
- **子组件:** `src/views/afterSales/checkin/components/`

---

## 前端开发任务清单

### Phase 1: 列表页面 (`CheckinView.vue`)

- [ ] **任务 1.1 (筛选区):**
    - [ ] 在筛选条件中，新增“车主IC”输入框。
    - [ ] 新增“登记单状态”下拉选择器，选项为：`全部`、`正常`、`已取消`。
    - [ ] 将这两个新筛选条件的值加入到调用列表API的请求参数中。

- [ ] **任务 1.2 (列表):**
    - [ ] 新增“登记单状态”列，用于展示 `status` 字段。
    - [ ] 新增“车龄”列，用于展示 `vehicleAge` 字段。
    - [ ] 将原有的“关联维修单号”列的表头和数据字段，修改为“环检单号” (`inspectionOrderNo`)。

- [ ] **任务 1.3 (操作栏):**
    - [ ] 将原有的“删除”按钮修改为“取消”按钮。
    - [ ] “取消”按钮的可见性/可用性逻辑：仅当该行数据的 `status` 为 `NORMAL` (正常) 且 `inspectionOrderNo` 为空时，该按钮才可点击。

### Phase 2: 核心逻辑

- [ ] **任务 2.1 (取消功能):**
    - [ ] 创建一个“取消登记”的模态框（Dialog/Modal）组件。
    - [ ] 模态框中包含一个必填的文本域（Textarea），用于输入“取消原因”。
    - [ ] 当用户点击列表中的“取消”按钮时，弹出此模态框。
    - [ ] 用户填写原因并点击确认后，调用 `POST /api/aftersales/checkin/{id}/cancel` 接口。
    - [ ] 接口调用成功后，刷新列表数据。

- [ ] **任务 2.2 (新增/编辑/详情):**
    - [ ] **手动录入:** 在新增登记的流程中，确保当通过车牌或IC无法查询到车辆时，用户可以直接在表单中手动输入车牌号、车型、颜色、里程数等信息。
    - [ ] **详情页:** 确保详情页面的布局和字段与新增/编辑页面保持一致，并设置为只读模式。可以考虑复用表单组件，并传入一个 `isReadonly` 或 `mode='detail'` 的 prop。
    - [ ] **创建环检单:** 点击“创建环检单”按钮弹出的模态框中，需要展示当前登记单的所有核心信息（车辆、客户、服务、备注），且全部为只读。

### Phase 3: 类型与API定义

- [ ] **任务 3.1 (类型定义):**
    - [ ] 更新 `src/types/afterSales/checkin.d.ts` 文件。
    - [ ] 在列表项的类型定义中，增加 `status`, `vehicleAge`, `checkinId` 字段。
    - [ ] 将 `repairOrderNo` 字段重命名或替换为 `inspectionOrderNo`。

- [ ] **任务 3.2 (API 定义):**
    - [ ] 更新 `src/api/modules/afterSales/checkin.ts` 文件。
    - [ ] 修改获取列表的接口函数，使其接受 `ownerIc` 和 `status` 作为查询参数。
    - [ ] 新增一个 `cancelCheckin` 函数，用于调用 `POST /api/aftersales/checkin/{id}/cancel` 接口，它应接受 `id` 和 `cancelReason` 作为参数。

---

请在开发完成后，进行自测，确保所有功能点均按要求实现。
