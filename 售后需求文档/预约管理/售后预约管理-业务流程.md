# 售后预约管理-业务流程分析文档

## 文档说明
本文档基于售后预约管理需求分析，建立完整的业务流程主线，识别关键业务节点，为系统设计提供流程依据。

## 1. 核心业务流程主线

### 1.1 售后预约全流程概览

```mermaid
graph TD
    A[客户APP预约下单] --> B[预约信息同步到DMS]
    B --> C[服务顾问查看预约]
    C --> D[客户到店]
    D --> E{客户是否签到?}
    E -->|是| F[APP扫码签到]
    E -->|否| G[预约状态：未履约]
    F --> H[预约状态：已到店]
    H --> I[服务顾问创建环检单]
    I --> J[客户重新预约或联系]
```

### 1.2 业务流程分解

#### 阶段一：预约创建阶段
**起点**：客户有售后服务需求
**终点**：预约单在DMS系统生成
**关键节点**：
1. 客户通过APP选择服务类型，可选择 **保养 (Maintenance)** 或 **维修 (Repair)**。
2. 用户可根据 **车龄 (Vehicle Age)** 和 **里程 (Mileage)** 选择推荐的保养套餐。
3. 保养套餐需要清晰地展示其包含的所有 **工时 (Labor)** 和 **零件 (Parts)** 明细。
4. 用户在选择保养套餐后，可以选择 **线上支付** 或 **到店支付**。
5. 选择门店和预约时间段。
6. 填写车辆信息和服务描述。
7. 确认预约并提交。
8. 系统生成预约单并同步到DMS。

#### 阶段二：预约管理阶段
**起点**：预约单在DMS系统显示
**终点**：客户到店签到
**关键节点**：
1. 服务顾问登录DMS预约管理页面
2. 查看当日预约列表和概况
3. 通过筛选和搜索查找特定预约
4. 查看预约详情了解客户需求
5. 为预约做好接待准备

#### 阶段三：客户到店阶段
**起点**：客户到达门店
**终点**：完成签到确认
**关键节点**：
1. 接待人员引导客户
2. 客户使用APP扫描门店二维码
3. APP验证预约信息
4. 完成签到操作
5. 系统实时更新预约状态为"已到店"

#### 阶段四：服务启动阶段
**起点**：客户完成签到
**终点**：创建环检单
**关键节点**：
1. 服务顾问确认客户到店
2. 核对客户和车辆信息
3. 确认送修人信息（可编辑）
4. 创建环检单
5. 系统自动分配服务顾问

## 2. 详细业务流程分析

### 2.1 服务顾问日常工作流程

```mermaid
graph TD
    SA1[上班后登录DMS系统] --> SA2[进入预约管理页面]
    SA2 --> SA3[通过搜索功能快速查找特定预约]
    SA3 --> SA4[点击预约单号查看详细信息]
    SA4 --> SA5[对已到店的预约创建环检单]
    SA5 --> SA6[系统自动分配服务顾问并更新状态]
    SA6 --> SA7[处理客户描述的维修需求]
    SA7 --> SA8[安排相应的技师和工位]
```

**流程说明**：
- **触发条件**：服务顾问日常开始工作。
- **核心步骤**：登录系统 -> 查看预约概况 -> 搜索查找特定预约 -> 查看详情 -> 对已到店客户创建环检单 -> 安排具体服务。
- **关键决策点**：根据预约状态（如"已到店"）和客户需求，决定是否创建环检单并启动服务。
- **异常处理**：处理未履约的预约，联系客户并记录。

### 2.2 门店经理监控流程

```mermaid
graph TD
    SM1[登录DMS] --> SM2[查看预约管理页面的整体统计数据]
    SM2 --> SM3[监控各状态预约分布和到店率指标]
    SM3 --> SM4[分析维修类型分布进行资源调配]
    SM4 --> SM5[导出预约数据进行业务分析]
    SM5 --> SM6[查看详细预约信息进行质量监控]
```

**流程说明**：
- **触发条件**：门店经理需要进行日常业务监控或数据分析。
- **核心步骤**：查看统计概览 -> 监控核心指标 -> 分析数据 -> 导出报告 -> 质量抽查。
- **关键指标**：当日预约总数、已到店/未到店/未履约数量、到店率、维修类型分布。
- **管理决策**：基于数据分析进行服务资源调配、流程优化和员工绩效评估。

### 2.3 客户到店签到流程

```mermaid
graph TD
    C1[客户到店后由接待人员引导] --> C2[客户使用APP扫描门店二维码]
    C2 --> C3[APP验证预约信息并完成签到]
    C3 --> C4[系统实时更新预约状态为"已到店"]
    C4 --> C5[DMS预约管理页面实时刷新显示最新状态]
```

**流程说明**：
- **触发条件**：已预约的客户到达门店。
- **核心步骤**：引导 -> 扫码 -> APP自动签到 -> 系统状态实时同步。
- **容错机制**：如扫码失败，支持服务顾问在DMS后台手动确认客户到店。
- **实时同步**：签到成功后，DMS页面的预约状态应在5秒内刷新，颜色标识随之改变。

## 3. 业务节点详细分析

### 3.1 关键业务节点识别

#### 节点1：预约信息同步
- **业务含义**：APP预约数据传输到DMS系统
- **输入**：客户预约信息（APP端）
- **输出**：预约单记录（DMS端）
- **关键要素**：数据完整性、同步实时性
- **异常情况**：网络中断、数据格式错误

#### 节点2：预约状态变更
- **业务含义**：预约从"未到店"变更为"已到店"
- **触发条件**：客户APP扫码签到成功
- **影响范围**：DMS预约列表、统计数据、服务安排
- **时效要求**：5秒内完成状态同步

#### 节点3：环检单创建
- **业务含义**：基于已到店预约创建质检记录
- **前置条件**：预约状态为"已到店"
- **业务价值**：启动正式服务流程
- **关联影响**：服务顾问分配、工位安排

#### 节点4：数据权限控制
- **业务含义**：确保门店数据访问隔离
- **实现方式**：基于用户门店归属过滤数据
- **安全要求**：跨店数据访问禁止
- **审计要求**：所有操作记录日志

### 3.2 业务规则分析

#### 状态转换规则
1. **初始状态**：预约创建后默认为"未到店"
2. **正向转换**：未到店 → APP签到 → 已到店
3. **异常转换**：未到店 → 超时 → 未履约
4. **终态限制**：已到店和未履约为终态，不可逆转

#### 权限控制规则
1. **门店隔离**：用户只能访问所属门店的预约数据
2. **角色权限**：
   - 服务顾问：查看、创建环检单
   - 门店经理：完整权限、数据导出
3. **操作限制**：一个预约单只能创建一次环检单
4. **审计要求**：所有关键操作（如创建、更新、导出）需要记录审计日志

#### 数据处理规则
1. **搜索匹配**：支持预约单号、车牌号、预约人手机号、送修人手机号的模糊搜索
2. **状态显示**：不同状态使用不同颜色标识
3. **批量处理**：支持对预约进行批量状态更新
4. **数据导出**：导出的数据中，客户手机号等敏感信息需要按规则进行脱敏处理
5. **支付规则**：保养类预约需要显示支付信息（线上预付/到店支付），维修类不强制显示

## 4. 异常流程处理

### 4.1 预约未履约处理流程

```mermaid
graph TD
    E1[系统检测预约超时] --> E2[自动更新状态为未履约]
    E2 --> E3[生成未履约记录]
    E3 --> E4[通知相关服务顾问]
    E4 --> E5{是否需要联系客户?}
    E5 -->|是| E6[服务顾问联系客户]
    E5 -->|否| E7[记录未履约原因]
    E6 --> E8{客户是否重新预约?}
    E8 -->|是| E9[协助客户重新预约]
    E8 -->|否| E10[记录客户反馈]
    E7 --> E11[更新预约处理状态]
    E9 --> E11
    E10 --> E11
```

### 4.2 系统同步异常处理

```mermaid
graph TD
    S1[APP签到操作] --> S2[向DMS推送状态]
    S2 --> S3{推送是否成功?}
    S3 -->|成功| S4[DMS更新状态]
    S3 -->|失败| S5[启动重试机制]
    S5 --> S6[重试次数计数]
    S6 --> S7{重试次数<3?}
    S7 -->|是| S2
    S7 -->|否| S8[记录异常日志]
    S8 --> S9[人工介入处理]
    S4 --> S10[状态同步完成]
```