# 售后预约管理-用户旅程文档及功能清单

## 1. 用户旅程

### 1.1 服务顾问 - 日常预约管理旅程

2. **查找与筛选：** 使用搜索框（支持预约单号、车牌、手机号）和筛选条件（状态、服务类型、日期等）快速定位到需要处理的预约。
3. **查看详情：** 点击预约单号，在弹窗中仔细查看预约的完整信息，包括客户信息、车辆信息、具体的服务内容（如服务包详情）以及支付状态。
4. **创建环检单：** 当客户到店后，系统状态自动更新为"已到店"。此时，为该预约创建环检单，在弹窗中确认信息（可编辑送修人信息），正式启动服务流程。
5. **处理与跟进：** 对于未履约的客户，进行联系和记录。在日常工作中，持续监控预约列表的状态变化。

### 1.2 门店经理 - 业务监控与管理旅程

2. **趋势分析：** 利用筛选功能，分析不同时间段、不同服务顾问的预约处理情况，识别业务瓶颈或增长点。
3. **质量控制：** 通过查看预约详情和操作历史，对服务流程进行质量抽查，确保服务规范得到执行。
4. **决策支持：** 点击"导出"按钮，将筛选后的数据导出为Excel表格，用于制作周报、月报，为门店的资源调配和服务优化提供数据支持。

## 2. 核心功能点清单

### 2.2 预约列表管理功能 (Appointment List Management)
- **功能描述**：以表格形式展示预约数据，并提供丰富的查询和操作功能。
- **功能点**：
  - **数据展示**：
    - 表格列：预约单号、车牌号、预约人姓名、预约人手机号、送修人姓名、送修人手机号、预约日期、预约时间段、服务类型、预约状态、服务顾问、环检单编号、创建时间、操作。
    - 状态标签：使用不同颜色清晰标识各个预约状态（如：未到店-蓝色，已到店-绿色，未履约-灰色）。
    - 分页：支持每页10/20/50/100条切换，并显示总数。
  - **筛选与搜索**：
    - 快速搜索框：支持预约单号、车牌号、预约人手机号、送修人手机号的模糊搜索。
    - 下拉筛选：支持按预约状态、服务类型、服务顾问进行单选或多选筛选。
    - 日期筛选：支持按创建时间选择日期范围。
    - 提供"重置"按钮以清除所有筛选条件。
  - **操作**：
    - **详情查看**：点击预约单号或"详情"按钮，弹出预约详情窗口。
    - **创建环检单**：仅对状态为"已到店"且无环检单的预约显示此按钮。点击后弹出确认窗口。
    - **数据导出**：提供"导出Excel"按钮，可将当前筛选结果的数据导出。

### 2.3 预约详情查看功能 (Appointment Detail View)
- **功能描述**：通过弹窗模式展示预约单的全部详细信息。
- **功能点**：
  - **分段信息展示**：
    - **预约信息**：预约单号、状态、预约时间、服务类型、客户描述。
    - **客户信息**：预约下单人姓名/手机、送修人姓名/手机。
    - **车辆信息**：车牌号、VIN、车型、配置、颜色、当前里程数、车龄。
    - **服务内容**：若为保养类型，则详细展示服务包名称、包含的工时和零配件清单。
    - **支付信息**：显示支付状态、支付金额、支付方式（线上支付/到店支付）、支付流水号。

### 2.4 质检单创建功能 (Inspection Creation)
- **功能描述**：集成在预约列表的操作中，用于启动正式服务流程。
- **功能点**：
  - 触发条件：预约状态为"已到店"。
  - 弹出确认窗口，展示完整的预约信息供服务顾问最终核对。
  - **支持编辑送修人信息**：允许在创建前最后修改送修人的姓名和手机号。
  - 创建成功后，在预约列表中关联并显示新生成的"环检单编号"。

### 2.5 数据同步和状态管理 (Data & Status Sync)
- **功能描述**：确保系统状态的实时性和准确性。
- **功能点**：
  - 实时接收APP端客户签到后的状态更新，DMS页面自动刷新。
  - 系统根据业务规则自动处理超时预约，将其状态更新为"未履约"。
  - 所有状态变更及关键操作均记录日志，以便追溯。
  - 支持批量更新预约状态。

### 2.6 权限控制功能 (Access Control)
- **功能描述**：根据用户角色和归属进行严格的权限管理。
- **功能点**：
  - **数据隔离**：服务顾问和门店经理默认只能看到自己所属门店的数据。
  - **角色权限**：
    - 服务顾问：拥有查看、筛选、创建环检单等操作权限。
    - 门店经理：拥有门店下所有数据的查看权限，以及数据导出权限。
  - **操作审计**：记录所有用户的关键操作日志。

### 2.7 Super APP 预约功能
- **功能描述**: 用户在Super APP上进行预约时提供的功能。
- **功能点**:
  - 用户可以选择 **保养 (Maintenance)** 或 **维修 (Repair)** 作为服务类型。
  - 用户可以根据 **车龄 (Vehicle Age)** 和 **里程 (Mileage)** 查看推荐的保养套餐。
  - 用户可以查看保养套餐包含的 **工时 (Labor)** 和 **零件 (Parts)** 明细。
  - 用户在选择保养套餐后，可以选择 **线上支付** 或 **到店支付**。

--- 