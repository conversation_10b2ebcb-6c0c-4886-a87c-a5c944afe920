# 售后预约管理-预约管理主页面设计文档

## 页面概述

### 页面定位
预约管理主页面是DMS系统中售后预约业务的核心工作界面，集成了预约看板概览功能和详细管理功能，为服务顾问和门店经理提供一站式的预约管理解决方案。

### 设计理念
- **角色适配**：同一页面满足服务顾问和门店经理的不同需求
- **效率优先**：关键操作不超过3次点击，常用功能前置显示
- **数据驱动**：基于实时数据提供准确的业务洞察

### 用户价值
- **服务顾问**：高效管理日常预约工作，快速响应客户需求
- **门店经理**：实时监控业务状况，进行数据驱动的运营决策
- **系统管理**：统一的数据入口，简化系统维护复杂度

## 页面布局设计

### 整体布局结构

页面采用经典的列表+操作区布局，从上到下分为6个功能区域：

```
┌─────────────────────────────────────────────────────────────┐
│ 1. 页面头部区域 (Header Area) - 高度60px                      │
├─────────────────────────────────────────────────────────────┤
│ 2. 搜索筛选区域 (Filter Area) - 高度120px                     │
├─────────────────────────────────────────────────────────────┤
│ 3. 操作统计区域 (Statistics Area) - 高度80px                  │
├─────────────────────────────────────────────────────────────┤
│ 4. 数据表格区域 (Table Area) - 自适应高度                     │
├─────────────────────────────────────────────────────────────┤
│ 5. 分页控件区域 (Pagination Area) - 高度60px                  │
└─────────────────────────────────────────────────────────────┘
```

### 1. 页面头部区域设计

#### 布局结构
- **位置**：页面顶部固定
- **高度**：60px
- **背景色**：白色 (#FFFFFF)
- **边框**：底部1px实线，颜色 #E5E5E5

#### 内容组成
```
┌─────────────────┬─────────────────────────────────┬─────────────────┐
│ 页面标题        │         面包屑导航               │   用户信息       │
│ 预约管理        │   售后管理 > 预约管理             │   张三 | 退出    │
│ (H1, 18px)      │   (14px, 灰色)                  │   (14px)        │
└─────────────────┴─────────────────────────────────┴─────────────────┘
```

#### 设计细节
- **页面标题**：使用18px字体，字重600，颜色#333333
- **面包屑**：使用14px字体，颜色#666666，">"分隔符
- **用户信息**：显示当前登录用户姓名和退出链接

### 2. 搜索筛选区域设计

#### 布局结构
- **样式**：卡片式布局，圆角8px，阴影效果
- **背景色**：白色 (#FFFFFF)
- **内边距**：20px
- **外边距**：底部16px

#### 第一行筛选控件
```
┌──────────────┬──────────────┬──────────────┬──────────────┬──────────────┐
│  预约单号     │   车牌号     │  预约人手机   │  送修人手机   │   创建时间    │
│ [输入框280px] │[输入框150px] │[输入框150px] │[输入框150px] │[日期范围选择器]│
└──────────────┴──────────────┴──────────────┴──────────────┴──────────────┘
```

#### 第二行筛选控件
```
┌──────────────┬──────────────┬──────────────┬──────────────┬──────────────┐
│   状态筛选    │  服务类型    │  服务顾问    │   搜索按钮    │   重置按钮    │
│ [多选下拉框] │ [单选下拉框] │ [多选下拉框] │ [主要按钮]   │ [次要按钮]   │
└──────────────┴──────────────┴──────────────┴──────────────┴──────────────┘
```

#### 控件设计规范
- **输入框**：高度40px，边框1px #D9D9D9，圆角4px
- **下拉框**：高度40px，支持多选标签显示
- **日期选择器**：支持快速选择今天、昨天、本周、本月
- **按钮**：高度40px，圆角6px，搜索按钮蓝色主题，重置按钮灰色

### 3. 操作统计区域设计

#### 布局结构
- **样式**：卡片式布局，与筛选区域间距16px
- **高度**：80px
- **内容分布**：左侧统计信息，右侧操作按钮

#### 统计信息展示
```
┌─────────────────────────────────────────────────────┬─────────────┐
│ 共找到 156 条记录                                    │             │
│ 未到店(45) 已到店(89) 未履约(22)                    │ [导出Excel] │
│ 保养(98) 维修(58)                                   │             │
└─────────────────────────────────────────────────────┴─────────────┘
```

#### 设计规范
- **统计数字**：使用16px字体，字重600，蓝色主题 (#1890FF)
- **状态标签**：使用对应状态的颜色，圆角标签样式
- **导出按钮**：120px宽度，蓝色主题，包含下载图标

### 4. 数据表格区域设计

#### 表格整体设计
- **样式**：卡片式布局，圆角8px，阴影效果
- **表头**：灰色背景 (#FAFAFA)，高度48px
- **行高**：数据行高度56px，便于操作按钮布局
- **边框**：表格线条使用 #F0F0F0

#### 表格列结构设计

| 列名 | 宽度 | 对齐 | 数据类型 | 特殊样式 |
|------|------|------|----------|----------|
| 预约单号 | 140px | 左对齐 | 链接文本 | 蓝色可点击，字重500 |
| 车牌号 | 100px | 中心 | 高亮文本 | 黑色字体，字重600 |
| 预约人姓名 | 100px | 中心 | 普通文本 | 常规显示 |
| 预约人手机 | 120px | 中心 | 脱敏文本 | 138****1234格式 |
| 送修人姓名 | 100px | 中心 | 普通文本 | 常规显示 |
| 送修人手机 | 120px | 中心 | 脱敏文本 | 138****1234格式 |
| 预约日期 | 100px | 中心 | 日期文本 | MM-DD格式显示 |
| 预约时间段 | 120px | 中心 | 时间文本 | HH:MM-HH:MM格式 |
| 服务类型 | 80px | 中心 | 是 | 彩色标签，圆角样式 |
| 预约状态 | 100px | 中心 | 徽章 | 状态徽章，不同颜色 |
| 服务顾问 | 100px | 中心 | 普通文本 | 常规显示 |
| 环检单编号 | 120px | 中心 | 链接文本 | 有值时显示为链接 |
| 创建时间 | 140px | 中心 | 时间文本 | MM-DD HH:MM格式 |
| 操作 | 160px | 中心 | 按钮组 | 操作按钮集合 |

#### 状态颜色规范
- **未到店**：蓝色徽章 (背景#E6F7FF, 文字#1890FF, 边框#91D5FF)
- **已到店**：绿色徽章 (背景#F6FFED, 文字#52C41A, 边框#B7EB8F)
- **未履约**：红色徽章 (背景#FFF2F0, 文字#FF4D4F, 边框#FFCCC7)

#### 操作按钮设计
每行操作列包含以下按钮（根据状态动态显示）：

1. **详情按钮**
   - 样式：文本按钮，蓝色主题
   - 图标：eye图标 + "详情"文字
   - 显示条件：所有预约都显示

2. **创建环检单按钮**
   - 样式：主要按钮，绿色主题
   - 图标：plus图标 + "创建环检单"文字
   - 显示条件：状态为"已到店"且未创建环检单

3. **更多操作下拉**
   - 样式：次要按钮，灰色主题
   - 图标：more图标
   - 内容：取消预约、编辑等操作

### 5. 分页控件区域设计

#### 布局结构
- **位置**：表格下方，与表格间距16px
- **高度**：60px
- **对齐方式**：居中对齐

#### 分页组件设计
```
┌──────────────┬─────────────────────────────────┬──────────────────┐
│ 每页显示条数  │          页码导航                │    页面信息       │
│ 每页 [20] 条 │ 首页 上一页 1 2 [3] 4 5 下一页 末页 │ 第41-60条,共156条 │
└──────────────┴─────────────────────────────────┴──────────────────┘
```

#### 交互设计
- **每页条数**：支持10/20/50/100选项
- **页码导航**：当前页高亮显示，支持直接点击
- **快速跳转**：支持输入页码直接跳转
- **总数显示**：实时显示当前页范围和总记录数

## 页面交互设计

### 1. 页面加载交互

#### 初始加载
1. **默认筛选**：页面加载时自动设置日期为今天
2. **数据获取**：并行加载预约列表和统计数据
3. **加载状态**：显示骨架屏，避免白屏等待
4. **错误处理**：网络异常时显示重试提示

#### 加载性能
- **首屏加载**：< 2秒完成数据显示
- **后续交互**：< 1秒响应用户操作
- **大数据优化**：虚拟滚动支持1000+条记录

### 2. 搜索筛选交互

#### 实时搜索
- **搜索框**：输入时实时过滤，防抖500ms
- **筛选联动**：筛选条件变更时立即更新结果
- **条件保存**：筛选条件在会话中持久化
- **重置功能**：一键清空所有筛选条件

#### 筛选结果展示
- **结果统计**：实时更新找到的记录数
- **无结果处理**：显示友好的无数据提示
- **筛选标签**：显示当前活跃的筛选条件
- **快速清除**：支持单独清除某个筛选条件

### 3. 表格数据交互

#### 数据展示
- **状态更新**：客户签到后5秒内自动刷新状态
- **排序功能**：支持按时间、状态等字段排序
- **行选择**：支持单选和多选操作
- **行高亮**：鼠标悬停时行背景变色

#### 操作反馈
- **按钮状态**：操作按钮根据预约状态动态显示
- **加载提示**：操作执行时显示loading状态
- **成功反馈**：操作成功后显示绿色提示信息
- **错误处理**：操作失败时显示红色错误信息

### 4. 详情查看交互

#### 弹窗设计
- **触发方式**：点击预约单号或详情按钮
- **弹窗尺寸**：宽度800px，高度自适应，最大90vh
- **关闭方式**：ESC键、点击遮罩、关闭按钮
- **内容滚动**：弹窗内容支持垂直滚动

#### 内容展示
- **信息分组**：按业务逻辑分段展示信息
- **差异化显示**：保养和维修类型显示不同内容
- **数据格式化**：时间、金额等按规范格式显示
- **状态标识**：重要状态信息突出显示

### 5. 环检单创建交互

#### 创建流程
1. **权限检查**：验证预约状态和用户权限
2. **信息确认**：展示预约信息供用户确认
3. **信息编辑**：支持编辑送修人相关信息
4. **创建执行**：显示创建进度和结果反馈
5. **状态同步**：自动更新页面显示状态

#### 用户体验
- **确认对话框**：清晰展示即将创建的信息
- **编辑友好**：必填字段标识，格式验证提示
- **进度反馈**：创建过程显示进度条或loading
- **成功跳转**：创建成功后可选择跳转到环检单

## 响应式设计

### 1. PC端布局 (≥1200px)

#### 完整功能展示
- 所有功能区域完整显示
- 表格显示所有列信息
- 筛选控件水平排列
- 操作按钮完整显示

#### 布局优化
- 最大宽度1440px，超出部分居中
- 左右边距最小20px
- 表格列宽度固定，内容居中对齐

### 2. 平板端布局 (768px-1199px)

#### 布局调整
- 筛选控件可能换行显示
- 表格隐藏次要列（如VIN等）
- 操作按钮合并为下拉菜单
- 统计信息垂直排列

#### 交互优化
- 增大触摸热区至44px
- 优化下拉框的触摸体验
- 弹窗宽度调整为90%

### 3. 移动端布局 (<768px)

#### 布局重构
- 筛选控件纵向排列
- 表格转换为卡片列表布局
- 搜索功能收缩为抽屉
- 分页改为加载更多

#### 卡片设计
```
┌─────────────────────────────────────┐
│ AP202411250001        [未到店]      │
│ 京A12345 | BMW X5 | 保养            │
│ 张三 138****1234                   │
│ 今日 09:00-10:00 | 李四(服务顾问)    │
│ [详情] [创建环检单]                 │
└─────────────────────────────────────┘
```

## 技术实现要点

### 1. 前端技术栈
- **框架**：Vue 3 + TypeScript
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router 4

### 2. 性能优化
- **虚拟滚动**：大数据量表格性能优化
- **懒加载**：图片和非关键资源懒加载
- **缓存策略**：筛选条件和用户偏好缓存
- **防抖节流**：搜索和筛选操作优化

### 3. 数据管理
- **实时更新**：WebSocket或轮询实现状态同步
- **乐观更新**：操作即时反馈，后台验证
- **错误重试**：网络异常时自动重试机制
- **离线支持**：基础功能离线可用

### 4. 安全考虑
- **数据脱敏**：敏感信息自动处理
- **权限控制**：基于角色的功能访问控制
- **操作日志**：关键操作记录审计日志
- **XSS防护**：用户输入内容安全处理

## 可用性设计

### 1. 操作效率
- **键盘快捷键**：支持常用操作快捷键
- **批量操作**：支持多选批量处理
- **快速跳转**：预约单号直接跳转
- **操作记忆**：记住用户操作偏好

### 2. 信息架构
- **视觉层次**：重要信息突出显示
- **信息密度**：平衡信息量和可读性
- **状态指示**：清晰的状态标识系统
- **错误提示**：友好的错误信息提示

### 3. 学习成本
- **界面一致性**：遵循统一的设计语言
- **操作引导**：新用户操作提示
- **帮助文档**：内置帮助和使用说明
- **渐进式披露**：高级功能按需显示

## 验收标准

### 1. 功能完整性
- ✅ 预约列表正确显示所有必要信息
- ✅ 搜索筛选功能正确过滤数据
- ✅ 预约详情完整展示相关信息
- ✅ 环检单创建流程正确执行
- ✅ 数据统计信息准确计算

### 2. 性能指标
- ✅ 页面首次加载时间 < 2秒
- ✅ 搜索响应时间 < 1秒
- ✅ 状态同步时间 < 5秒
- ✅ 支持1000+条记录流畅展示

### 3. 用户体验
- ✅ 界面布局合理，信息层次清晰
- ✅ 操作流程简洁，关键功能易发现
- ✅ 错误处理友好，提示信息明确
- ✅ 响应式设计适配各种设备

### 4. 数据安全
- ✅ 门店数据正确隔离
- ✅ 敏感信息正确脱敏
- ✅ 用户权限正确控制
- ✅ 操作日志正确记录

## 维护和扩展

### 1. 代码维护
- **组件化设计**：功能模块独立，便于维护
- **代码规范**：遵循团队编码规范
- **单元测试**：关键功能单元测试覆盖
- **文档完善**：组件和API文档齐全

### 2. 功能扩展
- **插件机制**：支持功能插件扩展
- **配置驱动**：通过配置调整功能行为
- **API标准化**：遵循RESTful API设计
- **版本兼容**：向后兼容的升级策略

### 3. 监控运维
- **性能监控**：页面性能实时监控
- **错误追踪**：前端错误自动上报
- **用户行为**：关键操作数据统计
- **A/B测试**：功能改进效果验证

## 变更记录
- 2024-11-25：初始版本，基于售后预约管理需求分析创建