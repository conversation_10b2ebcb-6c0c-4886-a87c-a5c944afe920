# 售后预约管理-预约详情弹窗设计文档

## 页面概述

### 页面定位
预约详情弹窗是预约管理系统中的信息展示组件，以模态弹窗形式呈现预约单的完整详细信息，为服务顾问和门店经理提供全面的预约信息查看功能。

### 设计目标
- **信息完整性**：展示预约单的所有关键信息
- **分类展示**：按业务逻辑分段展示，便于理解
- **差异化处理**：保养和维修类型信息差异化显示
- **操作便捷**：支持多种开启和关闭方式

### 触发场景
- 点击预约管理列表中的预约单号（链接样式）
- 点击预约列表操作列中的"详情"按钮
- 从其他页面跳转查看特定预约详情

## 弹窗整体设计

### 弹窗规格
- **宽度**：800px（固定宽度）
- **高度**：自适应内容，最大高度90vh
- **位置**：屏幕居中显示
- **遮罩**：半透明黑色背景，透明度0.5
- **圆角**：8px圆角设计
- **阴影**：0 8px 24px rgba(0,0,0,0.12)

### 弹窗结构
```
┌─────────────────────────────────────────────────────────────┐
│ 弹窗头部区域 (Header) - 高度60px                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 弹窗内容区域 (Content) - 自适应高度                          │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ 弹窗底部区域 (Footer) - 高度60px                              │
└─────────────────────────────────────────────────────────────┘
```

## 弹窗头部设计

### 布局结构
- **高度**：60px
- **内边距**：0 24px
- **背景色**：白色 (#FFFFFF)
- **边框**：底部1px实线 #F0F0F0

### 内容组成
```
┌─────────────────────────────────────────────────┬─────────────┐
│ 预约详情 - AP202411250001                        │      ×      │
│ (18px字体，字重600，颜色#333333)                  │  (关闭按钮)  │
└─────────────────────────────────────────────────┴─────────────┘
```

### 交互设计
- **标题格式**："预约详情 - [预约单号]"
- **关闭按钮**：24x24px图标，鼠标悬停变色
- **可点击区域**：关闭按钮44x44px热区

## 弹窗内容设计

### 内容布局
- **内边距**：24px
- **背景色**：白色 (#FFFFFF)
- **滚动**：内容超出时垂直滚动
- **分段间距**：各信息段之间24px间距

### 信息分段结构

#### 1. 预约基本信息段
```
┌─────────────────────────────────────────────────────────────┐
│ 📋 预约信息                                                  │
│ ─────────────────────────────────────────────────────────── │
│ 预约单号：AP202411250001                                     │
│ 预约状态：[已到店] (彩色徽章显示)                            │
│ 预约时间：2024-11-25 09:00-10:00                           │
│ 服务类型：[保养] (彩色标签显示)                              │
│ 创建时间：2024-11-24 15:30:22                             │
│ 客户描述：车辆异响，需要检查发动机                            │
└─────────────────────────────────────────────────────────────┘
```

**设计规范**：
- 段标题：16px字体，字重600，颜色#333333，配图标
- 字段标签：14px字体，字重400，颜色#666666
- 字段值：14px字体，字重400，颜色#333333
- 状态徽章：使用对应的颜色主题
- 客户描述：如果内容较长，限制3行显示，支持展开

#### 2. 客户信息段
```
┌─────────────────────────────────────────────────────────────┐
│ 👤 客户信息                                                  │
│ ─────────────────────────────────────────────────────────── │
│ 预约下单人：张三                                             │
│ 预约人手机：138****1234                                      │
│ 送修人姓名：李四                                             │
│ 送修人手机：139****5678                                      │
└─────────────────────────────────────────────────────────────┘
```

**设计规范**：
- 手机号脱敏：138****1234格式显示
- 姓名显示：完整显示客户姓名
- 差异标识：当预约人和送修人不同时，送修人字段高亮显示

#### 3. 车辆信息段
```
┌─────────────────────────────────────────────────────────────┐
│ 🚗 车辆信息                                                  │
│ ─────────────────────────────────────────────────────────── │
│ 车牌号：京A12345                                             │
│ VIN码：WBADT53452CJ12345                                    │
│ 车型：BMW X5                配置：2.0T豪华版                  │
│ 颜色：珠光白                里程：20000KM                     │
│ 车龄：20个月                                                 │
└─────────────────────────────────────────────────────────────┘
```

**设计规范**：
- 车牌号：18px字体，字重600，突出显示
- VIN码：等宽字体显示，便于识别
- 双列布局：车型配置、颜色里程等信息成对显示
- 单位标识：里程数显示KM单位，车龄显示月单位

#### 4. 门店信息段
```
┌─────────────────────────────────────────────────────────────┐
│ 🏪 门店信息                                                  │
│ ─────────────────────────────────────────────────────────── │
│ 预约门店：北京朝阳4S店                                       │
│ 服务顾问：李四                                               │
│ 联系电话：010-12345678                                       │
└─────────────────────────────────────────────────────────────┘
```

#### 5. 服务内容段（保养类型显示）
```
┌─────────────────────────────────────────────────────────────┐
│ 🔧 服务内容                                                  │
│ ─────────────────────────────────────────────────────────── │
│ **保养套餐：** 基础保养套餐 (PKG001)                         │
│ **预估总价：** ¥800                                          │
│                                                             │
│ **工时明细 (Labor Details)**                                │
│ ┌──────────────┬──────────┬──────────┬──────┬───────────┐                │
│ │ 项目名称     │ 项目编码 │ 标准工时 │ 单价 │ 小计      │                │
│ ├──────────────┼──────────┼──────────┼──────┼───────────┤                │
│ │ 更换机油机滤 │ L001     │ 1.0h     │ ¥200 │ ¥200      │                │
│ │ 全车检查     │ L002     │ 0.5h     │ ¥100 │ ¥50       │                │
│ └──────────────┴──────────┴──────────┴──────┴───────────┘                │
│                                                             │
│ **零件明细 (Parts Details)**                                │
│ ┌──────────────┬──────────┬──────┬──────┬───────────┐                │
│ │ 零件名称     │ 零件编码 │ 数量 │ 单价 │ 小计      │                │
│ ├──────────────┼──────────┼──────┼──────┼───────────┤                │
│ │ 宝马原厂机油 │ P001     │ 5L   │ ¥80  │ ¥400      │                │
│ │ 原厂机油滤芯 │ P002     │ 1个  │ ¥150 │ ¥150      │                │
│ └──────────────┴──────────┴──────┴──────┴───────────┘                │
└─────────────────────────────────────────────────────────────┘
```

**显示条件**：仅当服务类型为"保养"时显示此段
**设计规范**：
- 表格样式：清晰的表格边框和表头背景
- 金额显示：¥符号 + 数字，右对齐
- 合计突出：合计金额加粗显示

#### 6. 支付信息段
```
┌─────────────────────────────────────────────────────────────┐
│ 💳 支付信息                                                  │
│ ─────────────────────────────────────────────────────────── │
│ 支付方式：[线上支付] (标签显示)                              │
│ 支付状态：[已支付] (绿色徽章)                                │
│ 支付金额：¥800                                              │
│ 支付流水号：PAY202411250001                                 │
│ 支付时间：2024-11-24 15:35:18                             │
└─────────────────────────────────────────────────────────────┘
```

**显示条件**：根据是否有支付信息显示
**设计规范**：
- 支付状态：已支付(绿色)、未支付(橙色)、已退款(灰色)
- 支付金额：加粗显示，突出重要信息
- 流水号：等宽字体，便于复制

#### 7. 操作历史段
```
┌─────────────────────────────────────────────────────────────┐
│ 📝 操作历史                                                  │
│ ─────────────────────────────────────────────────────────── │
│ ○ 2024-11-24 15:30:22  系统                                │
│   预约创建成功                                               │
│                                                             │
│ ○ 2024-11-25 09:05:12  客户(APP)                           │
│   客户完成到店签到                                           │
│                                                             │
│ ● 2024-11-25 09:10:35  李四(服务顾问)                       │
│   创建环检单 QC202411250001                                 │
└─────────────────────────────────────────────────────────────┘
```

**设计规范**：
- 时间线样式：左侧圆点标识，垂直线连接
- 当前状态：实心圆点表示当前状态
- 历史记录：空心圆点表示历史记录
- 操作信息：时间 + 操作人 + 操作内容
- 环检单链接：环检单编号显示为可点击链接

## 弹窗底部设计

### 布局结构
- **高度**：60px
- **内边距**：0 24px
- **背景色**：#FAFAFA
- **边框**：顶部1px实线 #F0F0F0

### 按钮布局
```
┌─────────────────────────────────────────────────┬─────────────┐
│                                                 │   [关闭]    │
│                                                 │             │
└─────────────────────────────────────────────────┴─────────────┘
```

### 按钮设计
- **关闭按钮**：次要按钮样式，灰色主题
- **按钮尺寸**：高度32px，最小宽度80px
- **文字**："关闭"
- **对齐**：右对齐

## 交互行为设计

### 1. 打开弹窗
- **触发方式**：
  - 点击预约单号链接
  - 点击"详情"按钮
  - 程序调用API
- **动画效果**：淡入动画，持续300ms
- **遮罩显示**：同步显示背景遮罩
- **焦点管理**：弹窗获得焦点，支持ESC关闭

### 2. 关闭弹窗
- **关闭方式**：
  - 点击头部关闭按钮
  - 点击底部关闭按钮
  - 按ESC键
  - 点击背景遮罩
- **动画效果**：淡出动画，持续200ms
- **焦点回归**：焦点返回到触发元素

### 3. 内容滚动
- **滚动条**：自定义样式滚动条
- **滚动行为**：平滑滚动，支持键盘导航
- **回到顶部**：内容较长时显示回到顶部按钮

### 4. 数据刷新
- **实时更新**：预约状态变更时自动刷新内容
- **加载状态**：数据更新时显示加载指示器
- **错误处理**：数据加载失败时显示重试选项

## 响应式设计

### 1. 桌面端 (≥1200px)
- **弹窗宽度**：800px固定宽度
- **内容布局**：双列布局适用字段并排显示
- **字体大小**：标准字体大小
- **间距**：24px标准间距

### 2. 平板端 (768px-1199px)
- **弹窗宽度**：90%屏幕宽度，最大600px
- **内容布局**：单列布局
- **字体调整**：保持标准字体
- **间距调整**：减少为20px

### 3. 移动端 (<768px)
- **弹窗样式**：全屏显示，类似页面
- **头部调整**：增加返回按钮
- **内容适配**：单列布局，加大行距
- **字体优化**：适当增大字体大小
- **触控优化**：增大可点击区域

### 移动端全屏布局
```
┌─────────────────────────────────────┐
│ ← 预约详情                    ×     │  (固定头部)
├─────────────────────────────────────┤
│                                     │
│        滚动内容区域                  │  (可滚动)
│                                     │
├─────────────────────────────────────┤
│              [关闭]                 │  (固定底部)
└─────────────────────────────────────┘
```

## 数据加载和错误处理

### 1. 数据加载
- **初始状态**：显示骨架屏
- **加载时长**：超过2秒显示加载进度
- **缓存策略**：相同预约短时间内使用缓存

### 2. 错误处理
- **网络错误**：显示网络异常提示和重试按钮
- **数据错误**：显示数据加载失败信息
- **权限错误**：显示无权限访问提示
- **数据不存在**：显示预约不存在或已删除

### 3. 数据实时性
- **状态同步**：预约状态变更时自动更新
- **推送更新**：接收到更新推送时刷新内容
- **版本检查**：检测数据版本冲突

## 可访问性设计

### 1. 键盘导航
- **Tab序列**：合理的Tab导航顺序
- **ESC关闭**：ESC键快速关闭弹窗
- **Enter确认**：Enter键确认操作
- **焦点指示**：清晰的焦点指示器

### 2. 屏幕阅读器
- **语义标签**：使用合适的HTML语义标签
- **ARIA属性**：添加必要的ARIA属性
- **标题层级**：正确的标题层级结构
- **描述文本**：为复杂内容提供描述

### 3. 对比度和可读性
- **颜色对比**：确保足够的颜色对比度
- **字体大小**：最小字体不小于14px
- **颜色编码**：不仅依赖颜色传达信息
- **图标说明**：为图标提供文字说明

## 性能优化

### 1. 渲染优化
- **虚拟化**：大量数据时使用虚拟化技术
- **懒加载**：非必要内容延迟加载
- **图片优化**：使用合适的图片格式和尺寸

### 2. 内存管理
- **组件卸载**：正确清理事件监听器
- **数据清理**：及时清理不需要的数据引用
- **防抖处理**：避免频繁的数据更新

## 测试验证

### 1. 功能测试
- ✅ 弹窗正常打开和关闭
- ✅ 各信息段正确显示
- ✅ 保养和维修类型差异化显示
- ✅ 状态更新正确反映
- ✅ 错误情况正确处理

### 2. 兼容性测试
- ✅ 主流浏览器兼容性
- ✅ 不同屏幕尺寸适配
- ✅ 触摸设备交互正常
- ✅ 键盘导航功能完整

### 3. 性能测试
- ✅ 弹窗打开速度 < 300ms
- ✅ 数据加载速度 < 1秒
- ✅ 内存占用合理
- ✅ 无明显的性能瓶颈

## 变更记录
- 2024-11-25：初始版本，基于售后预约管理需求分析创建 