# 环检单管理模块迭代需求文档 (PRD) - V1.2

## 1. 文档信息

| 项目 | 内容 |
| :--- | :--- |
| **产品名称** | DMS 售后管理系统 |
| **模块名称** | 环检单管理 |
| **迭代版本** | V1.2 |
| **需求提出人** | MaJie |
| **产品经理** | Gemini AI |
| **发布日期** | 2025-08-04 |

## 2. 需求背景与目标

### 2.1 背景
当前版本的环检单管理功能较为基础，缺少标准化的检查流程、结构化的内容录入以及高效的客户确认机制。同时，随着业务流程的演进，“分配技师”这一环节已被废弃，需要在系统中彻底移除，以避免流程混淆和数据冗余。为了提升服务顾问的工作效率、改善客户服务体验并确保数据准确性，本次迭代将对环检单管理模块进行全面的功能增强和流程优化。

### 2.2 目标
1.  **流程标准化：** 建立从创建、检查、提交、确认到生成工单的标准化、自动化的业务闭环。
2.  **体验优化：** 优化筛选、列表和按钮逻辑，为服务顾问提供更流畅、高效的操作体验。
3.  **数据结构化：** 将环检内容从非结构化文本升级为结构化数据，便于后续的数据分析和智能推荐。
4.  **功能移除：** 彻底移除已废弃的“分配技师”功能，简化系统逻辑。

## 3. 功能需求详述 (Features)

### 3.1 筛选和列表视图
| 序号 | 功能点 | 详细描述 |
| :--- | :--- | :--- |
| 3.1.1 | 筛选条件调整 | 1. 新增"送修人手机号"精准查询字段。<br>2. 调整筛选条件布局为两行显示：第一行（环检单号、车牌号、送修人名称、送修人手机号），第二行（服务顾问、环检状态、创建时间范围、操作按钮）。<br>3. 完善环检状态选项，统一为：待环检、环检中、待确认、已确认。 |
| 3.1.2 | 列表字段调整 | 1. 调整列表字段顺序为：序号, 环检单号, 环检状态, 送修人名称, 送修人手机号, 车牌号, 车型, 车型配置, 颜色, 服务顾问, 登记类型, 服务类型, 确认渠道, 客户确认时间, 创建时间, 更新时间, 操作。<br>2. 新增"确认渠道"字段显示（线上确认/线下确认）。<br>3. 移除"技师"列。 |

### 3.2 详情/编辑弹窗
| 序号 | 功能点 | 详细描述 |
| :--- | :--- | :--- |
| 3.2.1 | 整体布局 | 弹窗从上至下分为四个只读信息区（客户信息、车辆信息、环检单信息）和一个核心可编辑区（环检内容清单）。 |
| 3.2.2 | 环检单信息区 | 展示环检状态、登记类型、服务类型、服务顾问、创建时间、确认渠道、客户确认时间等信息。 |
| 3.2.3 | 结构化环检内容 | 1. 停车区域记录：等候区、离开区、停车区位置记录。<br>2. 仪表盘检查：里程数记录、电池电量、续航里程、能耗显示等数值输入。<br>3. 功能性检查：仪表指示器、空调系统、雨刮清洗器、信息娱乐系统等。<br>4. 警告灯检查：电池系统、电机系统、充电系统等警告灯状态。<br>5. 外观检查：前后左右视图、车顶视图、充电口盖等。<br>6. 电动系统检查：高压电池、电机系统、充电系统检查。<br>7. 轮胎检查：胎纹深度、轮胎压力、TPMS功能等具体测量数据。<br>8. 所有检查项统一使用单选组，提供四个选项：良好(✓)、需要注意(△)、不良(✗)、不适用(○)。<br>9. 支持为每个检查项上传相关的照片附件。 |

### 3.4 工单创建功能
| 序号 | 功能点 | 详细描述 |
| :--- | :--- | :--- |
| 3.4.1 | 信息展示区域 | 1. **关联环检单信息**：环检状态、登记类型、服务类型、服务顾问、创建时间、确认渠道、客户确认时间。<br>2. **客户信息**：送修人、送修手机。<br>3. **车辆信息**：车牌号、VIN、车型、车型配置、颜色、里程数、车龄。 |
| 3.4.2 | 工单基本信息 | 1. 工单类型选择（维修/保养/保险），默认继承环检单服务类型。<br>2. 工单优先级选择（普通/紧急），默认为普通。<br>3. 工单备注输入框，支持手动添加说明。 |
| 3.4.3 | 推荐维修项目 | 基于环检结果中的"不良"项智能推荐维修项目，以复选框形式展示，支持手动添加额外维修项目。 |
| 3.4.4 | 费用统计 | 显示工单预估费用统计，包含推荐项目的预估费用合计。 |


## 4. 非功能性需求
| 序号 | 需求类型 | 描述 |
| :--- | :--- | :--- |
| 4.1 | **用户体验** | 1. 所有关键操作（提交、撤回等）增加二次确认弹窗。<br>2. 操作成功后给予明确的Toast反馈信息。<br>3. 列表默认按创建时间倒序排列。 |
| 4.2 | **安全性** | 严格遵循现有数据权限体系，服务顾问只能操作其负责范围内的环检单。 |
