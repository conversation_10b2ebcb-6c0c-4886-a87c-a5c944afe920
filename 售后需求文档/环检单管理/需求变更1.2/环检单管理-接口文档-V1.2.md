# 环检单管理 API 接口文档 - V1.2

## 1. 概述

本文档定义了环检单管理模块 (V1.2) 所需的后端 API 接口。本次迭代主要涉及对现有接口的调整、一个接口的移除以及一个新接口的增加。

## 2. 接口详情

### 2.1 获取环检单列表 (查询)

- **Endpoint:** `GET /after-sales/inspection/list`
- **描述:** 根据指定的筛选条件，分页查询环检单列表。
- **变更:**
    - **新增** 查询参数 `serviceAdvisor`。
    - **移除** 查询参数 `technician`。
    - **确保** 响应体中的列表项包含 `vehicleModel`, `vehicleConfig`, `updateTime` 字段。

- **请求参数 (Query Parameters):**

| 参数名 | 类型 | 是否必须 | 描述 |
| :--- | :--- | :--- | :--- |
| `inspectionNo` | String | N | 环检单号 (精准查询) |
| `inspectionStatus` | String | N | 环检状态 (枚举: `pending`, `in_progress`, `pending_confirm`, `confirmed`) |
| `licensePlateNo` | String | N | 车牌号 (精准查询) |
| `repairmanName` | String | N | 送修人名称 (模糊查询) |
| `repairmanPhone` | String | N | **(新增)** 送修人手机号 (精准查询) |
| `serviceAdvisor` | String | N | **(新增)** 服务顾问名称或ID |
| `createTimeRange` | Array[String] | N | 创建时间的起止范围, e.g., `["2025-08-01", "2025-08-04"]` |
| `page` | Integer | N | 页码，默认为 1 |
| `pageSize` | Integer | N | 每页数量，默认为 10 |

- **响应体 (Response Body - `200 OK`):**

```json
{
  "list": [
    {
      "inspectionNo": "IV20250804001",
      "inspectionStatus": "pending_confirm",
      "repairmanName": "张三",
      "repairmanPhone": "13800138000",
      "licensePlateNo": "京A88888",
      "vehicleModel": "Model S",
      "vehicleConfig": "豪华版",
      "color": "红色",
      "serviceAdvisor": "李四",
      "registerType": "appointment",
      "serviceType": "repair",
      "customerConfirmTime": "2025-08-04 10:30:00",
      "createTime": "2025-08-04 09:00:00",
      "updateTime": "2025-08-04 10:00:00"
    }
  ],
  "total": 1
}
```

### 2.2 分配技师 (已废弃)

- **Endpoint:** `POST /after-sales/inspection/assign`
- **描述:** 此接口已废弃，将从后端服务中移除。

### 2.3 获取可打印的环检单 (新增)

- **Endpoint:** `GET /after-sales/inspection/printable`
- **描述:** 根据环检单号，获取用于前端打印的、格式化好的 HTML 内容。
- **请求参数 (Query Parameters):**

| 参数名 | 类型 | 是否必须 | 描述 |
| :--- | :--- | :--- | :--- |
| `inspectionNo` | String | Y | 要打印的环检单的唯一编号 |

- **响应体 (Response Body - `200 OK`):**

```json
{
  "htmlContent": "<html>...完整的、包含二维码和所有环检信息的HTML结构...</html>"
}
```

### 2.4 其他接口 (无变更)

以下接口在此次迭代中无方法签名或路径变更，但其内部逻辑可能需要根据新的业务规则（如状态流转）进行调整：

-   `POST /after-sales/inspection/{inspectionNo}/submit` (提交确认)
-   `POST /after-sales/inspection/{inspectionNo}/recall` (撤回)
-   `POST /after-sales/inspection/{inspectionNo}/confirm` (客户确认)
-   `POST /after-sales/inspection/create-work-order` (创建工单)
