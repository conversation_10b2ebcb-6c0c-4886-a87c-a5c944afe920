# 环检单管理 - 迭代纪要 (V1.2)

## 1. 需求背景与目标

本次迭代 (V1.2) 旨在对DMS系统的环检单管理模块进行全面功能增强和流程优化。核心目标是完善环检单管理功能，实现标准化的环检流程管理，覆盖从筛选查询、结构化内容录入、客户确认到工单集成的全过程，从而显著提升服务顾问的工作效率和客户的服务体验。同时，根据业务调整，将彻底移除旧有的“分配技师”功能及其相关的所有业务逻辑和界面元素。

## 2. 需求变更详情 (Changelog)

### 筛选条件模块调整
1.  新增"送修人手机号"精准查询字段。
2.  调整筛选条件布局为两行显示：第一行（环检单号、车牌号、送修人名称、送修人手机号），第二行（服务顾问、环检状态、创建时间范围、操作按钮）。
3.  完善环检状态选项，统一为：待环检、环检中、待确认、已确认。

### 列表字段模块调整
4.  新增"登记类型"字段显示（预约/自然到店）。
5.  新增"服务类型"字段显示（保养/维修）。
6.  新增"客户确认时间"字段显示。
7.  新增"颜色"字段显示。
8.  新增"确认渠道"字段显示（线上确认/线下确认）。
9.  调整列表字段顺序，新增"车型"、"车型配置"、"更新时间"列，移除"技师"列。
10. 最终列表字段顺序为：序号, 环检单号, 环检状态, 送修人名称, 送修人手机号, 车牌号, 车型, 车型配置, 颜色, 服务顾问, 登记类型, 服务类型, 确认渠道, 客户确认时间, 创建时间, 更新时间, 操作。

### 操作按钮动态显示逻辑调整
9.  **详情按钮**：始终展示。
10. **编辑按钮**：仅当状态为“待环检”或“环检中”时显示。
11. **提交确认按钮**：仅当状态为“环检中”时显示。
12. **撤回按钮**：仅当状态为“待确认”时显示。
13. **打印按钮**：仅当状态为“待确认”时显示。
14. **客户确认按钮**：仅当状态为“待确认”时显示。
15. **创建工单按钮**：仅当状态为“已确认”时显示。
16. 去除分配技师按钮及相关功能。

### **详情编辑弹窗调整 (根据新文档详细化)**
17. **整体布局**: 弹窗从上至下分为四个只读信息区（客户信息、车辆信息、环检单信息）和一个核心可编辑区（环检内容清单）。
18. **信息展示**:
    *   **客户信息区**: 展示送修人名称、手机号等。
    *   **车辆信息区**: 展示车牌号、VIN、车型配置、颜色、车龄。
    *   **环检单信息区**: 展示环检状态、登记类型、服务类型、服务顾问、创建时间、确认渠道、客户确认时间等。
19. **环检内容清单结构化**: 
    *   **停车区域记录**: 等候区、离开区、停车区位置记录
    *   **仪表盘检查**: 里程数记录、电池电量、续航里程、能耗显示等数值输入
    *   **功能性检查**: 仪表指示器、空调系统、雨刮清洗器、信息娱乐系统
    *   **警告灯检查**: 电池系统、电机系统、充电系统等警告灯状态
    *   **外观检查**: 前后左右视图、车顶视图、充电口盖
    *   **电动系统检查**: 高压电池、电机系统、充电系统
    *   **轮胎检查**: 胎纹深度(FR/FL/RR/RL)、轮胎压力(FR/FL/RR/RL)、TPMS功能等具体测量数据
20. **统一检查结果选项**: 所有检查项统一使用单选组，提供四个选项：良好(✓)、需要注意(△)、不良(✗)、不适用(○)。
21. **附件上传**: 支持为每个检查项上传相关的照片附件。
22. **客户自述问题**: 新增一个独立的文本区域，用于记录客户口述的问题。
23. **编辑权限**: 严格控制编辑权限，只有在“待环检”和“环检中”状态下，环检内容清单区域才可编辑，其他所有信息区均为只读。

### 客户确认流程调整
24. 完善线上确认：系统自动推送至Super APP，客户在线确认。
25. 完善线下确认：服务顾问打印纸质单据，客户签字后上传照片。
26. 新增二次确认机制，确保操作安全。
27. 客户确认弹窗功能：展示环检单基本信息、客户确认时间选择器、签字照片上传功能（支持拖拽上传）、照片质量校验机制。

### 工单创建功能调整
28. **信息展示区域调整**: 分为三个独立信息区域：关联环检单信息、客户信息、车辆信息。
29. **工单基本信息**: 工单类型选择（维修/保养/保险）、工单优先级选择（普通/紧急）、工单备注输入。
30. **推荐维修项目**: 基于环检结果中的"不良"项智能推荐维修项目，以复选框形式展示。
31. **费用统计**: 显示工单预估费用统计，包含推荐项目的预估费用合计。
32. 自动继承环检单的客户和车辆信息。
33. 工单类型自动继承环检单服务类型。
34. 支持手动添加额外维修项目。
35. 仅"已确认"状态的环检单可创建工单。
36. 创建工单后建立关联关系，支持工单创建后跳转到工单管理页面。

### 业务流程控制调整
32. 严格按照：待环检 → 环检中 → 待确认 → 已确认 的状态流转。
33. 支持"待确认"状态撤回到"环检中"，撤回操作需要二次确认。
34. 登记类型限制：预约类型服务可为保养/维修，自然到店仅为维修。
35. 必填字段校验：创建时必须填写完整送修人和车辆信息。
36. 线下确认校验：必须上传客户签字照片并填写确认时间。

### 系统集成调整
37. 环检单提交后自动生成带二维码的电子环检单。
38. 自动推送至客户Super APP，支持APP端确认结果同步回DMS系统。
39. 支持环检单打印功能，打印内容包含二维码和完整环检信息。
40. 支持按筛选条件导出环检单数据，导出格式为Excel。

### 用户体验优化
41. 所有关键操作增加二次确认。
42. 操作成功后显示明确的反馈信息。
43. 列表默认按创建时间倒序排列，状态用不同颜色标识。
44. 支持列表字段排序功能。

### 功能移除
45. 移除分配技师功能及相关弹窗组件。
46. 移除分配技师按钮及相关业务逻辑。
47. 移除技师相关的数据字段和接口调用。

## 3. 开发实施计划

| 角色 | 任务分类 | 文件路径/模块 | 任务描述 |
| :--- | :--- | :--- | :--- |
| **后端** | API 调整 | `/after-sales/inspection/list` | 确保列表接口返回 `vehicleModel`, `vehicleConfig`, `updateTime` 字段。 |
| **后端** | API 调整 | `/after-sales/inspection/list` | 查询参数新增 `serviceAdvisor` 支持。 |
| **后端** | API 移除 | `/after-sales/inspection/assign` | 移除分配技师的API端点。 |
| **后端** | API 新增 | `/after-sales/inspection/printable` | 新增获取用于打印的HTML内容的API端点。 |
| **后端** | 业务逻辑 | 环检单模块 | 确保状态流转、数据校验等业务规则与需求一致。 |
| **前端** | **类型定义** | `src/types/afterSales/inspection.d.ts` | 移除`AssignTechnicianForm`；从`InspectionListItem`和`InspectionSearchParams`中移除`technician`；为`InspectionSearchParams`添加`serviceAdvisor`。**新增详细的环检内容清单类型定义**。 |
| **前端** | **API 服务** | `src/api/modules/afterSales/inspection.ts` | 移除`assignTechnician`函数；新增`getPrintableInspection`函数。 |
| **前端** | **国际化** | `src/locales/modules/afterSales/` | 更新环检状态的中文翻译：待环检、环检中、待确认、已确认。 |
| **前端** | **视图组件** | `src/views/afterSales/inspection/components/InspectionSearchForm.vue` | 调整筛选条件布局；新增送修人手机号、服务顾问筛选；移除技师筛选。 |
| **前端** | **视图组件** | `src/views/afterSales/inspection/components/InspectionTable.vue` | 调整列顺序和字段；移除技师列；新增车型配置、更新时间列；调整按钮显示逻辑；新增打印按钮。 |
| **前端** | **视图组件** | `src/views/afterSales/inspection/components/DetailEditDialog.vue` | **根据新的详细规格，彻底重构弹窗，实现多区域信息展示和结构化的环检内容编辑功能。** |
| **前端** | **视图组件** | `src/views/afterSales/inspection/components/CustomerConfirmDialog.vue` | 重构弹窗，实现签字照片上传、时间选择、信息展示等功能。 |
| **前端** | **视图组件** | `src/views/afterSales/inspection/components/CreateWorkOrderDialog.vue` | 重构弹窗，实现信息继承、智能推荐等功能。 |
| **前端** | **主视图** | `src/views/afterSales/inspection/InspectionView.vue` | 移除所有技师分配相关逻辑；新增打印处理逻辑。 |
| **前端** | **文件删除** | `src/views/afterSales/inspection/components/AssignDialog.vue` | 删除已废弃的分配技师弹窗组件文件。 |
| **测试** | 功能测试 | 环检单管理模块 | 对所有变更点进行功能验证，确保符合需求。 |
| **测试** | 流程测试 | 端到端流程 | 测试从创建环检单到创建工单的完整业务流程。 |
| **测试** | UI 测试 | 环检单页面 | 验证页面布局、字段显示、按钮状态等UI表现。 |

## 4. 面向AI工程师的详细开发提示词

1.  **类型定义修改**:
    *   指令: "打开 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/types/afterSales/inspection.d.ts`。删除 `AssignTechnicianForm` 接口。从 `InspectionListItem` 和 `InspectionSearchParams` 接口中删除 `technician` 字段。在 `InspectionSearchParams` 接口中添加 `serviceAdvisor?: string;` 字段。**然后，根据详细规格，为环检内容清单创建一个详细的类型接口 `InspectionContent`，包含所有检查大类和子项。**"

2.  **API服务层修改**:
    *   指令: "打开 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/api/modules/afterSales/inspection.ts`。删除 `assignTechnician` 函数及其所有引用。新增一个 `getPrintableInspection` 函数，它接受 `inspectionNo` 作为参数，并返回一个包含HTML字符串的Promise。"

3.  **搜索表单组件重构**:
    *   指令: "打开 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/InspectionSearchForm.vue`。移除‘技师’筛选框。新增‘送修人手机号’输入框和‘服务顾问’选择框。按照第一行（环检单号、车牌号、送修人名称、送修人手机号）、第二行（服务顾问、环检状态、创建时间范围、操作按钮）的布局重新组织所有筛选条件。"

4.  **列表格组件重构**:
    *   指令: "打开 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/InspectionTable.vue`。删除‘技师’列。在‘车型’列后新增‘车型配置’列，在‘创建时间’列后新增‘更新时间’列。将列的最终顺序调整为：序号, 环检单号, 环检状态, 送修人名称, 送修人手机号, 车牌号, 车型, 车型配置, 颜色, 服务顾问, 登记类型, 服务类型, 客户确认时间, 创建时间, 更新时间, 操作。修改编辑按钮的`v-if`，使其在`pending`和`in_progress`状态下都显示。在‘待确认’状态的操作中，新增一个‘打印’按钮。"

5.  **主视图逻辑清理**:
    *   指令: "打开 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/InspectionView.vue`。删除对 `AssignDialog` 组件的导入和使用。删除 `technicians` 和 `technicianOptions` 相关的 `ref` 和 `computed`。删除 `fetchTechnicianList`, `handleAssignTechnician`, `handleAssignConfirm` 函数。新增 `handlePrint` 函数，用于处理打印逻辑。"

6.  **详情编辑弹窗重构 (核心任务)**:
    *   指令: "**打开 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/DetailEditDialog.vue`。清空现有内容，并根据最新的详细规格重新实现。弹窗布局要求如下：**
        *   **只读信息区**: 使用 `<el-descriptions>` 创建三个独立的卡片，分别展示‘客户信息’、‘车辆信息’和‘环检单信息’，这些内容不可编辑。
        *   **环检内容清单区**: 这是核心可编辑区域，仅在编辑模式下启用。
            *   使用 `<el-card>` 或类似组件将环检内容按大类（如【仪表盘检查】、【功能性检查】等）进行分组。
            *   对于每个具体的检查子项（如‘里程数记录’），使用 `<el-form-item>`。
            *   对于需要选择结果的检查项，使用 `<el-radio-group>` 提供‘良好’、‘需要注意’、‘不良’、‘不适用’四个选项。
            *   在每个检查项旁边，提供一个用于上传照片的 `<el-upload>` 组件。
            *   对于需要文本输入的项（如‘里程数记录’），使用 `<el-input>`。
        *   **客户自述问题**: 在环检内容清单区末尾，添加一个 `<el-input type="textarea">` 用于记录客户自述问题。
        *   **底部操作区**: 包含‘保存’和‘取消’按钮。"

7.  **客户确认弹窗重构**:
    *   指令: "打开 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/CustomerConfirmDialog.vue`。弹窗需要展示环检单基本信息，并包含一个必填的‘客户确认时间’选择器和一个支持拖拽上传的‘签字照片’上传组件，上传组件需要有照片质量校验提示。"

8.  **创建工单弹窗重构**:
    *   指令: "打开 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/CreateWorkOrderDialog.vue`。确保客户和车辆信息能从环检单自动继承。根据环检结果中的‘不良’项，在‘推荐维修项目’区域动态生成复选框。工单类型需自动继承环检单的服务类型。"

9.  **废弃文件删除**:
    *   指令: "从文件系统中删除 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/AssignDialog.vue` 文件。"

10. **国际化文件更新**:
    *   指令: "找到位于 `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/locales/` 目录下的中文语言文件，在 `afterSales.inspection.status` 对象中，更新或添加以下键值对：`"pending": "待环检"`, `"in_progress": "环检中"`, `"pending_confirm": "待确认"`, `"confirmed": "已确认"`。"
