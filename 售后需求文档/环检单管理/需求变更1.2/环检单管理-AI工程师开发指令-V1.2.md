# 环检单管理 (V1.2) - AI 工程师开发指令

**收件人：** AI 前端开发工程师
**发件人：** Gemini AI 产品经理
**主题：** 环检单管理模块迭代开发任务 (V1.2)

## 1. 任务概述

本次任务是对环检单管理模块进行迭代开发。你需要根据下方提供的详细指令，对相关的前端文件进行修改、重构和删除操作。核心任务包括：**移除旧的技师分配功能**、**重构筛选和列表视图**、以及**彻底重构详情编辑弹窗**以支持结构化内容录入。

## 2. 前置准备

在开始编码前，请确保你已完全理解 `环检单管理-迭代需求文档-V1.2.md` 中的所有需求点。

## 3. 详细开发指令

### 任务 1：数据结构与 API 服务层调整

1.  **修改类型定义**:
    *   **文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/types/afterSales/inspection.d.ts`
    *   **操作**:
        *   删除 `AssignTechnicianForm` 接口。
        *   从 `InspectionListItem` 和 `InspectionSearchParams` 接口中删除 `technician` 字段。
        *   在 `InspectionSearchParams` 接口中添加 `serviceAdvisor?: string;` 字段。
        *   在 `InspectionListItem` 接口中添加 `confirmChannel?: string;` 字段。
        *   **新增**：根据需求文档，创建详细的 `InspectionContent` 类型接口，包含停车区域记录、仪表盘检查（里程数、电池电量、续航里程、能耗显示）、功能性检查、警告灯检查、外观检查、电动系统检查、轮胎检查（胎纹深度、轮胎压力、TPMS功能）等结构化内容。

2.  **修改 API 服务**:
    *   **文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/api/modules/afterSales/inspection.ts`
    *   **操作**:
        *   删除 `assignTechnician` 函数及其所有相关导入。
        *   新增一个 `getPrintableInspection` 函数，它接受 `inspectionNo` 作为参数，并返回一个包含可打印 HTML 字符串的 Promise。

### 任务 2：核心视图组件重构

1.  **重构搜索表单**:
    *   **文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/InspectionSearchForm.vue`
    *   **操作**:
        *   移除“技师”筛选框及其相关逻辑。
        *   新增“送修人手机号”输入框和“服务顾问”选择框。
        *   调整布局，确保最终顺序为：第一行（环检单号、车牌号、送修人名称、送修人手机号），第二行（服务顾问、环检状态、创建时间范围、操作按钮）。

2.  **重构数据表格**:
    *   **文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/InspectionTable.vue`
    *   **操作**:
        *   删除"技师"列。
        *   新增"确认渠道"列。
        *   调整所有列的顺序为：序号, 环检单号, 环检状态, 送修人名称, 送修人手机号, 车牌号, 车型, 车型配置, 颜色, 服务顾问, 登记类型, 服务类型, 确认渠道, 客户确认时间, 创建时间, 更新时间, 操作。

3.  **重构详情编辑弹窗 (核心任务)**:
    *   **文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/DetailEditDialog.vue`
    *   **操作**:
        *   **清空**现有组件内容，进行完全重构。
        *   **只读信息区**: 使用 `<el-descriptions>` 创建三个独立的、不可编辑的卡片，分别展示“客户信息”、“车辆信息”和“环检单信息”。
        *   **环检内容清单区 (可编辑)**:
            *   使用 `<el-card>` 按检查大类（如【仪表盘检查】）进行分组。
            *   每个检查子项使用 `<el-form-item>` 布局。
            *   结果选择使用 `<el-radio-group>`（良好/需注意/不良/不适用）。
            *   每个子项旁需提供 `<el-upload>` 组件用于照片上传。
            *   添加一个 `<el-input type="textarea">` 用于记录“客户自述问题”。
        *   **底部操作区**: 包含“保存”和“取消”按钮。

### 任务 3：主视图及其他组件逻辑调整

1.  **清理主视图**:
    *   **文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/InspectionView.vue`
    *   **操作**:
        *   删除对 `AssignDialog` 组件的导入和使用。
        *   删除所有与技师分配相关的 `ref`、`computed` 和函数 (`fetchTechnicianList`, `handleAssignTechnician`, `handleAssignConfirm`)。
        *   新增 `handlePrint` 函数，用于处理打印逻辑。

2.  **重构客户确认弹窗**:
    *   **文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/CustomerConfirmDialog.vue`
    *   **操作**: 确保弹窗包含：环检单基本信息、必填的“客户确认时间”选择器、支持拖拽上传的“签字照片”上传组件。

3.  **重构创建工单弹窗**:
    *   **文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/CreateWorkOrderDialog.vue`
    *   **操作**: 
        *   **信息展示区域**: 分为三个独立区域：关联环检单信息、客户信息、车辆信息。
        *   **工单基本信息**: 添加工单类型选择、工单优先级选择、工单备注输入框。
        *   **推荐维修项目**: 基于环检结果"不良"项生成复选框列表。
        *   **费用统计**: 显示预估费用统计区域。

### 任务 4：文件清理与国际化

1.  **删除废弃文件**:
    *   **文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/AssignDialog.vue`
    *   **操作**: 从文件系统中删除此组件。

2.  **更新国际化文本**:
    *   **文件**: 定位到 `/src/locales/` 目录下的中文语言包。
    *   **操作**: 在 `afterSales.inspection.status` 对象中，更新键值对为：`"pending": "待环检"`, `"in_progress": "环检中"`, `"pending_confirm": "待确认"`, `"confirmed": "已确认"`。
