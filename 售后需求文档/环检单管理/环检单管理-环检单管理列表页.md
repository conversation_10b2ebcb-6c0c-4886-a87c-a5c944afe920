## 环检单管理 - 环检单管理列表页

### 1. 页面作用

本页面是环检单管理的核心入口，为技师经理、技师和服务顾问提供统一的环检单查看、筛选和操作界面。它支持用户快速查找所需环检单，并根据自身角色权限执行相关业务操作，如分配、编辑、提交、确认、打印、创建工单等。

### 2. 页面设计原则

- **响应式布局：** 页面设计考虑PC端和移动端适配，确保在不同设备上均有良好的展示效果。
- **现代化UI设计：** 采用简洁、清晰的现代UI风格，提升用户体验。
- **关键操作突出：** 新增、筛选、导出等关键操作按钮设计醒目，便于用户快速定位和使用。
- **数据展示结构化：** 列表数据按表格形式清晰展示，字段对齐，便于用户快速查找和理解信息。
- **操作路径清晰：** 针对不同角色的操作按钮动态显示，减少认知负担，引导用户执行正确的业务流程。

### 3. 页面布局

页面采用经典的列表+操作区布局，自上而下分为三个主要区域：筛选区域、功能区域和列表区域。

#### 整体页面布局结构
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                筛选区域                                            │
│  ┌─────────────────┬─────────────────┬─────────────────┬─────────────────────────┐  │
│  │   环检单号      │   环检状态      │    车牌号       │     送修人名称          │  │
│  │  [输入框]       │  [下拉选择]     │   [输入框]      │     [输入框]            │  │
│  └─────────────────┴─────────────────┴─────────────────┴─────────────────────────┘  │
│  ┌─────────────────┬─────────────────┬─────────────────────────────────────────────┐  │
│  │     技师        │  送修人手机号   │         创建时间起止                        │  │
│  │  [下拉选择]     │   [输入框]      │    [日期范围选择器]                         │  │
│  └─────────────────┴─────────────────┴─────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐  │
│  │                        [查询] [重置]                                           │  │
│  └─────────────────────────────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                功能区域                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐  │
│  │                              [导出]                                           │  │
│  └─────────────────────────────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                列表区域                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐  │
│  │ 序号│环检单号│环检状态│送修人│手机号│车牌号│车型│颜色│服务顾问│技师│...│操作    │  │
│  ├─────────────────────────────────────────────────────────────────────────────────┤  │
│  │  1  │HJ001   │待环检  │张三  │138xx │京A123│Model│红色│李顾问  │王师傅│...│详情 │  │
│  │  2  │HJ002   │环检中  │李四  │139xx │京B456│Model│蓝色│赵顾问  │陈师傅│...│编辑 │  │
│  │  3  │HJ003   │待确认  │王五  │137xx │京C789│Model│白色│孙顾问  │刘师傅│...│打印 │  │
│  │ ... │ ...    │ ...   │ ... │ ... │ ... │ ... │ ... │ ...   │ ... │...│ ... │  │
│  └─────────────────────────────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐  │
│  │                    共100条 第1页/共10页 [上一页] [下一页]                      │  │
│  └─────────────────────────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

#### 3.1 筛选区域 (顶部)

- **布局：** 水平排列，包含多个查询字段和操作按钮。
- **元素：**
    - **环检单号：** 文本输入框，支持精准查询。
    - **环检状态：** 下拉选择框，提供"待缓检"、"缓检中"、"待确认"、"已确认"等选项。
    - **车牌号：** 文本输入框，支持精准查询。
    - **送修人名称：** 文本输入框，支持模糊查询。
    - **技师：** 下拉选择框，可选择系统中的技师列表。
    - **送修人手机号：** 文本输入框，支持精准查询。
    - **创建时间起止：** 日期范围选择器，包含开始日期和结束日期。
    - **查询按钮：** 点击后根据筛选条件刷新列表数据。
    - **重置按钮：** 点击后清空所有筛选条件。

#### 3.2 功能区域 (中间)

- **布局：** 筛选区域下方，列表区域上方，主要放置全局性操作按钮。
- **元素：**
    - **导出按钮：** "导出"图标或文字按钮，点击后将当前筛选条件下的列表数据导出为Excel文件。

#### 3.3 列表区域 (主体)

- **布局：** 核心数据展示区域，采用表格形式，支持分页。
- **元素：**
    - **表格：**
        - **表头字段：**
            - 序号
            - 环检单号
            - 环检状态
            - 送修人名称
            - 送修人手机号
            - 车牌号
            - 车型配置
            - 颜色
            - 服务顾问
            - 技师
            - 登记类型（显示"预约"或"自然到店"）
            - 服务类型（显示"保养"或"维修"）
            - 客户确认时间
            - 创建时间
            - 更新时间
        - **数据排序：** 默认按"创建时间"倒序排列（越新的数据越靠前）。
        - **操作栏：** 表格每行右侧，根据当前环检单状态和用户角色动态显示操作按钮。
            - **详情按钮：** 所有角色在任何状态下始终展示。
            - **分配环检单按钮 (技师经理)：** 仅当环检单状态为"待环检"或"环检中"时亮起。
            - **编辑按钮 (技师)：** 仅当环检单状态非"待确认"和"已确认"时亮起。
            - **提交确认按钮 (技师)：** 技师完成编辑并保存后亮起。
            - **撤回按钮 (技师)：** 仅当环检单状态为"待确认"时亮起。
            - **打印按钮 (服务顾问)：** 仅当环检单状态为"待确认"时亮起。
            - **客户确认按钮 (服务顾问)：** 仅当环检单状态为"待确认"时亮起。
            - **创建工单按钮 (服务顾问)：** 客户确认环检单后可操作。
    - **分页控件：** 列表下方，显示总条数、当前页码、总页数，并提供前后翻页、跳转页码功能。

### 4. 关键交互 (本页面)

- 用户通过筛选条件和查询按钮，快速定位目标环检单。
- 点击"导出"按钮，将符合筛选条件的列表数据导出至本地。
- 在列表的操作栏中，不同角色根据业务场景和环检单状态，点击相应的操作按钮（如详情、分配、编辑、提交、撤回、打印、客户确认、创建工单），触发对应的弹窗或页面跳转。 
