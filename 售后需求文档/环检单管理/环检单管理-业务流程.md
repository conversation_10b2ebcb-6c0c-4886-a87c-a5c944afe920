# 环检单管理-业务流程分析

## 1. 总体业务流程图

```mermaid
graph TD
    A[客户到店] --> B{登记类型}
    B -->|预约| C[预约到店页面]
    B -->|自然到店| D[到店登记页面]
    C --> E[服务顾问创建环检单]
    D --> E
    E --> F[环检单状态：待环检]
    F --> G[服务顾问执行环检]
    G --> H[服务顾问填写环检内容并保存]
    H --> I[环检单状态：环检中]
    I --> J[服务顾问提交确认]
    J --> K[环检单状态：待确认]
    K --> L[系统推送至Super APP]
    K --> M{客户确认方式}
    M -->|线上确认| N[客户在Super APP确认]
    M -->|线下确认| O[服务顾问打印环检单]
    O --> P[客户纸质签字]
    P --> Q[服务顾问上传签字照片]
    N --> R[环检单状态：已确认]  
    Q --> R
    R --> S[服务顾问手动创建工单]
    S --> T[进入后续维修/保养流程]
    
    K --> U[撤回操作]
    U --> I
```

## 2. 详细业务流程分析

### 2.1 环检单创建阶段

**触发条件：** 客户到店（预约或自然到店）
**参与角色：** 服务顾问
**主要动作：**
1. 客户自然进店或预约到店后，服务顾问接待客户
2. 服务顾问通过"预约到店页面"或"到店登记页面"创建环检单
3. 录入或确认客户信息（送修人名称、送修人手机号等）
4. 录入或确认车辆信息（车牌号、VIN号、车型配置、颜色等）
5. 确定登记类型（预约/自然到店）和服务类型（保养/维修）
6. 系统自动生成环检单号并指定服务顾问

**输出结果：** 
- 环检单创建完成，状态为"待环检"
- 系统生成唯一环检单号

**业务规则：**
- 预约类型：服务类型可为"保养"或"维修"
- 自然到店类型：服务类型仅为"维修"
- 必须填写完整的送修人和车辆信息

### 2.2 环检执行阶段

**触发条件：** 环检单状态为"待环检"
**参与角色：** 服务顾问
**主要动作：**
1. 服务顾问开始执行环检，按照环检内容清单逐项检查
2. 在环检详情/编辑页面填写环检内容，包括：
   - 停车区域记录
   - 仪表盘检查（里程数记录、电池电量检查、续航里程等）
   - 功能性检查（仪表指示器、空调系统、雨刮器等）
   - 警告灯检查（电池系统、电机系统、充电系统等警告灯）
   - 外观检查（前后左右视图、车顶视图、充电口盖等）
   - 电动系统检查（高压电池、电机系统、充电系统）
   - 轮胎检查（轮胎磨损、胎压监测等）
3. 对每个检查项标记结果（良好✓、需要注意△、不良✗、不适用）
4. 可上传相关照片作为附件
5. 服务顾问保存环检内容

**输出结果：**
- 环检内容填写并保存完成
- 环检单状态更新为"环检中"

**业务规则：**
- 必须按照标准环检清单执行检查
- 环检内容仅服务顾问可编辑
- 保存后状态自动变为"环检中"

### 2.3 提交确认阶段

**触发条件：** 环检单状态为"环检中"，环检内容已填写完成
**参与角色：** 服务顾问
**主要动作：**
1. 服务顾问在环检单列表页点击"提交确认"按钮
2. 系统弹出二次确认框
3. 服务顾问确认提交
4. 系统生成带二维码的电子环检单
5. 系统自动推送环检单至客户的Super APP

**输出结果：**
- 环检单状态更新为"待确认"
- 电子环检单推送至客户APP
- 服务顾问无法再编辑环检内容（除非撤回）

**业务规则：**
- 提交后服务顾问不能编辑环检内容
- 系统自动记录提交时间
- 必须完成环检内容填写才能提交

### 2.4 客户确认阶段

#### 2.4.1 线上确认流程
**触发条件：** 环检单状态为"待确认"，客户选择线上确认
**参与角色：** 客户
**主要动作：**
1. 客户收到Super APP推送通知
2. 客户在APP上查看环检单详细信息
3. 客户确认环检结果
4. APP将确认结果同步给DMS系统

**输出结果：**
- 环检单状态更新为"已确认"
- 系统记录客户确认时间和确认方式（线上确认）

#### 2.4.2 线下确认流程
**触发条件：** 客户选择线下签字确认或无法线上确认
**参与角色：** 服务顾问、客户
**主要动作：**
1. 服务顾问在系统点击"打印"按钮
2. 系统打印带有二维码的纸质环检单
3. 服务顾问向客户展示纸质环检单
4. 客户在纸质单据上签字确认
5. 服务顾问点击"客户确认"按钮，进入上传页面
6. 服务顾问填写客户确认时间并上传签字照片
7. 服务顾问点击"确认"提交

**输出结果：**
- 环检单状态更新为"已确认"
- 系统保存客户确认照片和确认时间
- 记录确认方式为"线下确认"

**业务规则：**
- "打印"和"客户确认"按钮仅在"待确认"状态下可用
- 必须上传清晰的客户签字照片
- 必须填写准确的客户确认时间

### 2.5 工单创建阶段

**触发条件：** 环检单状态为"已确认"
**参与角色：** 服务顾问
**主要动作：**
1. 服务顾问确认客户已完成环检单确认
2. 在环检单列表页或详情页点击"创建工单"按钮
3. 系统跳转至工单创建页面，自动带入客户和车辆信息
4. 服务顾问补充工单相关信息并创建工单

**输出结果：**
- 工单创建完成，状态为"待处理"
- 工单与环检单建立关联关系
- 进入后续维修/保养流程

**业务规则：**
- 只有"已确认"状态的环检单才能创建工单
- 一个环检单只能创建一个工单
- 创建工单是手动操作，由服务顾问执行

## 3. 异常流程处理

### 3.1 环检单撤回流程
**场景：** 服务顾问提交后发现环检内容需要修改
**条件：** 环检单状态为"待确认"
**流程：**
1. 服务顾问在列表页点击"撤回"按钮
2. 系统弹出二次确认框："确认要撤回该环检单吗？"
3. 服务顾问确认撤回操作
4. 环检单状态回滚为"环检中"
5. 服务顾问可重新编辑环检内容

**业务规则：**
- 只有"待确认"状态的环检单可以撤回
- 撤回后客户APP中的环检单自动失效
- 撤回操作需要二次确认

### 3.2 客户确认异常处理
**场景1：** 客户对环检结果有异议
**处理方式：**
1. 客户可拒绝线上确认，环检单状态保持"待确认"
2. 服务顾问与客户沟通，必要时执行"撤回"操作重新检查

**场景2：** 线下确认照片质量问题
**处理方式：**
1. 如上传的签字照片不清晰，服务顾问可重新上传
2. 系统支持多次上传，以最后一次为准

## 4. 业务规则总结

### 4.1 状态流转规则
- **待环检** → **环检中**（服务顾问开始编辑并保存后）
- **环检中** → **待确认**（服务顾问提交确认后）
- **待确认** → **已确认**（客户确认后）
- **待确认** → **环检中**（服务顾问撤回后）

### 4.2 按钮显示逻辑
- **详情按钮：** 始终展示
- **编辑按钮：** 仅当环检单状态非"待确认"和"已确认"时可用
- **提交确认按钮：** 服务顾问完成编辑并保存后可用
- **撤回按钮：** 仅当环检单状态为"待确认"时可用
- **打印按钮：** 仅当环检单状态为"待确认"时可用
- **客户确认按钮：** 仅当环检单状态为"待确认"时可用
- **创建工单按钮：** 仅当环检单状态为"已确认"时可用

### 4.3 权限控制规则
- **服务顾问：** 拥有环检单的全量操作权限（创建、编辑、提交、撤回、打印、处理确认、创建工单等）
- **数据权限：** 服务顾问可查看和操作自己负责的或本店的环检单（根据具体数据权限配置）

### 4.4 数据完整性规则
- 环检单必须包含完整的送修人和车辆信息
- 提交客户确认前必须按标准清单填写环检内容
- 客户确认必须有明确的确认时间和确认方式
- 线下确认必须上传客户签字照片
- 所有状态变更和关键操作必须有操作日志记录 