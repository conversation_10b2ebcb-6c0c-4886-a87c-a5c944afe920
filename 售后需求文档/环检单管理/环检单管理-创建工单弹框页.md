# 环检单管理-创建工单弹框页面描述文档

## 页面概述

**页面名称：** 创建工单弹框页面  
**页面标识：** InspectionCreateWorkOrderModal  
**页面类型：** 弹窗表单页面  
**适配终端：** PC端主要，移动端兼容  
**弹窗尺寸：** 1000px × 700px（可调整）

## 页面功能定位

创建工单弹框页面是环检单管理流程的最后环节，用于服务顾问在客户确认环检单后，根据环检结果创建后续的维修或保养工单。页面采用弹窗形式，自动继承环检单中的客户和车辆信息，支持基于环检结果智能推荐维修项目。

### 页面目标
- **承接环检**：基于已确认的环检单创建工单，确保业务流程连贯
- **智能推荐**：根据环检结果自动推荐需要处理的维修保养项目
- **信息继承**：自动继承环检单的客户、车辆等基础信息，减少重复录入
- **快速创建**：简化工单创建流程，提高服务顾问工作效率

## 页面布局设计

### 整体布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                     弹窗标题栏                              │
│  [基于环检单创建工单]                              [×]      │
├─────────────────────────────────────────────────────────────┤
│                     页面内容区                              │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                 环检单信息区（只读）                    │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                  客户信息区                             │  │
│  │                （继承显示）                             │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                  车辆信息区                             │  │
│  │                （继承显示）                             │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                    工单基本信息                         │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                  环检问题项目推荐                       │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                   工时项目列表                          │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                   零件项目列表                          │  │
│  └─────────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                   工单费用统计                          │  │
│  └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                     底部操作区                              │
│              [创建并推送客户] [保存草稿] [取消]             │
└─────────────────────────────────────────────────────────────┘
```

## 详细功能描述

### 1. 弹窗标题栏

#### 1.1 标题显示
- **页面标题**：基于环检单创建工单
- **环检单号**：HJ20241201001
- **标题样式**：16px字体，粗体显示，显示关联的环检单号

#### 1.2 关闭控制
- **关闭按钮**：右上角X按钮
- **关闭确认**：如有未保存数据，弹出确认对话框
- **快捷键**：支持ESC键关闭

### 2. 环检单信息区（只读）

#### 2.1 环检单概要信息
```
┌─ 关联环检单信息 ──────────────────────────────┐
│  环检单号：HJ20241201001    环检状态：已确认  │
│  创建时间：2024-12-01 09:30  确认时间：2024-12-01 11:45 │
│  执行技师：张师傅            服务顾问：李顾问  │
│  登记类型：预约             服务类型：保养    │
└──────────────────────────────────────────────┘
```

#### 2.2 显示规则
- **信息来源**：直接从环检单继承，不可编辑
- **状态校验**：仅显示"已确认"状态的环检单信息
- **样式设计**：浅灰色背景，表示只读信息

### 3. 客户信息区（继承显示）

#### 3.1 客户信息布局
```
┌─ 客户信息（来自环检单）──────────────────────┐
│  预约客户：张三        预约手机：139****5678  │
│  送修人：  张三        送修手机：139****5678  │
│  备注信息：客户反映刹车时有异响              │
└──────────────────────────────────────────────┘
```

#### 3.2 信息继承规则
- **预约信息**：自动继承环检单中的预约客户信息（如适用）
- **送修信息**：自动继承环检单中的送修人信息
- **客户问题**：继承环检单中的客户自述问题描述
- **编辑限制**：客户基本信息不可编辑，保持与环检单一致

### 4. 车辆信息区（继承显示）

#### 4.1 车辆信息布局
```
┌─ 车辆信息（来自环检单）──────────────────────┐
│  车牌号：   京A12345     VIN号：   LHGAB1234567890 │
│  车型配置：比亚迪唐DM-i 112KM旗舰型  颜色：珍珠白  │
│  当前里程：25,680公里    车龄：3年              │
└──────────────────────────────────────────────┘
```

#### 4.2 信息继承规则
- **车辆标识**：自动继承车牌号、VIN号等车辆识别信息
- **车辆配置**：继承环检单中的车型配置和颜色信息
- **里程信息**：继承环检时记录的当前里程数
- **车龄信息**：继承环检单中的车龄信息
- **编辑限制**：车辆基本信息不可编辑，保持数据一致性

### 5. 工单基本信息

#### 5.1 工单信息布局
```
┌─ 工单基本信息 ────────────────────────────────┐
│  工单类型：  [● 维修  ○ 保养  ○ 保险]         │
│  工单优先级：[● 普通  ○ 紧急]                  │
│  工单备注：  [基于环检单HJ20241201001创建]    │
│              [________________________]    │
└──────────────────────────────────────────────┘
```

#### 5.2 字段详细说明
| 字段名称 | 字段类型 | 是否必填 | 默认值 | 说明 |
|---------|---------|----------|---------|------|
| 工单类型 | 单选按钮 | 是 | 继承环检单服务类型 | 维修/保养/保险 |
| 工单优先级 | 单选按钮 | 是 | 普通 | 普通/紧急 |
| 工单备注 | 多行文本 | 否 | 自动生成关联信息 | 工单相关备注说明 |

#### 5.3 智能默认值
- **工单类型**：自动继承环检单的服务类型（保养/维修）
- **优先级判断**：根据环检结果中"不良"项目数量自动判断优先级
- **备注预填**：自动填入环检单关联信息

### 6. 项目选择区

#### 6.1 项目选择布局
```
┌─ 项目选择 ────────────────────────────────────┐
│  搜索添加：[搜索项目名称或编码...] [🔍]         │
└──────────────────────────────────────────────┘
```

#### 6.2 搜索功能设计
- **模糊搜索**：支持项目名称或编码的模糊搜索
- **联想提示**：输入时实时显示匹配的项目列表
- **分类筛选**：可按保养、维修、索赔类型筛选
- **快速选择**：点击搜索结果直接添加到工单

#### 6.3 智能推荐
- **建议项目**：基于车型和里程数推荐常见项目
- **最近使用**：显示当前用户最近使用的项目
- **套餐项目**：保养工单显示对应的套餐项目

#### 6.4 项目添加规则
- **保养工单**：只能添加保养项目，套餐项目自动添加
- **维修工单**：可添加维修项目和索赔项目
- **委外项目**：支持标记需要委外的项目
- **索赔限制**：增项时不能添加索赔项目

### 7. 工时项目列表

#### 7.1 工时列表布局
```
┌─ 工时项目 ────────────────────────────────────┐
│  [添加工时] [全部清除]                         │
│  ┌────┬────────┬────┬────┬────┬────┬────┬──┐ │
│  │类型│项目名称│索赔│增项│工时│单价│小计│  │ │
│  ├────┼────────┼────┼────┼────┼────┼────┼──┤ │
│  │保养│机油更换│否  │否  │1.0 │80  │80  │X │ │
│  │维修│刹车维修│是  │否  │2.5 │120 │300 │X │ │
│  └────┴────────┴────┴────┴────┴────┴────┴──┘ │
│  统计：工时总数量：3.5小时  工时总价：380元     │
└──────────────────────────────────────────────┘
```

#### 7.2 表头字段说明
| 字段名称 | 字段宽度 | 字段类型 | 编辑性 | 说明 |
|---------|---------|----------|--------|------|
| 类型 | 80px | 徽章显示 | 只读 | 保养/维修/索赔 |
| 工时code | 200px | 文本显示 | 只读 | 工时code |
| 工时名称 | 200px    | 文本显示 | 只读   | 工时完整名称       |
| 是否增项 | 60px | 是/否 | 只读 | 增项模式下自动标记 |
| 标准工时 | 80px | 数字输入 | 可编辑 | 小时，支持小数 |
| 工时单价 | 80px | 数字输入 | 只读 | 元/小时 |
| 工时小计 | 80px | 计算显示 | 只读 | 工时×单价 |
| 操作 | 50px | 删除按钮 | 可操作 | 删除当前工时项 |

#### 7.3 功能按钮
- **添加工时**：手动添加工时项目
- **全部清除**：清空所有工时项目（需确认）
- **删除**：删除单个工时项目

#### 7.4 数据校验
- **工时范围**：0.1-99.9小时
- **必填校验**：工时和单价必须填写
- **重复校验**：同一项目不能重复添加

### 8. 零件项目列表

#### 8.1 零件列表布局
```
┌─ 零件项目 ────────────────────────────────────┐
│  [添加零件] [全部清除]                         │
│  ┌──────┬────┬────┬────┬────┬────┬────┬──┐   │
│  │零件名│索赔│增项│库存│数量│单价│小计│  │   │
│  ├──────┼────┼────┼────┼────┼────┼────┼──┤   │
│  │机油滤│否  │否  │50  │1   │25  │25  │X │   │
│  │刹车片│是  │否  │8   │2   │150 │300 │X │   │
│  └──────┴────┴────┴────┴────┴────┴────┴──┘   │
│  统计：零件总数量：3个  零件总价：325元         │
└──────────────────────────────────────────────┘
```

#### 8.2 表头字段说明
| 字段名称 | 字段宽度 | 字段类型 | 编辑性 | 说明 |
|---------|---------|----------|--------|------|
| 零件名称 | 150px | 文本显示 | 只读 | 零件完整名称 |
| 是否索赔 | 60px | 是/否 | 只读 | 根据零件类型自动判断 |
| 是否增项 | 60px | 是/否 | 只读 | 增项模式下自动标记 |
| 可用库存 | 80px | 数字显示 | 只读 | 当前可用库存数量 |
| 零件数量 | 80px | 数字输入 | 可编辑 | 使用数量 |
| 零件单价 | 80px | 数字输入 | 只读 | 单个零件价格 |
| 零件小计 | 80px | 计算显示 | 只读 | 数量×单价 |
| 操作 | 50px | 删除按钮 | 可操作 | 删除当前零件项 |

#### 8.3 库存管理
- **实时库存**：显示当前实时可用库存
- **库存预警**：库存不足时红色警告
- **数量限制**：使用数量不能超过可用库存
- **库存预留**：保存时自动预留对应数量

#### 8.4 数据校验
- **数量范围**：1-999个
- **库存校验**：使用数量≤可用库存
- **必填校验**：数量和单价必须填写

### 9. 工单费用统计

#### 9.1 费用统计布局
```
┌─ 费用预算统计 ────────────────────────────────┐
│  工时费用：320元   │
│  零件费用：325元    │
│  其他费用：0元                                 │
│  ─────────────────                            │
│  工单预计总金额：645元                         │
│        │
└──────────────────────────────────────────────┘
```

#### 9.2 统计规则
- **工时总价**：所有工时项目小计之和
- **零件总价**：所有零件项目小计之和
- **工单总金额**：工时总价 + 零件总价
- **实时更新**：任何金额变化都实时更新统计

#### 9.3 显示格式
- **金额格式**：¥999.99 格式显示
- **千分位**：超过1000元显示千分位分隔符
- **突出显示**：总金额加粗显示

### 10. 底部操作区

#### 10.1 操作按钮设计
```
[创建并推送客户]  [保存]  [取消]
```

#### 10.2 按钮详细说明
| 按钮名称 | 按钮类型 | 显示条件 | 功能描述 |
|---------|---------|----------|----------|
| 创建并推送客户 | 主要按钮 | 信息完整且通过校验 | 创建工单并推送给客户确认 |
| 保存 | 次要按钮 | 始终显示 | 保存工单为草稿状态 |
| 取消 | 普通按钮 | 始终显示 | 取消创建并关闭弹窗 |

#### 10.3 按钮行为

**创建并推送客户按钮：**
1. **完整性校验**：验证工单基本信息和项目完整性
2. **库存校验**：确认零件库存充足
3. **业务规则校验**：检查工单创建的业务规则
4. **创建工单**：创建工单，状态为"待确认"
5. **关联环检单**：建立工单与环检单的关联关系
6. **推送客户**：发送工单确认通知给客户
7. **操作反馈**：显示创建成功消息并关闭弹窗

**保存草稿按钮：**
1. **基础校验**：验证必填的基本信息
2. **保存草稿**：保存工单数据，状态为"草稿"
3. **保持关联**：保持与环检单的关联关系
4. **继续编辑**：保持弹窗打开状态
5. **操作反馈**：显示保存成功消息

**取消按钮：**
1. **变更检查**：检查是否有未保存的变更
2. **确认提示**：如有变更，弹出确认对话框
3. **关闭弹窗**：确认后关闭弹窗，不保存任何变更

---

**文档版本**：v1.0  
**创建时间**：2024年12月  
**维护人员**：DMS产品团队  
**关联文档**：环检单管理业务流程文档、环检单管理用户旅程文档 
