### **需求迭代开发指令 (V1.0)**

`[需求迭代任务卡]`

## **1. 任务元信息**

*   **模块名称**: DMS售后管理系统 - 环检单管理
*   **迭代版本**: V1.2
*   **需求提出人**: MaJie
*   **执行人**: AI 开发工程师
*   **日期**: 2025-08-01

## **2. 输入信息 (由需求方填写)**

#### **2.1 迭代目标**
完善环检单管理功能，实现标准化的环检流程管理，包括筛选条件优化、环检内容结构化、客户确认流程完善、工单创建集成等核心功能，提升服务顾问工作效率和客户服务体验。同时，移除分配技师功能及相关业务逻辑。

#### **2.2 相关文档与代码路径**
*(请提供所有相关文件的准确路径，这将极大提升AI的分析效率)*

*   **需求文档目录**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/环检单管理`
*   **API 接口文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/api/modules/afterSales/inspection.ts`
*   **前端视图文件**:
    主页面：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/InspectionView.vue`
    筛选组件：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/InspectionSearchForm.vue`
    列表组件：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/InspectionTable.vue`
    详情编辑弹窗：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/DetailEditDialog.vue`
    客户确认弹窗：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/CustomerConfirmDialog.vue`
    创建工单弹窗：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/views/afterSales/inspection/components/CreateWorkOrderDialog.vue`
*   **类型定义文件**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/types/afterSales/inspection.d.ts`
*   **国际化目录**: `/Users/<USER>/Desktop/DMS-Work/dms_frontend/src/locales/modules/afterSales`

#### **2.3 核心数据结构 (可选，强烈推荐)**
实体信息及实体关系：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/环检单管理/环检单管理-实体信息及实体关系.md`
业务流程文档：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/环检单管理/环检单管理-业务流程.md`
用户旅程文档：`/Users/<USER>/Desktop/DMS-Work/dms_frontend/售后需求文档/环检单管理/环检单管理-用户旅程文档及功能清单.md`

#### **2.4 需求变更清单**
*(请按页面或功能模块，结构化地列出所有具体的变更点)*

**筛选条件模块调整：**
1. 新增"送修人手机号"精准查询字段
2. 调整筛选条件布局为两行显示：第一行（环检单号、车牌号、送修人名称、服务顾问），第二行（送修人手机号、环检状态、创建时间范围、操作按钮）
3. 完善环检状态选项：待环检、环检中、待确认、已确认

**列表字段模块调整：**
4. 新增"登记类型"字段显示（预约/自然到店）
5. 新增"服务类型"字段显示（保养/维修）
6. 新增"客户确认时间"字段显示
7. 新增"颜色"字段显示
8. 调整列表字段顺序，按需求文档规范重新排列

**操作按钮动态显示逻辑调整：**
9. 详情按钮：始终展示
10. 编辑按钮：仅当状态非"待确认"和"已确认"时显示
11. 提交确认按钮：服务顾问完成编辑并保存后显示
12. 撤回按钮：仅当状态为"待确认"时显示
13. 打印按钮：仅当状态为"待确认"时显示
14. 客户确认按钮：仅当状态为"待确认"时显示
15. 创建工单按钮：仅当状态为"已确认"时显示
16. 去除分配技师按钮及相关功能

**详情编辑弹窗调整：**
17. 环检内容清单结构化：停车区域记录、仪表盘检查、功能性检查、警告灯检查、外观检查、电动系统检查、轮胎检查
18. 统一检查结果选项：良好(✓)、需要注意(△)、不良(✗)、不适用(○)
19. 支持每个检查项上传相关照片附件
20. 新增客户自述问题记录区域
21. 编辑状态下仅环检内容可编辑，客户信息、车辆信息等基础信息不可编辑

**客户确认流程调整：**
22. 完善线上确认：系统自动推送至Super APP，客户在线确认
23. 完善线下确认：服务顾问打印纸质单据，客户签字后上传照片
24. 新增二次确认机制，确保操作安全
25. 客户确认弹窗功能：展示环检单基本信息、客户确认时间选择器、签字照片上传功能（支持拖拽上传）、照片质量校验机制

**工单创建功能调整：**
26. 自动继承环检单的客户和车辆信息
27. 基于环检结果智能推荐维修项目
28. 工单类型自动继承环检单服务类型
29. 支持手动添加额外维修项目
30. 仅"已确认"状态的环检单可创建工单
31. 创建工单后建立关联关系，支持工单创建后跳转到工单管理页面

**业务流程控制调整：**
32. 严格按照：待环检 → 环检中 → 待确认 → 已确认 的状态流转
33. 支持"待确认"状态撤回到"环检中"，撤回操作需要二次确认
34. 登记类型限制：预约类型服务可为保养/维修，自然到店仅为维修
35. 必填字段校验：创建时必须填写完整送修人和车辆信息
36. 线下确认校验：必须上传客户签字照片并填写确认时间

**系统集成调整：**
37. 环检单提交后自动生成带二维码的电子环检单
38. 自动推送至客户Super APP，支持APP端确认结果同步回DMS系统
39. 支持环检单打印功能，打印内容包含二维码和完整环检信息
40. 支持按筛选条件导出环检单数据，导出格式为Excel

**用户体验优化：**
41. 所有关键操作增加二次确认
42. 操作成功后显示明确的反馈信息
43. 列表默认按创建时间倒序排列，状态用不同颜色标识
44. 支持列表字段排序功能

**功能移除：**
45. 移除分配技师功能及相关弹窗组件
46. 移除分配技师按钮及相关业务逻辑
47. 移除技师相关的数据字段和接口调用


---

## **3. 输出信息 (由 AI 执行并生成)**

#### **3.1 变更分析与执行**
*   **[AI执行]** 你将首先阅读并分析你在 `2.2` 中提供的所有相关文档，并结合 `2.3` 和 `2.4` 的信息，在内存中构建完整的迭代任务图谱。
*   **[AI执行]** 你将严格按照 **UI/线框图 -> 接口/类型定义 -> 需求/规格文档 -> 国际化文件** 的顺序，分步对相关文件进行 `edit_file` 操作，并等待我的确认。

#### **3.2 生成迭代交付物**
*   **[AI执行]** 在所有文件修改得到我的确认后，你将为我生成一份完整的迭代总结文档。

*   **文件名**: `环检单管理-迭代纪要-V1.2.md`
*   **存放路径**: `docs/售后管理/环检单管理/`
*   **文档内容**:
    *   **1. 需求背景与目标**: 对本次迭代的简要总结。
    *   **2. 需求变更详情 (Changelog)**: 清晰列出所有具体的变更项。
    *   **3. 开发实施计划**:
        *   包含后端、前端、测试等角色的详细任务分解表。
        *   前端任务中应包含对 `视图`、`类型`、`API服务` 和 `国际化` 文件的修改。
    *   **4. 面向AI工程师的详细开发提示词**: (可选，若需要) 生成一份事无巨细、可直接用于后续开发的提示词。

#### **3.3 文件保存**
*   **[AI执行]** 完成 `3.2` 中迭代纪要的生成后，你将调用 `edit_file` 工具，将这份新生成的文档保存在我指定的 `存放路径` 下。
