## 环检单管理 - 用户旅程文档及功能清单

### 1. 用户旅程 (User Journey)

#### 1.1 客户 (Customer) 旅程
1. **到店登记/预约核销：** 客户到店，向服务顾问提供车辆信息或预约信息。
2. **等待环检完成：** 客户等待服务顾问完成车辆环检并提交确认。
3. **查看环检单：** 通过Super APP接收并查看电子环检单详细内容。
4. **确认环检单：** 在Super APP上线上确认环检结果，或在线下纸质单上签字确认。
5. **进入后续服务：** 环检单确认后，等待或进入后续维修/保养流程。

#### 1.2 服务顾问 (Service Advisor) 旅程
1. **创建环检单：** 根据客户到店情况（预约/自然到店）在DMS系统中通过"预约到店页面"或"到店登记页面"创建环检单。
2. **执行环检：** 按照环检内容清单对车辆进行详细检查，包括：
   - 停车区域记录
   - 仪表盘检查（里程数记录、电池电量检查、续航里程等）
   - 功能性检查（仪表指示器、空调系统、雨刮器等）
   - 警告灯检查（电池系统、电机系统、充电系统等警告灯）
   - 外观检查（前后左右视图、车顶视图、充电口盖等）
   - 电动系统检查（高压电池、电机系统、充电系统）
   - 轮胎检查（轮胎磨损、胎压监测等）
3. **填写环检内容：** 在DMS系统环检详情/编辑页面中填写各类检查项及结果（良好✓、需要注意△、不良✗、不适用），可上传相关照片作为附件。
4. **提交确认：** 完成环检内容填写并保存后，点击"提交确认"按钮，使环检单状态变为"待确认"，系统自动推送至客户APP。
5. **引导客户确认：** 通知客户查看Super APP进行线上确认。
6. **处理线下确认：** 如客户选择线下确认，则点击"打印"按钮打印纸质环检单引导客户签字，然后点击"客户确认"按钮上传签字照片到系统。
7. **撤回操作：** 如需修改环检内容，可在"待确认"状态下点击"撤回"按钮，使状态变回"环检中"进行重新编辑。
8. **创建工单：** 客户确认环检单后，在DMS系统中点击"创建工单"按钮手动创建工单，进入后续流程。
9. **管理环检单：** 在整个流程中，可通过列表页查看环检单状态，使用筛选功能查找特定环检单，查看详情信息。
10. **导出数据：** 根据需要，按筛选条件导出环检单数据用于统计或分析。

### 2. 核心功能点 (Core Functionality)

#### 2.1 环检单全流程管理
- **环检单创建：** 服务顾问根据客户到店情况（预约/自然到店）在DMS系统中通过指定入口页面创建环检单，录入送修人信息和车辆信息。
- **环检内容填写：** 服务顾问在环检详情/编辑页面填写结构化的环检内容，包括停车区域记录、仪表盘检查、功能性检查、警告灯检查、外观检查、电动系统检查、轮胎检查等各类检查项的详细信息和检查结果（良好/需要注意/不良/不适用）。
- **提交确认：** 服务顾问完成环检填写并保存后，可点击"提交确认"按钮（需二次确认），将环检单状态置为"待确认"，系统自动生成带二维码的电子环检单并推送至Super APP。
- **撤回操作：** 服务顾问可撤回处于"待确认"状态的环检单（需二次确认），使其状态变回"环检中"，以便重新编辑。
- **电子环检单生成：** 系统自动生成带二维码的电子环检单，用于Super APP推送和线下打印。

#### 2.2 客户确认管理
- **Super APP线上确认：** 系统将电子环检单推送至Super APP，供客户在线查看详情并进行确认。
- **线下签字确认：** 如线上确认失败或客户选择线下，服务顾问可点击"打印"按钮打印纸质环检单引导客户进行线下签字。
- **签字照片上传：** 服务顾问需在系统中点击"客户确认"按钮，在弹窗中填写客户确认时间并上传客户签字的纸质单据照片。

#### 2.3 工单创建
- **手动创建工单：** 客户确认环检单后，服务顾问可在DMS系统中点击"创建工单"按钮，系统将自动携带相关信息跳转至工单创建页面。

#### 2.4 数据查询与导出
- **列表筛选：** 提供多维度筛选条件供服务顾问查询环检单列表：
  - 环检单号（精准查询）
  - 环检状态（下拉选择：待环检、环检中、待确认、已确认）
  - 车牌号（精准查询）
  - 送修人名称（模糊查询）
  - 服务顾问（下拉选择）
  - 送修人手机号（精准查询）
  - 创建时间起止（时间范围筛选器）
- **详情查看：** 服务顾问可查看环检单的详细信息，包括客户信息、车辆信息、环检单信息、环检内容清单等。
- **数据导出：** 服务顾问可根据筛选条件，点击"导出"按钮将环检单列表数据导出为Excel格式。

#### 2.5 列表管理功能
- **默认排序：** 环检单列表默认按创建时间倒序排列。
- **列表字段显示：** 序号、环检单号、环检状态、送修人名称、送修人手机号、车牌号、车型配置、颜色、服务顾问、登记类型（预约/自然到店）、服务类型（保养/维修）、客户确认时间、创建时间、更新时间。
- **操作按钮：** 根据环检单状态动态显示操作按钮：
  - **详情按钮：** 始终展示
  - **编辑按钮：** 仅当环检单状态非"待确认"和"已确认"时亮起
  - **提交确认按钮：** 服务顾问完成编辑并保存后亮起
  - **撤回按钮：** 仅当环检单状态为"待确认"时亮起
  - **打印按钮：** 仅当环检单状态为"待确认"时亮起
  - **客户确认按钮：** 仅当环检单状态为"待确认"时亮起
  - **创建工单按钮：** 仅当环检单状态为"已确认"时亮起

#### 2.6 页面交互功能
- **环检单详情/编辑弹窗：** 展示完整的环检单信息，在编辑状态下仅环检内容可编辑，其余信息不可编辑。
- **客户确认弹窗：** 用于线下确认时上传客户签字照片，包含环检单基本信息展示、客户确认时间选择、照片上传功能。
- **二次确认机制：** 提交确认和撤回操作都需要弹出二次确认框确保操作安全。

#### 2.7 权限控制
- **服务顾问权限：** 拥有其负责的（或本店的）环检单的创建、编辑、提交、撤回、打印、处理客户确认、创建工单、查看列表详情、导出数据等权限。
- **数据权限：** 根据具体数据权限配置，服务顾问可查看和操作相应范围内的环检单。

#### 2.8 业务规则控制
- **服务类型限制：** 登记类型为"预约"时服务类型可为"保养"或"维修"；登记类型为"自然到店"时服务类型仅为"维修"。
- **状态流转控制：** 严格按照 待环检 → 环检中 → 待确认 → 已确认 的状态流转，支持待确认状态撤回到环检中。
- **必填字段校验：** 环检单创建时必须填写完整的送修人和车辆信息；线下确认时必须上传客户签字照片并填写确认时间。 