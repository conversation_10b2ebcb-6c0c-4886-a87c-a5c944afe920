# 环检单管理功能调整清单

## 文档说明
**创建时间：** 2024年12月  
**版本：** v1.0  
**目的：** 基于最新需求文档，梳理环检单管理功能的调整点  
**范围：** 严格按照需求文档范围，不超出业务边界

## 1. 主要功能调整点

### 1.1 筛选条件调整
**当前状态：** 基础筛选功能  
**需求调整：**
- 新增 **送修人手机号** 精准查询字段
- 筛选条件布局调整为两行：
  - 第一行：环检单号、车牌号、送修人名称、服务顾问
  - 第二行：送修人手机号、创建时间范围、环检状态、操作按钮
- 环检状态选项完善：待环检、环检中、待确认、已确认

### 1.2 列表字段调整
**当前状态：** 基础字段显示  
**需求调整：**
- 新增 **登记类型** 字段（预约/自然到店）
- 新增 **服务类型** 字段（保养/维修）
- 新增 **客户确认时间** 字段
- 新增 **颜色** 字段
- 调整列表字段顺序，按需求文档规范排列

### 1.3 操作按钮动态显示逻辑
**当前状态：** 简单状态判断  
**需求调整：**
- **详情按钮**：始终展示
- **编辑按钮**：仅当状态非"待确认"和"已确认"时显示
- **提交确认按钮**：服务顾问完成编辑并保存后显示
- **撤回按钮**：仅当状态为"待确认"时显示
- **打印按钮**：仅当状态为"待确认"时显示
- **客户确认按钮**：仅当状态为"待确认"时显示
- **创建工单按钮**：仅当状态为"已确认"时显示

## 2. 详情编辑弹窗调整

### 2.1 环检内容清单结构化
**当前状态：** 简化的检查项目  
**需求调整：**
- **停车区域记录**：具体停车位置信息
- **仪表盘检查**：里程数记录、电池电量检查、续航里程等
- **功能性检查**：仪表指示器、空调系统、雨刮器等
- **警告灯检查**：电池系统、电机系统、充电系统等警告灯
- **外观检查**：前后左右视图、车顶视图、充电口盖等
- **电动系统检查**：高压电池、电机系统、充电系统
- **轮胎检查**：轮胎磨损、胎压监测等

### 2.2 检查结果标准化
**当前状态：** 基础选项  
**需求调整：**
- 统一检查结果选项：良好(✓)、需要注意(△)、不良(✗)、不适用(○)
- 支持每个检查项上传相关照片附件
- 新增客户自述问题记录区域

### 2.3 权限控制
**需求调整：**
- 编辑状态下仅环检内容可编辑
- 客户信息、车辆信息等基础信息不可编辑
- 根据用户角色控制编辑权限

## 3. 客户确认流程调整

### 3.1 确认方式完善
**当前状态：** 基础确认功能  
**需求调整：**
- **线上确认**：系统自动推送至Super APP，客户在线确认
- **线下确认**：服务顾问打印纸质单据，客户签字后上传照片
- 新增二次确认机制，确保操作安全

### 3.2 客户确认弹窗功能
**需求调整：**
- 展示环检单基本信息
- 客户确认时间选择器
- 签字照片上传功能（支持拖拽上传）
- 照片质量校验机制

## 4. 工单创建功能调整

### 4.1 创建工单弹窗
**当前状态：** 基础工单创建  
**需求调整：**
- 自动继承环检单的客户和车辆信息
- 基于环检结果智能推荐维修项目
- 工单类型自动继承环检单服务类型
- 支持手动添加额外维修项目

### 4.2 业务规则控制
**需求调整：**
- 仅"已确认"状态的环检单可创建工单
- 创建工单后建立关联关系
- 支持工单创建后跳转到工单管理页面

## 5. 业务流程控制调整

### 5.1 状态流转控制
**需求调整：**
- 严格按照：待环检 → 环检中 → 待确认 → 已确认 的流转
- 支持"待确认"状态撤回到"环检中"
- 撤回操作需要二次确认

### 5.2 业务规则验证
**需求调整：**
- **登记类型限制**：预约类型服务可为保养/维修，自然到店仅为维修
- **必填字段校验**：创建时必须填写完整送修人和车辆信息
- **线下确认校验**：必须上传客户签字照片并填写确认时间

## 6. 数据权限和角色控制

### 6.1 服务顾问权限
**需求调整：**
- 拥有负责环检单的创建、编辑、提交、撤回权限
- 可处理客户确认、打印、创建工单操作
- 可查看列表详情、导出数据

### 6.2 数据范围控制
**需求调整：**
- 根据数据权限配置显示相应范围内的环检单
- 支持按服务顾问、门店等维度进行数据隔离

## 7. 系统集成调整

### 7.1 Super APP集成
**需求调整：**
- 环检单提交后自动生成带二维码的电子环检单
- 自动推送至客户Super APP
- 支持APP端确认结果同步回DMS系统

### 7.2 打印功能
**需求调整：**
- 支持环检单打印功能
- 打印内容包含二维码和完整环检信息
- 支持批量打印操作

## 8. 数据导出功能

### 8.1 导出条件
**需求调整：**
- 支持按筛选条件导出环检单数据
- 导出格式为Excel
- 包含所有列表显示字段

### 8.2 导出内容
**需求调整：**
- 环检单基本信息
- 客户和车辆信息
- 环检内容和结果
- 确认信息和时间

## 9. 用户体验优化

### 9.1 交互优化
**需求调整：**
- 所有关键操作增加二次确认
- 操作成功后显示明确的反馈信息
- 支持快捷键操作（如ESC关闭弹窗）

### 9.2 数据展示优化
**需求调整：**
- 列表默认按创建时间倒序排列
- 状态用不同颜色标识
- 支持列表字段排序功能

## 10. 实施优先级

### 高优先级（P0）
1. 筛选条件字段调整
2. 列表字段完善
3. 操作按钮动态显示逻辑
4. 环检内容清单结构化
5. 状态流转控制

### 中优先级（P1）
1. 客户确认流程完善
2. 工单创建功能
3. 业务规则验证
4. 权限控制

### 低优先级（P2）
1. Super APP集成
2. 打印功能
3. 数据导出优化
4. 用户体验优化

---

**注意事项：**
1. 所有调整严格按照需求文档执行，不超出业务范围
2. 涉及数据库结构变更的功能需要提前规划
3. 与Super APP的集成需要协调移动端开发
4. 打印功能需要考虑不同浏览器的兼容性
5. 权限控制需要与用户管理系统对接