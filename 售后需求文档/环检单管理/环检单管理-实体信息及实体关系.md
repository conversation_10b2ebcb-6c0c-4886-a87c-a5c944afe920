# 环检单管理 - 实体信息及实体关系

本文档定义了"环检单管理"功能所涉及的核心实体、它们的属性以及实体之间的关系。

## 1. 核心实体定义

### 1.1 环检单 (InspectionSheet)

环检单是本次业务的核心实体，记录了车辆入场后、工单创建前的标准化检查过程和结果。

| 字段名 (逻辑名) | 字段名 (物理名) | 数据类型 | 是否可空 | 主/外键 | 注释 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 环检单ID | id | Long | N | PK | 唯一主键 |
| 环检单号 | inspection_no | VARCHAR(32) | N | | 业务唯一编号，按规则生成 |
| 环检状态 | status | VARCHAR(20) | N | | 枚举值：待环检、环检中、待确认、已确认 |
| 客户ID | customer_id | Long | Y | FK | 关联客户实体 |
| 送修人名称 | presenter_name | VARCHAR(50) | N | | |
| 送修人手机号 | presenter_phone | VARCHAR(20) | N | | |
| 车辆ID | vehicle_id | Long | Y | FK | 关联车辆实体 |
| 车牌号 | license_plate | VARCHAR(20) | N | | |
| VIN号 | vin | VARCHAR(17) | Y | | |
| 车型配置 | model_config | VARCHAR(100) | Y | | |
| 颜色 | color | VARCHAR(20) | Y | | |
| 登记类型 | registration_type | VARCHAR(20) | N | | 枚举：预约、自然到店 |
| 服务类型 | service_type | VARCHAR(20) | Y | | 枚举：保养、维修 |
| 服务顾问ID | service_advisor_id | Long | Y | FK | 关联员工/用户实体 |
| 服务顾问名称 | service_advisor_name | VARCHAR(50) | Y | | |
| 客户确认时间 | customer_confirm_time | DATETIME | Y | | 客户最终确认的时间 |
| 客户确认方式 | customer_confirm_method | VARCHAR(20) | Y | | 枚举：线上确认、线下确认 |
| 客户确认照片 | customer_confirm_photo_url | VARCHAR(255) | Y | | 线下确认时上传的照片URL |
| 备注 | remarks | VARCHAR(500) | Y | | |
| 创建时间 | create_time | DATETIME | N | | |
| 更新时间 | update_time | DATETIME | N | | |
| 关联工单ID | work_order_id | Long | Y | FK | 环检单确认后创建的工单ID |

### 1.2 环检项 (InspectionItem)

环检项用于存储每一次环检的具体检查内容和结果。一个环检单包含多个环检项。

| 字段名 (逻辑名) | 字段名 (物理名) | 数据类型 | 是否可空 | 主/外键 | 注释 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 环检项ID | id | Long | N | PK | 唯一主键 |
| 环检单ID | inspection_sheet_id | Long | N | FK | 关联环检单 |
| 检查大类 | category | VARCHAR(50) | N | | 例如：停车区域记录、仪表盘检查、功能性检查、警告灯检查、外观检查、电动系统检查、轮胎检查 |
| 检查子项 | sub_category | VARCHAR(100) | N | | 例如：等候区、里程数记录、前视图检查、高压电池检查等 |
| 检查结果 | result | VARCHAR(20) | Y | | 枚举：良好、需要注意、不良、不适用 |
| 备注/说明 | notes | VARCHAR(500) | Y | | 对检查结果的详细描述 |
| 附件URL列表 | attachment_urls | TEXT | Y | | 检查时拍摄的照片URL，多个用逗号分隔 |

## 2. 实体关系图 (ERD)

```mermaid
erDiagram
    CUSTOMER ||--|{ INSPECTION_SHEET : "has"
    VEHICLE ||--|{ INSPECTION_SHEET : "has"
    USER ||--|{ INSPECTION_SHEET : "is handled by (ServiceAdvisor)"
    INSPECTION_SHEET ||--|{ INSPECTION_ITEM : "contains"
    INSPECTION_SHEET }o--|| WORK_ORDER : "creates"

    CUSTOMER {
        long id PK
        string name
        string phone
    }
    VEHICLE {
        long id PK
        string license_plate
        string vin
        string model_config
        string color
    }
    USER {
        long id PK
        string name
        string role
    }
    INSPECTION_SHEET {
        long id PK
        string inspection_no
        string status
        long customer_id FK
        string presenter_name
        string presenter_phone
        long vehicle_id FK
        string license_plate
        string registration_type
        string service_type
        long service_advisor_id FK
        datetime customer_confirm_time
        string customer_confirm_method
        string customer_confirm_photo_url
        long work_order_id FK
    }
    INSPECTION_ITEM {
        long id PK
        long inspection_sheet_id FK
        string category
        string sub_category
        string result
        string notes
        text attachment_urls
    }
    WORK_ORDER {
        long id PK
        string work_order_no
    }
```

## 3. 枚举值定义

### 3.1 环检单状态 (InspectionStatus)

| 枚举值 | 描述 |
| :--- | :--- |
| TO_INSPECT | 待环检 |
| INSPECTING | 环检中 |
| PENDING_CONFIRMATION | 待确认 |
| CONFIRMED | 已确认 |

### 3.2 登记类型 (RegistrationType)

| 枚举值 | 描述 |
| :--- | :--- |
| APPOINTMENT | 预约 |
| WALK_IN | 自然到店 |

### 3.3 服务类型 (ServiceType)

| 枚举值 | 描述 |
| :--- | :--- |
| MAINTENANCE | 保养 |
| REPAIR | 维修 |

### 3.4 客户确认方式 (ConfirmMethod)

| 枚举值 | 描述 |
| :--- | :--- |
| ONLINE | 线上确认 |
| OFFLINE | 线下确认 |

### 3.5 检查结果 (InspectionResult)

| 枚举值 | 描述 |
| :--- | :--- |
| GOOD | 良好 |
| ATTENTION_REQUIRED | 需要注意 |
| NOT_GOOD | 不良 |
| NOT_APPLICABLE | 不适用 |

## 4. 业务规则约束

### 4.1 服务类型约束
- 登记类型为"预约"时：服务类型可为"保养"或"维修"
- 登记类型为"自然到店"时：服务类型仅为"维修"

### 4.2 状态流转约束
- 待环检 → 环检中 → 待确认 → 已确认
- 待确认状态可回退到环检中（撤回操作）

### 4.3 客户确认约束
- 线下确认时必须上传客户确认照片
- 确认时必须记录客户确认时间和确认方式 