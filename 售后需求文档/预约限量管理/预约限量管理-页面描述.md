# 预约限量管理 - 页面描述文档

## 页面设计原则

本页面采用现代化的Web界面设计，遵循以下原则：
- **响应式布局**：适配PC端和移动端，确保在不同设备上都有良好的用户体验
- **现代化UI设计**：使用Element Plus组件库，界面简洁清晰，符合现代审美
- **关键操作突出**：重要按钮和功能入口使用显著的视觉设计，减少用户认知负担
- **数据结构化展示**：采用表格和卡片布局，便于快速查找和操作
- **实时反馈**：操作过程中提供即时的状态反馈和校验提示

## 主页面布局描述

### 整体布局结构
页面采用经典的管理系统布局，分为五个主要区域：页面标题、门店信息卡片、功能操作区、数据列表区和分页控件。

```
┌─────────────────────────────────────────────┐
│                预约限量管理                    │  ← 页面标题区
├─────────────────────────────────────────────┤
│ 🏪 门店信息卡片区域                           │  ← 门店信息展示
├─────────────────────────────────────────────┤
│ 📋 已配置限量列表     [+ 新增预约限量]        │  ← 功能操作区
├─────────────────────────────────────────────┤
│ 📊 数据表格区域（主要内容）                   │  ← 数据列表区
├─────────────────────────────────────────────┤
│                                   [分页控件] │  ← 分页控制区
└─────────────────────────────────────────────┘
```

### 1. 页面标题区域
**位置**：页面顶部
**内容**：标题文字"预约限量管理"
**样式**：
- 字体大小：28px
- 颜色：#303133（深灰色）
- 对齐方式：居中
- 背景色：#f5f7fa（浅灰背景）
- 内边距：上下20px

### 2. 门店信息卡片区域
**位置**：标题下方
**布局**：使用ElCard组件，两列布局展示门店信息

```
┌─────────────────────────────────────────────┐
│ 门店名称：XXX门店     门店编码：001            │
│ 💡 提示：您有权限配置本门店的预约限量           │
└─────────────────────────────────────────────┘
```

**组件结构**：
```vue
<el-card class="mb-20 store-info-card">
  <el-row :gutter="20">
    <el-col :span="12">门店名称：{{ storeInfo.name }}</el-col>
    <el-col :span="12">门店编码：{{ storeInfo.code }}</el-col>
  </el-row>
  <el-row class="permission-tip">
    <el-icon><InfoFilled /></el-icon>
    <span>权限提示信息</span>
  </el-row>
</el-card>
```

**样式特点**：
- 卡片阴影：轻微阴影效果
- 字体大小：16px（信息行），14px（提示行）
- 颜色：#606266（信息），#909399（提示）
- 图标：使用InfoFilled图标

### 3. 功能操作区域
**位置**：门店信息卡片下方
**布局**：左右布局，左侧标题，右侧操作按钮

```
┌─────────────────────────────────────────────┐
│ 已配置限量列表                [+ 新增预约限量]  │
└─────────────────────────────────────────────┘
```

**组件结构**：
```vue
<el-card class="mb-20">
  <div class="card-header">
    <h2>已配置限量列表</h2>
    <el-button type="primary" :icon="Plus" @click="openNewQuotaModal">
      新增预约限量
    </el-button>
  </div>
</el-card>
```

**按钮样式**：
- 类型：主要按钮（primary）
- 图标：Plus图标
- 颜色：主题蓝色
- 状态：hover时颜色加深

### 4. 数据列表区域
**位置**：功能操作区下方
**组件**：ElTable表格组件
**表头字段**：

| 列名 | 宽度 | 对齐 | 说明 |
|------|------|------|------|
| 序号 | 80px | 居中 | 自动序号 |
| 配置日期 | 150px | 左对齐 | YYYY-MM-DD格式 |
| 时段数量 | 120px | 居中 | 数字+单位"个" |
| 总配额 | 100px | 居中 | 数字 |
| 已预约数量 | 120px | 居中 | 数字 |
| 最后更新时间 | 180px | 居中 | YYYY-MM-DD HH:mm格式 |
| 操作 | 100px | 居中 | 按钮组 |

**操作列设计**：
- **编辑按钮**：仅未过期配置显示，link类型，primary颜色
- **过期标识**：过期配置显示"已过期"文字，灰色斜体

**空状态设计**：
```vue
<div class="empty-state">
  <el-icon class="empty-icon"><Calendar /></el-icon>
  <p>暂无已配置的预约限量</p>
</div>
```

### 5. 分页控制区域
**位置**：表格下方
**布局**：右对齐
**功能**：支持页码跳转、页大小选择

```vue
<el-pagination
  background
  layout="total, sizes, prev, pager, next, jumper"
  :total="totalRecords"
  :page-sizes="[10, 20, 50]"
/>
```

## 弹窗页面设计

### 弹窗整体布局
弹窗采用模态对话框形式，宽度600px，分为四个主要区域：

```
┌─────────────────────────────────┐
│        新增/编辑预约限量           │  ← 弹窗标题
├─────────────────────────────────┤
│ 📅 日期选择区域                  │  ← 日期配置
├─────────────────────────────────┤
│ ⏰ 时段配置区域                  │  ← 时段配置
├─────────────────────────────────┤
│ 📋 配置说明区域                  │  ← 规则说明
├─────────────────────────────────┤
│ 汇总信息              [取消][保存] │  ← 底部操作
└─────────────────────────────────┘
```

### 1. 日期选择区域
**布局**：卡片形式，包含日期选择器和提示信息

```vue
<el-card class="mb-20">
  <template #header>
    <span>📅 选择配置日期</span>
  </template>
  <el-form-item label="配置日期">
    <el-date-picker
      v-model="modalSelectedDate"
      type="date"
      value-format="YYYY-MM-DD"
      placeholder="请选择配置日期"
      :disabled-date="disablePastDates"
    />
  </el-form-item>
  <div v-if="hasExistingConfig" class="existing-config-tip">
    <el-tag type="warning">该日期已有配置</el-tag>
  </div>
  <div class="info-tip">
    <el-icon><InfoFilled /></el-icon>
    <span>只能配置今日及未来日期的预约限量</span>
  </div>
</el-card>
```

**功能特点**：
- 日期限制：禁用过去日期
- 已有配置提示：显示警告标签
- 操作指引：底部说明文字

### 2. 时段配置区域
**布局**：可滚动的时段列表，每个时段为独立卡片

```vue
<el-card class="mb-20">
  <template #header>
    <div class="card-header-flex">
      <span>⏰ 时段配置</span>
      <el-button type="info" :icon="Plus" link @click="addModalTimeSlot">
        添加时段
      </el-button>
    </div>
  </template>
  
  <!-- 空状态 -->
  <div v-if="modalTimeSlots.length === 0" class="empty-state-modal">
    <el-icon class="empty-icon"><Clock /></el-icon>
    <p>暂无时段配置</p>
    <p>点击右上角"添加时段"开始配置</p>
  </div>
  
  <!-- 时段列表 -->
  <div class="time-slot-list">
    <el-card v-for="(slot, index) in modalTimeSlots" :key="slot.id">
      <template #header>
        <div class="card-header-flex">
          <span>时段 {{ index + 1 }}</span>
          <el-button type="danger" :icon="Delete" link>删除</el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="开始时间">
            <el-select v-model="slot.start">
              <el-option 
                v-for="item in timeOptions" 
                :key="item.value"
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="结束时间">
            <el-select v-model="slot.end">
              <el-option 
                v-for="item in endTimeOptions" 
                :key="item.value"
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="配额">
            <el-input 
              v-model.number="slot.quota"
              type="number"
              :min="1"
              placeholder="请输入配额，默认为1"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
  </div>
</el-card>
```

**时段卡片样式**：
- 背景色：#F8F8F8（浅灰）
- 边框：1px虚线 #DCDFE6
- 间距：底部10px间距
- 三列布局：开始时间、结束时间、配额

**时间选择器特点**：
- 选项范围：8:00-18:00
- 时间间隔：30分钟
- 智能过滤：结束时间自动过滤无效选项

### 3. 配置说明区域
**布局**：简单的列表形式展示配置规则

```vue
<el-card>
  <template #header>
    <span>📋 配置说明</span>
  </template>
  <ul>
    <li>预约时段必须在营业时间内（8:00-18:00）</li>
    <li>时段之间不能重叠</li>
    <li>每个时段的配额数量至少为1</li>
    <li>配置保存后立即生效</li>
  </ul>
</el-card>
```

### 4. 底部操作区域
**布局**：左侧汇总信息，右侧操作按钮

```vue
<template #footer>
  <div class="dialog-footer-buttons">
    <span class="summary-info">{{ modalSummaryInfo }}</span>
    <div>
      <el-button @click="closeModal">取消</el-button>
      <el-button type="primary" :icon="Check" @click="saveConfig">
        保存配置
      </el-button>
    </div>
  </div>
</template>
```

**汇总信息格式**：
- 示例："2024-01-01 · 3个时段 · 总配额15"
- 动态计算：根据当前配置实时更新
- 样式：粗体，深色文字

## 交互行为设计

### 1. 日期选择联动
- **触发**：用户选择日期
- **行为**：自动检查该日期是否已有配置
- **反馈**：显示"已有配置"标签或清空时段列表

### 2. 时段智能建议
- **触发**：点击"添加时段"按钮
- **行为**：自动计算建议的开始和结束时间
- **逻辑**：基于最后一个时段的结束时间，建议下一个连续时段

### 3. 实时校验反馈
- **时段重叠校验**：配置保存时检查，显示具体冲突信息
- **时间有效性校验**：结束时间必须晚于开始时间
- **配额校验**：必须为正整数，最小值1

### 4. 确认交互
- **关闭弹窗确认**：有未保存修改时显示确认对话框
- **删除时段确认**：直接删除，无二次确认
- **保存成功反馈**：显示成功提示，自动关闭弹窗

## 响应式设计

### PC端适配（≥1200px）
- 表格列宽充分展开
- 弹窗宽度600px居中显示
- 时段配置三列布局

### 平板适配（768px-1199px）
- 表格支持横向滚动
- 弹窗宽度90%
- 时段配置保持三列但间距缩小

### 移动端适配（<768px）
- 表格改为卡片式布局
- 弹窗全屏显示
- 时段配置改为单列堆叠布局

## 状态管理

### 加载状态
- **列表加载**：表格显示loading骨架屏
- **保存加载**：按钮显示loading动画
- **数据加载**：弹窗内容显示加载指示器

### 错误状态
- **网络错误**：显示重试按钮
- **校验错误**：表单项下方显示红色错误文字
- **操作失败**：全局消息提示

### 成功状态
- **保存成功**：全局成功消息
- **操作完成**：按钮短暂显示成功状态

## 无障碍设计

### 键盘支持
- Tab键顺序访问所有可交互元素
- Enter键确认操作
- Esc键关闭弹窗

### 屏幕阅读器支持
- 所有按钮和输入框都有适当的aria-label
- 表格有合适的表头关联
- 状态变化有语音提示

### 色彩对比
- 文字与背景对比度符合WCAG 2.1标准
- 重要信息不仅依赖颜色区分
- 支持高对比度模式 