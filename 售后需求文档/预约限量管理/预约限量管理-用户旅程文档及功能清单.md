# 预约限量管理 - 用户旅程文档及功能清单

## 1. 用户旅程

### 1.1 门店管理人员 - 新增配置旅程

1. **准备配置：** 登录DMS系统进入预约限量管理页面，查看当前配置列表，确认需要新增的日期。
2. **配置时段：** 点击新增预约限量按钮，选择配置日期，添加时段并设置时间和配额。
3. **添加时段：** 继续添加更多时段，检查配置汇总确保时段完整性。
4. **保存确认：** 点击保存配置，等待系统校验，查看保存结果并确认配置生效。

### 1.2 门店管理人员 - 编辑配置旅程

1. **查找配置：** 查看配置列表，找到需要修改的配置，确认配置未过期。
2. **编辑操作：** 点击编辑按钮，加载现有配置，修改时段设置和调整配额数量。
3. **删除时段：** 删除不需要的时段，确认修改内容。
4. **保存更新：** 保存配置，查看更新结果确认变更生效。

### 1.3 系统自动生成旅程

1. **触发执行：** 每月1日凌晨自动触发，计算下个月工作日，检查现有配置避免冲突。
2. **生成配置：** 遍历每个工作日，生成默认时段配置，设置默认配额并保存到数据库。
3. **完成处理：** 记录生成日志，更新配置统计，发送完成通知。

## 2. 核心功能点清单

### 2.1 预约限量管理主页面 (Appointment Quota Management)

1. **门店信息展示：**
   * 显示门店名称和编码。
   * 显示权限提示信息。
   * 支持多语言显示。

2. **配置列表管理：**
   * 分页显示已配置限量，显示配置状态（有效/过期）。
   * 支持按日期排序，显示配置统计信息。
   * 过期配置灰化显示，已有配置标签提示。

3. **新增限量配置：**
   * 日期选择（限制今日及未来），禁止选择过去日期。
   * 时段动态添加/删除，时间选择器（8:30-17:00，1小时间隔）。
   * 配额数量设置，**新增时间段默认限额为1**，实时配置汇总显示。
   * 时段重叠检查，开始结束时间验证，营业时间范围限制。

4. **编辑限量配置：**
   * 加载现有配置数据，支持修改时段和配额。
   * **禁止对当天及之前的预约限额进行修改**。
   * 限制过期配置编辑，保留原有配置状态。
   * 支持单个时段删除，支持批量清空，删除前确认提示。

5. **智能校验系统：**
   * 输入时实时校验，错误信息明确提示，成功操作确认。
   * 配额数量必须为正整数，最小值限制（≥1），数值格式验证。
   * 自动计算下一时段时间，避免时段重叠，智能时间间隔推荐。

### 2.2 系统自动化管理 (System Automation)

1. **定时任务执行：**
   * 每月1日凌晨执行，计算下个月工作日，生成默认配置。
   * 排除周末（周六、周日），集成节假日API，支持节假日配置。
   * 检查已有人工配置，跳过冲突日期，记录跳过原因。

2. **默认配置生成：**
   * 基于**门店自定义工作日**和**1小时时间间隔**生成默认时段，配额默认为1。
   * 批量插入数据库，事务处理保证一致性，异常回滚机制。

3. **任务监控功能：**
   * 记录任务执行时间，记录生成的配置数量，记录异常和错误。
   * 任务失败自动重试，重试次数限制，重试间隔设置。

### 2.3 数据管理与集成 (Data Management & Integration)

1. **配置存储管理：**
   * 新增配置记录，查询配置列表，更新配置信息，软删除历史配置。
   * 时段数据关联管理，级联删除处理，时段排序和索引。

2. **统计数据管理：**
   * 预约数量实时统计，剩余配额计算，配置使用率统计。
   * 过期配置归档，历史数据查询，数据清理策略。

3. **接口集成功能：**
   * AS_03接口支持（查询可预约时段，返回剩余配额，计算可预约状态）。
   * 接收预约成功通知，更新已预约数量，处理预约取消。
   * 操作日志记录，审计信息追踪，日志查询和分析。

4. **权限控制：**
   * 门店级别权限控制，操作权限验证，跨门店访问限制。
   * 门店数据隔离，用户数据范围限制，敏感操作二次确认。

--- 