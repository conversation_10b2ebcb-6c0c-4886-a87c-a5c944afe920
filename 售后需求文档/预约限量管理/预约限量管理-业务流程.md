# 预约限量管理 - 业务流程分析文档

## 业务流程主线确认

### 主要业务场景
预约限量管理是售后业务线中用于控制线上预约流入的管理功能，主要服务于门店管理人员配置和管理预约资源限量的需求。

## 核心业务流程

### 1. 手动配置流程（主流程）

```mermaid
flowchart TD
    A[门店管理人员登录DMS系统] --> B[进入预约限量管理页面]
    B --> C[查看已配置限量列表]
    C --> D{选择操作}
    
    D -->|新增配置| E[点击新增预约限量按钮]
    D -->|编辑配置| F[点击编辑按钮]
    D -->|查看详情| G[查看配置详情]
    
    E --> H[打开配置弹窗]
    F --> H
    H --> I[选择配置日期]
    I --> J{日期是否已有配置}
    
    J -->|是| K[加载已有配置数据]
    J -->|否| L[初始化空时段列表]
    
    K --> M[配置时段信息]
    L --> M
    M --> N[添加/编辑时段]
    N --> O[设置开始时间]
    O --> P[设置结束时间]
    P --> Q[设置配额数量, 新增默认为1]
    Q --> R{是否继续添加时段}
    
    R -->|是| N
    R -->|否| S[系统校验配置]
    S --> T{校验是否通过}
    
    T -->|否| U[显示错误提示]
    U --> M
    T -->|是| V[保存配置]
    V --> W[更新数据库]
    W --> X[刷新列表页面]
    X --> Y[配置完成]
    
    G --> Z[显示配置详情]
```

### 2. 自动生成流程（辅助流程）

```mermaid
flowchart TD
    A[每月1日凌晨] --> B[定时任务启动]
    B --> C[计算下个月日期范围]
    C --> D[遍历下个月每一天]
    D --> E{是否为**门店自定义工作日**}
    
    E -->|否-周末或非工作日| F[跳过该日期]
    E -->|是| G[检查是否已有人工配置]
    
    G -->|已有配置| H[跳过该日期]
    G -->|无配置| I[生成默认配置]
    
    I --> J[按1小时间隔创建默认时段]
    J --> K[设置每时段配额为1]
    K --> L[保存到数据库]
    L --> M[记录生成日志]
    
    F --> N{是否为月末最后一天}
    H --> N
    M --> N
    
    N -->|否| D
    N -->|是| O[生成任务完成]
    O --> P[发送完成通知]
```

## 详细业务节点分析

### 节点1：系统进入与权限验证
**触发条件：** 门店管理人员访问预约限量管理页面
**业务逻辑：**
- 验证用户身份和权限
- 加载门店基本信息（门店名称、门店编码）
- 显示权限提示信息

**输入数据：** 用户登录凭证、门店ID
**输出数据：** 门店信息、用户权限状态
**异常处理：** 无权限时显示提示信息

### 节点2：配置列表查询与展示
**触发条件：** 页面初始化或刷新
**业务逻辑：**
- 查询当前门店的所有限量配置
- 计算每个配置的过期状态
- 支持分页展示

**输入数据：** 门店ID、分页参数
**输出数据：** 配置列表、总记录数
**关键字段：** 序号、配置日期、时段数量、总配额、已预约数量、最后更新时间、操作

### 节点3：日期选择与配置初始化
**触发条件：** 新增或编辑配置时选择日期
**业务逻辑：**
- 限制只能选择今日及未来日期
- 检查选定日期是否已有配置
- 加载已有配置或初始化空配置

**输入数据：** 选择的日期
**输出数据：** 时段配置列表（可能为空）
**业务规则：** 
- 过去日期不可选
- 已有配置显示提示标签

### 节点4：时段配置管理
**触发条件：** 添加、编辑、删除时段操作
**业务逻辑：**
- 支持动态添加/删除时段
- 自动计算建议的下一时段时间
- 实时校验时段配置合理性

**输入数据：** 时段信息（开始时间、结束时间、配额）
**输出数据：** 更新后的时段列表
**业务规则：**
- 时段不能重叠
- 开始时间必须早于结束时间
- 配额必须为正整数
- 时段必须在营业时间内（8:00-18:00）

### 节点5：配置校验与保存
**触发条件：** 点击保存按钮
**业务逻辑：**
- 执行完整性校验
- 保存配置到数据库
- 更新相关统计信息

**校验规则：**
1. 日期必须选择
2. 至少包含一个时段
3. 所有时段时间必须完整
4. 时段不能重叠
5. 配额必须为正数
6. **禁止** 对当天及之前的预约限额进行修改

**输入数据：** 完整配置信息
**输出数据：** 保存结果、错误信息（如有）

### 节点6：自动生成任务执行
**触发条件：** 每月1日凌晨定时任务
**业务逻辑：**
- 计算下个月的工作日
- 为每个工作日生成默认配置
- 跳过已有人工配置的日期

**默认配置规则：**
- 时段：8:00-9:00, 9:00-10:00, 10:00-11:00, 11:00-12:00, 14:00-15:00, 15:00-16:00, 16:00-17:00, 17:00-18:00
- 配额：每时段1个
- 跳过午休时间：12:00-14:00

## 业务流程的状态管理

### 配置状态
1. **草稿状态：** 正在编辑但未保存的配置
2. **有效状态：** 已保存且日期未过期的配置
3. **过期状态：** 日期已过的历史配置

### 操作权限状态
1. **可编辑：** 未过期的配置
2. **只读：** 过期的配置
3. **可删除：** 未来日期且无预约的配置

## 异常流程处理

### 1. 配置冲突处理
**场景：** 手动配置与自动生成冲突
**处理：** 自动生成跳过已有人工配置的日期

### 2. 时段重叠处理
**场景：** 用户配置了重叠的时段
**处理：** 校验阶段阻止保存，显示具体冲突信息

### 3. 网络异常处理
**场景：** 保存配置时网络中断
**处理：** 显示错误提示，保留用户输入数据

### 4. 数据不一致处理
**场景：** 多用户同时编辑同一配置
**处理：** 后保存者覆盖，显示冲突提示

## 业务流程与外部系统集成

### 1. 与客户端预约系统集成
**集成点：** AS_03接口调用
**数据流向：** DMS → Super APP
**集成内容：** 实时查询可预约时段和剩余配额

### 2. 与预约统计系统集成
**集成点：** 预约数量更新
**数据流向：** 预约系统 → DMS
**集成内容：** 实时更新已预约数量

### 3. 与日志系统集成
**集成点：** 操作记录
**数据流向：** DMS → 日志系统
**集成内容：** 配置操作、自动生成记录

## 业务规则总结

### 时间相关规则
1. 只能配置当日及未来日期
2. 时段必须在营业时间内（8:00-18:00）
3. 时段之间不能重叠
4. 开始时间必须早于结束时间

### 配额相关规则
1. 每个时段配额至少为1
2. 配额必须为正整数
3. 已预约数量不能超过配额

### 自动化规则
1. 每月1日自动生成下月配置
2. 只为工作日生成，跳过周末和节假日
3. 不覆盖已有人工配置
4. 默认配额为1，可后续调整

### 权限规则
1. 只有门店管理人员可以配置
2. 过期配置不允许编辑
3. 配置立即生效 