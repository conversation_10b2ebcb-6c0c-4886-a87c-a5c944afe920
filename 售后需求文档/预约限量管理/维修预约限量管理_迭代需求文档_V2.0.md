# 维修预约限量管理 - 迭代需求文档 V2.0

**产品模块：** 售后 -> 维修预约限量管理
**版本：** 2.0
**需求提出人：** [用户姓名]
**产品经理：** Gemini
**日期：** 2025-07-30
**目标：** 优化维修预约限量页面的用户体验和操作逻辑，简化界面，并收紧编辑权限。

---

## 需求变更总览

本次迭代主要包含以下三个核心改动点：

1.  **UI简化：** 移除“新增/编辑”模态框中的【配置说明】区域。
2.  **UI简化：** 移除主列表页面门店信息下的【权限提示】文案。
3.  **逻辑调整：** 将“编辑”功能的权限收紧，仅允许用户操作【明天】及之后日期的预约限量数据。

---

## 详细需求描述

### 1. 改动点：移除模态框中的【配置说明】

- **Jira Ticket:** [链接到对应的Jira任务]
- **变更类型:** UI/UX 优化

#### **【现状分析】**

根据《预约限量管理-页面描述文档》，当前在点击“新增预约限量”或“编辑预约限量”时，弹出的模态框底部包含一个“配置说明”区域，用于向用户提示配置时的基本规则。

*   **功能逻辑：** 该区域为静态信息展示，用于向用户提示配置时的基本规则，如时段不能重叠、配额必须大于等于1等。
*   **界面示意图 (ASCII):**

    ```
    ┌───────────────────────────────────────────┐
    │ ●  新增/编辑预约限量                      │
    ├───────────────────────────────────────────┤
    │                                           │
    │         [ 📅 日期选择区域 ]               │
    │                                           │
    ├───────────────────────────────────────────┤
    │                                           │
    │         [ ⏰ 时段配置区域 ]               │
    │                                           │
    ├───────────────────────────────────────────┤
    │                                           │
    │         [ 📋 配置说明区域 ]               │  <-- 当前存在的区域
    │                                           │
    ├───────────────────────────────────────────┤
    │ 汇总信息...                  [取消] [保存] │
    └───────────────────────────────────────────┘
    ```

#### **【期望变更】**

*   **功能逻辑：** 移除该“配置说明”区域。此改动旨在简化界面，减少信息冗余。
*   **界面示意图 (ASCII):**

    ```
    ┌───────────────────────────────────────────┐
    │ ●  新增/编辑预约限量                      │
    ├───────────────────────────────────────────┤
    │                                           │
    │         [ 📅 日期选择区域 ]               │
    │                                           │
    ├───────────────────────────────────────────┤
    │                                           │
    │         [ ⏰ 时段配置区域 ]               │
    │                                           │
    ├───────────────────────────────────────────┤
    │ 汇总信息...                  [取消] [保存] │
    └───────────────────────────────────────────┘
    ```

---

### 2. 改动点：移除列表页的门店信息提示文案

- **Jira Ticket:** [链接到对应的Jira任务]
- **变更类型:** UI/UX 优化

#### **【现状分析】**

根据《预约限量管理-页面描述文档》，主页面顶部有一个“门店信息卡片”，用于展示当前登录用户所属的门店信息，并包含一行提示文案。

*   **功能逻辑：** 该卡片除了显示门店名称和编码外，还包含一行静态的提示文案：“您当前只能管理本门店的预约限量配置”，以明确用户的操作范围。
*   **界面示意图 (ASCII):**

    ```
    ┌─────────────────────────────────────────────┐
    │ 🏪 门店信息卡片区域                         │
    ├─────────────────────────────────────────────┤
    │ 门店名称：XXX门店     门店编码：001           │
    │ 💡 您当前只能管理本门店的预约限量配置         │  <-- 当前存在的提示文案
    └─────────────────────────────────────────────┘
    ```

#### **【期望变更】**

*   **功能逻辑：** 移除这行提示文案。改动后，该卡片将只纯粹地展示门店的基础信息（名称和编码）。
*   **界面示意图 (ASCII):**

    ```
    ┌─────────────────────────────────────────────┐
    │ 🏪 门店信息卡片区域                         │
    ├─────────────────────────────────────────────┤
    │ 门店名称：XXX门店     门店编码：001           │
    │                                             │  <-- 提示文案被移除
    └─────────────────────────────────────────────┘
    ```

---

### 3. 改动点：编辑功能仅限未来日期

- **Jira Ticket:** [链接到对应的Jira任务]
- **变更类型:** 业务逻辑变更

#### **【现状分析】**

当前逻辑是，对于列表中的某一项，如果它**未过期**（包含今天），则“编辑”按钮是可见且可点击的。

*   **功能逻辑：** 业务文档意图是只能编辑未来配置，但前端实现上可能允许编辑当天的数据。
*   **界面示意图 (ASCII) - 列表行：**

    ```
    | 配置日期       | ... | 操作        |
    |----------------|-----|-------------|
    | 2025-07-29 (昨天) | ... | [已过期]    |  <-- 不可编辑
    | 2025-07-30 (今天) | ... | [编辑]      |  <-- 当前可以编辑
    | 2025-07-31 (明天) | ... | [编辑]      |  <-- 当前可以编辑
    ```

#### **【期望变更】**

*   **功能逻辑：**
    1.  **核心变更：** 将编辑权限的判断基准从“未过期”（包含今天）收紧为“严格大于今天”。
    2.  **前端实现：** 在预约限量管理列表页，对于日期为**今天或今天以前**的配置，其“编辑”按钮需要被**禁用**（置灰）或**隐藏**。
    3.  **后端校验：** **强烈建议**在后端 `saveQuotaConfig` 接口中也增加同样的校验逻辑，即检查 `configDate` 是否大于当前服务器日期。这可以防止用户通过API等非常规手段提交修改，确保数据一致性和业务规则的强制执行。

*   **界面示意图 (ASCII) - 列表行：**

    ```
    | 配置日期       | ... | 操作        |
    |----------------|-----|-------------|
    | 2025-07-29 (昨天) | ... | [已过期]    |  <-- 保持不可编辑
    | 2025-07-30 (今天) | ... | [不可编辑]  |  <-- 期望变更为不可编辑 (按钮置灰)
    | 2025-07-31 (明天) | ... | [编辑]      |  <-- 保持可编辑
    ```
