# 维修预约限量管理 - 接口文档

## 📋 接口定义

### 1. 获取门店信息

-   **接口地址**：`/after-sales/quota/store-info`
-   **请求方式**：`GET`
-   **接口描述**：获取当前用户所属的门店基本信息，用于页面顶部展示。

#### 请求参数

无

#### 响应结果

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "S001",
    "name": "演示服务中心",
    "code": "DMS-DEMO-STORE"
  }
}
```

| 字段名 | 类型   | 说明         |
| :----- | :----- | :----------- |
| `id`   | string | 门店唯一标识 |
| `name` | string | 门店完整名称 |
| `code` | string | 门店编码     |

---

### 2. 获取预约限量配置列表

-   **接口地址**：`/after-sales/quota/list`
-   **请求方式**：`GET`
-   **接口描述**：分页查询指定门店的预约限量配置列表。

#### 请求参数

| 参数名     | 类型    | 必填 | 说明                                   |
| :--------- | :------ | :--- | :------------------------------------- |
| `page`     | integer | 否   | 当前页码，默认为 1                     |
| `pageSize` | integer | 否   | 每页显示条数，默认为 10                |
| `storeId`  | string  | 否   | 门店ID (通常由后端根据用户身份自动注入) |

#### 响应结果

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 101,
        "configDate": "2025-08-01",
        "timeSlotCount": 8,
        "totalQuota": 12,
        "bookedQuantity": 5,
        "lastUpdateTime": "2025-07-29 10:30:00",
        "isExpired": false
      }
    ],
    "total": 1
  }
}
```

| 字段名             | 类型    | 说明                               |
| :----------------- | :------ | :--------------------------------- |
| `data.list`        | array   | 配置列表数组                       |
| `data.list[].id`   | integer | 配置唯一标识                       |
| `data.list[].configDate` | string  | 配置日期 (YYYY-MM-DD)              |
| `data.list[].timeSlotCount` | integer | 当天配置的时段总数                 |
| `data.list[].totalQuota` | integer | 当天配置的总配额数                 |
| `data.list[].bookedQuantity` | integer | 当天已产生的预约数量               |
| `data.list[].lastUpdateTime` | string  | 最后更新时间 (YYYY-MM-DD HH:mm:ss) |
| `data.list[].isExpired` | boolean | 是否已过期 (true/false)            |
| `data.total`       | integer | 总记录数                           |

---

### 3. 获取指定日期的时段配置

-   **接口地址**：`/after-sales/quota/time-slots/{date}`
-   **请求方式**：`GET`
-   **接口描述**：根据指定日期获取已有的时段配置。用于编辑场景加载现有数据。

#### 请求参数

| 参数名 | 类型   | 必填 | 说明                  |
| :----- | :----- | :--- | :-------------------- |
| `date` | string | 是   | 路径参数，格式 YYYY-MM-DD |

#### 响应结果

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 201,
      "start": "09:00",
      "end": "10:00",
      "quota": 2
    },
    {
      "id": 202,
      "start": "10:00",
      "end": "11:00",
      "quota": 1
    }
  ]
}
```

| 字段名        | 类型    | 说明           |
| :------------ | :------ | :------------- |
| `data[].id`    | integer | 时段唯一标识   |
| `data[].start` | string  | 开始时间 (HH:mm) |
| `data[].end`   | string  | 结束时间 (HH:mm) |
| `data[].quota` | integer | 该时段的配额数 |

---

### 4. 保存预约限量配置

-   **接口地址**：`/after-sales/quota/config`
-   **请求方式**：`POST`
-   **接口描述**：新增或更新某一天的预约限量配置。后端通过 `date` 字段判断是新增还是更新。

#### 请求参数

```json
{
  "date": "2025-08-02",
  "timeSlots": [
    {
      "start": "09:00",
      "end": "10:00",
      "quota": 2
    },
    {
      "start": "10:00",
      "end": "11:00",
      "quota": 3
    }
  ]
}
```

| 参数名              | 类型    | 必填 | 说明                               |
| :------------------ | :------ | :--- | :--------------------------------- |
| `date`              | string  | 是   | 配置日期 (YYYY-MM-DD)              |
| `timeSlots`         | array   | 是   | 时段配置列表                       |
| `timeSlots[].start` | string  | 是   | 开始时间 (HH:mm)                   |
| `timeSlots[].end`   | string  | 是   | 结束时间 (HH:mm)                   |
| `timeSlots[].quota` | integer | 是   | 配额数量，必须为正整数             |

#### 响应结果

```json
{
  "code": 200,
  "message": "配置保存成功",
  "data": {
    "success": true,
    "message": "配置保存成功"
  }
}
```

| 字段名      | 类型    | 说明           |
| :---------- | :------ | :------------- |
| `success`   | boolean | 操作是否成功   |
| `message`   | string  | 操作结果的文字描述 |

## ⚙️ 交互功能说明

### 页面初始化

-   **功能描述：** 进入页面时，加载并展示核心信息。
-   **调用接口：**
    1.  并行调用 `GET /after-sales/quota/store-info` 获取门店信息。
    2.  并行调用 `GET /after-sales/quota/list` 获取第一页的配置列表。

### 列表操作

-   **功能描述：** 对配置列表进行分页、刷新等操作。
-   **调用接口：**
    -   **分页/改变每页条数：** 调用 `GET /after-sales/quota/list` 并传入对应的 `page` 和 `pageSize` 参数。

### 新增/编辑配置

-   **功能描述：** 通过弹窗完成对单日预约限量的配置。
-   **调用接口：**
    -   **打开编辑弹窗：** 调用 `GET /after-sales/quota/time-slots/{date}` 并传入选定日期，以加载该日期的现有配置。
    -   **打开新增弹窗：** 不调用接口，前端直接打开空状态弹窗。
    -   **保存配置：** 调用 `POST /after-sales/quota/config`，将弹窗内配置的日期和所有时段信息作为请求体发送。

## 📝 数据字典

### 状态码说明

| 状态码 | 说明             |
| :----- | :--------------- |
| 200    | 请求成功         |
| 400    | 请求参数错误     |
| 401    | 未授权，需要登录 |
| 403    | 权限不足         |
| 404    | 资源不存在       |
| 500    | 服务器内部错误   |
