# 预约限量管理 - 实体信息及实体关系分析文档

## 数据流分析

### 数据流完整性分析
预约限量管理系统的数据流经过以下主要环节：

1. **配置输入流**：门店管理人员 → 配置界面 → 配置数据 → 数据库存储
2. **配置查询流**：客户端预约请求 → AS_03接口 → 限量配置查询 → 可预约状态返回
3. **统计更新流**：预约系统 → 预约数量更新 → 限量配置统计 → 实时剩余配额
4. **自动生成流**：定时任务 → 工作日计算 → 默认配置生成 → 数据库存储

## 核心实体识别

### 1. 限量配置实体 (QuotaConfiguration)
**实体描述：** 代表门店在特定日期的预约限量配置信息
**生命周期：** 创建 → 有效 → 过期 → 历史记录
**状态变化：** 草稿 → 有效 → 过期

**实体属性：**
```typescript
interface QuotaConfig {
  id: number;                    // 配置唯一标识
  storeId: string;              // 门店ID
  configDate: string;           // 配置日期 (YYYY-MM-DD)
  timeSlotCount: number;        // 时段数量
  totalQuota: number;           // 总配额
  bookedQuantity: number;       // 已预约数量
  lastUpdateTime: string;       // 最后更新时间
  isExpired: boolean;           // 是否过期
  createdBy: string;            // 创建人
  createdTime: string;          // 创建时间
  updatedBy: string;            // 更新人
  configType: 'MANUAL' | 'AUTO'; // 配置类型：手动/自动
}
```

### 2. 时段实体 (TimeSlot)
**实体描述：** 代表单个预约时段的配置信息
**生命周期：** 创建 → 关联配置 → 使用 → 过期
**状态变化：** 可用 → 部分占用 → 满员 → 过期

**实体属性：**
```typescript
interface TimeSlot {
  id: number;                   // 时段唯一标识
  configId: number;             // 关联的配置ID
  start: string;                // 开始时间 (HH:mm)
  end: string;                  // 结束时间 (HH:mm)
  quota: number;                // 配额数量
  bookedCount: number;          // 已预约数量
  remainingQuota: number;       // 剩余配额 (计算字段)
  isAvailable: boolean;         // 是否可预约 (计算字段)
  sortOrder: number;            // 排序顺序
}
```

### 3. 门店实体 (Store)
**实体描述：** 代表提供预约服务的门店信息
**生命周期：** 创建 → 活跃 → 维护 → 关闭
**状态变化：** 正常营业 → 维护中 → 暂停服务

**实体属性：**
```typescript
interface Store {
  id: string;                   // 门店唯一标识
  name: string;                 // 门店名称
  code: string;                 // 门店编码
  address: string;              // 门店地址
  operatingHours: {             // 营业时间, 支持按门店自定义每周的工作日
    "Monday": "08:30-17:00",
    "Tuesday": "08:30-17:00",
    "Wednesday": "08:30-17:00",
    "Thursday": "08:30-17:00",
    "Friday": "08:30-17:00",
    "Saturday": "08:30-12:00",
    "Sunday": null // or not present
  };
  isActive: boolean;            // 是否活跃
  timezone: string;             // 时区
}
```

### 4. 用户实体 (User)
**实体描述：** 代表有权限操作预约限量配置的用户
**生命周期：** 创建 → 活跃 → 停用
**状态变化：** 正常 → 锁定 → 停用

**实体属性：**
```typescript
interface User {
  id: string;                   // 用户唯一标识
  username: string;             // 用户名
  displayName: string;          // 显示名称
  storeId: string;              // 所属门店ID
  role: 'STORE_MANAGER' | 'SERVICE_ADVISOR'; // 用户角色
  permissions: string[];        // 权限列表
  isActive: boolean;            // 是否活跃
  lastLoginTime: string;        // 最后登录时间
}
```

### 5. 预约单实体 (Appointment)
**实体描述：** 代表客户的预约记录，与限量配置关联
**生命周期：** 创建 → 确认 → 进行 → 完成/取消
**状态变化：** 待确认 → 已确认 → 进行中 → 已完成

**实体属性：**
```typescript
interface Appointment {
  id: string;                   // 预约唯一标识
  customerId: string;           // 客户ID
  storeId: string;              // 门店ID
  appointmentDate: string;      // 预约日期
  timeSlotId: number;           // 时段ID
  status: 'PENDING' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  createdTime: string;          // 创建时间
  confirmedTime: string;        // 确认时间
  serviceType: string;          // 服务类型
}
```

### 6. 操作日志实体 (OperationLog)
**实体描述：** 记录所有配置操作的日志信息
**生命周期：** 创建 → 存储 → 查询 → 归档
**状态变化：** 新建 → 已记录 → 已归档

**实体属性：**
```typescript
interface OperationLog {
  id: string;                   // 日志唯一标识
  operationType: 'CREATE' | 'UPDATE' | 'DELETE' | 'AUTO_GENERATE';
  entityType: 'QUOTA_CONFIG' | 'TIME_SLOT';
  entityId: string;             // 操作的实体ID
  userId: string;               // 操作用户ID
  storeId: string;              // 门店ID
  operationTime: string;        // 操作时间
  operationDetails: string;     // 操作详情
  beforeData: object;           // 操作前数据
  afterData: object;            // 操作后数据
}
```

## 实体关系分析

### 1. 一对多关系

#### 门店 → 限量配置 (1:N)
- **关系描述：** 一个门店可以有多个日期的限量配置
- **外键：** QuotaConfig.storeId → Store.id
- **业务规则：** 每个门店的每个日期只能有一个配置
- **级联操作：** 门店删除时，相关配置需要处理

#### 限量配置 → 时段 (1:N)
- **关系描述：** 一个限量配置包含多个时段设置
- **外键：** TimeSlot.configId → QuotaConfig.id
- **业务规则：** 时段不能重叠，必须在营业时间内
- **级联操作：** 配置删除时，相关时段一并删除



#### 时段 → 预约单 (1:N)
- **关系描述：** 一个时段可以接受多个预约（在配额范围内）
- **外键：** Appointment.timeSlotId → TimeSlot.id
- **业务规则：** 预约数量不能超过时段配额
- **级联操作：** 时段删除时，需要处理相关预约

### 2. 多对多关系

#### 用户 → 操作日志 (N:M)
- **关系描述：** 用户可以产生多个操作日志，日志可以关联多个用户（审批场景）
- **中间表：** 直接通过OperationLog.userId关联
- **业务规则：** 所有操作都必须记录日志

### 3. 实体关系图

```mermaid
erDiagram
    Store ||--o{ QuotaConfig : "has"
    Store ||--o{ User : "employs"
    Store ||--o{ Appointment : "receives"
    
    QuotaConfig ||--o{ TimeSlot : "contains"
    QuotaConfig ||--o{ OperationLog : "generates"
    
    TimeSlot ||--o{ Appointment : "accepts"
    
    User ||--o{ OperationLog : "performs"
    User ||--o{ QuotaConfig : "creates/updates"
    
    Store {
        string id PK
        string name
        string code
        string address
        object operatingHours
        boolean isActive
        string timezone
    }
    
    QuotaConfig {
        number id PK
        string storeId FK
        string configDate
        number timeSlotCount
        number totalQuota
        number bookedQuantity
        string lastUpdateTime
        boolean isExpired
        string createdBy
        string configType
    }
    
    TimeSlot {
        number id PK
        number configId FK
        string start
        string end
        number quota
        number bookedCount
        number sortOrder
    }
    
    User {
        string id PK
        string username
        string displayName
        string storeId FK
        string role
        array permissions
        boolean isActive
    }
    
    Appointment {
        string id PK
        string customerId
        string storeId FK
        string appointmentDate
        number timeSlotId FK
        string status
        string createdTime
        string serviceType
    }
    
    OperationLog {
        string id PK
        string operationType
        string entityType
        string entityId
        string userId FK
        string storeId FK
        string operationTime
        string operationDetails
    }
```

## 数据约束和完整性

### 1. 主键约束
- 所有实体都有唯一主键
- 复合主键：(storeId, configDate) 在QuotaConfig中确保唯一性

### 2. 外键约束
- 所有外键引用都必须存在
- 级联删除策略明确定义

### 3. 业务约束
- 时段不能重叠：同一配置下的时段时间不能重叠
- 配额限制：预约数量不能超过配额
- 时间有效性：结束时间必须晚于开始时间
- 日期有效性：配置日期不能是过去日期

### 4. 数据完整性规则
- 必填字段：所有核心字段都不能为空
- 数据格式：时间、日期格式必须标准化
- 数值范围：配额、数量等必须为正数

## 计算字段和派生数据

### 1. 剩余配额计算
```typescript
remainingQuota = quota - bookedCount
```

### 2. 可预约状态计算
```typescript
isAvailable = remainingQuota > 0 && configDate >= currentDate
```

### 3. 配置过期状态
```typescript
isExpired = configDate < currentDate
```

### 4. 总配额统计
```typescript
totalQuota = sum(timeSlots.map(slot => slot.quota))
```

## 数据生命周期管理

### 1. 配置数据
- **创建**：用户手动创建或系统自动生成
- **更新**：仅允许未过期配置更新
- **删除**：软删除，保留历史记录
- **归档**：定期归档过期数据

### 2. 日志数据
- **创建**：所有操作都生成日志
- **保留**：按策略保留（如6个月）
- **归档**：定期归档到历史库
- **清理**：按策略清理过期日志

### 3. 统计数据
- **更新**：实时更新预约统计
- **同步**：定期同步确保数据一致性
- **备份**：定期备份统计数据 