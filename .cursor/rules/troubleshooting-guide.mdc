---
description: 
globs: 
alwaysApply: false
---
# DMS 前端故障排除指南

## 常见错误及解决方案

### 1. TypeScript 声明文件错误
**错误信息**: `Could not find a declaration file for module 'element-plus/dist/locale/zh-cn.mjs'`

**解决方案**:
1. 在 [types/element-plus.d.ts](mdc:src/types/element-plus.d.ts) 中添加声明
2. 确保 tsconfig.json 包含 types 目录

### 2. Vue Router 路由警告
**错误信息**: `[Vue Router warn]: No match found for location with path "/home"`

**解决方案**:
1. 检查 [router/index.ts](mdc:src/router/index.ts) 路由配置
2. 确保访问的路径与定义的路由匹配
3. 使用正确的路径导航（如 `/` 而不是 `/home`）

### 3. 页面空白问题
**症状**: 页面完全空白，无任何内容显示

**排查步骤**:
1. 检查浏览器控制台是否有 JavaScript 错误
2. 确认 [main.ts](mdc:src/main.ts) 中所有插件都正确安装
3. 验证 Vue I18n 是否正确初始化：`setupI18n(app)`
4. 检查路由配置是否正确

### 4. 国际化键值缺失警告
**错误信息**: `[intlify] Not found 'common.edit' key in 'en' locale messages`

**解决方案**:
1. 在 [en.json](mdc:src/locales/en.json) 中添加缺失的键
2. 在 [zh.json](mdc:src/locales/zh.json) 中添加对应的中文翻译
3. 确保两个语言包的键结构保持一致

### 5. Element Plus 组件无法解析
**错误信息**: `[Vue warn]: Failed to resolve component: el-date-picker`

**解决方案**:
1. 确认在 [main.ts](mdc:src/main.ts) 中导入并使用 Element Plus：
   ```typescript
   import ElementPlus from 'element-plus'
   import 'element-plus/dist/index.css'
   app.use(ElementPlus)
   ```
2. 检查 Element Plus 样式是否正确导入

### 6. SCSS 变量未定义
**错误信息**: `Undefined variable: $primary-color`

**解决方案**:
1. 确保在 [_variables.scss](mdc:src/assets/styles/_variables.scss) 中定义了变量
2. 在使用变量的文件中正确导入变量文件
3. 检查 Vite 配置中的 SCSS 预处理器设置

### 7. API 请求失败
**症状**: 网络请求返回 404 或其他错误

**排查步骤**:
1. 检查环境变量 `VITE_USE_MOCK` 设置
2. 如果使用 Mock，确认 [mock/data/](mdc:src/mock/data) 中有对应数据
3. 检查 [api/index.ts](mdc:src/api/index.ts) 中的 baseURL 配置
4. 验证 API 模块中的请求路径是否正确

### 8. Element Plus 或国际化工具未导入
**错误信息**: `Uncaught ReferenceError: ElMessage is not defined` 或类似 `xxx is not defined`

**解决方案**:
1. 检查报错文件，确认 `Element Plus` 组件（如 `ElMessage`, `ElNotification` 等）或国际化实例 (`i18nGlobal`) 是否已在文件顶部显式导入。
2. 示例导入：
   ```typescript
   import { ElMessage } from 'element-plus';
   import { i18nGlobal } from '@/plugins/i18n';
   ```
3. 确保导入路径正确。

### 9. Element Plus 按钮布局不生效
**症状**: 尝试对 `el-form-item` 内的按钮进行对齐（如 `text-align: right` 或 `display: flex; justify-content: flex-end;`）但未生效，按钮未按预期位置显示。

**问题原因**: `Element Plus` 的 `el-form-item` 组件有其默认的复杂布局和样式，可能会干扰内部元素的精确对齐，即使应用了正确的 CSS 属性也可能无法达到预期效果。

**解决方案**:
1. **移除 `el-form-item` 包装**: 将按钮（或需要精确控制布局的元素）从 `el-form-item` 中移除，直接放置在可以控制其布局的容器中（例如 `el-col`）。
2. **直接对容器应用样式**: 对包含按钮的 `el-col` 元素应用布局样式。例如，要实现按钮在全宽行中右对齐：
   - 确保 `el-col` 占据所需宽度（例如 `span="24"` 表示100%宽度）。
   - 在 `el-col` 上直接添加 `text-align: right;` 属性。这将使其内部的行内级或行内块级子元素（如 `el-button`）在容器内右对齐。

   ```html
   <el-col :span="24" class="buttons-right-aligned">
     <el-button type="primary">查询</el-button>
     <el-button>重置</el-button>
   </el-col>
   ```
   ```scss
   .buttons-right-aligned {
     text-align: right;
   }
   ```

### 10. 页面宽度或布局异常
**症状**: 页面宽度不按预期扩展、被限制在部分区域，或布局出现意外挤压/错位。

**根本原因**:
1.  **全局 CSS 限制**: `src/assets/main.css` 或 `src/assets/base.css` 等全局样式文件可能对 `html`, `body`, `#app` 等根元素设置了 `max-width`、`width`、`display: flex` 或 `display: grid` 并指定了列宽（如 `grid-template-columns`），这些样式会限制整个应用的渲染区域。
2.  **CSS 优先级与继承**: 组件内部样式可能被更高优先级的全局样式覆盖，导致局部修改不生效。
3.  **Element Plus 网格/弹性盒误用**: `el-row` 和 `el-col` 的 `span` 值不当，或嵌套层级问题可能导致内部元素布局异常。
4.  **不正确的 CSS 注释**: 在 CSS 文件中使用 `//` 进行注释可能导致解析错误。

**排查步骤与解决方案**:
1.  **检查全局 CSS**: 优先检查 `src/assets/main.css` 和 `src/assets/base.css`。
    *   定位 `@media` 媒体查询内部对 `body` 或 `#app` 的样式定义，特别是 `display: flex`、`place-items`、`display: grid` 和 `grid-template-columns` 等属性。
    *   移除或调整这些可能限制宽度的属性，确保 `body` 和 `#app` 能够充分扩展其容器。
    *   例如，移除 `@media (min-width: 1024px)` 中 `body` 的 `display: flex; place-items: center;` 和 `#app` 的 `grid-template-columns`。
2.  **组件容器设置**: 在组件的根容器（如 `.page-container`）中，设置 `max-width` 和 `margin: 0 auto;`，以在全局限制解除后，控制页面的最大宽度并使其居中。
3.  **Element Plus 组件宽度**: 确认 `el-card` 等块级组件的 `width` 属性是否已设置为 `100%` 或允许其自适应。通常，它们默认会占据 100% 宽度，无需额外设置。
4.  **CSS 注释规范**: 确保 CSS 文件中所有注释都使用 `/* ... */` 语法，避免使用 `//`。

**预防措施**:
*   **自上而下理解布局**: 在修改特定组件样式前，先理解其父级乃至全局样式对布局的影响。
*   **多维度调试**: 结合浏览器开发者工具（检查 Computed Styles 和 Layout）进行调试。
*   **遵守 CSS 规范**: 使用标准的 CSS 注释语法和最佳实践。
*   **测试不同屏幕尺寸**: 在开发过程中，经常在不同屏幕尺寸下测试页面布局，确保响应式效果。

## 调试工具和技巧

### 1. 浏览器开发者工具
- **Console**: 查看 JavaScript 错误和警告
- **Network**: 监控 API 请求状态
- **Vue DevTools**: 检查 Vue 组件状态和数据

### 2. 开发服务器重启
修改以下文件后建议重启开发服务器：
- [main.ts](mdc:src/main.ts)
- [vite.config.ts](mdc:vite.config.ts)
- [tsconfig.json](mdc:tsconfig.json)
- 环境变量文件

重启命令：
```bash
# 停止服务器 (Ctrl+C)
# 清除缓存
rm -rf node_modules/.vite
# 重新启动
npm run dev
```

### 3. 强制刷新浏览器
当遇到缓存问题时：
- **Windows/Linux**: Ctrl + Shift + R
- **macOS**: Cmd + Shift + R

### 4. 检查依赖版本
确保关键依赖版本兼容：
```bash
npm list vue
npm list element-plus
npm list vue-i18n
```

## 预防措施

### 1. 代码提交前检查
- 运行 `npm run build` 确保构建成功
- 检查控制台无错误和警告
- 验证所有功能正常工作

### 2. 国际化最佳实践
- 新增文本时同时更新中英文语言包
- 使用有意义的键名，避免重复
- 定期检查是否有未使用的翻译键

### 3. TypeScript 类型安全
- 为 API 响应定义明确的接口类型
- 使用 TypeScript 严格模式
- 及时修复类型错误，不要使用 `any`

### 4. 组件开发规范
- 使用 Composition API 和 `<script setup>`
- 合理使用 Props 和 Emits 类型定义
- 遵循 Vue 3 最佳实践

### 5. Element Plus 表单布局最佳实践
- **标题上方，输入框下方**: 在 `<el-form>` 标签上添加 `label-position="top"` 属性，确保表单项的标签显示在输入框的上方。
  ```html
  <el-form :model="formData" label-position="top">
    <!-- 表单项 -->
  </el-form>
  ```
- **多列布局**: 
  - 移除 `<el-form>` 上的 `:inline="true"` 属性。
  - 在 `<el-form>` 内部使用 `<el-row :gutter="20">` 包裹表单项。
  - 将每个 `<el-form-item>` 包裹在 `<el-col :span="X">` 中，其中 `X` 可以是 24（单列）、12（两列）、8（三列）、6（四列）等，根据需要调整列宽。
  ```html
  <el-form :model="searchParams" class="search-form" label-position="top">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="车辆识别码">
          <el-input v-model="searchParams.vin" placeholder="请输入车辆识别码" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="车型">
          <el-input v-model="searchParams.model" placeholder="请输入车型" clearable />
        </el-form-item>
      </el-col>
      <!-- 更多列 -->
      <el-col :span="24">
        <el-form-item>
          <el-button type="primary">查询</el-button>
          <el-button>重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  ```
