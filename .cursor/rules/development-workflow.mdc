# DMS 前端开发工作流程

## 项目启动流程

### 1. 环境准备
确保安装了以下依赖：
- Node.js (推荐 18+)
- npm 或 yarn
- Git

### 2. 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd dms-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 3. 环境变量配置
创建 `.env.local` 文件：
```
VITE_USE_MOCK=true
VITE_API_BASE_URL=http://localhost:3000/api
```

## 新功能开发流程

### 1. 创建新页面
1. 在 `src/views/` 下创建页面组件
2. 在 [router/index.ts](mdc:src/router/index.ts) 中添加路由配置
3. 更新导航菜单（如果需要）

### 2. 添加新的 API 接口
1. 在 [types/module.d.ts](mdc:src/types/module.d.ts) 中定义接口类型
2. 在 `src/api/modules/` 下创建或更新 API 模块
3. 如果使用 Mock，在 `src/mock/data/` 中添加测试数据

### 3. 国际化文本添加
1. 在 [en.json](mdc:src/locales/en.json) 中添加英文文本
2. 在 [zh.json](mdc:src/locales/zh.json) 中添加中文文本
3. 在组件中使用 `$t('key')` 或 `t('key')` 引用

### 4. 样式开发
1. 全局样式：修改 [styles/index.scss](mdc:src/assets/styles/index.scss)
2. 变量定义：在 [_variables.scss](mdc:src/assets/styles/_variables.scss) 中添加
3. 混合宏：在 [_mixins.scss](mdc:src/assets/styles/_mixins.scss) 中定义
4. 组件样式：使用 `<style scoped lang="scss">`

## 代码规范

### 1. Vue 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

// 国际化
const { t } = useI18n()

// 响应式数据
const loading = ref(false)
const formData = reactive({})

// 方法定义
const handleSubmit = () => {
  // 处理逻辑
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped lang="scss">
// 组件样式
</style>
```

### 2. TypeScript 类型定义
```typescript
// 接口定义
interface UserInfo {
  id: number
  name: string
  email: string
}

// API 响应类型
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}
```

### 3. API 调用规范
```typescript
// 在组件中使用 API
import { getUserList } from '@/api/modules/user'

const fetchData = async () => {
  try {
    loading.value = true
    const response = await getUserList(params)
    // 处理响应数据
  } catch (error) {
    // 错误处理
  } finally {
    loading.value = false
  }
}
```

## 测试和调试

### 1. 开发环境测试
- 启动开发服务器：`npm run dev`
- 检查浏览器控制台无错误
- 验证功能正常工作
- 测试不同语言切换

### 2. 构建测试
```bash
# 构建项目
npm run build

# 预览构建结果
npm run preview
```

### 3. 代码质量检查
```bash
# TypeScript 类型检查
npm run type-check

# 代码格式化
npm run format

# 代码检查
npm run lint
```

## 部署流程

### 1. 生产环境配置
创建 `.env.production` 文件：
```
VITE_USE_MOCK=false
VITE_API_BASE_URL=https://api.example.com
```

### 2. 构建和部署
```bash
# 生产构建
npm run build

# 构建产物在 dist/ 目录
# 将 dist/ 目录内容部署到服务器
```

## 版本控制

### 1. Git 工作流
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交代码
git add .
git commit -m "feat: add new feature"

# 推送分支
git push origin feature/new-feature

# 创建 Pull Request
```

### 2. 提交信息规范
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 性能优化

### 1. 代码分割
- 使用动态导入：`const Component = () => import('./Component.vue')`
- 路由懒加载：`component: () => import('@/views/Page.vue')`

### 2. 资源优化
- 图片压缩和格式优化
- 使用 CDN 加载第三方库
- 启用 gzip 压缩

### 3. 缓存策略
- 合理设置浏览器缓存
- 使用 Service Worker（如果需要）
- API 响应缓存

## 监控和维护

### 1. 错误监控
- 集成错误监控服务（如 Sentry）
- 监控 API 请求失败率
- 关注用户体验指标

### 2. 性能监控
- 页面加载时间
- 首屏渲染时间
- 交互响应时间

### 3. 定期维护
- 依赖包更新
- 安全漏洞修复
- 代码重构和优化
