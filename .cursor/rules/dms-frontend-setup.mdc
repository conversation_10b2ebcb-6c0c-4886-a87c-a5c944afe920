# DMS 前端项目开发规则

## 项目概述
这是一个基于 Vue 3 + TypeScript + Element Plus + Vue I18n + SCSS 的 DMS（经销商管理系统）前端项目。

# DMS 前端项目开发规则

## 项目概述
这是一个基于 Vue 3 + TypeScript + Element Plus + Vue I18n + SCSS 的 DMS（经销商管理系统）前端项目。

## 技术栈
- **框架**: Vue 3 + TypeScript
- **UI 组件库**: Element Plus
- **国际化**: Vue I18n
- **样式**: SCSS
- **HTTP 客户端**: Axios
- **路由**: Vue Router
- **构建工具**: Vite

## 核心配置文件

### 主入口文件
主应用入口是 [main.ts](mdc:src/main.ts)，所有核心插件都必须在此文件中正确注册：
- Element Plus 组件库及样式
- Vue I18n 国际化插件
- Vue Router 路由
- 全局样式

### 国际化配置
国际化配置位于 [i18n.ts](mdc:src/plugins/i18n.ts)，包含：
- Vue I18n 实例配置
- Element Plus 语言包集成
- 浏览器语言检测
- localStorage 语言存储

语言包文件：
- [en.json](mdc:src/locales/en.json) - 英文翻译
- [zh.json](mdc:src/locales/zh.json) - 中文翻译

### API 封装
API 配置位于 [api/index.ts](mdc:src/api/index.ts)，包含：
- Axios 实例配置
- 请求/响应拦截器
- 统一错误处理
- 国际化错误消息

### 样式系统
全局样式入口是 [styles/index.scss](mdc:src/assets/styles/index.scss)，包含：
- [_variables.scss](mdc:src/assets/styles/_variables.scss) - SCSS 变量
- [_mixins.scss](mdc:src/assets/styles/_mixins.scss) - SCSS 混合宏

## 开发规范

### 1. 新增插件或组件库
当添加新的 Vue 插件或组件库时，必须在 [main.ts](mdc:src/main.ts) 中正确注册：
```typescript
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { setupI18n } from './plugins/i18n'

const app = createApp(App)
app.use(ElementPlus)
setupI18n(app)
```

### 2. 国际化文本
所有用户界面文本都必须使用国际化：
- 在组件中使用 `$t('key')` 或 `t('key')`
- 确保所有使用的键都在语言包中存在
- 新增文本时同时更新中英文语言包

### 3. TypeScript 声明
第三方模块缺少声明文件时，在 [types/](mdc:src/types/) 目录下创建声明文件：
- [module.d.ts](mdc:src/types/module.d.ts) - 业务类型定义
- [element-plus.d.ts](mdc:src/types/element-plus.d.ts) - Element Plus 声明

### 4. API 开发
新增 API 模块时：
- 在 [api/modules/](mdc:src/api/modules/) 下创建模块文件
- 根据 `VITE_USE_MOCK` 环境变量决定使用 Mock 或真实 API
- Mock 数据放在 [mock/data/](mdc:src/mock/data/) 目录

### 5. UI/UX 设计规范
- **弹窗表单布局**: 为保持现代感和良好用户体验，弹窗内的表单推荐采用单列布局，且标签文字应位于输入框的上方 (`label-position="top"`)。例如：
  ```vue
  <el-dialog width="500px" class="modern-dialog">
    <el-form :model="formData" label-position="top">
      <el-form-item label="字段名称">
        <el-input v-model="formData.field"></el-input>
      </el-form-item>
      <!-- 更多表单项 -->
    </el-form>
  </el-dialog>
  ```
- **间距**: 确保页面各区域、表单项之间有足够的垂直和水平间距，避免内容过于紧凑。

## 常见问题解决方案

### 1. 控制台国际化警告
如果出现 `[intlify] Not found 'xxx' key` 警告：
- 检查语言包文件是否包含对应的键
- 确保中英文语言包的键保持一致

### 2. Element Plus 组件无法解析
如果出现 `Failed to resolve component: el-xxx` 错误：
- 确认 Element Plus 已在 [main.ts](mdc:src/main.ts) 中全局注册
- 检查是否导入了 Element Plus 样式文件

### 3. 路由警告
如果出现路由匹配警告：
- 检查 [router/index.ts](mdc:src/router/index.ts) 中的路由配置
- 确保访问的路径与配置的路由匹配

### 4. 页面空白
如果页面完全空白：
- 检查浏览器控制台错误信息
- 确认所有插件都在 [main.ts](mdc:src/main.ts) 中正确安装
- 检查是否有 JavaScript 运行时错误

## 调试技巧

### 1. 开发服务器重启
修改核心配置文件后，需要：
- 停止开发服务器 (Ctrl+C)
- 清除缓存：`rm -rf node_modules/.vite`
- 重新启动：`npm run dev`

### 2. 强制刷新
浏览器缓存问题时使用强制刷新：
- Windows/Linux: Ctrl + Shift + R
- macOS: Cmd + Shift + R

### 3. 控制台调试
始终关注浏览器控制台的：
- 错误信息（红色）
- 警告信息（黄色）
- 网络请求状态

## 项目结构
```
src/
├── api/                 # API 封装
│   ├── index.ts        # Axios 配置
│   └── modules/        # 业务 API 模块
├── assets/             # 静态资源
│   └── styles/         # 全局样式
├── components/         # 公共组件
├── locales/           # 国际化语言包
├── mock/              # Mock 数据
├── plugins/           # Vue 插件配置
├── router/            # 路由配置
├── types/             # TypeScript 声明
├── views/             # 页面组件
└── main.ts            # 应用入口
```

## 环境变量
- `VITE_USE_MOCK`: 是否使用 Mock 数据（true/false）
- `VITE_API_BASE_URL`: API 基础地址
