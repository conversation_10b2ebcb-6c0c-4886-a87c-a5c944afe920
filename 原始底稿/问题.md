vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.status.submitted' key in 'zh' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Fall back to translate 'parts.status.submitted' key with 'en' locale.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.status.submitted' key in 'en' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.approve' key in 'zh' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Fall back to translate 'parts.approve' key with 'en' locale.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.approve' key in 'en' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.status.received' key in 'zh' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Fall back to translate 'parts.status.received' key with 'en' locale.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.status.received' key in 'en' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.approve' key in 'zh' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Fall back to translate 'parts.approve' key with 'en' locale.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.approve' key in 'en' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.status.voided' key in 'zh' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Fall back to translate 'parts.status.voided' key with 'en' locale.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.status.voided' key in 'en' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.approve' key in 'zh' locale messages.
vue-i18n.js?v=be6d61fa:121 [intlify] Fall back to translate 'parts.approve' key with 'en' locale.
vue-i18n.js?v=be6d61fa:121 [intlify] Not found 'parts.approve' key in 'en' locale messages.
﻿

