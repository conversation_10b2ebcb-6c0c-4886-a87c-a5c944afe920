### 数据库设计规范 (V2.0 - 公司标准版)

本规范旨在为项目提供一套统一的、高效的数据库设计标准，以保证数据的一致性、可维护性和高性能。**本文档根据《数据库语句生成规则》进行编写。**

#### 1. 命名规范
- **表命名**：
  - **格式**: 小写字母 + 下划线。
  - **命名**: 表类型前缀 + 模块名。
  - **业务表**: `tt_` 开头，例如: `tt_user_info`, `tt_order_detail`。
  - **字典表**: `tc_` 开头，例如: `tc_dict_type`, `tc_status_config`。
  - **关联表**: `tr_` 开头，例如: `tr_user_role`, `tr_order_product`。
- **字段命名**：
  - **格式**: 小写字母 + 下划线, 例如: `user_name`。
  - **主键**: 统一命名为 `id`。
  - **外键**: 命名为 `{关联表名}_id`，例如 `user_id`。
- **索引命名**：
  - **普通索引**: `idx_{字段名}`。
  - **唯一索引**: `uk_{字段名}`。

#### 2. 必需字段规范
以下字段为所有表的**必须包含**字段：

| 字段名 | 数据类型 | 默认值 | 注释 |
|---|---|---|---|
| `id` | `BIGINT` | `AUTO_INCREMENT` | 主键ID |
| `is_deleted` | `TINYINT(1)` | `0` | 是否删除 (0:未删除, 1:已删除) |
| `created_by` | `VARCHAR(36)` | `NULL` | 创建人ID |
| `created_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP` | 创建时间 |
| `updated_by` | `VARCHAR(36)` | `NULL` | 更新人ID |
| `updated_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 更新时间 |

#### 3. 数据类型选择
- **整数**：`BIGINT` 用于主键；`INT` 用于一般整数；`TINYINT` 用于布尔值、状态值。
- **字符串**：优先使用 `VARCHAR`。长文本使用 `TEXT`。固定长度（如状态码）可使用 `CHAR`。
- **时间**：**推荐使用 `TIMESTAMP`**。根据业务需要可选用 `DATETIME` 或 `DATE`。
- **精确数值**：涉及金额、价格等，**必须**使用 `DECIMAL`。

#### 4. 索引设计原则
- **查询驱动**：为需要模糊搜索、排序、关联查询以及`WHERE`条件中经常使用的字段建立索引。
- **主键索引**：自动创建。
- **唯一索引**：用于保证业务逻辑上的唯一性字段，使用`UNIQUE KEY`。
- **联合索引**：遵循"最左前缀"原则。
- **数量限制**：索引数量建议不超过5个，特殊情况另行讨论。

#### 5. 约束与关联设计
- **外键约束**：**不使用**物理外键约束。通过业务逻辑保证数据完整性。
- **非空约束**：必填字段使用 `NOT NULL`。
- **默认值**：状态字段和时间字段应设置合理的默认值。
- **关联字段**：在字段注释中必须清晰说明其关联关系，例如：`COMMENT '用户ID，关联tt_user_info.id'`。

#### 6. 表创建模板
##### 业务表模板 (`tt_`)
```sql
CREATE TABLE `tt_表名` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  -- 业务字段
  `字段名` VARCHAR(100) NOT NULL COMMENT '字段说明',
  -- 必需字段
  `is_deleted` TINYINT(1) DEFAULT '0' COMMENT '是否删除',
  `created_by` VARCHAR(36) DEFAULT NULL COMMENT '创建人ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` VARCHAR(36) DEFAULT NULL COMMENT '更新人ID',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  -- 索引
  KEY `idx_字段名` (`字段名`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务表说明';
```
*(字典表和关联表模板请参考《数据库语句生成规则》)* 