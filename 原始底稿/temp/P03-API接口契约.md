# P03: API接口契约

## **核心资产声明**
以下接口设计严格以 `P02-领域实体模型` 为事实标准。所有接口的请求/响应结构、DTO定义和字段的`归属表`必须与P02实体定义完全一致，不得修改或演绎。

## **接口设计总览**

基于P01用户动作清单，共需设计 **12个核心接口**：

---

## 1. 查询预约列表接口

**业务动作**: 查询预约列表（搜索、重置、分页、初始化加载）  
**接口描述**: 根据多维度条件查询预约列表，支持分页。  
**请求方法**: `GET`  
**接口路径**: `/api/v1/appointments`

### 请求参数

| 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 | 备注 |
|:---|:---|:---|:---|:---|:---|
| `appointmentNumber` | `String` | `false` | `Appointment` | 预约单号 | 模糊匹配 |
| `licensePlate` | `String` | `false` | `Vehicle` | 车牌号 | 模糊匹配 |
| `reservationPhone` | `String` | `false` | `Customer` | 预约人手机号 | 模糊匹配 |
| `servicePhone` | `String` | `false` | `Customer` | 送修人手机号 | 模糊匹配 |
| `startDate` | `Date` | `false` | `Appointment` | 开始日期 | 日期范围查询 |
| `endDate` | `Date` | `false` | `Appointment` | 结束日期 | 日期范围查询 |
| `status` | `String` | `false` | `Appointment` | 预约状态 | not_arrived/arrived/no_show |
| `serviceType` | `String` | `false` | `Appointment` | 服务类型 | maintenance/repair |
| `serviceAdvisorId` | `Long` | `false` | `Staff` | 服务顾问ID | 精确匹配 |
| `page` | `Integer` | `false` | `不适用(计算字段)` | 页码 | 默认值1 |
| `size` | `Integer` | `false` | `不适用(计算字段)` | 每页大小 | 默认值10 |

### 响应DTO: `AppointmentListVO`

```json
{
  "code": 200,
  "msg": "success",
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "appointmentNumber": "APT20241215001",
        "licensePlate": "京A12345",
        "reservationContactName": "张三",
        "reservationContactPhone": "13800138000",
        "serviceContactName": "李四",
        "serviceContactPhone": "13900139000",
        "appointmentDate": "2024-12-15",
        "timeSlot": "09:00-10:00",
        "serviceType": "maintenance",
        "status": "not_arrived",
        "serviceAdvisorName": "王顾问",
        "qualityInspectionId": null,
        "createdAt": "2024-12-14T10:30:00",
        "vin": "LVVDB11B8ED123456"
      }
    ],
    "total": 50
  }
}
```

---

## 2. 查询看板预约列表接口

**业务动作**: 看板预约列表查询（状态筛选、日期切换）  
**接口描述**: 看板页面专用的预约列表查询，数据结构简化。  
**请求方法**: `GET`  
**接口路径**: `/api/v1/appointments/dashboard`

### 请求参数

| 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 | 备注 |
|:---|:---|:---|:---|:---|:---|
| `appointmentDate` | `Date` | `true` | `Appointment` | 预约日期 | 指定日期 |
| `status` | `String` | `false` | `Appointment` | 预约状态 | 状态筛选 |
| `storeId` | `Long` | `false` | `Store` | 门店ID | 门店权限 |

### 响应DTO: `DashboardAppointmentListVO`

```json
{
  "code": 200,
  "msg": "success",
  "success": true,
  "data": [
    {
      "id": 1,
      "plateNumber": "京A12345",
      "appointmentDate": "2024-12-15",
      "timeSlot": "09:00-10:00",
      "serviceType": "maintenance",
      "status": "not_arrived"
    }
  ]
}
```

### 字段归属表验证

| 响应字段名 | **归属表** | P02对应字段 | 验证结果 |
|:---|:---|:---|:---|
| `id` | `Appointment` | `id` | ✅ 一致 |
| `plateNumber` | `Vehicle` | `license_plate` | ✅ 一致 |
| `appointmentDate` | `Appointment` | `appointment_date` | ✅ 一致 |
| `timeSlot` | `Appointment` | `time_slot` | ✅ 一致 |
| `serviceType` | `Appointment` | `service_type` | ✅ 一致 |
| `status` | `Appointment` | `status` | ✅ 一致 |

---

## 3. 查询预约详情接口

**业务动作**: 查看预约详情（点击详情按钮、点击预约单号链接）  
**接口描述**: 获取单个预约单的完整详细信息。  
**请求方法**: `GET`  
**接口路径**: `/api/v1/appointments/{appointmentId}`

### 请求参数

| 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 |
|:---|:---|:---|:---|:---|
| `appointmentId` | `Long` | `true` | `Appointment` | 预约单ID |

### 响应DTO: `AppointmentDetailVO`

```json
{
  "code": 200,
  "msg": "success",
  "success": true,
  "data": {
    "id": 1,
    "appointmentNumber": "APT20241215001",
    "status": "not_arrived",
    "appointmentDate": "2024-12-15",
    "timeSlot": "09:00-10:00",
    "serviceType": "maintenance",
    "customerDescription": "车辆定期保养",
    "reservationContact": {
      "name": "张三",
      "phone": "13800138000"
    },
    "serviceContact": {
      "name": "李四", 
      "phone": "13900139000"
    },
    "vehicle": {
      "licensePlate": "京A12345",
      "vin": "LVVDB11B8ED123456",
      "model": "理想L9",
      "variant": "Max版",
      "color": "珍珠白",
      "mileage": 15000
    },
    "store": {
      "name": "北京朝阳店",
      "address": "北京市朝阳区工体北路甲6号"
    },
    "serviceAdvisor": {
      "name": "王顾问"
    },
    "maintenancePackage": {
      "packageCode": "PKG001",
      "name": "15000公里保养套餐",
      "totalAmount": 1500.00,
      "items": [
        {
          "itemCode": "OIL001",
          "name": "机油更换",
          "quantity": 1,
          "price": 500.00
        }
      ]
    },
    "paymentStatus": "paid",
    "paymentAmount": 1500.00,
    "paymentOrderNumber": "PAY20241215001",
    "createdAt": "2024-12-14T10:30:00"
  }
}
```

---

## 4. 查询预约统计接口

**业务动作**: 预约看板统计展示（页面初始化、日期切换）  
**接口描述**: 获取指定日期的预约统计数据。  
**请求方法**: `GET`  
**接口路径**: `/api/v1/appointments/statistics`

### 请求参数

| 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 |
|:---|:---|:---|:---|:---|
| `statisticsDate` | `Date` | `true` | `不适用(计算字段)` | 统计日期 |
| `storeId` | `Long` | `false` | `Store` | 门店ID |

### 响应DTO: `AppointmentStatisticsVO`

```json
{
  "code": 200,
  "msg": "success",
  "success": true,
  "data": {
    "totalAppointments": 20,
    "arrivedCount": 12,
    "notArrivedCount": 6,
    "notFulfilledCount": 2,
    "arrivalRate": 60.0,
    "tomorrowCount": 15
  }
}
```

---

## 5. 创建环检单接口

**业务动作**: 创建环检单（点击创建环检单按钮，确认创建）  
**接口描述**: 基于预约单创建环检单。  
**请求方法**: `POST`  
**接口路径**: `/api/v1/appointments/{appointmentId}/quality-inspection`

### 请求参数

| 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 |
|:---|:---|:---|:---|:---|
| `appointmentId` | `Long` | `true` | `Appointment` | 预约单ID |

### 请求体: `CreateQualityInspectionForm`

```json
{
  "serviceContactName": "李四",
  "serviceContactPhone": "13900139000"
}
```

| 请求体字段 | **归属表** | P02对应字段 | 验证结果 |
|:---|:---|:---|:---|
| `serviceContactName` | `Customer` | `name` | ✅ 一致 |
| `serviceContactPhone` | `Customer` | `phone` | ✅ 一致 |

### 响应DTO: `CreateQualityInspectionVO`

```json
{
  "code": 200,
  "msg": "环检单创建成功",
  "success": true,
  "data": {
    "qualityInspectionId": "QI20241215001",
    "appointmentId": 1,
    "serviceAdvisorName": "王顾问"
  }
}
```

---

## 6. 取消预约接口

**业务动作**: 取消预约（点击取消预约按钮）  
**接口描述**: 取消指定预约单。  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/appointments/{appointmentId}/cancel`

### 请求参数

| 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 |
|:---|:---|:---|:---|:---|
| `appointmentId` | `Long` | `true` | `Appointment` | 预约单ID |

### 请求体: `CancelAppointmentForm`

```json
{
  "reason": "客户临时有事"
}
```

| 请求体字段 | **归属表** | P02对应字段 | 验证结果 |
|:---|:---|:---|:---|
| `reason` | `不适用(业务字段)` | - | ✅ 业务字段 |

### 响应DTO

```json
{
  "code": 200,
  "msg": "预约取消成功",
  "success": true,
  "data": {
    "appointmentId": 1,
    "newStatus": "cancelled",
    "cancelledAt": "2024-12-15T10:30:00"
  }
}
```

---

## 7. 更新预约状态接口

**业务动作**: 更新预约状态（手动状态更新）  
**接口描述**: 手动更新预约单状态。  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/appointments/{appointmentId}/status`

### 请求参数

| 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 |
|:---|:---|:---|:---|:---|
| `appointmentId` | `Long` | `true` | `Appointment` | 预约单ID |

### 请求体: `UpdateStatusForm`

```json
{
  "status": "arrived",
  "remark": "客户已到店"
}
```

| 请求体字段 | **归属表** | P02对应字段 | 验证结果 |
|:---|:---|:---|:---|
| `status` | `Appointment` | `status` | ✅ 一致 |
| `remark` | `不适用(业务字段)` | - | ✅ 业务字段 |

### 响应DTO

```json
{
  "code": 200,
  "msg": "状态更新成功",
  "success": true,
  "data": {
    "appointmentId": 1,
    "oldStatus": "not_arrived",
    "newStatus": "arrived",
    "updatedAt": "2024-12-15T10:30:00"
  }
}
```

---

## 8. 获取服务顾问列表接口

**业务动作**: 服务顾问下拉选择（页面初始化加载）  
**接口描述**: 获取可用的服务顾问列表。  
**请求方法**: `GET`  
**接口路径**: `/api/v1/staff/service-advisors`

### 请求参数

| 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 |
|:---|:---|:---|:---|:---|
| `storeId` | `Long` | `false` | `Store` | 门店ID |

### 响应DTO: `ServiceAdvisorListVO`

```json
{
  "code": 200,
  "msg": "success", 
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "王顾问",
      "staffCode": "SA001"
    }
  ]
}
```

### 字段归属表验证

| 响应字段名 | **归属表** | P02对应字段 | 验证结果 |
|:---|:---|:---|:---|
| `id` | `Staff` | `id` | ✅ 一致 |
| `name` | `Staff` | `name` | ✅ 一致 |
| `staffCode` | `Staff` | `staff_code` | ✅ 一致 |

---

## 9. 获取技师列表接口

**业务动作**: 技师下拉选择（页面初始化加载）  
**接口描述**: 获取可用的技师列表。  
**请求方法**: `GET`  
**接口路径**: `/api/v1/staff/technicians`

### 请求参数

| 参数名 | 类型 | 是否必须 | **归属表** | 中文语义 |
|:---|:---|:---|:---|:---|
| `storeId` | `Long` | `false` | `Store` | 门店ID |

### 响应DTO: `TechnicianListVO`

```json
{
  "code": 200,
  "msg": "success",
  "success": true, 
  "data": [
    {
      "id": 2,
      "name": "李师傅",
      "staffCode": "TECH001"
    }
  ]
}
```

---

## 10. 导出预约数据接口

**业务动作**: 导出Excel（点击导出Excel按钮）  
**接口描述**: 导出当前筛选条件下的预约数据为Excel文件。  
**请求方法**: `POST`  
**接口路径**: `/api/v1/appointments/export`

### 请求体: `ExportAppointmentForm`

```json
{
  "appointmentNumber": "",
  "licensePlate": "",
  "reservationPhone": "",
  "servicePhone": "",
  "startDate": "2024-12-15",
  "endDate": "2024-12-15",
  "status": "not_arrived",
  "serviceType": "maintenance",
  "serviceAdvisorId": null
}
```

### 字段归属表验证

| 请求体字段 | **归属表** | P02对应字段 | 验证结果 |
|:---|:---|:---|:---|
| `appointmentNumber` | `Appointment` | `appointment_number` | ✅ 一致 |
| `licensePlate` | `Vehicle` | `license_plate` | ✅ 一致 |
| `reservationPhone` | `Customer` | `phone` | ✅ 一致 |
| `servicePhone` | `Customer` | `phone` | ✅ 一致 |
| `startDate` | `Appointment` | `appointment_date` | ✅ 一致 |
| `endDate` | `Appointment` | `appointment_date` | ✅ 一致 |
| `status` | `Appointment` | `status` | ✅ 一致 |
| `serviceType` | `Appointment` | `service_type` | ✅ 一致 |
| `serviceAdvisorId` | `Staff` | `id` | ✅ 一致 |

### 响应DTO

```json
{
  "code": 200,
  "msg": "导出成功",
  "success": true,
  "data": {
    "downloadUrl": "/api/v1/files/download/appointments_20241215.xlsx",
    "fileName": "预约数据_20241215.xlsx"
  }
}
```

---

## 11. 接收APP签到信息接口

**业务动作**: APP扫码签到数据同步（系统自动化）  
**接口描述**: 接收APP端推送的客户签到信息，更新预约状态。  
**请求方法**: `POST`  
**接口路径**: `/api/v1/appointments/check-in`

### 请求体: `AppCheckInForm`

```json
{
  "appointmentNumber": "APT20241215001",
  "licensePlate": "京A12345", 
  "storeCode": "STORE001",
  "checkInTime": "2024-12-15T09:05:00",
  "deviceInfo": "iPhone 15 Pro Max"
}
```

### 请求体字段归属表验证

| 请求体字段 | **归属表** | P02对应字段 | 验证结果 |
|:---|:---|:---|:---|
| `appointmentNumber` | `Appointment` | `appointment_number` | ✅ 一致 |
| `licensePlate` | `Vehicle` | `license_plate` | ✅ 一致 |
| `storeCode` | `Store` | `store_code` | ✅ 一致 |
| `checkInTime` | `Appointment` | `check_in_time` | ✅ 一致 |
| `deviceInfo` | `Appointment` | `check_in_device` | ✅ 一致 |

### 响应DTO

```json
{
  "code": 200,
  "msg": "签到成功",
  "success": true,
  "data": {
    "appointmentId": 1,
    "newStatus": "arrived",
    "checkInTime": "2024-12-15T09:05:00"
  }
}
```

---

## 12. 预约状态自动维护接口

**业务动作**: 预约状态自动维护（系统自动化）  
**接口描述**: 系统定时任务自动检查并维护预约状态，处理超时预约。  
**请求方法**: `POST`  
**接口路径**: `/api/v1/appointments/auto-maintenance`

### 请求体: `AutoMaintenanceForm`

```json
{
  "currentTime": "2024-12-15T18:00:00",
  "batchSize": 100,
  "storeId": null
}
```

### 请求体字段归属表验证

| 请求体字段 | **归属表** | P02对应字段 | 验证结果 |
|:---|:---|:---|:---|
| `currentTime` | `不适用(系统字段)` | - | ✅ 系统时间 |
| `batchSize` | `不适用(系统字段)` | - | ✅ 批处理大小 |
| `storeId` | `Store` | `id` | ✅ 一致 |

### 响应DTO: `AutoMaintenanceVO`

```json
{
  "code": 200,
  "msg": "自动维护完成",
  "success": true,
  "data": {
    "processedCount": 15,
    "updatedToNoShow": 8,
    "updatedToOther": 7,
    "executionTime": "2024-12-15T18:00:00",
    "summary": [
      {
        "appointmentId": 1,
        "oldStatus": "not_arrived",
        "newStatus": "no_show",
        "reason": "预约时间已超时2小时"
      }
    ]
  }
}
```

---

## **交叉验证总结**

### ✅ **完整性验证**
- P01中的19个用户动作 → 对应12个API接口，全部覆盖
- P01中的所有数据元素 → 在对应接口的DTO中全部体现

### ✅ **一致性验证** 
- 所有接口DTO结构严格遵循P02实体定义
- 所有字段的`归属表`列均可在P02中找到精确对应
- 所有关联关系都通过外键ID正确表达

### ✅ **规范符合性验证**
- 所有接口遵循RESTful设计规范
- 统一使用`ResultData<T>`响应结构
- 分页接口使用`PageResult<T>`数据结构
- HTTP方法语义正确（GET查询、POST创建、PUT更新）

### ✅ **业务逻辑验证**
- 接口设计完全支持P01中定义的所有用户动作
- 数据流转逻辑与业务场景完全匹配
- 状态变更和数据同步需求得到满足

### **12个接口分类汇总**：

1. **查询类接口（4个）**：预约列表、看板列表、预约详情、预约统计
2. **操作类接口（5个）**：创建环检单、取消预约、更新状态、APP签到、预约状态自动维护
3. **基础数据接口（2个）**：服务顾问列表、技师列表  
4. **功能性接口（1个）**：导出数据

---

**P03: API接口契约** 已完成交付，所有12个接口设计以P02为唯一事实来源，确保了数据结构的完整一致性。 