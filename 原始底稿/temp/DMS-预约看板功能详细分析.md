**售后业务流程：DMS门店预约看板功能详细分析输出 (最终整合版)**

本文档详细分析DMS门店预约看板业务功能，为门店人员提供预约信息的实时监控、状态管理和服务安排的核心工具。该功能是售后预约阶段的重要组成部分，旨在为门店层面提供预约履约管理、资源调配决策和客户服务支持，其数据来源于DMS预约单管理系统。

## 角色职责分工说明

### 门店接待人员 (Store Reception Staff)
- **主要职责**：客户到店第一线接待，负责引导客户使用APP完成到店签到
- **操作系统**：主要协助客户使用APP进行签到操作
- **核心业务**：接待客户、引导扫码签到、确认客户身份、协助处理签到异常

### 门店服务顾问 (Store Service Advisor) 
- **主要职责**：基于DMS预约看板监控预约状态，安排后续服务工作
- **操作系统**：主要使用DMS预约看板系统
- **核心业务**：查看预约概览、监控客户到店状态、根据预约状态安排服务、跟踪预约进度

### 门店经理 (Store Manager)
- **主要职责**：整体预约业务监控管理，进行业务分析和决策
- **操作系统**：主要使用DMS预约看板系统
- **核心业务**：监控门店整体预约情况、分析服务类型分布、导出数据进行业务分析、质量监控

## 1. 用户旅程：不同角色参与的业务动作

### 门店服务顾问用户旅程 (Store Service Advisor User Journey)

*角色定位：门店服务顾问主要负责在DMS系统中监控预约情况，安排后续服务工作，不直接参与客户签到操作。*

**场景1: 查看当日预约概览**

*   **Given** 门店服务顾问上班后需要了解当日预约情况。
*   **When** 服务顾问登录DMS系统进入预约看板页面，查看页面顶部统计卡片区域。
*   **Then** 系统展示当日预约总数、已到店数、未到店数、到店率等关键指标；服务顾问可以快速了解当日工作量和进度。

**场景2: 按状态查看预约列表**

*   **Given** 服务顾问需要了解不同状态的预约情况。
*   **When** 服务顾问点击页面左侧的"未到店"、"已到店"、"未履约"状态按钮。
*   **Then** 系统按选中状态过滤展示预约列表，包含车牌号、预约日期、预约时间段、维修类型、预约状态等关键信息；服务顾问可以查看不同状态下的预约情况并安排相应工作。

**场景3: 监控客户到店状态**

*   **Given** 客户已到店并完成签到，服务顾问需要了解客户到店情况。
*   **When** 服务顾问点击"已到店"状态按钮查看已到店的预约列表。
*   **Then** 系统显示所有已到店的预约，状态显示为"已到店"；服务顾问可以根据预约时间段和车牌号安排后续的服务工作。

**场景4: 查看明日预约准备**

*   **Given** 服务顾问需要查看明日预约情况以便提前准备。
*   **When** 服务顾问点击页面右侧的"明天预约"按钮。
*   **Then** 系统切换显示明日预约数据，包括统计数据和预约列表；服务顾问可以了解明日工作安排，默认显示"未到店"状态的预约。

### 门店接待人员用户旅程 (Store Reception Staff User Journey)

*角色定位：门店接待人员主要负责客户到店接待和引导客户使用APP完成到店签到，是客户到店流程的第一线服务人员。*

**场景1: 引导客户APP扫码签到**

*   **Given** 客户到店需要完成预约签到。
*   **When** 门店接待人员引导客户使用APP扫描门店二维码进行签到操作。
*   **Then** 客户通过APP扫码完成签到，系统自动将预约状态更新为"已到店"并同步至DMS系统；客户完成签到流程，可以等待服务安排。

**场景2: 协助客户处理签到问题**

*   **Given** 客户到店但在签到过程中遇到问题（如非本人到店、APP操作困难等）。
*   **When** 接待人员协助客户在APP中输入车牌号或其他验证信息完成签到。
*   **Then** 系统完成身份验证后更新预约状态，接待人员确认客户完成签到流程。

**场景3: 处理异常签到情况**

*   **Given** 客户到店但预约信息存在问题（如时间不符、门店不符等）。
*   **When** 接待人员发现客户预约信息异常，无法正常完成签到。
*   **Then** 接待人员需要联系相关人员处理异常情况，或引导客户重新预约。

### 门店经理用户旅程 (Store Manager User Journey)

*角色定位：门店经理主要负责整体预约业务的监控管理，通过DMS预约看板进行业务分析和决策。*

**场景1: 监控门店预约完成情况**

*   **Given** 门店经理需要监控当日预约履约情况。
*   **When** 门店经理查看预约看板的统计卡片，重点关注到店率指标和各状态按钮的数量统计。
*   **Then** 系统展示实时的到店率数据和各状态预约数量；门店经理可以评估客户履约情况，必要时进行客户回访或服务调整。

**场景2: 分析服务类型分布**

*   **Given** 门店经理需要了解当日预约的服务类型分布以便资源调配。
*   **When** 门店经理通过切换不同状态按钮查看各状态下维修类型的分布情况。
*   **Then** 系统显示保养和维修类型的预约分布；门店经理可以据此调配技师和工位资源。

**场景3: 查看预约单详细状态**

*   **Given** 门店经理需要了解预约单的具体服务内容和状态信息。
*   **When** 门店经理点击预约单的"详情"按钮查看完整信息。
*   **Then** 系统展示预约单的完整档案，包括客户信息、服务内容、状态历史等；门店经理可以进行全面的质量监控。


## 2. 业务规则说明

### 1. **数据访问权限规则**
- 门店人员只能查看所属门店的预约数据，不能跨门店访问。
- **门店接待人员**：主要负责引导客户使用APP完成签到，协助处理签到异常。
- **服务顾问**：可以查看DMS预约看板中的所有预约信息，但不能直接修改预约基本信息。
- **门店经理**：具有完整的预约查看权限，包括查看详情和导出数据，可进行业务分析和决策。
- 所有查看和操作行为需要记录审计日志。

### 2. **预约状态显示规则**
- **简化状态体系**：预约状态简化为三种核心状态 - 未到店、已到店、未履约。
- **状态映射规则**：
  - **未到店**：待处理状态
  - **已到店**：客户已完成APP扫码签到，正在进行或等待服务
  - **未履约**：客户在预约时间段内未完成APP扫码签到
- **状态切换逻辑**：通过页面左侧状态按钮进行切换，默认显示"未到店"状态。

### 3. **数据实时性规则**
- 预约状态变更需要实时更新到看板显示。
- 统计数据（总预约、已到店、未到店、到店率）需要实时计算。
- 客户APP扫码签到后状态需要立即从"未到店"更新为"已到店"。

### 4. **APP扫码签到规则**
- 每个门店生成唯一的二维码，放置在前台接待区域。
- 客户使用预约APP扫描门店二维码完成签到。
- 支持非预约人代理签到，通过车牌号验证身份。
- 签到操作需要记录操作时间、设备信息等。
- 一个预约单只能签到一次，已签到的预约不能重复操作。

### 5. **数据同步规则**
- **数据来源**：预约状态变更由APP端触发，实时同步至DMS系统。
- **二维码方案**：
  - 每个门店生成固定的签到二维码，包含门店标识信息。
  - 客户使用APP扫码后，APP端验证预约信息并完成签到。
  - APP端实时将签到成功信息推送至DMS系统。
  - DMS接收到签到信息后，将预约状态更新为【已到店】。
- **系统集成要求**：DMS需要提供标准API接口，支持与APP端的实时数据同步。

### 6. **数据导出规则**
- 导出数据需要包含当前筛选条件下的所有预约信息。
- 敏感信息（如客户手机号、身份证号）在导出时需要脱敏处理。
- 记录导出操作的审计日志（时间、用户、范围）。

## 3. 详细功能清单

### 一、预约看板主页面功能点

1.  **预约统计概览展示**
    *   **接口维度:** 获取预约统计数据 (Get Appointment Statistics)
    *   **功能描述:** 页面顶部展示当前选中日期的关键预约统计指标。
    *   **语义和规则:**
        *   数据来源：聚合当前门店在指定日期的所有预约单数据。
        *   展示内容：总预约数、已到店数、未到店数、到店率（计算公式：已到店数/总预约数*100%）。
        *   数据联动：统计数据仅与日期切换联动，不受状态按钮筛选影响。

### 统计数据计算逻辑详细说明

#### 1.1 总预约数计算逻辑
*   **计算公式：** 总预约数 = 当前门店当日所有预约单的总数量
*   **统计范围：** 包含所有状态（未到店 + 已到店 + 未履约）
*   **计算说明：** 统计指定日期内本门店的全部预约单

#### 1.2 已到店数计算逻辑
*   **计算公式：** 已到店数 = 预约状态为"已到店"的预约单数量
*   **状态定义：** 客户已完成APP扫码签到的预约
*   **计算说明：** 统计已完成到店签到确认的预约数量

#### 1.3 未到店数计算逻辑
*   **计算公式：** 未到店数 = 预约状态为"未到店"的预约单数量
*   **状态定义：** 客户尚未完成签到，处于待处理状态
*   **计算说明：** 统计等待客户到店签到的预约数量

#### 1.4 到店率计算逻辑
*   **计算公式：** 到店率 = (已到店数 ÷ 总预约数) × 100%
*   **数据精度：** 保留两位小数
*   **特殊情况处理：**
    *   当总预约数为0时：到店率 = 0.00%
    *   当已到店数 > 总预约数时：到店率 = 100.00%（异常保护）
*   **展示格式：** XX.XX%（例如：85.67%）

#### 1.5 数据验证规则
*   **完整性验证：** 未到店数 + 已到店数 + 未履约数 = 总预约数
*   **实时更新：** 
    *   APP签到成功：未到店数-1，已到店数+1，到店率重新计算
    *   新增预约：总预约数+1，对应状态数+1
    *   状态变更：原状态数-1，新状态数+1
    *   日期切换：重新计算所有数据

2.  **日期切换功能**
    *   **接口维度:** 按日期查询预约列表 (Query Appointments by Date)
    *   **功能描述:** 支持查看今日预约（默认）和明日预约数据，明日预约按钮位于页面右侧。
    *   **语义和规则:**
        *   默认显示：页面默认展示今日预约数据，无需今日按钮。
        *   按钮显示：右侧显示"明天预约(数量)"按钮。
        *   切换逻辑：点击明日预约按钮切换到明日数据，同步更新统计数据和预约列表。

3.  **预约状态切换功能**
    *   **接口维度:** 按状态筛选预约列表 (Filter Appointments by Status)
    *   **功能描述:** 页面左侧提供三个状态按钮：未到店、已到店、未履约，支持快速切换查看不同状态的预约。
    *   **语义和规则:**
        *   状态按钮：未到店（默认选中）、已到店、未履约。
        *   按钮显示：显示格式为"状态名称(数量)"，如"未到店(15)"。
        *   切换逻辑：点击状态按钮切换列表显示，统计数据保持基于全部预约的计算。
        *   视觉反馈：当前选中状态按钮高亮显示。

### 状态按钮数量统计计算逻辑详细说明

#### 3.1 状态按钮数量计算公式
*   **"未到店"按钮：** 未到店数量 = 状态为"未到店"的预约单数量
*   **"已到店"按钮：** 已到店数量 = 状态为"已到店"的预约单数量  
*   **"未履约"按钮：** 未履约数量 = 状态为"未履约"的预约单数量

#### 3.2 按钮显示格式
*   **显示规则：** "状态名称(数量)"
*   **显示示例：** 
    *   未到店(8) - 表示有8个待到店预约
    *   已到店(12) - 表示有12个已签到预约
    *   未履约(3) - 表示有3个未履约预约

#### 3.3 数据完整性验证
*   **验证公式：** 未到店数量 + 已到店数量 + 未履约数量 = 总预约数
*   **异常处理：** 当公式不成立时，触发数据校验和修复

#### 3.4 状态数量同步规则
*   **APP签到成功：** 未到店数-1，已到店数+1
*   **状态变更为未履约：** 原状态数-1，未履约数+1
*   **新增预约：** 对应状态数+1，总数+1
*   **日期切换：** 重新计算所有状态数量

4.  **预约列表展示**
    *   **接口维度:** 获取预约列表详情 (Get Appointment List Details)
    *   **功能描述:** 以表格形式展示选中日期和状态的预约单信息，采用简化字段设计。
    *   **语义和规则:**
        *   列表字段：车牌号、预约日期、预约时间段、维修类型、预约状态。
        *   数据格式：维修类型和预约状态使用彩色标签显示。
        *   排序规则：默认按预约时间段排序。
        *   状态显示：状态字段显示"未到店"、"已到店"、"未履约"三种状态。

5.  **APP签到数据同步功能**
    *   **接口维度:** APP签到数据同步接收 (APP Check-in Data Sync Reception)
    *   **功能描述:** 接收APP端的扫码签到信息，实现预约数据的实时更新。
    *   **语义和规则:**
        *   同步时机：客户使用APP扫码完成签到后实时推送到DMS。
        *   数据接收：接收车牌号、签到时间、门店标识、设备信息等。
        *   状态更新：自动将预约状态从"未到店"更新为"已到店"。
        *   数据验证：验证预约单的有效性和门店归属权限。
        *   异常处理：处理重复签到、跨门店签到等异常情况。

6.  **门店二维码管理**
    *   **接口维度:** 门店二维码生成与管理 (Store QR Code Generation and Management)
    *   **功能描述:** 为每个门店生成唯一的签到二维码，供客户扫码使用。
    *   **语义和规则:**
        *   二维码生成：基于门店标识生成唯一二维码，包含门店ID等必要信息。
        *   二维码展示：可通过管理界面查看和打印门店二维码。
        *   二维码更新：支持重新生成二维码（如安全需要）。
        *   使用方式：打印二维码并放置在门店前台接待区域。

7.  **维修类型统计**
    *   **接口维度:** 按维修类型统计预约数据 (Statistics by Service Type)
    *   **功能描述:** 展示当日预约按维修类型（保养/维修）的分类统计。
    *   **语义和规则:**
        *   统计维度：保养类预约、维修类预约。
        *   展示方式：可以通过统计卡片或图表形式展示。
        *   用途：辅助门店进行资源（技师、工位）调配。

8.  **列表分页管理**
    *   **接口维度:** 分页查询预约列表 (Paginated Query Appointments)
    *   **功能描述:** 提供分页功能优化大数据量下的列表显示性能和用户体验。
    *   **语义和规则:**
        *   分页规格：支持每页显示10、20、50、100条记录的切换。
        *   分页导航：提供首页、上一页、下一页、末页导航按钮。
        *   页码跳转：支持直接输入页码进行快速跳转。
        *   信息展示：显示当前页范围、总记录数、总页数等分页信息。
        *   联动逻辑：切换日期或状态按钮时自动重置到第一页。

### 二、预约详情查看功能点

1.  **获取预约单完整信息**
    *   **接口维度:** 获取预约单详情 (Get Appointment Details)
    *   **功能描述:** 通过点击预约单详情按钮，在弹窗中展示完整的预约信息。
    *   **语义和规则:**
        *   基本信息：预约单号、客户信息、车辆信息、预约时间等。
        *   服务信息：维修类型、选择的保养套餐（如适用）、客户描述、预估服务时长等。
        *   状态信息：当前状态、支付状态（针对保养套餐）、签到信息等。
        *   历史记录：状态变更历史、操作日志等。

2.  **支付状态展示**
    *   **接口维度:** 获取支付状态信息 (Get Payment Status)
    *   **功能描述:** 针对保养套餐预约，展示详细的支付和退款状态信息。
    *   **语义和规则:**
        *   支付信息：支付状态、支付金额、支付流水号、支付时间等。
        *   退款信息：退款状态、退款金额、退款流水号、退款时间等（如适用）。
        *   状态标识：使用不同颜色标识支付成功、待支付、退款中、已退款等状态。

### 三、数据导出功能点

1.  **预约数据导出**
    *   **接口维度:** 导出预约数据 (Export Appointment Data)
    *   **功能描述:** 支持将当前筛选条件下的预约数据导出为Excel文件。
    *   **语义和规则:**
        *   导出范围：按当前页面的筛选条件和日期范围导出。
        *   数据字段：包含预约单基本信息、状态信息、客户信息（脱敏）等。
        *   文件格式：Excel格式，包含表头说明。
        *   权限控制：需要具备导出权限的用户才能执行。

### 四、搜索和筛选功能点

1.  **快速搜索功能**
    *   **接口维度:** 搜索预约单 (Search Appointments)
    *   **功能描述:** 支持通过车牌号、客户手机号等关键字快速搜索预约单。
    *   **语义和规则:**
        *   搜索字段：车牌号、VIN、客户手机号。
        *   搜索方式：支持模糊匹配和精确匹配。
        *   搜索结果：实时过滤显示匹配的预约单。

2.  **高级筛选功能**
    *   **接口维度:** 高级筛选预约列表 (Advanced Filter Appointments)
    *   **功能描述:** 提供多维度的组合筛选条件，支持精确查找预约单。
    *   **语义和规则:**
        *   筛选维度：维修类型、预约时间段等。
        *   筛选逻辑：支持多条件AND组合筛选。
        *   筛选结果：实时更新列表，统计数据不受筛选影响。

## 5. 系统集成架构

### APP扫码签到集成流程

#### 5.1 数据同步时序图
```
DMS系统          APP端              门店接待人员          服务顾问
   |                  |                     |                  |
   |-- 生成门店二维码 -->|                     |                  |
   |                  |                     |                  |
   |                  |<--- 客户到店 ----------|                  |
   |                  |                     |                  |
   |                  |-- 扫描门店二维码 ----->|                  |
   |                  |<-- 验证预约信息 -------|                  |
   |                  |                     |                  |
   |                  |-- 完成签到操作 ------->|                  |
   |<-- 实时推送签到信息 ---|                     |                  |
   |                  |                     |                  |
   |-- 更新预约状态 ---|                     |                  |
   |                  |                     |                  |
   |-- 看板数据更新 ---------------------------|----------------->|
```

#### 5.2 API接口定义

**5.2.1 DMS生成门店二维码**
- **接口名称**：生成门店二维码接口 (Generate Store QR Code)
- **调用时机**：门店初始化或二维码更新时
- **生成内容**：包含门店ID、有效期等信息的二维码
- **使用方式**：打印二维码并放置在门店前台

**5.2.2 APP端向DMS推送签到信息**
- **接口名称**：接收APP签到信息接口 (Receive APP Check-in)
- **调用时机**：客户在APP中完成扫码签到后实时推送
- **接收字段**：预约单号、车牌号、签到时间、门店ID、设备信息等
- **处理逻辑**：验证预约单有效性、更新预约状态为"已到店"

**5.2.3 客户身份验证接口**
- **接口名称**：预约身份验证接口 (Appointment Identity Verification)
- **调用时机**：非预约人代理签到时
- **验证方式**：通过车牌号、手机号等信息验证
- **返回结果**：验证成功后允许完成签到操作

#### 5.3 数据一致性保障
- **重试机制**：APP签到失败时支持自动重试，最多重试3次
- **异常监控**：记录所有签到操作日志，便于问题排查
- **数据校验**：接收APP数据时进行格式验证和业务规则校验
- **状态回滚**：签到异常时支持数据状态回滚机制
- **离线处理**：支持网络异常时的离线签到缓存和后续同步

## 6. 核心对象（只标注了核心字段）

1.  **预约单表 (Appointments)**
    *   **用途：** 存储售后预约的基本信息和状态。
    *   **字段：**
        *   预约单号 (Appointment Number): 唯一标识预约单
        *   客户OneID (Customer OneID): 关联客户身份
        *   车辆VIN (Vehicle VIN): 车辆唯一标识
        *   车牌号 (License Plate): 车牌号码
        *   门店编号 (Store ID): 预约门店
        *   预约日期 (Appointment Date): 预约服务日期
        *   预约时间段 (Time Slot): 具体时间段
        *   维修类型 (Service Type): 保养或维修
        *   预约状态 (Status): 简化状态（未到店、已到店、未履约）
        *   客户描述 (Customer Description): 客户填写的服务需求
        *   服务顾问 (Service Advisor): 分配的服务顾问
        *   技师 (Technician): 分配的技师
        *   签到时间 (Check-in Time): APP扫码签到时间
        *   签到设备 (Check-in Device): 签到时的设备信息
        *   创建时间 (Created Time): 预约创建时间
        *   更新时间 (Updated Time): 最后更新时间

2.  **门店二维码表 (StoreQRCodes)**
    *   **用途：** 存储各门店的签到二维码信息。
    *   **字段：**
        *   二维码ID (QR Code ID): 唯一标识二维码
        *   门店编号 (Store ID): 关联门店
        *   二维码内容 (QR Content): 二维码包含的数据内容
        *   二维码URL (QR URL): 二维码图片地址
        *   生成时间 (Generated Time): 二维码生成时间
        *   有效期 (Valid Until): 二维码有效期
        *   状态 (Status): 二维码状态（有效、已失效、已替换）

3.  **APP签到记录表 (AppCheckInRecords)**
    *   **用途：** 记录APP端的签到操作历史。
    *   **字段：**
        *   记录ID (Record ID): 唯一标识签到记录
        *   预约单号 (Appointment Number): 关联预约单
        *   车牌号 (License Plate): 签到车辆
        *   门店编号 (Store ID): 签到门店
        *   签到时间 (Check-in Time): 签到操作时间
        *   设备信息 (Device Info): 签到设备信息
        *   IP地址 (IP Address): 签到网络地址
        *   签到状态 (Check-in Status): 成功、失败、异常等
        *   错误信息 (Error Message): 签到失败时的错误描述

4.  **预约统计表 (AppointmentStatistics)**
    *   **用途：** 存储预约的统计数据，支持快速查询。
    *   **字段：**
        *   统计日期 (Statistics Date): 统计的日期
        *   门店ID (Store ID): 门店标识
        *   总预约数 (Total Count): 当日总预约数
        *   已到店数 (Arrived Count): 已到店预约数
        *   未到店数 (Not Arrived Count): 未到店预约数
        *   未履约数 (No Show Count): 未履约预约数
        *   到店率 (Arrival Rate): 到店率百分比
        *   保养预约数 (Maintenance Count): 保养类预约数
        *   维修预约数 (Repair Count): 维修类预约数 