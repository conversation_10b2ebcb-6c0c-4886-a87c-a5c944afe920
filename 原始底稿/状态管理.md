
### **Pinia 核心概念**

1.  **Store (仓库)：** Pinia 的核心概念，类似于 Vuex 中的 Module。每个 Store 都是一个独立的、功能独立的逻辑单元，包含：
    * **State (状态)：** 存储数据的地方。
    * **Getters (获取器)：** 类似于计算属性，用于从 State 中派生数据，且具有缓存机制。
    * **Actions (动作)：** 用于执行异步操作（如数据请求）或修改 State。Actions 可以通过 `dispatch` 或直接调用来触发。
    * **Setters (mutations 变种)：** 在 Pinia 中，State 可以直接修改，也可以通过 Action 来修改。没有 Vuex 中严格的 Mutations 概念，使得状态修改更直观。
2.  **Vue Devtools 集成：** Pinia 与 Vue Devtools 深度集成，提供了出色的调试体验，可以方便地查看状态变化、Actions 触发等。
3.  **插件系统：** 允许扩展 Pinia 的功能，例如数据持久化、错误报告等。

### **Pinia 状态管理实践**

结合我们之前讨论的业务模块和技术栈，以下是 Pinia 的组织和使用建议。

#### **1. Pinia 目录结构 (`src/stores`)**

我们将 `src/stores` 目录按业务模块进行划分，每个模块一个 Store 文件。

```
src/
├── stores/
│   ├── index.ts                # Pinia 实例的创建和导出
│   ├── modules/                # 各个业务模块的 Store 定义
│   │   ├── user.ts             # 用户认证、权限、用户信息等
│   │   ├── sales.ts            # 销售模块相关状态 (例如：车辆列表的全局筛选、订单的临时数据)
│   │   ├── customer.ts         # 客户模块相关状态
│   │   └── settings.ts         # 全局应用设置、主题配置等
```

#### **2. `src/stores/index.ts` - Pinia 实例创建**

这是 Pinia 的入口文件，在这里创建 Pinia 实例并导出，供 `main.ts` 使用。

```typescript
// src/stores/index.ts
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'; // 用于状态持久化

const pinia = createPinia();

// 注册持久化插件 (如果需要将某些状态存储在 localStorage/sessionStorage)
pinia.use(piniaPluginPersistedstate);

export default pinia;
```

#### **3. `src/main.ts` - 注册 Pinia**

在应用入口文件 `main.ts` 中注册 Pinia。

```typescript
// src/main.ts
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import pinia from './stores'; // 引入 Pinia 实例
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import '@/assets/styles/index.scss'; // 引入全局Sass样式（包含Element Plus主题变量）

const app = createApp(App);

app.use(pinia); // 注册 Pinia
app.use(router);
app.use(ElementPlus);

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}

app.mount('#app');
```

#### **4. `src/stores/modules/user.ts` - 用户 Store 示例 (最常用)**

用户 Store 通常包含登录状态、用户信息、认证 Token、权限等。这类数据通常需要持久化。

```typescript
// src/stores/modules/user.ts
import { defineStore } from 'pinia';
import { login, logout, getUserInfo } from '@/api/modules/auth'; // 假设有认证API
import router from '@/router';
import { ElMessage } from 'element-plus';

interface UserInfo {
  id: string;
  username: string;
  nickname: string;
  avatar: string;
  roles: string[]; // 用户角色
  permissions: string[]; // 用户权限 (例如：['sales:vehicle:add', 'customer:view'])
}

interface UserState {
  token: string | null;
  userInfo: UserInfo | null;
  hasPermission: boolean; // 是否已加载权限 (防止重复加载)
}

export const useUserStore = defineStore('user', {
  // 定义状态
  state: (): UserState => ({
    token: null,
    userInfo: null,
    hasPermission: false, // 初始为 false，首次登录或刷新页面时加载
  }),

  // 定义 getters
  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.userInfo?.roles.includes('admin') || false,
    // 判断是否有特定权限 (例如，判断是否有新增车辆的权限)
    canAddVehicle: (state) => state.userInfo?.permissions.includes('sales:vehicle:add') || false,
  },

  // 定义 actions
  actions: {
    /**
     * 用户登录
     * @param username 用户名
     * @param password 密码
     */
    async login(username: string, password: string): Promise<void> {
      try {
        // 调用 API 进行登录，假设返回 token
        const res = await login({ username, password });
        this.token = res.token; // 存储 token
        ElMessage.success('登录成功！');
        await this.fetchUserInfo(); // 登录成功后获取用户信息和权限
        router.push('/'); // 跳转到首页
      } catch (error) {
        this.token = null; // 登录失败清除 token
        this.userInfo = null;
        ElMessage.error('登录失败，请检查用户名或密码！');
        throw error; // 抛出错误以便组件捕获
      }
    },

    /**
     * 获取用户信息和权限
     */
    async fetchUserInfo(): Promise<void> {
      try {
        // 调用 API 获取用户信息和权限列表
        const res = await getUserInfo();
        this.userInfo = res.userInfo;
        this.hasPermission = true;
      } catch (error) {
        this.userInfo = null;
        this.token = null; // 获取用户信息失败，清除 token
        this.hasPermission = false;
        ElMessage.error('获取用户信息失败！');
        router.push('/login'); // 跳转到登录页
        throw error;
      }
    },

    /**
     * 用户登出
     */
    async logout(): Promise<void> {
      try {
        await logout(); // 调用 API 登出 (可选，如果后端有登出接口)
        this.clearToken();
        ElMessage.success('已登出！');
        router.push('/login');
      } catch (error) {
        ElMessage.error('登出失败！');
        console.error(error);
      }
    },

    /**
     * 清除 Token 和用户信息
     */
    clearToken(): void {
      this.token = null;
      this.userInfo = null;
      this.hasPermission = false;
    },

    /**
     * 检查用户是否拥有特定权限
     * @param permission 权限字符串
     */
    checkPermission(permission: string): boolean {
      if (!this.userInfo || !this.userInfo.permissions) {
        return false;
      }
      return this.userInfo.permissions.includes(permission);
    },
    /**
     * 检查用户是否拥有任意一个权限
     * @param permissions 权限字符串数组
     */
    checkAnyPermission(permissions: string[]): boolean {
      if (!this.userInfo || !this.userInfo.permissions) {
        return false;
      }
      return permissions.some(p => this.userInfo!.permissions.includes(p));
    }
  },

  // 配置持久化 (如果需要)
  // 默认存储在 localStorage
  // 也可以配置为 sessionStorage，或指定存储的 key
  persist: {
    key: 'dms_user_store',
    paths: ['token', 'userInfo'], // 只持久化 token 和 userInfo
    storage: localStorage, // 默认就是 localStorage
  },
});
```

**在组件中使用 `userStore`：**

```vue
<template>
  <el-button v-if="userStore.canAddVehicle" type="primary">新增车辆</el-button>
  <el-button v-if="userStore.isLoggedIn" @click="userStore.logout">登出</el-button>
  <el-button v-else @click="router.push('/login')">登录</el-button>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user';
import { useRouter } from 'vue-router';

const userStore = useUserStore();
const router = useRouter();

// 也可以在 setup 中直接调用 action
// const handleLogin = async () => {
//   try {
//     await userStore.login('testuser', 'password');
//   } catch (e) {
//     // ...
//   }
// };
</script>
```

#### **5. `src/stores/modules/sales.ts` - 销售模块 Store 示例**

管理销售模块相关的状态，例如车辆列表的筛选条件、某些临时数据等。

```typescript
// src/stores/modules/sales.ts
import { defineStore } from 'pinia';
import { getVehicleList } from '@/api/modules/sales'; // 引入 API

interface SalesState {
  vehicleListFilter: {
    model: string;
    status: string;
  };
  currentVehicleCount: number; // 车辆总数
}

export const useSalesStore = defineStore('sales', {
  state: (): SalesState => ({
    vehicleListFilter: {
      model: '',
      status: '',
    },
    currentVehicleCount: 0,
  }),
  getters: {
    // 可以在这里派生出基于过滤器的查询参数
    getVehicleListParams: (state) => ({
      ...state.vehicleListFilter,
      // 还可以添加分页信息，但通常分页在组件内部管理更灵活
    }),
  },
  actions: {
    // 更新车辆列表的筛选条件
    setVehicleListFilter(filter: { model?: string; status?: string }) {
      if (filter.model !== undefined) {
        this.vehicleListFilter.model = filter.model;
      }
      if (filter.status !== undefined) {
        this.vehicleListFilter.status = filter.status;
      }
    },

    // 假设需要在全局刷新车辆总数
    async refreshVehicleCount() {
      try {
        // 这里的 getVehicleList 假设可以不传分页参数，或者传入 { page: 1, pageSize: 1 } 获取总数
        const res = await getVehicleList({ page: 1, pageSize: 1 });
        this.currentVehicleCount = res.total;
      } catch (error) {
        console.error('刷新车辆总数失败:', error);
        // ElMessage.error('刷新车辆总数失败');
      }
    }
  },
  // 如果筛选条件需要记住，也可以持久化
  // persist: {
  //   key: 'dms_sales_filter',
  //   paths: ['vehicleListFilter'],
  // },
});
```

**在组件中使用 `salesStore`：**

```vue
<template>
  <div class="list-page">
    <el-form :inline="true" :model="salesStore.vehicleListFilter" class="search-form">
      <el-form-item label="车辆型号">
        <el-input v-model="salesStore.vehicleListFilter.model" placeholder="请输入型号" clearable></el-input>
      </el-form-item>
      <el-form-item label="车辆状态">
        <el-select v-model="salesStore.vehicleListFilter.status" placeholder="请选择状态" clearable>
          <el-option label="在库" value="in_stock"></el-option>
          <el-option label="已售" value="sold"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { useSalesStore } from '@/stores/modules/sales'; // 引入 sales store
import { getVehicleList, type VehicleListItem } from '@/api/modules/sales';

const salesStore = useSalesStore(); // 获取 sales store 实例

const tableData = ref<VehicleListItem[]>([]);
const loading = ref(false);
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const fetchVehicles = async () => {
  loading.value = true;
  try {
    const params = {
      ...salesStore.vehicleListFilter, // 从 store 中获取筛选条件
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
    };
    const res = await getVehicleList(params);
    tableData.value = res.list;
    pagination.total = res.total;
    salesStore.currentVehicleCount = res.total; // 更新 store 中的总数
  } catch (error) {
    console.error('获取车辆列表失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.currentPage = 1; // 搜索时重置回第一页
  fetchVehicles();
};

const handleReset = () => {
  salesStore.setVehicleListFilter({ model: '', status: '' }); // 重置 store 中的筛选条件
  pagination.currentPage = 1;
  fetchVehicles(); // 重置后重新查询
};

// 监听 store 中筛选条件的变化，自动触发查询
watch(salesStore.vehicleListFilter, () => {
  handleSearch();
}, { deep: true }); // 深度监听对象

onMounted(() => {
  fetchVehicles();
});
</script>
```

### **Pinia 状态管理最佳实践**

* **模块化 Store：** 将不同业务逻辑的状态分别放入独立的 Store 文件中，提高可维护性。
* **按需导入：** 在组件中只需要导入并使用需要的 Store，不会引入额外的代码。
* **状态持久化：** 对于需要跨页面或刷新后仍然保留的状态（如用户登录 Token、某些用户偏好设置），使用 `pinia-plugin-persistedstate` 插件进行持久化。
* **Actions 封装异步逻辑：** 将数据请求和复杂的业务逻辑封装在 Actions 中，组件只负责触发 Actions 并从 Store 中获取数据，实现职责分离。
* **Getters 派生状态：** 利用 Getters 缓存计算属性，避免重复计算，并保持组件的简洁。
* **TypeScript 强类型：** 为 State、Getters、Actions 的参数和返回值定义清晰的类型，借助 IDE 提示和编译检查，大幅减少运行时错误。
* **遵循单一职责原则：** 一个 Store 应该只管理一个或一组紧密相关的业务状态。
* **避免过度使用：** 并非所有数据都需要放到 Pinia 中。只有当数据需要在多个不相关的组件之间共享，或者需要全局可访问时，才考虑使用 Pinia。组件内部的状态（例如表单的临时输入值、组件的显示/隐藏状态）仍然可以使用 `ref` 或 `reactive` 管理。

