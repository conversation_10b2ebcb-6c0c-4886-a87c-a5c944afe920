# 售后预约管理系统需求文档

## 1. 概述

### 1.1 系统简介
售后预约管理系统是DMS（经销商管理系统）的核心模块之一，主要用于管理客户的售后服务预约，包括保养和维修两种类型的预约。系统支持预约的全生命周期管理，从预约创建到环检单生成的完整流程。

### 1.2 主要功能
- 预约信息查询与筛选
- 预约详情查看
- 环检单创建确认
- 预约状态管理
- 服务内容管理（保养类型）
- 支付信息管理（保养类型）

## 2. 页面线框图

### 2.1 主页面线框图

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                         预约管理 - 售后预约管理                                          │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                                             │
│  ┌─────────────────────────────────────── 搜索条件 ───────────────────────────────────────┐              │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                        │              │
│  │  │  预约单号   │ │   车牌号    │ │ 预约人手机  │ │ 送修人手机  │                        │              │
│  │  │ [_________] │ │ [_________] │ │ [_________] │ │ [_________] │                        │              │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘                        │              │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                        │              │
│  │  │  创建时间   │ │    状态     │ │  作业类型   │ │  服务顾问   │                        │              │
│  │  │[开始～结束] │ │ [▼_______] │ │ [▼_______] │ │ [▼_______] │                        │              │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘                        │              │
│  │                                                          [搜索] [重置]                   │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│                                                                                                             │
│  ┌─────────────────────────────────────── 操作区域 ───────────────────────────────────────┐              │
│  │  总计 XX 条记录                                                        [导出Excel]     │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│                                                                                                             │
│  ┌─────────────────────────────────────── 数据表格 ───────────────────────────────────────┐              │
│  │┌─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────────┐│              │
│  ││预约 │车牌 │预约 │预约 │送修 │送修 │预约 │时间 │作业 │状态 │服务 │环检 │创建 │  操作   ││              │
│  ││单号 │号码 │联系人│电话 │联系人│电话 │日期 │段   │类型 │     │顾问 │单号 │时间 │         ││              │
│  │├─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────────┤│              │
│  ││AP001│粤A  │张三 │1380 │李四 │1390 │2024-│09:00│保养 │已到 │王五 │QI001│2024-│[详情]   ││              │
│  ││     │123AB│     │0001 │     │0002 │01-15│-11:00│     │店   │     │     │01-15│[创建环检]││              │
│  │├─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────────┤│              │
│  ││AP002│粤A  │王六 │1380 │     │     │2024-│14:00│维修 │已预 │     │     │2024-│[详情]   ││              │
│  ││     │456CD│     │0003 │     │     │01-15│-16:00│     │约   │     │     │01-15│         ││              │
│  │└─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────────┘│              │
│  │                                                                                        │              │
│  │                              [总数] [每页] [上一页] [1][2][3] [下一页] [跳转]            │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 保养类型预约详情对话框

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                    预约详情对话框 - AP001详情                                             │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────── 预约信息 ───────────────────────────────────────┐              │
│  │  预约单号: AP001                    │  状态: [已到店]                                   │              │
│  │  预约时间: 2024-01-15 09:00-11:00   │  作业类型: [保养]                                │              │
│  │  客户描述: 定期保养，检查发动机                                                          │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 客户信息 ───────────────────────────────────────┐              │
│  │  预约联系人: 张三                   │  预约电话: 13800000001                           │              │
│  │  送修联系人: 李四                   │  送修电话: 13900000002                           │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 车辆信息 ───────────────────────────────────────┐              │
│  │  车牌号: 粤A123AB                   │  车架号: LNBSCZGM8JX123456                       │              │
│  │  车型: Model S                      │  款式: 标准版                                    │              │
│  │  颜色: 白色                         │  里程: 25000km                                   │              │
│  │  车龄: 18个月                       │  交车时间: 2022-07-15                            │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 预约相关 ───────────────────────────────────────┐              │
│  │  门店: 北京朝阳店                   │  服务顾问: 王五                                  │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 服务内容 ───────────────────────────────────────┐              │
│  │  套餐名称: 标准保养套餐 (SP001)      │  预估总额: ¥800                                 │              │
│  │  ┌─工时明细──────────────────────────────────────────────────────────────────┐       │              │
│  │  │项目名称     │项目代码│标准工时│单价 │小计 │                                  │       │              │
│  │  │机油更换     │L001   │2.0h    │¥50 │¥100│                                  │       │              │
│  │  │滤芯更换     │L002   │1.0h    │¥30 │¥30 │                                  │       │              │
│  │  └────────────────────────────────────────────────────────────────────────┘       │              │
│  │  ┌─零件明细──────────────────────────────────────────────────────────────────┐       │              │
│  │  │零件名称     │零件代码│数量    │单价  │小计 │                                 │       │              │
│  │  │机油         │P001   │4L      │¥80  │¥320│                                 │       │              │
│  │  │机滤         │P002   │1个     │¥150 │¥150│                                 │       │              │
│  │  └────────────────────────────────────────────────────────────────────────┘       │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 支付信息 ───────────────────────────────────────┐              │
│  │  支付方式: [在线支付]                │  支付状态: [已支付]                               │              │
│  │  支付金额: ¥800                     │  支付订单号: PAY123456789                        │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│                                                                        [关闭]                             │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

### 2.3 维修类型预约详情对话框

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                              预约详情对话框 - AP002详情 (维修类型)                                        │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────── 预约信息 ───────────────────────────────────────┐              │
│  │  预约单号: AP002                    │  状态: [已预约]                                   │              │
│  │  预约时间: 2024-01-15 14:00-16:00   │  作业类型: [维修]                                │              │
│  │  客户描述: 发动机异响，需要检查维修                                                      │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 客户信息 ───────────────────────────────────────┐              │
│  │  预约联系人: 王六                   │  预约电话: 13800000003                           │              │
│  │  送修联系人: 王六                   │  送修电话: 13800000003                           │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 车辆信息 ───────────────────────────────────────┐              │
│  │  车牌号: 粤A456CD                   │  车架号: LNBSCZGM8JX654321                       │              │
│  │  车型: Model X                      │  车型配置: 豪华版                                │              │
│  │  颜色: 黑色                         │  里程: 45000km                                   │              │
│  │  车龄: 36个月                       │  交车时间: 2021-01-15                            │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 预约相关 ───────────────────────────────────────┐              │
│  │  门店: 北京朝阳店                   │  服务顾问: 未分配                                │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│                                                                                                             │
│  ※ 维修类型预约不显示服务内容和支付信息，具体维修项目将在到店检查后确定                    │              │
│                                                                                                             │
│                                                                        [关闭]                             │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

### 2.4 保养类型环检单创建确认对话框

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                  环检单创建确认对话框 - AP001                                             │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────── 提示信息 ───────────────────────────────────────┐              │
│  │  ℹ️ 确认以下信息无误后，将为该预约创建环检单                                              │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 预约信息 ───────────────────────────────────────┐              │
│  │  预约单号: AP001                    │  状态: [已到店]                                   │              │
│  │  预约时间: 2024-01-15 09:00-11:00   │  服务类型: [保养]                                │              │
│  │  客户描述: 定期保养，检查发动机                                                          │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 客户信息 ───────────────────────────────────────┐              │
│  │  预约联系人: 张三                   │  预约电话: 13800000001                           │              │
│  │  送修联系人: [_________]              │  送修电话: [_________]                            │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 车辆信息 ───────────────────────────────────────┐              │
│  │  车牌号: 粤A123AB                   │  车架号: LNBSCZGM8JX123456                       │              │
│  │  车型: Model S                      │  款式: 标准版                                    │              │
│  │  颜色: 白色                         │  里程: 25000km                                   │              │
│  │  车龄: 18个月                       │  交车时间: 2022-07-15                            │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 服务内容确认 ────────────────────────────────────┐              │
│  │  套餐名称: 标准保养套餐 (SP001)      │  预估总额: ¥800                                 │              │
│  │  ┌─工时明细──────────────────────────────────────────────────────────────────┐       │              │
│  │  │项目名称     │项目代码│标准工时│单价 │小计 │                                  │       │              │
│  │  │机油更换     │L001   │2.0h    │¥50 │¥100│                                  │       │              │
│  │  │滤芯更换     │L002   │1.0h    │¥30 │¥30 │                                  │       │              │
│  │  └────────────────────────────────────────────────────────────────────────┘       │              │
│  │  ┌─零件明细──────────────────────────────────────────────────────────────────┐       │              │
│  │  │零件名称     │零件代码│数量    │单价  │小计 │                                 │       │              │
│  │  │机油         │P001   │4L      │¥80  │¥320│                                 │       │              │
│  │  │机滤         │P002   │1个     │¥150 │¥150│                                 │       │              │
│  │  └────────────────────────────────────────────────────────────────────────┘       │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 支付信息确认 ────────────────────────────────────┐              │
│  │  支付方式: [在线支付]                │  支付状态: [已支付]                               │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│                                                            [取消]  [确认创建]                            │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

### 2.5 维修类型环检单创建确认对话框

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                            环检单创建确认对话框 - AP002 (维修类型)                                        │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────── 提示信息 ───────────────────────────────────────┐              │
│  │  ℹ️ 确认以下信息无误后，将为该预约创建环检单                                              │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 预约信息 ───────────────────────────────────────┐              │
│  │  预约单号: AP002                    │  状态: [已到店]                                   │              │
│  │  预约时间: 2024-01-15 14:00-16:00   │  服务类型: [维修]                                │              │
│  │  客户描述: 发动机异响，需要检查维修                                                      │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 客户信息 ───────────────────────────────────────┐              │
│  │  预约联系人: 王六                   │  预约电话: 13800000003                           │              │
│  │  送修联系人: [_________]              │  送修电话: [_________]                            │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│  ┌─────────────────────────────────────── 车辆信息 ───────────────────────────────────────┐              │
│  │  车牌号: 粤A456CD                   │  车架号: LNBSCZGM8JX654321                       │              │
│  │  车型: Model X                      │  车型配置: 豪华版                                │              │
│  │  颜色: 黑色                         │  里程: 45000km                                   │              │
│  │  车龄: 36个月                       │  交车时间: 2021-01-15                            │              │
│  └─────────────────────────────────────────────────────────────────────────────────────────┘              │
│                                                                                                             │
│  ※ 维修类型预约不包含预设服务套餐，具体维修项目和费用将在技师检查后确定                    │              │
│                                                                                                             │
│                                                            [取消]  [确认创建]                            │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

## 3. 页面交互功能点

### 3.1 主页面交互功能

#### 3.1.1 搜索筛选功能
- **预约单号搜索**：支持精确匹配和模糊搜索
- **车牌号搜索**：支持模糊搜索，自动去除空格
- **预约人手机搜索**：支持精确匹配
- **送修人手机搜索**：支持精确匹配
- **创建时间范围筛选**：支持日期范围选择，默认不设置范围
- **状态筛选**：
  - 已预约 (scheduled)
  - 已到店 (arrived)
  - 待支持 (pending_support)
  - 已取消 (cancelled)
  - 未到店 (no_show)
- **作业类型筛选**：
  - 保养 (maintenance)
  - 维修 (repair)
- **服务顾问筛选**：从主数据获取服务顾问列表

#### 3.1.2 操作按钮功能
- **搜索按钮**：执行筛选查询，重置页码为1
- **重置按钮**：清空所有搜索条件，重新加载数据
- **导出Excel按钮**：导出当前筛选结果的Excel文件

#### 3.1.3 表格功能
- **分页功能**：支持页码跳转、每页条数调整
- **排序功能**：支持按创建时间排序
- **操作列功能**：
  - **详情按钮**：查看预约详细信息
  - **创建环检按钮**：条件显示，仅当状态为"已到店"且未创建环检单时显示

### 3.2 预约详情对话框交互功能

#### 3.2.1 显示规则
- **基础信息**：始终显示预约信息、客户信息、车辆信息、预约相关信息
- **服务内容**：仅当作业类型为"保养"时显示
- **支付信息**：仅当作业类型为"保养"时显示

#### 3.2.2 车龄计算
- 根据交车时间自动计算车龄（以月为单位）
- 计算公式：当前时间 - 交车时间
- 显示格式：XX个月

#### 3.2.3 价格格式化
- 统一使用人民币符号¥
- 千位分隔符显示
- 格式：¥1,000

### 3.3 环检单创建确认对话框交互功能

#### 3.3.1 信息确认
- 显示完整的预约信息供用户确认
- 送修联系人信息可编辑（姓名和电话）

#### 3.3.2 条件显示
- **服务内容确认**：仅当作业类型为"保养"时显示
- **支付信息确认**：仅当作业类型为"保养"时显示

#### 3.3.3 操作功能
- **取消按钮**：关闭对话框，不执行任何操作
- **确认创建按钮**：创建环检单，更新预约状态

## 4. API接口设计

### 4.1 预约列表查询接口

```typescript
GET /api/afterSales/appointments

// 请求参数
interface AppointmentListParams {
  appointmentId?: string;        // 预约单号
  licensePlate?: string;         // 车牌号
  reservationPhone?: string;     // 预约人手机号
  servicePhone?: string;         // 送修人手机号
  dateRange?: [string, string];  // 创建时间范围 ['YYYY-MM-DD', 'YYYY-MM-DD']
  status?: string;              // 状态
  serviceType?: string;         // 服务类型
  serviceAdvisorId?: string;    // 服务顾问ID
  technicianId?: string;        // 技师ID
  page: number;                 // 页码，从1开始
  pageSize: number;             // 每页条数
}

// 响应数据
interface AppointmentListResponse {
  code: number;
  message: string;
  data: {
    list: AppointmentListItem[];
    total: number;
    page: number;
    pageSize: number;
  };
}
```

### 4.2 预约详情查询接口

```typescript
GET /api/afterSales/appointments/{id}

// 响应数据
interface AppointmentDetailResponse {
  code: number;
  message: string;
  data: AppointmentDetail;
}
```

### 4.3 创建环检单接口

```typescript
POST /api/afterSales/appointments/{id}/quality-inspection

// 请求参数
interface CreateQualityInspectionRequest {
  name: string;    // 送修联系人姓名
  phone: string;   // 送修联系人电话
}

// 响应数据
interface CreateQualityInspectionResponse {
  code: number;
  message: string;
  data: {
    qualityInspectionId: string;  // 生成的环检单号
  };
}
```

### 4.4 服务顾问列表接口

```typescript
GET /api/masterData/serviceAdvisors

// 响应数据
interface ServiceAdvisorListResponse {
  code: number;
  message: string;
  data: ServiceAdvisor[];
}
```

### 4.5 技师列表接口

```typescript
GET /api/masterData/technicians

// 响应数据
interface TechnicianListResponse {
  code: number;
  message: string;
  data: Technician[];
}
```

### 4.6 字典数据接口

```typescript
GET /api/dictionary/batch

// 请求参数
interface BatchDictionaryRequest {
  types: string[];  // 字典类型数组，如：['APPOINTMENT_STATUS', 'WORK_ORDER_TYPE']
}

// 响应数据
interface BatchDictionaryResponse {
  code: number;
  message: string;
  data: {
    [type: string]: DictionaryItem[];
  };
}
```

## 5. 实体对象分析

### 5.1 预约列表项实体 (AppointmentListItem)

```typescript
interface AppointmentListItem {
  id: string;                           // 预约单号
  licensePlate: string;                 // 车牌号
  reservationContactName: string;       // 预约联系人姓名
  reservationContactPhone: string;      // 预约联系人电话
  serviceContactName?: string;          // 送修联系人姓名（可选）
  serviceContactPhone?: string;         // 送修联系人电话（可选）
  appointmentTime: string;              // 预约日期 'YYYY-MM-DD'
  timeSlot: string;                     // 时间段 'HH:mm-HH:mm'
  serviceType: 'maintenance' | 'repair'; // 作业类型
  status: AppointmentStatus;            // 预约状态
  serviceAdvisor?: {                    // 服务顾问（可选）
    id: string;
    name: string;
  };
  qualityInspectionId?: string;         // 环检单号（可选）
  inspectionCreated: boolean;           // 是否已创建环检单
  createdAt: string;                    // 创建时间 'YYYY-MM-DD HH:mm:ss'
}
```

### 5.2 预约详情实体 (AppointmentDetail)

```typescript
interface AppointmentDetail {
  // 基础信息
  id: string;                           // 预约单号
  status: AppointmentStatus;            // 预约状态
  appointmentTime: string;              // 预约日期
  timeSlot: string;                     // 时间段
  serviceType: 'maintenance' | 'repair'; // 服务类型
  customerDescription?: string;         // 客户描述

  // 客户信息
  reservationContactName: string;       // 预约联系人姓名
  reservationContactPhone: string;      // 预约联系人电话
  serviceContactName: string;           // 送修联系人姓名
  serviceContactPhone: string;          // 送修联系人电话

  // 车辆信息
  licensePlate: string;                 // 车牌号
  vin: string;                         // 车架号
  model: string;                       // 车型
  variant: string;                     // 车型配置
  color: string;                       // 颜色
  mileage?: number;                    // 里程（可选）
  deliveryDate?: string;               // 交车时间（可选，用于车龄计算）

  // 预约相关
  store: {                             // 门店信息
    id: string;
    name: string;
  };
  serviceAdvisor?: {                   // 服务顾问（可选）
    id: string;
    name: string;
  };

  // 保养套餐信息（仅保养类型）
  maintenancePackage?: MaintenancePackage;

  // 支付信息（仅保养类型）
  paymentMethod?: 'online' | 'offline'; // 支付方式
  paymentStatus?: PaymentStatus;        // 支付状态
  paymentAmount?: number;               // 支付金额
  paymentOrderNumber?: string;          // 支付订单号
}
```

### 5.3 保养套餐实体 (MaintenancePackage)

```typescript
interface MaintenancePackage {
  name: string;                         // 套餐名称
  code: string;                         // 套餐代码
  totalAmount: number;                  // 总金额
  laborItems: LaborItem[];              // 工时项目列表
  partsItems: PartsItem[];              // 零件项目列表
}
```

### 5.4 工时项目实体 (LaborItem)

```typescript
interface LaborItem {
  id: string;                           // 项目ID
  name: string;                         // 项目名称
  code: string;                         // 项目代码
  standardHours: number;                // 标准工时
  unitPrice: number;                    // 单价
  subtotal: number;                     // 小计
}
```

### 5.5 零件项目实体 (PartsItem)

```typescript
interface PartsItem {
  id: string;                           // 零件ID
  name: string;                         // 零件名称
  code: string;                         // 零件代码
  quantity: number;                     // 数量
  unit: string;                         // 单位
  unitPrice: number;                    // 单价
  subtotal: number;                     // 小计
}
```

### 5.6 服务顾问实体 (ServiceAdvisor)

```typescript
interface ServiceAdvisor {
  id: string;                           // 服务顾问ID
  name: string;                         // 姓名
  code: string;                         // 工号
  phone?: string;                       // 电话（可选）
  email?: string;                       // 邮箱（可选）
  status: 'active' | 'inactive';       // 状态
}
```

### 5.7 技师实体 (Technician)

```typescript
interface Technician {
  id: string;                           // 技师ID
  name: string;                         // 姓名
  code: string;                         // 工号
  phone?: string;                       // 电话（可选）
  specialization: string[];             // 专业领域
  level: 'junior' | 'senior' | 'expert'; // 技师等级
  status: 'active' | 'inactive';       // 状态
}
```

### 5.8 字典项实体 (DictionaryItem)

```typescript
interface DictionaryItem {
  code: string;                         // 字典代码
  name: string;                         // 字典名称
  value?: string;                       // 字典值（可选）
  description?: string;                 // 描述（可选）
  sort: number;                         // 排序
  status: 'active' | 'inactive';       // 状态
}
```

### 5.9 枚举定义

```typescript
// 预约状态枚举
type AppointmentStatus = 
  | 'scheduled'        // 已预约
  | 'arrived'          // 已到店
  | 'pending_support'  // 待支持
  | 'cancelled'        // 已取消
  | 'no_show';         // 未到店

// 支付状态枚举
type PaymentStatus = 
  | 'unpaid'           // 未支付
  | 'paid'             // 已支付
  | 'refunded';        // 已退款
```

## 6. 业务规则

### 6.1 预约状态流转规则
1. **已预约** → **已到店**：客户按时到达
2. **已预约** → **未到店**：客户未按时到达
3. **已预约** → **已取消**：客户主动取消或系统取消
4. **已到店** → **待支持**：开始服务流程

### 6.2 环检单创建规则
1. 仅当预约状态为"已到店"时可创建环检单
2. 每个预约只能创建一次环检单
3. 创建环检单时必须确认送修联系人信息
4. 创建成功后自动分配服务顾问

### 6.3 作业类型业务规则
1. **保养类型**：
   - 必须有预设的保养套餐
   - 必须有支付信息
   - 显示详细的工时和零件明细
2. **维修类型**：
   - 不预设服务套餐
   - 不显示支付信息
   - 维修项目在检查后确定

### 6.4 车龄计算业务规则
1. **计算基准**：以交车时间为基准进行计算
2. **计算精度**：以月为单位，向下取整
3. **异常处理**：交车时间为空时显示"未知"
4. **边界情况**：交车时间晚于当前时间时显示"未知"

### 6.5 数据校验规则
1. **手机号格式**：11位数字，以1开头
2. **车牌号格式**：符合中国车牌号标准
3. **金额格式**：非负数，保留2位小数
4. **日期格式**：YYYY-MM-DD
5. **时间格式**：HH:mm-HH:mm

## 7. 国际化支持

### 7.1 多语言键值定义
系统支持中英文双语，所有文本内容通过国际化键值管理：

```typescript
// 国际化键值示例
const i18nKeys = {
  // 页面标题
  'appointmentManagement': '预约管理',
  
  // 表单标签
  'labels.appointmentId': '预约单号',
  'labels.licensePlate': '车牌号',
  'labels.status': '状态',
  
  // 按钮文本
  'buttons.search': '搜索',
  'buttons.reset': '重置',
  'buttons.detail': '详情',
  
  // 状态文本
  'statuses.scheduled': '已预约',
  'statuses.arrived': '已到店',
  
  // 消息提示
  'messages.operationSuccess': '操作成功',
  'messages.operationFailed': '操作失败'
};
```

## 8. 性能要求

### 8.1 响应时间要求
- 页面加载时间：≤ 2秒
- 搜索响应时间：≤ 1秒
- 详情查看响应时间：≤ 1秒
- 环检单创建响应时间：≤ 3秒

### 8.2 数据量支持
- 单页显示记录数：10-100条可配置
- 总记录数支持：≥ 10万条
- 搜索结果集：≤ 5000条

### 8.3 并发要求
- 支持100个并发用户同时操作
- 数据一致性保证：防止重复创建环检单

## 9. 安全要求

### 9.1 权限控制
- 按角色控制功能访问权限
- 按部门控制数据访问范围
- 敏感信息脱敏显示

### 9.2 数据安全
- 客户手机号部分脱敏显示
- 操作日志记录
- 数据传输加密

## 10. 技术实现要点

### 10.1 前端技术栈
- Vue 3.x + TypeScript
- Element Plus UI组件库
- Pinia状态管理
- Vue Router路由管理
- Vue I18n国际化

### 10.2 关键技术实现
- 响应式数据绑定
- 组件化开发
- 车龄计算：基于交车时间的动态计算（当前时间 - 交车时间）
- 懒加载和虚拟滚动（大数据量场景）
- 防抖搜索
- 表单验证
- 错误边界处理

### 10.3 代码组织结构
```
src/views/afterSales/appointments/
├── AppointmentsView.vue              # 主页面
├── components/
│   ├── AppointmentDetailDialog.vue   # 详情对话框
│   └── QualityInspectionDialog.vue   # 环检单确认对话框
└── types/
    └── appointments.d.ts             # 类型定义
```

### 10.4 关键算法实现

#### 车龄计算函数
```typescript
// 计算车龄（月）- 基于交车时间
const calculateVehicleAgeInMonths = (deliveryDate: string | undefined): string => {
  if (!deliveryDate) {
    return '未知';
  }
  const delivery = new Date(deliveryDate);
  const now = new Date();
  const years = now.getFullYear() - delivery.getFullYear();
  const months = now.getMonth() - delivery.getMonth();
  const totalMonths = years * 12 + months;
  return totalMonths >= 0 ? `${totalMonths}个月` : '未知';
};
```

## 11. 测试要求

### 11.1 单元测试
- 组件渲染测试
- 工具函数测试
- 数据处理逻辑测试

### 11.2 集成测试
- API接口调用测试
- 用户交互流程测试
- 错误处理测试

### 11.3 端到端测试
- 完整业务流程测试
- 浏览器兼容性测试
- 性能压力测试

---

**文档版本**：v1.0  
**创建日期**：2024年1月  
**最后更新**：2024年1月  
**文档状态**：待评审 