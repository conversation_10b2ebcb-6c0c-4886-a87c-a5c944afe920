# 预约详情显示规则 - 优化技术方案

## 1. 任务背景

### 1.1 核心任务
- **预约详情对话框**：根据作业类型（保养/维修）条件显示服务内容和支付信息
- **环检单创建确认对话框**：根据作业类型条件显示服务内容确认和支付信息确认
- **车龄显示**：使用后端计算好的车龄数据，前端仅负责展示

### 1.2 业务规则
- **保养类型** (`serviceType: 'maintenance'`)：显示服务内容和支付信息
- **维修类型** (`serviceType: 'repair'`)：不显示服务内容和支付信息
- **车龄字段**：由后端计算，前端添加`vehicleAge`字段用于展示

## 2. 现状分析

### 2.1 已实现的功能 ✅
1. **AppointmentDetailDialog.vue**:
   - 服务内容条件显示：`v-if="appointmentDetail.serviceType === 'maintenance'"` ✅
   
2. **QualityInspectionDialog.vue**:
   - 服务内容确认条件显示：`v-if="appointmentData.serviceType === 'maintenance'"` ✅
   - 支付信息确认条件显示：`v-if="appointmentData.serviceType === 'maintenance'"` ✅

### 2.2 需要修复的问题 🐛

#### 问题1：支付信息显示条件错误
**位置**: `src/views/afterSales/appointments/components/AppointmentDetailDialog.vue` 第229行

**当前代码**:
```typescript
v-if="appointmentDetail.paymentStatus && appointmentDetail.serviceType === 'maintenance'"
```

**问题分析**:
- 双重条件导致保养类型预约在无支付状态时不显示支付信息区域
- 应该只判断作业类型，保养类型始终显示支付信息区域

**修复方案**:
```typescript
v-if="appointmentDetail.serviceType === 'maintenance'"
```

#### 问题2：Mock数据缺少车龄字段
**影响文件**:
- `src/types/afterSales/appointments.d.ts` - 类型定义
- `src/mock/data/afterSales/appointments.ts` - 列表mock数据
- `src/api/modules/afterSales/appointments.ts` - 详情mock数据

**问题分析**:
- 当前使用`productionDate`计算车龄，应改为后端提供的`vehicleAge`字段
- Mock数据需要添加车龄字段以便前端展示

#### 问题3：Mock数据中保养套餐数据不完整
**位置**: `src/api/modules/afterSales/appointments.ts` 第47-49行

**当前代码**:
```typescript
paymentStatus: 'paid',
// 缺少 paymentAmount 和 paymentMethod
paymentOrderNumber: 'PAY_' + Date.now(),
```

**修复方案**:
```typescript
paymentStatus: 'paid',
paymentAmount: 950,
paymentMethod: 'online',
paymentOrderNumber: 'PAY_' + Date.now(),
```

## 3. 技术方案

### 3.1 架构设计

```
┌─────────────────────────────────────────┐
│            后端数据层                   │
│  - vehicleAge: "18个月"                 │
│  - serviceType: "maintenance|repair"    │
│  - maintenancePackage?: {...}          │
│  - paymentStatus?: "paid|unpaid"       │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│            前端展示层                   │
│  - 条件显示逻辑                        │
│  - 数据格式化展示                      │
│  - 用户交互处理                        │
└─────────────────────────────────────────┘
```

### 3.2 条件显示逻辑优化

#### 预约详情对话框显示规则
```typescript
// 基础信息 - 始终显示
- 预约信息 ✅
- 客户信息 ✅ 
- 车辆信息 ✅
- 预约相关信息 ✅

// 服务内容 - 仅保养类型显示
v-if="appointmentDetail.serviceType === 'maintenance'"

// 支付信息 - 仅保养类型显示（修复条件）
v-if="appointmentDetail.serviceType === 'maintenance'"  // 移除paymentStatus条件
```

#### 环检单确认对话框显示规则
```typescript
// 基础信息 - 始终显示
- 预约信息 ✅
- 客户信息 ✅ (含可编辑送修人信息)
- 车辆信息 ✅

// 服务内容确认 - 仅保养类型显示 ✅
v-if="appointmentData.serviceType === 'maintenance'"

// 支付信息确认 - 仅保养类型显示 ✅
v-if="appointmentData.serviceType === 'maintenance'"
```

### 3.3 数据结构优化

#### 类型定义更新
```typescript
// src/types/afterSales/appointments.d.ts
export interface AppointmentListItem {
  // ... 其他字段
  vehicleAge?: string;           // ✅ 新增：后端计算的车龄
  productionDate?: string;       // 保留：生产日期（备用）
}

export interface AppointmentDetail extends AppointmentListItem {
  // ... 继承车龄字段
  paymentAmount?: number;        // 确保支付金额字段存在
  paymentMethod?: 'online' | 'store'; // 确保支付方式字段存在
}
```

#### Mock数据更新
```typescript
// 列表数据添加车龄字段
const mockAppointmentList: AppointmentListItem[] = [
  {
    // ... 其他字段
    vehicleAge: '18个月',         // ✅ 新增车龄字段
    productionDate: '2022-03-15'  // 保留原字段
  }
];

// 详情数据完善支付信息
const mockDetail: AppointmentDetail = {
  // ... 其他字段
  vehicleAge: '18个月',           // ✅ 新增车龄字段
  paymentStatus: 'paid',
  paymentAmount: 950,             // ✅ 新增支付金额
  paymentMethod: 'online',        // ✅ 新增支付方式
  paymentOrderNumber: 'PAY_' + Date.now(),
};
```

## 4. 实施计划

### 4.1 第一优先级：修复支付信息显示条件 🚨
**时间**: 15分钟
**文件**: `AppointmentDetailDialog.vue`
**修改**: 第229行条件判断

### 4.2 第二优先级：完善Mock数据 📊
**时间**: 30分钟
**文件**: 
- `appointments.d.ts` - 添加vehicleAge类型
- `appointments.ts` (API) - 完善详情mock数据
- `appointments.ts` (Mock) - 添加列表车龄字段

### 4.3 第三优先级：更新车龄显示逻辑 🔄
**时间**: 15分钟
**文件**: 两个对话框组件
**修改**: 车龄显示改为使用vehicleAge字段

### 4.4 第四优先级：测试验证 🧪
**时间**: 30分钟
**范围**: 完整的条件显示功能测试

## 5. 详细实施步骤

### Step 1: 修复支付信息显示条件
```typescript
// src/views/afterSales/appointments/components/AppointmentDetailDialog.vue
// 第229行修改
- v-if="appointmentDetail.paymentStatus && appointmentDetail.serviceType === 'maintenance'"
+ v-if="appointmentDetail.serviceType === 'maintenance'"
```

### Step 2: 更新类型定义
```typescript
// src/types/afterSales/appointments.d.ts
export interface AppointmentListItem {
  // ... 现有字段
  vehicleAge?: string;           // 新增：车龄（后端计算）
  productionDate?: string;       // 保留：生产日期
}
```

### Step 3: 更新详情Mock数据
```typescript
// src/api/modules/afterSales/appointments.ts
const mockDetail: AppointmentDetail = {
  // ... 现有字段
  vehicleAge: '18个月',           // 新增
  paymentAmount: 950,             // 新增
  paymentMethod: 'online',        // 新增
  // ... 其他字段
};
```

### Step 4: 更新列表Mock数据
```typescript
// src/mock/data/afterSales/appointments.ts
// 为每个预约项添加vehicleAge字段
{
  // ... 现有字段
  vehicleAge: '18个月',           // 根据车辆计算的车龄
}
```

### Step 5: 更新车龄显示
```typescript
// 两个对话框组件中
// 将车龄计算改为直接显示
- {{ calculateVehicleAgeInMonths(appointmentData.productionDate) }}
+ {{ appointmentData.vehicleAge || tc('unknown') }}
```

## 6. 测试用例

### 6.1 保养类型预约测试
```typescript
// 测试数据
const maintenanceAppointment = {
  serviceType: 'maintenance',
  maintenancePackage: { /* 套餐数据 */ },
  paymentStatus: 'paid',
  paymentAmount: 950,
  paymentMethod: 'online'
};

// 期望结果
- ✅ 显示服务内容区域
- ✅ 显示支付信息区域
- ✅ 显示保养套餐明细
- ✅ 显示工时和零件表格
```

### 6.2 维修类型预约测试
```typescript
// 测试数据
const repairAppointment = {
  serviceType: 'repair',
  customerDescription: '发动机异响'
};

// 期望结果
- ✅ 显示基础信息区域
- ❌ 不显示服务内容区域
- ❌ 不显示支付信息区域
- ✅ 显示客户描述
```

### 6.3 车龄显示测试
```typescript
// 测试数据
const appointmentWithAge = {
  vehicleAge: '18个月'
};

// 期望结果
- ✅ 直接显示"18个月"
- ✅ 无计算逻辑
- ✅ 空值时显示"未知"
```

## 7. 验收标准

### 7.1 功能验收
- [ ] 保养类型预约显示完整的服务内容和支付信息
- [ ] 维修类型预约仅显示基础信息，隐藏服务内容和支付信息
- [ ] 车龄字段正确显示后端计算的值
- [ ] 环检单确认对话框条件显示正常

### 7.2 数据验收
- [ ] Mock数据包含完整的车龄、支付金额、支付方式字段
- [ ] 保养类型预约包含完整的保养套餐数据
- [ ] 维修类型预约数据结构正确

### 7.3 用户体验验收
- [ ] 不同类型预约的信息展示符合业务逻辑
- [ ] 页面加载和切换流畅
- [ ] 数据显示完整准确

## 8. 风险控制

### 8.1 低风险项
- 条件显示逻辑修改（已有基础逻辑）
- Mock数据完善（不影响现有功能）

### 8.2 注意事项
- 确保现有功能不受影响
- 验证两种类型预约的显示效果
- 检查国际化文本正确性

---

**预计完成时间**: 1.5小时  
**测试时间**: 30分钟  
**总计**: 2小时 