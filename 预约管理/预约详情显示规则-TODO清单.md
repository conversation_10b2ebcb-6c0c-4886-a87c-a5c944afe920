# 预约详情显示规则 - TODO 清单

## 📋 任务概览
- **总任务数**: 8个
- **预计工期**: 2小时
- **优先级**: 高
- **核心目标**: 条件显示规则 + 车龄字段展示

## 🚨 第一优先级：修复支付信息显示条件 (15分钟)

### ✅ Task 1: 修复支付信息显示逻辑
- **文件**: `src/views/afterSales/appointments/components/AppointmentDetailDialog.vue`
- **位置**: 第229行
- **问题**: 双重条件导致保养类型预约在无支付状态时不显示支付信息
- **修改**: 
  ```typescript
  // 当前代码
  - v-if="appointmentDetail.paymentStatus && appointmentDetail.serviceType === 'maintenance'"
  
  // 修复后
  + v-if="appointmentDetail.serviceType === 'maintenance'"
  ```

**验收标准**:
- [ ] 保养类型预约始终显示支付信息区域（无论paymentStatus状态）
- [ ] 维修类型预约不显示支付信息区域
- [ ] 现有功能不受影响

## 📊 第二优先级：完善Mock数据 (30分钟)

### ✅ Task 2: 更新类型定义
- **文件**: `src/types/afterSales/appointments.d.ts`
- **修改**: 添加vehicleAge字段
  ```typescript
  export interface AppointmentListItem {
    // ... 现有字段
    vehicleAge?: string;           // ✅ 新增：后端计算的车龄
    productionDate?: string;       // 保留：生产日期（备用）
  }
  ```

**验收标准**:
- [ ] 类型定义包含vehicleAge字段
- [ ] 保持向下兼容性

### ✅ Task 3: 完善详情Mock数据
- **文件**: `src/api/modules/afterSales/appointments.ts`
- **修改**: 添加缺失的支付字段和车龄字段
  ```typescript
  const mockDetail: AppointmentDetail = {
    // ... 现有字段
    vehicleAge: '18个月',           // ✅ 新增
    paymentAmount: 950,             // ✅ 新增
    paymentMethod: 'online',        // ✅ 新增
    // ... 其他字段
  };
  ```

**验收标准**:
- [ ] 详情数据包含完整的车龄、支付金额、支付方式
- [ ] 保养类型预约有完整的maintenancePackage数据
- [ ] 数据结构符合类型定义

### ✅ Task 4: 更新列表Mock数据
- **文件**: `src/mock/data/afterSales/appointments.ts`
- **修改**: 为每个预约项添加vehicleAge字段
  ```typescript
  // 为每个预约项添加车龄数据
  {
    id: 'APT001',
    // ... 现有字段
    vehicleAge: '18个月',           // ✅ 基于交车时间计算的车龄
    productionDate: '2022-03-15'    // 保留原字段
  }
  ```

**验收标准**:
- [ ] 所有列表项包含vehicleAge字段
- [ ] 车龄数据合理（与车辆信息匹配）
- [ ] 保持原有字段不变

## 🔄 第三优先级：更新车龄显示逻辑 (15分钟)

### ✅ Task 5: 更新预约详情对话框车龄显示
- **文件**: `src/views/afterSales/appointments/components/AppointmentDetailDialog.vue`
- **位置**: 第147行
- **修改**: 
  ```typescript
  // 当前代码
  - {{ calculateVehicleAgeInMonths(appointmentDetail.productionDate) }}
  
  // 修复后
  + {{ appointmentDetail.vehicleAge || tc('unknown') }}
  ```

**验收标准**:
- [ ] 直接显示后端计算的车龄
- [ ] 空值时显示"未知"
- [ ] 无前端计算逻辑

### ✅ Task 6: 更新环检单确认对话框车龄显示
- **文件**: `src/views/afterSales/appointments/components/QualityInspectionDialog.vue`
- **位置**: 第192行
- **修改**: 
  ```typescript
  // 当前代码
  - {{ calculateVehicleAgeInMonths(appointmentData.productionDate) }}
  
  // 修复后
  + {{ appointmentData.vehicleAge || tc('unknown') }}
  ```

**验收标准**:
- [ ] 直接显示后端计算的车龄
- [ ] 空值时显示"未知"
- [ ] 无前端计算逻辑

### ✅ Task 7: 清理无用的车龄计算函数
- **范围**: 两个对话框组件
- **修改**: 删除本地的calculateVehicleAgeInMonths函数
  ```typescript
  // 删除这些本地函数（第34-48行 和 第38-48行）
  - const calculateVehicleAgeInMonths = (productionDate: string | undefined): string => {
  -   // ... 计算逻辑
  - };
  ```

**验收标准**:
- [ ] 删除重复的车龄计算函数
- [ ] 代码简洁，无冗余逻辑
- [ ] 功能正常运行

## 🧪 第四优先级：测试验证 (30分钟)

### ✅ Task 8: 完整功能测试
- **范围**: 条件显示和车龄展示功能
- **测试用例**:
  1. **保养类型预约**:
     - 显示服务内容区域 ✅
     - 显示支付信息区域 ✅
     - 显示车龄信息 ✅
  
  2. **维修类型预约**:
     - 不显示服务内容区域 ❌
     - 不显示支付信息区域 ❌
     - 显示车龄信息 ✅
  
  3. **车龄显示**:
     - 直接展示后端数据 ✅
     - 空值显示"未知" ✅

**验收标准**:
- [ ] 保养类型预约显示完整信息
- [ ] 维修类型预约隐藏服务内容和支付信息
- [ ] 车龄字段正确显示
- [ ] 所有交互功能正常

## 📊 任务进度跟踪

| 任务 | 预计时间 | 状态 | 备注 |
|------|----------|------|------|
| Task 1 | 15分钟 | ⭕ | 核心问题，最高优先级 |
| Task 2 | 10分钟 | ⭕ | 类型定义基础 |
| Task 3 | 10分钟 | ⭕ | 详情数据完善 |
| Task 4 | 10分钟 | ⭕ | 列表数据完善 |
| Task 5 | 5分钟 | ⭕ | 详情对话框车龄 |
| Task 6 | 5分钟 | ⭕ | 确认对话框车龄 |
| Task 7 | 5分钟 | ⭕ | 清理冗余代码 |
| Task 8 | 30分钟 | ⭕ | 完整测试 |

## 🎯 关键验证点

### 保养类型预约 (`serviceType: 'maintenance'`)
```
✅ 预约信息
✅ 客户信息  
✅ 车辆信息 (含车龄)
✅ 预约相关信息
✅ 服务内容 (保养套餐、工时、零件)
✅ 支付信息 (支付方式、状态、金额)
```

### 维修类型预约 (`serviceType: 'repair'`)
```
✅ 预约信息
✅ 客户信息  
✅ 车辆信息 (含车龄)
✅ 预约相关信息
❌ 服务内容 (隐藏)
❌ 支付信息 (隐藏)
```

## 🔗 相关文件路径
- **技术方案**: [预约详情显示规则-优化技术方案.md](./预约详情显示规则-优化技术方案.md)
- **需求文档**: [售后预约管理系统需求文档.md](./售后预约管理系统需求文档.md)

---

**目标**: 确保条件显示逻辑正确，车龄字段正常展示  
**时间**: 2小时完成  
**验收**: 保养/维修类型预约显示规则符合业务需求 