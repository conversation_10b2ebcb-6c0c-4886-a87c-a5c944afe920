# 售后预约管理 - 车龄计算优化 TODO 清单

## 📋 任务概览
- **总任务数**: 15个
- **预计工期**: 3-5个工作日
- **优先级**: 高
- **负责人**: 前端开发团队

## 🎯 第一阶段：基础设施搭建 (1-2天)

### ✅ Task 1: 创建公共工具函数
- **文件**: `src/utils/vehicle.ts`
- **优先级**: 高
- **预计时间**: 2小时
- **依赖**: 无

**详细任务**:
```typescript
// 1. 创建 src/utils/vehicle.ts 文件
// 2. 实现 calculateVehicleAgeInMonths 函数
// 3. 实现 formatVehicleAge 函数
// 4. 添加 JSDoc 注释
// 5. 考虑国际化支持
```

**验收标准**:
- [ ] 函数正确计算车龄（基于交车时间）
- [ ] 处理边界情况（空值、未来日期、无效日期）
- [ ] 包含完整的类型定义和注释
- [ ] 支持国际化文本

### ✅ Task 2: 修改类型定义
- **文件**: `src/types/afterSales/appointments.d.ts`
- **优先级**: 高
- **预计时间**: 30分钟
- **依赖**: 无

**详细任务**:
```typescript
// 修改 AppointmentListItem 接口
// 1. 将 productionDate?: string 改为 deliveryDate?: string
// 2. 更新相关注释
// 3. 确保类型一致性
```

**验收标准**:
- [ ] `productionDate` 字段已替换为 `deliveryDate`
- [ ] 类型定义与业务需求一致
- [ ] 相关接口继承正确

### ✅ Task 3: 更新模拟数据
- **文件**: `src/mock/data/afterSales/appointments.ts`
- **优先级**: 中
- **预计时间**: 30分钟
- **依赖**: Task 2

**详细任务**:
```typescript
// 1. 将所有 productionDate 字段改为 deliveryDate
// 2. 调整测试数据的日期值（模拟真实交车时间）
// 3. 确保日期格式一致性 (YYYY-MM-DD)
```

**验收标准**:
- [ ] 所有mock数据使用 `deliveryDate` 字段
- [ ] 日期值合理（交车时间 > 生产时间）
- [ ] 日期格式统一

## 🔧 第二阶段：组件重构 (2-3天)

### ✅ Task 4: 重构主页面组件
- **文件**: `src/views/afterSales/appointments/AppointmentsView.vue`
- **优先级**: 高
- **预计时间**: 1小时
- **依赖**: Task 1

**详细任务**:
```typescript
// 1. 导入公共工具函数: import { calculateVehicleAgeInMonths } from '@/utils/vehicle'
// 2. 删除本地的 calculateVehicleAgeInMonths 函数 (第56-66行)
// 3. 检查是否有模板中使用车龄计算的地方
// 4. 更新相关字段引用 (productionDate → deliveryDate)
```

**验收标准**:
- [ ] 删除重复的车龄计算函数
- [ ] 正确导入和使用公共工具函数
- [ ] 所有相关字段引用已更新
- [ ] 页面功能正常运行

### ✅ Task 5: 重构预约详情对话框
- **文件**: `src/views/afterSales/appointments/components/AppointmentDetailDialog.vue`
- **优先级**: 高
- **预计时间**: 1小时
- **依赖**: Task 1

**详细任务**:
```typescript
// 1. 导入公共工具函数
// 2. 删除本地的 calculateVehicleAgeInMonths 函数 (第34-44行)
// 3. 更新模板中的调用 (第147行):
//    {{ calculateVehicleAgeInMonths(appointmentDetail.productionDate) }}
//    改为:
//    {{ calculateVehicleAgeInMonths(appointmentDetail.deliveryDate) }}
// 4. 测试对话框显示效果
```

**验收标准**:
- [ ] 删除重复的车龄计算函数
- [ ] 模板正确调用新的工具函数
- [ ] 车龄显示正确
- [ ] 对话框其他功能不受影响

### ✅ Task 6: 重构环检单确认对话框
- **文件**: `src/views/afterSales/appointments/components/QualityInspectionDialog.vue`
- **优先级**: 高
- **预计时间**: 1小时
- **依赖**: Task 1

**详细任务**:
```typescript
// 1. 导入公共工具函数
// 2. 删除本地的 calculateVehicleAgeInMonths 函数 (第38-48行)
// 3. 更新模板中的调用 (第192行):
//    {{ calculateVehicleAgeInMonths(appointmentData.productionDate) }}
//    改为:
//    {{ calculateVehicleAgeInMonths(appointmentData.deliveryDate) }}
// 4. 测试对话框显示效果
```

**验收标准**:
- [ ] 删除重复的车龄计算函数
- [ ] 模板正确调用新的工具函数
- [ ] 车龄显示正确
- [ ] 环检单创建功能正常

### ✅ Task 7: 检查其他相关文件
- **范围**: 全项目搜索
- **优先级**: 中
- **预计时间**: 1小时
- **依赖**: Task 1-6

**详细任务**:
```bash
# 1. 全局搜索 productionDate 引用
grep -r "productionDate" src/
# 2. 全局搜索 calculateVehicleAgeInMonths 定义
grep -r "calculateVehicleAgeInMonths" src/
# 3. 检查 API 接口定义
# 4. 检查路由和状态管理相关文件
```

**验收标准**:
- [ ] 确认没有遗漏的 `productionDate` 引用
- [ ] 确认没有重复的车龄计算函数
- [ ] API接口定义与前端类型一致

## 🧪 第三阶段：测试验证 (1天)

### ✅ Task 8: 编写单元测试
- **文件**: `tests/utils/vehicle.test.ts`
- **优先级**: 高
- **预计时间**: 2小时
- **依赖**: Task 1

**详细任务**:
```typescript
// 1. 创建测试文件 tests/utils/vehicle.test.ts
// 2. 测试正常情况下的车龄计算
// 3. 测试边界情况（空值、未来日期、无效日期）
// 4. 测试国际化支持
// 5. 达到 90% 以上的代码覆盖率
```

**验收标准**:
- [ ] 测试覆盖率 ≥ 90%
- [ ] 所有边界情况都有测试用例
- [ ] 测试用例可读性强，有清晰的描述

### ✅ Task 9: 编写组件测试
- **文件**: `tests/components/AppointmentDetailDialog.test.ts` 等
- **优先级**: 中
- **预计时间**: 2小时
- **依赖**: Task 5, 6

**详细任务**:
```typescript
// 1. 测试预约详情对话框的车龄显示
// 2. 测试环检单确认对话框的车龄显示
// 3. 测试不同数据情况下的显示效果
// 4. 确保组件渲染正常
```

**验收标准**:
- [ ] 组件正确渲染车龄信息
- [ ] 不同数据情况下显示正确
- [ ] 测试用例稳定可靠

### ✅ Task 10: 集成测试
- **范围**: 整个预约管理模块
- **优先级**: 高
- **预计时间**: 1小时
- **依赖**: Task 1-9

**详细任务**:
```typescript
// 1. 测试主页面到详情对话框的完整流程
// 2. 测试环检单创建的完整流程
// 3. 验证数据流的正确性
// 4. 检查用户体验是否符合预期
```

**验收标准**:
- [ ] 完整的用户操作流程正常
- [ ] 数据在各个组件间正确传递
- [ ] 用户界面显示友好

## 🐛 第四阶段：问题修复和优化 (0.5天)

### ✅ Task 11: 修复 linter 错误
- **范围**: 修改过的所有文件
- **优先级**: 高
- **预计时间**: 30分钟
- **依赖**: Task 1-10

**详细任务**:
```bash
# 1. 运行 ESLint 检查
npm run lint
# 2. 修复代码风格问题
# 3. 优化类型定义
# 4. 清理无用的导入
```

**验收标准**:
- [ ] 无 ESLint 错误
- [ ] 代码风格统一
- [ ] 类型定义准确

### ✅ Task 12: 性能优化
- **范围**: 车龄计算相关代码
- **优先级**: 中
- **预计时间**: 1小时
- **依赖**: Task 11

**详细任务**:
```typescript
// 1. 检查是否需要计算属性缓存
// 2. 优化日期计算逻辑
// 3. 考虑使用 memo 化缓存结果
// 4. 检查内存泄漏风险
```

**验收标准**:
- [ ] 车龄计算性能良好
- [ ] 无明显的性能瓶颈
- [ ] 内存使用合理

## 📝 第五阶段：文档和代码审查 (0.5天)

### ✅ Task 13: 更新文档
- **文件**: 相关的 README 和注释
- **优先级**: 中
- **预计时间**: 30分钟
- **依赖**: Task 12

**详细任务**:
```markdown
// 1. 更新组件文档
// 2. 更新 API 接口文档
// 3. 更新工具函数文档
// 4. 添加使用示例
```

**验收标准**:
- [ ] 文档与代码实现一致
- [ ] 包含清晰的使用示例
- [ ] 文档结构清晰易读

### ✅ Task 14: 代码审查准备
- **范围**: 所有修改的代码
- **优先级**: 高
- **预计时间**: 1小时
- **依赖**: Task 13

**详细任务**:
```bash
# 1. 整理代码提交记录
# 2. 准备 PR 描述
# 3. 列出关键修改点
# 4. 准备演示数据
```

**验收标准**:
- [ ] 代码提交记录清晰
- [ ] PR 描述详细准确
- [ ] 关键修改点有说明

### ✅ Task 15: 最终验证
- **范围**: 整个功能模块
- **优先级**: 高
- **预计时间**: 1小时
- **依赖**: Task 14

**详细任务**:
```typescript
// 1. 端到端功能测试
// 2. 浏览器兼容性检查
// 3. 响应式布局检查
// 4. 用户体验最终确认
```

**验收标准**:
- [ ] 所有功能正常工作
- [ ] 浏览器兼容性良好
- [ ] 用户体验友好

## 📊 进度跟踪

### 任务状态说明
- ⭕ 未开始
- 🔄 进行中
- ✅ 已完成
- ❌ 已阻塞
- ⚠️ 需注意

### 关键里程碑
1. **Day 1 End**: Task 1-3 完成（基础设施搭建）
2. **Day 2 End**: Task 4-7 完成（组件重构）
3. **Day 3 End**: Task 8-10 完成（测试验证）
4. **Day 4 End**: Task 11-15 完成（完整交付）

### 风险点监控
- 🚨 **高风险**: Task 2 (类型定义修改) - 可能影响其他模块
- ⚠️ **中风险**: Task 7 (检查其他文件) - 可能发现意外依赖
- 📝 **注意点**: 确保与后端API的字段名保持一致

## 🔗 相关链接
- [技术方案文档](./售后预约管理-车龄计算优化技术方案.md)
- [需求文档](./售后预约管理系统需求文档.md)
- [Git 分支策略](../规范/Git分支管理规范.md)

---

**最后更新**: 2024年1月  
**维护人**: 前端开发团队  
**审查人**: 技术负责人 