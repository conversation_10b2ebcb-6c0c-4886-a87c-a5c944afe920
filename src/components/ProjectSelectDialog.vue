<template>
  <el-dialog
    :model-value="visible"
    title="选择项目"
    width="800px"
    :close-on-click-modal="false"
    :modal="false"
    @update:model-value="updateVisible"
  >
    <div class="project-select-dialog">
      <!-- 搜索区域 -->
      <el-row :gutter="20" class="mb-15">
        <el-col :span="12">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索项目名称或编码..."
            :prefix-icon="Search"
            @input="handleSearch"
            clearable
          />
        </el-col>
        <el-col :span="12">
          <el-select
            v-model="selectedType"
            placeholder="选择项目类型"
            @change="handleSearch"
            clearable
            :loading="dictionaryLoading"
          >
            <el-option value="" label="全部类型" />
            <el-option
              v-for="option in projectTypeOptions"
              :key="option.code"
              :value="option.code"
              :label="option.name"
            />
          </el-select>
        </el-col>
      </el-row>

      <!-- 项目列表 -->
      <el-table
        ref="tableRef"
        :data="filteredProjects"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="item_code" label="项目编码" width="120" />
        <el-table-column prop="item_name" label="项目名称" width="200" />
        <el-table-column label="项目类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.item_type === 'maintenance' ? 'success' : (row.item_type === 'repair' ? 'warning' : 'primary')"
            >
              {{ getProjectTypeText(row.item_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="标准工时" width="100" align="right">
          <template #default="{ row }">
            {{ row.standard_hours || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="工时单价" width="100" align="right">
          <template #default="{ row }">
            ¥{{ row.labor_rate || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="套餐项目" width="80" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.is_package" type="success" size="small">
              套餐
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSearch"
          @current-change="handleSearch"
        />
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="updateVisible(false)">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="selectedProjects.length === 0"
        >
          确定选择 ({{ selectedProjects.length }})
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Search } from '@element-plus/icons-vue';
import type { ProjectItem } from '@/types/workOrder';
import { getProjectItems } from '@/api/modules/workOrder';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// Props
interface Props {
  visible: boolean;
  workOrderType?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  workOrderType: ''
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [projects: ProjectItem[]];
}>();

// 数据
const loading = ref(false);
const projects = ref<ProjectItem[]>([]);
const selectedProjects = ref<ProjectItem[]>([]);
const tableRef = ref();

// 使用字典数据
const {
  options: projectTypeOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.PROJECT_TYPE);

// 搜索参数
const searchKeyword = ref('');
const selectedType = ref('');
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 过滤后的项目列表
const filteredProjects = computed(() => {
  let result = [...projects.value];

  if (searchKeyword.value) {
    result = result.filter(item =>
      item.item_name.includes(searchKeyword.value) ||
      item.item_code.includes(searchKeyword.value)
    );
  }

  if (selectedType.value) {
    result = result.filter(item => item.item_type === selectedType.value);
  }

  // 根据工单类型过滤
  if (props.workOrderType === 'maintenance') {
    result = result.filter(item => item.item_type === 'maintenance');
  } else if (props.workOrderType === 'repair') {
    result = result.filter(item =>
      item.item_type === 'repair' || item.item_type === 'claim'
    );
  }

  total.value = result.length;

  // 分页
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return result.slice(start, end);
});

// 获取项目列表
const fetchProjects = async () => {
  loading.value = true;
  try {
    const response = await getProjectItems({
      workOrderType: props.workOrderType
    });
    projects.value = response;
  } catch (error) {
    console.error('Failed to fetch projects:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  // filteredProjects 计算属性会自动更新
};

// 选择变化
const handleSelectionChange = (selection: ProjectItem[]) => {
  selectedProjects.value = selection;
};

// 确认选择
const handleConfirm = () => {
  emit('confirm', selectedProjects.value);
  updateVisible(false);
};

// 更新显示状态
const updateVisible = (value: boolean) => {
  emit('update:visible', value);
};

// 获取项目类型文本
const getProjectTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    maintenance: '保养',
    repair: '维修',
    claim: '索赔'
  };
  return typeMap[type] || type;
};

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    fetchProjects();
    selectedProjects.value = [];
    searchKeyword.value = '';
    selectedType.value = '';
    currentPage.value = 1;
  }
});

onMounted(() => {
  if (props.visible) {
    fetchProjects();
  }
});
</script>

<style lang="scss" scoped>
.project-select-dialog {
  .mb-15 {
    margin-bottom: 15px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
