<template>
  <div class="dictionary-usage-example">
    <h2>字典组件使用示例</h2>
    
    <!-- 使用通用字典选择器 -->
    <el-card class="mb-20">
      <template #header>
        <span>通用字典选择器示例</span>
      </template>
      
      <el-form :model="formData" label-width="120px">
        <!-- 工单优先级选择 -->
        <el-form-item label="工单优先级">
          <DictionarySelect
            v-model="formData.priority"
            :dictionary-type="DICTIONARY_TYPES.WORK_ORDER_PRIORITY"
            placeholder="请选择优先级"
            style="width: 200px"
            @change="handlePriorityChange"
          />
        </el-form-item>
        
        <!-- 工单状态选择（多选） -->
        <el-form-item label="工单状态">
          <DictionarySelect
            v-model="formData.statuses"
            :dictionary-type="DICTIONARY_TYPES.WORK_ORDER_STATUS"
            placeholder="请选择状态"
            multiple
            style="width: 300px"
            @change="handleStatusChange"
          />
        </el-form-item>
        
        <!-- 买方类型选择（带全部选项） -->
        <el-form-item label="买方类型">
          <DictionarySelect
            v-model="formData.buyerType"
            :dictionary-type="DICTIONARY_TYPES.BUYER_TYPE"
            placeholder="请选择买方类型"
            show-all
            all-label="全部类型"
            style="width: 200px"
            @change="handleBuyerTypeChange"
          />
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 使用通用字典单选组 -->
    <el-card class="mb-20">
      <template #header>
        <span>通用字典单选组示例</span>
      </template>
      
      <el-form :model="formData" label-width="120px">
        <!-- 客户来源选择 -->
        <el-form-item label="客户来源">
          <DictionaryRadio
            v-model="formData.customerSource"
            :dictionary-type="DICTIONARY_TYPES.CUSTOMER_SOURCE"
            @change="handleCustomerSourceChange"
          />
        </el-form-item>
        
        <!-- 工单类型选择（带禁用选项） -->
        <el-form-item label="工单类型">
          <DictionaryRadio
            v-model="formData.workOrderType"
            :dictionary-type="DICTIONARY_TYPES.WORK_ORDER_TYPE"
            :disabled-options="disabledWorkOrderTypes"
            @change="handleWorkOrderTypeChange"
          />
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 使用组合函数的示例 -->
    <el-card class="mb-20">
      <template #header>
        <span>使用组合函数示例</span>
      </template>
      
      <el-form :model="formData" label-width="120px">
        <!-- 申请状态选择 -->
        <el-form-item label="申请状态">
          <el-select
            v-model="formData.requisitionStatus"
            placeholder="请选择申请状态"
            :loading="requisitionStatusLoading"
            style="width: 200px"
          >
            <el-option
              v-for="option in requisitionStatusOptions"
              :key="option.code"
              :value="option.code"
              :label="option.name"
            />
          </el-select>
        </el-form-item>
        
        <!-- 显示选中的状态名称 -->
        <el-form-item label="状态名称">
          <el-input
            :value="getRequisitionStatusName(formData.requisitionStatus)"
            readonly
            style="width: 200px"
          />
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表单数据展示 -->
    <el-card>
      <template #header>
        <span>表单数据</span>
      </template>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import DictionarySelect from '@/components/common/DictionarySelect.vue';
import DictionaryRadio from '@/components/common/DictionaryRadio.vue';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 表单数据
const formData = ref({
  priority: '',
  statuses: [],
  buyerType: '',
  customerSource: '',
  workOrderType: '',
  requisitionStatus: '',
});

// 使用单个字典的组合函数示例
const { 
  options: requisitionStatusOptions, 
  getNameByCode: getRequisitionStatusName,
  loading: requisitionStatusLoading 
} = useDictionary(DICTIONARY_TYPES.REQUISITION_STATUS);

// 禁用的工单类型（根据客户来源动态计算）
const disabledWorkOrderTypes = computed(() => {
  if (formData.value.customerSource === 'walk_in') {
    return ['maintenance']; // 自然到店客户不能选择保养
  }
  return [];
});

// 事件处理函数
const handlePriorityChange = (value: string) => {
  ElMessage.info(`优先级已选择: ${value}`);
};

const handleStatusChange = (value: string[]) => {
  ElMessage.info(`状态已选择: ${value.join(', ')}`);
};

const handleBuyerTypeChange = (value: string) => {
  ElMessage.info(`买方类型已选择: ${value}`);
};

const handleCustomerSourceChange = (value: string) => {
  ElMessage.info(`客户来源已选择: ${value}`);
  // 根据客户来源重置工单类型
  if (value === 'walk_in' && formData.value.workOrderType === 'maintenance') {
    formData.value.workOrderType = '';
  }
};

const handleWorkOrderTypeChange = (value: string) => {
  ElMessage.info(`工单类型已选择: ${value}`);
};
</script>

<style scoped lang="scss">
.dictionary-usage-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.mb-20 {
  margin-bottom: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}
</style>
