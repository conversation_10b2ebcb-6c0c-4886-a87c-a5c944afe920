<template>
  <div class="language-test-example">
    <el-card header="语言切换测试">
      <div class="test-content">
        <div class="language-info">
          <h3>当前语言信息</h3>
          <p><strong>当前语言代码:</strong> {{ currentLanguage }}</p>
          <p><strong>当前语言名称:</strong> {{ currentLanguageName }}</p>
          <p><strong>HTTP请求语言代码:</strong> {{ httpLanguageCode }}</p>
        </div>

        <div class="language-switcher">
          <h3>语言切换</h3>
          <LanguageSelector />

          <div class="manual-switch" style="margin-top: 16px;">
            <h4>编程式切换：</h4>
            <el-button-group>
              <el-button
                @click="switchToLanguage('zh')"
                :loading="switching"
                :type="currentLanguage === 'zh' ? 'primary' : 'default'"
              >
                切换到中文
              </el-button>
              <el-button
                @click="switchToLanguage('en')"
                :loading="switching"
                :type="currentLanguage === 'en' ? 'primary' : 'default'"
              >
                Switch to English
              </el-button>
            </el-button-group>
          </div>
        </div>

        <div class="translation-test">
          <h3>翻译测试</h3>
          <p>{{ tc('confirm') }}</p>
          <p>{{ tc('cancel') }}</p>
          <p>{{ tc('save') }}</p>
        </div>

        <div class="api-test">
          <h3>API请求测试</h3>
          <el-button @click="testApiRequest" :loading="loading">
            测试API请求
          </el-button>
          <div v-if="lastRequestInfo" class="request-info">
            <h4>上次请求信息:</h4>
            <pre>{{ JSON.stringify(lastRequestInfo, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getCurrentLanguage, getLanguageDisplayName, switchLanguage } from '@/plugins/i18n';
import { http } from '@/utils/http';
import { ElMessage } from 'element-plus';
import LanguageSelector from '@/components/common/LanguageSelector.vue';

// 使用通用模块的国际化
const { tc } = useModuleI18n('common');
const loading = ref(false);
const switching = ref(false);
const lastRequestInfo = ref<Record<string, unknown> | null>(null);

// 当前语言信息
const currentLanguage = computed(() => getCurrentLanguage());
const currentLanguageName = computed(() => getLanguageDisplayName(currentLanguage.value));

// HTTP请求语言代码
const httpLanguageCode = computed(() => {
  switch (currentLanguage.value) {
    case 'zh':
      return 'zh_CN';
    case 'en':
      return 'en_US';
    default:
      return 'zh_CN';
  }
});

// 监听语言变化
watch(currentLanguage, (newLang) => {
  console.log('语言已切换为:', newLang);
});

// 编程式语言切换
const switchToLanguage = async (locale: 'zh' | 'en') => {
  if (locale === currentLanguage.value || switching.value) return;

  switching.value = true;
  try {
    await switchLanguage(locale);
    ElMessage.success(`语言已切换为: ${locale === 'zh' ? '中文' : 'English'}`);
  } catch (error) {
    console.error('语言切换失败:', error);
    ElMessage.error('语言切换失败，请重试');
  } finally {
    switching.value = false;
  }
};

// 测试API请求
const testApiRequest = async () => {
  loading.value = true;
  try {
    // 模拟一个API请求
    const response = await http.get('/test', { testParam: 'value' });
    lastRequestInfo.value = {
      language: currentLanguage.value,
      httpLanguageCode: httpLanguageCode.value,
      timestamp: new Date().toISOString(),
      response: response || 'Mock response'
    };
  } catch (error: unknown) {
    lastRequestInfo.value = {
      language: currentLanguage.value,
      httpLanguageCode: httpLanguageCode.value,
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Request failed'
    };
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped lang="scss">
.language-test-example {
  padding: 20px;

  .test-content {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .language-info,
    .language-switcher,
    .translation-test,
    .api-test {
      padding: 16px;
      border: 1px solid var(--el-border-color);
      border-radius: 6px;
      background: var(--el-bg-color-page);

      h3, h4 {
        margin: 0 0 12px 0;
        color: var(--el-text-color-primary);
      }

      p {
        margin: 8px 0;
        color: var(--el-text-color-regular);
      }
    }

    .manual-switch {
      h4 {
        margin: 8px 0;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }

    .request-info {
      margin-top: 12px;

      pre {
        background: var(--el-fill-color-light);
        padding: 12px;
        border-radius: 4px;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
      }
    }
  }
}
</style>
