<template>
  <el-dialog
    :model-value="visible"
    :title="t('createWorkOrderModal.title')"
    width="1000px"
    :before-close="handleClose"
    :modal="false"
    class="work-order-create-modal"
  >
    <div class="modal-content">
      <!-- 环检单信息区（只读） -->
      <el-card class="mb-20 inspection-info-card">
        <template #header>
          <span>{{ t('createWorkOrderModal.inspectionInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <span class="label">{{ t('inspectionForm.form.inspectionNo') }}:</span>
              <span class="value">{{ inspectionFormData?.inspectionNo }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">{{ t('inspectionForm.form.inspectionStatus') }}:</span>
              <span class="value">{{ t(`inspectionForm.status.${inspectionFormData?.inspectionStatus}`) }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">{{ t('inspectionForm.form.createTime') }}:</span>
              <span class="value">{{ inspectionFormData?.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <span class="label">{{ t('inspectionForm.table.serviceAdvisor') }}:</span>
              <span class="value">{{ inspectionFormData?.serviceAdvisor }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户和车辆信息区 -->
      <el-row :gutter="20" class="mb-20">
        <el-col :span="12">
          <el-card class="customer-info-card">
            <template #header>
              <span>{{ t('createWorkOrderModal.customerInfo') }}</span>
            </template>
            <div class="info-item">
              <span class="label">{{ t('inspectionForm.table.repairmanName') }}:</span>
              <span class="value">{{ inspectionFormData?.repairmanName }}</span>
            </div>
            <div class="info-item">
              <span class="label">{{ t('inspectionForm.table.repairmanPhone') }}:</span>
              <span class="value">{{ inspectionFormData?.repairmanPhone }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="vehicle-info-card">
            <template #header>
              <span>{{ t('createWorkOrderModal.vehicleInfo') }}</span>
            </template>
            <el-row :gutter="10">
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">{{ t('inspectionForm.table.licensePlateNo') }}:</span>
                  <span class="value">{{ inspectionFormData?.licensePlateNo }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">{{ t('createWorkOrderModal.form.vehicleModel') }}:</span>
                  <span class="value">{{ inspectionFormData?.vehicleModel }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">{{ t('createWorkOrderModal.form.vehicleConfig') }}:</span>
                  <span class="value">{{ inspectionFormData?.vehicleConfig }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">{{ t('inspectionForm.table.color') }}:</span>
                  <span class="value">{{ inspectionFormData?.color }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">{{ t('createWorkOrderModal.form.mileage') }}:</span>
                  <span class="value">{{ inspectionFormData?.mileage }} {{ t('createWorkOrderModal.statistics.km') }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">{{ t('createWorkOrderModal.form.vehicleAge') }}:</span>
                  <span class="value">{{ inspectionFormData?.vehicleAge }} {{ t('createWorkOrderModal.statistics.months') }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>

      <!-- 工单基本信息 -->
      <el-card class="mb-20 work-order-basic-card">
        <template #header>
          <span>{{ t('createWorkOrderModal.workOrderBasic') }}</span>
        </template>
        <el-form :model="workOrderForm" class="work-order-form" label-position="top">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('createWorkOrderModal.form.workOrderType')">
                <el-radio-group v-model="workOrderForm.workOrderType">
                  <el-radio value="maintenance">{{ t('createWorkOrderModal.workOrderTypes.maintenance') }}</el-radio>
                  <el-radio value="repair">{{ t('createWorkOrderModal.workOrderTypes.repair') }}</el-radio>
                  <el-radio value="claim">{{ t('createWorkOrderModal.workOrderTypes.claim') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('createWorkOrderModal.form.priority')">
                <el-radio-group v-model="workOrderForm.priority">
                  <el-radio value="normal">{{ t('createWorkOrderModal.priorities.normal') }}</el-radio>
                  <el-radio value="urgent">{{ t('createWorkOrderModal.priorities.urgent') }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="t('createWorkOrderModal.form.remarks')">
                <el-input
                  v-model="workOrderForm.remarks"
                  type="textarea"
                  :rows="3"
                  :placeholder="t('createWorkOrderModal.placeholders.enterRemarks')"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 项目选择区 -->
      <el-card class="mb-20 project-selection-card">
        <template #header>
          <span>{{ t('createWorkOrderModal.projectSelection') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="16">
            <el-input
              v-model="projectSearchKeyword"
              :placeholder="t('createWorkOrderModal.placeholders.searchProject')"
              @keyup.enter="handleSearchProject"
            >
              <template #append>
                <el-button :icon="Search" @click="handleSearchProject" />
              </template>
            </el-input>
          </el-col>
          <el-col :span="8">
            <el-button type="primary" @click="handleSearchProject">
              {{ t('createWorkOrderModal.buttons.searchAdd') }}
            </el-button>
          </el-col>
        </el-row>
        <!-- 搜索结果 -->
        <div v-if="searchResults.length > 0" class="search-results mt-10">
          <el-tag
            v-for="result in searchResults"
            :key="result.projectId"
            class="mr-10 mb-10"
            type="info"
            :closable="false"
            @click="handleAddProject(result)"
            style="cursor: pointer"
          >
            {{ result.projectName }} - {{ result.projectCode }}
          </el-tag>
        </div>
      </el-card>

      <!-- 工时项目列表 -->
      <el-card class="mb-20 labor-items-card">
        <template #header>
          <div class="card-header">
            <span>{{ t('createWorkOrderModal.laborItems') }}</span>
            <div class="header-actions">
              <el-button size="small" type="primary" @click="handleAddLabor">
                {{ t('createWorkOrderModal.buttons.addLabor') }}
              </el-button>
              <el-button size="small" @click="handleClearAllLabor">
                {{ t('createWorkOrderModal.buttons.clearAll') }}
              </el-button>
            </div>
          </div>
        </template>
        <el-table :data="laborItems" style="width: 100%">
          <el-table-column :label="tc('index')" type="index" width="60" />
          <el-table-column :label="t('createWorkOrderModal.form.laborCode')" prop="laborCode" min-width="120" />
          <el-table-column :label="t('createWorkOrderModal.form.laborName')" prop="laborName" min-width="150" />
          <el-table-column :label="t('createWorkOrderModal.form.isClaim')" width="80">
            <template #default="scope">
              {{ scope.row.isClaim ? tc('yes') : tc('no') }}
            </template>
          </el-table-column>
          <el-table-column :label="t('createWorkOrderModal.form.isAdded')" width="80">
            <template #default="scope">
              {{ scope.row.isAdded ? tc('yes') : tc('no') }}
            </template>
          </el-table-column>
          <el-table-column :label="t('createWorkOrderModal.form.standardHours')" width="100">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.standardHours"
                :min="0.1"
                :max="99.9"
                :step="0.1"
                :precision="1"
                size="small"
                @change="calculateLaborSubtotal(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="t('createWorkOrderModal.form.unitPrice')" width="100">
            <template #default="scope">
              <span>{{ scope.row.unitPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="t('createWorkOrderModal.form.subtotal')" width="100">
            <template #default="scope">
              <span>{{ scope.row.subtotal.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="tc('operations')" width="60" fixed="right">
            <template #default="scope">
              <el-button type="danger" :icon="Delete" link @click="handleRemoveLabor(scope.$index)" />
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer">
          <span>{{ t('createWorkOrderModal.statistics.totalLaborHours') }}: {{ totalLaborHours.toFixed(1) }} {{ t('createWorkOrderModal.statistics.hours') }}</span>
          <span class="ml-20">{{ t('createWorkOrderModal.form.laborCost') }}: {{ laborCost.toFixed(2) }} {{ t('createWorkOrderModal.statistics.yuan') }}</span>
        </div>
      </el-card>

      <!-- 零件项目列表 -->
      <el-card class="mb-20 parts-items-card">
        <template #header>
          <div class="card-header">
            <span>{{ t('createWorkOrderModal.partsItems') }}</span>
            <div class="header-actions">
              <el-button size="small" type="primary" @click="handleAddParts">
                {{ t('createWorkOrderModal.buttons.addParts') }}
              </el-button>
              <el-button size="small" @click="handleClearAllParts">
                {{ t('createWorkOrderModal.buttons.clearAll') }}
              </el-button>
            </div>
          </div>
        </template>
        <el-table :data="partsItems" style="width: 100%">
          <el-table-column :label="tc('index')" type="index" width="60" />
          <el-table-column :label="t('createWorkOrderModal.form.partName')" prop="partName" min-width="150" />
          <el-table-column :label="t('createWorkOrderModal.form.isClaim')" width="80">
            <template #default="scope">
              {{ scope.row.isClaim ? tc('yes') : tc('no') }}
            </template>
          </el-table-column>
          <el-table-column :label="t('createWorkOrderModal.form.isAdded')" width="80">
            <template #default="scope">
              {{ scope.row.isAdded ? tc('yes') : tc('no') }}
            </template>
          </el-table-column>
          <el-table-column :label="t('createWorkOrderModal.form.availableStock')" width="100">
            <template #default="scope">
              <span :class="{ 'low-stock': scope.row.availableStock < 10 }">
                {{ scope.row.availableStock }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="t('createWorkOrderModal.form.quantity')" width="100">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.quantity"
                :min="1"
                :max="scope.row.availableStock"
                size="small"
                @change="calculatePartsSubtotal(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="t('createWorkOrderModal.form.unitPrice')" width="100">
            <template #default="scope">
              <span>{{ scope.row.unitPrice }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="t('createWorkOrderModal.form.subtotal')" width="100">
            <template #default="scope">
              <span>{{ scope.row.subtotal.toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="tc('operations')" width="60" fixed="right">
            <template #default="scope">
              <el-button type="danger" :icon="Delete" link @click="handleRemoveParts(scope.$index)" />
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer">
          <span>{{ t('createWorkOrderModal.statistics.totalPartsCount') }}: {{ totalPartsCount }} {{ t('createWorkOrderModal.statistics.pieces') }}</span>
          <span class="ml-20">{{ t('createWorkOrderModal.form.partsCost') }}: {{ partsCost.toFixed(2) }} {{ t('createWorkOrderModal.statistics.yuan') }}</span>
        </div>
      </el-card>

      <!-- 费用统计 -->
      <el-card class="mb-20 cost-summary-card">
        <template #header>
          <span>{{ t('createWorkOrderModal.costSummary') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="cost-item">
              <span class="label">{{ t('createWorkOrderModal.form.laborCost') }}:</span>
              <span class="value">{{ laborCost.toFixed(2) }} {{ t('createWorkOrderModal.statistics.yuan') }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="cost-item">
              <span class="label">{{ t('createWorkOrderModal.form.partsCost') }}:</span>
              <span class="value">{{ partsCost.toFixed(2) }} {{ t('createWorkOrderModal.statistics.yuan') }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="cost-item total-cost">
              <span class="label">{{ t('createWorkOrderModal.form.totalAmount') }}:</span>
              <span class="value">{{ totalAmount.toFixed(2) }} {{ t('createWorkOrderModal.statistics.yuan') }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 底部操作区 -->
    <template #footer>
      <div class="dialog-footer-buttons">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button @click="handleSaveDraft">{{ t('createWorkOrderModal.buttons.saveDraft') }}</el-button>
        <el-button type="primary" @click="handleCreateAndNotify">
          {{ t('createWorkOrderModal.buttons.createAndNotify') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { Search, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type {
  InspectionFormListItem,
  WorkOrderCreateForm,
  LaborItem,
  PartsItem,
  ProjectSearchItem
} from '@/types/module.d'

// 国际化 - 使用多个模块
const { t: tWorkOrder } = useModuleI18n('workOrder')
const { t: tInspectionForm } = useModuleI18n('inspectionForm')
const { t: tCommon } = useModuleI18n('common')

// 创建统一的翻译函数
const t = (key: string, params?: Record<string, string | number>) => {
  if (key.startsWith('inspectionForm.')) {
    return tInspectionForm(key.replace('inspectionForm.', ''), params)
  }
  return tWorkOrder(key, params)
}

// 通用翻译函数
const tc = (key: string, params?: Record<string, string | number>) => {
  return tCommon(key, params)
}

// Props 和 Emits
interface Props {
  modelValue: boolean
  inspectionFormData?: InspectionFormListItem
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  inspectionFormData: undefined
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'created': [workOrder: WorkOrderCreateForm]
  'saved': [workOrder: WorkOrderCreateForm]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 工单表单
const workOrderForm = reactive({
  workOrderType: 'maintenance' as 'maintenance' | 'repair' | 'claim',
  priority: 'normal' as 'normal' | 'urgent',
  remarks: ''
})

// 项目搜索
const projectSearchKeyword = ref('')
const searchResults = ref<ProjectSearchItem[]>([])

// 工时和零件项目
const laborItems = ref<LaborItem[]>([])
const partsItems = ref<PartsItem[]>([])

// 是否有未保存的变更
const hasUnsavedChanges = ref(false)

// 计算属性
const totalLaborHours = computed(() => {
  return laborItems.value.reduce((total, item) => total + item.standardHours, 0)
})

const laborCost = computed(() => {
  return laborItems.value.reduce((total, item) => total + item.subtotal, 0)
})

const totalPartsCount = computed(() => {
  return partsItems.value.reduce((total, item) => total + item.quantity, 0)
})

const partsCost = computed(() => {
  return partsItems.value.reduce((total, item) => total + item.subtotal, 0)
})

const totalAmount = computed(() => {
  return laborCost.value + partsCost.value
})

// 监听变更
watch([workOrderForm, laborItems, partsItems], () => {
  hasUnsavedChanges.value = true
}, { deep: true })

// 初始化默认值
watch(() => props.inspectionFormData, (newData) => {
  if (newData) {
    // 根据环检单的服务类型自动设置工单类型
    if (newData.serviceType === 'maintenance') {
      workOrderForm.workOrderType = 'maintenance'
    } else if (newData.serviceType === 'repair') {
      workOrderForm.workOrderType = 'repair'
    }

    // 自动生成备注
    workOrderForm.remarks = t('createWorkOrderModal.messages.autoGeneratedRemarks', { inspectionNo: newData.inspectionNo })

    hasUnsavedChanges.value = false
  }
}, { immediate: true })

// 项目搜索
const handleSearchProject = () => {
  if (!projectSearchKeyword.value.trim()) return

  // 模拟搜索结果
  const mockResults: ProjectSearchItem[] = [
    {
      projectId: '1',
      projectCode: 'L001',
      projectName: t('createWorkOrderModal.mockData.oilChange'),
      projectType: 'labor',
      standardHours: 1.0,
      unitPrice: 80
    },
    {
      projectId: '2',
      projectCode: 'P001',
      projectName: t('createWorkOrderModal.mockData.oilFilter'),
      projectType: 'parts',
      unitPrice: 25,
      availableStock: 50
    }
  ]

  searchResults.value = mockResults.filter(item =>
    item.projectName.includes(projectSearchKeyword.value) ||
    item.projectCode.includes(projectSearchKeyword.value)
  )
}

// 添加项目
const handleAddProject = (project: ProjectSearchItem) => {
  if (project.projectType === 'labor') {
    // 检查是否已存在
    const exists = laborItems.value.find(item => item.laborId === project.projectId)
    if (exists) {
      ElMessage.warning(t('createWorkOrderModal.messages.duplicateProject'))
      return
    }

    const laborItem: LaborItem = {
      laborId: project.projectId,
      laborCode: project.projectCode,
      laborName: project.projectName,
      type: workOrderForm.workOrderType,
      isClaim: false,
      isAdded: false,
      standardHours: project.standardHours || 1.0,
      unitPrice: project.unitPrice,
      subtotal: (project.standardHours || 1.0) * project.unitPrice
    }

    laborItems.value.push(laborItem)
    ElMessage.success(t('createWorkOrderModal.messages.projectAdded'))
  } else {
    // 检查是否已存在
    const exists = partsItems.value.find(item => item.partId === project.projectId)
    if (exists) {
      ElMessage.warning(t('createWorkOrderModal.messages.duplicateProject'))
      return
    }

    const partsItem: PartsItem = {
      partId: project.projectId,
      partCode: project.projectCode,
      partName: project.projectName,
      isClaim: false,
      isAdded: false,
      availableStock: project.availableStock || 0,
      quantity: 1,
      unitPrice: project.unitPrice,
      subtotal: project.unitPrice
    }

    partsItems.value.push(partsItem)
    ElMessage.success(t('createWorkOrderModal.messages.projectAdded'))
  }

  // 清空搜索
  projectSearchKeyword.value = ''
  searchResults.value = []
}

// 工时相关操作
const handleAddLabor = () => {
  // 这里可以打开一个选择工时项目的对话框
  ElMessage.info(t('createWorkOrderModal.messages.useSearchForLabor'))
}

const handleClearAllLabor = () => {
  if (laborItems.value.length === 0) return

  ElMessageBox.confirm(
    t('createWorkOrderModal.messages.confirmClearAll'),
    tc('tip'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning'
    }
  ).then(() => {
    laborItems.value = []
    ElMessage.success(tc('operationSuccessful'))
  })
}

const handleRemoveLabor = (index: number) => {
  laborItems.value.splice(index, 1)
  ElMessage.success(t('createWorkOrderModal.messages.projectRemoved'))
}

const calculateLaborSubtotal = (item: LaborItem) => {
  item.subtotal = item.standardHours * item.unitPrice
}

// 零件相关操作
const handleAddParts = () => {
  // 这里可以打开一个选择零件项目的对话框
  ElMessage.info(t('createWorkOrderModal.messages.useSearchForParts'))
}

const handleClearAllParts = () => {
  if (partsItems.value.length === 0) return

  ElMessageBox.confirm(
    t('createWorkOrderModal.messages.confirmClearAll'),
    tc('tip'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning'
    }
  ).then(() => {
    partsItems.value = []
    ElMessage.success(tc('operationSuccessful'))
  })
}

const handleRemoveParts = (index: number) => {
  partsItems.value.splice(index, 1)
  ElMessage.success(t('createWorkOrderModal.messages.projectRemoved'))
}

const calculatePartsSubtotal = (item: PartsItem) => {
  if (item.quantity > item.availableStock) {
    ElMessage.warning(t('createWorkOrderModal.messages.quantityExceeded'))
    item.quantity = item.availableStock
  }
  item.subtotal = item.quantity * item.unitPrice
}

// 保存草稿
const handleSaveDraft = () => {
  if (!props.inspectionFormData) return

  const workOrderData: WorkOrderCreateForm = {
    inspectionFormData: props.inspectionFormData,
    workOrderType: workOrderForm.workOrderType,
    priority: workOrderForm.priority,
    remarks: workOrderForm.remarks,
    laborItems: laborItems.value,
    partsItems: partsItems.value
  }

  emit('saved', workOrderData)
  hasUnsavedChanges.value = false
  ElMessage.success(t('createWorkOrderModal.messages.saveDraftSuccess'))
}

// 创建并推送客户
const handleCreateAndNotify = () => {
  if (!props.inspectionFormData) return

  // 基础校验
  if (!workOrderForm.workOrderType) {
    ElMessage.warning(t('createWorkOrderModal.placeholders.selectWorkOrderType'))
    return
  }

  if (laborItems.value.length === 0 && partsItems.value.length === 0) {
    ElMessage.warning(t('createWorkOrderModal.messages.addAtLeastOneItem'))
    return
  }

  ElMessageBox.confirm(
    t('createWorkOrderModal.messages.confirmCreateAndNotify'),
    tc('tip'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'info'
    }
  ).then(() => {
    const workOrderData: WorkOrderCreateForm = {
      inspectionFormData: props.inspectionFormData!,
      workOrderType: workOrderForm.workOrderType,
      priority: workOrderForm.priority,
      remarks: workOrderForm.remarks,
      laborItems: laborItems.value,
      partsItems: partsItems.value
    }

    emit('created', workOrderData)
    hasUnsavedChanges.value = false
    ElMessage.success(t('createWorkOrderModal.messages.createSuccess'))
    visible.value = false
  })
}

// 关闭处理
const handleClose = () => {
  if (hasUnsavedChanges.value) {
    ElMessageBox.confirm(
      t('createWorkOrderModal.messages.unsavedChanges'),
      tc('tip'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    ).then(() => {
      visible.value = false
      resetForm()
    })
  } else {
    visible.value = false
    resetForm()
  }
}

// 重置表单
const resetForm = () => {
  workOrderForm.workOrderType = 'maintenance'
  workOrderForm.priority = 'normal'
  workOrderForm.remarks = ''
  laborItems.value = []
  partsItems.value = []
  projectSearchKeyword.value = ''
  searchResults.value = []
  hasUnsavedChanges.value = false
}

// 对外暴露方法
defineExpose({
  open: () => {
    visible.value = true
    resetForm()
  }
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.work-order-create-modal {
  .modal-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .inspection-info-card,
  .customer-info-card,
  .vehicle-info-card,
  .work-order-basic-card,
  .project-selection-card,
  .labor-items-card,
  .parts-items-card,
  .cost-summary-card {
    margin-bottom: 20px;
  }

  .info-item {
    display: flex;
    margin-bottom: 10px;

    .label {
      font-weight: 500;
      color: #606266;
      margin-right: 10px;
      min-width: 80px;
    }

    .value {
      color: #303133;
    }
  }

  .work-order-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .search-results {
    .el-tag {
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .table-footer {
    margin-top: 10px;
    text-align: right;
    font-weight: 500;
    color: #606266;

    .ml-20 {
      margin-left: 20px;
    }
  }

  .cost-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .label {
      font-weight: 500;
      color: #606266;
      margin-right: 10px;
    }

    .value {
      color: #303133;
      font-weight: 500;
    }

    &.total-cost {
      .label,
      .value {
        font-size: 16px;
        font-weight: 600;
        color: #E6A23C;
      }
    }
  }

  .low-stock {
    color: #F56C6C;
    font-weight: 600;
  }

  .dialog-footer-buttons {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;

    .el-button {
      margin-left: 10px;
    }
  }
}

// 表格样式
:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-20 {
  margin-left: 20px;
}
</style>
