<template>
  <div ref="chartContainer" class="chart-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
import type { ECharts } from 'echarts';

interface TrendData {
  date: string;
  stock: number;
}

interface Props {
  data: TrendData[];
  safetyStockLine?: number;
  height?: string;
}

const props = withDefaults(defineProps<Props>(), {
  safetyStockLine: 0,
  height: '300px'
});

const chartContainer = ref<HTMLElement>();
let chartInstance: ECharts | null = null;

const initChart = () => {
  if (!chartContainer.value) return;
  
  chartInstance = echarts.init(chartContainer.value);
  updateChart();
};

const updateChart = () => {
  if (!chartInstance || !props.data.length) return;

  const dates = props.data.map(item => item.date);
  const stocks = props.data.map(item => item.stock);
  const maxStock = Math.max(...stocks, props.safetyStockLine || 0);
  const minStock = Math.min(...stocks, props.safetyStockLine || 0);
  const yAxisMax = Math.ceil(maxStock * 1.2);
  const yAxisMin = Math.max(0, Math.floor(minStock * 0.8));

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: (params: any) => {
        const param = params[0];
        return `${param.axisValue}<br/>库存数量: ${param.value}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
      axisLabel: {
        fontSize: 12,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: yAxisMin,
      max: yAxisMax,
      axisLabel: {
        fontSize: 12,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa'
        }
      }
    },
    series: [
      {
        name: '库存数量',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#409eff',
          width: 2
        },
        itemStyle: {
          color: '#409eff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
            ]
          }
        },
        data: stocks
      },
      // 安全库存线
      ...(props.safetyStockLine > 0 ? [{
        name: '安全库存线',
        type: 'line',
        symbol: 'none',
        lineStyle: {
          color: '#e6a23c',
          width: 2,
          type: 'dashed'
        },
        data: new Array(dates.length).fill(props.safetyStockLine),
        markLine: {
          silent: true,
          symbol: 'none',
          label: {
            show: true,
            position: 'middle',
            formatter: `安全库存线 (${props.safetyStockLine})`,
            color: '#e6a23c',
            fontSize: 12
          },
          lineStyle: {
            color: '#e6a23c',
            type: 'dashed',
            width: 2
          },
          data: [
            {
              yAxis: props.safetyStockLine
            }
          ]
        }
      }] : [])
    ]
  };

  chartInstance.setOption(option);
};

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

watch(() => props.data, () => {
  nextTick(() => {
    updateChart();
  });
}, { deep: true });

watch(() => props.safetyStockLine, () => {
  nextTick(() => {
    updateChart();
  });
});

onMounted(() => {
  nextTick(() => {
    initChart();
  });

  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', resizeChart);
});
</script>

<style scoped lang="scss">
.chart-container {
  width: 100%;
  height: v-bind(height);
  min-height: 200px;
}
</style>