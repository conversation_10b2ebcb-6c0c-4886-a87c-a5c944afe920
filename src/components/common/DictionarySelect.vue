<template>
  <el-select
    :model-value="modelValue"
    @update:model-value="handleChange"
    :placeholder="placeholder"
    :clearable="clearable"
    :multiple="multiple"
    :disabled="disabled"
    :loading="loading || dictionaryLoading"
    :size="size"
    :style="style"
    v-bind="$attrs"
  >
    <!-- 全部选项 -->
    <el-option 
      v-if="showAll && !multiple"
      :value="allValue" 
      :label="allLabel" 
    />
    
    <!-- 字典选项 -->
    <el-option
      v-for="option in options"
      :key="option.code"
      :value="option.code"
      :label="option.name"
    />
  </el-select>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useDictionary } from '@/composables/useDictionary';
import type { DictionaryType } from '@/constants/dictionary';

interface Props {
  modelValue?: string | string[] | number | number[];
  dictionaryType: DictionaryType;
  placeholder?: string;
  clearable?: boolean;
  multiple?: boolean;
  disabled?: boolean;
  loading?: boolean;
  size?: 'large' | 'default' | 'small';
  style?: string | Record<string, any>;
  showAll?: boolean;
  allLabel?: string;
  allValue?: string | number;
}

interface Emits {
  (e: 'update:modelValue', value: string | string[] | number | number[]): void;
  (e: 'change', value: string | string[] | number | number[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择',
  clearable: true,
  multiple: false,
  disabled: false,
  loading: false,
  size: 'default',
  showAll: false,
  allLabel: '全部',
  allValue: '',
});

const emit = defineEmits<Emits>();

// 使用字典数据
const { 
  options, 
  loading: dictionaryLoading 
} = useDictionary(props.dictionaryType);

// 处理值变化
const handleChange = (value: string | string[] | number | number[]) => {
  emit('update:modelValue', value);
  emit('change', value);
};
</script>

<script lang="ts">
export default {
  name: 'DictionarySelect',
  inheritAttrs: false,
};
</script>
