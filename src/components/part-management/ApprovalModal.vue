<template>
  <el-form :model="form" label-width="auto">
    <el-form-item :label="$t('partManagementHQ.requisitionNumber')">
      <el-input v-model="requisitionData.requisitionNumber" disabled></el-input>
    </el-form-item>
    <el-form-item :label="$t('partManagementHQ.requisitionDate')">
      <el-input v-model="requisitionData.requisitionDate" disabled></el-input>
    </el-form-item>
    <el-form-item :label="$t('partManagementHQ.approvalResult')">
      <el-select v-model="form.approvalResult" :placeholder="$t('partManagementHQ.selectApprovalResult')">
        <el-option :label="$t('partManagementHQ.statusApproved')" value="approved"></el-option>
        <el-option :label="$t('partManagementHQ.statusRejected')" value="rejected"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-if="form.approvalResult === 'rejected'" :label="$t('partManagementHQ.rejectionReason')" prop="rejectionReason" :rules="[{ required: true, message: $t('partManagementHQ.rejectionReasonRequired'), trigger: 'blur' }]" >
      <el-input
        v-model="form.rejectionReason"
        type="textarea"
        :rows="3"
        :placeholder="$t('partManagementHQ.enterRejectionReason')"
        maxlength="1000"
        show-word-limit
      ></el-input>
    </el-form-item>
  </el-form>

  <h4 class="mt-4">{{ $t('partManagementHQ.requisitionDetails') }}</h4>
  <el-table :data="requisitionData.detailList" style="width: 100%;" border class="mt-2">
    <el-table-column type="index" :label="$t('common.sequence')" width="60"></el-table-column>
    <el-table-column prop="partName" :label="$t('partManagementHQ.partName')" min-width="120"></el-table-column>
    <el-table-column prop="partNumber" :label="$t('partManagementHQ.partNumber')" min-width="120"></el-table-column>
    <el-table-column prop="quantity" :label="$t('partManagementHQ.quantity')" width="80"></el-table-column>
    <el-table-column prop="unit" :label="$t('partManagementHQ.unit')" width="80"></el-table-column>
    <el-table-column prop="requisitionStatus" :label="$t('partManagementHQ.requisitionStatus')" min-width="100">
      <template #default="{ row }">
        <el-tag :type="getStatusTagType(row.requisitionStatus)">{{ getStatusLabel(row.requisitionStatus) }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="requisitionDate" :label="$t('partManagementHQ.requisitionDate')" min-width="120"></el-table-column>
    <el-table-column prop="expectedArrivalTime" :label="$t('partManagementHQ.expectedArrivalTime')" min-width="140"></el-table-column>
    <el-table-column prop="supplierName" :label="$t('partManagementHQ.supplierName')" min-width="120"></el-table-column>
  </el-table>

  <div class="dialog-footer mt-4" style="text-align: right;">
    <el-button @click="handleCancel">{{ $t('common.cancel') }}</el-button>
    <el-button type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</el-button>
  </div>
</template>

<script setup lang="ts">
import { reactive, defineProps, defineEmits, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';

const { t } = useI18n();

const props = defineProps({
  requisitionData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['submitSuccess', 'cancel']);

const form = reactive({
  approvalResult: '',
  rejectionReason: ''
});

// Watch for changes in requisitionData to reset form when modal opens with new data
watch(() => props.requisitionData, (newVal) => {
  if (newVal) {
    form.approvalResult = '';
    form.rejectionReason = '';
  }
}, { immediate: true });

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'submitted':
      return ''; // 普通标签
    case 'approved':
      return 'success'; // 成功标签
    case 'rejected':
      return 'danger'; // 危险标签
    case 'shipped':
      return 'warning'; // 警示标签
    case 'received':
      return 'success'; // 成功标签
    case 'cancelled':
      return ''; // 普通标签
    default:
      return '';
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'submitted':
      return t('partManagementHQ.statusSubmitted');
    case 'approved':
      return t('partManagementHQ.statusApproved');
    case 'rejected':
      return t('partManagementHQ.statusRejected');
    case 'shipped':
      return t('partManagementHQ.statusShipped');
    case 'received':
      return t('partManagementHQ.statusReceived');
    case 'cancelled':
      return t('partManagementHQ.statusCancelled');
    default:
      return status;
  }
};

const handleSubmit = () => {
  if (form.approvalResult === 'rejected' && !form.rejectionReason) {
    ElMessage.error(t('partManagementHQ.rejectionReasonRequired'));
    return;
  }
  if (!form.approvalResult) {
    ElMessage.error(t('partManagementHQ.selectApprovalResult'));
    return;
  }

  console.log('审批提交', { ...props.requisitionData, ...form });
  ElMessage.success(t('partManagementHQ.approvalSuccess'));
  emit('submitSuccess');
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<style scoped>
.mt-2 {
  margin-top: 10px;
}
.mt-4 {
  margin-top: 20px;
}
</style> 