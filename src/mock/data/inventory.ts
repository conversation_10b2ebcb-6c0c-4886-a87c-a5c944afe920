import type { InventoryListItem } from '@/api/modules/inventory'

// 模拟库存数据 - 包含与报损记录相关的零件
const mockInventoryList: InventoryListItem[] = [
  // 与报损记录相关的零件
  {
    id: 1,
    partName: '刹车片',
    partNumber: 'BP001',
    supplierName: '博世汽车配件',
    totalInventory: 95, // 原本100，报损了5个
    availableInventory: 80,
    lockedInventory: 10,
    defectiveCount: 5, // 包含报损的数量
    pendingCount: 0,
    safetyStock: 20,
    inventoryStatus: 'normal'
  },
  {
    id: 2,
    partName: '机油滤清器',
    partNumber: 'OF002',
    supplierName: '曼牌滤清器',
    totalInventory: 149, // 原本150，报损了1个
    availableInventory: 120,
    lockedInventory: 25,
    defectiveCount: 4, // 包含报损的数量
    pendingCount: 15,
    safetyStock: 30,
    inventoryStatus: 'normal'
  },
  {
    id: 3,
    partName: '火花塞',
    partNumber: 'SP001',
    supplierName: 'NGK火花塞',
    totalInventory: 196, // 原本200，报损了4个
    availableInventory: 150,
    lockedInventory: 30,
    defectiveCount: 16, // 包含报损的数量
    pendingCount: 20,
    safetyStock: 50,
    inventoryStatus: 'normal'
  },
  {
    id: 4,
    partName: '空气滤清器',
    partNumber: 'AF001',
    supplierName: '马勒滤清器',
    totalInventory: 98, // 原本100，报损了2个
    availableInventory: 75,
    lockedInventory: 15,
    defectiveCount: 8, // 包含报损的数量
    pendingCount: 10,
    safetyStock: 25,
    inventoryStatus: 'normal'
  },
  {
    id: 5,
    partName: '机油',
    partNumber: 'OIL001',
    supplierName: '美孚润滑油',
    totalInventory: 297, // 原本300，报损了3个
    availableInventory: 250,
    lockedInventory: 35,
    defectiveCount: 12, // 包含报损的数量
    pendingCount: 25,
    safetyStock: 60,
    inventoryStatus: 'normal'
  },
  // 其他常规库存数据
  ...Array.from({ length: 95 }, (_, index) => ({
    id: index + 6,
    partName: `零件${index + 6}`,
    partNumber: `PN${String(index + 6).padStart(6, '0')}`,
    supplierName: `供应商${index % 8 + 1}`,
    totalInventory: Math.floor(Math.random() * 1000) + 50,
    availableInventory: Math.floor(Math.random() * 800) + 30,
    lockedInventory: Math.floor(Math.random() * 100) + 5,
    defectiveCount: Math.floor(Math.random() * 30),
    pendingCount: Math.floor(Math.random() * 50),
    safetyStock: Math.floor(Math.random() * 100) + 20,
    inventoryStatus: Math.random() > 0.85 ? 'belowSafety' : 'normal'
  }))
]

// 模拟API响应
export const getInventoryList = (params: any) => {
  const { page, pageSize, partName, partNumber, supplierName, inventoryStatus } = params

  // 过滤数据
  let filteredData = [...mockInventoryList]

  if (partName) {
    filteredData = filteredData.filter(item =>
      item.partName.toLowerCase().includes(partName.toLowerCase())
    )
  }

  if (partNumber) {
    filteredData = filteredData.filter(item =>
      item.partNumber.toLowerCase().includes(partNumber.toLowerCase())
    )
  }

  if (supplierName) {
    filteredData = filteredData.filter(item =>
      item.supplierName.toLowerCase().includes(supplierName.toLowerCase())
    )
  }

  if (inventoryStatus) {
    filteredData = filteredData.filter(item =>
      item.inventoryStatus === inventoryStatus
    )
  }

  // 分页
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const paginatedData = filteredData.slice(start, end)

  return {
    code: 200,
    data: {
      list: paginatedData,
      total: filteredData.length,
      page,
      pageSize
    },
    message: 'success'
  }
}
