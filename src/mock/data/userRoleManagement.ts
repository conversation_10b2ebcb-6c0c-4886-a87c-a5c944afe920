import type { ApiResponse, PageResponse } from '@/types/permission'

// 用户类型定义
export interface ExtendedUser {
  id: string
  username: string
  fullName: string
  phone: string
  email: string
  userStatus: 'normal' | 'disabled' | 'locked'
  userType: 'admin' | 'factory' | 'store'
  entryDate: string
  createTime?: string
  lastLoginTime?: string
  avatar?: string
  primaryStoreId?: string
  primaryStoreName?: string
  storeRoles?: UserStoreRole[]
}

// 角色类型定义
export interface ExtendedRole {
  id: string
  roleName: string
  roleCode: string
  roleType: 'system' | 'business' | 'custom'
  roleStatus: 'normal' | 'disabled'
  roleScope: 'all' | 'custom' | 'department' | 'departmentAndBelow' | 'onlyPersonal'
  description?: string
  roleSource: 'factory' | 'store'
  storeId?: string
  belongStoreName?: string
  createTime?: string
  updateTime?: string
  menuIds?: string[]
  deptIds?: string[]
}

// 用户门店角色关系
export interface UserStoreRole {
  id: string
  userId: string
  storeId: string
  storeName?: string
  departmentId: string
  departmentName?: string
  position: string
  roleIds: string[]
  roleName?: string
  roleType?: 'system' | 'business' | 'custom'
  isPrimary: boolean
  createTime?: string
}

// 门店数据
export const mockStores = [
  {
    id: 'store_1',
    storeCode: 'HQ',
    storeName: '总部',
    storeStatus: 'active',
    createTime: '2023-01-01T00:00:00Z',
  },
  {
    id: 'store_2',
    storeCode: 'SH001',
    storeName: '上海旗舰店',
    storeStatus: 'active',
    createTime: '2023-01-16T11:00:00Z',
  },
  {
    id: 'store_3',
    storeCode: 'BJ001',
    storeName: '北京体验店',
    storeStatus: 'active',
    createTime: '2023-01-17T12:00:00Z',
  },
  {
    id: 'store_4',
    storeCode: 'GZ001',
    storeName: '广州专卖店',
    storeStatus: 'active',
    createTime: '2023-02-10T09:00:00Z',
  }
]

// 角色数据
export const mockRoles: ExtendedRole[] = [
  // 厂端系统角色 - 所有门店可见
  {
    id: 'role_1',
    roleName: '系统管理员',
    roleCode: 'SYSTEM_ADMIN',
    roleType: 'system',
    roleStatus: 'normal',
    roleScope: 'all',
    description: '拥有系统所有权限',
    roleSource: 'factory',
    createTime: '2023-01-01T00:00:00Z',
    menuIds: ['1', '1-1', '1-2'], // 系统管理及子菜单
  },
  {
    id: 'role_2',
    roleName: '销售经理',
    roleCode: 'SALES_MANAGER',
    roleType: 'business',
    roleStatus: 'normal',
    roleScope: 'department',
    description: '负责销售部门管理',
    roleSource: 'factory',
    createTime: '2023-01-01T00:00:00Z',
    menuIds: ['2', '2-1'], // 销售管理及订单管理
  },
  {
    id: 'role_3',
    roleName: '销售顾问',
    roleCode: 'SALES_CONSULTANT',
    roleType: 'business',
    roleStatus: 'normal',
    roleScope: 'onlyPersonal',
    description: '负责客户接待和车辆销售',
    roleSource: 'factory',
    createTime: '2023-01-01T00:00:00Z',
    menuIds: ['2-1'], // 仅订单管理
  },
  {
    id: 'role_4',
    roleName: '售后主管',
    roleCode: 'SERVICE_MANAGER',
    roleType: 'business',
    roleStatus: 'normal',
    roleScope: 'department',
    description: '负责售后服务部门管理',
    roleSource: 'factory',
    createTime: '2023-01-01T00:00:00Z',
  },

  // 门店自定义角色 - 仅当前门店可见
  {
    id: 'role_5',
    roleName: '上海店销售组长',
    roleCode: 'SH_SALES_LEADER',
    roleType: 'custom',
    roleStatus: 'normal',
    roleScope: 'departmentAndBelow',
    description: '上海店销售团队组长',
    roleSource: 'store',
    storeId: 'store_2',
    belongStoreName: '上海旗舰店',
    createTime: '2023-01-16T11:30:00Z',
  },
  {
    id: 'role_6',
    roleName: '上海店库存管理员',
    roleCode: 'SH_INVENTORY_ADMIN',
    roleType: 'custom',
    roleStatus: 'normal',
    roleScope: 'onlyPersonal',
    description: '上海店库存专员',
    roleSource: 'store',
    storeId: 'store_2',
    belongStoreName: '上海旗舰店',
    createTime: '2023-01-16T11:35:00Z',
  },
  {
    id: 'role_7',
    roleName: '北京店VIP顾问',
    roleCode: 'BJ_VIP_CONSULTANT',
    roleType: 'custom',
    roleStatus: 'normal',
    roleScope: 'onlyPersonal',
    description: '北京店VIP客户专属顾问',
    roleSource: 'store',
    storeId: 'store_3',
    belongStoreName: '北京体验店',
    createTime: '2023-01-17T12:30:00Z',
  }
]

// 用户数据
export const mockUsers: ExtendedUser[] = [
  // 管理员用户
  // 管理员用户
  {
    id: 'user_1',
    username: 'admin',
    fullName: '系统管理员',
    phone: '13800138000',
    email: '<EMAIL>',
    userStatus: 'normal',
    userType: 'admin',
    entryDate: '2023-01-01',
    createTime: '2023-01-01T00:00:00Z',
    lastLoginTime: '2023-06-01T10:00:00Z',
    primaryStoreId: 'store_1',
    primaryStoreName: '总部',
    storeRoles: [
      {
        id: 'usr_1',
        userId: 'user_1',
        storeId: 'store_1',
        storeName: '总部',
        departmentId: 'dept_1',
        departmentName: '技术部',
        position: '系统管理员',
        roleIds: ['role_1'],
        roleName: '系统管理员',
        isPrimary: true,
        createTime: '2023-01-01T00:00:00Z'
      }
    ]
  },

  // 厂端用户
  {
    id: 'user_2',
    username: 'factory_manager',
    fullName: '张厂长',
    phone: '13900139001',
    email: '<EMAIL>',
    userStatus: 'normal',
    userType: 'factory',
    entryDate: '2023-01-05',
    createTime: '2023-01-05T09:00:00Z',
    lastLoginTime: '2023-06-02T09:30:00Z',
    primaryStoreId: 'store_1',
    primaryStoreName: '总部',
    storeRoles: [
      {
        id: 'usr_2',
        userId: 'user_2',
        storeId: 'store_1',
        storeName: '总部',
        departmentId: 'dept_1',
        departmentName: '管理部',
        position: '厂端经理',
        roleId: 'role_2',
        roleName: '销售经理',
        isPrimary: true,
        createTime: '2023-01-05T09:00:00Z'
      }
    ]
  },

  // 店端用户 - 在多家门店拥有不同角色
  {
    id: 'user_3',
    username: 'store_sales',
    fullName: '李销售',
    phone: '13900139002',
    email: '<EMAIL>',
    userStatus: 'normal',
    userType: 'store',
    entryDate: '2023-01-10',
    createTime: '2023-01-10T09:00:00Z',
    lastLoginTime: '2023-06-03T14:20:00Z',
    primaryStoreId: 'store_2',
    primaryStoreName: '上海旗舰店',
    storeRoles: [
      {
        id: 'usr_3_1',
        userId: 'user_3',
        storeId: 'store_2',
        storeName: '上海旗舰店',
        departmentId: 'dept_2_1',
        departmentName: '销售部',
        position: '高级销售顾问',
        roleId: 'role_3',
        roleName: '销售顾问',
        isPrimary: true,
        createTime: '2023-01-10T09:00:00Z'
      },
      {
        id: 'usr_3_2',
        userId: 'user_3',
        storeId: 'store_2',
        storeName: '上海旗舰店',
        departmentId: 'dept_2_1',
        departmentName: '销售部',
        position: '销售组长',
        roleId: 'role_5',
        roleName: '上海店销售组长',
        isPrimary: false,
        createTime: '2023-01-16T11:40:00Z'
      },
      {
        id: 'usr_3_3',
        userId: 'user_3',
        storeId: 'store_3',
        storeName: '北京体验店',
        departmentId: 'dept_3_1',
        departmentName: '销售部',
        position: '销售顾问',
        roleId: 'role_3',
        roleName: '销售顾问',
        isPrimary: false,
        createTime: '2023-02-01T10:00:00Z'
      }
    ]
  }
]

// API响应处理
// 获取角色列表（根据门店筛选）
export function getRoles(params: any = {}): ApiResponse<PageResponse<ExtendedRole>> {
  let filteredRoles = [...mockRoles];

  if (params.storeId) {
    filteredRoles = filteredRoles.filter(role =>
      role.roleSource === 'factory' ||
      (role.roleSource === 'store' && role.storeId === params.storeId)
    );
  }

  if (params.roleName) {
    filteredRoles = filteredRoles.filter(role =>
      role.roleName.includes(params.roleName)
    );
  }

  if (params.roleType) {
    filteredRoles = filteredRoles.filter(role =>
      role.roleType === params.roleType
    );
  }

  if (params.roleSource) {
    filteredRoles = filteredRoles.filter(role =>
      role.roleSource === params.roleSource
    );
  }

  const current = params.current || 1;
  const size = params.size || 10;
  const total = filteredRoles.length;
  const pages = Math.ceil(total / size);
  const start = (current - 1) * size;
  const end = start + size;
  const records = filteredRoles.slice(start, end);

  return {
    code: 200,
    message: '获取成功',
    data: { records, current, size, total, pages },
    timestamp: Date.now()
  };
}

// 获取用户列表
export function getUsers(params: any = {}): ApiResponse<PageResponse<ExtendedUser>> {
  let filteredUsers = [...mockUsers]

  if (params.username) {
    filteredUsers = filteredUsers.filter(user =>
      user.username.includes(params.username) || user.fullName.includes(params.username)
    )
  }

  if (params.userType) {
    filteredUsers = filteredUsers.filter(user => user.userType === params.userType)
  }

  if (params.storeId) {
    filteredUsers = filteredUsers.filter(user =>
      user.storeRoles?.some(sr => sr.storeId === params.storeId)
    )
  }

  const current = params.current || 1;
  const size = params.size || 10;
  const total = filteredUsers.length;
  const pages = Math.ceil(total / size);
  const start = (current - 1) * size;
  const end = start + size;
  const records = filteredUsers.slice(start, end);

  return {
    code: 200,
    message: '获取成功',
    data: { records, current, size, total, pages },
    timestamp: Date.now()
  };
}

// 获取用户详情
export function getUserDetail(userId: string): ApiResponse<ExtendedUser | undefined> {
  const user = mockUsers.find(u => u.id === userId);
  return {
    code: user ? 200 : 404,
    message: user ? '获取成功' : '用户不存在',
    data: user,
    timestamp: Date.now()
  };
}

// 分配用户角色
export function assignUserRoles(
  userId: string,
  storeRolesData: UserStoreRole[]
): ApiResponse<ExtendedUser | undefined> {
  const userIndex = mockUsers.findIndex(u => u.id === userId);
  if (userIndex === -1) {
    return { code: 404, message: '用户不存在', data: undefined, timestamp: Date.now() };
  }

  const user = mockUsers[userIndex];
  user.storeRoles = storeRolesData;

  const primaryRole = storeRolesData.find(sr => sr.isPrimary);
  if (primaryRole) {
    user.primaryStoreId = primaryRole.storeId;
    user.primaryStoreName = primaryRole.storeName;
  } else {
    user.primaryStoreId = undefined;
    user.primaryStoreName = undefined;
  }

  return { code: 200, message: '分配成功', data: user, timestamp: Date.now() };
}

// 获取用户在指定门店的角色
export function getUserRolesByStore(userId: string, storeId: string): ApiResponse<UserStoreRole[]> {
  const user = mockUsers.find(u => u.id === userId);
  if (!user || !user.storeRoles) {
    return {
      code: 200,
      message: '获取成功',
      data: [],
      timestamp: Date.now()
    };
  }

  const roles = user.storeRoles.filter(sr => sr.storeId === storeId);

  return {
    code: 200,
    message: '获取成功',
    data: roles,
    timestamp: Date.now()
  };
}

// 获取用户所有的门店和角色
export function getAllUserStoreRoles(userId: string): ApiResponse<UserStoreRole[]> {
  const user = mockUsers.find(u => u.id === userId);
  if (!user || !user.storeRoles) {
    return {
      code: 200,
      message: '获取成功',
      data: [],
      timestamp: Date.now()
    };
  }

  return {
    code: 200,
    message: '获取成功',
    data: user.storeRoles,
    timestamp: Date.now()
  };
}

/**
 * 更新角色信息
 * @param roleData 要更新的角色数据
 * @returns
 */
export const updateRole = (roleData: Partial<ExtendedRole> & { id: string }): ExtendedRole | undefined => {
  const roleIndex = mockRoles.findIndex(r => r.id === roleData.id)
  if (roleIndex !== -1) {
    mockRoles[roleIndex] = { ...mockRoles[roleIndex], ...roleData }
    return mockRoles[roleIndex]
  }
  return undefined
}
