import type { 
  OrderListItem, 
  OrderStats, 
  ApprovalRecord, 
  RightOption,
  OrderStatus,
  PaymentStatus,
  ApprovalStatus,
  InsuranceStatus,
  JpjRegistrationStatus,
  PaymentMethod,
  ApprovalType
} from '@/types/order.d'

// 模拟订单统计数据
export const mockOrderStats: OrderStats = {
  monthlyOrderCount: 1248,
  monthlyOrderGrowth: 12.5,
  todayOrderCount: 35,
  todayOrderGrowth: 8.2,
  topStore: '吉隆坡中央门店',
  topStoreOrderCount: 189,
  hotModel: 'Myvi 1.5 Premium X',
  hotModelOrderCount: 234,
  pendingDeliveryCount: 42
}

// 模拟订单列表数据
export const mockOrderList: OrderListItem[] = [
  {
    id: '1',
    orderNumber: 'KL2023120401',
    storeName: '吉隆坡中央门店',
    createTime: '2023-12-04 10:30:00',
    customerName: '张三',
    customerPhone: '138****1234',
    buyerName: '张三',
    buyerPhone: '138****1234',
    buyerType: '个人',
    model: 'Myvi',
    variant: '1.5 Premium X',
    color: '白色',
    vin: 'LVSGCQ****0001',
    paymentMethod: 'installment' as PaymentMethod,
    loanApprovalStatus: '已通过',
    orderStatus: 'confirmed' as OrderStatus,
    approvalStatus: 'approved' as ApprovalStatus,
    paymentStatus: 'deposit_paid' as PaymentStatus,
    insuranceStatus: 'completed' as InsuranceStatus,
    jpjRegistrationStatus: 'completed' as JpjRegistrationStatus
  },
  {
    id: '2',
    orderNumber: 'KL2023120402',
    storeName: '雪兰莪门店',
    createTime: '2023-12-04 14:20:00',
    customerName: '李四',
    customerPhone: '139****5678',
    buyerName: '李四',
    buyerPhone: '139****5678',
    buyerType: '个人',
    model: 'Alza',
    variant: '1.5 Premium',
    color: '黑色',
    vin: 'LVSGCQ****0002',
    paymentMethod: 'full_payment' as PaymentMethod,
    orderStatus: 'pending_delivery' as OrderStatus,
    approvalStatus: 'approved' as ApprovalStatus,
    paymentStatus: 'fully_paid' as PaymentStatus,
    insuranceStatus: 'completed' as InsuranceStatus,
    jpjRegistrationStatus: 'completed' as JpjRegistrationStatus
  },
  {
    id: '3',
    orderNumber: 'KL2023120403',
    storeName: '槟城门店',
    createTime: '2023-12-04 16:45:00',
    customerName: '王五',
    customerPhone: '137****9012',
    buyerName: '王五',
    buyerPhone: '137****9012',
    buyerType: '个人',
    model: 'Axia',
    variant: '1.0 G',
    color: '红色',
    vin: 'LVSGCQ****0003',
    paymentMethod: 'installment' as PaymentMethod,
    loanApprovalStatus: '审核中',
    orderStatus: 'confirmed' as OrderStatus,
    approvalStatus: 'pending' as ApprovalStatus,
    paymentStatus: 'deposit_paid' as PaymentStatus,
    insuranceStatus: 'pending' as InsuranceStatus,
    jpjRegistrationStatus: 'pending' as JpjRegistrationStatus
  },
  {
    id: '4',
    orderNumber: 'KL2023120404',
    storeName: '柔佛门店',
    createTime: '2023-12-03 09:15:00',
    customerName: '赵六',
    customerPhone: '136****3456',
    buyerName: '赵六',
    buyerPhone: '136****3456',
    buyerType: '个人',
    model: 'Myvi',
    variant: '1.3 Standard G',
    color: '银色',
    vin: 'LVSGCQ****0004',
    paymentMethod: 'full_payment' as PaymentMethod,
    orderStatus: 'completed' as OrderStatus,
    approvalStatus: 'approved' as ApprovalStatus,
    paymentStatus: 'fully_paid' as PaymentStatus,
    insuranceStatus: 'completed' as InsuranceStatus,
    jpjRegistrationStatus: 'completed' as JpjRegistrationStatus
  },
  {
    id: '5',
    orderNumber: 'KL2023120405',
    storeName: '吉隆坡中央门店',
    createTime: '2023-12-03 13:30:00',
    customerName: '钱七',
    customerPhone: '135****7890',
    buyerName: '钱七',
    buyerPhone: '135****7890',
    buyerType: '个人',
    model: 'Alza',
    variant: '1.5 Standard',
    color: '蓝色',
    vin: 'LVSGCQ****0005',
    paymentMethod: 'installment' as PaymentMethod,
    loanApprovalStatus: '已驳回',
    orderStatus: 'cancelled' as OrderStatus,
    approvalStatus: 'rejected' as ApprovalStatus,
    paymentStatus: 'pending_deposit' as PaymentStatus,
    insuranceStatus: 'pending' as InsuranceStatus,
    jpjRegistrationStatus: 'pending' as JpjRegistrationStatus
  },
  {
    id: '6',
    orderNumber: 'KL2023120406',
    storeName: '雪兰莪门店',
    createTime: '2023-12-02 11:45:00',
    customerName: '孙八',
    customerPhone: '134****2345',
    buyerName: '孙八',
    buyerPhone: '134****2345',
    buyerType: '个人',
    model: 'Axia',
    variant: '1.0 E',
    color: '绿色',
    vin: 'LVSGCQ****0006',
    paymentMethod: 'full_payment' as PaymentMethod,
    orderStatus: 'confirmed' as OrderStatus,
    approvalStatus: 'approved' as ApprovalStatus,
    paymentStatus: 'pending_final' as PaymentStatus,
    insuranceStatus: 'completed' as InsuranceStatus,
    jpjRegistrationStatus: 'pending' as JpjRegistrationStatus
  },
  {
    id: '7',
    orderNumber: 'KL2023120407',
    storeName: '槟城门店',
    createTime: '2023-12-02 15:20:00',
    customerName: '周九',
    customerPhone: '133****6789',
    buyerName: '周九',
    buyerPhone: '133****6789',
    buyerType: '个人',
    model: 'Myvi',
    variant: '1.5 Premium X',
    color: '黄色',
    vin: 'LVSGCQ****0007',
    paymentMethod: 'installment' as PaymentMethod,
    loanApprovalStatus: '已通过',
    orderStatus: 'pending_delivery' as OrderStatus,
    approvalStatus: 'approved' as ApprovalStatus,
    paymentStatus: 'fully_paid' as PaymentStatus,
    insuranceStatus: 'completed' as InsuranceStatus,
    jpjRegistrationStatus: 'completed' as JpjRegistrationStatus
  },
  {
    id: '8',
    orderNumber: 'KL2023120408',
    storeName: '柔佛门店',
    createTime: '2023-12-01 10:10:00',
    customerName: '吴十',
    customerPhone: '132****0123',
    buyerName: '吴十',
    buyerPhone: '132****0123',
    buyerType: '个人',
    model: 'Alza',
    variant: '1.5 Premium',
    color: '紫色',
    vin: 'LVSGCQ****0008',
    paymentMethod: 'full_payment' as PaymentMethod,
    orderStatus: 'completed' as OrderStatus,
    approvalStatus: 'approved' as ApprovalStatus,
    paymentStatus: 'fully_paid' as PaymentStatus,
    insuranceStatus: 'completed' as InsuranceStatus,
    jpjRegistrationStatus: 'completed' as JpjRegistrationStatus
  }
]

// 模拟审核记录数据
export const mockApprovalRecords: ApprovalRecord[] = [
  {
    id: '1',
    approvalNumber: 'APKL2023120401',
    orderId: '1',
    orderNumber: 'KL2023120401',
    applicantId: 'cust001',
    applicantName: '张三',
    approvalType: 'color_change' as ApprovalType,
    applicationReason: '客户要求更换车辆颜色',
    approvalLevel: 'first',
    approverId: 'sm001',
    approverName: '销售经理A',
    approvalResult: 'approved' as ApprovalStatus,
    applicationTime: '2023-12-04 10:30:00',
    approvalTime: '2023-12-04 11:00:00',
    changeDetails: {
      before: { color: '黑色' },
      after: { color: '白色' }
    }
  },
  {
    id: '2',
    approvalNumber: 'APKL2023120402',
    orderId: '2',
    orderNumber: 'KL2023120402',
    applicantId: 'cust002',
    applicantName: '李四',
    approvalType: 'order_cancel' as ApprovalType,
    applicationReason: '客户临时取消购车计划',
    approvalLevel: 'first',
    approverId: 'sm002',
    approverName: '销售经理B',
    approvalResult: 'pending' as ApprovalStatus,
    applicationTime: '2023-12-04 14:20:00'
  },
  {
    id: '3',
    approvalNumber: 'APKL2023120403',
    orderId: '3',
    orderNumber: 'KL2023120403',
    applicantId: 'cust003',
    applicantName: '王五',
    approvalType: 'buyer_info_change' as ApprovalType,
    applicationReason: '客户要求更新购车人信息',
    approvalLevel: 'first',
    approverId: 'sc001',
    approverName: '销售顾问A',
    approvalResult: 'approved' as ApprovalStatus,
    applicationTime: '2023-12-04 16:45:00',
    approvalTime: '2023-12-04 17:15:00',
    changeDetails: {
      before: { buyerName: '王五', buyerPhone: '137****9012' },
      after: { buyerName: '王五', buyerPhone: '137****9999' }
    }
  },
  {
    id: '4',
    approvalNumber: 'APKL2023120404',
    orderId: '4',
    orderNumber: 'KL2023120404',
    applicantId: 'cust004',
    applicantName: '赵六',
    approvalType: 'order_cancel' as ApprovalType,
    applicationReason: '客户资金问题',
    approvalLevel: 'final',
    approverId: 'rm001',
    approverName: '区域经理A',
    approvalResult: 'rejected' as ApprovalStatus,
    rejectionReason: '订单已进入交车阶段，无法取消',
    applicationTime: '2023-12-03 09:15:00',
    approvalTime: '2023-12-03 10:30:00'
  }
]

// 模拟权益选项数据
export const mockRightOptions: RightOption[] = [
  {
    rightCode: 'RIGHT001',
    rightName: '首次购车优惠',
    mode: '折扣',
    discountAmount: 2000,
    effectiveDate: '2023-12-01',
    expiryDate: '2024-12-01',
    available: true
  },
  {
    rightCode: 'RIGHT002',
    rightName: '全款购车返现',
    mode: '返现',
    discountAmount: 1500,
    effectiveDate: '2023-12-01',
    expiryDate: '2024-06-30',
    available: true
  },
  {
    rightCode: 'RIGHT003',
    rightName: '节日促销优惠',
    mode: '折扣',
    discountAmount: 1000,
    effectiveDate: '2023-12-01',
    expiryDate: '2024-01-31',
    available: true
  },
  {
    rightCode: 'RIGHT004',
    rightName: '会员专享优惠',
    mode: '折扣',
    discountAmount: 800,
    effectiveDate: '2023-12-01',
    expiryDate: '2024-12-31',
    available: true
  },
  {
    rightCode: 'RIGHT005',
    rightName: '置换补贴',
    mode: '补贴',
    discountAmount: 3000,
    effectiveDate: '2023-12-01',
    expiryDate: '2024-12-31',
    available: false
  },
  {
    rightCode: 'RIGHT006',
    rightName: '贷款优惠',
    mode: '利率优惠',
    discountAmount: 500,
    effectiveDate: '2023-12-01',
    expiryDate: '2024-12-31',
    available: true
  }
] 