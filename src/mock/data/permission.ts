import type { Store, Department, Menu, Role, User } from '@/types/permission'

// 找到 `import type { Store, ... } from '@/types/permission'`
// ...

// 用下面的代码块替换现有的 `mockStores` 数组
export const mockStores: Store[] = [
  // 1. 定义总部
  {
    id: 'hq_perodua',
    storeCode: 'HQ001',
    storeName: 'Perodua 总部',
    storeStatus: 'active',
    storeType: 'headquarter', // 类型为总部
    manager: 'Mr. CEO',
    contactPerson: 'Admin HQ',
    contactPhone: '03-8888-8888',
    province: '雪兰莪州',
    city: '赛城',
    district: '雪邦区',
    detailAddress: 'Perodua Global Manufacturing Sdn. Bhd., Jalan Bistari, 43900 Sepang, Selangor',
    storeProperties: ['sales', 'after_sales'],
    createTime: '2020-01-01T10:00:00Z',
    // 总部没有 parentId
  },
  // 2. 将其他门店作为总部的子门店
  {
    id: 'store_sh_01',
    storeCode: 'SH001',
    storeName: '上海旗舰店',
    storeStatus: 'active',
    storeType: 'branch', // 类型为分店
    manager: '王经理',
    contactPerson: '李四',
    contactPhone: '13800138001',
    province: '上海市',
    city: '上海市',
    district: '浦东新区',
    detailAddress: '世纪大道 1 号',
    storeProperties: ['sales'],
    createTime: '2023-01-16T11:00:00Z',
    parentId: 'hq_perodua', // 指向总部的 ID
  },
  {
    id: 'store_nj_01',
    storeCode: 'NJ001',
    storeName: '南京体验店',
    storeStatus: 'inactive', // 停用状态
    storeType: 'branch',
    manager: '刘经理',
    contactPerson: '张三',
    contactPhone: '13900139002',
    province: '江苏省',
    city: '南京市',
    district: '建邺区',
    detailAddress: '江东中路 80 号',
    storeProperties: ['after_sales'],
    createTime: '2023-01-17T12:00:00Z',
    parentId: 'hq_perodua', // 指向总部的 ID
  },
  {
    id: 'store_gz_01',
    storeCode: 'GZ001',
    storeName: '广州服务中心',
    storeStatus: 'active',
    storeType: 'branch',
    manager: '陈经理',
    contactPerson: '赵五',
    contactPhone: '13700137003',
    province: '广东省',
    city: '广州市',
    district: '天河区',
    detailAddress: '天河路 208 号',
    storeProperties: ['sales', 'after_sales'],
    createTime: '2023-02-10T09:00:00Z',
    parentId: 'hq_perodua', // 指向总部的 ID
  },
  {
    id: 'wh_sh_01',
    storeCode: 'WH_SH_001',
    storeName: '上海配件仓库',
    storeStatus: 'active',
    storeType: 'warehouse', // 类型为仓库
    manager: '孙主管',
    contactPerson: '周七',
    contactPhone: '13600136004',
    province: '上海市',
    city: '上海市',
    district: '嘉定区',
    detailAddress: '安亭镇墨玉南路1000号',
    storeProperties: [],
    createTime: '2023-03-20T14:00:00Z',
    parentId: 'store_sh_01', // 可以将仓库挂在某个分店下，形成二级结构
  },
];

export const mockDepartments: Department[] = [
    // 上海旗舰店部门
    {
        id: 'dept_1',
        departmentName: '销售部',
        departmentCode: 'SALES',
        departmentStatus: 'normal',
        departmentType: 'business',
        parentId: undefined,
        departmentLevel: 1,
        departmentHead: '李经理',
        description: '负责整车销售业务',
        createTime: '2023-01-16T11:05:00Z',
        children: [
            {
                id: 'dept_1_1',
                departmentName: '整车销售组',
                departmentCode: 'SALES_CAR',
                departmentStatus: 'normal',
                departmentType: 'business',
                parentId: 'dept_1',
                departmentLevel: 2,
                departmentHead: '张组长',
                description: '负责新车销售',
                createTime: '2023-01-16T11:06:00Z',
            },
            {
                id: 'dept_1_2',
                departmentName: '二手车销售组',
                departmentCode: 'SALES_USED',
                departmentStatus: 'normal',
                departmentType: 'business',
                parentId: 'dept_1',
                departmentLevel: 2,
                departmentHead: '王组长',
                description: '负责二手车销售',
                createTime: '2023-01-16T11:07:00Z',
            }
        ]
    },
    {
        id: 'dept_2',
        departmentName: '售后服务部',
        departmentCode: 'SERVICE',
        departmentStatus: 'normal',
        departmentType: 'business',
        parentId: undefined,
        departmentLevel: 1,
        departmentHead: '陈经理',
        description: '负责车辆维修保养服务',
        createTime: '2023-01-16T11:10:00Z',
        children: [
            {
                id: 'dept_2_1',
                departmentName: '维修组',
                departmentCode: 'SERVICE_REPAIR',
                departmentStatus: 'normal',
                departmentType: 'business',
                parentId: 'dept_2',
                departmentLevel: 2,
                departmentHead: '刘师傅',
                description: '负责车辆维修',
                createTime: '2023-01-16T11:11:00Z',
            },
            {
                id: 'dept_2_2',
                departmentName: '保养组',
                departmentCode: 'SERVICE_MAINTAIN',
                departmentStatus: 'normal',
                departmentType: 'business',
                parentId: 'dept_2',
                departmentLevel: 2,
                departmentHead: '马师傅',
                description: '负责车辆保养',
                createTime: '2023-01-16T11:12:00Z',
            },
            {
                id: 'dept_2_3',
                departmentName: '配件管理组',
                departmentCode: 'SERVICE_PARTS',
                departmentStatus: 'normal',
                departmentType: 'support',
                parentId: 'dept_2',
                departmentLevel: 2,
                departmentHead: '孙主管',
                description: '负责配件管理',
                createTime: '2023-01-16T11:13:00Z',
            }
        ]
    },
    {
        id: 'dept_3',
        departmentName: '行政管理部',
        departmentCode: 'ADMIN',
        departmentStatus: 'normal',
        departmentType: 'management',
        parentId: undefined,
        departmentLevel: 1,
        departmentHead: '周经理',
        description: '负责行政管理工作',
        createTime: '2023-01-16T11:15:00Z',
        children: [
            {
                id: 'dept_3_1',
                departmentName: '人事组',
                departmentCode: 'ADMIN_HR',
                departmentStatus: 'normal',
                departmentType: 'management',
                parentId: 'dept_3',
                departmentLevel: 2,
                departmentHead: '吴主管',
                description: '负责人事管理',
                createTime: '2023-01-16T11:16:00Z',
            },
            {
                id: 'dept_3_2',
                departmentName: '财务组',
                departmentCode: 'ADMIN_FINANCE',
                departmentStatus: 'normal',
                departmentType: 'management',
                parentId: 'dept_3',
                departmentLevel: 2,
                departmentHead: '郑会计',
                description: '负责财务管理',
                createTime: '2023-01-16T11:17:00Z',
            }
        ]
    },

    // 南京体验店部门
    {
        id: 'dept_4',
        departmentName: '销售部',
        departmentCode: 'NJ_SALES',
        departmentStatus: 'normal',
        departmentType: 'business',
        parentId: undefined,
        departmentLevel: 1,
        departmentHead: '钱经理',
        description: '南京店销售部门',
        createTime: '2023-01-17T12:05:00Z',
        children: [
            {
                id: 'dept_4_1',
                departmentName: '展厅销售组',
                departmentCode: 'NJ_SALES_SHOW',
                departmentStatus: 'normal',
                departmentType: 'business',
                parentId: 'dept_4',
                departmentLevel: 2,
                departmentHead: '赵顾问',
                description: '负责展厅销售',
                createTime: '2023-01-17T12:06:00Z',
            }
        ]
    },
    {
        id: 'dept_5',
        departmentName: '客服部',
        departmentCode: 'NJ_SERVICE',
        departmentStatus: 'disabled',
        departmentType: 'support',
        parentId: undefined,
        departmentLevel: 1,
        departmentHead: '孙经理',
        description: '南京店客服部门（已停用）',
        createTime: '2023-01-17T12:10:00Z',
    },

    // 华北总店部门
    {
        id: 'dept_6',
        departmentName: '总经理办公室',
        departmentCode: 'HB_GM',
        departmentStatus: 'normal',
        departmentType: 'management',
        parentId: undefined,
        departmentLevel: 1,
        departmentHead: '总经理',
        description: '华北总店管理层',
        createTime: '2023-02-10T09:05:00Z',
    },
    {
        id: 'dept_7',
        departmentName: '运营部',
        departmentCode: 'HB_OPS',
        departmentStatus: 'normal',
        departmentType: 'business',
        parentId: undefined,
        departmentLevel: 1,
        departmentHead: '运营总监',
        description: '负责整体运营管理',
        createTime: '2023-02-10T09:10:00Z',
        children: [
            {
                id: 'dept_7_1',
                departmentName: '市场推广组',
                departmentCode: 'HB_OPS_MKT',
                departmentStatus: 'normal',
                departmentType: 'business',
                parentId: 'dept_7',
                departmentLevel: 2,
                departmentHead: '市场主管',
                description: '负责市场推广活动',
                createTime: '2023-02-10T09:11:00Z',
            },
            {
                id: 'dept_7_2',
                departmentName: '客户关系组',
                departmentCode: 'HB_OPS_CRM',
                departmentStatus: 'normal',
                departmentType: 'business',
                parentId: 'dept_7',
                departmentLevel: 2,
                departmentHead: 'CRM主管',
                description: '负责客户关系维护',
                createTime: '2023-02-10T09:12:00Z',
            }
        ]
    },

    // 华南体验中心部门
    {
        id: 'dept_8',
        departmentName: '体验部',
        departmentCode: 'HN_EXP',
        departmentStatus: 'normal',
        departmentType: 'business',
        parentId: undefined,
        departmentLevel: 1,
        departmentHead: '体验经理',
        description: '负责客户体验服务',
        createTime: '2023-03-20T14:05:00Z',
        children: [
            {
                id: 'dept_8_1',
                departmentName: '试驾服务组',
                departmentCode: 'HN_EXP_DRIVE',
                departmentStatus: 'normal',
                departmentType: 'business',
                parentId: 'dept_8',
                departmentLevel: 2,
                departmentHead: '试驾专员',
                description: '负责试驾服务',
                createTime: '2023-03-20T14:06:00Z',
            },
            {
                id: 'dept_8_2',
                departmentName: '产品讲解组',
                departmentCode: 'HN_EXP_INTRO',
                departmentStatus: 'normal',
                departmentType: 'business',
                parentId: 'dept_8',
                departmentLevel: 2,
                departmentHead: '产品专家',
                description: '负责产品介绍',
                createTime: '2023-03-20T14:07:00Z',
            }
        ]
    },
    {
        id: 'dept_9',
        departmentName: '技术支持部',
        departmentCode: 'HN_TECH',
        departmentStatus: 'normal',
        departmentType: 'support',
        parentId: undefined,
        departmentLevel: 1,
        departmentHead: '技术经理',
        description: '负责技术支持服务',
        createTime: '2023-03-20T14:10:00Z',
    }
];

// ==================== 菜单 Mock 数据（已扩充） ====================
export const mockMenus: Menu[] = [
  {
    id: '1',
    menuName: '系统管理',
    menuCode: 'system',
    menuType: 'directory',
    menuSide: 'factory', // 厂端
    menuStatus: 'normal',
    menuIcon: 'Setting',
    permission: 'system',
    sortOrder: 1,
    isVisible: true,
    isCache: false,
    children: [
      {
        id: '1-1',
        parentId: '1',
        menuName: '菜单管理',
        menuCode: 'system:menu',
        menuType: 'menu',
        menuSide: 'factory', // 厂端
        menuStatus: 'normal',
        component: '/system/MenuManagement',
        menuPath: '/system/menu',
        permission: 'system:menu:list',
        sortOrder: 1,
        isVisible: true,
        isCache: false,
      },
      {
        id: '1-2',
        parentId: '1',
        menuName: '角色管理',
        menuCode: 'system:role',
        menuType: 'menu',
        menuSide: 'factory', // 厂端
        menuStatus: 'normal',
        component: '/system/RoleManagement',
        menuPath: '/system/role',
        permission: 'system:role:list',
        sortOrder: 2,
        isVisible: true,
        isCache: false,
      },
    ]
  },
  {
    id: '2',
    menuName: '销售管理',
    menuCode: 'sales',
    menuType: 'directory',
    menuSide: 'dealer', // 店端
    menuStatus: 'normal',
    menuIcon: 'ShoppingCart',
    permission: 'sales',
    sortOrder: 2,
    isVisible: true,
    isCache: false,
    children: [
      {
        id: '2-1',
        parentId: '2',
        menuName: '订单管理',
        menuCode: 'sales:order',
        menuType: 'menu',
        menuSide: 'dealer', // 店端
        menuStatus: 'normal',
        component: '/order/OrderManagementView',
        menuPath: '/order/list',
        permission: 'sales:order:list',
        sortOrder: 1,
        isVisible: true,
        isCache: false,
      }
    ]
  }
];


/**
 * 将扁平的部门列表转换为树形结构
 * (这是一个辅助函数，用于构建部门树)
 * @param departments 部门列表
 * @param parentId 父ID
 * @returns
 */
const buildDepartmentTree = (departments, parentId) => {
  const tree = []
  departments.forEach(dept => {
    // 检查 parentId 是否匹配 (根节点的 parentId 是 undefined)
    if (dept.parentId === parentId) {
      const children = buildDepartmentTree(departments, dept.id)
      if (children.length > 0) {
        dept.children = children
      }
      tree.push(dept)
    }
  })
  return tree
}

/**
 * 获取部门树 (Mock)
 * @param params 查询参数，可以包含 storeId
 * @returns 返回一个 Promise，解析为部门树结构的数组
 */
export const getDepartmentTree = (params) => {
  return new Promise(resolve => {
    setTimeout(() => {
      let filteredDepartments = mockDepartments;

      // 如果提供了 storeId，则筛选对应门店的部门
      if (params?.storeId) {
        filteredDepartments = mockDepartments.filter(d => d.storeId === params.storeId);
      }

      // 将筛选后的扁平列表构建成树形结构
      const tree = buildDepartmentTree(filteredDepartments);
      resolve(tree);
    }, 100); // 模拟网络延迟
  });
};
