import type { 
  VehicleAllocationOrderItem, 
  AvailableVehicle, 
  AllocationRecord, 
  OrderAllocationDetail,
  OrderAllocationTimelineItem,
  AllocationStatistics,
  AllocationStatus,
  OrderStatus,
  StockStatus,
  OperationType,
  ProcessResult
} from '@/types/sales/vehicleAllocation';

// 模拟车辆配车订单数据
export const mockVehicleAllocationOrders: VehicleAllocationOrderItem[] = [
  {
    id: 1,
    orderNumber: 'ORD2024001',
    customerName: '张三',
    customerPhone: '13800138001',
    store: '北京门店',
    salesConsultant: '李销售',
    model: 'Model 3',
    variant: 'Long Range',
    color: '珍珠白',
    factoryOrderNumber: 'FON2024001',
    vin: 'LRW3E7FS1EC123456',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-15 10:30:00',
  },
  {
    id: 2,
    orderNumber: 'ORD2024002',
    customerName: '李四',
    customerPhone: '13800138002',
    store: '上海门店',
    salesConsultant: '王销售',
    model: 'Model Y',
    variant: 'Performance',
    color: '深海蓝',
    factoryOrderNumber: 'FON2024002',
    vin: 'LRW3E7FS2EC234567',
    allocationStatus: 'allocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-16 14:20:00',
    allocationTime: '2024-01-17 09:15:00',
  },
  {
    id: 3,
    orderNumber: 'ORD2024003',
    customerName: '王五',
    customerPhone: '13800138003',
    store: '北京门店',
    salesConsultant: '赵销售',
    model: 'Model S',
    variant: 'Plaid',
    color: '曜石黑',
    factoryOrderNumber: 'FON2024003',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-17 16:45:00',
  },
  {
    id: 4,
    orderNumber: 'ORD2024004',
    customerName: '赵六',
    customerPhone: '13800138004',
    store: '深圳门店',
    salesConsultant: '孙销售',
    model: 'Model X',
    variant: 'Plaid',
    color: '中国红',
    factoryOrderNumber: 'FON2024004',
    vin: 'LRW3E7FS4EC456789',
    allocationStatus: 'allocated',
    orderStatus: 'ready_delivery',
    orderCreateTime: '2024-01-18 11:20:00',
    allocationTime: '2024-01-19 13:30:00',
  },
  {
    id: 5,
    orderNumber: 'ORD2024005',
    customerName: '孙七',
    customerPhone: '13800138005',
    store: '广州门店',
    salesConsultant: '周销售',
    model: 'Model 3',
    variant: 'Standard',
    color: '珍珠白',
    factoryOrderNumber: 'FON2024005',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'cancel_review',
    orderCreateTime: '2024-01-19 08:10:00',
  },
  {
    id: 6,
    orderNumber: 'ORD2024006',
    customerName: '周八',
    customerPhone: '13800138006',
    store: '成都门店',
    salesConsultant: '吴销售',
    model: 'Model Y',
    variant: 'Long Range',
    color: '冷光银',
    factoryOrderNumber: 'FON2024006',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-20 11:00:00',
  },
  {
    id: 7,
    orderNumber: 'ORD2024007',
    customerName: '吴九',
    customerPhone: '13800138007',
    store: '北京门店',
    salesConsultant: '李销售',
    model: 'Model 3',
    variant: 'Performance',
    color: '深海蓝',
    factoryOrderNumber: 'FON2024007',
    vin: 'LRW3E7FS7EC789012',
    allocationStatus: 'allocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-21 13:20:00',
    allocationTime: '2024-01-22 10:00:00',
  },
  {
    id: 8,
    orderNumber: 'ORD2024008',
    customerName: '郑十',
    customerPhone: '13800138008',
    store: '上海门店',
    salesConsultant: '王销售',
    model: 'Model S',
    variant: 'Long Range',
    color: '珍珠白',
    factoryOrderNumber: 'FON2024008',
    vin: '',
    allocationStatus: 'unallocated',
    orderStatus: 'confirmed',
    orderCreateTime: '2024-01-22 15:00:00',
  }
];

// 模拟可配车辆数据
export const mockAvailableVehicles: AvailableVehicle[] = [
  {
    id: 1,
    vin: 'AVAIL001VIN12345',
    factoryOrderNumber: 'FFONN2024001',
    model: 'Model 3',
    variant: 'Long Range',
    color: '珍珠白',
    warehouseName: '北京仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-10 09:00:00',
  },
  {
    id: 2,
    vin: 'AVAIL002VIN23456',
    factoryOrderNumber: 'FFONN2024002',
    model: 'Model Y',
    variant: 'Performance',
    color: '深海蓝',
    warehouseName: '上海仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-11 10:30:00',
  },
  {
    id: 3,
    vin: 'AVAIL003VIN34567',
    factoryOrderNumber: 'FFONN2024003',
    model: 'Model S',
    variant: 'Plaid',
    color: '曜石黑',
    warehouseName: '深圳仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-12 14:20:00',
  },
  {
    id: 4,
    vin: 'AVAIL004VIN45678',
    factoryOrderNumber: 'FFONN2024004',
    model: 'Model X',
    variant: 'Plaid',
    color: '中国红',
    warehouseName: '广州仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-13 11:45:00',
  },
  {
    id: 5,
    vin: 'AVAIL005VIN56789',
    factoryOrderNumber: 'FFONN2024005',
    model: 'Model 3',
    variant: 'Standard',
    color: '珍珠白',
    warehouseName: '成都仓库',
    stockStatus: 'in_stock',
    lockStatus: false,
    inStockTime: '2024-01-14 16:00:00',
  }
];

// 模拟配车记录数据
export const mockAllocationRecords: AllocationRecord[] = [
  {
    id: 1,
    recordNumber: 'AR2024001',
    orderNumber: 'ORD2024002',
    customerName: '李四',
    vin: 'LRW3E7FS2EC234567',
    operationType: 'allocate',
    operator: '管理员',
    operationTime: '2024-01-17 09:15:00',
    processResult: 'success',
    remarks: '配车成功',
    operationDetails: '将VIN为LRW3E7FS2EC234567的车辆配给订单ORD2024002',
  },
  {
    id: 2,
    recordNumber: 'AR2024002',
    orderNumber: 'ORD2024007',
    customerName: '吴九',
    vin: 'LRW3E7FS7EC789012',
    operationType: 'allocate',
    operator: '管理员',
    operationTime: '2024-01-22 10:00:00',
    processResult: 'success',
    remarks: '配车成功',
    operationDetails: '将VIN为LRW3E7FS7EC789012的车辆配给订单ORD2024007',
  },
];

// 模拟订单配车时间轴数据
export const mockOrderAllocationTimelines: Record<string, OrderAllocationTimelineItem[]> = {
  'ORD2024002': [
    {
      id: 1,
      operationType: 'allocate',
      operator: '管理员',
      operationTime: '2024-01-17 09:15:00',
      vin: 'LRW3E7FS2EC234567',
      warehouseName: '上海仓库',
      processResult: 'success',
      remarks: '配车成功',
      operationDetails: '将VIN为LRW3E7FS2EC234567的车辆配给订单ORD2024002',
      isSystemOperation: false,
    },
  ],
  'ORD2024007': [
    {
      id: 2,
      operationType: 'allocate',
      operator: '管理员',
      operationTime: '2024-01-22 10:00:00',
      vin: 'LRW3E7FS7EC789012',
      warehouseName: '北京仓库',
      processResult: 'success',
      remarks: '配车成功',
      operationDetails: '将VIN为LRW3E7FS7EC789012的车辆配给订单ORD2024007',
      isSystemOperation: false,
    },
  ],
};

// 模拟配车统计数据
export const mockAllocationStatistics: AllocationStatistics = {
  totalAllocations: 35,
  totalCancellations: 12,
  successRate: 92.3,
  averageAllocationTime: 4.5,
};

// 门店选项
export const mockStoreOptions = [
  { label: '北京门店', value: '北京门店' },
  { label: '上海门店', value: '上海门店' },
  { label: '深圳门店', value: '深圳门店' },
  { label: '广州门店', value: '广州门店' },
  { label: '成都门店', value: '成都门店' },
];

// 销售顾问选项（按门店分组）
export const mockSalesConsultantOptions: Record<string, Array<{label: string, value: string, storeId: string}>> = {
  '北京门店': [
    { label: '李销售', value: '李销售', storeId: '北京门店' },
    { label: '赵销售', value: '赵销售', storeId: '北京门店' },
  ],
  '上海门店': [
    { label: '王销售', value: '王销售', storeId: '上海门店' },
  ],
  '深圳门店': [
    { label: '孙销售', value: '孙销售', storeId: '深圳门店' },
  ],
  '广州门店': [
    { label: '周销售', value: '周销售', storeId: '广州门店' },
  ],
  '成都门店': [
    { label: '吴销售', value: '吴销售', storeId: '成都门店' },
  ],
};

// 配车状态选项
export const mockAllocationStatusOptions = [
  { label: '已配车', value: 'allocated' },
  { label: '未配车', value: 'unallocated' },
];

// 订单状态选项
export const mockOrderStatusOptions = [
  { label: '已提交', value: 'submitted' },
  { label: '已确认', value: 'confirmed' },
  { label: '取消审核中', value: 'cancel_review' },
  { label: '取消审核通过', value: 'cancel_approved' },
  { label: '已取消', value: 'cancelled' },
  { label: '待交车', value: 'ready_delivery' },
  { label: '已交车', value: 'delivered' },
];

// 操作类型选项
export const mockOperationTypeOptions = [
  { label: '配车', value: 'allocate' },
  { label: '取消配车', value: 'cancel_allocate' },
  { label: '查看详情', value: 'view_detail' },
  { label: '系统自动取消', value: 'system_auto_cancel' },
];