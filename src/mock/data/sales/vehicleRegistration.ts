// 车辆登记Mock数据

import type {
  VehicleRegistrationSearchParams,
  VehicleRegistrationPageResponse,
  VehicleRegistrationItem,
  VehicleRegistrationDetail,
  ApiResponse,
  SalesAdvisorOption
} from '@/types/sales/vehicleRegistration';

// 动态生成模拟数据（25-30条，便于测试分页）
function generateMockData(): VehicleRegistrationItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData: VehicleRegistrationItem[] = [];

  const statuses = ['pending', 'processing', 'success', 'failed'];
  const insuranceStatuses = ['insured', 'not_insured'];
  const vehicleModels = ['MYVI 1.5L', 'ALZA 1.5L', 'AXIA 1.0L', 'BEZZA 1.3L'];
  const colors = ['白色', '黑色', '银色', '红色', '蓝色'];
  const companies = ['大东方保险', '丰隆保险', '安联保险', '太平洋保险', '友邦保险'];

  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      id: `VR${String(i + 1).padStart(6, '0')}`,
      orderNo: `ORD${String(i + 1).padStart(6, '0')}`,
      customerName: `客户${i + 1}`,
      customerPhone: `1${String(Math.floor(Math.random() * *********) + *********)}`,
      vin: `WVWZZZ1JZ3W${String(Math.floor(Math.random() * 900000) + 100000)}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleColor: colors[Math.floor(Math.random() * colors.length)],
      insuranceStatus: insuranceStatuses[Math.floor(Math.random() * insuranceStatuses.length)],
      companyName: companies[Math.floor(Math.random() * companies.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      lastPushTime: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
      registrationFee: Math.floor(Math.random() * 1000) + 100,
      salesAdvisorName: `销售顾问${Math.floor(Math.random() * 10) + 1}`,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000).toISOString()
    });
  }

  return mockData;
}

const mockData = generateMockData();

// 获取车辆登记列表Mock
export const getVehicleRegistrationListMock = (
  params: VehicleRegistrationSearchParams
): Promise<ApiResponse<VehicleRegistrationPageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.orderNumber) {
        filteredData = filteredData.filter(item =>
          item.orderNo.toLowerCase().includes(params.orderNumber!.toLowerCase())
        );
      }

      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.customerName.includes(params.customerName!)
        );
      }

      if (params.customerPhone) {
        filteredData = filteredData.filter(item =>
          item.customerPhone.includes(params.customerPhone!)
        );
      }

      if (params.registrationStatus) {
        filteredData = filteredData.filter(item =>
          item.status === params.registrationStatus
        );
      }

      if (params.vin) {
        filteredData = filteredData.filter(item =>
          item.vin.toLowerCase().includes(params.vin!.toLowerCase())
        );
      }

      if (params.insuranceStatus) {
        filteredData = filteredData.filter(item =>
          item.insuranceStatus === params.insuranceStatus
        );
      }

      if (params.salesAdvisor) {
        filteredData = filteredData.filter(item =>
          item.salesAdvisorName.includes(params.salesAdvisor!)
        );
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        code: '200',
        message: '获取成功',
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        },
        timestamp: Date.now()
      });
    }, 500);
  });
};

// 获取车辆登记详情Mock
export const getVehicleRegistrationDetailMock = (
  id: string
): Promise<ApiResponse<VehicleRegistrationDetail>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockData.find(item => item.id === id);
      if (!item) {
        resolve({
          code: '404',
          message: '记录不存在',
          result: null as any,
          timestamp: Date.now()
        });
        return;
      }

      // 生成费用明细
      const feeDetails = [
        {
          id: Math.floor(Math.random() * 9000) + 1000,
          registrationId: parseInt(id),
          feeTypeDisplay: '登记费',
          amount: Math.floor(Math.random() * 200) + 100,
          createdAt: item.createdAt,
          updatedAt: item.createdAt
        },
        {
          id: Math.floor(Math.random() * 9000) + 1000,
          registrationId: parseInt(id),
          feeTypeDisplay: '手续费',
          amount: Math.floor(Math.random() * 100) + 50,
          createdAt: item.createdAt,
          updatedAt: item.createdAt
        }
      ];

      const totalFeeAmount = feeDetails.reduce((sum, fee) => sum + fee.amount, 0);

      // 生成操作日志
      const operationLogs = [
        {
          id: Math.floor(Math.random() * 9000) + 1000,
          registrationId: parseInt(id),
          operationTime: item.createdAt,
          operationType: 'CREATE',
          operationTypeName: '创建登记',
          operationSource: 'DMS',
          operatorId: 9000,
          operatorName: item.salesAdvisorName,
          ipAddress: '127.0.0.1',
          result: 'SUCCESS',
          resultName: '成功',
          beforeStatus: null,
          beforeStatusName: null,
          afterStatus: 'pending',
          afterStatusName: '待推送',
          remark: '初次创建车辆登记',
          createdAt: item.createdAt
        }
      ];

      // 根据状态添加更多日志
      if (item.status !== 'pending') {
        operationLogs.push({
          id: Math.floor(Math.random() * 9000) + 1000,
          registrationId: parseInt(id),
          operationTime: item.lastPushTime,
          operationType: 'PUSH_START',
          operationTypeName: '开始推送',
          operationSource: 'SYSTEM',
          operatorId: -1,
          operatorName: 'SYSTEM',
          ipAddress: null,
          result: 'SUCCESS',
          resultName: '成功',
          beforeStatus: 'pending',
          beforeStatusName: '待推送',
          afterStatus: 'processing',
          afterStatusName: '推送中',
          remark: '开始推送至JPJ系统',
          createdAt: item.lastPushTime
        });

        if (item.status === 'success') {
          operationLogs.push({
            id: Math.floor(Math.random() * 9000) + 1000,
            registrationId: parseInt(id),
            operationTime: item.lastPushTime,
            operationType: 'PUSH_SUCCESS',
            operationTypeName: '推送成功',
            operationSource: 'SYSTEM',
            operatorId: -1,
            operatorName: 'SYSTEM',
            ipAddress: null,
            result: 'SUCCESS',
            resultName: '成功',
            beforeStatus: 'processing',
            beforeStatusName: '推送中',
            afterStatus: 'success',
            afterStatusName: '推送成功',
            remark: 'JPJ推送成功',
            createdAt: item.lastPushTime
          });
        } else if (item.status === 'failed') {
          operationLogs.push({
            id: Math.floor(Math.random() * 9000) + 1000,
            registrationId: parseInt(id),
            operationTime: item.lastPushTime,
            operationType: 'PUSH_FAILED',
            operationTypeName: '推送失败',
            operationSource: 'SYSTEM',
            operatorId: -1,
            operatorName: 'SYSTEM',
            ipAddress: null,
            result: 'FAILED',
            resultName: '失败',
            beforeStatus: 'processing',
            beforeStatusName: '推送中',
            afterStatus: 'failed',
            afterStatusName: '推送失败',
            remark: 'JPJ推送失败，网络连接超时',
            createdAt: item.lastPushTime
          });
        }
      }

      const detail: VehicleRegistrationDetail = {
        // 基本信息
        id: parseInt(id),
        status: item.status,
        statusName: item.status === 'pending' ? '待推送' :
                   item.status === 'processing' ? '推送中' :
                   item.status === 'success' ? '推送成功' : '推送失败',
        createdAt: item.createdAt,
        updatedAt: new Date().toISOString(),
        completionTime: item.status === 'success' ? item.lastPushTime : null,
        lastPushTime: item.lastPushTime,
        pushCount: item.status === 'pending' ? 0 : Math.floor(Math.random() * 3) + 1,
        maxPushCount: null,
        failureReason: item.status === 'failed' ? '网络连接超时，请重试' : null,
        registrationFee: Math.floor(Math.random() * 500) + 100,
        certificateNumber: item.status === 'success' ? `CERT${Math.floor(Math.random() * 900000) + 100000}` : null,
        jpjCertificateNumber: item.status === 'success' ? `JPJ${Math.floor(Math.random() * 900000) + 100000}` : null,
        jpjReferenceNumber: item.status === 'success' ? `REF${Math.floor(Math.random() * 900000) + 100000}` : null,
        jpjResponseData: null,
        version: null,

        // 订单信息
        orderId: Math.floor(Math.random() * 9000) + 1000,
        orderNumber: item.orderNo,
        orderStatus: '待交付',
        paymentStatus: 'PAID',

        // 客户信息
        customerName: item.customerName,
        customerPhone: item.customerPhone,
        customerIdType: '身份证',
        customerIdNumber: `${Math.floor(Math.random() * 900000) + 100000}${Math.floor(Math.random() * 90) + 10}${Math.floor(Math.random() * 9000) + 1000}`,
        customerEmail: `customer${Math.floor(Math.random() * 1000)}@example.com`,
        customerAddress: `地址${Math.floor(Math.random() * 100) + 1}号`,
        customerCity: '吉隆坡',
        customerState: '雪兰莪',
        customerPostcode: `${Math.floor(Math.random() * 90000) + 10000}`,
        customerType: '个人',

        // 车辆信息
        vin: item.vin,
        engineNo: `ENG${Math.floor(Math.random() * 900000) + 100000}`,
        vehicleModel: item.vehicleModel,
        vehicleVariant: '1.0 X',
        vehicleColor: item.vehicleColor,
        otrAmount: Math.floor(Math.random() * 50000) + 30000,
        totalAmount: Math.floor(Math.random() * 60000) + 35000,

        // 保险信息
        insuranceNumber: `INS${Math.floor(Math.random() * 900000) + 100000}`,
        insuranceCompany: item.companyName,
        insuranceStatus: item.insuranceStatus,
        insuranceStartDate: new Date().toISOString().split('T')[0],
        insuranceEndDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],

        // 费用信息
        feeDetails,
        totalFeeAmount,

        // 操作日志
        operationLogs,

        // 状态标识
        canPush: item.status === 'pending',
        canRetry: item.status === 'failed',
        isCompleted: item.status === 'success',

        // 操作员信息
        operatorId: null,
        operatorName: null,
        createdBy: 'system',
        updatedBy: 'system'
      };

      resolve({
        code: '200',
        message: '获取成功',
        result: detail,
        timestamp: Date.now()
      });
    }, 300);
  });
};

// 推送车辆登记Mock
export const pushVehicleRegistrationMock = (
  id: string
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const success = Math.random() > 0.2; // 80%成功率

      if (success) {
        // 更新mock数据状态
        const item = mockData.find(item => item.id === id);
        if (item) {
          item.status = 'processing';
          item.lastPushTime = new Date().toISOString();
        }

        resolve({
          code: '200',
          message: '推送成功',
          result: true,
          timestamp: Date.now()
        });
      } else {
        resolve({
          code: '500',
          message: '推送失败，请重试',
          result: false,
          timestamp: Date.now()
        });
      }
    }, 1000);
  });
};

// 重新推送Mock
export const retryPushVehicleRegistrationMock = (
  id: string
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70%成功率

      if (success) {
        const item = mockData.find(item => item.id === id);
        if (item) {
          item.status = 'success';
          item.lastPushTime = new Date().toISOString();
        }

        resolve({
          code: '200',
          message: '重新推送成功',
          result: true,
          timestamp: Date.now()
        });
      } else {
        resolve({
          code: '500',
          message: '重新推送失败，请重试',
          result: false,
          timestamp: Date.now()
        });
      }
    }, 1000);
  });
};

// 获取销售顾问列表Mock
export const getSalesAdvisorsListMock = (): Promise<ApiResponse<SalesAdvisorOption[]>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const advisors: SalesAdvisorOption[] = [
        { value: 'advisor1', label: '销售顾问1' },
        { value: 'advisor2', label: '销售顾问2' },
        { value: 'advisor3', label: '销售顾问3' },
        { value: 'advisor4', label: '销售顾问4' },
        { value: 'advisor5', label: '销售顾问5' }
      ];

      resolve({
        code: '200',
        message: '获取成功',
        result: advisors,
        timestamp: Date.now()
      });
    }, 200);
  });
};

// 导出数据Mock
export const exportVehicleRegistrationDataMock = (
  params: VehicleRegistrationSearchParams
): Promise<ApiResponse<string>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟导出处理
      resolve({
        code: '200',
        message: '导出成功',
        result: 'export_file_url',
        timestamp: Date.now()
      });
    }, 2000);
  });
};
