import type {
  InvoiceSearchParams,
  InvoicePageResponse,
  InvoiceItem,
  InvoiceDetail,
  InvoiceOperationLog,
  ApiResponse
} from '@/types/sales/invoiceManagement';

// 动态生成模拟数据（30-35条，便于测试分页）
function generateMockData(): InvoiceItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 30;
  const mockData: InvoiceItem[] = [];

  const salesTypes = ['retail', 'fleet', 'government'];
  const paymentMethods = ['cash', 'loan', 'lease'];
  const invoiceStatuses = ['draft', 'issued', 'sent', 'paid'];
  const vehicleModels = ['MYVI 1.5L', 'ALZA 1.5L', 'AXIA 1.0L', 'BEZZA 1.3L'];
  const colors = ['白色', '黑色', '银色', '红色', '蓝色'];
  const stores = ['KL001', 'PJ002', 'SB003', 'JB004'];
  const consultants = ['张三', '李四', '王五', '赵六'];
  const financeCompanies = ['Bank Islam', 'CIMB Bank', 'Maybank', 'Public Bank'];

  for (let i = 0; i < dataCount; i++) {
    const hasLoan = Math.random() > 0.4;
    const loanAmount = hasLoan ? Math.floor(Math.random() * 50000) + 10000 : 0;

    mockData.push({
      id: `INV${String(i + 1).padStart(6, '0')}`,
      invoiceNumber: `522102-${String(Math.floor(Math.random() * 900000) + 100000)}`,
      invoiceDate: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      orderNumber: `ORD${String(i + 1).padStart(6, '0')}`,
      customerName: `客户${i + 1}`,
      customerPhone: `1${String(Math.floor(Math.random() * *********) + *********)}`,
      customerEmail: `customer${i + 1}@example.com`,
      customerAddress: `地址${i + 1}号`,
      customerState: ['Kuala Lumpur', 'Selangor', 'Johor', 'Penang'][Math.floor(Math.random() * 4)],
      customerCity: ['Kuala Lumpur', 'Petaling Jaya', 'Johor Bahru', 'George Town'][Math.floor(Math.random() * 4)],
      customerPostcode: String(Math.floor(Math.random() * 90000) + 10000),
      vin: `WVWZZZ1JZ3W${String(Math.floor(Math.random() * 900000) + 100000)}`,
      model: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      variant: 'Standard',
      color: colors[Math.floor(Math.random() * colors.length)],
      salesStore: stores[Math.floor(Math.random() * stores.length)],
      salesConsultant: consultants[Math.floor(Math.random() * consultants.length)],
      paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
      financeCompany: hasLoan ? financeCompanies[Math.floor(Math.random() * financeCompanies.length)] : '',
      loanAmount,
      invoiceAmount: Math.floor(Math.random() * 80000) + 20000,
      createdTime: new Date(Date.now() - Math.floor(Math.random() * 60) * 24 * 60 * 60 * 1000).toISOString(),
      status: invoiceStatuses[Math.floor(Math.random() * invoiceStatuses.length)],
      salesType: salesTypes[Math.floor(Math.random() * salesTypes.length)]
    });
  }

  return mockData;
}

const mockData = generateMockData();

export const getInvoiceListMock = (
  params: InvoiceSearchParams
): Promise<ApiResponse<InvoicePageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      if (params.invoiceNumber) {
        filteredData = filteredData.filter(item =>
          item.invoiceNumber.toLowerCase().includes(params.invoiceNumber!.toLowerCase())
        );
      }

      if (params.customerName) {
        filteredData = filteredData.filter(item =>
          item.customerName.includes(params.customerName!)
        );
      }

      if (params.customerPhone) {
        filteredData = filteredData.filter(item =>
          item.customerPhone.includes(params.customerPhone!)
        );
      }

      if (params.customerEmail) {
        filteredData = filteredData.filter(item =>
          item.customerEmail.toLowerCase().includes(params.customerEmail!.toLowerCase())
        );
      }

      if (params.orderNumber) {
        filteredData = filteredData.filter(item =>
          item.orderNumber.includes(params.orderNumber!)
        );
      }

      if (params.vin) {
        filteredData = filteredData.filter(item =>
          item.vin.toLowerCase().includes(params.vin!.toLowerCase())
        );
      }

      if (params.salesType) {
        filteredData = filteredData.filter(item =>
          item.salesType === params.salesType
        );
      }

      if (params.salesStore) {
        filteredData = filteredData.filter(item =>
          item.salesStore === params.salesStore
        );
      }

      if (params.salesConsultant) {
        filteredData = filteredData.filter(item =>
          item.salesConsultant === params.salesConsultant
        );
      }

      if (params.invoiceDateStart && params.invoiceDateEnd) {
        filteredData = filteredData.filter(item => {
          const invoiceDate = new Date(item.invoiceDate);
          const startDate = new Date(params.invoiceDateStart!);
          const endDate = new Date(params.invoiceDateEnd!);
          return invoiceDate >= startDate && invoiceDate <= endDate;
        });
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        code: '200',
        message: '获取成功',
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        },
        timestamp: Date.now()
      });
    }, 500);
  });
};

export const getInvoiceDetailMock = (
  id: string
): Promise<ApiResponse<InvoiceDetail>> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const invoice = mockData.find(item => item.id === id);
      if (invoice) {
        // 模拟添加详细信息
        const detailInvoice: InvoiceDetail = {
          ...invoice,
          invoiceCompany: 'Perodua Sales Sdn Bhd',
          companyAddress: 'Rawang, Selangor, Malaysia',
          gstNumber: 'GST123456789',
          sstNumber: 'SST987654321',
          contactPhone: '+60-3-1234-5678',
          contactEmail: '<EMAIL>',
          deliveryNumber: `DEL${invoice.invoiceNumber.slice(-6)}`,
          salesConsultantId: `SC${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
          tinNumber: `TIN${Math.floor(Math.random() * 1000000)}`,
          modelCode: invoice.model.split(' ')[0],
          modelDescription: `${invoice.model} ${invoice.variant}`,
          engineNumber: `ENG${Math.floor(Math.random() * 1000000)}`,
          chassisNumber: `CHS${Math.floor(Math.random() * 1000000)}`,
          engineDisplacement: '1.5L',
          vehicleRegistrationDate: invoice.invoiceDate,
          creator: '系统管理员',
          updater: '系统管理员',
          updateTime: new Date().toISOString(),
          financeType: invoice.loanAmount > 0 ? 'hire_purchase' : 'cash',
          loanPeriod: invoice.loanAmount > 0 ? '84个月' : '',
          insuranceCompany: 'Allianz General Insurance',
          agentCode: 'AG001',
          policyNumber: `POL${Math.floor(Math.random() * 1000000)}`,
          issueDate: invoice.invoiceDate,
          insuranceAmount: Math.floor(Math.random() * 5000) + 2000,
          vehicleSalesPrice: invoice.invoiceAmount - 2000,
          licensePlateFee: 200,
          accessories: [
            {
              specification: 'Exterior',
              accessoryName: '车身贴膜',
              unitPrice: 1500,
              quantity: 1,
              amount: 1500
            },
            {
              specification: 'Interior',
              accessoryName: '脚垫套装',
              unitPrice: 300,
              quantity: 1,
              amount: 300
            }
          ],
          accessoryAmount: 1800,
          otrFees: [
            {
              feeCode: 'OTR001',
              feeType: '路税',
              taxAmount: 120,
              effectiveDate: invoice.invoiceDate,
              expiryDate: new Date(new Date(invoice.invoiceDate).getTime() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            },
            {
              feeCode: 'OTR002',
              feeType: '保险费',
              taxAmount: 200,
              effectiveDate: invoice.invoiceDate,
              expiryDate: invoice.invoiceDate
            }
          ],
          otrAmount: 320,
          adjustmentAmount: 0,
          invoiceNetValue: invoice.invoiceAmount,
          receipts: [
            {
              receiptNumber: `RCP${Math.floor(Math.random() * 1000000)}`,
              receiptType: '定金',
              receiptNo: `SN${Math.floor(Math.random() * 1000000)}`,
              paymentChannel: '银行转账',
              paidAmount: 10000,
              collectionType: '现金',
              arrivalTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
              remarks: '定金收据'
            },
            {
              receiptNumber: `RCP${Math.floor(Math.random() * 1000000)}`,
              receiptType: '尾款',
              receiptNo: `SN${Math.floor(Math.random() * 1000000)}`,
              paymentChannel: '银行转账',
              paidAmount: invoice.invoiceAmount - 10000,
              collectionType: '转账',
              arrivalTime: invoice.createdTime,
              remarks: '尾款收据'
            }
          ]
        };
        resolve({
          code: '200',
          message: '获取成功',
          result: detailInvoice,
          timestamp: Date.now()
        });
      } else {
        reject({
          code: '404',
          message: '发票不存在',
          result: null,
          timestamp: Date.now()
        });
      }
    }, 300);
  });
};

export const printInvoiceMock = (
  id: string
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '200',
        message: '发票打印成功',
        result: true,
        timestamp: Date.now()
      });
    }, 1000);
  });
};

export const batchPrintInvoicesMock = (
  ids: string[]
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '200',
        message: `成功打印 ${ids.length} 张发票`,
        result: true,
        timestamp: Date.now()
      });
    }, 2000);
  });
};

export const sendInvoiceEmailMock = (
  id: string,
  email: string
): Promise<ApiResponse<boolean>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '200',
        message: '发票邮件发送成功',
        result: true,
        timestamp: Date.now()
      });
    }, 1500);
  });
};

export const exportInvoiceDataMock = (
  config: any
): Promise<ApiResponse<string>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '200',
        message: '数据导出成功',
        result: 'https://example.com/invoices-export.xlsx',
        timestamp: Date.now()
      });
    }, 3000);
  });
};

export const getInvoiceOperationLogsMock = (
  id: string
): Promise<ApiResponse<InvoiceOperationLog[]>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockLogs: InvoiceOperationLog[] = [
        {
          id: '1',
          operationType: 'VIEW_DETAIL',
          operator: '张三',
          operationTime: '2024-01-15 10:30:00',
          operationDescription: '查看发票详细信息',
          operationResult: 'SUCCESS'
        },
        {
          id: '2',
          operationType: 'PRINT',
          operator: '李四',
          operationTime: '2024-01-15 14:20:00',
          operationDescription: '打印发票PDF文件',
          operationResult: 'SUCCESS'
        },
        {
          id: '3',
          operationType: 'EMAIL_SEND',
          operator: '王五',
          operationTime: '2024-01-16 09:15:00',
          operationDescription: '向客户发送发票邮件',
          operationResult: 'FAILED',
          errorMessage: '邮箱地址无效'
        },
        {
          id: '4',
          operationType: 'EXPORT_DATA',
          operator: '赵六',
          operationTime: '2024-01-16 16:45:00',
          operationDescription: '导出发票数据为Excel格式',
          operationResult: 'SUCCESS'
        }
      ];
      resolve({
        code: '200',
        message: '获取成功',
        result: mockLogs,
        timestamp: Date.now()
      });
    }, 500);
  });
};
