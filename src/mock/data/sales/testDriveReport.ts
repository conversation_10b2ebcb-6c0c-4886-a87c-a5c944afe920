import type { TestDriveSearchParams, TestDrivePageResponse, TestDriveStats, TestDriveItem } from '@/types/sales/testDriveReport';

// 动态生成模拟数据
function generateMockData() {
  const dataCount = Math.floor(Math.random() * 6) + 25; // 25-30条数据
  const mockData = [];

  // 使用数据字典编码
  const storeOptions = ['01300001001', '01300001002', '01300001003', '01300001004']; // 门店编码
  const modelOptions = ['01300002001', '01300002002', '01300002003']; // 车型编码
  const sourceOptions = ['01300004001', '01300004002', '01300004003']; // 客户来源编码
  const idTypeOptions = ['01300006001', '01300006002', '01300006003']; // 证件类型编码

  const customerNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
  const consultantNames = ['顾问A', '顾问B', '顾问C', '顾问D', '顾问E'];
  const variants = ['标准版', '豪华版', '旗舰版'];

  for (let i = 0; i < dataCount; i++) {
    const baseDate = new Date();
    const randomDays = Math.floor(Math.random() * 30);
    const startTime = new Date(baseDate.getTime() - randomDays * 24 * 60 * 60 * 1000);
    const endTime = new Date(startTime.getTime() + Math.random() * 2 * 60 * 60 * 1000);
    const createTime = new Date(startTime.getTime() - Math.random() * 24 * 60 * 60 * 1000);
    
    const startMileage = Math.floor(Math.random() * 1000) + 5000;
    const endMileage = startMileage + Math.floor(Math.random() * 50) + 10;
    
    mockData.push({
      id: `TD${String(i + 1).padStart(6, '0')}`,
      testDriveNo: `TD${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,
      customerName: customerNames[Math.floor(Math.random() * customerNames.length)],
      customerPhone: `138${String(Math.floor(Math.random() * 99999999)).padStart(8, '0')}`,
      model: modelOptions[Math.floor(Math.random() * modelOptions.length)],
      variant: variants[Math.floor(Math.random() * variants.length)],
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      mileage: endMileage - startMileage,
      consultantName: consultantNames[Math.floor(Math.random() * consultantNames.length)],
      storeName: storeOptions[Math.floor(Math.random() * storeOptions.length)],
      createTime: createTime.toISOString(),
      
      // 详情字段
      customerId: 1000 + i,
      customerIdType: idTypeOptions[Math.floor(Math.random() * idTypeOptions.length)],
      customerIdNumber: `${Math.floor(Math.random() * 900000000) + 100000000}${String(Math.floor(Math.random() * 900000000) + 100000000)}`,
      driverName: customerNames[Math.floor(Math.random() * customerNames.length)],
      driverPhone: `139${String(Math.floor(Math.random() * 99999999)).padStart(8, '0')}`,
      driverIdType: idTypeOptions[Math.floor(Math.random() * idTypeOptions.length)],
      driverIdNumber: `${Math.floor(Math.random() * 900000000) + 100000000}${String(Math.floor(Math.random() * 900000000) + 100000000)}`,
      source: sourceOptions[Math.floor(Math.random() * sourceOptions.length)],
      sourceChannel: sourceOptions[Math.floor(Math.random() * sourceOptions.length)],
      email: `customer${i + 1}@example.com`,
      driverLicenseNumber: `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String(Math.floor(Math.random() * **********)).padStart(10, '0')}`,
      startMileage,
      endMileage,
      feedback: `试驾体验很好，车辆性能优秀，操控感受${Math.floor(Math.random() * 5) + 1}分`,
      consultantId: 2000 + Math.floor(Math.random() * 10),
      storeId: parseInt(storeOptions[Math.floor(Math.random() * storeOptions.length)]),
      storeRegion: ['华北', '华东', '华南', '西南'][Math.floor(Math.random() * 4)],
      updateTime: new Date().toISOString(),
      editable: Math.random() > 0.5
    });
  }

  return mockData;
}

const mockData = generateMockData();

export const getTestDriveList = (params: TestDriveSearchParams): Promise<TestDrivePageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      // 车型筛选
      if (params.model) {
        filteredData = filteredData.filter(item => item.model === params.model);
      }

      // 门店筛选
      if (params.storeIds && params.storeIds.length > 0) {
        filteredData = filteredData.filter(item =>
          params.storeIds!.includes(item.storeId!)
        );
      }

      // 时间范围筛选
      if (params.startTimeBegin && params.startTimeEnd) {
        filteredData = filteredData.filter(item => {
          const itemTime = new Date(item.startTime);
          return itemTime >= new Date(params.startTimeBegin!) &&
                 itemTime <= new Date(params.startTimeEnd!);
        });
      }

      // 分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        result: {
          records: filteredData.slice(start, end),
          total,
          pageNum,
          pageSize,
          pages
        }
      });
    }, 500); // 模拟网络延迟
  });
};

export const getTestDriveStats = (): Promise<{ result: TestDriveStats }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        result: {
          monthlyCount: Math.floor(Math.random() * 500) + 100,
          dailyCount: Math.floor(Math.random() * 20) + 5,
          topStore: '北京朝阳门店',
          topModel: 'ModelX'
        }
      });
    }, 300);
  });
};

export const getTestDriveDetailMock = (testDriveNo: string): Promise<{ result: TestDriveItem }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const item = mockData.find(item => item.testDriveNo === testDriveNo) || mockData[0];
      resolve({
        result: item
      });
    }, 300);
  });
};
