import type {
  OrderApprovalSearchParams,
  OrderApprovalPageResponse,
  OrderApprovalListItem,
  ApprovalType,
  ApprovalResult
} from '@/types/sales/orderApproval';

// 动态生成Mock数据（25-30条）
function generateMockOrderApprovalData(): OrderApprovalListItem[] {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData: OrderApprovalListItem[] = [];

  const approvalTypes: ApprovalType[] = ['cancel_order', 'modify_info', 'price_adjustment'];
  const approvalResults: ApprovalResult[] = ['approved', 'rejected', 'pending'];
  const submitterNames = ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十'];
  const dealerIds = ['dealer_001', 'dealer_002', 'dealer_003', 'dealer_004'];
  const reasons = [
    '客户因个人原因需要取消订单',
    '购车人信息需要更新',
    '客户要求更改车辆颜色',
    '价格调整申请',
    '订单信息修正',
    '客户联系方式变更',
    '配置升级申请',
    '交付时间调整'
  ];

  for (let i = 0; i < dataCount; i++) {
    const approvalType = approvalTypes[Math.floor(Math.random() * approvalTypes.length)];
    const approvalResult = approvalResults[Math.floor(Math.random() * approvalResults.length)];
    const submitterName = submitterNames[Math.floor(Math.random() * submitterNames.length)];
    const dealerId = dealerIds[Math.floor(Math.random() * dealerIds.length)];
    const reason = reasons[Math.floor(Math.random() * reasons.length)];
    
    // 生成提交时间（最近30天内）
    const submitTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    
    const item: OrderApprovalListItem = {
      id: `approval_${String(i + 1).padStart(3, '0')}`,
      approvalNo: `AP${String(i + 1).padStart(6, '0')}`,
      approvalType,
      orderNo: `ORD${String(i + 1).padStart(8, '0')}`,
      submitterName,
      submitTime: submitTime.toISOString().slice(0, 19).replace('T', ' '),
      reason,
      dealerId
    };

    // 如果不是pending状态，添加审批相关信息
    if (approvalResult !== 'pending') {
      const approvalTime = new Date(submitTime.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000);
      item.approvalResult = approvalResult;
      item.approvalTime = approvalTime.toISOString().slice(0, 19).replace('T', ' ');
      item.approvalUserName = `审批人${Math.floor(Math.random() * 5) + 1}`;
    }

    mockData.push(item);
  }

  return mockData.sort((a, b) => new Date(b.submitTime).getTime() - new Date(a.submitTime).getTime());
}

const mockData = generateMockOrderApprovalData();

export const getOrderApprovalListMock = (
  params: OrderApprovalSearchParams
): Promise<OrderApprovalPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];

      // 按审批类型过滤
      if (params.approvalType) {
        filteredData = filteredData.filter(item =>
          item.approvalType === params.approvalType
        );
      }

      // 按订单编号过滤
      if (params.orderNo) {
        filteredData = filteredData.filter(item =>
          item.orderNo.includes(params.orderNo!)
        );
      }

      // 按提交人过滤
      if (params.submitterName) {
        filteredData = filteredData.filter(item =>
          item.submitterName.includes(params.submitterName!)
        );
      }

      // 按提交时间范围过滤
      if (params.submitTimeStart) {
        filteredData = filteredData.filter(item =>
          item.submitTime >= params.submitTimeStart!
        );
      }
      if (params.submitTimeEnd) {
        filteredData = filteredData.filter(item =>
          item.submitTime <= params.submitTimeEnd!
        );
      }

      // 按审批结果过滤
      if (params.approvalResult) {
        filteredData = filteredData.filter(item =>
          item.approvalResult === params.approvalResult
        );
      }

      // 按门店过滤
      if (params.dealerId) {
        filteredData = filteredData.filter(item =>
          item.dealerId === params.dealerId
        );
      }

      // 按状态过滤（pending/approved）
      if (params.status) {
        if (params.status === 'pending') {
          filteredData = filteredData.filter(item =>
            !item.approvalResult || item.approvalResult === 'pending'
          );
        } else {
          filteredData = filteredData.filter(item =>
            item.approvalResult && item.approvalResult !== 'pending'
          );
        }
      }

      // 标准MyBatisPlus分页处理
      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 20;
      const total = filteredData.length;
      const pages = Math.ceil(total / pageSize);
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        records: filteredData.slice(start, end),
        total,
        pageNum,
        pageSize,
        pages
      });
    }, 500);
  });
};
