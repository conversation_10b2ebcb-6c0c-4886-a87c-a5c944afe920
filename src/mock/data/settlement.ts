import type {
  SettlementListItem,
  SettlementDetail,
  PaymentRecord,
  SettlementLaborItem,
  SettlementPartItem,
  OperationLog
} from '@/types/module'

// 结算单列表模拟数据
const settlementList: SettlementListItem[] = [
  {
    id: '1',
    settlementNo: 'ST202412120001',
    workOrderNo: 'WO202412120001',
    workOrderType: 'maintenance',
    settlementStatus: 'pending_settlement',
    paymentStatus: 'pending',
    customerName: '张三',
    customerPhone: '13888888888',
    vehiclePlate: '京A12345',
    vehicleModel: 'Model Y',
    vehicleConfig: '标准版',
    vehicleColor: '珍珠白',
    technician: '李技师',
    serviceAdvisor: '陈经理',
    totalAmount: 965.00,
    paidAmount: 725.00,
    payableAmount: 240.00,
    createdAt: '2024-12-12 14:30:00',
    inspectionStatus: '质检通过'
  },
  {
    id: '2',
    settlementNo: 'ST202412120002',
    workOrderNo: 'WO202412120002',
    workOrderType: 'repair',
    settlementStatus: 'completed',
    paymentStatus: 'fully_paid',
    customerName: '王五',
    customerPhone: '13999999999',
    vehiclePlate: '京B67890',
    vehicleModel: 'Model 3',
    vehicleConfig: '高性能版',
    vehicleColor: '深空灰',
    technician: '赵技师',
    serviceAdvisor: '陈经理',
    totalAmount: 1200.00,
    paidAmount: 1200.00,
    payableAmount: 0.00,
    createdAt: '2024-12-12 09:15:00',
    inspectionStatus: '质检通过'
  },
  {
    id: '3',
    settlementNo: 'ST202412120003',
    workOrderNo: 'WO202412120003',
    workOrderType: 'warranty',
    settlementStatus: 'pending_settlement',
    paymentStatus: 'pending',
    customerName: '陈七',
    customerPhone: '13777777777',
    vehiclePlate: '京C54321',
    vehicleModel: 'Model S',
    vehicleConfig: 'Plaid版',
    vehicleColor: '红色',
    technician: '孙技师',
    serviceAdvisor: '王顾问',
    totalAmount: 2500.00,
    paidAmount: 0.00,
    payableAmount: 0.00,
    createdAt: '2024-12-12 16:45:00',
    inspectionStatus: '质检通过'
  },
  {
    id: '4',
    settlementNo: 'ST202412110001',
    workOrderNo: 'WO202412110001',
    workOrderType: 'maintenance',
    settlementStatus: 'pre_settlement',
    paymentStatus: 'pending',
    customerName: '刘九',
    customerPhone: '13666666666',
    vehiclePlate: '京D98765',
    vehicleModel: 'Model X',
    vehicleConfig: '长续航版',
    vehicleColor: '白色',
    technician: '吴技师',
    serviceAdvisor: '王顾问',
    totalAmount: 800.00,
    paidAmount: 0.00,
    payableAmount: 300.00,
    createdAt: '2024-12-11 11:20:00',
    inspectionStatus: '质检中'
  }
]

// 工时费用明细模拟数据
const laborItems: SettlementLaborItem[] = [
  {
    laborCode: 'M001',
    laborName: '机油更换',
    standardHours: 0.5,
    unitPrice: 200.00,
    laborType: '标准工时',
    subtotal: 100.00,
    receivableAmount: 0.00,
    remarks: '保养套餐内项目',
    isPackageItem: true
  },
  {
    laborCode: 'M002',
    laborName: '机滤更换',
    standardHours: 0.3,
    unitPrice: 150.00,
    laborType: '标准工时',
    subtotal: 45.00,
    receivableAmount: 0.00,
    remarks: '保养套餐内项目',
    isPackageItem: true
  },
  {
    laborCode: 'A001',
    laborName: '空调滤芯更换',
    standardHours: 0.2,
    unitPrice: 100.00,
    laborType: '增项工时',
    subtotal: 20.00,
    receivableAmount: 20.00,
    remarks: '客户额外需求',
    isPackageItem: false
  }
]

// 零件费用明细模拟数据
const partItems: SettlementPartItem[] = [
  {
    partCode: 'P001',
    partName: '机油（5L）',
    quantity: 1,
    unitPrice: 500.00,
    partType: '原厂件',
    subtotal: 500.00,
    receivableAmount: 0.00,
    remarks: '保养套餐内零件',
    isPackageItem: true
  },
  {
    partCode: 'P002',
    partName: '机滤',
    quantity: 1,
    unitPrice: 80.00,
    partType: '原厂件',
    subtotal: 80.00,
    receivableAmount: 0.00,
    remarks: '保养套餐内零件',
    isPackageItem: true
  },
  {
    partCode: 'P003',
    partName: '空调滤芯',
    quantity: 1,
    unitPrice: 240.00,
    partType: '原厂件',
    subtotal: 240.00,
    receivableAmount: 240.00,
    remarks: '客户额外需求',
    isPackageItem: false
  }
]

// 操作日志模拟数据
const operationLogs: OperationLog[] = [
  {
    operation: '系统自动生成结算单',
    operator: '系统',
    operatedAt: '2024-12-12 14:30:00'
  },
  {
    operation: '质检状态更新为"质检通过"',
    operator: '质检员小李',
    operatedAt: '2024-12-12 15:15:00'
  },
  {
    operation: '结算单推送给客户',
    operator: '陈经理',
    operatedAt: '2024-12-12 15:30:00'
  }
]

// 结算单详情模拟数据
const settlementDetails: SettlementDetail[] = [
  {
    id: '1',
    settlementNo: 'ST202412120001',
    workOrderNo: 'WO202412120001',
    workOrderType: 'maintenance',
    settlementStatus: 'pending_settlement',
    paymentStatus: 'pending',
    createdAt: '2024-12-12 14:30:00',
    serviceAdvisor: '陈经理',

    // 客户信息
    customerName: '张三',
    customerPhone: '13888888888',

    // 车辆信息
    vehiclePlate: '京A12345',
    vin: 'LRW1234567890ABCD',
    vehicleModel: 'Model Y',
    vehicleConfig: '标准版',
    vehicleColor: '珍珠白',
    vehicleAge: '2年',
    mileage: '35,000公里',

    // 服务包信息
    servicePackage: {
      packageCode: 'SP001',
      packageName: 'Model Y 标准保养服务包'
    },

    // 工时费用明细
    laborItems,

    // 零件费用明细
    partItems,

    // 费用汇总
    laborTotalAmount: 145.00,
    laborReceivableAmount: 20.00,
    partTotalAmount: 820.00,
    partReceivableAmount: 240.00,
    warrantyAmount: 0.00,
    totalAmount: 965.00,
    packageRightsDeduction: 725.00,
    receivableTotal: 260.00,
    payableAmount: 240.00,

    // 支付信息
    paidAmount: 725.00,
    discountAmount: 20.00,
    remarks: '客户额外需求空调滤芯更换',

    // 操作日志
    operationLogs
  }
]

// 收退款记录模拟数据
const paymentRecords: PaymentRecord[] = [
  {
    id: '1_1',
    paymentNo: 'PY202412120001',
    businessType: '收款',
    transactionNo: 'TXN123456789',
    paymentMethod: 'cash',
    amount: 200.00,
    paymentType: '全款',
    paymentTime: '2024-12-12 15:30:00',
    remarks: '客户现金支付',
    createdAt: '2024-12-12 15:30:00'
  },
  {
    id: '2_1',
    paymentNo: 'PY202412120002',
    businessType: '收款',
    transactionNo: 'TXN987654321',
    paymentMethod: 'pos',
    amount: 1200.00,
    paymentType: '全款',
    paymentTime: '2024-12-12 10:15:00',
    remarks: 'POS机刷卡支付',
    createdAt: '2024-12-12 10:15:00'
  }
]

export const settlementMockData = {
  list: settlementList,
  details: settlementDetails,
  paymentRecords
}
