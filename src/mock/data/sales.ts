// dms-frontend/src/mock/data/sales.ts
// 导入你在 dms-frontend/src/types/module.d.ts 中定义的类型
import type { VehicleListItem } from '@/types/module.d.ts';

// 示例：模拟车辆列表数据
export const mockVehicleList: VehicleListItem[] = [
  {
    id: '1001',
    vin: 'VINTESLAX0001',
    model: 'Model X',
    brand: 'Tesla',
    color: 'Pearl White Multi-Coat',
    price: 88000,
    status: 'in_stock',
    manufactureDate: '2023-01-15',
    engineNumber: 'ENGX001',
    createdAt: '2023-01-20T10:00:00Z',
    updatedAt: '2023-01-20T10:00:00Z'
  },
  {
    id: '1002',
    vin: 'VINTESLA30002',
    model: 'Model 3',
    brand: 'Tesla',
    color: 'Deep Blue Metallic',
    price: 49000,
    status: 'sold',
    manufactureDate: '2022-11-20',
    engineNumber: 'ENG3002',
    createdAt: '2022-12-01T11:30:00Z',
    updatedAt: '2023-02-10T15:00:00Z'
  },
  {
    id: '1003',
    vin: 'VINHONDA_CIVIC003',
    model: 'Civic',
    brand: 'Honda',
    color: 'Crystal Black Pearl',
    price: 28500,
    status: 'in_stock',
    manufactureDate: '2024-03-01',
    engineNumber: 'ENGHC003',
    createdAt: '2024-03-05T09:00:00Z',
    updatedAt: '2024-03-05T09:00:00Z'
  },
  {
    id: '1004',
    vin: 'VINTOYOTA_CAMRY004',
    model: 'Camry',
    brand: 'Toyota',
    color: 'Super White',
    price: 32000,
    status: 'reserved',
    manufactureDate: '2023-09-10',
    engineNumber: 'ENGTC004',
    createdAt: '2023-09-15T14:00:00Z',
    updatedAt: '2023-10-01T11:00:00Z'
  },
  {
    id: '1005',
    vin: 'VINTESLAY0005',
    model: 'Model Y',
    brand: 'Tesla',
    color: 'Midnight Silver Metallic',
    price: 55000,
    status: 'in_stock',
    manufactureDate: '2024-02-20',
    engineNumber: 'ENGY005',
    createdAt: '2024-02-25T08:00:00Z',
    updatedAt: '2024-02-25T08:00:00Z'
  },
  {
    id: '1006',
    vin: 'VINBMWX50006',
    model: 'X5',
    brand: 'BMW',
    color: 'Black Sapphire Metallic',
    price: 70000,
    status: 'in_stock',
    manufactureDate: '2023-10-05',
    engineNumber: 'ENGBX006',
    createdAt: '2023-10-10T16:00:00Z',
    updatedAt: '2023-10-10T16:00:00Z'
  },
  {
    id: '1007',
    vin: 'VINAUDIA40007',
    model: 'A4',
    brand: 'Audi',
    color: 'Mythos Black Metallic',
    price: 45000,
    status: 'in_stock',
    manufactureDate: '2024-01-01',
    engineNumber: 'ENGA4007',
    createdAt: '2024-01-05T09:00:00Z',
    updatedAt: '2024-01-05T09:00:00Z'
  },
  {
    id: '1008',
    vin: 'VINMERCEDESE_CLASS008',
    model: 'E-Class',
    brand: 'Mercedes-Benz',
    color: 'Obsidian Black Metallic',
    price: 60000,
    status: 'sold',
    manufactureDate: '2022-07-20',
    engineNumber: 'ENGME008',
    createdAt: '2022-08-01T10:00:00Z',
    updatedAt: '2022-09-01T12:00:00Z'
  },
  {
    id: '1009',
    vin: 'VINVOLVOXC60009',
    model: 'XC60',
    brand: 'Volvo',
    color: 'Onyx Black Metallic',
    price: 50000,
    status: 'in_stock',
    manufactureDate: '2023-04-10',
    engineNumber: 'ENGVX009',
    createdAt: '2023-04-15T11:00:00Z',
    updatedAt: '2023-04-15T11:00:00Z'
  },
  {
    id: '1010',
    vin: 'VINKIA_SPORTAGE010',
    model: 'Sportage',
    brand: 'KIA',
    color: 'Gravity Gray',
    price: 29000,
    status: 'in_stock',
    manufactureDate: '2024-05-01',
    engineNumber: 'ENGKS010',
    createdAt: '2024-05-05T13:00:00Z',
    updatedAt: '2024-05-05T13:00:00Z'
  }
  // 可以继续添加更多模拟数据
]; 