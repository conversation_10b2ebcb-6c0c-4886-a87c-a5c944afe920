import type { Invoice, InvoiceDetail, AccessoryItem, OTRFeeItem, ReceiptItem } from '@/types/invoice';

// 发票Mock数据
export const mockInvoiceList: InvoiceDetail[] = [
  {
    id: 1,
    invoiceNumber: '522102-2024001',
    invoiceDate: '2024-01-15',
    orderNumber: 'ORD20240115001',
    customerName: '李明',
    customerPhone: '+***********',
    customerEmail: '<EMAIL>',
    customerAddress: '123 Jalan Sultan, Kuala Lumpur',
    customerState: '吉隆坡',
    customerCity: '吉隆坡',
    customerPostcode: '50000',
    vin: 'ABCDE12345678901234',
    model: 'Proton X70',
    variant: 'Premium 2WD',
    color: '白色',
    salesStore: '门店1',
    salesConsultant: '张三',
    paymentMethod: '贷款',
    financeCompany: 'Bank Islam',
    loanAmount: 85000,
    invoiceAmount: 95000,
    createdTime: '2024-01-15 10:30:00',
    status: '已开具',
    // InvoiceDetail 特有字段
    companyName: 'PERODUA SALES SDN BHD',
    companyAddress: 'Your Company Address',
    gstNumber: '************',
    sstNumber: 'B16-1808-********',
    contactPhone: '03-********',
    contactEmail: '<EMAIL>',
    deliveryNumber: 'DEL2024001',
    salesConsultantId: '500598',
    tinNumber: 'EI0000000010',
    modelCode: 'MODELX',
    modelDescription: '轿车',
    engineNumber: '2NR3D31119',
    chassisNumber: 'PM2M8D6SD02448917',
    engineCapacity: '1496 cc',
    fuelType: 'Petrol',
    transmission: 'Automatic',
    year: '2025',
    vehicleRegistrationDate: '09-MAY-2025',
    creator: '系统管理员',
    updater: '系统管理员',
    updateTime: '2024-01-15 11:00:00',
    financeType: 'Finance',
    loanTerm: 108,
    interestRate: 0.05,
    monthlyPayment: 850,
    insuranceCompany: 'TAKAFUL IKHLAS GENERAL BERHAD',
    agentCode: 'TI',
    policyNumber: 'MMP25635488',
    policyDate: '08-MAY-2025',
    insuranceAmount: 2179.10,
    vehiclePrice: 47898.33,
    licensePlateFee: 0.00,
    accessories: [
      { id: 1, category: '内饰', name: '皮革座椅', unitPrice: 1500, quantity: 1, totalPrice: 1500 },
      { id: 2, category: '外观', name: '运动轮毂', unitPrice: 1300, quantity: 1, totalPrice: 1300 }
    ],
    totalAccessoryAmount: 2800.00,
    subtotal: 60015.55,
    otrFees: [
      { id: 1, billNumber: 'OTR001', feeItem: '路税', price: 120, effectiveDate: '2025-05-09', expiryDate: '2026-05-08' },
      { id: 2, billNumber: 'OTR002', feeItem: '注册费', price: 200, effectiveDate: '2025-05-09', expiryDate: '2025-05-09' }
    ],
    totalOtrFeeAmount: 320.00,
    insurancePremium: 2179.10,
    totalSalesPrice: 62484.65,
    adjustmentAmount: 0.00,
    invoiceNetValue: 62484.65,
    receipts: [
      { id: 1, receiptNumber: 'R001', businessType: '销售', serialNumber: 'LS001', channel: '银行转账', amount: 30000, collectionType: '定金', arrivalTime: '2024-01-10 09:00:00', remark: '定金支付' },
      { id: 2, receiptNumber: 'R002', businessType: '销售', serialNumber: 'LS002', channel: '贷款', amount: 32484.65, collectionType: '尾款', arrivalTime: '2024-01-15 10:00:00', remark: '贷款到账' }
    ]
  },
  {
    id: 2,
    invoiceNumber: '522102-2024002',
    invoiceDate: '2024-01-16',
    orderNumber: 'ORD20240116001',
    customerName: '王芳',
    customerPhone: '+***********',
    customerEmail: '<EMAIL>',
    customerAddress: '456 Jalan Putra, Petaling Jaya',
    customerState: '雪兰莪',
    customerCity: '八打灵再也',
    customerPostcode: '47400',
    vin: 'FGHIJ12345678901234',
    model: 'Proton Saga',
    variant: 'Standard AT',
    color: '蓝色',
    salesStore: '门店2',
    salesConsultant: '王五',
    paymentMethod: '现金',
    financeCompany: '-',
    loanAmount: 0,
    invoiceAmount: 45000,
    createdTime: '2024-01-16 14:20:00',
    status: '已开具',
    // InvoiceDetail 特有字段
    companyName: 'PERODUA SALES SDN BHD',
    companyAddress: 'Your Company Address',
    gstNumber: '************',
    sstNumber: 'B16-1808-********',
    contactPhone: '03-********',
    contactEmail: '<EMAIL>',
    deliveryNumber: 'DEL2024002',
    salesConsultantId: '500598',
    tinNumber: 'EI0000000010',
    modelCode: 'MODELY',
    modelDescription: '轿车',
    engineNumber: '2NR3D31119',
    chassisNumber: 'PM2M8D6SD02448917',
    engineCapacity: '1496 cc',
    fuelType: 'Petrol',
    transmission: 'Automatic',
    year: '2025',
    vehicleRegistrationDate: '09-MAY-2025',
    creator: '系统管理员',
    updater: '系统管理员',
    updateTime: '2024-01-16 14:30:00',
    financeType: 'Cash',
    loanTerm: 0,
    interestRate: 0,
    monthlyPayment: 0,
    insuranceCompany: 'TAKAFUL IKHLAS GENERAL BERHAD',
    agentCode: 'TI',
    policyNumber: 'MMP25635489',
    policyDate: '08-MAY-2025',
    insuranceAmount: 0,
    vehiclePrice: 45000,
    licensePlateFee: 0.00,
    accessories: [],
    totalAccessoryAmount: 0,
    subtotal: 45000,
    otrFees: [
      { id: 3, billNumber: 'OTR003', feeItem: '路税', price: 100, effectiveDate: '2025-05-09', expiryDate: '2026-05-08' }
    ],
    totalOtrFeeAmount: 100,
    insurancePremium: 0,
    totalSalesPrice: 45100,
    adjustmentAmount: 0.00,
    invoiceNetValue: 45100,
    receipts: [
      { id: 3, receiptNumber: 'R003', businessType: '销售', serialNumber: 'LS003', channel: '现金', amount: 45000, collectionType: '全款', arrivalTime: '2024-01-16 14:25:00', remark: '全款支付' }
    ]
  },
  {
    id: 3,
    invoiceNumber: '522102-2024003',
    invoiceDate: '2024-01-17',
    orderNumber: 'ORD20240117001',
    customerName: '陈强',
    customerPhone: '+***********',
    customerEmail: '<EMAIL>',
    customerAddress: '789 Jalan Tun Razak, Kuala Lumpur',
    customerState: '吉隆坡',
    customerCity: '吉隆坡',
    customerPostcode: '50400',
    vin: 'KLMNO12345678901234',
    model: 'Proton Persona',
    variant: 'Premium CVT',
    color: '红色',
    salesStore: '门店1',
    salesConsultant: '李四',
    paymentMethod: 'PHP',
    financeCompany: 'Public Bank',
    loanAmount: 52000,
    invoiceAmount: 58000,
    createdTime: '2024-01-17 09:15:00',
    status: '已开具',
    // InvoiceDetail 特有字段
    companyName: 'PERODUA SALES SDN BHD',
    companyAddress: 'Your Company Address',
    gstNumber: '************',
    sstNumber: 'B16-1808-********',
    contactPhone: '03-********',
    contactEmail: '<EMAIL>',
    deliveryNumber: 'DEL2024003',
    salesConsultantId: '500598',
    tinNumber: 'EI0000000010',
    modelCode: 'MODELZ',
    modelDescription: '掀背车',
    engineNumber: '2NR3D31119',
    chassisNumber: 'PM2M8D6SD02448917',
    engineCapacity: '1496 cc',
    fuelType: 'Petrol',
    transmission: 'CVT',
    year: '2025',
    vehicleRegistrationDate: '09-MAY-2025',
    creator: '系统管理员',
    updater: '系统管理员',
    updateTime: '2024-01-17 09:30:00',
    financeType: 'Finance',
    loanTerm: 84,
    interestRate: 0.04,
    monthlyPayment: 700,
    insuranceCompany: 'TAKAFUL IKHLAS GENERAL BERHAD',
    agentCode: 'TI',
    policyNumber: 'MMP25635490',
    policyDate: '08-MAY-2025',
    insuranceAmount: 2000,
    vehiclePrice: 58000,
    licensePlateFee: 0.00,
    accessories: [
      { id: 4, category: '内饰', name: '脚垫', unitPrice: 100, quantity: 1, totalPrice: 100 }
    ],
    totalAccessoryAmount: 100,
    subtotal: 58100,
    otrFees: [
      { id: 4, billNumber: 'OTR004', feeItem: '路税', price: 110, effectiveDate: '2025-05-09', expiryDate: '2026-05-08' }
    ],
    totalOtrFeeAmount: 110,
    insurancePremium: 2000,
    totalSalesPrice: 60210,
    adjustmentAmount: 0.00,
    invoiceNetValue: 60210,
    receipts: [
      { id: 4, receiptNumber: 'R004', businessType: '销售', serialNumber: 'LS004', channel: '银行转账', amount: 52000, collectionType: '贷款', arrivalTime: '2024-01-17 09:20:00', remark: '贷款到账' }
    ]
  }
]; 