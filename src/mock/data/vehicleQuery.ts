import type { VehicleListItem, VehicleConfiguration, WarehouseInfo, PaginationResponse } from '@/types/vehicleQuery';

// 模拟车辆列表数据
const mockVehicleList: VehicleListItem[] = [
  {
    factoryOrderNo: 'FO-20230101-001',
    vin: 'VIN12345678901234567',
    model: 'Model X',
    variant: 'Standard',
    color: 'Pearl White',
    warehouseName: 'Warehouse A',
    stockStatus: 'inStock',
    lockStatus: 'unlocked',
    invoiceStatus: 'notInvoiced',
    invoiceDate: '',
    deliveryStatus: 'notDelivered',
    deliveryDate: '',
    storageDate: '2023-01-05',
    productionDate: '2022-12-20',
  },
  {
    factoryOrderNo: 'FO-20230101-002',
    vin: 'VIN76543210987654321',
    model: 'Model Y',
    variant: 'Long Range',
    color: 'Deep Blue',
    warehouseName: 'Warehouse B',
    stockStatus: 'allocated',
    lockStatus: 'locked',
    invoiceStatus: 'invoiced',
    invoiceDate: '2023-01-10',
    deliveryStatus: 'notDelivered',
    deliveryDate: '',
    storageDate: '2023-01-08',
    productionDate: '2022-12-25',
  },
  {
    factoryOrderNo: 'FO-20230101-003',
    vin: 'VIN11223344556677889',
    model: 'Model X',
    variant: 'Performance',
    color: 'Black',
    warehouseName: 'Warehouse A',
    stockStatus: 'inStock',
    lockStatus: 'unlocked',
    invoiceStatus: 'notInvoiced',
    invoiceDate: '',
    deliveryStatus: 'notDelivered',
    deliveryDate: '',
    storageDate: '2023-01-06',
    productionDate: '2022-12-21',
  },
  {
    factoryOrderNo: 'FO-20230101-004',
    vin: 'VIN99887766554433221',
    model: 'Model S',
    variant: 'Plaid',
    color: 'Red',
    warehouseName: 'Warehouse C',
    stockStatus: 'inTransit',
    lockStatus: 'unlocked',
    invoiceStatus: 'notInvoiced',
    invoiceDate: '',
    deliveryStatus: 'notDelivered',
    deliveryDate: '',
    storageDate: '2023-01-09',
    productionDate: '2022-12-28',
  },
  {
    factoryOrderNo: 'FO-20230101-005',
    vin: 'VIN55443322118877665',
    model: 'Model Y',
    variant: 'Standard',
    color: 'Pearl White',
    warehouseName: 'Warehouse B',
    stockStatus: 'transferred',
    lockStatus: 'unlocked',
    invoiceStatus: 'invoiced',
    invoiceDate: '2023-01-12',
    deliveryStatus: 'delivered',
    deliveryDate: '2023-01-15',
    storageDate: '2023-01-07',
    productionDate: '2022-12-23',
  },
];

// 模拟车型配置数据（用于级联选择）
const mockVehicleConfigurations: any[] = [
  {
    model: 'Model X',
    variants: [
      { variant: 'Standard', colorOptions: ['Pearl White', 'Black', 'Red'] },
      { variant: 'Performance', colorOptions: ['Black', 'Deep Blue'] },
    ],
  },
  {
    model: 'Model Y',
    variants: [
      { variant: 'Standard', colorOptions: ['Pearl White', 'Deep Blue'] },
      { variant: 'Long Range', colorOptions: ['Black', 'Red', 'Deep Blue'] },
    ],
  },
  {
    model: 'Model S',
    variants: [
      { variant: 'Plaid', colorOptions: ['Black', 'Red', 'Pearl White'] },
    ],
  },
];

// 模拟仓库信息
const mockWarehouseInfo: WarehouseInfo[] = [
  { warehouseName: 'Warehouse A', warehouseCode: 'WA001', warehouseLocation: 'Location A', warehouseStatus: 'Active' },
  { warehouseName: 'Warehouse B', warehouseCode: 'WB002', warehouseLocation: 'Location B', warehouseStatus: 'Active' },
  { warehouseName: 'Warehouse C', warehouseCode: 'WC003', warehouseLocation: 'Location C', warehouseStatus: 'Inactive' },
];

export const getMockVehicleList = (params: any): Promise<PaginationResponse<VehicleListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredList = [...mockVehicleList];

      if (params.vin) {
        filteredList = filteredList.filter(item => item.vin.includes(params.vin));
      }
      if (params.factoryOrderNo) {
        filteredList = filteredList.filter(item => item.factoryOrderNo.includes(params.factoryOrderNo));
      }
      if (params.warehouseName) {
        filteredList = filteredList.filter(item => item.warehouseName === params.warehouseName);
      }
      if (params.model) {
        filteredList = filteredList.filter(item => item.model === params.model);
      }
      if (params.variant) {
        filteredList = filteredList.filter(item => item.variant === params.variant);
      }
      if (params.color) {
        filteredList = filteredList.filter(item => item.color === params.color);
      }
      if (params.lockStatus) {
        filteredList = filteredList.filter(item => item.lockStatus === params.lockStatus);
      }
      if (params.invoiceStatus) {
        filteredList = filteredList.filter(item => item.invoiceStatus === params.invoiceStatus);
      }

      // Date range filtering
      if (params.invoiceDateStart && params.invoiceDateEnd) {
        filteredList = filteredList.filter(item => item.invoiceDate >= params.invoiceDateStart && item.invoiceDate <= params.invoiceDateEnd);
      }
      if (params.storageDateStart && params.storageDateEnd) {
        filteredList = filteredList.filter(item => item.storageDate >= params.storageDateStart && item.storageDate <= params.storageDateEnd);
      }
      if (params.productionDateStart && params.productionDateEnd) {
        filteredList = filteredList.filter(item => item.productionDate >= params.productionDateStart && item.productionDate <= params.productionDateEnd);
      }

      // 优先展示库存状态为"在库"且锁定状态为"否"的可配车辆
      // 筛选出符合条件的车辆并置顶
      const availableVehicles = filteredList.filter(item => item.stockStatus === 'inStock' && item.lockStatus === 'unlocked');
      const otherVehicles = filteredList.filter(item => !(item.stockStatus === 'inStock' && item.lockStatus === 'unlocked'));
      const sortedAndFilteredList = [...availableVehicles, ...otherVehicles];

      // 默认按入库时间倒序排列 (针对所有数据，不只是可配车辆)
      sortedAndFilteredList.sort((a, b) => new Date(b.storageDate).getTime() - new Date(a.storageDate).getTime());


      const { pageNum = 1, pageSize = 10 } = params;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const paginatedList = sortedAndFilteredList.slice(start, end);

      resolve({
        list: paginatedList,
        total: sortedAndFilteredList.length,
        pageNum,
        pageSize,
      });
    }, 500); // 模拟网络延迟
  });
};

export const getMockVehicleConfiguration = (): Promise<any[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockVehicleConfigurations);
    }, 200);
  });
};

export const getMockWarehouseInfo = (): Promise<WarehouseInfo[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockWarehouseInfo);
    }, 200);
  });
};
