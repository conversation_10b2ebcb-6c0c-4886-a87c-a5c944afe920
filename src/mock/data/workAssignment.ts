import type { 
  WorkOrderListItem, 
  TechnicianInfo, 
  AssignmentStatistics,
  TechnicianSchedule,
  TechnicianTimelineItem
} from '@/types/module.d.ts';

// 工单 Mock 数据
export const mockWorkOrderList: WorkOrderListItem[] = [
  {
    workOrderId: 'WO20241210001',
    workOrderType: 'repair',
    priority: 'urgent',
    status: 'pending_assign',
    customerName: '王先生',
    customerPhone: '13812345678',
    licensePlate: '京A88888',
    vehicleModel: '宝马X5 2.0T 豪华版 白色',
    vinNumber: 'WBXPC9C59EP123456',
    serviceAdvisorId: 'SA001',
    serviceAdvisorName: '李服务',
    estimatedWorkHours: 4.5,
    totalAmount: 3500.00,
    waitingDuration: 45,
    hasWarrantyClaim: true,
    hasOutsourcing: false,
    hasAdditionalItems: false,
    isUrgent: true,
    creationTime: '2024-12-10 08:30:00',
    updateTime: '2024-12-10 08:30:00',
    mainServiceItems: ['发动机故障诊断', '更换点火线圈'],
    customerRequirements: '客户要求今日必须完成维修'
  },
  {
    workOrderId: 'WO20241210002',
    workOrderType: 'maintenance',
    priority: 'normal',
    status: 'pending_assign',
    customerName: '张女士',
    customerPhone: '13987654321',
    licensePlate: '京B66666',
    vehicleModel: '奔驰C260L 1.5T 运动版 黑色',
    vinNumber: 'WDD2050461F123456',
    serviceAdvisorId: 'SA002',
    serviceAdvisorName: '王服务',
    estimatedWorkHours: 2.0,
    totalAmount: 1200.00,
    waitingDuration: 120,
    hasWarrantyClaim: false,
    hasOutsourcing: false,
    hasAdditionalItems: false,
    isUrgent: false,
    creationTime: '2024-12-10 07:00:00',
    updateTime: '2024-12-10 07:00:00',
    mainServiceItems: ['定期保养', '更换机油机滤']
  },
  {
    workOrderId: 'WO20241210003',
    workOrderType: 'insurance',
    priority: 'normal',
    status: 'pending_start',
    customerName: '刘先生',
    customerPhone: '13611111111',
    licensePlate: '京C12345',
    vehicleModel: '奥迪A4L 2.0T 进取版 银色',
    vinNumber: 'WAUZZZ4B5KN123456',
    serviceAdvisorId: 'SA001',
    serviceAdvisorName: '李服务',
    estimatedWorkHours: 6.0,
    totalAmount: 4800.00,
    assignedTechnicianId: 'T001',
    assignedTechnicianName: '张师傅',
    assignmentTime: '2024-12-10 09:15:00',
    estimatedStartTime: '2024-12-10 10:00:00',
    estimatedFinishTime: '2024-12-10 16:00:00',
    waitingDuration: 195,
    hasWarrantyClaim: false,
    hasOutsourcing: true,
    hasAdditionalItems: false,
    isUrgent: false,
    creationTime: '2024-12-10 06:45:00',
    updateTime: '2024-12-10 09:15:00',
    mainServiceItems: ['车身钣金修复', '喷漆处理'],
    assignmentNotes: '客户要求使用原厂配件'
  },
  {
    workOrderId: 'WO20241210004',
    workOrderType: 'repair',
    priority: 'normal',
    status: 'in_progress',
    customerName: '陈女士',
    customerPhone: '13722222222',
    licensePlate: '京D99999',
    vehicleModel: '大众帕萨特 1.4T 舒适版 蓝色',
    vinNumber: 'LSVAMA2A5KH123456',
    serviceAdvisorId: 'SA003',
    serviceAdvisorName: '赵服务',
    estimatedWorkHours: 3.5,
    totalAmount: 2800.00,
    assignedTechnicianId: 'T002',
    assignedTechnicianName: '李师傅',
    assignmentTime: '2024-12-10 08:00:00',
    estimatedStartTime: '2024-12-10 08:30:00',
    estimatedFinishTime: '2024-12-10 12:00:00',
    actualStartTime: '2024-12-10 08:35:00',
    waitingDuration: 240,
    hasWarrantyClaim: false,
    hasOutsourcing: false,
    hasAdditionalItems: true,
    isUrgent: false,
    creationTime: '2024-12-10 04:30:00',
    updateTime: '2024-12-10 08:35:00',
    mainServiceItems: ['变速箱维修', '更换变速箱油'],
    assignmentNotes: '已与客户确认增项内容'
  },
  {
    workOrderId: 'WO20241210005',
    workOrderType: 'maintenance',
    priority: 'urgent',
    status: 'pending_assign',
    customerName: '孙先生',
    customerPhone: '13833333333',
    licensePlate: '京E77777',
    vehicleModel: '特斯拉Model 3 标准续航版 红色',
    vinNumber: '5YJ3E1EA5KF123456',
    serviceAdvisorId: 'SA002',
    serviceAdvisorName: '王服务',
    estimatedWorkHours: 1.5,
    totalAmount: 800.00,
    waitingDuration: 30,
    hasWarrantyClaim: true,
    hasOutsourcing: false,
    hasAdditionalItems: false,
    isUrgent: true,
    creationTime: '2024-12-10 09:00:00',
    updateTime: '2024-12-10 09:00:00',
    mainServiceItems: ['电池系统检测', '软件更新'],
    customerRequirements: 'VIP客户，优先处理'
  },
  {
    workOrderId: 'WO20241210006',
    workOrderType: 'repair',
    priority: 'normal',
    status: 'completed',
    customerName: '周女士',
    customerPhone: '13944444444',
    licensePlate: '京F55555',
    vehicleModel: '丰田凯美瑞 2.0L 豪华版 白色',
    vinNumber: 'JTNBE46K403123456',
    serviceAdvisorId: 'SA001',
    serviceAdvisorName: '李服务',
    estimatedWorkHours: 2.5,
    totalAmount: 1800.00,
    assignedTechnicianId: 'T003',
    assignedTechnicianName: '王师傅',
    assignmentTime: '2024-12-10 06:30:00',
    estimatedStartTime: '2024-12-10 07:00:00',
    estimatedFinishTime: '2024-12-10 09:30:00',
    actualStartTime: '2024-12-10 07:05:00',
    actualFinishTime: '2024-12-10 09:20:00',
    waitingDuration: 390,
    hasWarrantyClaim: false,
    hasOutsourcing: false,
    hasAdditionalItems: false,
    isUrgent: false,
    creationTime: '2024-12-09 23:00:00',
    updateTime: '2024-12-10 09:20:00',
    mainServiceItems: ['空调系统维修', '更换压缩机'],
    assignmentNotes: '已完成，质检通过'
  }
];

// 技师信息 Mock 数据
export const mockTechnicianList: TechnicianInfo[] = [
  {
    technicianId: 'T001',
    employeeNumber: 'EMP001',
    technicianName: '张师傅',
    department: '维修部',
    position: '高级技师',
    specialization: '发动机专家',
    skillLevel: '高级',
    currentStatus: 'busy',
    workLoadStatus: 'busy',
    currentWorkOrders: 2,
    totalEstimatedHours: 6.5,
    availableHours: 1.5,
    workStartTime: '08:00',
    workEndTime: '18:00',
    breakStartTime: '12:00',
    breakEndTime: '13:00',
    totalCompletedOrders: 856,
    averageEfficiency: 1.05,
    qualityScore: 4.8,
    customerSatisfaction: 4.9,
    certifications: ['BMW认证技师', '发动机专业认证'],
    phoneNumber: '13700001111',
    hireDate: '2020-03-15'
  },
  {
    technicianId: 'T002',
    employeeNumber: 'EMP002',
    technicianName: '李师傅',
    department: '维修部',
    position: '中级技师',
    specialization: '变速箱专家',
    skillLevel: '中级',
    currentStatus: 'busy',
    workLoadStatus: 'moderate',
    currentWorkOrders: 1,
    totalEstimatedHours: 3.5,
    availableHours: 4.5,
    workStartTime: '08:00',
    workEndTime: '18:00',
    breakStartTime: '12:00',
    breakEndTime: '13:00',
    totalCompletedOrders: 642,
    averageEfficiency: 0.98,
    qualityScore: 4.6,
    customerSatisfaction: 4.7,
    certifications: ['变速箱专业认证'],
    phoneNumber: '13700002222',
    hireDate: '2021-07-20'
  },
  {
    technicianId: 'T003',
    employeeNumber: 'EMP003',
    technicianName: '王师傅',
    department: '维修部',
    position: '高级技师',
    specialization: '电气系统专家',
    skillLevel: '高级',
    currentStatus: 'available',
    workLoadStatus: 'idle',
    currentWorkOrders: 0,
    totalEstimatedHours: 0,
    availableHours: 8.0,
    workStartTime: '08:00',
    workEndTime: '18:00',
    breakStartTime: '12:00',
    breakEndTime: '13:00',
    totalCompletedOrders: 723,
    averageEfficiency: 1.12,
    qualityScore: 4.9,
    customerSatisfaction: 4.8,
    certifications: ['电气系统认证', 'Tesla认证技师'],
    phoneNumber: '13700003333',
    hireDate: '2019-11-08'
  },
  {
    technicianId: 'T004',
    employeeNumber: 'EMP004',
    technicianName: '赵师傅',
    department: '钣金部',
    position: '中级技师',
    specialization: '钣金喷漆专家',
    skillLevel: '中级',
    currentStatus: 'available',
    workLoadStatus: 'moderate',
    currentWorkOrders: 1,
    totalEstimatedHours: 4.0,
    availableHours: 4.0,
    workStartTime: '08:00',
    workEndTime: '18:00',
    breakStartTime: '12:00',
    breakEndTime: '13:00',
    totalCompletedOrders: 445,
    averageEfficiency: 0.95,
    qualityScore: 4.5,
    customerSatisfaction: 4.6,
    certifications: ['钣金喷漆认证'],
    phoneNumber: '13700004444',
    hireDate: '2022-04-12'
  },
  {
    technicianId: 'T005',
    employeeNumber: 'EMP005',
    technicianName: '孙师傅',
    department: '维修部',
    position: '初级技师',
    specialization: '保养维护专家',
    skillLevel: '初级',
    currentStatus: 'available',
    workLoadStatus: 'idle',
    currentWorkOrders: 0,
    totalEstimatedHours: 0,
    availableHours: 8.0,
    workStartTime: '08:00',
    workEndTime: '18:00',
    breakStartTime: '12:00',
    breakEndTime: '13:00',
    totalCompletedOrders: 287,
    averageEfficiency: 0.88,
    qualityScore: 4.3,
    customerSatisfaction: 4.4,
    certifications: ['基础维修认证'],
    phoneNumber: '13700005555',
    hireDate: '2023-09-01'
  },
  {
    technicianId: 'T006',
    employeeNumber: 'EMP006',
    technicianName: '周师傅',
    department: '维修部',
    position: '高级技师',
    specialization: '综合维修专家',
    skillLevel: '高级',
    currentStatus: 'busy',
    workLoadStatus: 'overloaded',
    currentWorkOrders: 3,
    totalEstimatedHours: 9.5,
    availableHours: 0,
    workStartTime: '08:00',
    workEndTime: '18:00',
    breakStartTime: '12:00',
    breakEndTime: '13:00',
    totalCompletedOrders: 934,
    averageEfficiency: 1.08,
    qualityScore: 4.7,
    customerSatisfaction: 4.8,
    certifications: ['多品牌认证技师', '故障诊断专家'],
    phoneNumber: '13700006666',
    hireDate: '2018-05-22'
  }
];

// 服务顾问 Mock 数据
export const mockServiceAdvisors = [
  { id: 'SA001', name: '李服务' },
  { id: 'SA002', name: '王服务' },
  { id: 'SA003', name: '赵服务' },
  { id: 'SA004', name: '钱服务' }
];

// 派工统计 Mock 数据
export const mockAssignmentStatistics: AssignmentStatistics = {
  statsDate: '2024-12-10',
  totalOrders: 128,
  pendingAssignment: 23,
  assignedOrders: 56,
  inProgressOrders: 32,
  completedOrders: 47,
  cancelledOrders: 2,
  totalTechnicians: 6,
  availableTechnicians: 3,
  busyTechnicians: 2,
  overloadedTechnicians: 1,
  averageAssignmentTime: 15.5,
  averageWaitingTime: 45.2,
  onTimeCompletionRate: 0.857,
  reassignmentRate: 0.035,
  totalEstimatedHours: 156.5,
  totalActualHours: 142.8,
  averageEfficiency: 1.02,
  utilizationRate: 0.73
};

// 技师时间轴数据
export const generateTechnicianSchedules = (): TechnicianSchedule[] => {
  const schedules: TechnicianSchedule[] = [];
  
  mockTechnicianList.forEach(technician => {
    const workItems: TechnicianTimelineItem[] = [];
    
    // 根据技师当前工单数生成时间轴数据
    if (technician.currentWorkOrders > 0) {
      for (let i = 0; i < technician.currentWorkOrders; i++) {
        const startHour = 8 + i * 3; // 每3小时一个工单
        const workHours = Math.random() * 3 + 1; // 1-4小时工时
        
        workItems.push({
          workOrderId: `WO2024121000${i + 1}`,
          customerName: ['王先生', '张女士', '李先生'][i] || '客户',
          licensePlate: [`京A${1000 + i}`, `京B${2000 + i}`, `京C${3000 + i}`][i] || '京A1234',
          workOrderType: ['repair', 'maintenance', 'insurance'][i % 3] as any,
          priority: i === 0 ? 'urgent' : 'normal',
          estimatedStartTime: `${startHour.toString().padStart(2, '0')}:00`,
          estimatedFinishTime: `${(startHour + Math.ceil(workHours)).toString().padStart(2, '0')}:00`,
          estimatedWorkHours: workHours,
          status: i === 0 ? 'in_progress' : 'pending_start',
          isConflict: false
        });
      }
    }
    
    // 计算空闲时间段
    const availableSlots = [];
    let currentTime = 8; // 从8点开始
    
    workItems.forEach(item => {
      const startTime = parseInt(item.estimatedStartTime.split(':')[0]);
      if (currentTime < startTime) {
        availableSlots.push({
          startTime: `${currentTime.toString().padStart(2, '0')}:00`,
          endTime: `${startTime.toString().padStart(2, '0')}:00`
        });
      }
      currentTime = parseInt(item.estimatedFinishTime.split(':')[0]);
    });
    
    // 添加最后的空闲时间段
    if (currentTime < 18) {
      availableSlots.push({
        startTime: `${currentTime.toString().padStart(2, '0')}:00`,
        endTime: '18:00'
      });
    }
    
    schedules.push({
      technicianInfo: technician,
      workItems,
      availableSlots,
      workloadPercentage: (technician.totalEstimatedHours / 8) * 100,
      totalScheduledHours: technician.totalEstimatedHours,
      remainingHours: technician.availableHours
    });
  });
  
  return schedules;
}; 