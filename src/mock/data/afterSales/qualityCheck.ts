import type {
  QualityCheckListItem,
  QualityCheckSearchParams,
  QualityCheckDetail,
  QualityCheckSubmitForm,
  QualityCheckAuditForm,
  QualityCheckReworkForm,
  QualityCheckStatistics,
  QualityCheckTemplate,
  PaginationResponse,
  ApiResponse,
  ExportQualityCheckParams,
  BatchQualityCheckOperation,
  QualityCheckStatus,
  WorkOrderType,
  QualityCheckItemType,
  QualityCheckResult
} from '@/types/afterSales/qualityCheck';

// 动态生成质检Mock数据
function generateMockQualityCheckData(): QualityCheckListItem[] {
  const dataCount = Math.floor(Math.random() * 20) + 50; // 50-70条数据
  const mockData: QualityCheckListItem[] = [];
  
  const statuses: QualityCheckStatus[] = ['pending_check', 'checking', 'pending_review', 'passed', 'rework'];
  const workOrderTypes: WorkOrderType[] = ['maintenance', 'repair', 'insurance'];
  const vehicleModels = ['Model S', 'Model X', 'Model 3', 'Model Y', 'Cybertruck'];
  const colors = ['白色', '黑色', '红色', '蓝色', '银色', '灰色'];
  const technicians = ['张师傅', '李师傅', '王师傅', '赵师傅', '陈师傅', '刘师傅'];
  const customers = ['张三', '李四', '王五', '赵六', '陈七', '刘八', '孙九', '周十'];
  const configs = ['标准版', '长续航版', '高性能版', 'Plaid版'];
  
  for (let i = 0; i < dataCount; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const workOrderType = workOrderTypes[Math.floor(Math.random() * workOrderTypes.length)];
    
    const estimatedHours = Math.round((Math.random() * 6 + 1) * 2) / 2;
    const actualHours = status !== 'pending_check' ? Math.round((Math.random() * 6 + 1) * 2) / 2 : undefined;
    
    mockData.push({
      id: `qc-${i + 1}`,
      qualityCheckNo: `QC${String(Date.now() + i).slice(-8)}`,
      status,
      workOrderNo: `WO${String(i + 1).padStart(6, '0')}`,
      workOrderType,
      isClaimRelated: Math.random() > 0.7,
      isOutsourceRelated: Math.random() > 0.8,
      serviceCustomerName: customers[Math.floor(Math.random() * customers.length)],
      serviceCustomerPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      plateNumber: `粤B${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleConfig: configs[Math.floor(Math.random() * configs.length)],
      vehicleColor: colors[Math.floor(Math.random() * colors.length)],
      technicianName: technicians[Math.floor(Math.random() * technicians.length)],
      startTime: status !== 'pending_check' ? createTime.toISOString() : undefined,
      finishTime: (status === 'passed' || status === 'rework') ? 
        new Date(createTime.getTime() + Math.random() * 8 * 60 * 60 * 1000).toISOString() : undefined,
      estimatedHours,
      actualHours,
      reworkReason: status === 'rework' ? ['质检不合格，需要重新处理', '零件质量问题', '工艺不达标', '客户要求返工'][Math.floor(Math.random() * 4)] : undefined,
      reworkRequirement: status === 'rework' ? ['请检查制动系统相关项目', '重新更换零件', '按标准工艺重做', '联系客户确认需求'][Math.floor(Math.random() * 4)] : undefined,
      createTime: createTime.toISOString(),
      updateTime: createTime.toISOString()
    });
  }
  
  return mockData;
}

// 生成质检项目Mock数据
function generateMockQualityCheckItems(qualityCheckId: string): any[] {
  return [
    // 制动系统检查
    {
      id: 'item-1',
      qualityCheckId,
      categoryCode: 'BRAKE_SYSTEM',
      categoryName: '制动系统检查',
      itemCode: 'BRAKE_PEDAL_TRAVEL',
      itemName: '制动踏板行程',
      itemType: 'NUMERIC' as QualityCheckItemType,
      numericValue: 120,
      standardValue: '≤150mm',
      unit: 'mm',
      isRequired: true,
      sortOrder: 1,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    {
      id: 'item-2',
      qualityCheckId,
      categoryCode: 'BRAKE_SYSTEM',
      categoryName: '制动系统检查',
      itemCode: 'BRAKE_FLUID_LEVEL',
      itemName: '制动液液位',
      itemType: 'BOOLEAN' as QualityCheckItemType,
      checkResult: 'PASS' as QualityCheckResult,
      standardValue: '在MIN-MAX之间',
      isRequired: true,
      sortOrder: 2,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    {
      id: 'item-3',
      qualityCheckId,
      categoryCode: 'BRAKE_SYSTEM',
      categoryName: '制动系统检查',
      itemCode: 'BRAKE_DISC_THICKNESS',
      itemName: '制动盘厚度',
      itemType: 'NUMERIC' as QualityCheckItemType,
      numericValue: 22,
      standardValue: '≥20mm',
      unit: 'mm',
      isRequired: true,
      sortOrder: 3,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    // 电气系统检查
    {
      id: 'item-4',
      qualityCheckId,
      categoryCode: 'ELECTRICAL_SYSTEM',
      categoryName: '电气系统检查',
      itemCode: 'BATTERY_VOLTAGE',
      itemName: '电池电压',
      itemType: 'NUMERIC' as QualityCheckItemType,
      numericValue: 12.6,
      standardValue: '12.0-12.8V',
      unit: 'V',
      isRequired: true,
      sortOrder: 4,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    {
      id: 'item-5',
      qualityCheckId,
      categoryCode: 'ELECTRICAL_SYSTEM',
      categoryName: '电气系统检查',
      itemCode: 'LIGHTS_FUNCTION',
      itemName: '灯光功能',
      itemType: 'BOOLEAN' as QualityCheckItemType,
      checkResult: 'PASS' as QualityCheckResult,
      standardValue: '所有灯光正常工作',
      isRequired: true,
      sortOrder: 5,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    // 外观检查
    {
      id: 'item-6',
      qualityCheckId,
      categoryCode: 'APPEARANCE_CHECK',
      categoryName: '外观检查',
      itemCode: 'PAINT_CONDITION',
      itemName: '漆面状况',
      itemType: 'TEXT' as QualityCheckItemType,
      textValue: '漆面光洁，无划痕',
      standardValue: '漆面完好，无明显缺陷',
      isRequired: true,
      sortOrder: 6,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    },
    {
      id: 'item-7',
      qualityCheckId,
      categoryCode: 'APPEARANCE_CHECK',
      categoryName: '外观检查',
      itemCode: 'PANEL_ALIGNMENT',
      itemName: '钣金对齐',
      itemType: 'BOOLEAN' as QualityCheckItemType,
      checkResult: 'PASS' as QualityCheckResult,
      standardValue: '钣金对齐良好',
      isRequired: true,
      sortOrder: 7,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
  ];
}

const mockQualityCheckData = generateMockQualityCheckData();

/**
 * 获取质检单列表Mock数据
 */
export const getMockQualityCheckList = (
  params: QualityCheckSearchParams
): Promise<PaginationResponse<QualityCheckListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockQualityCheckData];
      
      if (params.workOrderNo) {
        filteredData = filteredData.filter(item => 
          item.workOrderNo.toLowerCase().includes(params.workOrderNo!.toLowerCase())
        );
      }
      
      if (params.qualityCheckNo) {
        filteredData = filteredData.filter(item => 
          item.qualityCheckNo.toLowerCase().includes(params.qualityCheckNo!.toLowerCase())
        );
      }
      
      if (params.status && params.status.length > 0) {
        filteredData = filteredData.filter(item => 
          params.status!.includes(item.status)
        );
      }
      
      if (params.workOrderType) {
        filteredData = filteredData.filter(item => item.workOrderType === params.workOrderType);
      }
      
      if (params.technicianName) {
        filteredData = filteredData.filter(item => 
          item.technicianName.includes(params.technicianName!)
        );
      }
      
      if (params.plateNumber) {
        filteredData = filteredData.filter(item => 
          item.plateNumber.includes(params.plateNumber!)
        );
      }
      
      if (params.serviceCustomerName) {
        filteredData = filteredData.filter(item => 
          item.serviceCustomerName.includes(params.serviceCustomerName!)
        );
      }
      
      if (params.isClaimRelated !== undefined) {
        filteredData = filteredData.filter(item => item.isClaimRelated === params.isClaimRelated);
      }
      
      if (params.isOutsourceRelated !== undefined) {
        filteredData = filteredData.filter(item => item.isOutsourceRelated === params.isOutsourceRelated);
      }
      
      // 时间范围过滤
      if (params.createTimeStart) {
        filteredData = filteredData.filter(item => 
          item.createTime >= params.createTimeStart!
        );
      }
      
      if (params.createTimeEnd) {
        filteredData = filteredData.filter(item => 
          item.createTime <= params.createTimeEnd!
        );
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        data: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 500);
  });
};

/**
 * 获取质检单详情Mock数据
 */
export const getMockQualityCheckDetail = (id: string): Promise<QualityCheckDetail> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const qualityCheck = mockQualityCheckData.find(item => item.id === id);
      if (!qualityCheck) {
        throw new Error('质检单不存在');
      }

      const detail: QualityCheckDetail = {
        qualityCheck: {
          id: qualityCheck.id,
          qualityCheckNo: qualityCheck.qualityCheckNo,
          workOrderNo: qualityCheck.workOrderNo,
          workOrderType: qualityCheck.workOrderType,
          status: qualityCheck.status,
          technicianId: 'tech-001',
          technicianName: qualityCheck.technicianName,
          storeId: 'store-001',
          storeName: '深圳南山店',
          isClaimRelated: qualityCheck.isClaimRelated,
          isOutsourceRelated: qualityCheck.isOutsourceRelated,
          startTime: qualityCheck.startTime,
          finishTime: qualityCheck.finishTime,
          estimatedHours: qualityCheck.estimatedHours,
          actualHours: qualityCheck.actualHours,
          reworkReason: qualityCheck.reworkReason,
          reworkRequirement: qualityCheck.reworkRequirement,
          createTime: qualityCheck.createTime,
          updateTime: qualityCheck.updateTime
        },
        checkItems: generateMockQualityCheckItems(qualityCheck.id),
        workOrderInfo: {
          workOrderNo: qualityCheck.workOrderNo,
          workOrderType: qualityCheck.workOrderType,
          description: `${qualityCheck.workOrderType === 'maintenance' ? '定期保养' : qualityCheck.workOrderType === 'repair' ? '故障维修' : '保险维修'}服务`,
          symptoms: ['异响', '性能下降', '指示灯亮起'].slice(0, Math.floor(Math.random() * 3) + 1),
          diagnosis: '初步诊断结果',
          serviceAdvisor: '服务顾问A',
          estimatedHours: qualityCheck.estimatedHours || 2,
          actualHours: qualityCheck.actualHours
        },
        vehicleInfo: {
          plateNumber: qualityCheck.plateNumber,
          vehicleModel: qualityCheck.vehicleModel,
          vehicleConfig: qualityCheck.vehicleConfig,
          vehicleColor: qualityCheck.vehicleColor,
          vin: `TSLA${String(Math.floor(Math.random() * 1000000000000)).padStart(13, '0')}`,
          mileage: Math.floor(Math.random() * 100000) + 1000,
          vehicleAge: Math.floor(Math.random() * 60) + 1
        },
        serviceInfo: {
          serviceCustomerName: qualityCheck.serviceCustomerName,
          serviceCustomerPhone: qualityCheck.serviceCustomerPhone,
          serviceType: qualityCheck.workOrderType === 'maintenance' ? '定期保养' : '维修服务',
          serviceDate: qualityCheck.createTime,
          serviceLocation: '深圳南山店',
          serviceNotes: '客户要求高质量服务'
        }
      };

      resolve(detail);
    }, 800);
  });
};

/**
 * 提交质检结果Mock数据
 */
export const submitMockQualityCheck = (data: QualityCheckSubmitForm): Promise<ApiResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: data.isDraft ? '草稿保存成功' : '质检结果提交成功',
        data: {
          qualityCheckId: data.qualityCheckId,
          submitTime: new Date().toISOString()
        }
      });
    }, 1000);
  });
};

/**
 * 审核质检结果Mock数据
 */
export const auditMockQualityCheck = (data: QualityCheckAuditForm): Promise<ApiResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '质检审核完成',
        data: {
          qualityCheckId: data.qualityCheckId,
          auditResult: data.auditResult,
          auditTime: new Date().toISOString()
        }
      });
    }, 1000);
  });
};

/**
 * 处理返工Mock数据
 */
export const processMockRework = (data: QualityCheckReworkForm): Promise<ApiResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '返工处理成功',
        data: {
          qualityCheckId: data.qualityCheckId,
          reworkStartTime: data.reworkStartTime,
          processTime: new Date().toISOString()
        }
      });
    }, 1000);
  });
};

/**
 * 获取质检统计Mock数据
 */
export const getMockQualityCheckStats = (): Promise<QualityCheckStatistics> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const todayTotal = Math.floor(Math.random() * 20) + 10;
      const todayPassed = Math.floor(todayTotal * (0.7 + Math.random() * 0.2));
      const todayFailed = todayTotal - todayPassed;

      const weekTotal = Math.floor(Math.random() * 100) + 50;
      const weekPassed = Math.floor(weekTotal * (0.75 + Math.random() * 0.15));
      const weekFailed = weekTotal - weekPassed;

      resolve({
        todayTotal,
        todayPassed,
        todayFailed,
        weekTotal,
        weekPassed,
        weekFailed,
        passRate: Math.round((weekPassed / weekTotal) * 100) / 100,
        avgProcessTime: Math.round((Math.random() * 3 + 2) * 10) / 10,
        reworkRate: Math.round((weekFailed / weekTotal) * 100) / 100,
        monthlyTrend: Array.from({ length: 6 }, (_, i) => {
          const month = new Date();
          month.setMonth(month.getMonth() - i);
          const total = Math.floor(Math.random() * 200) + 100;
          const passed = Math.floor(total * (0.7 + Math.random() * 0.2));
          return {
            month: month.toISOString().slice(0, 7),
            total,
            passed,
            failed: total - passed,
            passRate: Math.round((passed / total) * 100) / 100
          };
        }).reverse()
      });
    }, 300);
  });
};

/**
 * 获取质检模板Mock数据
 */
export const getMockQualityCheckTemplate = (workOrderType: string): Promise<QualityCheckTemplate[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const templates: QualityCheckTemplate[] = [
        {
          categoryCode: 'BRAKE_SYSTEM',
          categoryName: '制动系统检查',
          items: [
            {
              itemCode: 'BRAKE_PEDAL_TRAVEL',
              itemName: '制动踏板行程',
              itemType: 'NUMERIC',
              standardValue: '≤150mm',
              unit: 'mm',
              isRequired: true,
              sortOrder: 1
            },
            {
              itemCode: 'BRAKE_FLUID_LEVEL',
              itemName: '制动液液位',
              itemType: 'BOOLEAN',
              standardValue: '在MIN-MAX之间',
              isRequired: true,
              sortOrder: 2
            }
          ]
        },
        {
          categoryCode: 'ELECTRICAL_SYSTEM',
          categoryName: '电气系统检查',
          items: [
            {
              itemCode: 'BATTERY_VOLTAGE',
              itemName: '电池电压',
              itemType: 'NUMERIC',
              standardValue: '12.0-12.8V',
              unit: 'V',
              isRequired: true,
              sortOrder: 1
            },
            {
              itemCode: 'LIGHTS_FUNCTION',
              itemName: '灯光功能',
              itemType: 'BOOLEAN',
              standardValue: '所有灯光正常工作',
              isRequired: true,
              sortOrder: 2
            }
          ]
        }
      ];

      resolve(templates);
    }, 300);
  });
};

/**
 * 导出质检数据Mock
 */
export const exportMockQualityCheckData = (params: ExportQualityCheckParams): Promise<Blob> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟生成Excel文件
      const csvContent = `质检单编号,工单编号,质检状态,工单类型,技师,车牌号,客户姓名,创建时间
${mockQualityCheckData.slice(0, 10).map(item =>
  `${item.qualityCheckNo},${item.workOrderNo},${item.status},${item.workOrderType},${item.technicianName},${item.plateNumber},${item.serviceCustomerName},${item.createTime}`
).join('\n')}`;

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      resolve(blob);
    }, 2000);
  });
};

/**
 * 批量操作Mock数据
 */
export const batchMockQualityCheckOperation = (data: BatchQualityCheckOperation): Promise<ApiResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: `批量${data.operation}操作成功`,
        data: {
          processedCount: data.qualityCheckIds.length,
          operation: data.operation
        }
      });
    }, 1500);
  });
};
