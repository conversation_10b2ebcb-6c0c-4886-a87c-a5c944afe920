// src/mock/data/afterSales/inspection.ts

import type {
  InspectionSearchParams,
  InspectionPageResponse,
  InspectionListItem,
  Technician,
  InspectionStatus,
  RegisterType,
  ServiceType
} from '@/types/afterSales/inspection.d.ts';

// 生成动态 Mock 数据
function generateMockInspectionData(): InspectionListItem[] {
  const data: InspectionListItem[] = [];
  const names = ['张三', '李四', '王五', '李华', '赵六', '王大锤', '钱七', '孙八'];
  const advisors = ['李四', '赵六', '钱七', '孙八'];
  const licensePlates = ['粤A12345', '京B67890', '沪C54321', '浙D98765', '苏E11111'];
  const vehicleModels = ['Model Y 2023', 'Model 3 2022', 'Model X 2024', 'Model S 2023'];
  const configs = ['长续航版', '标准续航版', '高性能版'];
  const colors = ['白色', '黑色', '蓝色', '红色', '银色'];
  const statuses: InspectionStatus[] = ['pending', 'in_progress', 'pending_confirm', 'confirmed'];
  const registerTypes: RegisterType[] = ['appointment', 'walk_in'];
  const serviceTypes: ServiceType[] = ['maintenance', 'repair'];
  const confirmChannels = ['线上确认', '线下确认'];

  for (let i = 1; i <= 28; i++) {
    const createDate = new Date();
    createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 30));
    const updateDate = new Date(createDate.getTime() + Math.random() * 86400000);

    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const confirmTime = status === 'confirmed' ?
      new Date(updateDate.getTime() + Math.random() * 86400000).toISOString().slice(0, 16).replace('T', ' ') : '';

    data.push({
      inspectionNo: `IF${String(i).padStart(8, '0')}`,
      inspectionStatus: status,
      repairmanName: names[Math.floor(Math.random() * names.length)],
      repairmanPhone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      licensePlateNo: licensePlates[Math.floor(Math.random() * licensePlates.length)],
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleConfig: configs[Math.floor(Math.random() * configs.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      mileage: Math.floor(Math.random() * 100000) + 5000,
      vehicleAge: Math.floor(Math.random() * 60) + 6,
      serviceAdvisor: advisors[Math.floor(Math.random() * advisors.length)],
      registerType: registerTypes[Math.floor(Math.random() * registerTypes.length)],
      serviceType: serviceTypes[Math.floor(Math.random() * serviceTypes.length)],
      confirmChannel: status === 'confirmed' ? confirmChannels[Math.floor(Math.random() * confirmChannels.length)] : undefined,
      customerConfirmTime: confirmTime,
      createTime: createDate.toISOString().slice(0, 16).replace('T', ' '),
      updateTime: updateDate.toISOString().slice(0, 16).replace('T', ' '),
      inspectionContent: null
    });
  }

  return data.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
}

const mockInspectionData = generateMockInspectionData();

// 技师Mock数据
const mockTechnicians: Technician[] = [
  { id: '1', name: '王五', level: '高级技师', specialties: ['发动机', '变速箱'] },
  { id: '2', name: '孙八', level: '中级技师', specialties: ['电气系统', '空调'] },
  { id: '3', name: '李华', level: '初级技师', specialties: ['轮胎', '刹车'] }
];

export const getInspectionList = (params: InspectionSearchParams): Promise<InspectionPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockInspectionData];

      // 应用筛选条件
      if (params.inspectionNo) {
        filteredData = filteredData.filter(item =>
          item.inspectionNo.toLowerCase().includes(params.inspectionNo!.toLowerCase())
        );
      }

      if (params.inspectionStatus) {
        filteredData = filteredData.filter(item =>
          item.inspectionStatus === params.inspectionStatus
        );
      }

      if (params.licensePlateNo) {
        filteredData = filteredData.filter(item =>
          item.licensePlateNo.includes(params.licensePlateNo!)
        );
      }

      if (params.repairmanName) {
        filteredData = filteredData.filter(item =>
          item.repairmanName.includes(params.repairmanName!)
        );
      }

      if (params.serviceAdvisor) {
        filteredData = filteredData.filter(item =>
          item.serviceAdvisor.includes(params.serviceAdvisor!)
        );
      }

      if (params.repairmanPhone) {
        filteredData = filteredData.filter(item =>
          item.repairmanPhone.includes(params.repairmanPhone!)
        );
      }

      if (params.createTimeRange && params.createTimeRange.length === 2) {
        const [startDate, endDate] = params.createTimeRange;
        filteredData = filteredData.filter(item => {
          const itemDate = item.createTime.split(' ')[0];
          return itemDate >= startDate && itemDate <= endDate;
        });
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};



export const submitForConfirm = (inspectionNo: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        mockInspectionData[index].inspectionStatus = 'pending_confirm';
        mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      resolve({ success: true });
    }, 500);
  });
};

export const recallInspection = (inspectionNo: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        mockInspectionData[index].inspectionStatus = 'in_progress';
        mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      resolve({ success: true });
    }, 500);
  });
};

export const customerConfirm = (inspectionNo: string, confirmTime: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        mockInspectionData[index].inspectionStatus = 'confirmed';
        mockInspectionData[index].customerConfirmTime = confirmTime;
        mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      resolve({ success: true });
    }, 500);
  });
};

export const createWorkOrder = (workOrderData: {
  inspectionNo: string;
  serviceType: string;
  priority: string;
  description: string;
  recommendedItems: Array<{ id: number; name: string; selected: boolean }>;
}): Promise<{ success: boolean; workOrderNo?: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 生成工单号
      const workOrderNo = `WO${new Date().getFullYear()}${String(Date.now()).slice(-6)}`;

      // 模拟创建工单成功
      console.log('Mock: Creating work order for inspection:', workOrderData.inspectionNo);
      console.log('Work order data:', workOrderData);

      resolve({
        success: true,
        workOrderNo: workOrderNo
      });
    }, 800);
  });
};

export const getPrintableInspection = (inspectionNo: string): Promise<{ html: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const inspection = mockInspectionData.find(item => item.inspectionNo === inspectionNo);

      if (!inspection) {
        resolve({ html: '<p>环检单不存在</p>' });
        return;
      }

      // 生成打印用的HTML内容
      const html = `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
          <h1 style="text-align: center; color: #333;">环检单报告</h1>
          <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0;">
            <h3>基本信息</h3>
            <p><strong>环检单号:</strong> ${inspection.inspectionNo}</p>
            <p><strong>车牌号:</strong> ${inspection.licensePlateNo}</p>
            <p><strong>车型:</strong> ${inspection.vehicleModel}</p>
            <p><strong>配置:</strong> ${inspection.vehicleConfig}</p>
            <p><strong>颜色:</strong> ${inspection.color}</p>
            <p><strong>里程数:</strong> ${inspection.mileage} km</p>
            <p><strong>车龄:</strong> ${inspection.vehicleAge} 个月</p>
            <p><strong>送修人:</strong> ${inspection.repairmanName}</p>
            <p><strong>联系电话:</strong> ${inspection.repairmanPhone}</p>
            <p><strong>服务顾问:</strong> ${inspection.serviceAdvisor}</p>
            <p><strong>状态:</strong> ${inspection.inspectionStatus}</p>
            <p><strong>创建时间:</strong> ${inspection.createTime}</p>
            ${inspection.customerConfirmTime ? `<p><strong>客户确认时间:</strong> ${inspection.customerConfirmTime}</p>` : ''}
            ${inspection.confirmChannel ? `<p><strong>确认渠道:</strong> ${inspection.confirmChannel}</p>` : ''}
          </div>
          <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0;">
            <h3>环检内容</h3>
            <p>详细的环检内容将在此处显示...</p>
          </div>
          <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>打印时间: ${new Date().toLocaleString()}</p>
          </div>
        </div>
      `;

      resolve({ html });
    }, 500);
  });
};
