// src/mock/data/afterSales/dashboard.ts

import type { 
  DashboardSearchParams, 
  DashboardResponse, 
  DashboardAppointmentItem,
  DashboardStats 
} from '@/types/afterSales/dashboard.d.ts';

// 生成动态 Mock 数据
function generateMockAppointments(): DashboardAppointmentItem[] {
  const plateNumbers = ['粤A12345', '京B67890', '沪C11111', '深D22222', '浙E33333', '苏F44444', '川G55555', '鲁H66666'];
  const serviceTypes: ('maintenance' | 'repair')[] = ['maintenance', 'repair'];
  const statuses: ('arrived' | 'notArrived' | 'notFulfilled')[] = ['arrived', 'notArrived', 'notFulfilled'];
  
  const appointments: DashboardAppointmentItem[] = [];
  const today = new Date().toISOString().split('T')[0];
  const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  
  // 生成今日预约数据
  for (let i = 0; i < 8; i++) {
    const hour = 9 + i;
    appointments.push({
      plateNumber: plateNumbers[i % plateNumbers.length],
      appointmentDate: today,
      timeSlot: `${hour.toString().padStart(2, '0')}:00-${(hour + 1).toString().padStart(2, '0')}:00`,
      serviceType: serviceTypes[Math.floor(Math.random() * serviceTypes.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)]
    });
  }
  
  // 生成明日预约数据
  for (let i = 0; i < 5; i++) {
    const hour = 9 + i;
    appointments.push({
      plateNumber: plateNumbers[(i + 3) % plateNumbers.length],
      appointmentDate: tomorrow,
      timeSlot: `${hour.toString().padStart(2, '0')}:00-${(hour + 1).toString().padStart(2, '0')}:00`,
      serviceType: serviceTypes[Math.floor(Math.random() * serviceTypes.length)],
      status: 'notArrived' // 明日预约默认未到店
    });
  }
  
  return appointments;
}

const mockAppointments = generateMockAppointments();

export const getDashboardData = (params: DashboardSearchParams): Promise<DashboardResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const today = new Date().toISOString().split('T')[0];
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      let filteredAppointments = [...mockAppointments];
      
      // 根据筛选类型过滤数据
      switch (params.filterType) {
        case 'notArrived':
          filteredAppointments = filteredAppointments.filter(item =>
            item.appointmentDate === today && item.status === 'notArrived'
          );
          break;
        case 'tomorrow':
          filteredAppointments = filteredAppointments.filter(item => 
            item.appointmentDate === tomorrow
          );
          break;
        default:
          filteredAppointments = filteredAppointments.filter(item => 
            item.appointmentDate === today
          );
      }
      
      // 计算统计数据
      const todayAppointments = mockAppointments.filter(item => item.appointmentDate === today);
      const totalAppointments = todayAppointments.length;
      const arrivedCount = todayAppointments.filter(item => item.status === 'arrived').length;
      const notArrivedCount = todayAppointments.filter(item => item.status === 'notArrived').length;
      const notFulfilledCount = todayAppointments.filter(item => item.status === 'notFulfilled').length;
      const arrivalRate = totalAppointments > 0 ? Math.round((arrivedCount / totalAppointments) * 100) : 0;
      const tomorrowCount = mockAppointments.filter(item => item.appointmentDate === tomorrow).length;
      
      const stats: DashboardStats = {
        totalAppointments,
        arrivedCount,
        notArrivedCount,
        notFulfilledCount,
        arrivalRate,
        tomorrowCount
      };
      
      resolve({
        appointments: filteredAppointments,
        stats
      });
    }, 300);
  });
};
