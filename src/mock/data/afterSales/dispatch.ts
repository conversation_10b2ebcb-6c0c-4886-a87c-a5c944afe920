import type {
  DispatchListItem,
  DispatchSearchParams,
  TechnicianInfo,
  AssignmentFormData,
  ReassignmentFormData,
  WorkOrderDetail,
  PaginationResponse,
  ApiResponse,
  DispatchStatistics,
  TechnicianWorkload,
  BatchOperationData,
  ExportDispatchParams,
  WorkOrderPriority,
  WorkOrderType,
  WorkOrderStatus,
  CustomerSource,
  AssignmentStatus
} from '@/types/afterSales/dispatch';

// 动态生成派工Mock数据
function generateMockDispatchData(): DispatchListItem[] {
  const dataCount = Math.floor(Math.random() * 10) + 30; // 30-40条数据
  const mockData: DispatchListItem[] = [];
  
  const priorities: WorkOrderPriority[] = ['urgent', 'normal'];
  const types: WorkOrderType[] = ['repair', 'maintenance', 'insurance'];
  const statuses: WorkOrderStatus[] = ['pendingAssignment', 'pendingStart', 'inProgress', 'paused', 'completed', 'cancelled', 'pendingQualityInspection'];
  const sources: CustomerSource[] = ['appointment', 'walkIn'];
  const vehicleModels = ['Model Y', 'Model 3', 'Model S', 'Model X', 'Cybertruck'];
  const colors = ['白色', '黑色', '红色', '蓝色', '银色', '灰色'];
  const technicians = ['技师A-王强', '技师B-李明', '技师C-张伟', '技师D-刘涛', '技师E-陈杰', '技师F-赵磊'];
  const advisors = ['顾问A-林小雨', '顾问B-张大明', '顾问C-李晓华', '顾问D-王美丽', '顾问E-陈志强'];
  const configurations = ['长续航', '标准续航', '高性能', 'Plaid'];
  
  for (let i = 0; i < dataCount; i++) {
    const creationTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
    const isAssigned = Math.random() > 0.3;
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const priority = priorities[Math.floor(Math.random() * priorities.length)];
    
    // 根据状态确定分配状态
    let assignmentStatus: AssignmentStatus = 'pending';
    if (status !== 'pendingAssignment') {
      assignmentStatus = 'assigned';
    }
    
    const estimatedWorkHours = Math.round((Math.random() * 8 + 0.5) * 2) / 2;
    const estimatedStartTime = isAssigned ? 
      new Date(creationTime.getTime() + Math.random() * 24 * 60 * 60 * 1000) : 
      new Date();
    const estimatedFinishTime = new Date(estimatedStartTime.getTime() + estimatedWorkHours * 60 * 60 * 1000);
    
    mockData.push({
      id: i + 1,
      workOrderNo: `WO${String(Date.now() + i).slice(-8)}`,
      priority,
      workOrderType: types[Math.floor(Math.random() * types.length)],
      workOrderStatus: status,
      creationTime: creationTime.toISOString().slice(0, 19).replace('T', ' '),
      customerSource: sources[Math.floor(Math.random() * sources.length)],
      repairmanName: `客户${i + 1}`,
      repairmanPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      licensePlateNumber: `粤B${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      configuration: configurations[Math.floor(Math.random() * configurations.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      vehicleAge: Math.floor(Math.random() * 60) + 1,
      mileage: Math.floor(Math.random() * 100000) + 1000,
      serviceAdvisor: advisors[Math.floor(Math.random() * advisors.length)],
      assignmentStatus,
      technician: assignmentStatus === 'assigned' ? technicians[Math.floor(Math.random() * technicians.length)] : '',
      estimatedWorkHours,
      estimatedStartTime: assignmentStatus === 'assigned' ? estimatedStartTime.toISOString().slice(0, 19).replace('T', ' ') : '',
      estimatedFinishTime: assignmentStatus === 'assigned' ? estimatedFinishTime.toISOString().slice(0, 19).replace('T', ' ') : '',
      actualStartTime: status === 'inProgress' || status === 'completed' ? 
        new Date(estimatedStartTime.getTime() + (Math.random() - 0.5) * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ') : '',
      actualFinishTime: status === 'completed' ? 
        new Date(estimatedFinishTime.getTime() + (Math.random() - 0.5) * 2 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ') : '',
      isPaused: status === 'paused',
      pauseReason: status === 'paused' ? ['等待配件', '客户要求', '设备故障', '其他紧急任务'][Math.floor(Math.random() * 4)] : undefined,
      completionTime: status === 'completed' ? new Date(estimatedFinishTime.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ') : undefined,
      qualityInspector: status === 'completed' || status === 'pendingQualityInspection' ? `质检员${Math.floor(Math.random() * 3) + 1}` : undefined,
      qualityInspectionTime: status === 'completed' ? new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ') : undefined,
      notes: Math.random() > 0.7 ? `备注信息${i + 1}` : undefined
    });
  }
  
  return mockData;
}

// 生成技师Mock数据
function generateMockTechnicianData(): TechnicianInfo[] {
  const technicians = [
    { name: '王强', department: '维修部', skills: ['发动机维修', '电气系统', '底盘维修'] },
    { name: '李明', department: '维修部', skills: ['车身维修', '喷漆', '钣金'] },
    { name: '张伟', department: '保养部', skills: ['常规保养', '轮胎更换', '机油更换'] },
    { name: '刘涛', department: '维修部', skills: ['变速箱维修', '制动系统', '悬挂系统'] },
    { name: '陈杰', department: '电子部', skills: ['电子诊断', '软件升级', '传感器维修'] },
    { name: '赵磊', department: '保养部', skills: ['空调维修', '滤芯更换', '液体更换'] }
  ];
  
  return technicians.map((tech, index) => ({
    id: `tech_${index + 1}`,
    name: tech.name,
    department: tech.department,
    status: ['available', 'busy', 'offline'][Math.floor(Math.random() * 3)] as any,
    currentWorkload: Math.floor(Math.random() * 8),
    maxWorkload: 8,
    skills: tech.skills,
    workingHours: {
      start: '08:00',
      end: '18:00'
    },
    currentWorkOrders: Array.from({ length: Math.floor(Math.random() * 3) }, (_, i) => `WO${Date.now() + i}`),
    efficiency: Math.round((Math.random() * 0.3 + 0.7) * 100) / 100,
    experience: Math.floor(Math.random() * 10) + 1
  }));
}

const mockDispatchData = generateMockDispatchData();
const mockTechnicianData = generateMockTechnicianData();

/**
 * 获取派工列表Mock数据
 */
export const getMockDispatchList = (
  params: DispatchSearchParams
): Promise<PaginationResponse<DispatchListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockDispatchData];
      
      if (params.workOrderNo) {
        filteredData = filteredData.filter(item => 
          item.workOrderNo.toLowerCase().includes(params.workOrderNo!.toLowerCase())
        );
      }
      
      if (params.priority) {
        filteredData = filteredData.filter(item => item.priority === params.priority);
      }
      
      if (params.workOrderType) {
        filteredData = filteredData.filter(item => item.workOrderType === params.workOrderType);
      }
      
      if (params.assignmentStatus) {
        filteredData = filteredData.filter(item => item.assignmentStatus === params.assignmentStatus);
      }
      
      if (params.workOrderStatus) {
        filteredData = filteredData.filter(item => item.workOrderStatus === params.workOrderStatus);
      }
      
      if (params.customerSource) {
        filteredData = filteredData.filter(item => item.customerSource === params.customerSource);
      }
      
      if (params.repairmanName) {
        filteredData = filteredData.filter(item => 
          item.repairmanName.includes(params.repairmanName!)
        );
      }
      
      if (params.licensePlateNumber) {
        filteredData = filteredData.filter(item => 
          item.licensePlateNumber.includes(params.licensePlateNumber!)
        );
      }
      
      if (params.serviceAdvisor) {
        filteredData = filteredData.filter(item => 
          item.serviceAdvisor.includes(params.serviceAdvisor!)
        );
      }
      
      if (params.technician) {
        filteredData = filteredData.filter(item => 
          item.technician.includes(params.technician!)
        );
      }
      
      // 时间范围过滤
      if (params.creationTimeStart) {
        filteredData = filteredData.filter(item => 
          item.creationTime >= params.creationTimeStart!
        );
      }
      
      if (params.creationTimeEnd) {
        filteredData = filteredData.filter(item => 
          item.creationTime <= params.creationTimeEnd!
        );
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        data: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 500);
  });
};

/**
 * 获取技师列表Mock数据
 */
export const getMockTechnicianList = (): Promise<TechnicianInfo[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockTechnicianData);
    }, 300);
  });
};

/**
 * 获取工单详情Mock数据
 */
export const getMockWorkOrderDetail = (workOrderNo: string): Promise<WorkOrderDetail> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const workOrder = mockDispatchData.find(item => item.workOrderNo === workOrderNo);
      if (!workOrder) {
        throw new Error('工单不存在');
      }

      const detail: WorkOrderDetail = {
        id: workOrder.id,
        workOrderNo: workOrder.workOrderNo,
        priority: workOrder.priority,
        workOrderType: workOrder.workOrderType,
        workOrderStatus: workOrder.workOrderStatus,
        creationTime: workOrder.creationTime,
        customerInfo: {
          name: workOrder.repairmanName,
          phone: workOrder.repairmanPhone,
          source: workOrder.customerSource
        },
        vehicleInfo: {
          licensePlateNumber: workOrder.licensePlateNumber,
          model: workOrder.vehicleModel,
          configuration: workOrder.configuration,
          color: workOrder.color,
          age: workOrder.vehicleAge,
          mileage: workOrder.mileage
        },
        serviceInfo: {
          advisor: workOrder.serviceAdvisor,
          description: `${workOrder.workOrderType === 'repair' ? '维修' : workOrder.workOrderType === 'maintenance' ? '保养' : '保险'}服务`,
          symptoms: ['异响', '漏油', '性能下降'].slice(0, Math.floor(Math.random() * 3) + 1),
          diagnosis: '初步诊断结果'
        },
        assignmentInfo: {
          status: workOrder.assignmentStatus,
          technician: workOrder.technician,
          estimatedWorkHours: workOrder.estimatedWorkHours,
          estimatedStartTime: workOrder.estimatedStartTime,
          estimatedFinishTime: workOrder.estimatedFinishTime,
          actualStartTime: workOrder.actualStartTime,
          actualFinishTime: workOrder.actualFinishTime,
          notes: workOrder.notes || ''
        },
        progressInfo: {
          isPaused: workOrder.isPaused,
          pauseReason: workOrder.pauseReason,
          completionTime: workOrder.completionTime,
          qualityInspector: workOrder.qualityInspector,
          qualityInspectionTime: workOrder.qualityInspectionTime,
          workLog: [
            {
              id: '1',
              timestamp: workOrder.creationTime,
              operator: '系统',
              action: '创建工单',
              description: '工单创建成功'
            }
          ]
        }
      };

      resolve(detail);
    }, 800);
  });
};

/**
 * 提交分配Mock数据
 */
export const submitMockAssignment = (data: AssignmentFormData): Promise<ApiResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '工单分配成功',
        data: {
          workOrderNo: data.workOrderNo,
          technicianId: data.technicianId,
          assignmentTime: new Date().toISOString()
        }
      });
    }, 1000);
  });
};

/**
 * 提交重新分配Mock数据
 */
export const submitMockReassignment = (data: ReassignmentFormData): Promise<ApiResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '工单重新分配成功',
        data: {
          workOrderNo: data.workOrderNo,
          originalTechnician: data.originalTechnician,
          newTechnicianId: data.newTechnicianId,
          reassignmentTime: new Date().toISOString()
        }
      });
    }, 1000);
  });
};

/**
 * 获取派工统计Mock数据
 */
export const getMockDispatchStatistics = (): Promise<DispatchStatistics> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const total = mockDispatchData.length;
      const pending = mockDispatchData.filter(item => item.assignmentStatus === 'pending').length;
      const inProgress = mockDispatchData.filter(item => item.workOrderStatus === 'inProgress').length;
      const completed = mockDispatchData.filter(item => item.workOrderStatus === 'completed').length;
      const paused = mockDispatchData.filter(item => item.workOrderStatus === 'paused').length;

      resolve({
        totalWorkOrders: total,
        pendingAssignment: pending,
        inProgress,
        completed,
        paused,
        averageCompletionTime: 4.5,
        technicianUtilization: 0.75
      });
    }, 300);
  });
};

/**
 * 获取技师工作负载Mock数据
 */
export const getMockTechnicianWorkload = (): Promise<TechnicianWorkload[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const workload = mockTechnicianData.map(tech => ({
        technicianId: tech.id,
        technicianName: tech.name,
        currentWorkOrders: tech.currentWorkload,
        maxCapacity: tech.maxWorkload,
        utilizationRate: tech.currentWorkload / tech.maxWorkload,
        estimatedAvailableTime: new Date(Date.now() + Math.random() * 8 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
      }));

      resolve(workload);
    }, 300);
  });
};

/**
 * 批量操作Mock数据
 */
export const submitMockBatchOperation = (data: BatchOperationData): Promise<ApiResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: `批量${data.operation}操作成功`,
        data: {
          processedCount: data.workOrderNos.length,
          operation: data.operation
        }
      });
    }, 1500);
  });
};

/**
 * 导出派工数据Mock
 */
export const exportMockDispatchData = (params: ExportDispatchParams): Promise<Blob> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟生成Excel文件
      const csvContent = `工单编号,优先级,工单类型,工单状态,创建时间,送修人,车牌号,技师,预计工时
${mockDispatchData.slice(0, 10).map(item =>
  `${item.workOrderNo},${item.priority},${item.workOrderType},${item.workOrderStatus},${item.creationTime},${item.repairmanName},${item.licensePlateNumber},${item.technician},${item.estimatedWorkHours}`
).join('\n')}`;

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      resolve(blob);
    }, 2000);
  });
};
