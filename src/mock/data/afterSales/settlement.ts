import type {
  SettlementListItem,
  SettlementSearchParams,
  SettlementDetail,
  PaymentForm,
  PaymentRecord,
  SettlementStatistics,
  PaginationResponse,
  ApiResponse,
  ExportSettlementParams,
  BatchSettlementOperation,
  SettlementStatus,
  PaymentStatus,
  WorkOrderType,
  PaymentMethod
} from '@/types/afterSales/settlement';

// 动态生成结算Mock数据
function generateMockSettlementData(): SettlementListItem[] {
  const dataCount = Math.floor(Math.random() * 30) + 40; // 40-70条数据
  const mockData: SettlementListItem[] = [];
  
  const statuses: SettlementStatus[] = ['pre_settlement', 'pending_settlement', 'completed', 'cancelled'];
  const paymentStatuses: PaymentStatus[] = ['pending', 'deposit_paid', 'fully_paid', 'refunding', 'refunded'];
  const workOrderTypes: WorkOrderType[] = ['maintenance', 'repair', 'warranty'];
  const vehicleModels = ['Model S', 'Model X', 'Model 3', 'Model Y', 'Cybertruck'];
  const colors = ['珍珠白', '深空灰', '中国红', '冷光银', '深海蓝'];
  const technicians = ['李技师', '王技师', '张技师', '赵技师', '陈技师'];
  const advisors = ['陈经理', '王顾问', '李经理', '赵顾问', '刘经理'];
  const customers = ['张三', '李四', '王五', '赵六', '陈七', '刘八'];
  const configs = ['标准版', '长续航版', '高性能版', 'Plaid版'];
  
  for (let i = 0; i < dataCount; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
    const workOrderType = workOrderTypes[Math.floor(Math.random() * workOrderTypes.length)];
    
    const totalAmount = Math.round((Math.random() * 2000 + 500) * 100) / 100;
    const paidAmount = paymentStatus === 'fully_paid' ? totalAmount : 
                     paymentStatus === 'deposit_paid' ? Math.round(totalAmount * 0.5 * 100) / 100 : 0;
    const payableAmount = totalAmount - paidAmount;
    
    mockData.push({
      id: `settlement-${i + 1}`,
      settlementNo: `ST${String(Date.now() + i).slice(-8)}`,
      workOrderNo: `WO${String(i + 1).padStart(6, '0')}`,
      workOrderType,
      settlementStatus: status,
      paymentStatus,
      customerName: customers[Math.floor(Math.random() * customers.length)],
      customerPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
      vehiclePlate: `粤B${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleConfig: configs[Math.floor(Math.random() * configs.length)],
      vehicleColor: colors[Math.floor(Math.random() * colors.length)],
      technician: technicians[Math.floor(Math.random() * technicians.length)],
      serviceAdvisor: advisors[Math.floor(Math.random() * advisors.length)],
      totalAmount,
      paidAmount,
      payableAmount,
      createdAt: createTime.toISOString(),
      inspectionStatus: Math.random() > 0.2 ? '质检通过' : '质检中'
    });
  }
  
  return mockData;
}

// 生成工时明细Mock数据
function generateMockLaborItems(settlementId: string): any[] {
  return [
    {
      id: 'labor-1',
      laborCode: 'L001',
      laborName: '更换制动片',
      standardHours: 2.0,
      actualHours: 2.5,
      unitPrice: 120.00,
      totalAmount: 300.00,
      receivableAmount: 300.00,
      warrantyAmount: 0.00,
      discountAmount: 0.00,
      remarks: '前轮制动片更换'
    },
    {
      id: 'labor-2',
      laborCode: 'L002',
      laborName: '更换机油机滤',
      standardHours: 1.0,
      actualHours: 1.0,
      unitPrice: 80.00,
      totalAmount: 80.00,
      receivableAmount: 80.00,
      warrantyAmount: 0.00,
      discountAmount: 0.00,
      remarks: '定期保养项目'
    }
  ];
}

// 生成零件明细Mock数据
function generateMockPartItems(settlementId: string): any[] {
  return [
    {
      id: 'part-1',
      partCode: 'P001',
      partName: '制动片',
      specification: '前轮制动片套装',
      unit: '套',
      quantity: 1,
      unitPrice: 280.00,
      totalAmount: 280.00,
      receivableAmount: 280.00,
      warrantyAmount: 0.00,
      discountAmount: 0.00,
      remarks: '原厂配件'
    },
    {
      id: 'part-2',
      partCode: 'P002',
      partName: '机油滤清器',
      specification: '标准机滤',
      unit: '个',
      quantity: 1,
      unitPrice: 45.00,
      totalAmount: 45.00,
      receivableAmount: 45.00,
      warrantyAmount: 0.00,
      discountAmount: 0.00,
      remarks: '原厂配件'
    },
    {
      id: 'part-3',
      partCode: 'P003',
      partName: '机油',
      specification: '5W-30全合成机油',
      unit: '升',
      quantity: 4,
      unitPrice: 60.00,
      totalAmount: 240.00,
      receivableAmount: 240.00,
      warrantyAmount: 0.00,
      discountAmount: 0.00,
      remarks: '原厂机油'
    }
  ];
}

const mockSettlementData = generateMockSettlementData();

/**
 * 获取结算单列表Mock数据
 */
export const getMockSettlementList = (
  params: SettlementSearchParams
): Promise<PaginationResponse<SettlementListItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockSettlementData];
      
      if (params.settlementNo) {
        filteredData = filteredData.filter(item => 
          item.settlementNo.toLowerCase().includes(params.settlementNo!.toLowerCase())
        );
      }
      
      if (params.workOrderNo) {
        filteredData = filteredData.filter(item => 
          item.workOrderNo.toLowerCase().includes(params.workOrderNo!.toLowerCase())
        );
      }
      
      if (params.settlementStatus) {
        filteredData = filteredData.filter(item => item.settlementStatus === params.settlementStatus);
      }
      
      if (params.paymentStatus) {
        filteredData = filteredData.filter(item => item.paymentStatus === params.paymentStatus);
      }
      
      if (params.customerName) {
        filteredData = filteredData.filter(item => 
          item.customerName.includes(params.customerName!)
        );
      }
      
      if (params.vehiclePlate) {
        filteredData = filteredData.filter(item => 
          item.vehiclePlate.includes(params.vehiclePlate!)
        );
      }
      
      if (params.technician) {
        filteredData = filteredData.filter(item => 
          item.technician.includes(params.technician!)
        );
      }
      
      if (params.serviceAdvisor) {
        filteredData = filteredData.filter(item => 
          item.serviceAdvisor.includes(params.serviceAdvisor!)
        );
      }
      
      // 时间范围过滤
      if (params.createTimeStart) {
        filteredData = filteredData.filter(item => 
          item.createdAt >= params.createTimeStart!
        );
      }
      
      if (params.createTimeEnd) {
        filteredData = filteredData.filter(item => 
          item.createdAt <= params.createTimeEnd!
        );
      }
      
      // 金额范围过滤
      if (params.amountMin !== undefined) {
        filteredData = filteredData.filter(item => item.totalAmount >= params.amountMin!);
      }
      
      if (params.amountMax !== undefined) {
        filteredData = filteredData.filter(item => item.totalAmount <= params.amountMax!);
      }
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        data: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 500);
  });
};

/**
 * 获取结算单详情Mock数据
 */
export const getMockSettlementDetail = (id: string): Promise<SettlementDetail> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const settlement = mockSettlementData.find(item => item.id === id);
      if (!settlement) {
        throw new Error('结算单不存在');
      }
      
      const laborItems = generateMockLaborItems(id);
      const partItems = generateMockPartItems(id);
      
      const detail: SettlementDetail = {
        id: settlement.id,
        settlementNo: settlement.settlementNo,
        workOrderNo: settlement.workOrderNo,
        workOrderType: settlement.workOrderType,
        settlementStatus: settlement.settlementStatus,
        paymentStatus: settlement.paymentStatus,
        createdAt: settlement.createdAt,
        serviceAdvisor: settlement.serviceAdvisor,
        
        // 客户信息
        customerName: settlement.customerName,
        customerPhone: settlement.customerPhone,
        
        // 车辆信息
        vehiclePlate: settlement.vehiclePlate,
        vin: `TSLA${String(Math.floor(Math.random() * 1000000000000)).padStart(13, '0')}`,
        vehicleModel: settlement.vehicleModel,
        vehicleConfig: settlement.vehicleConfig,
        vehicleColor: settlement.vehicleColor,
        vehicleAge: `${Math.floor(Math.random() * 60) + 1}个月`,
        mileage: `${Math.floor(Math.random() * 100000) + 1000}km`,
        
        // 服务包信息
        servicePackage: Math.random() > 0.5 ? {
          packageCode: 'SP001',
          packageName: 'Model Y 标准保养服务包'
        } : undefined,
        
        // 费用明细
        laborItems,
        partItems,
        
        // 费用汇总
        laborTotalAmount: laborItems.reduce((sum, item) => sum + item.totalAmount, 0),
        laborReceivableAmount: laborItems.reduce((sum, item) => sum + item.receivableAmount, 0),
        partTotalAmount: partItems.reduce((sum, item) => sum + item.totalAmount, 0),
        partReceivableAmount: partItems.reduce((sum, item) => sum + item.receivableAmount, 0),
        warrantyAmount: 0,
        totalAmount: settlement.totalAmount,
        packageRightsDeduction: 0,
        receivableTotal: settlement.totalAmount,
        payableAmount: settlement.payableAmount,
        paidAmount: settlement.paidAmount,
        discountAmount: 0,
        remarks: '结算备注信息',
        
        // 操作日志
        operationLogs: [
          {
            id: '1',
            operationType: '创建结算单',
            operationTime: settlement.createdAt,
            operator: '系统',
            operationContent: '结算单创建成功',
            remarks: ''
          },
          {
            id: '2',
            operationType: '推送结算',
            operationTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            operator: settlement.serviceAdvisor,
            operationContent: '结算单已推送给客户',
            remarks: ''
          }
        ]
      };
      
      resolve(detail);
    }, 800);
  });
};

/**
 * 获取收退款记录Mock数据
 */
export const getMockPaymentRecords = (settlementId: string): Promise<PaymentRecord[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const records: PaymentRecord[] = [
        {
          id: 'payment-1',
          paymentNo: `PY${String(Date.now()).slice(-8)}`,
          businessType: '收款',
          transactionNo: `TXN${String(Math.floor(Math.random() * 1000000000))}`,
          paymentMethod: 'pos',
          amount: 500.00,
          paymentType: '定金',
          paymentTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          remarks: 'POS机刷卡支付',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'payment-2',
          paymentNo: `PY${String(Date.now() + 1).slice(-8)}`,
          businessType: '收款',
          transactionNo: `TXN${String(Math.floor(Math.random() * 1000000000))}`,
          paymentMethod: 'wechat',
          amount: 465.00,
          paymentType: '尾款',
          paymentTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          remarks: '微信支付',
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString()
        }
      ];
      resolve(records);
    }, 300);
  });
};

/**
 * 处理收退款Mock数据
 */
export const processMockPayment = (data: PaymentForm): Promise<ApiResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: `${data.businessType}处理成功`,
        data: {
          paymentNo: `PY${String(Date.now()).slice(-8)}`,
          transactionNo: `TXN${String(Math.floor(Math.random() * 1000000000))}`,
          paymentTime: new Date().toISOString()
        }
      });
    }, 1000);
  });
};

/**
 * 获取结算统计Mock数据
 */
export const getMockSettlementStats = (): Promise<SettlementStatistics> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const todayTotal = Math.floor(Math.random() * 20) + 10;
      const todayCompleted = Math.floor(todayTotal * (0.7 + Math.random() * 0.2));
      const todayAmount = Math.round((Math.random() * 50000 + 20000) * 100) / 100;

      const weekTotal = Math.floor(Math.random() * 100) + 50;
      const weekCompleted = Math.floor(weekTotal * (0.75 + Math.random() * 0.15));
      const weekAmount = Math.round((Math.random() * 300000 + 100000) * 100) / 100;

      const monthTotal = Math.floor(Math.random() * 400) + 200;
      const monthCompleted = Math.floor(monthTotal * (0.8 + Math.random() * 0.1));
      const monthAmount = Math.round((Math.random() * 1000000 + 500000) * 100) / 100;

      resolve({
        todayTotal,
        todayCompleted,
        todayAmount,
        weekTotal,
        weekCompleted,
        weekAmount,
        monthTotal,
        monthCompleted,
        monthAmount,
        completionRate: Math.round((monthCompleted / monthTotal) * 100) / 100,
        avgSettlementAmount: Math.round((monthAmount / monthCompleted) * 100) / 100,
        monthlyTrend: Array.from({ length: 6 }, (_, i) => {
          const month = new Date();
          month.setMonth(month.getMonth() - i);
          const total = Math.floor(Math.random() * 400) + 200;
          const completed = Math.floor(total * (0.7 + Math.random() * 0.2));
          const amount = Math.round((Math.random() * 1000000 + 500000) * 100) / 100;
          return {
            month: month.toISOString().slice(0, 7),
            total,
            completed,
            amount,
            completionRate: Math.round((completed / total) * 100) / 100
          };
        }).reverse()
      });
    }, 300);
  });
};

/**
 * 导出结算数据Mock
 */
export const exportMockSettlementData = (params: ExportSettlementParams): Promise<Blob> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟生成Excel文件
      const csvContent = `结算单号,工单编号,结算状态,支付状态,客户姓名,车牌号,结算金额,创建时间
${mockSettlementData.slice(0, 10).map(item =>
  `${item.settlementNo},${item.workOrderNo},${item.settlementStatus},${item.paymentStatus},${item.customerName},${item.vehiclePlate},${item.totalAmount},${item.createdAt}`
).join('\n')}`;

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      resolve(blob);
    }, 2000);
  });
};

/**
 * 批量操作Mock数据
 */
export const batchMockSettlementOperation = (data: BatchSettlementOperation): Promise<ApiResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: `批量${data.operation}操作成功`,
        data: {
          processedCount: data.settlementIds.length,
          operation: data.operation
        }
      });
    }, 1500);
  });
};
