// src/mock/data/afterSales/workAssignmentDashboard.ts

import type { 
  DashboardStatistics,
  TechnicianSchedule,
  AssignedOrderInfo,
  WorkloadData,
  StatusDistribution,
  TechnicianPerformance,
  OrderFlowData,
  DepartmentWorkload,
  DashboardFilters
} from '@/types/afterSales/workAssignmentDashboard.d.ts';

// 生成技师排班Mock数据
function generateMockTechnicianSchedules(): TechnicianSchedule[] {
  const technicianNames = ['陈师傅', '刘师傅', '王师傅', '张师傅', '李师傅', '赵师傅'];
  const departments = ['维修部', '保养部', '检测部'];
  const specialties = [
    ['发动机维修', '变速箱维修'],
    ['电气系统', '空调系统'],
    ['底盘维修', '刹车系统'],
    ['车身维修', '喷漆'],
    ['轮胎更换', '四轮定位'],
    ['电池检测', '充电系统']
  ];
  const statuses = ['available', 'busy', 'break', 'offline'] as const;

  return technicianNames.map((name, index) => {
    const assignedOrdersCount = Math.floor(Math.random() * 4) + 1;
    const assignedOrders: AssignedOrderInfo[] = [];
    
    for (let i = 0; i < assignedOrdersCount; i++) {
      const startTime = new Date();
      startTime.setHours(8 + i * 2, Math.floor(Math.random() * 60));
      const duration = Math.floor(Math.random() * 120) + 60; // 60-180分钟
      const endTime = new Date(startTime.getTime() + duration * 60000);
      
      assignedOrders.push({
        workOrderId: `WO${String(index * 10 + i + 1).padStart(8, '0')}`,
        workOrderNo: `WO${new Date().getFullYear()}${String(index * 10 + i + 1).padStart(6, '0')}`,
        customerName: `客户${index * 10 + i + 1}`,
        licensePlate: `京A${String(index * 10 + i + 1).padStart(5, '0')}`,
        vehicleModel: ['Model Y 2023', 'Model 3 2022', 'Model X 2024'][Math.floor(Math.random() * 3)],
        workOrderType: ['maintenance', 'repair', 'inspection', 'insurance'][Math.floor(Math.random() * 4)] as any,
        priority: ['low', 'normal', 'high', 'urgent'][Math.floor(Math.random() * 4)] as any,
        estimatedDuration: duration,
        scheduledStartTime: startTime.toISOString().slice(0, 16).replace('T', ' '),
        scheduledEndTime: endTime.toISOString().slice(0, 16).replace('T', ' '),
        actualStartTime: Math.random() > 0.5 ? startTime.toISOString().slice(0, 16).replace('T', ' ') : undefined,
        status: ['scheduled', 'in_progress', 'completed', 'delayed'][Math.floor(Math.random() * 4)] as any,
        progress: Math.floor(Math.random() * 100)
      });
    }

    const totalCapacity = 8 * 60; // 8小时工作制
    const workloadMinutes = assignedOrders.reduce((sum, order) => sum + order.estimatedDuration, 0);
    const workloadPercentage = Math.min((workloadMinutes / totalCapacity) * 100, 100);

    return {
      technicianInfo: {
        technicianId: `T${String(index + 1).padStart(3, '0')}`,
        technicianName: name,
        technicianCode: `TECH${String(index + 1).padStart(3, '0')}`,
        department: departments[Math.floor(Math.random() * departments.length)],
        skillLevel: Math.floor(Math.random() * 5) + 1,
        specialties: specialties[index],
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`
      },
      workingHours: {
        start: '08:00',
        end: '18:00',
        breakStart: '12:00',
        breakEnd: '13:00'
      },
      currentStatus: statuses[Math.floor(Math.random() * statuses.length)],
      assignedOrders,
      workloadPercentage,
      availableCapacity: Math.max(totalCapacity - workloadMinutes, 0),
      totalCapacity,
      efficiency: Math.floor(Math.random() * 30) + 70 // 70-100%
    };
  });
}

// 生成看板统计数据
function generateMockDashboardStatistics(): DashboardStatistics {
  const totalOrders = Math.floor(Math.random() * 100) + 50;
  const pendingAssignment = Math.floor(totalOrders * 0.15);
  const assignedOrders = Math.floor(totalOrders * 0.25);
  const inProgressOrders = Math.floor(totalOrders * 0.35);
  const completedOrders = totalOrders - pendingAssignment - assignedOrders - inProgressOrders;

  return {
    totalOrders,
    pendingAssignment,
    assignedOrders,
    inProgressOrders,
    completedOrders,
    averageAssignmentTime: Math.floor(Math.random() * 30) + 15, // 15-45分钟
    technicianUtilization: Math.floor(Math.random() * 40) + 60, // 60-100%
    todayCompletedOrders: Math.floor(Math.random() * 20) + 10,
    todayNewOrders: Math.floor(Math.random() * 25) + 15
  };
}

// 生成工作负荷数据
function generateMockWorkloadData(): WorkloadData[] {
  const technicianNames = ['陈师傅', '刘师傅', '王师傅', '张师傅', '李师傅', '赵师傅'];
  
  return technicianNames.map((name, index) => {
    const maxCapacity = 8 * 60; // 8小时
    const currentWorkload = Math.floor(Math.random() * maxCapacity * 0.8) + maxCapacity * 0.2;
    const utilizationRate = (currentWorkload / maxCapacity) * 100;
    
    return {
      technicianId: `T${String(index + 1).padStart(3, '0')}`,
      technicianName: name,
      currentWorkload,
      maxCapacity,
      utilizationRate,
      assignedOrdersCount: Math.floor(Math.random() * 5) + 1,
      averageOrderDuration: Math.floor(Math.random() * 60) + 90, // 90-150分钟
      efficiency: Math.floor(Math.random() * 30) + 70 // 70-100%
    };
  });
}

// 生成状态分布数据
function generateMockStatusDistribution(): StatusDistribution[] {
  const statuses = [
    { status: 'pending_assign', color: '#F56C6C' },
    { status: 'assigned', color: '#E6A23C' },
    { status: 'in_progress', color: '#409EFF' },
    { status: 'completed', color: '#67C23A' }
  ];

  const total = 100;
  let remaining = total;
  
  return statuses.map((item, index) => {
    const count = index === statuses.length - 1 
      ? remaining 
      : Math.floor(Math.random() * (remaining / (statuses.length - index))) + 1;
    remaining -= count;
    
    return {
      status: item.status,
      count,
      percentage: (count / total) * 100,
      color: item.color
    };
  });
}

const mockTechnicianSchedules = generateMockTechnicianSchedules();
const mockDashboardStatistics = generateMockDashboardStatistics();
const mockWorkloadData = generateMockWorkloadData();
const mockStatusDistribution = generateMockStatusDistribution();

export const getDashboardStatistics = (): Promise<DashboardStatistics> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockDashboardStatistics);
    }, 300);
  });
};

export const getTechnicianSchedules = (filters: DashboardFilters): Promise<TechnicianSchedule[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockTechnicianSchedules];
      
      if (filters.department) {
        filteredData = filteredData.filter(item => 
          item.technicianInfo.department === filters.department
        );
      }
      
      if (filters.technicianId) {
        filteredData = filteredData.filter(item => 
          item.technicianInfo.technicianId === filters.technicianId
        );
      }
      
      resolve(filteredData);
    }, 400);
  });
};

export const getWorkloadData = (): Promise<WorkloadData[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockWorkloadData);
    }, 300);
  });
};

export const getStatusDistribution = (): Promise<StatusDistribution[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockStatusDistribution);
    }, 300);
  });
};

export const getTechnicianPerformance = (): Promise<TechnicianPerformance[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const performanceData = mockTechnicianSchedules.map(schedule => ({
        technicianId: schedule.technicianInfo.technicianId,
        technicianName: schedule.technicianInfo.technicianName,
        completedOrdersToday: Math.floor(Math.random() * 8) + 2,
        completedOrdersWeek: Math.floor(Math.random() * 40) + 20,
        completedOrdersMonth: Math.floor(Math.random() * 160) + 80,
        averageCompletionTime: Math.floor(Math.random() * 60) + 90, // 90-150分钟
        qualityScore: Math.floor(Math.random() * 20) + 80, // 80-100分
        customerSatisfaction: Math.floor(Math.random() * 15) + 85, // 85-100%
        onTimeCompletionRate: Math.floor(Math.random() * 20) + 80 // 80-100%
      }));

      resolve(performanceData);
    }, 400);
  });
};

export const getOrderFlowData = (): Promise<OrderFlowData[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const flowData: OrderFlowData[] = [];

      for (let hour = 8; hour <= 18; hour++) {
        flowData.push({
          hour: `${hour}:00`,
          newOrders: Math.floor(Math.random() * 8) + 2,
          assignedOrders: Math.floor(Math.random() * 6) + 1,
          completedOrders: Math.floor(Math.random() * 5) + 1,
          cancelledOrders: Math.floor(Math.random() * 2)
        });
      }

      resolve(flowData);
    }, 300);
  });
};

export const getDepartmentWorkload = (): Promise<DepartmentWorkload[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const departments = ['维修部', '保养部', '检测部'];
      const workloadData = departments.map((dept, index) => ({
        departmentId: `DEPT${String(index + 1).padStart(3, '0')}`,
        departmentName: dept,
        totalTechnicians: Math.floor(Math.random() * 8) + 5,
        activeTechnicians: Math.floor(Math.random() * 6) + 3,
        totalOrders: Math.floor(Math.random() * 30) + 20,
        completedOrders: Math.floor(Math.random() * 20) + 10,
        averageUtilization: Math.floor(Math.random() * 30) + 70, // 70-100%
        efficiency: Math.floor(Math.random() * 25) + 75 // 75-100%
      }));

      resolve(workloadData);
    }, 300);
  });
};
