import type { 
  OrderPaymentItem, 
  OrderPaymentListParams, 
  PaginationResponse,
  OrderDetailInfo,
  AddPaymentRecordForm,
  PaymentRecord
} from '@/types/module'

// 模拟收退款记录数据
const mockPaymentRecords: PaymentRecord[] = [
  {
    paymentRecordId: 'PR001',
    paymentRecordNumber: 'PAY202406010001',
    orderId: 'ORD001',
    businessType: '收款',
    transactionNumber: 'TXN2024060100001',
    channel: 'APP',
    amount: 1000.00,
    paymentType: 'Book Fee',
    arrivalTime: '2024-06-01',
    remark: 'APP自动推送',
    dataSource: 'APP推送',
    isDeletable: false,
    createTime: '2024-06-01 10:30:00',
    creator: 'system'
  },
  {
    paymentRecordId: 'PR002',
    paymentRecordNumber: 'PAY202406010002',
    orderId: 'ORD001',
    businessType: '收款',
    transactionNumber: 'TXN2024060100002',
    channel: '银行卡',
    amount: 50000.00,
    paymentType: '贷款',
    arrivalTime: '2024-06-02',
    remark: '银行卡转账',
    dataSource: '手动录入',
    isDeletable: true,
    createTime: '2024-06-02 14:20:00',
    creator: 'finance001'
  },
  {
    paymentRecordId: 'PR003',
    paymentRecordNumber: 'REF202406010001',
    orderId: 'ORD002',
    businessType: '退款',
    transactionNumber: 'TXN2024060100003',
    channel: 'APP',
    amount: 1000.00,
    paymentType: 'Book Fee',
    arrivalTime: '2024-06-03',
    remark: '客户取消订单退款',
    dataSource: 'APP推送',
    isDeletable: false,
    createTime: '2024-06-03 09:15:00',
    creator: 'system'
  }
]

// 模拟订单数据
const mockOrderPaymentList: OrderPaymentItem[] = [
  {
    orderId: 'ORD001',
    orderNumber: 'ORD202406010001',
    buyerName: '张三',
    buyerPhone: '0123456789',
    dealerStoreName: '吉隆坡总店',
    salesConsultantName: '李销售',
    vin: 'MHKA1CA0XL0123456',
    model: 'SAGA',
    variant: 'Standard',
    color: '珍珠白',
    orderCreateTime: '2024-06-01 10:00:00',
    orderStatus: '已确认',
    paymentStatus: '已支付定金',
    vehicleSalesPrice: 65000.00,
    insuranceAmount: 3000.00,
    otrAmount: 70000.00,
    discountAmount: 2000.00,
    totalAmount: 68000.00,
    paidAmount: 51000.00,
    unpaidAmount: 17000.00,
    loanAmount: 50000.00,
    canInvoice: false,
    invoiceTime: undefined,
    invoiceNumber: undefined,
    createTime: '2024-06-01 10:00:00',
    updateTime: '2024-06-02 14:30:00'
  },
  {
    orderId: 'ORD002',
    orderNumber: 'ORD202406010002',
    buyerName: '李四',
    buyerPhone: '0187654321',
    dealerStoreName: '新山分店',
    salesConsultantName: '王顾问',
    vin: 'MHKA1CA0XL0123457',
    model: 'PERSONA',
    variant: 'Premium',
    color: '宝石蓝',
    orderCreateTime: '2024-06-01 14:00:00',
    orderStatus: '已取消',
    paymentStatus: '退款完成',
    vehicleSalesPrice: 75000.00,
    insuranceAmount: 3500.00,
    otrAmount: 80000.00,
    discountAmount: 1500.00,
    totalAmount: 78500.00,
    paidAmount: 0.00,
    unpaidAmount: 78500.00,
    canInvoice: false,
    invoiceTime: undefined,
    invoiceNumber: undefined,
    createTime: '2024-06-01 14:00:00',
    updateTime: '2024-06-03 09:20:00'
  },
  {
    orderId: 'ORD003',
    orderNumber: 'ORD202406010003',
    buyerName: '王五',
    buyerPhone: '0112345678',
    dealerStoreName: '吉隆坡总店',
    salesConsultantName: '陈顾问',
    vin: 'MHKA1CA0XL0123458',
    model: 'MYVI',
    variant: 'Advance',
    color: '炫酷红',
    orderCreateTime: '2024-06-02 09:00:00',
    orderStatus: '已交车',
    paymentStatus: '已支付尾款',
    vehicleSalesPrice: 55000.00,
    insuranceAmount: 2500.00,
    otrAmount: 60000.00,
    discountAmount: 3000.00,
    totalAmount: 57000.00,
    paidAmount: 57000.00,
    unpaidAmount: 0.00,
    canInvoice: true,
    invoiceTime: '2024-06-10 15:30:00',
    invoiceNumber: 'INV202406100001',
    createTime: '2024-06-02 09:00:00',
    updateTime: '2024-06-10 16:00:00'
  }
]

// 模拟详情数据
const mockOrderDetail: OrderDetailInfo = {
  // 订单基础信息
  orderId: 'ORD001',
  orderNumber: 'ORD202406010001',
  orderCreateTime: '2024-06-01 10:00:00',
  orderStatus: '已确认',
  paymentStatus: '已支付定金',
  paymentMethod: '贷款',
  loanAmount: 50000.00,
  canInvoice: false,
  invoiceTime: undefined,
  invoiceNumber: undefined,

  // 客户信息
  ordererName: '张三',
  ordererPhone: '0123456789',
  buyerName: '张三',
  buyerPhone: '0123456789',
  buyerIdType: 'IC',
  buyerIdNumber: '123456-78-9012',
  buyerEmail: '<EMAIL>',
  buyerAddress: 'No. 123, Jalan ABC, Taman DEF',
  buyerState: 'Selangor',
  buyerCity: 'Petaling Jaya',
  buyerPostcode: '47300',

  // 门店信息
  dealerRegion: '中央地区',
  dealerCity: '吉隆坡',
  dealerStoreName: '吉隆坡总店',
  salesConsultantName: '李销售',
  salesConsultantPhone: '0198765432',
  salesConsultantEmail: '<EMAIL>',

  // 车辆信息
  vin: 'MHKA1CA0XL0123456',
  model: 'SAGA',
  variant: 'Standard',
  color: '珍珠白',
  options: ['倒车摄像头', '导航系统', '真皮座椅'],
  warehouseName: '吉隆坡仓库',
  productionDate: '2024-05-15',

  // 价格信息
  vehicleSalesPrice: 65000.00,
  insuranceAmount: 3000.00,
  otrAmount: 70000.00,
  discountAmount: 2000.00,
  totalAmount: 68000.00,
  paidAmount: 51000.00,
  unpaidAmount: 17000.00,

  // 收退款历史
  paymentRecords: mockPaymentRecords.filter(record => record.orderId === 'ORD001')
}

// 已使用的流水号（用于唯一性检查）
const usedTransactionNumbers = new Set([
  'TXN2024060100001',
  'TXN2024060100002',
  'TXN2024060100003'
])

// 获取订单收款列表
export const getOrderPaymentList = (params: OrderPaymentListParams): Promise<PaginationResponse<OrderPaymentItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockOrderPaymentList]

      // 应用筛选条件
      if (params.orderNumber) {
        filteredData = filteredData.filter(item => 
          item.orderNumber.includes(params.orderNumber!)
        )
      }
      if (params.buyerName) {
        filteredData = filteredData.filter(item => 
          item.buyerName.includes(params.buyerName!)
        )
      }
      if (params.buyerPhone) {
        filteredData = filteredData.filter(item => 
          item.buyerPhone.includes(params.buyerPhone!)
        )
      }
      if (params.orderStatus) {
        filteredData = filteredData.filter(item => 
          item.orderStatus === params.orderStatus
        )
      }
      if (params.paymentStatus) {
        filteredData = filteredData.filter(item => 
          item.paymentStatus === params.paymentStatus
        )
      }
      if (params.canInvoice !== undefined) {
        filteredData = filteredData.filter(item => 
          item.canInvoice === params.canInvoice
        )
      }
      if (params.startDate && params.endDate) {
        filteredData = filteredData.filter(item => {
          const createTime = new Date(item.orderCreateTime)
          const start = new Date(params.startDate!)
          const end = new Date(params.endDate!)
          return createTime >= start && createTime <= end
        })
      }

      // 分页处理
      const total = filteredData.length
      const start = (params.page - 1) * params.pageSize
      const end = start + params.pageSize
      const data = filteredData.slice(start, end)

      resolve({
        list: data,
        total,
        page: params.page,
        pageSize: params.pageSize
      })
    }, 500)
  })
}

// 获取订单详情
export const getOrderDetail = (orderId: string): Promise<OrderDetailInfo> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (orderId === 'ORD001') {
        resolve(mockOrderDetail)
      } else {
        // 为其他订单生成基本详情数据
        const orderItem = mockOrderPaymentList.find(item => item.orderId === orderId)
        if (orderItem) {
          const detail: OrderDetailInfo = {
            ...mockOrderDetail,
            orderId: orderItem.orderId,
            orderNumber: orderItem.orderNumber,
            orderCreateTime: orderItem.orderCreateTime,
            orderStatus: orderItem.orderStatus,
            paymentStatus: orderItem.paymentStatus,
            buyerName: orderItem.buyerName,
            buyerPhone: orderItem.buyerPhone,
            vin: orderItem.vin,
            model: orderItem.model,
            variant: orderItem.variant,
            color: orderItem.color,
            vehicleSalesPrice: orderItem.vehicleSalesPrice,
            insuranceAmount: orderItem.insuranceAmount,
            otrAmount: orderItem.otrAmount,
            discountAmount: orderItem.discountAmount,
            totalAmount: orderItem.totalAmount,
            paidAmount: orderItem.paidAmount,
            unpaidAmount: orderItem.unpaidAmount,
            paymentRecords: mockPaymentRecords.filter(record => record.orderId === orderId)
          }
          resolve(detail)
        } else {
          reject(new Error('订单不存在'))
        }
      }
    }, 300)
  })
}

// 添加收退款记录
export const addPaymentRecord = (orderId: string, data: AddPaymentRecordForm): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 检查流水号是否已存在
      if (usedTransactionNumbers.has(data.transactionNumber)) {
        reject(new Error('流水号已存在'))
        return
      }

      // 创建新的收退款记录
      const newRecord: PaymentRecord = {
        paymentRecordId: `PR${Date.now()}`,
        paymentRecordNumber: `PAY${Date.now()}`,
        orderId: orderId,
        businessType: data.businessType,
        transactionNumber: data.transactionNumber,
        channel: data.channel,
        amount: data.amount,
        paymentType: data.paymentType,
        arrivalTime: data.arrivalTime,
        remark: data.remark || '',
        dataSource: '手动录入',
        isDeletable: true,
        createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
        creator: 'finance001'
      }

      // 添加到数据中
      mockPaymentRecords.push(newRecord)
      usedTransactionNumbers.add(data.transactionNumber)

      // 更新订单的支付状态和金额
      const orderIndex = mockOrderPaymentList.findIndex(item => item.orderId === orderId)
      if (orderIndex !== -1) {
        const order = mockOrderPaymentList[orderIndex]
        const orderRecords = mockPaymentRecords.filter(record => record.orderId === orderId)
        
        // 计算已付金额
        const totalPayment = orderRecords
          .filter(record => record.businessType === '收款')
          .reduce((sum, record) => sum + record.amount, 0)
        const totalRefund = orderRecords
          .filter(record => record.businessType === '退款')
          .reduce((sum, record) => sum + record.amount, 0)
        
        order.paidAmount = Math.max(0, totalPayment - totalRefund)
        order.unpaidAmount = Math.max(0, order.totalAmount - order.paidAmount)

        // 更新支付状态
        if (order.paidAmount < 1000) {
          order.paymentStatus = '待支付定金'
        } else if (order.paidAmount >= 1000 && order.paidAmount < order.totalAmount) {
          order.paymentStatus = '已支付定金'
        } else if (order.paidAmount >= order.totalAmount) {
          order.paymentStatus = '已支付尾款'
          order.canInvoice = true
        }

        order.updateTime = new Date().toISOString().replace('T', ' ').substring(0, 19)
      }

      resolve()
    }, 500)
  })
}

// 删除收退款记录
export const deletePaymentRecord = (orderId: string, recordId: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const recordIndex = mockPaymentRecords.findIndex(record => 
        record.paymentRecordId === recordId && record.orderId === orderId
      )

      if (recordIndex === -1) {
        reject(new Error('记录不存在'))
        return
      }

      const record = mockPaymentRecords[recordIndex]
      if (!record.isDeletable) {
        reject(new Error('APP数据不可删除'))
        return
      }

      // 删除记录
      const deletedRecord = mockPaymentRecords.splice(recordIndex, 1)[0]
      usedTransactionNumbers.delete(deletedRecord.transactionNumber)

      // 更新订单的支付状态和金额
      const orderIndex = mockOrderPaymentList.findIndex(item => item.orderId === orderId)
      if (orderIndex !== -1) {
        const order = mockOrderPaymentList[orderIndex]
        const orderRecords = mockPaymentRecords.filter(record => record.orderId === orderId)
        
        // 重新计算已付金额
        const totalPayment = orderRecords
          .filter(record => record.businessType === '收款')
          .reduce((sum, record) => sum + record.amount, 0)
        const totalRefund = orderRecords
          .filter(record => record.businessType === '退款')
          .reduce((sum, record) => sum + record.amount, 0)
        
        order.paidAmount = Math.max(0, totalPayment - totalRefund)
        order.unpaidAmount = Math.max(0, order.totalAmount - order.paidAmount)

        // 重新判断支付状态
        if (order.paidAmount < 1000) {
          order.paymentStatus = '待支付定金'
          order.canInvoice = false
        } else if (order.paidAmount >= 1000 && order.paidAmount < order.totalAmount) {
          order.paymentStatus = '已支付定金'
          order.canInvoice = false
        } else if (order.paidAmount >= order.totalAmount) {
          order.paymentStatus = '已支付尾款'
          order.canInvoice = true
        }

        order.updateTime = new Date().toISOString().replace('T', ' ').substring(0, 19)
      }

      resolve()
    }, 300)
  })
}

// 检查流水号是否存在
export const checkTransactionNumber = (transactionNumber: string): Promise<boolean> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(usedTransactionNumbers.has(transactionNumber))
    }, 200)
  })
} 