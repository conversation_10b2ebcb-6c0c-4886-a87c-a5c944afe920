import type {
  WorkOrder,
  WorkOrderListItem,
  LaborItem,
  PartsItem,
  ProjectItem,
  PartsSelectItem,
  User,
  OperationLog
} from '@/types/workOrder';

// 工单列表Mock数据
export const workOrderListData: WorkOrderListItem[] = [
  {
    work_order_id: 'wo_001',
    work_order_number: 'WO20241201001',
    work_order_type: 'repair',
    work_order_priority: 'urgent',
    customer_source: 'appointment',
    work_order_status: 'in_progress',
    payment_status: 'deposit_paid',
    qc_status: 'pending',
    is_claim: true,
    is_outsourced: false,
    has_additional_items: true,
    customer_name: '张三',
    sender_name: '张三',
    sender_phone: '139****5678',
    license_plate: '京A12345',
    model_config_color: '宝马X3 2.0T 豪华版 白色',
    total_amount: 5000.00,
    service_advisor_name: '李服务',
    technician_name: '王技师',
    confirmation_time: '2024-12-01 14:30',
    start_time: '2024-12-02 09:30',
    created_time: '2024-12-01 09:00',
    updated_time: '2024-12-02 11:15'
  },
  {
    work_order_id: 'wo_002',
    work_order_number: 'WO20241201002',
    work_order_type: 'maintenance',
    work_order_priority: 'normal',
    customer_source: 'appointment',
    work_order_status: 'pending_qc',
    payment_status: 'paid',
    qc_status: 'pending',
    is_claim: false,
    is_outsourced: false,
    has_additional_items: false,
    customer_name: '李四',
    sender_name: '李四',
    sender_phone: '138****9876',
    license_plate: '京B67890',
    model_config_color: '奔驰C200L 豪华版 黑色',
    total_amount: 2000.00,
    service_advisor_name: '张服务',
    technician_name: '刘技师',
    confirmation_time: '2024-12-01 16:00',
    start_time: '2024-12-02 08:00',
    end_time: '2024-12-02 16:45',
    created_time: '2024-12-01 10:30',
    updated_time: '2024-12-02 16:45'
  },
  {
    work_order_id: 'wo_003',
    work_order_number: 'WO20241201003',
    work_order_type: 'repair',
    work_order_priority: 'normal',
    customer_source: 'walkin',
    work_order_status: 'pending_confirmation',
    payment_status: 'pending',
    is_claim: false,
    is_outsourced: true,
    has_additional_items: false,
    customer_name: '王五',
    sender_name: '王五',
    sender_phone: '137****1234',
    license_plate: '沪C11111',
    model_config_color: '奥迪A4L 进取版 银色',
    total_amount: 3500.00,
    service_advisor_name: '赵服务',
    created_time: '2024-12-01 15:20',
    updated_time: '2024-12-01 15:20'
  },
  {
    work_order_id: 'wo_004',
    work_order_number: 'WO20241201004',
    work_order_type: 'maintenance',
    work_order_priority: 'normal',
    customer_source: 'appointment',
    work_order_status: 'completed',
    payment_status: 'paid',
    qc_status: 'passed',
    is_claim: false,
    is_outsourced: false,
    has_additional_items: false,
    customer_name: '陈六',
    sender_name: '陈六',
    sender_phone: '136****5678',
    license_plate: '粤A88888',
    model_config_color: '本田雅阁 2.0T 尊贵版 珍珠白',
    total_amount: 1200.00,
    service_advisor_name: '李服务',
    technician_name: '张技师',
    confirmation_time: '2024-11-30 09:15',
    start_time: '2024-11-30 14:00',
    end_time: '2024-11-30 17:30',
    created_time: '2024-11-30 08:00',
    updated_time: '2024-11-30 17:45'
  },
  {
    work_order_id: 'wo_005',
    work_order_number: 'WO20241201005',
    work_order_type: 'repair',
    work_order_priority: 'urgent',
    customer_source: 'walkin',
    work_order_status: 'additional_pending',
    payment_status: 'deposit_paid',
    qc_status: 'pending',
    is_claim: true,
    is_outsourced: false,
    has_additional_items: true,
    customer_name: '刘七',
    sender_name: '刘七',
    sender_phone: '135****9999',
    license_plate: '浙A12345',
    model_config_color: '大众帕萨特 1.8T 舒适版 银灰色',
    total_amount: 4200.00,
    service_advisor_name: '张服务',
    technician_name: '李技师',
    confirmation_time: '2024-12-01 11:20',
    start_time: '2024-12-01 15:00',
    created_time: '2024-12-01 08:30',
    updated_time: '2024-12-01 16:20'
  },
  {
    work_order_id: 'wo_006',
    work_order_number: 'WO20241201006',
    work_order_type: 'insurance',
    work_order_priority: 'normal',
    customer_source: 'appointment',
    work_order_status: 'waiting_approval',
    payment_status: 'pending',
    qc_status: 'pending',
    is_claim: true,
    is_outsourced: true,
    has_additional_items: false,
    customer_name: '吴八',
    sender_name: '吴八',
    sender_phone: '134****7777',
    license_plate: '苏A66666',
    model_config_color: '丰田凯美瑞 2.5L 豪华版 黑色',
    total_amount: 8500.00,
    service_advisor_name: '赵服务',
    created_time: '2024-12-01 13:45',
    updated_time: '2024-12-01 14:00'
  },
  {
    work_order_id: 'wo_007',
    work_order_number: 'WO20241201007',
    work_order_type: 'repair',
    work_order_priority: 'normal',
    customer_source: 'walkin',
    work_order_status: 'waiting_parts',
    payment_status: 'deposit_paid',
    qc_status: 'pending',
    is_claim: false,
    is_outsourced: false,
    has_additional_items: false,
    customer_name: '郑九',
    sender_name: '郑九',
    sender_phone: '133****1111',
    license_plate: '鲁A55555',
    model_config_color: '福特蒙迪欧 2.0T 运动版 蓝色',
    total_amount: 2800.00,
    service_advisor_name: '李服务',
    technician_name: '王技师',
    confirmation_time: '2024-12-01 10:30',
    created_time: '2024-12-01 09:15',
    updated_time: '2024-12-01 11:00'
  },
  {
    work_order_id: 'wo_008',
    work_order_number: 'WO20241201008',
    work_order_type: 'maintenance',
    work_order_priority: 'normal',
    customer_source: 'appointment',
    work_order_status: 'confirmed',
    payment_status: 'paid',
    qc_status: 'pending',
    is_claim: false,
    is_outsourced: false,
    has_additional_items: false,
    customer_name: '孙十',
    sender_name: '孙十',
    sender_phone: '132****2222',
    license_plate: '川A77777',
    model_config_color: '日产天籁 2.0L 舒适版 白色',
    total_amount: 1500.00,
    service_advisor_name: '张服务',
    technician_name: '刘技师',
    confirmation_time: '2024-12-02 08:00',
    created_time: '2024-12-01 16:30',
    updated_time: '2024-12-02 08:15'
  },
  {
    work_order_id: 'wo_009',
    work_order_number: 'WO20241201009',
    work_order_type: 'repair',
    work_order_priority: 'urgent',
    customer_source: 'walkin',
    work_order_status: 'draft',
    payment_status: 'pending',
    qc_status: 'pending',
    is_claim: false,
    is_outsourced: false,
    has_additional_items: false,
    customer_name: '周十一',
    sender_name: '周十一',
    sender_phone: '131****3333',
    license_plate: '湘A99999',
    model_config_color: '马自达阿特兹 2.5L 旗舰版 红色',
    total_amount: 0.00,
    service_advisor_name: '赵服务',
    created_time: '2024-12-02 14:20',
    updated_time: '2024-12-02 14:20'
  },
  {
    work_order_id: 'wo_010',
    work_order_number: 'WO20241201010',
    work_order_type: 'maintenance',
    work_order_priority: 'normal',
    customer_source: 'appointment',
    work_order_status: 'pending_assignment',
    payment_status: 'paid',
    qc_status: 'pending',
    is_claim: false,
    is_outsourced: false,
    has_additional_items: false,
    customer_name: '钱十二',
    sender_name: '钱十二',
    sender_phone: '130****4444',
    license_plate: '闽A11111',
    model_config_color: '别克君威 1.5T 精英版 银色',
    total_amount: 1800.00,
    service_advisor_name: '李服务',
    confirmation_time: '2024-12-02 09:45',
    created_time: '2024-12-02 08:30',
    updated_time: '2024-12-02 10:00'
  }
];

// 完整工单详情Mock数据
export const workOrderDetailData: Record<string, WorkOrder> = {
  'wo_001': {
    work_order_id: 'wo_001',
    work_order_number: 'WO20241201001',
    work_order_type: 'repair',
    work_order_priority: 'urgent',
    customer_source: 'appointment',
    work_order_status: 'in_progress',
    payment_status: 'deposit_paid',
    qc_status: 'pending',
    is_claim: true,
    is_outsourced: false,
    has_additional_items: true,
    service_advisor_name: '李服务',
    technician_name: '王技师',
    estimated_hours: 3.5,
    actual_hours: 4.25,
    start_time: '2024-12-02 09:30',
    created_time: '2024-12-01 09:00',
    updated_time: '2024-12-02 11:15',
    customer_info: {
      customer_name: '张三',
      customer_phone: '139****5678',
      sender_name: '张三',
      sender_phone: '139****5678',
      confirmation_method: 'online',
      confirmation_time: '2024-12-01 14:30'
    },
    vehicle_info: {
      license_plate: '京A12345',
      vin_code: 'WBAPL11050A123456',
      model_config_color: '宝马X3 2.0T 豪华版 白色',
      warranty_status: '在保',
      warranty_expiry: '2025-12-01',
      remarks: '客户反馈发动机异响，需要检查'
    },
    labor_items: [
      {
        labor_item_id: 'li_001',
        item_type: 'repair',
        item_code: 'R001',
        item_name: '发动机检查维修',
        is_claim: true,
        is_additional: false,
        standard_hours: 2.5,
        labor_rate: 120,
        subtotal: 300
      },
      {
        labor_item_id: 'li_002',
        item_type: 'repair',
        item_code: 'R002',
        item_name: '轮胎平衡',
        is_claim: false,
        is_additional: true,
        standard_hours: 0.5,
        labor_rate: 100,
        subtotal: 50
      }
    ],
    parts_items: [
      {
        parts_item_id: 'pi_001',
        parts_code: 'P001',
        parts_name: '机油滤清器',
        is_claim: true,
        is_additional: false,
        available_stock: 45,
        quantity: 1,
        unit_price: 25,
        subtotal: 25
      },
      {
        parts_item_id: 'pi_002',
        parts_code: 'P002',
        parts_name: '轮胎气嘴',
        is_claim: false,
        is_additional: true,
        available_stock: 20,
        quantity: 4,
        unit_price: 5,
        subtotal: 20
      }
    ],
    cost_summary: {
      labor_amount: 350,
      parts_amount: 45,
      additional_amount: 70,
      total_amount: 465,
      deposit_amount: 232.5,
      balance_amount: 232.5
    }
  }
};

// 项目选择Mock数据
export const projectItemsData: ProjectItem[] = [
  // 保养项目
  {
    item_id: 'proj_001',
    item_code: 'M001',
    item_name: '机油更换',
    item_type: 'maintenance',
    standard_hours: 1.0,
    labor_rate: 80,
    is_package: true
  },
  {
    item_id: 'proj_002',
    item_code: 'M002',
    item_name: '空调滤清器更换',
    item_type: 'maintenance',
    standard_hours: 0.5,
    labor_rate: 80,
    is_package: true
  },
  {
    item_id: 'proj_003',
    item_code: 'M003',
    item_name: '空调清洗',
    item_type: 'maintenance',
    standard_hours: 1.5,
    labor_rate: 80,
    is_package: false
  },
  // 维修项目
  {
    item_id: 'proj_004',
    item_code: 'R001',
    item_name: '刹车片更换',
    item_type: 'repair',
    standard_hours: 2.5,
    labor_rate: 120,
    is_package: false
  },
  {
    item_id: 'proj_005',
    item_code: 'R002',
    item_name: '轮胎更换',
    item_type: 'repair',
    standard_hours: 1.0,
    labor_rate: 100,
    is_package: false
  },
  {
    item_id: 'proj_006',
    item_code: 'R003',
    item_name: '发动机检查维修',
    item_type: 'repair',
    standard_hours: 2.5,
    labor_rate: 120,
    is_package: false
  },
  // 索赔项目
  {
    item_id: 'proj_007',
    item_code: 'C001',
    item_name: '质保范围内的发动机维修',
    item_type: 'claim',
    standard_hours: 3.0,
    labor_rate: 150,
    is_package: false
  }
];

// 零件选择Mock数据
export const partsSelectData: PartsSelectItem[] = [
  {
    parts_id: 'parts_001',
    parts_code: 'P001',
    parts_name: '机油滤清器',
    unit_price: 25,
    available_stock: 50,
    is_claim: false
  },
  {
    parts_id: 'parts_002',
    parts_code: 'P002',
    parts_name: '空调滤清器',
    unit_price: 35,
    available_stock: 30,
    is_claim: false
  },
  {
    parts_id: 'parts_003',
    parts_code: 'P003',
    parts_name: '刹车片套装',
    unit_price: 150,
    available_stock: 8,
    is_claim: false
  },
  {
    parts_id: 'parts_004',
    parts_code: 'P004',
    parts_name: '轮胎195/65R15',
    unit_price: 300,
    available_stock: 12,
    is_claim: false
  },
  {
    parts_id: 'parts_005',
    parts_code: 'P005',
    parts_name: '机油5W-30 4L',
    unit_price: 120,
    available_stock: 25,
    is_claim: false
  },
  {
    parts_id: 'parts_006',
    parts_code: 'P006',
    parts_name: '质保范围内发动机零件',
    unit_price: 500,
    available_stock: 5,
    is_claim: true
  }
];

// 用户Mock数据
export const usersData: User[] = [
  {
    user_id: 'user_001',
    user_name: '李服务',
    user_role: 'service_advisor'
  },
  {
    user_id: 'user_002',
    user_name: '张服务',
    user_role: 'service_advisor'
  },
  {
    user_id: 'user_003',
    user_name: '赵服务',
    user_role: 'service_advisor'
  },
  {
    user_id: 'user_004',
    user_name: '王技师',
    user_role: 'technician'
  },
  {
    user_id: 'user_005',
    user_name: '刘技师',
    user_role: 'technician'
  },
  {
    user_id: 'user_006',
    user_name: '孙质检',
    user_role: 'qc_inspector'
  }
];

// 操作日志Mock数据
export const operationLogsData: Record<string, OperationLog[]> = {
  'wo_001': [
    {
      log_id: 'log_001',
      operation_type: '订单创建',
      operator_name: '李服务',
      operation_time: '2024-12-01 09:00',
      operation_description: '创建维修工单'
    },
    {
      log_id: 'log_002',
      operation_type: '订单推送',
      operator_name: '系统',
      operation_time: '2024-12-01 09:05',
      operation_description: '推送客户确认'
    },
    {
      log_id: 'log_003',
      operation_type: '客户确认',
      operator_name: '张三',
      operation_time: '2024-12-01 14:30',
      operation_description: '线上APP确认'
    },
    {
      log_id: 'log_004',
      operation_type: '定金支付',
      operator_name: '张三',
      operation_time: '2024-12-01 14:35',
      operation_description: '支付定金232.5元'
    },
    {
      log_id: 'log_005',
      operation_type: '订单分配',
      operator_name: '技师经理',
      operation_time: '2024-12-01 15:00',
      operation_description: '分配给技师王技师'
    },
    {
      log_id: 'log_006',
      operation_type: '订单开工',
      operator_name: '王技师',
      operation_time: '2024-12-02 09:30',
      operation_description: '技师开始工作'
    },
    {
      log_id: 'log_007',
      operation_type: '订单增项',
      operator_name: '李服务',
      operation_time: '2024-12-02 11:00',
      operation_description: '添加轮胎平衡项目'
    },
    {
      log_id: 'log_008',
      operation_type: '增项确认',
      operator_name: '张三',
      operation_time: '2024-12-02 11:15',
      operation_description: '客户确认增项'
    }
  ]
};
