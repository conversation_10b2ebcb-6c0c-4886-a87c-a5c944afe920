import { ref } from 'vue';
import { mockPartArchivesData } from './partArchivesData';

// 计算叫料单整体状态的函数 - 修复单零件状态逻辑
function calculateRequisitionStatus(items: DetailItem[], currentStatus: string): string {
  // 如果当前状态是提交、审批、驳回、作废等，不需要重新计算
  if (['submitted', 'approved', 'rejected', 'voided'].includes(currentStatus)) {
    return currentStatus;
  }

  const totalItems = items.length;

  // 统计收货情况
  const fullyReceivedItems = items.filter(item => item.isFullyReceived).length;
  const partiallyReceivedItems = items.filter(item =>
    item.receivedQuantity && item.receivedQuantity > 0 && !item.isFullyReceived
  ).length;

  // 统计发货情况
  const fullyShippedItems = items.filter(item => item.isFullyShipped).length;
  const partiallyShippedItems = items.filter(item =>
    item.shippedQuantity && item.shippedQuantity > 0 && !item.isFullyShipped
  ).length;

  // 优先判断收货状态
  if (fullyReceivedItems === totalItems) {
    // 所有零件都已完全收货
    return 'received';
  } else if (totalItems > 1 && (fullyReceivedItems > 0 || partiallyReceivedItems > 0)) {
    // 多个零件且有部分收货（只有多个零件才能有部分收货状态）
    return 'partialReceived';
  } else if (fullyShippedItems === totalItems) {
    // 所有零件都已完全发货但未收货
    return 'shipped';
  } else if (totalItems > 1 && (fullyShippedItems > 0 || partiallyShippedItems > 0)) {
    // 多个零件且有部分发货（只有多个零件才能有部分发货状态）
    return 'partialShipped';
  } else {
    // 没有零件发货，保持已审批状态
    return 'approved';
  }
}

interface DetailItem {
  partName: string;
  partNumber: string;
  quantity: number;
  unit: string;
  requisitionStatus: 'submitted' | 'approved' | 'rejected' | 'shipped' | 'partialShipped' | 'partialReceived' | 'received' | 'cancelled' | 'voided';
  requisitionDate: string;
  expectedArrivalTime: string;
  supplierName: string;
  // 新增字段用于跟踪零件级别的发货状态
  shippedQuantity?: number; // 已发货数量
  isFullyShipped?: boolean; // 是否完全发货
}

interface PartManagementItem {
  id: string;
  requisitionNumber: string;
  purchaseOrderNumber: string;
  requisitionDate: string;
  requisitionStatus: 'submitted' | 'approved' | 'rejected' | 'shipped' | 'partialShipped' | 'partialReceived' | 'received' | 'cancelled' | 'voided';
  partName: string;
  partNumber: string;
  supplierName: string;
  storeName: string; // 门店名称
  inventoryStatus: 'normal' | 'belowSafetyStock';
  items?: DetailItem[]; // Add items array for detail view
  rejectionReason?: string; // Add rejection reason field
  documentType?: string; // 新增字段，用于模拟单据类型
}

const mockPartManagementData: PartManagementItem[] = Array.from({ length: 200 }, (_, i) => {
  const itemsCount = Math.floor(Math.random() * 3) + 1; // 1 to 3 items per requisition

  // 根据零件数量决定可用的状态
  let requisitionStatusOptions: string[];
  if (itemsCount === 1) {
    // 单个零件：不能有部分发货或部分收货状态
    requisitionStatusOptions = ['submitted', 'approved', 'rejected', 'shipped', 'received', 'voided'];
  } else {
    // 多个零件：可以有所有状态
    requisitionStatusOptions = ['submitted', 'approved', 'rejected', 'shipped', 'partialShipped', 'partialReceived', 'received', 'voided'];
  }

  const status = requisitionStatusOptions[i % requisitionStatusOptions.length];

  // Generate a random date within the last 6 months
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
  const randomDate = new Date(sixMonthsAgo.getTime() + Math.random() * (new Date().getTime() - sixMonthsAgo.getTime()));
  const formattedRequisitionDate = randomDate.toISOString().slice(0, 10);

  // Generate expected arrival time 1-7 days after requisition date
  const expectedArrivalDate = new Date(randomDate);
  expectedArrivalDate.setDate(randomDate.getDate() + Math.floor(Math.random() * 7) + 1);
  const formattedExpectedArrivalTime = expectedArrivalDate.toISOString().slice(0, 10);

  const items: DetailItem[] = Array.from({ length: itemsCount }, (_, j) => {
    const quantity = Math.floor(Math.random() * 100) + 1;
    let shippedQuantity = 0;
    let isFullyShipped = false;

    // 根据状态设置发货和收货信息
    let receivedQuantity = 0;
    let isFullyReceived = false;

    if (status === 'received') {
      // 已收货状态：所有零件都完全收货（必须先发货）
      shippedQuantity = quantity;
      isFullyShipped = true;
      receivedQuantity = quantity;
      isFullyReceived = true;
    } else if (status === 'partialReceived') {
      // 部分收货状态：只有在多个零件时才会出现此状态
      if (itemsCount === 1) {
        // 如果只有一个零件，应该是已收货状态，而不是部分收货
        shippedQuantity = quantity;
        isFullyShipped = true;
        receivedQuantity = quantity;
        isFullyReceived = true;
      } else {
        // 多个零件的情况下设置部分收货
        if (j === 0) {
          // 第一个零件完全收货
          shippedQuantity = quantity;
          isFullyShipped = true;
          receivedQuantity = quantity;
          isFullyReceived = true;
        } else if (j === 1) {
          // 第二个零件已发货但部分收货
          shippedQuantity = quantity;
          isFullyShipped = true;
          receivedQuantity = Math.floor(quantity * 0.7); // 收货70%
          isFullyReceived = false;
        } else if (j === 2) {
          // 第三个零件已发货但未收货
          shippedQuantity = quantity;
          isFullyShipped = true;
          receivedQuantity = 0;
          isFullyReceived = false;
        }
        // 其他零件未发货
      }
    } else if (status === 'shipped') {
      // 已发货状态：所有零件都完全发货但未收货
      shippedQuantity = quantity;
      isFullyShipped = true;
    } else if (status === 'partialShipped') {
      // 部分发货状态：只有在多个零件时才会出现此状态
      if (itemsCount === 1) {
        // 如果只有一个零件，应该是已发货状态，而不是部分发货
        // 这种情况下将状态改为shipped
        shippedQuantity = quantity;
        isFullyShipped = true;
      } else {
        // 多个零件的情况下设置部分发货
        if (j === 0) {
          // 第一个零件完全发货
          shippedQuantity = quantity;
          isFullyShipped = true;
        } else if (j === 1) {
          // 第二个零件部分发货
          shippedQuantity = Math.floor(quantity * 0.6); // 发货60%
          isFullyShipped = false;
        }
        // 其他零件未发货
      }
    }

    // 从零件档案中随机选择一个零件
    const partArchiveIndex = (i + j) % mockPartArchivesData.length;
    const selectedPart = mockPartArchivesData[partArchiveIndex];

    return {
      partName: selectedPart.partName,
      partNumber: selectedPart.partNumber,
      quantity: quantity,
      unit: selectedPart.unit,
      requisitionStatus: status,
      requisitionDate: formattedRequisitionDate,
      expectedArrivalTime: formattedExpectedArrivalTime,
      supplierName: selectedPart.supplierName,
      shippedQuantity: shippedQuantity,
      isFullyShipped: isFullyShipped,
      receivedQuantity: receivedQuantity,
      isFullyReceived: isFullyReceived,
    };
  });

  // 采购单号逻辑：只有特定状态才有采购单号
  let purchaseOrderNumber = '';
  if (status === 'shipped' || status === 'partialShipped' || status === 'partialReceived' || status === 'received') {
    // 已发货、部分发货、部分收货、已收货状态必须有采购单号
    purchaseOrderNumber = `CG${2023000 + i}`;
  } else if (status === 'approved') {
    // 已审批状态有的有有的没有（约50%概率）
    purchaseOrderNumber = i % 2 === 0 ? `CG${2023000 + i}` : '';
  }
  // 其他状态（submitted, rejected, voided）没有采购单号

  // 模拟 documentType
  let documentType = '';
  if (Math.random() < 0.5) { // 约50%是叫料单
    documentType = 'requisition';
  } else if (Math.random() < 0.5) { // 另外50%中，约50%是报损单
    documentType = 'scrap';
  } else {
    documentType = 'picking';
  }

  // 重新计算叫料单状态，确保状态与零件发货情况一致
  const recalculatedStatus = calculateRequisitionStatus(items, status);

  // 更新所有零件的状态与叫料单状态保持一致
  items.forEach(item => {
    item.requisitionStatus = recalculatedStatus as any;
  });

  // 从零件档案中选择一个零件作为主记录显示
  const mainPartIndex = i % mockPartArchivesData.length;
  const mainPart = mockPartArchivesData[mainPartIndex];

  return {
    id: `req${i + 1}`,
    requisitionNumber: `${documentType.toUpperCase().substring(0, 4)}-${1000 + i}`,
    purchaseOrderNumber: purchaseOrderNumber,
    requisitionDate: formattedRequisitionDate,
    requisitionStatus: recalculatedStatus as PartManagementItem['requisitionStatus'],
    partName: mainPart.partName,
    partNumber: mainPart.partNumber,
    supplierName: mainPart.supplierName,
    storeName: `门店${i % 3 + 1}`,
    inventoryStatus: i % 2 === 0 ? 'normal' : 'belowSafetyStock',
    items: items,
    rejectionReason: status === 'rejected' ? `驳回原因：${i + 1}` : '',
    documentType: documentType, // Add documentType here
  };
});

export function addRequisitionItem(newItem: PartManagementItem) {
  mockPartManagementData.unshift(newItem); // 添加到数组开头，方便查看最新添加的数据
}

// 更新叫料单
export function updateRequisitionItem(requisitionNumber: string, updateData: Partial<PartManagementItem>) {
  const index = mockPartManagementData.findIndex(item => item.requisitionNumber === requisitionNumber);

  if (index === -1) {
    return { success: false, message: '未找到对应的叫料单' };
  }

  // 更新叫料单数据
  Object.assign(mockPartManagementData[index], updateData);

  return { success: true, message: '叫料单更新成功' };
}

// 模拟零件发货操作
export function shipPartialItems(requisitionNumber: string, shipmentDetails: Array<{partNumber: string, shippedQuantity: number}>) {
  const requisition = mockPartManagementData.find(item => item.requisitionNumber === requisitionNumber);
  if (!requisition || !requisition.items) {
    return { success: false, message: '叫料单不存在' };
  }

  // 更新零件发货信息
  shipmentDetails.forEach(shipment => {
    const item = requisition.items!.find(item => item.partNumber === shipment.partNumber);
    if (item) {
      item.shippedQuantity = (item.shippedQuantity || 0) + shipment.shippedQuantity;
      item.isFullyShipped = item.shippedQuantity >= item.quantity;
    }
  });

  // 重新计算叫料单状态
  const newStatus = calculateRequisitionStatus(requisition.items, requisition.requisitionStatus);
  requisition.requisitionStatus = newStatus as any;

  // 更新所有零件的状态
  requisition.items.forEach(item => {
    item.requisitionStatus = newStatus as any;
  });

  return {
    success: true,
    message: '发货成功',
    newStatus: newStatus,
    requisition: requisition
  };
}

// 模拟完整发货操作
export function shipAllItems(requisitionNumber: string) {
  const requisition = mockPartManagementData.find(item => item.requisitionNumber === requisitionNumber);
  if (!requisition || !requisition.items) {
    return { success: false, message: '叫料单不存在' };
  }

  // 将所有零件标记为完全发货
  requisition.items.forEach(item => {
    item.shippedQuantity = item.quantity;
    item.isFullyShipped = true;
    item.requisitionStatus = 'shipped';
  });

  // 更新叫料单状态为已发货
  requisition.requisitionStatus = 'shipped';

  return {
    success: true,
    message: '全部发货成功',
    requisition: requisition
  };
}

export function fetchPartManagementData(params: any) {
  const { page = 1, pageSize = 10, approvalType, partName, partNumber, requisitionNumber, purchaseOrderNumber, supplierName, storeName, requisitionStatus, inventoryStatus, status, scrapOrderNumber } = params;

  // 根据审批类型返回不同的数据
  if (approvalType === 'damage') {
    // 返回零件破损审批数据
    let filteredData = [...mockScrapRecords];

    if (partName) {
      filteredData = filteredData.filter(item => item.partName.includes(partName));
    }
    if (partNumber) {
      filteredData = filteredData.filter(item => item.partNumber.includes(partNumber));
    }
    if (storeName) {
      filteredData = filteredData.filter(item => item.storeName.includes(storeName));
    }
    if (scrapOrderNumber) {
      filteredData = filteredData.filter(item => item.scrapOrderNumber === scrapOrderNumber);
    }
    if (status) {
      // 为破损记录添加状态筛选
      filteredData = filteredData.filter(item => item.status === status);
    }

    // 按报损日期排序（最新的在前）
    filteredData.sort((a, b) => new Date(b.scrapDate).getTime() - new Date(a.scrapDate).getTime());

    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedData = filteredData.slice(start, end);

    return {
      data: paginatedData,
      total: filteredData.length,
    };
  } else {
    // 默认返回叫料审批数据
    let filteredData = [...mockPartManagementData]; // Create a shallow copy to avoid modifying the original array

    if (partName) {
      filteredData = filteredData.filter(item => item.partName.includes(partName));
    }
    if (partNumber) {
      filteredData = filteredData.filter(item => item.partNumber.includes(partNumber));
    }
    if (requisitionNumber) {
      filteredData = filteredData.filter(item => item.requisitionNumber.includes(requisitionNumber));
    }
    if (purchaseOrderNumber) {
      filteredData = filteredData.filter(item => item.purchaseOrderNumber.includes(purchaseOrderNumber));
    }
    if (supplierName) {
      filteredData = filteredData.filter(item => item.supplierName.includes(supplierName));
    }
    if (storeName) {
      filteredData = filteredData.filter(item => item.storeName.includes(storeName));
    }
    if (requisitionStatus) {
      filteredData = filteredData.filter(item => item.requisitionStatus === requisitionStatus);
    }
    if (inventoryStatus) {
      filteredData = filteredData.filter(item => item.inventoryStatus === inventoryStatus);
    }

    // 全局排序：已提交状态优先，按叫料日期从远到近排列
    filteredData.sort((a, b) => {
      // 如果a是已提交状态，b不是，则a排在前面
      if (a.requisitionStatus === 'submitted' && b.requisitionStatus !== 'submitted') {
        return -1;
      }
      // 如果b是已提交状态，a不是，则b排在前面
      if (b.requisitionStatus === 'submitted' && a.requisitionStatus !== 'submitted') {
        return 1;
      }
      // 如果都是已提交状态，按叫料日期从远到近排列（旧的在前）
      if (a.requisitionStatus === 'submitted' && b.requisitionStatus === 'submitted') {
        const dateA = new Date(a.requisitionDate);
        const dateB = new Date(b.requisitionDate);
        return dateA.getTime() - dateB.getTime(); // 从远到近
      }
      // 如果都不是已提交状态，按叫料日期从近到远排列（新的在前）
      const dateA = new Date(a.requisitionDate);
      const dateB = new Date(b.requisitionDate);
      return dateB.getTime() - dateA.getTime(); // 从近到远
    });

    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedData = filteredData.slice(start, end);

    return {
      data: paginatedData,
      total: filteredData.length,
    };
  }
}

interface ScrapRecordItem {
  id: string;
  scrapOrderNumber: string;
  partName: string;
  partNumber: string;
  scrapQuantity: number;
  scrapDate: string;
  scrapSource: 'receipt' | 'repair' | 'other';
  receiptTime: string;
  scrapReason: string;
  storeName: string; // 门店名称
  status: 'submitted' | 'approved' | 'rejected' | 'voided'; // 破损申请状态
  scrapImages?: string[]; // 报损图片URL数组
}

const mockScrapRecords: ScrapRecordItem[] = (() => {
  const records: ScrapRecordItem[] = [];
  let recordId = 3000;

  // 生成30个报损单，每个报损单可能包含1-3个不同的零件
  for (let i = 0; i < 30; i++) {
    const scrapOrderNumber = `BS2023${String(i + 1).padStart(3, '0')}`;
    const scrapDate = `2023-02-${(i % 28) + 1 < 10 ? '0' : ''}${(i % 28) + 1}`;
    const storeName = `门店${(i % 8) + 1}`;
    const scrapSource = ['receipt', 'repair', 'other'][i % 3];

    // 生成不同的状态，确保有各种状态的数据
    const statusOptions = ['submitted', 'approved', 'rejected', 'voided'];
    const status = statusOptions[i % statusOptions.length];

    // 每个报损单包含1-3个不同的零件
    const itemCount = Math.floor(Math.random() * 3) + 1;

    for (let j = 0; j < itemCount; j++) {
      recordId++;

      // 生成报损图片
      const imageCount = Math.floor(Math.random() * 3) + 1; // 1-3张图片
      const scrapImages = Array.from({ length: imageCount }, (_, imgIndex) =>
        `https://via.placeholder.com/300x200/ff6b6b/ffffff?text=报损图片${recordId}-${imgIndex + 1}`
      );

      // 从零件档案中选择一个零件
      const partArchiveIndex = (recordId + j) % mockPartArchivesData.length;
      const selectedPart = mockPartArchivesData[partArchiveIndex];

      const record: any = {
        id: `SR${recordId}`,
        scrapOrderNumber,
        partName: selectedPart.partName,
        partNumber: selectedPart.partNumber,
        scrapQuantity: (j + 1) * 2 + (i % 5), // 不同的数量
        scrapDate,
        scrapSource,
        receiptTime: scrapDate,
        scrapReason: `报损原因描述 ${recordId}: 这是关于零件报损的详细原因，包括零件损坏情况、发现时间、损坏程度等详细信息。`,
        storeName,
        status, // 添加状态字段
        scrapImages,
      };

      // 如果是收货报损，添加到货单号和收货单号
      if (scrapSource === 'receipt') {
        record.deliveryOrderNumber = `DH2023${String(recordId).padStart(3, '0')}`;
        record.receiptOrderNumber = `SH2023${String(recordId).padStart(3, '0')}`;
      }

      records.push(record);
    }
  }

  return records;
})();

export function fetchScrapRecords(params: any) {
  const { page = 1, pageSize = 10, partName, partNumber, scrapSource, receiptTime } = params;

  let filteredData = mockScrapRecords;

  if (partName) {
    filteredData = filteredData.filter(item => item.partName.includes(partName));
  }
  if (partNumber) {
    filteredData = filteredData.filter(item => item.partNumber.includes(partNumber));
  }
  if (scrapSource) {
    filteredData = filteredData.filter(item => item.scrapSource === scrapSource);
  }
  if (receiptTime) {
    // For simplicity, convert Date objects to YYYY-MM-DD string for comparison
    const formattedReceiptTime = new Date(receiptTime).toISOString().slice(0, 10);
    filteredData = filteredData.filter(item => item.receiptTime.includes(formattedReceiptTime));
  }
  // Note: scrapDateRange filtering is more complex and omitted for brevity in mock data

  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const paginatedData = filteredData.slice(start, end);

  return {
    data: paginatedData,
    total: filteredData.length,
  };
}
