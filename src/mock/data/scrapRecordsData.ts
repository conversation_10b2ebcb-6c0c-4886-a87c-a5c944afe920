export interface ScrapRecord {
  id: string;
  scrapOrderNumber: string;
  partName: string;
  partNumber: string;
  quantity: number;
  scrapReason: string;
  scrapImages?: string[]; // 报损图片URL数组
  scrapDate: string;
  scrapSource: string;
  status: 'submitted' | 'approved' | 'rejected' | 'voided'; // 单据状态
  rejectionReason?: string; // 驳回原因
  deliveryOrderNumber?: string; // 到货单号，仅收货报损时有值
  receiptOrderNumber?: string; // 收货单号，仅收货报损时有值
}

export const mockScrapRecords: ScrapRecord[] = [
  // Initial mock data for existing records
  {
    id: 'SR001',
    scrapOrderNumber: 'BS202301001',
    partName: '刹车片',
    partNumber: 'BP001',
    quantity: 1,
    scrapReason: '坏了，不能用',
    scrapImages: [
      'https://picsum.photos/400/300?random=1',
      'https://picsum.photos/400/300?random=2'
    ],
    scrapDate: '2023-01-01',
    scrapSource: 'repair',
    status: 'submitted',
  },
  {
    id: 'SR002',
    scrapOrderNumber: 'BS202301002',
    partName: '机油滤清器',
    partNumber: 'OF002',
    quantity: 1,
    scrapReason: '这个是坏的',
    scrapImages: [
      'https://picsum.photos/400/300?random=3'
    ],
    scrapDate: '2023-01-05',
    scrapSource: 'receipt',
    status: 'approved',
    deliveryOrderNumber: 'DH20230001',
    receiptOrderNumber: 'SH20230001',
  },
  // 同一报损单号的多个零件示例
  {
    id: 'SR003',
    scrapOrderNumber: 'BS202301003',
    partName: '火花塞',
    partNumber: 'SP001',
    quantity: 4,
    scrapReason: '批量报损：收货时发现包装破损，内部零件受潮无法使用',
    scrapImages: [
      'https://picsum.photos/400/300?random=4',
      'https://picsum.photos/400/300?random=5'
    ],
    scrapDate: '2023-01-10',
    scrapSource: 'receipt',
    status: 'rejected',
    rejectionReason: '报损原因不充分，需要提供更详细的损坏说明和现场照片。',
    deliveryOrderNumber: 'DH20230002',
    receiptOrderNumber: 'SH20230002',
  },
  {
    id: 'SR004',
    scrapOrderNumber: 'BS202301003', // 相同的报损单号
    partName: '空气滤清器',
    partNumber: 'AF001',
    quantity: 2,
    scrapReason: '批量报损：收货时发现包装破损，内部零件受潮无法使用',
    scrapImages: [
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2ZmOWZmMyIvPjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+56m65rCU5ruk5riF5Zmo5o6f5Z2P5Zu+PC90ZXh0Pjwvc3ZnPg=='
    ],
    scrapDate: '2023-01-10',
    scrapSource: 'receipt',
    status: 'rejected',
    rejectionReason: '报损原因不充分，需要提供更详细的损坏说明和现场照片。',
    deliveryOrderNumber: 'DH20230002',
    receiptOrderNumber: 'SH20230002',
  },
  {
    id: 'SR005',
    scrapOrderNumber: 'BS202301003', // 相同的报损单号
    partName: '机油',
    partNumber: 'OIL001',
    quantity: 3,
    scrapReason: '批量报损：收货时发现包装破损，内部零件受潮无法使用',
    scrapImages: [
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzU0YTBmZiIvPjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5py65rK55YyF6KOF5o6f5Z2P5Zu+PC90ZXh0Pjwvc3ZnPg=='
    ],
    scrapDate: '2023-01-10',
    scrapSource: 'receipt',
    status: 'rejected',
    rejectionReason: '报损原因不充分，需要提供更详细的损坏说明和现场照片。',
    deliveryOrderNumber: 'DH20230002',
    receiptOrderNumber: 'SH20230002',
  },
];

export const addScrapRecord = (record: ScrapRecord) => {
  mockScrapRecords.push(record);
};

export const fetchScrapRecordsData = (params: { page: number; pageSize: number; [key: string]: any }) => {
  const { page, pageSize, returnDetailRecords, ...query } = params;

  // 先按筛选条件过滤数据
  const filteredData = mockScrapRecords.filter(item => {
    const scrapOrderNumberMatch = query.scrapOrderNumber ? item.scrapOrderNumber.includes(query.scrapOrderNumber) : true;
    const partNameMatch = query.partName ? item.partName.includes(query.partName) : true;
    const partNumberMatch = query.partNumber ? item.partNumber.includes(query.partNumber) : true;
    const scrapDateMatch = query.scrapDateRange && query.scrapDateRange.length === 2
      ? item.scrapDate >= query.scrapDateRange[0] && item.scrapDate <= query.scrapDateRange[1]
      : true;
    const scrapSourceMatch = query.scrapSource ? item.scrapSource === query.scrapSource : true;
    const statusMatch = query.status ? item.status === query.status : true;

    return scrapOrderNumberMatch && partNameMatch && partNumberMatch && scrapDateMatch && scrapSourceMatch && statusMatch;
  });

  // 如果需要返回详细记录（用于主页显示）
  if (returnDetailRecords) {
    // 按报损日期排序，最新的在前
    filteredData.sort((a, b) => new Date(b.scrapDate).getTime() - new Date(a.scrapDate).getTime());

    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const data = filteredData.slice(start, end);

    return { data, total: filteredData.length };
  }

  // 默认按报损单号分组，以报损单为维度显示（用于报损记录弹窗）
  const groupedByOrderNumber = filteredData.reduce((acc, item) => {
    if (!acc[item.scrapOrderNumber]) {
      acc[item.scrapOrderNumber] = {
        scrapOrderNumber: item.scrapOrderNumber,
        scrapDate: item.scrapDate,
        scrapSource: item.scrapSource,
        status: item.status,
        rejectionReason: item.rejectionReason, // 添加驳回原因
        totalQuantity: 0,
        itemCount: 0,
        items: []
      };
    }
    acc[item.scrapOrderNumber].totalQuantity += item.quantity;
    acc[item.scrapOrderNumber].itemCount += 1;
    acc[item.scrapOrderNumber].items.push(item);
    return acc;
  }, {} as Record<string, any>);

  // 转换为数组并排序
  const groupedData = Object.values(groupedByOrderNumber);
  groupedData.sort((a: any, b: any) => new Date(b.scrapDate).getTime() - new Date(a.scrapDate).getTime());

  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const data = groupedData.slice(start, end);

  return { data, total: groupedData.length };
};

// 获取报损单详情
export const fetchScrapOrderDetails = (scrapOrderNumber: string) => {
  const orderItems = mockScrapRecords.filter(item => item.scrapOrderNumber === scrapOrderNumber);
  return orderItems;
};

// 作废报损单
export const voidScrapOrder = (scrapOrderNumber: string) => {
  // 将该报损单号下的所有记录状态改为作废
  mockScrapRecords.forEach(item => {
    if (item.scrapOrderNumber === scrapOrderNumber) {
      item.status = 'voided';
    }
  });
  return { success: true, message: '报损单作废成功' };
};

// 更新报损记录
export const updateScrapRecord = (scrapOrderNumber: string, updateData: Partial<ScrapRecord>) => {
  // 找到该报损单号下的所有记录并更新
  const recordsToUpdate = mockScrapRecords.filter(item => item.scrapOrderNumber === scrapOrderNumber);

  if (recordsToUpdate.length === 0) {
    return { success: false, message: '未找到对应的报损记录' };
  }

  // 更新记录
  recordsToUpdate.forEach(item => {
    Object.assign(item, updateData);
  });

  return { success: true, message: '报损记录更新成功' };
};
