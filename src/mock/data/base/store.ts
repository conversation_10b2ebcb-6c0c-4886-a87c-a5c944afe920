import type {
  StoreSearchParams,
  StorePageResponse,
  StoreItem,
  CreateStoreRequest,
  UpdateStoreRequest,
  ApiResponse
} from '@/types/base/store';

// 动态生成门店Mock数据（25-30条，便于测试分页）
function generateMockStores(): StoreItem[] {
  const storeCount = Math.floor(Math.random() * 6) + 25;
  const mockStores: StoreItem[] = [];

  // 总部
  mockStores.push({
    id: 'hq_perodua',
    storeCode: 'HQ001',
    storeName: 'PERODUA总部',
    storeShortName: 'PERODUA HQ',
    storeType: '02010001',
    storeProperties: ['sales', 'after_sales', 'parts', 'finance'],
    storeStatus: '00020001',
    manager: 'Mr. CEO',
    contactPerson: 'Admin HQ',
    contactPhone: '03-8888-8888',
    continent: '亚洲',
    city: '赛城',
    postalCode: '43900',
    detailAddress: 'Perodua Global Manufacturing Sdn. Bhd.',
    createTime: '2020-01-01T10:00:00Z',
    children: []
  });

  // 生成其他门店
  for (let i = 1; i < storeCount; i++) {
    const storeTypes = ['main', 'branch', 'warehouse'];
    const continents = ['亚洲', '欧洲', '北美洲', '南美洲', '非洲'];
    const cities = ['吉隆坡', '莎阿南', '新山', '乔治市', '怡保'];
    const postalCodes = ['50000', '40000', '80000', '10000', '30000'];

    mockStores.push({
      id: `store_${i.toString().padStart(3, '0')}`,
      storeCode: `ST${i.toString().padStart(3, '0')}`,
      storeName: `${cities[i % cities.length]}${storeTypes[i % storeTypes.length] === 'main' ? '主店' : storeTypes[i % storeTypes.length] === 'branch' ? '分店' : '仓库'}${i}`,
      storeShortName: `${cities[i % cities.length]}${i}`,
      storeType: storeTypes[i % storeTypes.length] as any,
      storeProperties: i % 2 === 0 ? ['sales'] : ['sales', 'after_sales'],
      storeStatus: i % 10 === 0 ? '00020002' : '00020001',
      manager: `经理${i}`,
      contactPerson: `联系人${i}`,
      contactPhone: `03-${(1000 + i).toString()}-${(1000 + i * 2).toString()}`,
      continent: continents[i % continents.length],
      city: cities[i % cities.length],
      postalCode: postalCodes[i % postalCodes.length],
      detailAddress: `${cities[i % cities.length]}街道${i}号`,
      parentId: i % 5 === 0 ? undefined : 'hq_perodua',
      createTime: new Date(2020 + (i % 4), (i % 12), (i % 28) + 1).toISOString(),
      children: []
    });
  }

  return mockStores;
}

const mockStores = generateMockStores();

// 注意：门店状态和门店属性字典现在通过统一的字典系统获取
// 使用 DICTIONARY_TYPES.COMMON_STATUS ('0002') 和 DICTIONARY_TYPES.STORE_PROPERTIES ('0200')

// 构建树形结构
const buildTree = (stores: StoreItem[]): StoreItem[] => {
  const storeMap = new Map(stores.map(store => [store.id, { ...store, children: [] }]));
  const tree: StoreItem[] = [];

  // 找到总部作为根节点
  const root = stores.find(s => s.storeType === '02010001');

  if (root) {
    const rootNode = storeMap.get(root.id)!;
    tree.push(rootNode);

    stores.forEach(store => {
      if (store.id === root.id) return;

      const childNode = storeMap.get(store.id)!;
      if (store.parentId === root.id || !store.parentId) {
        rootNode.children!.push(childNode);
      }
    });

    // 处理多级嵌套
    stores.forEach(store => {
      if (store.parentId && store.parentId !== root.id) {
        const parentNode = storeMap.get(store.parentId);
        if (parentNode) {
          const childNode = storeMap.get(store.id)!;
          if (!parentNode.children!.some(c => c.id === childNode.id)) {
            parentNode.children!.push(childNode);
          }
        }
      }
    });
  } else {
    stores.forEach(store => {
      if (store.parentId) {
        const parent = storeMap.get(store.parentId);
        if (parent) {
          parent.children!.push(storeMap.get(store.id)!);
        } else {
          tree.push(storeMap.get(store.id)!);
        }
      } else {
        tree.push(storeMap.get(store.id)!);
      }
    });
  }

  return tree;
};

// 获取门店列表（支持搜索和分页）
export const getStoreList = (params: StoreSearchParams): Promise<ApiResponse<StorePageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('🔍 Mock API 接收到搜索参数:', params);
      console.log('🔍 搜索条件分析:', {
        '门店名称': params.storeName || '(无)',
        '门店编码': params.storeCode || '(无)',
        '门店状态': params.storeStatus || '(无)',
        '是否有搜索条件': !!(params.storeName || params.storeCode || params.storeStatus)
      });

      let filteredData = [...mockStores];
      const originalCount = filteredData.length;

      // 搜索过滤
      if (params.storeName) {
        console.log('🔍 按门店名称过滤:', params.storeName);
        filteredData = filteredData.filter(store =>
          store.storeName.includes(params.storeName!)
        );
        console.log(`🔍 门店名称过滤后数量: ${filteredData.length}`);
      }

      if (params.storeCode) {
        console.log('🔍 按门店编码过滤:', params.storeCode);
        filteredData = filteredData.filter(store =>
          store.storeCode.includes(params.storeCode!)
        );
        console.log(`🔍 门店编码过滤后数量: ${filteredData.length}`);
      }

      if (params.storeStatus) {
        console.log('🔍 按门店状态过滤:', params.storeStatus);
        filteredData = filteredData.filter(store =>
          store.storeStatus === params.storeStatus
        );
        console.log(`🔍 门店状态过滤后数量: ${filteredData.length}`);
      }

      console.log(`🔍 过滤结果: ${originalCount} → ${filteredData.length}`);

      // 构建树形结构
      const treeData = buildTree(filteredData);

      // MyBatisPlus标准分页处理
      const current = params.current || 1;
      const size = params.size || 10;
      const total = filteredData.length;
      const pages = Math.ceil(total / size);

      resolve({
        code: '200',
        message: '获取成功',
        result: {
          records: treeData, // 返回树形结构数据
          total,
          current,
          size,
          pages
        },
        success: true,
        timestamp: Date.now(),
        traceId: `trace_${Date.now()}`
      });
    }, 500);
  });
};

// 获取门店详情
export const getStoreDetail = (id: string): Promise<ApiResponse<StoreItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const store = mockStores.find(s => s.id === id);
      if (store) {
        resolve({
          code: '200',
          message: '获取成功',
          result: store,
          success: true,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      } else {
        resolve({
          code: 404,
          message: '门店不存在',
          result: {} as StoreItem,
          success: false,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      }
    }, 300);
  });
};

// 新增门店
export const addStore = (data: CreateStoreRequest): Promise<ApiResponse<StoreItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newStore: StoreItem = {
        id: `store_${Date.now()}`,
        ...data,
        createTime: new Date().toISOString(),
        children: []
      };
      mockStores.unshift(newStore);

      resolve({
        code: '200',
        message: '新增成功',
        result: newStore,
        success: true,
        timestamp: Date.now(),
        traceId: `trace_${Date.now()}`
      });
    }, 300);
  });
};

// 更新门店
export const updateStore = (data: UpdateStoreRequest): Promise<ApiResponse<StoreItem>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockStores.findIndex(s => s.id === data.id);
      if (index !== -1) {
        mockStores[index] = { ...mockStores[index], ...data };
        resolve({
          code: '200',
          message: '更新成功',
          result: mockStores[index],
          success: true,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      } else {
        resolve({
          code: 404,
          message: '门店不存在',
          result: {} as StoreItem,
          success: false,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      }
    }, 300);
  });
};

// 删除门店
export const deleteStore = (id: string): Promise<ApiResponse<void>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockStores.findIndex(s => s.id === id);
      if (index !== -1) {
        mockStores.splice(index, 1);
        resolve({
          code: '200',
          message: '删除成功',
          result: undefined as any,
          success: true,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      } else {
        resolve({
          code: 404,
          message: '门店不存在',
          result: undefined as any,
          success: false,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      }
    }, 300);
  });
};

// 注意：门店状态和门店属性字典现在通过统一的字典系统获取
// 不再需要单独的Mock函数
