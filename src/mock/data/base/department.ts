import type {
  Department,
  DepartmentSearchParams,
  CreateDepartmentRequest,
  UpdateDepartmentRequest,
  ApiResponse,
  DepartmentPageResponse
} from '@/types/base/department';

// ✅ 动态生成部门Mock数据（参考门店管理风格）
function generateMockDepartmentData(): Department[] {
  const dataCount = Math.floor(Math.random() * 6) + 25; // 25-30条数据
  const mockData: Department[] = [];

  // ✅ 部门类型：使用数据字典编码（DEPARTMENT_TYPE: 0202）
  const departmentTypes = ['020201', '020202', '020203']; // 对应业务部门、支持部门、管理部门
  // ✅ 部门状态：使用数据字典编码（COMMON_STATUS: 0002）
  const departmentStatuses = ['00020001', '00020002']; // 对应正常、禁用

  const departmentNames = [
    '销售部', '市场部', '技术部', '人事部', '财务部', '运营部',
    '客服部', '产品部', '设计部', '法务部', '行政部', '采购部'
  ];

  for (let i = 0; i < dataCount; i++) {
    const createTime = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000);

    const department: Department = {
      id: `dept_${Date.now()}_${i}`,
      departmentName: departmentNames[i % departmentNames.length] + (i > 11 ? `${Math.floor(i/12) + 1}` : ''),
      departmentCode: `DEPT${String(i + 1).padStart(3, '0')}`,
      // ✅ 使用实际的字符串值
      departmentType: departmentTypes[Math.floor(Math.random() * departmentTypes.length)],
      departmentStatus: departmentStatuses[Math.floor(Math.random() * departmentStatuses.length)],
      departmentHead: `负责人${i + 1}`,
      sortOrder: i + 1,
      description: `${departmentNames[i % departmentNames.length]}的职能描述，负责相关业务的管理和执行`,
      createdAt: createTime.toISOString(),
      parentId: null,
      children: []
    };

    // 为前10个部门随机生成1-3个子部门
    if (i < 10 && Math.random() > 0.6) {
      const childCount = Math.floor(Math.random() * 3) + 1;
      department.children = generateChildDepartments(department.id, department.departmentName, childCount);
    }

    mockData.push(department);
  }

  return mockData;
}

// 生成子部门
function generateChildDepartments(parentId: string, parentName: string, count: number): Department[] {
  const children: Department[] = [];
  const childSuffixes = ['一组', '二组', '三组', '分部', '小组'];
  const departmentTypes = ['020201', '020202', '020203'];
  const departmentStatuses = ['00020001', '00020002'];

  for (let i = 0; i < count; i++) {
    const createTime = new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000);

    children.push({
      id: `dept_child_${Date.now()}_${i}`,
      departmentName: `${parentName}${childSuffixes[i]}`,
      departmentCode: `${parentId.slice(-3)}${String(i + 1).padStart(2, '0')}`,
      departmentType: departmentTypes[Math.floor(Math.random() * departmentTypes.length)],
      departmentStatus: Math.random() > 0.8 ? '00020002' : '00020001',
      departmentHead: `组长${i + 1}`,
      sortOrder: i + 1,
      description: `${parentName}下属${childSuffixes[i]}的职能描述`,
      createdAt: createTime.toISOString(),
      parentId: parentId,
      children: null
    });
  }

  return children;
}

const mockDepartmentData = generateMockDepartmentData();

// ✅ 获取部门分页列表（参考门店管理API响应格式）
export const getDepartmentList = (params: DepartmentSearchParams): Promise<ApiResponse<DepartmentPageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('🔍 Mock数据 - 原始树形数据:', mockDepartmentData);

      // 扁平化处理树形数据用于搜索，但保持完整的字段信息
      const flattenDepartments = (depts: Department[]): Department[] => {
        const result: Department[] = [];
        depts.forEach(dept => {
          // 创建完整的部门副本，确保所有字段都被保留
          const flatDept: Department = {
            id: dept.id,
            departmentName: dept.departmentName,
            departmentCode: dept.departmentCode,
            parentId: dept.parentId,
            departmentType: dept.departmentType,
            departmentStatus: dept.departmentStatus,
            departmentHead: dept.departmentHead,
            sortOrder: dept.sortOrder, // ✅ 确保sortOrder字段被保留
            description: dept.description,
            createdAt: dept.createdAt,
            children: dept.children // 保持children用于后续树形重建
          };
          result.push(flatDept);

          if (dept.children && dept.children.length > 0) {
            result.push(...flattenDepartments(dept.children));
          }
        });
        return result;
      };

      let filteredData = flattenDepartments(mockDepartmentData);
      console.log('🔍 Mock数据 - 扁平化后的数据:', filteredData);
      console.log('🔍 Mock数据 - 第一条记录的sortOrder:', filteredData[0]?.sortOrder);

      // ✅ 搜索过滤逻辑（参考门店管理）
      if (params.departmentName) {
        filteredData = filteredData.filter(dept =>
          dept.departmentName.includes(params.departmentName!)
        );
      }

      if (params.departmentCode) {
        filteredData = filteredData.filter(dept =>
          dept.departmentCode.includes(params.departmentCode!)
        );
      }

      if (params.departmentStatus) {
        filteredData = filteredData.filter(dept =>
          dept.departmentStatus === params.departmentStatus
        );
      }

      if (params.departmentType) {
        filteredData = filteredData.filter(dept =>
          dept.departmentType === params.departmentType
        );
      }

      // ✅ 分页处理（MyBatisPlus标准）
      const current = params.current || 1;
      const size = params.size || 10;
      const total = filteredData.length;
      const pages = Math.ceil(total / size);
      const start = (current - 1) * size;
      const end = start + size;
      const records = filteredData.slice(start, end);
      console.log('🔍 Mock数据 - 最终返回的records:', records);
      console.log('🔍 Mock数据 - 第一条记录的完整信息:', records[0]);

      // ✅ 返回与门店管理一致的响应格式
      resolve({
        code: '200',
        message: 'Success',
        result: {
          records,
          current,
          size,
          total,
          pages
        },
        success: true,
        timestamp: Date.now(),
        traceId: `trace_${Date.now()}`
      });
    }, 300); // 模拟网络延迟
  });
};

// ✅ 获取部门详情
export const getDepartmentDetail = (id: string): Promise<ApiResponse<Department>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const findDepartment = (depts: Department[]): Department | null => {
        for (const dept of depts) {
          if (dept.id === id) {
            return dept;
          }
          if (dept.children) {
            const found = findDepartment(dept.children);
            if (found) return found;
          }
        }
        return null;
      };

      const department = findDepartment(mockDepartmentData);

      if (department) {
        resolve({
          code: '200',
          message: 'Success',
          result: department,
          success: true,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      } else {
        resolve({
          code: '404',
          message: 'Department not found',
          result: {} as Department,
          success: false,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      }
    }, 200);
  });
};

// ✅ 新增部门
export const addDepartment = (data: CreateDepartmentRequest): Promise<ApiResponse<Department>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newDepartment: Department = {
        ...data,
        id: `dept_${Date.now()}`,
        createTime: new Date().toISOString(),
        children: []
      };

      if (data.parentId) {
        // 查找父部门并添加到其children中
        const findAndAddToParent = (depts: Department[]): boolean => {
          for (const dept of depts) {
            if (dept.id === data.parentId) {
              dept.children = dept.children || [];
              dept.children.push(newDepartment);
              return true;
            }
            if (dept.children && findAndAddToParent(dept.children)) {
              return true;
            }
          }
          return false;
        };
        findAndAddToParent(mockDepartmentData);
      } else {
        mockDepartmentData.push(newDepartment);
      }

      resolve({
        code: '200',
        message: 'Success',
        result: newDepartment,
        success: true,
        timestamp: Date.now(),
        traceId: `trace_${Date.now()}`
      });
    }, 400);
  });
};

// ✅ 更新部门
export const updateDepartment = (data: UpdateDepartmentRequest): Promise<ApiResponse<Department>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('🔍 Mock更新 - 接收到的数据:', data);
      console.log('🔍 Mock更新 - sortOrder值:', data.sortOrder);

      const findAndUpdate = (depts: Department[]): Department | null => {
        for (const dept of depts) {
          if (dept.id === data.id) {
            console.log('🔍 Mock更新 - 找到要更新的部门:', dept);
            console.log('🔍 Mock更新 - 更新前的sortOrder:', dept.sortOrder);

            Object.assign(dept, {
              ...data,
              createdAt: dept.createdAt // 保持原创建时间
            });

            console.log('🔍 Mock更新 - 更新后的部门:', dept);
            console.log('🔍 Mock更新 - 更新后的sortOrder:', dept.sortOrder);
            return dept;
          }
          if (dept.children) {
            const found = findAndUpdate(dept.children);
            if (found) return found;
          }
        }
        return null;
      };

      const updatedDept = findAndUpdate(mockDepartmentData);

      if (updatedDept) {
        resolve({
          code: '200',
          message: 'Success',
          result: updatedDept,
          success: true,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      } else {
        resolve({
          code: '404',
          message: 'Department not found',
          result: {} as Department,
          success: false,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      }
    }, 400);
  });
};

// ✅ 删除部门
export const deleteDepartment = (id: string): Promise<ApiResponse<void>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const findAndDelete = (depts: Department[], parentArray: Department[]): boolean => {
        for (let i = 0; i < depts.length; i++) {
          if (depts[i].id === id) {
            // 检查是否有子部门
            if (depts[i].children && depts[i].children.length > 0) {
              resolve({
                code: '400',
                message: '该部门下还有子部门，无法删除',
                result: undefined,
                success: false,
                timestamp: Date.now(),
                traceId: `trace_${Date.now()}`
              });
              return true;
            }
            parentArray.splice(i, 1);
            return true;
          }
          if (depts[i].children && findAndDelete(depts[i].children, depts[i].children)) {
            return true;
          }
        }
        return false;
      };

      if (findAndDelete(mockDepartmentData, mockDepartmentData)) {
        resolve({
          code: '200',
          message: 'Success',
          result: undefined,
          success: true,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      } else {
        resolve({
          code: '404',
          message: 'Department not found',
          result: undefined,
          success: false,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      }
    }, 300);
  });
};
