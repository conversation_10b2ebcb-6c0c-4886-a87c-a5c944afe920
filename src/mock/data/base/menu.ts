/**
 * 菜单管理Mock数据
 */

import type { Menu, MenuSearchParams, MenuPageResponse, CreateMenuRequest, UpdateMenuRequest, ApiResponse } from '@/types/base/menu';

// 生成Mock菜单数据
const generateMockMenuData = (): Menu[] => {
  const menus: Menu[] = [
    // 权限管理
    {
      id: 1,
      menuName: '权限管理',
      menuCode: 'FACTORY_PERMISSION',
      parentId: null,
      menuType: '02030001', // 目录
      menuStatus: '00020001', // 正常
      menuIcon: '',
      menuPath: '',
      component: '',
      sortOrder: 0,
      isVisible: 1,
      permission: '',
      menuSide: 'factory',
      isCache: 0,
      createdAt: '2025-07-03 15:32:53',
      children: [
        {
          id: 2,
          menuName: '系统管理',
          menuCode: 'FACTORY_SYSTEM',
          parentId: 1,
          menuType: 'directory',
          menuStatus: 'normal',
          menuIcon: 'el-icon-setting',
          menuPath: '/system',
          component: null,
          sortOrder: 10,
          isVisible: 1,
          permission: 'system:main',
          menuSide: 'factory',
          isCache: 0,
          createdAt: '2025-06-30 16:35:40',
          children: [
            {
              id: 16,
              menuName: '门店管理',
              menuCode: '门店管理',
              parentId: 2,
              menuType: 'menu',
              menuStatus: 'normal',
              menuIcon: 'el-icon-office-building',
              menuPath: '/base/store',
              component: '',
              sortOrder: 0,
              isVisible: 1,
              permission: 'system:department:view',
              menuSide: 'factory',
              isCache: 1,
              createdAt: '2025-06-30 16:35:40',
              children: [
                {
                  id: 17,
                  menuName: '新增门店',
                  menuCode: 'FACTORY_STORE_CREATE',
                  parentId: 16,
                  menuType: 'button',
                  menuStatus: 'normal',
                  menuIcon: null,
                  menuPath: null,
                  component: null,
                  sortOrder: 0,
                  isVisible: 1,
                  permission: 'system:store:create',
                  menuSide: 'factory',
                  isCache: 0,
                  createdAt: '2025-06-30 16:35:40',
                  children: null
                },
                {
                  id: 18,
                  menuName: '编辑门店',
                  menuCode: 'FACTORY_STORE_UPDATE',
                  parentId: 16,
                  menuType: 'button',
                  menuStatus: 'normal',
                  menuIcon: null,
                  menuPath: null,
                  component: null,
                  sortOrder: 0,
                  isVisible: 1,
                  permission: 'system:store:update',
                  menuSide: 'factory',
                  isCache: 0,
                  createdAt: '2025-06-30 16:35:40',
                  children: null
                }
              ]
            },
            {
              id: 20,
              menuName: '部门管理',
              menuCode: '部门管理',
              parentId: 2,
              menuType: 'menu',
              menuStatus: 'normal',
              menuIcon: 'el-icon-s-operation',
              menuPath: '/base/department',
              component: '',
              sortOrder: 0,
              isVisible: 1,
              permission: 'system:department:view',
              menuSide: 'factory',
              isCache: 1,
              createdAt: '2025-06-30 16:35:40',
              children: [
                {
                  id: 21,
                  menuName: '新增部门',
                  menuCode: 'FACTORY_DEPT_CREATE',
                  parentId: 20,
                  menuType: 'button',
                  menuStatus: 'normal',
                  menuIcon: null,
                  menuPath: null,
                  component: null,
                  sortOrder: 0,
                  isVisible: 1,
                  permission: 'system:department:create',
                  menuSide: 'factory',
                  isCache: 0,
                  createdAt: '2025-06-30 16:35:40',
                  children: null
                },
                {
                  id: 22,
                  menuName: '编辑部门',
                  menuCode: 'FACTORY_DEPT_UPDATE',
                  parentId: 20,
                  menuType: 'button',
                  menuStatus: 'normal',
                  menuIcon: null,
                  menuPath: null,
                  component: null,
                  sortOrder: 0,
                  isVisible: 0,
                  permission: 'system:department:update',
                  menuSide: 'factory',
                  isCache: 0,
                  createdAt: '2025-06-30 16:35:40',
                  children: null
                }
              ]
            }
          ]
        }
      ]
    }
  ];

  return menus;
};

const mockMenuData = generateMockMenuData();

// 扁平化菜单数据用于搜索
const flattenMenus = (menus: Menu[]): Menu[] => {
  const result: Menu[] = [];
  const addedIds = new Set<string | number>();

  const traverse = (menuList: Menu[]) => {
    menuList.forEach(menu => {
      if (!addedIds.has(menu.id)) {
        // 创建扁平化的菜单对象
        result.push({
          id: menu.id,
          menuName: menu.menuName,
          menuCode: menu.menuCode,
          parentId: menu.parentId,
          menuType: menu.menuType,
          menuStatus: menu.menuStatus,
          menuIcon: menu.menuIcon,
          menuPath: menu.menuPath,
          component: menu.component,
          sortOrder: menu.sortOrder,
          isVisible: menu.isVisible,
          permission: menu.permission,
          menuSide: menu.menuSide,
          isCache: menu.isCache,
          createdAt: menu.createdAt,
          children: menu.children // 保持children用于后续树形重建
        });
        addedIds.add(menu.id);
      }

      if (menu.children && menu.children.length > 0) {
        traverse(menu.children);
      }
    });
  };

  traverse(menus);
  return result;
};

// 获取菜单分页列表
export const getMenuList = (params: MenuSearchParams): Promise<ApiResponse<MenuPageResponse>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 直接返回树形结构数据，不进行扁平化
      let filteredData = [...mockMenuData];

      // 字典值映射 - 现在Mock数据已经使用字典编码，直接比较即可
      const matchesStatus = (menuStatus: string, searchStatus: string): boolean => {
        if (!searchStatus) return true;
        return menuStatus === searchStatus;
      };

      const matchesType = (menuType: string, searchType: string): boolean => {
        if (!searchType) return true;
        return menuType === searchType;
      };

      // 递归搜索过滤
      const filterTreeData = (data: Menu[]): Menu[] => {
        return data.filter(menu => {
          let match = true;

          // 菜单名称过滤
          if (params.menuName && !menu.menuName.includes(params.menuName)) {
            match = false;
          }

          // 菜单状态过滤
          if (!matchesStatus(menu.menuStatus, params.menuStatus)) {
            match = false;
          }

          // 菜单类型过滤
          if (!matchesType(menu.menuType, params.menuType)) {
            match = false;
          }

          // 如果当前节点匹配，保留整个子树
          if (match) {
            return true;
          }

          // 如果当前节点不匹配，检查子节点
          if (menu.children && menu.children.length > 0) {
            const filteredChildren = filterTreeData(menu.children);
            if (filteredChildren.length > 0) {
              menu.children = filteredChildren;
              return true;
            }
          }

          return false;
        });
      };

      if (params.menuName || params.menuStatus || params.menuType) {
        filteredData = filterTreeData(filteredData);
      }

      // 计算总数（包括所有子节点）
      const countAllNodes = (data: Menu[]): number => {
        let count = 0;
        data.forEach(menu => {
          count++;
          if (menu.children && menu.children.length > 0) {
            count += countAllNodes(menu.children);
          }
        });
        return count;
      };

      const total = countAllNodes(filteredData);
      const current = params.current || 1;
      const size = params.size || 10;
      const pages = Math.ceil(total / size);

      // 对于树形数据，通常不进行分页，返回所有数据
      const records = filteredData;

      resolve({
        code: '200',
        message: 'Success',
        result: {
          records,
          current,
          size,
          total,
          pages
        },
        success: true,
        timestamp: Date.now(),
        traceId: `trace_${Date.now()}`
      });
    }, 300);
  });
};

// 获取菜单树
export const getMenuTree = (): Promise<ApiResponse<Menu[]>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '200',
        message: 'Success',
        result: mockMenuData,
        success: true,
        timestamp: Date.now(),
        traceId: `trace_${Date.now()}`
      });
    }, 200);
  });
};

// 获取菜单详情
export const getMenuDetail = (id: string): Promise<ApiResponse<Menu>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const findMenu = (menus: Menu[]): Menu | null => {
        for (const menu of menus) {
          if (menu.id === id) {
            return menu;
          }
          if (menu.children) {
            const found = findMenu(menu.children);
            if (found) return found;
          }
        }
        return null;
      };
      
      const menu = findMenu(mockMenuData);
      
      if (menu) {
        resolve({
          code: '200',
          message: 'Success',
          result: menu,
          success: true,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      } else {
        resolve({
          code: '404',
          message: '菜单不存在',
          result: null as any,
          success: false,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      }
    }, 200);
  });
};

// 新增菜单
export const addMenu = (data: CreateMenuRequest): Promise<ApiResponse<Menu>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newMenu: Menu = {
        id: `menu_${Date.now()}`,
        ...data,
        createdAt: new Date().toISOString(),
        children: null
      };
      
      // 添加到Mock数据中
      if (data.parentId) {
        const findAndAdd = (menus: Menu[]): boolean => {
          for (const menu of menus) {
            if (menu.id === data.parentId) {
              if (!menu.children) {
                menu.children = [];
              }
              menu.children.push(newMenu);
              return true;
            }
            if (menu.children && findAndAdd(menu.children)) {
              return true;
            }
          }
          return false;
        };
        findAndAdd(mockMenuData);
      } else {
        mockMenuData.push(newMenu);
      }
      
      resolve({
        code: '200',
        message: 'Success',
        result: newMenu,
        success: true,
        timestamp: Date.now(),
        traceId: `trace_${Date.now()}`
      });
    }, 400);
  });
};

// 更新菜单
export const updateMenu = (data: UpdateMenuRequest): Promise<ApiResponse<Menu>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const findAndUpdate = (menus: Menu[]): Menu | null => {
        for (const menu of menus) {
          if (menu.id === data.id) {
            Object.assign(menu, {
              ...data,
              createdAt: menu.createdAt // 保持原创建时间
            });
            return menu;
          }
          if (menu.children) {
            const found = findAndUpdate(menu.children);
            if (found) return found;
          }
        }
        return null;
      };
      
      const updatedMenu = findAndUpdate(mockMenuData);
      
      if (updatedMenu) {
        resolve({
          code: '200',
          message: 'Success',
          result: updatedMenu,
          success: true,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      } else {
        resolve({
          code: '404',
          message: '菜单不存在',
          result: null as any,
          success: false,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      }
    }, 400);
  });
};

// 删除菜单
export const deleteMenu = (id: string): Promise<ApiResponse<null>> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const findAndDelete = (menus: Menu[], parentArray: Menu[]): boolean => {
        for (let i = 0; i < menus.length; i++) {
          if (menus[i].id === id) {
            parentArray.splice(i, 1);
            return true;
          }
          if (menus[i].children && findAndDelete(menus[i].children!, menus[i].children!)) {
            return true;
          }
        }
        return false;
      };
      
      const deleted = findAndDelete(mockMenuData, mockMenuData);
      
      if (deleted) {
        resolve({
          code: '200',
          message: 'Success',
          result: null,
          success: true,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      } else {
        resolve({
          code: '404',
          message: '菜单不存在',
          result: null,
          success: false,
          timestamp: Date.now(),
          traceId: `trace_${Date.now()}`
        });
      }
    }, 300);
  });
};
