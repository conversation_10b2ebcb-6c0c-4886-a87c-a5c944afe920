// 经销商端采购管理Mock数据

import type {
  CreatePurchaseOrderRequest,
  DealerDashboardStats,
  PartForSelection,
  PartType,
  PurchaseOrder,
  PurchaseOrderItem,
  PurchaseOrderListQuery,
  PurchaseOrderStatus,
  ShipmentOrder,
  SubmitOrderResponse,
  UpdatePurchaseOrderRequest
} from '@/types/parts/purchase-dealer';

// 随机生成工具函数
const randomInt = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const randomFloat = (min: number, max: number, decimals: number = 2): number => {
  return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
};

const randomDate = (daysAgo: number = 30): string => {
  const date = new Date();
  date.setDate(date.getDate() - randomInt(0, daysAgo));
  return date.toISOString().split('T')[0] + ' ' + 
         String(randomInt(8, 18)).padStart(2, '0') + ':' +
         String(randomInt(0, 59)).padStart(2, '0') + ':00';
};

const randomOrderNo = (prefix: string = 'JL'): string => {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const random = String(randomInt(1, 999)).padStart(3, '0');
  return `${prefix}${date}${random}`;
};

const getRandomRemark = (): string => {
  const remarks = [
    '部分配件急用，希望能优先处理',
    '常规补货申请',
    '紧急维修需要',
    '客户预定配件',
    '库存不足，需要补充',
    '季度采购计划',
    '促销活动备货',
    ''
  ];
  return remarks[randomInt(0, remarks.length - 1)];
};

const getRandomRejectionReason = (): string => {
  const rejectionReasons = [
    '申请配件数量过多，超出月度预算限制',
    '部分配件非紧急需要，建议下季度采购',
    '申请配件规格与实际需要不符，请重新确认',
    '当前库存充足，暂不需要补货',
    '申请备注信息不完整，请补充详细说明'
  ];
  return rejectionReasons[randomInt(0, rejectionReasons.length - 1)];
};

// 辅助函数定义
const getPartCategory = (partName: string): string => {
  const categories = {
    '发动机配件': ['火花塞', '机油滤清器', '空气滤清器', '汽油滤清器', '传动带'],
    '制动系统': ['刹车片前', '刹车盘前'],
    '悬挂系统': ['减震器前'],
    '电气系统': ['车灯泡', '保险丝', '蓄电池'],
    '车身附件': ['雨刮片'],
    '轮胎轮毂': ['轮胎'],
    '润滑系统': ['机油', '防冻液', '玻璃水']
  };
  
  for (const [category, parts] of Object.entries(categories)) {
    if (parts.some(part => partName.includes(part))) {
      return category;
    }
  }
  return '其他配件';
};

const getStockStatus = (): 'NORMAL' | 'WARNING' | 'SHORTAGE' => {
  const statuses: ('NORMAL' | 'WARNING' | 'SHORTAGE')[] = ['NORMAL', 'WARNING', 'SHORTAGE'];
  return statuses[randomInt(0, 2)];
};

const generateTrackingNumber = (): string => {
  const prefixes = ['SF', 'YT', 'ST', 'ZT'];
  const prefix = prefixes[randomInt(0, prefixes.length - 1)];
  const number = String(randomInt(100000000, 999999999));
  return `${prefix}${number}`;
};

const getShipmentStatus = (orderStatus: PurchaseOrderStatus): 'SHIPPED' | 'IN_TRANSIT' | 'DELIVERED' | 'RECEIVED' => {
  switch (orderStatus) {
    case 'shipped':
    case 'partiallyShipped':
      return 'IN_TRANSIT';
    case 'partiallyReceived':
      return 'DELIVERED';
    case 'received':
      return 'RECEIVED';
    default:
      return 'SHIPPED';
  }
};

// 零件基础数据池
const partDatabase = [
  // 原厂零件
  { code: 'P10001', name: '火花塞', type: 'ORIGINAL' as PartType, brand: 'NGK', price: randomFloat(35, 55), unit: '个' },
  { code: 'P10002', name: '机油滤清器', type: 'ORIGINAL' as PartType, brand: '博世', price: randomFloat(70, 90), unit: '个' },
  { code: 'P10003', name: '刹车片前', type: 'ORIGINAL' as PartType, brand: '布雷博', price: randomFloat(180, 220), unit: '套' },
  { code: 'P10004', name: '空气滤清器', type: 'ORIGINAL' as PartType, brand: '马勒', price: randomFloat(45, 65), unit: '个' },
  { code: 'P10005', name: '汽油滤清器', type: 'ORIGINAL' as PartType, brand: '博世', price: randomFloat(85, 105), unit: '个' },
  { code: 'P10006', name: '刹车盘前', type: 'ORIGINAL' as PartType, brand: '布雷博', price: randomFloat(350, 450), unit: '个' },
  { code: 'P10007', name: '传动带', type: 'ORIGINAL' as PartType, brand: '盖茨', price: randomFloat(120, 160), unit: '条' },
  { code: 'P10008', name: '减震器前', type: 'ORIGINAL' as PartType, brand: '倍适登', price: randomFloat(280, 350), unit: '个' },
  
  // 非原厂零件
  { code: 'NP2001', name: '雨刮片', type: 'NON_ORIGINAL' as PartType, brand: '博世', price: randomFloat(75, 95), unit: '对' },
  { code: 'NP2002', name: '车灯泡', type: 'NON_ORIGINAL' as PartType, brand: '飞利浦', price: randomFloat(25, 45), unit: '个' },
  { code: 'NP2003', name: '保险丝', type: 'NON_ORIGINAL' as PartType, brand: '立特', price: randomFloat(5, 15), unit: '个' },
  { code: 'NP2004', name: '玻璃水', type: 'NON_ORIGINAL' as PartType, brand: '3M', price: randomFloat(15, 25), unit: '瓶' },
  { code: 'NP2005', name: '轮胎', type: 'NON_ORIGINAL' as PartType, brand: '米其林', price: randomFloat(450, 650), unit: '条' },
  { code: 'NP2006', name: '蓄电池', type: 'NON_ORIGINAL' as PartType, brand: '瓦尔塔', price: randomFloat(380, 520), unit: '个' },
  { code: 'NP2007', name: '机油', type: 'NON_ORIGINAL' as PartType, brand: '美孚', price: randomFloat(280, 350), unit: '桶' },
  { code: 'NP2008', name: '防冻液', type: 'NON_ORIGINAL' as PartType, brand: '壳牌', price: randomFloat(80, 120), unit: '桶' }
];

// 生成随机采购订单明细
const generateRandomOrderItems = (count: number = randomInt(1, 5)): PurchaseOrderItem[] => {
  const selectedParts = partDatabase
    .sort(() => 0.5 - Math.random())
    .slice(0, count);
  
  return selectedParts.map((part, index) => {
    const quantity = randomInt(1, 100);
    const unitPrice = randomFloat(part.price * 0.8, part.price * 1.2);
    return {
      id: `item_${Date.now()}_${index}`,
      partId: part.code,
      partCode: part.code,
      partName: part.name,
      partType: part.type,
      brand: part.brand,
      specification: `规格${randomInt(100, 999)}`,
      unit: part.unit,
      unitPrice: unitPrice,
      quantity: quantity,
      shippedQuantity: 0,
      receivedQuantity: 0,
      amount: parseFloat((quantity * unitPrice).toFixed(2)),
      currentStock: randomInt(0, 50),
      safetyStock: randomInt(5, 20)
    };
  });
};

// 生成随机采购订单
const generateRandomPurchaseOrder = (status: PurchaseOrderStatus, id: string): PurchaseOrder => {
  const items = generateRandomOrderItems();
  const totalAmount = items.reduce((sum, item) => sum + item.amount, 0);
  const createTime = randomDate(30);
  
  // 生成期望到货日期（创建日期后3-10天）
  const expectedDate = new Date(createTime);
  expectedDate.setDate(expectedDate.getDate() + randomInt(3, 10));
  const expectedDeliveryDate = expectedDate.toISOString().split('T')[0];
  
  const order: PurchaseOrder = {
    id,
    orderNo: randomOrderNo(),
    dealerId: 2001,
    dealerName: 'A经销商-北京总店',
    dealerStoreInfo: {
      dealerId: 'DEALER001',
      dealerName: 'A经销商',
      storeName: '北京总店',
      storeCode: 'BJ001',
      region: '华北区',
      contactPerson: '张经理',
      contactPhone: '138-xxxx-xxxx'
    },
    warehouseId: 3001,
    warehouseName: '主仓库',
    expectedDeliveryDate,
    status,
    totalAmount: parseFloat(totalAmount.toFixed(2)),
    itemCount: items.length,
    remark: getRandomRemark(),
    rejectionReason: status === 'rejected' ? getRandomRejectionReason() : undefined,
    createTime,
    items
  };

  // 根据状态设置发货和收货数量
  if (['shipped', 'partiallyShipped', 'partiallyReceived', 'received'].includes(status)) {
    const shipDate = new Date(createTime);
    shipDate.setDate(shipDate.getDate() + randomInt(1, 3));
    order.shipTime = shipDate.toISOString().split('T')[0] + ' ' + 
                     String(randomInt(9, 17)).padStart(2, '0') + ':' +
                     String(randomInt(0, 59)).padStart(2, '0') + ':00';
    
    items.forEach(item => {
      if (status === 'shipped' || status === 'received') {
        // 全部发货
        item.shippedQuantity = item.quantity;
        if (status === 'received') {
          item.receivedQuantity = item.quantity;
        }
      } else if (status === 'partiallyShipped') {
        // 部分发货
        item.shippedQuantity = randomInt(0, item.quantity);
      } else if (status === 'partiallyReceived') {
        // 部分收货
        item.shippedQuantity = item.quantity;
        item.receivedQuantity = randomInt(1, item.quantity - 1);
      }
    });
  }

  return order;
};

// 动态生成采购订单列表
const generateMockDealerOrders = (): PurchaseOrder[] => {
  const orders: PurchaseOrder[] = [];
  const statuses: PurchaseOrderStatus[] = [
    'draft', 'submitted', 'approved', 'rejected', 
    'shipped', 'partiallyShipped', 'partiallyReceived', 'received', 'cancelled'
  ];
  
  // 确保每种状态至少有一个订单
  statuses.forEach((status, index) => {
    orders.push(generateRandomPurchaseOrder(status, `order_${1000 + index}`));
  });
  
  // 添加更多随机订单
  for (let i = 0; i < 20; i++) {
    const randomStatus = statuses[randomInt(0, statuses.length - 1)];
    orders.push(generateRandomPurchaseOrder(randomStatus, `order_${2000 + i}`));
  }
  
  // 按创建时间倒序排列
  return orders.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
};

// 生成仪表盘统计数据
const generateDashboardStats = (orders: PurchaseOrder[]): DealerDashboardStats => {
  const pendingApprovalCount = orders.filter(o => o.status === 'submitted').length;
  const inTransitCount = orders.filter(o => ['shipped', 'partiallyShipped'].includes(o.status)).length;
  const pendingReceiptCount = orders.filter(o => ['shipped', 'partiallyShipped', 'partiallyReceived'].includes(o.status)).length;
  
  // 计算本月申请总额
  const currentMonth = new Date().getMonth();
  const monthlyTotalAmount = orders
    .filter(o => {
      const orderMonth = new Date(o.createTime).getMonth();
      return orderMonth === currentMonth && o.status !== 'cancelled';
    })
    .reduce((sum, o) => sum + o.totalAmount, 0);
  
  return {
    pendingApprovalCount,
    inTransitCount,
    pendingReceiptCount,
    monthlyTotalAmount: parseFloat(monthlyTotalAmount.toFixed(2))
  };
};

// 配件选择数据
const mockPartsForSelection: PartForSelection[] = partDatabase.map(part => ({
  partId: part.code,
  partCode: part.code,
  partName: part.name,
  partType: part.type,
  brand: part.brand,
  specification: `规格${randomInt(100, 999)}`,
  unit: part.unit,
  purchasePrice: part.price,
  currentStock: randomInt(0, 100),
  availableStock: randomInt(0, 80),
  occupiedStock: randomInt(0, 20),
  safetyStock: randomInt(5, 30),
  category: getPartCategory(part.name),
  stockStatus: getStockStatus()
}));

// 生成全局数据
export const mockDealerOrders = generateMockDealerOrders();
export const mockDealerDashboard = generateDashboardStats(mockDealerOrders);

// 导出配件选择数据
export { mockPartsForSelection };

// 发货单信息 - 延迟初始化
let _mockShipmentOrders: ShipmentOrder[] | null = null;

export const mockShipmentOrders: ShipmentOrder[] = _mockShipmentOrders || (() => {
  _mockShipmentOrders = mockDealerOrders
    .filter(order => ['shipped', 'partiallyShipped', 'partiallyReceived', 'received'].includes(order.status))
    .map((order, index) => ({
      shipmentId: 500 + index,
      shipmentNo: `SH${order.orderNo.slice(2)}`,
      purchaseOrderId: parseInt(order.id.replace('order_', '')),
      shippingDate: order.shipTime || order.createTime,
      carrier: ['顺丰速运', '圆通快递', '申通快递', '中通快递'][randomInt(0, 3)],
      trackingNumber: generateTrackingNumber(),
      remarks: order.remark || '',
      status: getShipmentStatus(order.status),
      items: order.items.map((item, itemIndex) => ({
        itemId: 600 + index * 10 + itemIndex,
        shipmentOrderId: 500 + index,
        purchaseOrderItemId: parseInt(item.id.split('_')[1]) || 0,
        partId: item.partId,
        partCode: item.partCode,
        partName: item.partName,
        unit: item.unit,
        orderedQuantity: item.quantity,
        shippedQuantity: item.shippedQuantity || 0,
        receivedQuantity: item.receivedQuantity || 0
      }))
    }));
  return _mockShipmentOrders;
})();

// API Mock 函数
export const getMockDealerOrderList = async (params: PurchaseOrderListQuery) => {
  await new Promise(resolve => setTimeout(resolve, 300)); // 模拟网络延迟
  
  let filteredOrders = [...mockDealerOrders];
  
  // 状态筛选
  if (params.status) {
    filteredOrders = filteredOrders.filter(order => order.status === params.status);
  }
  
  // 订单号搜索
  if (params.orderNo) {
    filteredOrders = filteredOrders.filter(order => 
      order.orderNo.toLowerCase().includes(params.orderNo!.toLowerCase())
    );
  }
  
  // 分页
  const total = filteredOrders.length;
  const page = params.page || 1;
  const size = params.size || 20;
  const start = (page - 1) * size;
  const end = start + size;
  const list = filteredOrders.slice(start, end);
  
  return {
    list,
    total,
    page,
    size
  };
};

export const getMockDealerOrderDetail = async (id: string) => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const order = mockDealerOrders.find(o => o.id === id);
  if (!order) {
    throw new Error('订单不存在');
  }
  
  return order;
};

export const getMockPartsForSelection = async (params?: { keyword?: string; partType?: 'ORIGINAL' | 'NON_ORIGINAL'; }) => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  let filteredParts = [...mockPartsForSelection];
  
  if (params?.partType) {
    filteredParts = filteredParts.filter(part => part.partType === params.partType);
  }
  
  if (params?.keyword) {
    const keyword = params.keyword.toLowerCase();
    filteredParts = filteredParts.filter(part => 
      part.partCode.toLowerCase().includes(keyword) ||
      part.partName.toLowerCase().includes(keyword)
    );
  }
  
  return filteredParts;
};

export const getMockCreatePurchaseOrder = async (data: CreatePurchaseOrderRequest) => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const newOrder: PurchaseOrder = {
    id: `order_${Date.now()}`,
    orderNo: randomOrderNo(),
    dealerId: 2001,
    dealerName: 'A经销商-北京总店',
    dealerStoreInfo: {
      dealerId: 'DEALER001',
      dealerName: 'A经销商',
      storeName: '北京总店',
      storeCode: 'BJ001',
      region: '华北区',
      contactPerson: '张经理',
      contactPhone: '138-xxxx-xxxx'
    },
    warehouseId: data.warehouseId,
    warehouseName: '主仓库',
    expectedDeliveryDate: data.expectedDeliveryDate,
    status: 'draft',
    totalAmount: data.items.reduce((sum, item) => sum + (item.amount || item.quantity * item.unitPrice), 0),
    itemCount: data.items.length,
    remark: data.remark,
    createTime: new Date().toISOString().replace('T', ' ').slice(0, 19),
    items: data.items.map((item, index) => ({
      id: `item_${Date.now()}_${index}`,
      partId: item.partId,
      partCode: item.partCode,
      partName: item.partName,
      partType: item.partType,
      brand: item.brand || '',
      specification: item.specification || `规格${randomInt(100, 999)}`,
      unit: item.unit,
      unitPrice: item.unitPrice,
      quantity: item.quantity,
      shippedQuantity: 0,
      receivedQuantity: 0,
      amount: item.amount || parseFloat((item.quantity * item.unitPrice).toFixed(2)),
      currentStock: randomInt(0, 50),
      safetyStock: randomInt(5, 20)
    }))
  };
  
  mockDealerOrders.unshift(newOrder);
  return newOrder;
};

export const getMockUpdatePurchaseOrder = async (id: string, data: UpdatePurchaseOrderRequest) => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const orderIndex = mockDealerOrders.findIndex(o => o.id === id);
  if (orderIndex === -1) {
    throw new Error('订单不存在');
  }
  
  const currentOrder = mockDealerOrders[orderIndex];
  const updatedOrder: PurchaseOrder = {
    ...currentOrder,
    warehouseId: data.warehouseId || currentOrder.warehouseId,
    warehouseName: '主仓库',
    remark: data.remark !== undefined ? data.remark : currentOrder.remark,
    updateTime: new Date().toISOString().replace('T', ' ').slice(0, 19)
  };

  if (data.items) {
    const updatedItems = data.items.map((item, index) => ({
      id: item.id || `item_${Date.now()}_${index}`,
      partId: item.partId,
      partCode: item.partCode,
      partName: item.partName,
      partType: item.partType,
      brand: item.brand || '',
      specification: item.specification || `规格${randomInt(100, 999)}`,
      unit: item.unit,
      unitPrice: item.unitPrice,
      quantity: item.quantity,
      shippedQuantity: 0,
      receivedQuantity: 0,
      amount: item.amount || parseFloat((item.quantity * item.unitPrice).toFixed(2)),
      currentStock: randomInt(0, 50),
      safetyStock: randomInt(5, 20)
    }));
    
    updatedOrder.items = updatedItems;
    updatedOrder.totalAmount = updatedItems.reduce((sum, item) => sum + item.amount, 0);
    updatedOrder.itemCount = updatedItems.length;
  }
  
  mockDealerOrders[orderIndex] = updatedOrder;
  return updatedOrder;
};

export const getMockSubmitPurchaseOrder = async (id: string): Promise<SubmitOrderResponse> => {
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const order = mockDealerOrders.find(o => o.id === id);
  if (!order) {
    throw new Error('订单不存在');
  }
  
  if (order.status !== 'draft') {
    throw new Error('只有草稿状态的订单才能提交');
  }
  
  // 检查是否有混合类型的零件
  const originalParts = order.items.filter(item => item.partType === 'ORIGINAL');
  const nonOriginalParts = order.items.filter(item => item.partType === 'NON_ORIGINAL');
  
  const result: SubmitOrderResponse = {
    message: '',
    data: []
  };
  
  if (originalParts.length > 0 && nonOriginalParts.length > 0) {
    // 拆分订单
    result.message = '订单已根据零件类型自动拆分为2个独立订单';
    
    // 原厂零件订单
    const originalOrder: PurchaseOrder = {
      ...order,
      id: `order_${Date.now()}_original`,
      orderNo: randomOrderNo('JL'),
      status: 'submitted',
      items: originalParts,
      totalAmount: originalParts.reduce((sum, item) => sum + item.amount, 0),
      itemCount: originalParts.length
    };
    
    // 非原厂零件订单
    const nonOriginalOrder: PurchaseOrder = {
      ...order,
      id: `order_${Date.now()}_non_original`,
      orderNo: randomOrderNo('JL'),
      status: 'submitted',
      items: nonOriginalParts,
      totalAmount: nonOriginalParts.reduce((sum, item) => sum + item.amount, 0),
      itemCount: nonOriginalParts.length
    };
    
    // 添加到订单列表
    mockDealerOrders.unshift(originalOrder, nonOriginalOrder);
    
    // 移除原订单
    const originalIndex = mockDealerOrders.findIndex(o => o.id === id);
    if (originalIndex !== -1) {
      mockDealerOrders.splice(originalIndex, 1);
    }
    
    result.data = [
      {
        orderId: originalOrder.id,
        orderNo: originalOrder.orderNo,
        partType: 'ORIGINAL'
      },
      {
        orderId: nonOriginalOrder.id,
        orderNo: nonOriginalOrder.orderNo,
        partType: 'NON_ORIGINAL'
      }
    ];
  } else {
    // 单一类型订单
    order.status = 'submitted';
    const partType = originalParts.length > 0 ? 'ORIGINAL' : 'NON_ORIGINAL';
    
    result.message = '订单提交成功';
    result.data = [{
      orderId: order.id,
      orderNo: order.orderNo,
      partType
    }];
  }
  
  return result;
};

export const getMockDeletePurchaseOrder = async (id: string) => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const orderIndex = mockDealerOrders.findIndex(o => o.id === id);
  if (orderIndex === -1) {
    throw new Error('订单不存在');
  }
  
  if (mockDealerOrders[orderIndex].status !== 'draft') {
    throw new Error('只有草稿状态的订单才能删除');
  }
  
  mockDealerOrders.splice(orderIndex, 1);
  return { success: true };
};

export const getMockCancelPurchaseOrder = async (id: string) => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const order = mockDealerOrders.find(o => o.id === id);
  if (!order) {
    throw new Error('订单不存在');
  }
  
  if (order.status !== 'submitted') {
    throw new Error('只有已提交状态的订单才能作废');
  }
  
  order.status = 'cancelled';
  return order;
};

export const getMockConfirmReceipt = async (id: string, data: any) => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const order = mockDealerOrders.find(o => o.id === id);
  if (!order) {
    throw new Error('订单不存在');
  }
  
  // 更新收货数量
  data.items.forEach((receiptItem: any) => {
    const orderItem = order.items.find(item => item.id === receiptItem.itemId);
    if (orderItem) {
      orderItem.receivedQuantity = (orderItem.receivedQuantity || 0) + receiptItem.receiptQuantity;
    }
  });
  
  // 判断订单状态
  const allReceived = order.items.every(item => 
    (item.receivedQuantity || 0) >= (item.shippedQuantity || 0)
  );
  
  order.status = allReceived ? 'received' : 'partiallyReceived';
  
  return order;
};