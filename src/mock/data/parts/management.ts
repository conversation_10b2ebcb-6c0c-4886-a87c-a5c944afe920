// 零件管理模块化Mock数据
import type { 
  UnifiedSearchParams, 
  PartManagementPageResponse,
  WorkOrderDetail,
  MaterialOrderItem,
  UnifiedTableItem
} from '@/types/parts/management';
import { fetchPartManagementData } from '@/mock/data/partManagement';
import { fetchScrapRecordsData } from '@/mock/data/scrapRecordsData';
import { mockPartArchivesData } from '@/mock/data/partArchivesData';

// ===== 叫料单数据获取 =====
// 完全保留现有逻辑，从页面的叫料单数据获取部分迁移
export const getRequisitionList = (params: UnifiedSearchParams): Promise<PartManagementPageResponse> => {
  // 完全复制现有逻辑 (lines 993-1016)
  const requisitionResult = fetchPartManagementData({
    page: params.page,
    pageSize: params.pageSize,
    partName: params.partName,
    partNumber: params.partNumber,
    requisitionNumber: params.requisitionNumber,
    supplierName: params.supplierName,
    requisitionDateRange: params.requisitionDateRange,
    requisitionStatus: params.requisitionStatus,
    inventoryStatus: params.inventoryStatus,
    approvalType: 'requisition'
  });

  const data = requisitionResult.data.map((item: any) => ({
    id: item.id,
    requisitionNumber: item.requisitionNumber,
    purchaseOrderNumber: item.purchaseOrderNumber || '',
    requisitionDate: item.requisitionDate,
    requisitionStatus: item.requisitionStatus,
    documentType: '叫料单',
    items: item.items,
  }));

  return Promise.resolve({
    data,
    total: requisitionResult.total
  });
};

// ===== 报损记录数据获取 =====
// 完全保留现有逻辑，从页面的报损单数据获取部分迁移
export const getScrapRecordsList = (params: UnifiedSearchParams): Promise<PartManagementPageResponse> => {
  // 完全复制现有逻辑 (lines 938-968)
  const scrapParams = {
    page: params.page,
    pageSize: params.pageSize,
    scrapOrderNumber: params.requisitionNumber,
    partName: params.partName,
    partNumber: params.partNumber,
    scrapDateRange: params.requisitionDateRange,
    status: params.requisitionStatus,
    scrapSource: params.inventoryStatus,
    returnDetailRecords: true,
  };
  
  const scrapResult = fetchScrapRecordsData(scrapParams);
  const data = scrapResult.data.map((item) => ({
    id: item.id,
    requisitionNumber: item.scrapOrderNumber,
    purchaseOrderNumber: item.deliveryOrderNumber || '-',
    requisitionDate: item.scrapDate,
    requisitionStatus: item.status,
    partName: item.partName,
    partNumber: item.partNumber,
    scrapSource: item.scrapSource,
    documentType: '报损单',
    scrapQuantity: item.quantity,
    scrapReason: item.scrapReason,
    scrapImages: item.scrapImages,
    rejectionReason: item.rejectionReason,
    items: [item],
  }));

  return Promise.resolve({
    data,
    total: scrapResult.total
  });
};

// ===== 工单数据获取 =====
// 完全保留现有逻辑，从fetchWorkOrderData函数迁移 (lines 627-697)
export const getWorkOrderList = (params: UnifiedSearchParams): Promise<PartManagementPageResponse> => {
  const { page = 1, pageSize = 10, requisitionNumber, requisitionStatus, requisitionDateRange } = params;

  // 完全保留现有工单数据生成逻辑
  const mockWorkOrderData = [
    {
      workOrderNumber: 'WO20240001',
      createDate: '2024-01-15',
      workOrderStatus: 'pending',
    },
    {
      workOrderNumber: 'WO20240002',
      createDate: '2024-01-16',
      workOrderStatus: 'picked',
    },
    {
      workOrderNumber: 'WO20240003',
      createDate: '2024-01-17',
      workOrderStatus: 'outOfStock',
    },
    {
      workOrderNumber: 'WO20240004',
      createDate: '2024-01-18',
      workOrderStatus: 'pending',
    },
    {
      workOrderNumber: 'WO20240005',
      createDate: '2024-01-19',
      workOrderStatus: 'picked',
    },
    // 添加更多数据以支持分页测试 - 保留完整的生成逻辑
    ...Array.from({ length: 50 }, (_, i) => ({
      workOrderNumber: `WO2024${String(i + 6).padStart(4, '0')}`,
      createDate: `2024-01-${String((i % 28) + 1).padStart(2, '0')}`,
      workOrderStatus: ['pending', 'picked', 'outOfStock', 'closed'][i % 4],
    }))
  ];

  let filteredData = [...mockWorkOrderData];

  // 保留完整的筛选逻辑
  if (requisitionNumber) {
    filteredData = filteredData.filter(item =>
      item.workOrderNumber.toLowerCase().includes(requisitionNumber.toLowerCase())
    );
  }

  if (requisitionStatus) {
    filteredData = filteredData.filter(item => item.workOrderStatus === requisitionStatus);
  }

  if (requisitionDateRange && requisitionDateRange.length === 2) {
    const [startDate, endDate] = requisitionDateRange;
    filteredData = filteredData.filter(item => {
      const itemDate = new Date(item.createDate);
      const start = new Date(startDate);
      const end = new Date(endDate);
      return itemDate >= start && itemDate <= end;
    });
  }

  // 分页处理
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const paginatedData = filteredData.slice(start, end);

  const data = paginatedData.map((item) => ({
    id: item.workOrderNumber,
    requisitionNumber: item.workOrderNumber,
    purchaseOrderNumber: '-',
    requisitionDate: item.createDate,
    requisitionStatus: item.workOrderStatus,
    partName: '',
    partNumber: '',
    supplierName: '',
    documentType: 'picking',
    workOrderData: item,
  }));

  return Promise.resolve({
    data,
    total: filteredData.length
  });
};

// ===== 物料单数据获取 =====
// 完全保留现有逻辑，从loadMaterialOrderData函数迁移 (lines 1101-1169)
export const getMaterialOrderList = (): MaterialOrderItem[] => {
  // 完全复制现有逻辑
  const mockData = [
    {
      workOrderNumber: 'WO20240001',
      createDate: '2024-01-15',
      workOrderStatus: 'pending_pick' as any,
      customerName: '张三',
      vehicleModel: '奔驰C200',
      serviceType: '保养维修'
    },
    {
      workOrderNumber: 'WO20240002',
      createDate: '2024-01-16',
      workOrderStatus: 'picked' as any,
      customerName: '李四',
      vehicleModel: '宝马X3',
      serviceType: '故障维修'
    },
    {
      workOrderNumber: 'WO20240003',
      createDate: '2024-01-17',
      workOrderStatus: 'picked' as any,
      customerName: '王五',
      vehicleModel: '奥迪A4',
      serviceType: '定期保养'
    },
    {
      workOrderNumber: 'WO20240004',
      createDate: '2024-01-18',
      workOrderStatus: 'pending_pick' as any,
      customerName: '赵六',
      vehicleModel: '丰田凯美瑞',
      serviceType: '事故维修'
    },
    {
      workOrderNumber: 'WO20240005',
      createDate: '2024-01-19',
      workOrderStatus: 'pending_pick' as any,
      customerName: '钱七',
      vehicleModel: '本田雅阁',
      serviceType: '保养维修'
    },
    {
      workOrderNumber: 'WO20240006',
      createDate: '2024-01-20',
      workOrderStatus: 'outOfStock' as any,
      customerName: '孙八',
      vehicleModel: '大众帕萨特',
      serviceType: '故障维修'
    },
    {
      workOrderNumber: 'WO20240007',
      createDate: '2024-01-21',
      workOrderStatus: 'outOfStock' as any,
      customerName: '周九',
      vehicleModel: '现代索纳塔',
      serviceType: '事故维修'
    }
  ];

  return mockData;
};

// ===== 工单详情生成 =====
// 完全保留现有逻辑，从loadWorkOrderData函数迁移 (lines 1319-1339)
export const generateWorkOrderDetail = (row: any): WorkOrderDetail => {
  const workOrderNumber = row.workOrderNumber || row.requisitionNumber;

  // 保留完整的零件随机选择逻辑
  const shuffledParts = [...mockPartArchivesData].sort(() => 0.5 - Math.random());
  const selectedPartsCount = Math.floor(Math.random() * 4) + 2; // 选择2到5个零件
  const selectedParts = shuffledParts.slice(0, selectedPartsCount);

  return {
    workOrderNumber: workOrderNumber,
    generateDate: row.requisitionDate || row.createDate,
    parts: selectedParts.map(part => ({
      partName: part.partName,
      partCode: part.partNumber, // 使用零件档案中的partNumber
      quantity: Math.floor(Math.random() * 5) + 1 // 1到5的随机数量
    }))
  };
};

// ===== 统一数据获取API =====
// 根据单据类型路由到不同的数据获取函数
export const getPartManagementData = (params: UnifiedSearchParams): Promise<PartManagementPageResponse> => {
  switch (params.documentType) {
    case 'scrap':
      return getScrapRecordsList(params);
    case 'picking':
      return getWorkOrderList(params);
    default:
      return getRequisitionList(params);
  }
};

// ===== 物料单筛选功能 =====
// 支持物料单的筛选和分页功能
export const getFilteredMaterialOrderList = (filters: {
  workOrderNumber?: string;
  workOrderStatus?: string;
  createDateRange?: string[];
  page?: number;
  pageSize?: number;
}): { data: MaterialOrderItem[], total: number } => {
  let filteredData = getMaterialOrderList();

  // 工单号筛选
  if (filters.workOrderNumber) {
    filteredData = filteredData.filter(item =>
      item.workOrderNumber.toLowerCase().includes(filters.workOrderNumber!.toLowerCase())
    );
  }

  // 状态筛选
  if (filters.workOrderStatus) {
    filteredData = filteredData.filter(item => item.workOrderStatus === filters.workOrderStatus);
  }

  // 日期范围筛选
  if (filters.createDateRange && filters.createDateRange.length === 2) {
    const [startDate, endDate] = filters.createDateRange;
    filteredData = filteredData.filter(item => {
      const itemDate = new Date(item.createDate);
      const start = new Date(startDate);
      const end = new Date(endDate);
      return itemDate >= start && itemDate <= end;
    });
  }

  // 分页处理
  const page = filters.page || 1;
  const pageSize = filters.pageSize || 10;
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const paginatedData = filteredData.slice(start, end);

  return {
    data: paginatedData,
    total: filteredData.length
  };
};