import type {
  ReceiptStatistics,
  ReceiptOrder,
  ReceiptOrderItem,
  ReceiptConfirmRequest,
  PurchaseOrderExtended,
  ReceiptStatus,
  ReceiptAbnormalType
} from '@/types/parts/purchase-dealer';

// Mock收货统计数据
export const mockReceiptStatistics: ReceiptStatistics = {
  totalOrders: 25,
  pendingReceipt: 8,
  completedReceipt: 15,
  abnormalReceipt: 2,
  totalAmount: 485600.00,
  receivedAmount: 385600.00,
  receivedRate: 79.4,
  onTimeRate: 92.5
};

// Mock收货单明细数据（示例）
export const mockReceiptOrderItems: ReceiptOrderItem[] = [];

/**
 * 动态生成收货单数据
 */
function generateReceiptOrdersForOrder(purchaseOrderId: string): ReceiptOrder[] {
  const orderNumber = parseInt(purchaseOrderId) || 1;
  const baseDate = new Date('2024-01-08');
  
  return [
    // 已收货的订单
    {
      id: `receipt_${orderNumber}_001`,
      receiptNo: `SH202401${String(orderNumber).padStart(2, '0')}01`,
      purchaseOrderId: purchaseOrderId,
      purchaseOrderNo: `PO202401${String(orderNumber).padStart(2, '0')}01`,
      shipmentId: `ship_${orderNumber}_001`,
      shipmentNo: `SF202401${String(orderNumber).padStart(2, '0')}01`,
      supplierId: 'supplier_001',
      supplierName: '博世汽车配件有限公司',
      expectedDate: new Date(baseDate.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      actualReceiptDate: new Date(baseDate.getTime() + 2 * 24 * 60 * 60 * 1000 + 9.5 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      status: 'NORMAL',
      totalItems: 3,
      receivedItems: 3,
      abnormalItems: 0,
      handler: '张三',
      remark: '按时到货，质量良好',
      createTime: new Date(baseDate.getTime() + 14.5 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date(baseDate.getTime() + 2 * 24 * 60 * 60 * 1000 + 9.5 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      items: [
        {
          id: `item_${orderNumber}_001`,
          receiptOrderId: `receipt_${orderNumber}_001`,
          partId: `part_${orderNumber}_001`,
          partCode: 'BP001',
          partName: '刹车片套装',
          unit: '套',
          orderedQuantity: 10,
          shippedQuantity: 10,
          actualReceiptQuantity: 10,
          status: 'NORMAL',
          locationId: 'loc_001',
          locationName: 'A区-01货架',
          batchNo: `**********${String(orderNumber).padStart(3, '0')}`,
          unitPrice: 280.00,
          amount: 2800.00
        },
        {
          id: `item_${orderNumber}_002`,
          receiptOrderId: `receipt_${orderNumber}_001`,
          partId: `part_${orderNumber}_002`,
          partCode: 'BP002',
          partName: '机油滤清器',
          unit: '个',
          orderedQuantity: 20,
          shippedQuantity: 20,
          actualReceiptQuantity: 20,
          status: 'NORMAL',
          locationId: 'loc_002',
          locationName: 'B区-05货架',
          batchNo: `**********${String(orderNumber).padStart(3, '0')}`,
          unitPrice: 45.00,
          amount: 900.00
        },
        {
          id: `item_${orderNumber}_003`,
          receiptOrderId: `receipt_${orderNumber}_001`,
          partId: `part_${orderNumber}_003`,
          partCode: 'BP003',
          partName: '空气滤清器',
          unit: '个',
          orderedQuantity: 15,
          shippedQuantity: 15,
          actualReceiptQuantity: 15,
          status: 'NORMAL',
          locationId: 'loc_003',
          locationName: 'A区-03货架',
          batchNo: `**********${String(orderNumber).padStart(3, '0')}`,
          unitPrice: 85.00,
          amount: 1275.00
        }
      ]
    },
    
    // 部分收货异常的订单
    {
      id: `receipt_${orderNumber}_002`,
      receiptNo: `SH202401${String(orderNumber).padStart(2, '0')}02`,
      purchaseOrderId: purchaseOrderId,
      purchaseOrderNo: `PO202401${String(orderNumber).padStart(2, '0')}01`,
      shipmentId: `ship_${orderNumber}_002`,
      shipmentNo: `SF202401${String(orderNumber).padStart(2, '0')}02`,
      supplierId: 'supplier_002',
      supplierName: '德尔福汽车系统',
      expectedDate: new Date(baseDate.getTime() + 4 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      actualReceiptDate: new Date(baseDate.getTime() + 4 * 24 * 60 * 60 * 1000 + 14.33 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      status: 'SHORTAGE',
      totalItems: 2,
      receivedItems: 1,
      abnormalItems: 1,
      handler: '李四',
      remark: '部分零件数量不足',
      createTime: new Date(baseDate.getTime() + 1 * 24 * 60 * 60 * 1000 + 16.33 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date(baseDate.getTime() + 4 * 24 * 60 * 60 * 1000 + 14.33 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      items: [
        {
          id: `item_${orderNumber}_004`,
          receiptOrderId: `receipt_${orderNumber}_002`,
          partId: `part_${orderNumber}_004`,
          partCode: 'DF001',
          partName: '燃油喷嘴',
          unit: '个',
          orderedQuantity: 15,
          shippedQuantity: 12,
          actualReceiptQuantity: 10,
          status: 'SHORTAGE',
          abnormalType: 'QUANTITY_SHORTAGE',
          abnormalReason: '供应商库存不足，实际到货12个，其中2个包装损坏',
          locationId: 'loc_004',
          locationName: 'B区-02货架',
          batchNo: `**********${String(orderNumber).padStart(3, '0')}`,
          unitPrice: 380.00,
          amount: 5700.00
        },
        {
          id: `item_${orderNumber}_005`,
          receiptOrderId: `receipt_${orderNumber}_002`,
          partId: `part_${orderNumber}_005`,
          partCode: 'DF002',
          partName: '点火线圈',
          unit: '个',
          orderedQuantity: 8,
          shippedQuantity: 8,
          actualReceiptQuantity: 8,
          status: 'NORMAL',
          locationId: 'loc_005',
          locationName: 'C区-01货架',
          batchNo: `**********${String(orderNumber).padStart(3, '0')}`,
          unitPrice: 450.00,
          amount: 3600.00
        }
      ]
    },

    // 待收货的订单1
    {
      id: `receipt_${orderNumber}_003`,
      receiptNo: `SH202401${String(orderNumber + 7).toString().padStart(2, '0')}01`,
      purchaseOrderId: purchaseOrderId,
      purchaseOrderNo: `PO202401${String(orderNumber).padStart(2, '0')}01`,
      shipmentId: `ship_${orderNumber}_003`,
      shipmentNo: `YD202401${String(orderNumber + 7).toString().padStart(2, '0')}01`,
      supplierId: 'supplier_003',
      supplierName: '马勒过滤器',
      expectedDate: new Date(baseDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'PENDING',
      totalItems: 4,
      receivedItems: 0,
      abnormalItems: 0,
      createTime: new Date(baseDate.getTime() + 4 * 24 * 60 * 60 * 1000 + 10.25 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date(baseDate.getTime() + 4 * 24 * 60 * 60 * 1000 + 10.25 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      items: [
        {
          id: `item_${orderNumber}_006`,
          receiptOrderId: `receipt_${orderNumber}_003`,
          partId: `part_${orderNumber}_006`,
          partCode: 'ML001',
          partName: '汽油滤清器',
          unit: '个',
          orderedQuantity: 25,
          shippedQuantity: 25,
          actualReceiptQuantity: 0,
          status: 'PENDING',
          unitPrice: 65.00,
          amount: 1625.00
        },
        {
          id: `item_${orderNumber}_007`,
          receiptOrderId: `receipt_${orderNumber}_003`,
          partId: `part_${orderNumber}_007`,
          partCode: 'ML002',
          partName: '空调滤清器',
          unit: '个',
          orderedQuantity: 30,
          shippedQuantity: 30,
          actualReceiptQuantity: 0,
          status: 'PENDING',
          unitPrice: 95.00,
          amount: 2850.00
        },
        {
          id: `item_${orderNumber}_008`,
          receiptOrderId: `receipt_${orderNumber}_003`,
          partId: `part_${orderNumber}_008`,
          partCode: 'ML003',
          partName: '机油泵',
          unit: '个',
          orderedQuantity: 5,
          shippedQuantity: 5,
          actualReceiptQuantity: 0,
          status: 'PENDING',
          unitPrice: 680.00,
          amount: 3400.00
        },
        {
          id: `item_${orderNumber}_009`,
          receiptOrderId: `receipt_${orderNumber}_003`,
          partId: `part_${orderNumber}_009`,
          partCode: 'ML004',
          partName: '水泵',
          unit: '个',
          orderedQuantity: 3,
          shippedQuantity: 3,
          actualReceiptQuantity: 0,
          status: 'PENDING',
          unitPrice: 850.00,
          amount: 2550.00
        }
      ]
    },

    // 待收货的订单2
    {
      id: `receipt_${orderNumber}_004`,
      receiptNo: `SH202401${String(orderNumber + 8).toString().padStart(2, '0')}02`,
      purchaseOrderId: purchaseOrderId,
      purchaseOrderNo: `PO202401${String(orderNumber).padStart(2, '0')}01`,
      shipmentId: `ship_${orderNumber}_004`,
      shipmentNo: `EMS202401${String(orderNumber + 8).toString().padStart(2, '0')}02`,
      supplierId: 'supplier_004',
      supplierName: '采埃孚传动技术',
      expectedDate: new Date(baseDate.getTime() + 8 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'PENDING',
      totalItems: 3,
      receivedItems: 0,
      abnormalItems: 0,
      createTime: new Date(baseDate.getTime() + 5 * 24 * 60 * 60 * 1000 + 9.75 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date(baseDate.getTime() + 5 * 24 * 60 * 60 * 1000 + 9.75 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      items: [
        {
          id: `item_${orderNumber}_010`,
          receiptOrderId: `receipt_${orderNumber}_004`,
          partId: `part_${orderNumber}_010`,
          partCode: 'ZF001',
          partName: '离合器片',
          unit: '套',
          orderedQuantity: 8,
          shippedQuantity: 8,
          actualReceiptQuantity: 0,
          status: 'PENDING',
          unitPrice: 420.00,
          amount: 3360.00
        },
        {
          id: `item_${orderNumber}_011`,
          receiptOrderId: `receipt_${orderNumber}_004`,
          partId: `part_${orderNumber}_011`,
          partCode: 'ZF002',
          partName: '变速箱油',
          unit: '升',
          orderedQuantity: 20,
          shippedQuantity: 20,
          actualReceiptQuantity: 0,
          status: 'PENDING',
          unitPrice: 180.00,
          amount: 3600.00
        },
        {
          id: `item_${orderNumber}_012`,
          receiptOrderId: `receipt_${orderNumber}_004`,
          partId: `part_${orderNumber}_012`,
          partCode: 'ZF003',
          partName: '变速箱滤网',
          unit: '个',
          orderedQuantity: 8,
          shippedQuantity: 8,
          actualReceiptQuantity: 0,
          status: 'PENDING',
          unitPrice: 120.00,
          amount: 960.00
        }
      ]
    },

    // 待收货的紧急订单
    {
      id: `receipt_${orderNumber}_005`,
      receiptNo: `SH202401${String(orderNumber + 9).toString().padStart(2, '0')}03`,
      purchaseOrderId: purchaseOrderId,
      purchaseOrderNo: `PO202401${String(orderNumber).padStart(2, '0')}01`,
      shipmentId: `ship_${orderNumber}_005`,
      shipmentNo: `SF202401${String(orderNumber + 9).toString().padStart(2, '0')}03`,
      supplierId: 'supplier_001',
      supplierName: '博世汽车配件有限公司',
      expectedDate: new Date(baseDate.getTime() + 9 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'URGENT',
      totalItems: 2,
      receivedItems: 0,
      abnormalItems: 0,
      createTime: new Date(baseDate.getTime() + 6 * 24 * 60 * 60 * 1000 + 16.5 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date(baseDate.getTime() + 6 * 24 * 60 * 60 * 1000 + 16.5 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      items: [
        {
          id: `item_${orderNumber}_013`,
          receiptOrderId: `receipt_${orderNumber}_005`,
          partId: `part_${orderNumber}_013`,
          partCode: 'BP004',
          partName: '火花塞套装',
          unit: '套',
          orderedQuantity: 12,
          shippedQuantity: 12,
          actualReceiptQuantity: 0,
          status: 'PENDING',
          unitPrice: 320.00,
          amount: 3840.00
        },
        {
          id: `item_${orderNumber}_014`,
          receiptOrderId: `receipt_${orderNumber}_005`,
          partId: `part_${orderNumber}_014`,
          partCode: 'BP005',
          partName: '高压包',
          unit: '个',
          orderedQuantity: 6,
          shippedQuantity: 6,
          actualReceiptQuantity: 0,
          status: 'PENDING',
          unitPrice: 580.00,
          amount: 3480.00
        }
      ]
    }
  ];
}

/**
 * 获取收货概览Mock数据
 */
export function getMockReceiptOverview(purchaseOrderId: string): Promise<{
  orderInfo: PurchaseOrderExtended;
  statistics: ReceiptStatistics;
}> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const orderNumber = parseInt(purchaseOrderId) || 1;
      const dynamicReceiptOrders = generateReceiptOrdersForOrder(purchaseOrderId);
      
      const orderInfo: PurchaseOrderExtended = {
        id: purchaseOrderId,
        orderNo: `PO202401${String(orderNumber).padStart(2, '0')}01`,
        dealerName: '上海浦东4S店',
        warehouseId: 3001,
        warehouseName: '主仓库',
        expectedDeliveryDate: '2024-01-15',
        status: 'partiallyReceived',
        totalAmount: 485600.00,
        itemCount: 25,
        remark: '紧急补货，请优先处理',
        createTime: '2024-01-08 10:30:00',
        updateTime: '2024-01-10 09:30:00',
        items: [],
        receiptProgress: {
          totalQuantity: 125,
          receivedQuantity: 85,
          receivedRate: 68.0,
          inTransitQuantity: 30,
          abnormalQuantity: 10
        },
        receiptOrders: dynamicReceiptOrders,
        lastReceiptDate: '2024-01-10',
        nextExpectedDate: '2024-01-12'
      };

      resolve({
        orderInfo,
        statistics: mockReceiptStatistics
      });
    }, 500);
  });
}

/**
 * 获取收货单列表Mock数据
 */
export function getMockReceiptOrderList(
  purchaseOrderId: string,
  params?: {
    status?: 'PENDING' | 'COMPLETED';
    page?: number;
    size?: number;
  }
): Promise<{
  list: ReceiptOrder[];
  total: number;
  page: number;
  size: number;
}> {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 使用动态生成的收货单数据
      const dynamicReceiptOrders = generateReceiptOrdersForOrder(purchaseOrderId);
      let filteredOrders = dynamicReceiptOrders;
      
      if (params?.status) {
        const statusFilter = params.status === 'PENDING' ? 
          (order: ReceiptOrder) => !order.actualReceiptDate :
          (order: ReceiptOrder) => !!order.actualReceiptDate;
        filteredOrders = filteredOrders.filter(statusFilter);
      }

      const page = params?.page || 1;
      const size = params?.size || 10;
      const start = (page - 1) * size;
      const end = start + size;
      
      resolve({
        list: filteredOrders.slice(start, end),
        total: filteredOrders.length,
        page,
        size
      });
    }, 300);
  });
}

/**
 * 获取收货单详情Mock数据
 */
export function getMockReceiptOrderDetail(receiptOrderId: string): Promise<ReceiptOrder> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 从收货单ID中提取订单号
      const matches = receiptOrderId.match(/receipt_(\d+)_/);
      if (!matches) {
        reject(new Error('收货单ID格式不正确'));
        return;
      }
      
      const orderNumber = matches[1];
      const dynamicReceiptOrders = generateReceiptOrdersForOrder(orderNumber);
      const order = dynamicReceiptOrders.find(order => order.id === receiptOrderId);
      
      if (order) {
        resolve(order);
      } else {
        reject(new Error('收货单不存在'));
      }
    }, 300);
  });
}

/**
 * 确认收货Mock数据
 */
export function getMockConfirmReceipt(data: ReceiptConfirmRequest): Promise<ReceiptOrder> {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 从收货单ID中提取订单号
      const matches = data.receiptOrderId.match(/receipt_(\d+)_/);
      if (!matches) {
        reject(new Error('收货单ID格式不正确'));
        return;
      }
      
      const orderNumber = matches[1];
      const dynamicReceiptOrders = generateReceiptOrdersForOrder(orderNumber);
      const order = dynamicReceiptOrders.find(order => order.id === data.receiptOrderId);
      
      if (!order) {
        reject(new Error('收货单不存在'));
        return;
      }

      // 创建更新后的收货单（模拟更新）
      const updatedOrder = { ...order };
      updatedOrder.actualReceiptDate = data.receiptDate;
      updatedOrder.handler = data.handler;
      updatedOrder.remark = data.remark;
      
      // 更新明细信息
      data.items.forEach(item => {
        const orderItem = updatedOrder.items.find(i => i.id === item.itemId);
        if (orderItem) {
          orderItem.actualReceiptQuantity = item.actualReceiptQuantity;
          orderItem.status = item.status;
          orderItem.abnormalType = item.abnormalType;
          orderItem.abnormalReason = item.abnormalReason;
          orderItem.locationId = item.locationId;
          orderItem.batchNo = item.batchNo;
        }
      });

      // 更新整体状态
      const hasAbnormal = updatedOrder.items.some(item => item.status !== 'NORMAL' && item.status !== 'PENDING');
      updatedOrder.status = hasAbnormal ? 'SHORTAGE' : 'NORMAL';
      updatedOrder.receivedItems = updatedOrder.items.filter(item => item.actualReceiptQuantity > 0).length;
      updatedOrder.abnormalItems = updatedOrder.items.filter(item => item.status !== 'NORMAL' && item.status !== 'PENDING').length;
      updatedOrder.updateTime = new Date().toISOString().slice(0, 19).replace('T', ' ');

      resolve(updatedOrder);
    }, 800);
  });
}

/**
 * 获取异常类型选项Mock数据
 */
export function getMockAbnormalTypeOptions(): Promise<Array<{ code: string; name: string; }>> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        { code: 'QUANTITY_SHORTAGE', name: '数量不足' },
        { code: 'QUALITY_DAMAGE', name: '质量损坏' },
        { code: 'SPECIFICATION_ERROR', name: '规格错误' },
        { code: 'PACKAGING_DAMAGE', name: '包装损坏' },
        { code: 'OTHER', name: '其他' }
      ]);
    }, 200);
  });
}

/**
 * 导出收货报表Mock数据
 */
export function getMockExportReceiptReport(
  purchaseOrderId: string,
  params?: {
    format?: 'excel' | 'pdf';
    dateRange?: [string, string];
  }
): Promise<{ downloadUrl: string; fileName: string; }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const format = params?.format || 'excel';
      const timestamp = new Date().toISOString().slice(0, 10);
      
      resolve({
        downloadUrl: `/mock/downloads/receipt-report-${purchaseOrderId}-${timestamp}.${format}`,
        fileName: `收货报表-${purchaseOrderId}-${timestamp}.${format}`
      });
    }, 1000);
  });
}