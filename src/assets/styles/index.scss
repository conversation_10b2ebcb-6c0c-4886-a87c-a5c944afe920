// src/assets/styles/index.scss
// 可以在这里引入一个 CSS Reset 或 Normalize 样式，确保不同浏览器样式一致
// @import "./reset.scss"; // 如果你创建了 reset.scss 文件

// 定义全局 body 样式
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  background-color: #f5f7fa; // Element Plus 默认的背景色
  color: $text-color-regular; // 使用定义的文本颜色变量
}

// 定义一些常用通用类
.page-container {
  padding: $base-padding; // 使用定义的间距变量
}

.page-title {
  font-size: 24px;
  color: $text-color-primary;
  margin-bottom: $base-margin;
}

// 为 Element Plus 的卡片组件添加一些默认样式
.el-card {
  margin-bottom: $base-margin;
  @include shadow-card; // 使用定义的混合宏
}

// 常用辅助类
.mb-20 {
  margin-bottom: 20px;
}
.mt-10 {
  margin-top: 10px;
}

// Element Plus 遮罩背景修复 - 图片查看器设置淡色背景确保可用性
:deep(.el-image-viewer__mask) {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

:deep(.el-image-viewer__wrapper) {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

// 全局强制覆盖 - 图片查看器设置淡色背景
.el-image-viewer__mask {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.el-image-viewer__wrapper {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

// 使用更高权重的选择器 - 图片查看器设置淡色背景
html body .el-image-viewer__mask {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

html body .el-image-viewer__wrapper {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

// 确保图片查看器的关闭按钮可见
.el-image-viewer__close {
  color: #ffffff !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.el-image-viewer__close:hover {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

// 只针对模态框和抽屉的遮罩设置为淡灰色
.el-dialog__wrapper,
.el-drawer__wrapper {
  background-color: rgba(0, 0, 0, 0.3) !important;
}

// 更强力的全局覆盖 - 只针对模态框和抽屉的遮罩
html body .el-dialog__wrapper,
html body .el-drawer__wrapper {
  background-color: rgba(0, 0, 0, 0.3) !important;
}

// 确保模态框本身保持正常的白色背景
.el-dialog,
.el-drawer,
.el-dialog__header,
.el-dialog__body,
.el-dialog__footer,
.el-drawer__header,
.el-drawer__body {
  background-color: #ffffff !important;
}

// 确保下拉选择框保持正常显示
.el-select,
.el-select__wrapper,
.el-select-dropdown,
.el-select-dropdown__item,
.el-popper {
  background-color: #ffffff !important;
}

.el-select-dropdown {
  border: 1px solid #e4e7ed !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
}

// 使用属性选择器覆盖所有可能的图片查看器
[class*="image-viewer"],
[class*="el-image-viewer"] {
  background-color: rgba(0, 0, 0, 0.9) !important;
}

// 最终解决方案：直接覆盖所有可能的背景
* {
  &[class*="image-viewer"] {
    background-color: rgba(0, 0, 0, 0.9) !important;
  }
}

// 终极解决方案：使用最高权重选择器
html body div.el-image-viewer__mask {
  background-color: rgba(0, 0, 0, 0.9) !important;
  background: rgba(0, 0, 0, 0.9) !important;
}

html body div.el-image-viewer__wrapper {
  background-color: rgba(0, 0, 0, 0.9) !important;
  background: rgba(0, 0, 0, 0.9) !important;
}

// 使用 CSS 变量覆盖
:root {
  --el-overlay-color-lighter: rgba(0, 0, 0, 0.9);
  --el-overlay-color: rgba(0, 0, 0, 0.9);
  --el-overlay-color-darker: rgba(0, 0, 0, 0.9);
}
