<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router';
import SidebarMenu from './components/SidebarMenu.vue';
import LanguageSelector from './components/common/LanguageSelector.vue';
import { ref, computed } from 'vue';
import { Fold, User, SwitchButton } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';
import { useAuthStore } from '@/stores/auth';

const isCollapse = ref(false);
const route = useRoute();
const authStore = useAuthStore();

// 检查当前路由是否为登录页面
const isLoginPage = computed(() => route.name === 'login');

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await authStore.logout();
    // 退出后会自动跳转到登录页
  } catch {
    // 用户取消退出
  }
};

</script>

<template>
  <div class="common-layout">
    <!-- 登录页面布局 -->
    <template v-if="isLoginPage">
      <RouterView />
    </template>

    <!-- 主应用布局 -->
    <template v-else>
      <el-container>
        <!-- 侧边栏 -->
        <el-aside width="auto">
          <SidebarMenu :isCollapse="isCollapse" />
        </el-aside>

        <el-container>
          <!-- 头部 -->
          <el-header>
            <div class="header-left">
              <el-button @click="isCollapse = !isCollapse" :icon="Fold" circle />
            </div>

            <div class="header-right">
              <!-- 语言选择组件 -->
              <LanguageSelector />

              <!-- 用户信息和退出 -->
              <el-dropdown @command="handleLogout" trigger="hover">
                <el-button :icon="User" circle />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item disabled>
                      {{ authStore.userInfo?.realName || authStore.userInfo?.username }}
                    </el-dropdown-item>
                    <el-dropdown-item divided command="logout">
                      <el-icon><SwitchButton /></el-icon>
                      退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </el-header>

          <!-- 主内容区域 -->
          <el-main>
            <RouterView />
          </el-main>
        </el-container>
      </el-container>
    </template>
  </div>
</template>

<style scoped>
.common-layout {
  height: 100vh;
}

.el-container {
  height: 100%;
}

.el-aside {
  background-color: #f0f2f5;
}

.el-header {
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,.1);
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.el-main {
  padding: 20px;
  background-color: #f5f5f5;
}

/* 登录页面全屏显示 */
.common-layout:has(.login-container) {
  height: 100vh;
  overflow: hidden;
}
</style>
