import type { RouteRecordRaw } from 'vue-router';

const financeRoutes: RouteRecordRaw[] = [
  {
    path: '/finance/whole-vehicle-collection',
    name: 'WholeVehicleCollection',
    component: () => import('@/views/finance/wholeVehicleCollection/WholeVehicleCollectionView.vue'),
    meta: {
      title: 'menu.wholeVehicleCollection',
      icon: 'Tickets',
      requiresAuth: true
    }
  }
];

export default financeRoutes;
