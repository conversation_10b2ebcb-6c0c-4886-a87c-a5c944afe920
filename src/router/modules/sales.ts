import type { RouteRecordRaw } from 'vue-router'

// 销售模块路由配置
export const salesRoutes: RouteRecordRaw[] = [
  // 销售订单路由
  {
    path: '/sales/orders',
    name: 'sales-orders',
    component: () => import('@/views/sales/orders/OrdersView.vue'),
    meta: {
      title: 'menu.salesOrders',
      icon: 'List',
      requiresAuth: true
    }
  },
  {
    path: '/sales/orders/:orderNo/detail',
    name: 'sales-order-detail',
    component: () => import('@/views/sales/orders/OrderDetailView.vue'),
    meta: {
      title: 'menu.salesOrderDetail',
      requiresAuth: true,
      hideInMenu: true
    }
  },
  {
    path: '/sales/orders/:orderNo/edit',
    name: 'sales-order-edit',
    component: () => import('@/views/sales/orders/OrderEditView.vue'),
    meta: {
      title: 'menu.salesOrderEdit',
      requiresAuth: true,
      hideInMenu: true
    }
  },

  {
    path: '/sales/test-drive',
    name: 'sales-test-drive',
    component: () => import('@/views/sales/testDrive/TestDriveView.vue'),
    meta: {
      title: 'menu.testDriveList',
      icon: 'car',
      requiresAuth: true
    }
  },

  // 潜客管理路由
  {
    path: '/sales/prospects',
    name: 'sales-prospects',
    component: () => import('@/views/sales/prospects/ProspectsView.vue'),
    meta: {
      title: 'menu.prospects',
      icon: 'user',
      requiresAuth: true
    }
  },

  // 厂端潜客池管理路由
  {
    path: '/sales/factory-prospect',
    name: 'sales-factory-prospect',
    component: () => import('@/views/sales/factoryProspect/FactoryProspectView.vue'),
    meta: {
      title: 'menu.factoryProspect',
      icon: 'UserFilled',
      requiresAuth: true
    }
  },

  // 订单审批管理路由
  {
    path: '/sales/order-approval',
    name: 'sales-order-approval',
    component: () => import('@/views/sales/orderApproval/OrderApprovalView.vue'),
    meta: {
      title: 'menu.orderApproval',
      icon: 'DocumentChecked',
      requiresAuth: true
    }
  },

  // 交付管理路由
  {
    path: '/sales/delivery',
    name: 'sales-delivery',
    component: () => import('@/views/sales/delivery/DeliveryView.vue'),
    meta: {
      title: 'menu.deliveryManagement',
      icon: 'Truck',
      requiresAuth: true
    }
  },

  // 车辆查询路由
  {
    path: '/sales/vehicle-query',
    name: 'sales-vehicle-query',
    component: () => import('@/views/sales/vehicleQuery/VehicleQueryView.vue'),
    meta: {
      title: 'menu.vehicleQuery',
      icon: 'Search',
      requiresAuth: true
    }
  },

  // 试驾报表路由
  {
    path: '/sales/test-drive-report',
    name: 'sales-test-drive-report',
    component: () => import('@/views/sales/testDriveReport/TestDriveReportView.vue'),
    meta: {
      title: 'menu.testDriveReport',
      icon: 'Document',
      requiresAuth: true
    }
  },

  // 工厂订单管理路由
  {
    path: '/sales/factory-order-management',
    name: 'sales-factory-order-management',
    component: () => import('@/views/sales/factoryOrderManagement/FactoryOrderManagementView.vue'),
    meta: {
      title: 'menu.factoryOrderManagement',
      icon: 'List',
      requiresAuth: true
    }
  },

  // 车型主数据管理路由
  {
    path: '/sales/vehicle-model',
    name: 'VehicleModel',
    component: () => import('@/views/sales/vehicleModel/VehicleModelView.vue'),
    meta: {
      title: 'menu.vehicleModel',
      requiresAuth: true,
      icon: 'Car'
    }
  },

  // 车辆登记管理路由
  {
    path: '/sales/vehicle-registration',
    name: 'sales-vehicle-registration',
    component: () => import('@/views/sales/vehicleRegistration/VehicleRegistrationView.vue'),
    meta: {
      title: 'menu.vehicleRegistration',
      icon: 'Document',
      requiresAuth: true
    }
  },

  // 发票管理路由（重构版本）
  {
    path: '/sales/invoice-management',
    name: 'sales-invoice-management',
    component: () => import('@/views/sales/invoiceManagement/InvoiceManagementView.vue'),
    meta: {
      title: 'menu.invoiceManagement',
      icon: 'Tickets',
      requiresAuth: true
    }
  },
  {
    path: '/sales/vehicle-allocation',
    name: 'vehicle-allocation',
    component: () => import('@/views/sales/vehicleAllocation/VehicleAllocationView.vue'),
    meta: {
      title: 'menu.vehicleAllocation',
      requiresAuth: true,
      icon: 'Truck'
    }
  },

  // 其他销售模块路由可以在此添加
]

export default salesRoutes
