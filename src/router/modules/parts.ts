import type { RouteRecordRaw } from 'vue-router'

// 零件模块路由配置
export const partsRoutes: RouteRecordRaw[] = [
  // 库存管理
  {
    path: '/parts/inventory',
    name: 'parts-inventory',
    component: () => import('@/views/parts/inventory/InventoryManagementView.vue'),
    meta: {
      title: 'menu.partsInventory',
      icon: 'Box',
      requiresAuth: true,
      permissions: ['parts:inventory:view']
    }
  },
  
  // 采购管理 - 经销商端
  {
    path: '/parts/purchase/dealer',
    name: 'parts-purchase-dealer',
    component: () => import('@/views/parts/purchase/dealer/index.vue'),
    meta: {
      title: 'menu.dealerPurchase',
      icon: 'ShoppingCart',
      requiresAuth: true,
      permissions: ['parts:purchase:dealer:view'],
      roles: ['dealer', 'dealer_admin']
    }
  },
  {
    path: '/parts/purchase/dealer/create',
    name: 'parts-purchase-dealer-create',
    component: () => import('@/views/parts/purchase/dealer/create.vue'),
    meta: {
      title: 'menu.createPurchaseOrder',
      requiresAuth: true,
      permissions: ['parts:purchase:dealer:create'],
      roles: ['dealer', 'dealer_admin'],
      hideInMenu: true
    }
  },
  {
    path: '/parts/purchase/dealer/:id/edit',
    name: 'parts-purchase-dealer-edit',
    component: () => import('@/views/parts/purchase/dealer/edit.vue'),
    meta: {
      title: 'menu.editPurchaseOrder',
      requiresAuth: true,
      permissions: ['parts:purchase:dealer:edit'],
      roles: ['dealer', 'dealer_admin'],
      hideInMenu: true
    }
  },
  {
    path: '/parts/purchase/dealer/:id/detail',
    name: 'parts-purchase-dealer-detail',
    component: () => import('@/views/parts/purchase/dealer/detail.vue'),
    meta: {
      title: 'menu.purchaseOrderDetail',
      requiresAuth: true,
      permissions: ['parts:purchase:dealer:view'],
      roles: ['dealer', 'dealer_admin'],
      hideInMenu: true
    }
  },
  {
    path: '/parts/purchase/dealer/:id/receipt',
    name: 'parts-purchase-dealer-receipt',
    component: () => import('@/views/parts/purchase/dealer/receipt/index.vue'),
    meta: {
      title: 'menu.purchaseReceipt',
      requiresAuth: true,
      permissions: ['parts:purchase:dealer:receipt'],
      roles: ['dealer', 'dealer_admin'],
      hideInMenu: true
    }
  },
  
  // 采购管理 - 主机厂端
  {
    path: '/parts/purchase/oem',
    name: 'parts-purchase-oem',
    component: () => import('@/views/parts/purchase/oem/index.vue'),
    meta: {
      title: 'menu.oemPurchase',
      icon: 'DocumentChecked',
      requiresAuth: true,
      permissions: ['parts:purchase:oem:view'],
      roles: ['oem', 'oem_admin', 'oem_approver']
    }
  },
  {
    path: '/parts/purchase/oem/:id/detail',
    name: 'parts-purchase-oem-detail',
    component: () => import('@/views/parts/purchase/oem/detail.vue'),
    meta: {
      title: 'menu.oemPurchaseDetail',
      requiresAuth: true,
      permissions: ['parts:purchase:oem:view'],
      roles: ['oem', 'oem_admin', 'oem_approver'],
      hideInMenu: true
    }
  }
]

export default partsRoutes