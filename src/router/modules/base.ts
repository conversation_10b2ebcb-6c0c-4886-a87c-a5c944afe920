import type { RouteRecordRaw } from 'vue-router'

// 基础模块路由配置
export const baseRoutes: RouteRecordRaw[] = [
  // 门店管理
  {
    path: '/base/store',
    name: 'base-store',
    component: () => import('@/views/base/store/StoreView.vue'),
    meta: {
      title: 'menu.store',
      icon: 'OfficeBuilding',
      requiresAuth: true,
      permissions: ['system:store:view']
    }
  },
  // ✅ 部门管理
  {
    path: '/base/department',
    name: 'base-department',
    component: () => import('@/views/base/department/DepartmentView.vue'),
    meta: {
      title: 'menu.department',
      icon: 'OfficeBuilding',
      requiresAuth: true,
      permissions: ['system:department:view']
    }
  },
  // ✅ 菜单管理
  {
    path: '/base/menu',
    name: 'base-menu',
    component: () => import('@/views/base/menu/MenuView.vue'),
    meta: {
      title: 'menu.menuManagement',
      icon: 'Menu',
      requiresAuth: true,
      permissions: ['system:menu:view']
    }
  }
]

export default baseRoutes
