{"orderManagement": "厂端订单管理", "orderOverview": "订单概览统计", "filterConditions": "筛选条件", "orderNumber": "订单编号", "orderNumberPlaceholder": "请输入订单编号", "storeName": "门店", "storeNamePlaceholder": "请选择门店", "model": "车型", "modelPlaceholder": "请选择车型", "variant": "配置", "variantPlaceholder": "请先选择车型", "orderStatus": "订单状态", "orderStatusPlaceholder": "请选择订单状态", "paymentStatus": "支付状态", "paymentStatusPlaceholder": "请选择支付状态", "createDateRange": "下单日期", "createDateRangePlaceholder": "请选择下单日期范围", "exportExcel": "导出Excel", "customerName": "下单人", "customerPhone": "下单人手机号", "buyerName": "购车人", "buyerPhone": "购车人手机号", "buyerType": "购车人类别", "vin": "VIN", "paymentMethod": "付款方式", "loanApprovalStatus": "贷款审核状态", "approvalStatus": "订单审核状态", "insuranceStatus": "投保状态", "jpjRegistrationStatus": "JPJ车辆注册状态", "createTime": "订单创建时间", "color": "颜色", "monthlyOrderCount": "本月订单数量", "todayOrderCount": "今日订单数量", "topStore": "订单最多门店", "hotModel": "最热销车型", "pendingDeliveryCount": "待交车订单", "growth": "环比增长", "thisMonthOrders": "本月订单量", "orderStatuses": {"pending_deposit": "待支付定金", "confirmed": "已确认", "cancelled": "已取消", "pending_delivery": "待交车", "completed": "已完成"}, "paymentStatuses": {"pending_deposit": "待支付定金", "deposit_paid": "定金已付", "pending_final": "待支付尾款", "fully_paid": "已全款"}, "paymentMethods": {"full_payment": "全款", "installment": "分期"}, "approvalStatuses": {"pending": "待审批", "approved": "已审批-通过", "rejected": "已审批-驳回"}, "insuranceStatuses": {"pending": "未投保", "completed": "投保完成", "failed": "投保失败"}, "jpjRegistrationStatuses": {"pending": "待登记", "completed": "已登记", "failed": "登记失败"}, "editOrder": "编辑订单", "orderDetail": "订单详情", "personalDetails": "客户信息", "preferredOutletSalesAdvisor": "购车门店信息", "purchaseDetails": "购车信息", "vehicleInfo": "车辆信息", "invoiceInfo": "开票信息", "serviceRightsInfo": "服务&权益信息", "paymentInfo": "支付信息", "insuranceInfo": "保险信息", "otrFeesInfo": "OTR费用信息", "addRights": "新增权益", "pushToInsurance": "推送至保险系统", "submitDelivery": "提交交车", "backToList": "返回列表", "save": "保存", "salesSubtotal": "销售小计", "numberPlatesFee": "车牌费", "totalAmount": "整车开票价", "deposit": "定金", "finalPayment": "尾款金额", "remainingReceivable": "剩余应收", "loanAmount": "贷款金额", "loanTerm": "贷款期数", "accessoryTotalAmount": "选配件总金额", "rightsTotalDiscount": "权益优惠总金额", "insuranceTotalAmount": "保险总金额", "otrFeesTotalAmount": "登记费用总金额", "accessories": "选配件信息", "accessoryCode": "配件代码", "accessoryName": "配件名称", "accessoryCategory": "类别", "accessoryPrice": "配件单价", "accessoryQuantity": "数量", "accessoryTotalPrice": "总价", "rights": "权益列表", "rightCode": "权益代码", "rightName": "权益名称", "rightMode": "权益模式", "rightDiscountAmount": "优惠金额", "rightEffectiveDate": "生效日期", "rightExpiryDate": "终止日期", "deleteRight": "删除", "policyNumber": "保单号", "insuranceType": "保险类型", "insuranceCompany": "保险公司", "insuranceAmount": "保险价格", "insuranceEffectiveDate": "生效日期", "insuranceExpiryDate": "到期日期", "insuranceNotes": "备注", "otrTicketNumber": "票据单号", "otrFeeItem": "收费项目", "otrFeePrice": "收费价格", "otrEffectiveDate": "生效日期", "otrExpiryDate": "到期日期", "buyerIdType": "购车人身份证件类别", "buyerIdNumber": "购车人身份证件号", "buyerEmail": "购车人邮箱", "buyerAddress": "购车人地址", "buyerState": "购车人所在州", "buyerCity": "购车人所在城市", "buyerPostalCode": "购车人所在地邮编", "storeState": "所在州", "storeCity": "所在城市", "salesConsultant": "销售顾问", "invoicingType": "开票", "invoicingName": "开票名称", "invoicingPhone": "开票电话", "invoicingAddress": "开票地址", "confirmColorChange": "您已更改车辆颜色，是否提交审核？", "confirmSubmitDelivery": "确定要提交交车吗？", "deliveryConditionsNotMet": "交车前置条件不满足", "selectRights": "选择权益", "availableRights": "可用权益", "selectAll": "全选", "addSelectedRights": "添加"}