{"pageTitle": "Appointment Quota Management", "storeName": "Store Name", "storeCode": "Store Code", "permissionTip": "You are currently viewing the appointment quota configuration for the default store. Please contact the administrator to configure other stores.", "configuredListTitle": "Configured Quota List", "addNewQuota": "Add New Quota", "editQuotaTitle": "<PERSON>", "addNewQuotaTitle": "Add New Quota", "emptyState": "No appointment quota configured yet. Click \"Add New Quota\" to configure.", "unsavedChangesWarning": "You have unsaved changes, are you sure to close?", "dateMustBeFutureOrToday": "Configuration date cannot be earlier than today", "selectDateFirst": "Please select date first", "timeSlotExceedsOperatingHours": "Time slot setting exceeds operating hours (8:00-18:00)", "table": {"configDate": "Config Date", "timeSlotCount": "Time Slot Count", "totalQuota": "Total Quota", "bookedQuantity": "Booked Quantity", "lastUpdateTime": "Last Update Time", "operations": "Operations", "edit": "Edit", "expired": "Expired"}, "modal": {"selectDateTitle": "Select Date", "dateLabel": "Config Date", "datePlaceholder": "Please select config date", "existingConfig": "Existing Config", "dateTip": "Config date cannot be earlier than today", "timeSlotConfigTitle": "Time Slot Configuration", "addTimeSlot": "Add Time Slot", "noTimeSlots": "No time slots added yet", "clickAddPrompt": "Click \"Add Time Slot\" to start configuration", "timeSlot": "Time Slot", "startTime": "Start Time", "startTimePlaceholder": "Please select start time", "endTime": "End Time", "endTimePlaceholder": "Please select end time", "quota": "<PERSON><PERSON><PERSON>", "quotaPlaceholder": "Please enter quota", "configDescriptionTitle": "Configuration Description", "configDescription": {"item1": "The quota of each time slot represents the maximum number of vehicles that can be booked in that time slot.", "item2": "Time slots cannot overlap, and the end time must be later than the start time.", "item3": "It is recommended to set a reasonable quota based on the actual reception capacity of the store.", "item4": "The sum of all time slot quotas is the total quota for the day."}}, "validation": {"dateRequired": "Please select config date", "atLeastOneTimeSlot": "At least one time slot is required", "timeRequired": "Please set the start and end time of the time slot", "startBeforeEnd": "Start time must be earlier than end time", "quotaPositive": "Quota must be greater than 0", "timeSlotOverlap": "Time slot {slot1} overlaps with {slot2}, please adjust"}, "summary": {"selectDate": "Please select date", "addTimeSlot": "Please add time slot", "timeSlotsUnit": "time slots", "totalQuota": "Total Quota"}, "saveConfig": "Save Config", "labelWithColon": "{label}:"}