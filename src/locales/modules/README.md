# 国际化模块说明

本目录包含了项目的模块化国际化文件。

## 目录结构

```
modules/
├── common/           # 通用模块 (按钮、操作等)
├── sales/           # 销售模块
├── order/           # 订单模块
├── parts/           # 零件模块
└── ...              # 其他业务模块
```

## 使用方式

### 在组件中使用

```vue
<script setup>
import { useModuleI18n } from '@/composables/useModuleI18n'

// 使用特定模块的国际化
const { t, tc } = useModuleI18n('sales')

// t() 用于访问当前模块的翻译
const title = t('vehicleList') // 相当于 $t('sales.vehicleList')

// tc() 用于访问通用模块的翻译
const confirmText = tc('confirm') // 相当于 $t('common.confirm')
</script>
```

### 添加新模块

1. 在 `modules/` 目录下创建新的模块文件夹
2. 添加 `zh.json` 和 `en.json` 文件
3. 在 `loader.ts` 中注册新模块

## 注意事项

- 每个模块的文件结构应保持一致
- 新增模块后需要在 loader.ts 中注册
- 避免在不同模块中使用相同的键名
