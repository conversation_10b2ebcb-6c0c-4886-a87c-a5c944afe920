{"inventoryManagement": {"title": "Inventory Management", "dashboard": {"totalSkuCount": "Total SKU Count", "shortageCount": "Shortage Alert", "warningCount": "Inventory Warning", "occupiedValue": "Occupied Value", "skuGrowthRate": "SKU Growth Rate", "trendUp": "Up", "trendDown": "Down", "trendStable": "Stable", "shortageDescription": "Urgent Replenishment Required", "warningDescription": "Below Safety Stock", "occupiedDescription": "Pending Outbound Amount"}, "search": {"category": "Part Category", "partCode": "Part Code", "partName": "Part Name", "categoryPlaceholder": "Select category", "partCodePlaceholder": "Enter part code", "partNamePlaceholder": "Enter part name", "stockStatus": "Stock Status", "stockStatusPlaceholder": "Select stock status"}, "table": {"stockStatus": "Status", "partCode": "Part Code", "partName": "Part Name", "brand": "Brand", "specification": "Specification", "currentStock": "Current Stock", "occupiedStock": "Occupied Stock", "damagedStock": "Damaged Stock", "safetyStock": "Safety Stock", "availableStock": "Available Stock", "warehouseName": "Warehouse", "location": "Location", "lastCheckTime": "Last Check"}, "function": {"title": "Function Area"}, "actions": {"inventoryCheck": "Stock Check", "batchReplenishment": "<PERSON><PERSON>", "setSafetyStock": "Set Safety Stock", "adjustStock": "Adjust Stock", "adjust": "Adjust"}, "status": {"SHORTAGE": "Shortage", "WARNING": "Warning", "NORMAL": "Normal", "OVERSTOCKED": "Overstocked"}, "dialog": {"detailTitle": "Inventory Detail", "adjustTitle": "Adjust Inventory", "partInfo": "Part Information", "inventoryInfo": "Inventory Information", "inventoryTrend": "Inventory Trend", "adjustInfo": "Adjustment Information", "adjustType": "Adjustment Type", "adjustQuantity": "Quantity", "adjustReason": "Reason", "adjustedStock": "Adjusted Stock", "adjustWarning": "Warning: Inventory adjustment will directly affect the book inventory. Please operate carefully and ensure sufficient justification.", "confirmAdjust": "Confirm Adjustment", "currentStockHint": "Current", "remark": "Remark", "operator": "Operator", "adjustTypePlaceholder": "Select adjustment type", "adjustQuantityPlaceholder": "Enter quantity", "adjustReasonPlaceholder": "Select reason", "remarkPlaceholder": "Enter remark", "increase": "Increase Stock", "decrease": "Decrease Stock", "setTo": "Set To", "reasonInventoryCheck": "Inventory Check", "reasonSystemCorrection": "System Correction", "reasonOther": "Other", "unit": "Unit", "retailPrice": "Retail Price", "purchasePrice": "Purchase Price", "maximumStock": "Maximum Stock", "shelfNumber": "<PERSON><PERSON>", "lastCheckTime": "Last Check Time", "checkPerson": "Check Person", "safetyStockLine": "Safety Stock Line", "date": "Date", "stock": "Stock", "noTrendData": "No trend data", "last30Days": "Last 30 Days", "showingRecent5Records": "Showing recent 5 records, contact admin for complete data"}, "messages": {"adjustSuccess": "Inventory adjusted successfully", "replenishmentSuccess": "Replenishment order created successfully"}, "validation": {"adjustTypeRequired": "Please select an adjustment type", "quantityRequired": "Please enter a quantity", "quantityMin": "Quantity must be greater than 0", "reasonRequired": "Please select a reason"}}, "archives": {"title": "Part Archives", "partName": "Part Name", "partNumber": "Part Number", "supplierName": "Supplier Name", "partNamePlaceholder": "Enter part name", "partNumberPlaceholder": "Enter part number", "supplierNamePlaceholder": "Enter supplier name", "unit": "Unit", "purchasePrice": "Purchase Price"}, "purchase": {"dealer": {"title": "My Call Material Orders", "description": "Manage and track your parts purchase orders", "dashboard": {"pendingApproval": "Submitted", "inTransit": "In Transit Orders", "pendingReceipt": "Pending Receipt", "monthlyTotal": "Monthly Application Total", "todayOrders": "Today's New", "urgentOrders": "Urgent Orders", "amount": "Amount", "quickActions": "Quick Actions", "createOrder": "New Call Material Order", "importOrder": "Import Order", "exportData": "Export Data", "orderStats": "Order Statistics", "clickToView": "Click to View"}, "search": {"placeholder": "Order No...", "searchButton": "Search"}, "filters": {"title": "Filter Conditions", "all": "All", "draft": "Draft", "pendingApproval": "Submitted", "approved": "Approved", "rejected": "Rejected", "cancelled": "Cancelled", "inTransit": "In Transit", "partialReceived": "Partially Received", "completed": "Completed"}, "table": {"title": "Order List", "totalRecords": "Total {total} Records", "orderNo": "Call Material Order No.", "status": "Status", "warehouseName": "Target Warehouse", "totalAmount": "Order Amount", "orderStatus": "Order Status", "itemCount": "<PERSON><PERSON>", "createTime": "Created Date", "shipTime": "Ship Date", "expectedDelivery": "Expected Delivery", "actions": "Actions"}, "status": {"draft": "Draft", "submitted": "Submitted", "approved": "Approved", "rejected": "Rejected", "shipped": "Shipped", "partiallyShipped": "Partially Shipped", "partiallyReceived": "Partially Received", "received": "Received", "cancelled": "Cancelled"}, "statusDescription": {"draft": "Draft status, can be edited and deleted", "submitted": "Call material list completed at dealer end", "approved": "HQ reviewed and approved the dealer's call material order", "rejected": "HQ reviewed and rejected the call material order (rejection notice will appear in message center)", "shipped": "Status triggered by ERP, upon receiving arrival order number from ERP, DMS system automatically generates receipt order number, and when all parts in this call material order are shipped, the order status will be updated to shipped", "partiallyShipped": "Status triggered by ERP, upon receiving arrival order number from ERP, DMS system automatically generates receipt order number, and when some parts in this call material order are shipped, the order status will be updated to partially shipped", "partiallyReceived": "Taking the call material order as a whole, after some parts are shipped, receipt is performed for the shipped parts but for the entire call material order it is partial receipt, so the document status changes from partially shipped to partially received", "received": "All parts on the call material order have been received in the system", "cancelled": "Call material order can only be cancelled when in submitted status, this operation terminates all subsequent processes"}, "actions": {"create": "New Call Material Order", "edit": "Edit", "detail": "View", "submit": "Submit", "resubmit": "Resubmit", "cancel": "Cancel", "delete": "Delete", "receipt": "Receipt", "print": "Print", "export": "Export"}, "form": {"orderInfo": "Order Information", "storeName": "Store Name", "warehouseRequired": "Receiving Warehouse*", "warehousePlaceholder": "Main Warehouse", "expectedDeliveryDate": "Expected Delivery Date", "expectedDeliveryDatePlaceholder": "Please select expected delivery date", "remark": "Application Remark", "remarkPlaceholder": "Please enter remarks to be explained to OEM...", "rejectReason": "Rejection Reason", "partDetails": "Application Part Details", "addPart": "Add Part", "addOriginalPartsPlaceholder": "Please add original parts", "addNonOriginalPartsPlaceholder": "Please add non-original parts", "originalPartDetails": "Original Parts Details", "nonOriginalPartDetails": "Non-Original Parts Details", "unifiedPartDetails": "Parts Details", "originalTotal": "Original Parts Subtotal", "nonOriginalTotal": "Non-Original Parts Subtotal", "grandTotal": "Grand Total", "partTable": {"index": "No.", "partCode": "Part Code", "partName": "Part Name", "partType": "Part Type", "currentStock": "Current Stock", "safetyStock": "Safety Stock", "quantity": "Application Quantity*", "unitPrice": "Unit Price", "amount": "Amount", "actions": "Actions"}, "tip": "Tip: The order will be submitted to OEM for approval and can only be shipped after approval.", "tips": {"splitOrderTip": "Tip: Current order contains both original and non-original parts, it will be automatically split into 2 separate orders upon submission.", "originalOrderTip": "Tip: Original parts order will be submitted to OEM for approval and can only be shipped after approval.", "nonOriginalOrderTip": "Tip: Non-original parts order will be submitted to third-party suppliers for processing.", "addPartsTip": "Tip: Please add parts to be purchased.", "splitOrderConfirm": "Current order contains both original and non-original parts, it will be automatically split into 2 separate orders. Do you want to continue?"}, "saveDraft": "Save Draft", "submitApproval": "Submit for Approval", "cancel": "Cancel", "confirmSubmitTitle": "Confirm Submission", "continueSubmit": "Continue Submit"}, "selector": {"title": "Select Parts", "filterTitle": "Filter Conditions", "partType": "Part Type", "partCategory": "Part Category", "partCode": "Part Code", "stockStatus": "Stock Status", "stockStatusNormal": "Normal (Above Safety Stock)", "stockStatusWarning": "Warning (At or Below Safety Stock)", "originalPart": "Original Part", "nonOriginalPart": "Non-Original Part", "searchPlaceholder": "Part Code/Name...", "selectedParts": "Selected Parts", "selectedOriginalParts": "Selected Original Parts", "selectedNonOriginalParts": "Selected Non-Original Parts", "partTable": {"stockStatus": "Stock Status", "currentStock": "Current", "availableStock": "Available", "occupiedStock": "Occupied", "safetyStock": "Safety", "suggestedQuantity": "Suggested Quantity", "orderQuantity": "Order Quantity*"}, "status": {"SHORTAGE": "Shortage", "WARNING": "Warning", "NORMAL": "Normal"}, "category": {"engine": "Engine Parts", "body": "Body & Accessories", "chassis": "Chassis Parts", "electrical": "Electrical Equipment"}, "confirmAdd": "Confirm Add", "splitOrderWarning": "Tip: Original parts and non-original parts need to be generated into separate call material orders"}, "receipt": {"title": "Purchase Receipt", "selectShipment": "Select Shipment", "shipmentInfo": "Shipment Information", "relatedOrder": "Related Purchase Order", "shipmentNo": "Shipment No.", "shipDate": "Ship Date", "carrier": "Carrier", "trackingNo": "Tracking No.", "shipmentStatus": "Status", "receiptDetails": "Receipt Details", "receiptTable": {"index": "No.", "partCode": "Part Code", "partName": "Part Name", "orderedQty": "Ordered Qty", "receivedQty": "Received Qty", "shippedQty": "Shipped Qty", "receiptQty": "Receipt Qty*"}, "receiptInfo": "Receipt Information", "warehouse": "Warehouse", "receiptDate": "Receipt Date", "handler": "Receiver", "confirmReceipt": "Confirm Receipt", "noShipments": "No shipments available", "selectShipmentFirst": "Please select a shipment first"}, "validation": {"warehouseRequired": "Please select receiving warehouse", "expectedDeliveryDateRequired": "Please select expected delivery date", "reasonRequired": "Please enter reason", "quantityRequired": "Please enter application quantity", "quantityMin": "Application quantity must be greater than 0", "itemsRequired": "Please add part details"}, "messages": {"saveSuccess": "Saved successfully", "submitSuccess": "Submitted successfully", "loadPartsFailed": "Failed to load parts list", "confirmSubmit": "Confirm submit purchase application?", "confirmCancel": "Confirm cancel purchase order?", "confirmDelete": "Confirm delete purchase order?"}}, "oem": {"title": "Purchase Order Approval (OEM)", "dashboard": {"pendingApproval": "Pending My Approval", "approvedToday": "Approved Today", "pendingShipment": "Pending Shipment Orders", "weeklyTotal": "7-Day Application Total", "shippedToday": "Shipped Today", "monthlyTotal": "Monthly Total", "todayNew": "Today's New", "urgent": "<PERSON><PERSON>", "amount": "Amount", "monthlyOrders": "Monthly Orders", "quickActions": "Quick Actions", "batchApproval": "<PERSON><PERSON>", "batchShipment": "Batch Shipment", "dealerAnalysis": "Dealer Analysis", "popularParts": "Popular Parts", "clickToView": "Click to View"}, "search": {"placeholder": "Order No./Dealer Name...", "searchButton": "Search"}, "filters": {"all": "All", "pending": "Submitted", "approved": "Approved", "rejected": "Rejected", "pendingShipment": "Pending Shipment", "partialShipped": "Partially Shipped", "allShipped": "All Shipped"}, "table": {"orderNo": "Purchase Order No.", "dealerName": "Dealer Name", "totalAmount": "Order Amount", "orderStatus": "Order Status", "createTime": "Application Date", "actions": "Actions"}, "detail": {"title": "Order Processing", "orderBasicInfo": "Order Basic Information", "dealer": "Dealer", "applicant": "Applicant", "applicantPhone": "Contact Phone", "applyDate": "Application Date", "applyRemark": "Application Remark", "orderDetails": "Application Details (Approval/Shipment)", "detailTable": {"index": "No.", "partCode": "Part Code", "partName": "Part Name", "requestQty": "Requested Quantity", "factoryStock": "Factory Stock", "shipQty": "This Shipment*", "status": "Status", "stockSufficient": "Stock Sufficient", "stockInsufficient": "Stock Insufficient"}, "approvalSection": "Approval Section", "approvalResult": "Approval Result*", "pass": "Pass", "reject": "Reject", "approvalNote": "Approval Note", "approvalNotePlaceholder": "Please enter approval note or rejection reason...", "confirmApproval": "Confirm Approval", "shipmentSection": "Shipment Section", "carrier": "Carrier*", "carrierPlaceholder": "Please select", "trackingNo": "Tracking Number*", "trackingNoPlaceholder": "Please enter tracking number", "shipmentNote": "Shipment Note", "shipmentNotePlaceholder": "Out-of-stock parts expected to arrive next week and will be shipped then", "confirmShipment": "Confirm Shipment"}, "actions": {"approve": "Approve", "reject": "Reject", "ship": "Ship", "detail": "View"}, "status": {"pending": "Pending Approval", "approved": "Approved", "rejected": "Rejected", "pendingShipment": "Pending Shipment", "partialShipped": "Partially Shipped", "allShipped": "All Shipped"}, "validation": {"carrierRequired": "Please select carrier", "trackingNoRequired": "Please enter tracking number", "approvalResultRequired": "Please select approval result", "approvalNoteRequired": "Please enter approval note"}, "messages": {"approveSuccess": "Approved successfully", "shipSuccess": "Shipped successfully", "confirmApprove": "Confirm approve this purchase application?", "confirmReject": "Confirm reject this purchase application?", "confirmShip": "Confirm ship this order?"}}, "receipt": {"title": "Receipt Management", "backToList": "Back to Call Material Orders", "exportReport": "Export Report", "orderInfo": "Call Material Order Information", "receiptProgress": "Receipt Progress Overview", "receiptOrderList": "Receipt Order List", "pendingReceipt": "Pending Receipt Orders", "completedReceipt": "Completed Receipt Orders", "receiptDetail": "Receipt Order Detail", "confirmReceipt": "Receipt Confirmation", "basicInfo": {"orderNo": "Order No.", "dealerName": "Dealer", "orderStatus": "Order Status", "warehouse": "Receiving Warehouse", "expectedDeliveryDate": "Expected Delivery Date", "orderAmount": "Order Amount", "applyTime": "Application Time", "lastReceiptTime": "Last Receipt Time", "nextExpectedDate": "Next Expected Date", "applyRemark": "Application Remark"}, "progress": {"receiptProgress": "Receipt Progress", "inTransitGoods": "In-Transit Goods", "pendingReceiptOrders": "Pending Receipt Orders", "completedReceiptOrders": "Completed Receipt Orders", "abnormalReceipt": "Abnormal Receipt", "receiptAmount": "Receipt Amount", "onTimeRate": "On-Time Rate", "totalOrders": "Total Orders", "orderQuantity": "Order Quantity", "onTimeReceiptRate": "On-Time Receipt Rate", "allReceiptOrders": "All Receipt Orders", "abnormalAlert": "Abnormal receipt situation exists", "items": "items", "totalAmountPercentage": "{percentage} of total amount", "abnormalDescriptionBoth": "{orders} receipt orders have abnormalities, involving {quantity} items. Please handle promptly.", "abnormalDescriptionOrders": "{orders} receipt orders have abnormalities. Please handle promptly.", "abnormalDescriptionQuantity": "{quantity} items have abnormalities. Please handle promptly."}, "list": {"refresh": "Refresh", "receiptNo": "Receipt No.", "shipmentNo": "Shipment No.", "supplierName": "Supplier", "expectedReceiptDate": "Expected Receipt Date", "actualReceiptDate": "Actual Receipt Time", "goodsDetail": "Goods Detail", "received": "Received", "abnormal": "Abnormal", "signer": "Signer", "remark": "Remark", "viewDetail": "View Detail", "confirmReceipt": "Confirm Receipt", "noPendingOrders": "No pending receipt orders", "noCompletedOrders": "No completed receipt orders"}, "detail": {"receiptOrderInfo": "Receipt Order Information", "relatedPurchaseOrder": "Related Purchase Order", "supplier": "Supplier", "receiptStatus": "Receipt Status", "createTime": "Create Time", "partsDetail": "Parts Detail", "index": "No.", "partCode": "Part Code", "partName": "Part Name", "unit": "Unit", "orderedQuantity": "Ordered Quantity", "shippedQuantity": "Shipped Quantity", "actualReceiptQuantity": "Actual Receipt Quantity", "storageLocation": "Storage Location", "batchNo": "Batch No.", "abnormalInfo": "Abnormal Information", "unitPrice": "Unit Price", "amount": "Amount", "totalItems": "Total Items", "receivedItems": "Received Items", "abnormalItems": "Abnormal Items", "totalAmount": "Total Amount", "close": "Close"}, "confirm": {"selectShipment": "Select Shipment", "receiptDetails": "Receipt Detail Confirmation", "receiveAll": "Receive All", "setAllNormal": "Set All Normal", "actualReceiptQuantity": "Actual Receipt Quantity*", "difference": "Difference", "receiptStatus": "Receipt Status*", "abnormalType": "Abnormal Type", "abnormalReason": "Abnormal Reason", "enterAbnormalReason": "Enter abnormal reason", "storageLocation": "Storage Location", "batchNo": "Batch No.", "warehouseInfo": "Warehouse Information", "receiptDate": "Receipt Date*", "selectReceiptDate": "Select receipt date", "signer": "Signer*", "enterSignerName": "Enter signer name", "warehouse": "Warehouse", "receiptRemark": "Receipt Remark", "enterReceiptRemark": "Enter receipt remark...", "receiptSummary": "Receipt Summary", "totalItems": "Total Items", "normalItems": "Normal Receipt", "abnormalItems": "Abnormal Items", "totalReceiptQuantity": "Total Receipt Quantity", "noShipments": "No shipments available for receipt", "selectReceiptDateRequired": "Please select receipt date", "enterSignerRequired": "Please enter signer", "signerLengthValidation": "Signer name length should be between 2 and 50 characters"}, "status": {"NORMAL": "Normal", "SHORTAGE": "Shortage", "DAMAGE": "Damage", "REJECTED": "Rejected"}, "abnormalType": {"QUANTITY_SHORTAGE": "Quantity Shortage", "QUALITY_DAMAGE": "Quality Damage", "SPECIFICATION_ERROR": "Specification Error", "PACKAGING_DAMAGE": "Packaging Damage", "OTHER": "Other"}, "validation": {"receiptDateRequired": "Please select receipt date", "handlerRequired": "Please enter handler", "handlerLength": "Handler length should be between 2 and 50 characters", "abnormalReasonRequired": "Abnormal items must provide abnormal reason"}, "messages": {"exportSuccess": "Report exported successfully", "exportFailed": "Failed to export report", "confirmExport": "Confirm export receipt report for current order?", "receiptConfirmSuccess": "Receipt confirmed successfully", "receiptConfirmFailed": "Failed to confirm receipt", "loadReceiptOverviewFailed": "Failed to load receipt overview", "loadReceiptOrderListFailed": "Failed to load receipt order list", "loadReceiptOrderDetailFailed": "Failed to load receipt order detail", "receiveAllSuccess": "Set to receive all successfully", "setAllNormalSuccess": "Set all to normal status successfully", "abnormalReasonRequired": "Abnormal items must provide abnormal reason", "confirmReceiptItems": "Confirm receipt of {total} items? {normal} normal items, {abnormal} abnormal items.", "initDataFailed": "Failed to initialize data"}}}}