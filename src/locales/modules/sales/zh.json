{"salesProspectManagement": "销售潜客管理", "prospectId": "潜客ID", "inputProspectId": "请输入潜客ID", "prospectName": "潜客名称", "inputProspectName": "请输入潜客名称", "prospectPhone": "潜客手机号", "inputPhoneNumber": "请输入手机号", "sourceChannel": "来源渠道", "prospectLevel": "潜客级别", "prospectStatus": "潜客状态", "intentModel": "意向车型", "intentVariant": "意向配置", "intentColor": "意向颜色", "salesAdvisorId": "销售顾问工号", "salesAdvisorName": "销售顾问", "prospectCreationTime": "潜客创建时间", "lastFollowUpTime": "上次跟进时间", "nextFollowUpTime": "下次跟进时间", "markNoIntention": "标记无意向", "changeAdvisor": "变更顾问", "addProspectSuccess": "新增潜客成功", "followUpRecordAddSuccess": "跟进记录添加成功", "markNoIntentionApplySuccess": "标记无意向申请提交成功", "changeAdvisorSuccess": "变更顾问成功", "levelH": "H级", "levelA": "A级", "levelB": "B级", "levelC": "C级", "statusNew": "新建", "statusFollowing": "跟进中", "statusClosed": "已成交", "statusNoIntention": "无意向", "addProspect": "新增潜客", "foundMatchingLead": "找到匹配线索", "noMatchingLead": "未找到匹配线索", "customerName": "客户姓名", "phoneNumber": "手机号", "email": "邮箱", "idType": "身份证件类别", "idNumber": "身份证件号", "inputNameOrPhoneRequired": "请至少输入客户姓名或手机号", "noMatchingLeadMessage": "系统中没有找到匹配的线索信息，无法新增潜客。请确认输入的客户信息是否正确，或联系管理员先创建线索。", "inputIdNumber": "请输入证件号码", "inputEmail": "请输入邮箱地址", "intentionLevel": "意向级别", "idCard": "身份证", "passport": "护照", "residencePermit": "居住证", "inputProspectNameRequired": "请输入潜客名称", "inputPhoneNumberRequired": "请输入手机号", "invalidPhoneNumber": "请输入正确的手机号码", "selectIdTypeRequired": "请选择证件类别", "inputIdNumberRequired": "请输入证件号码", "inputEmailRequired": "请输入邮箱", "invalidEmail": "请输入正确的邮箱地址", "selectIntentionLevelRequired": "请选择意向级别", "prospectFollowUp": "潜客跟进", "prospectInfo": "潜客信息", "idTypePlaceholder": "请选择", "region": "地区", "selectRegion": "请选择地区", "prospectIntention": "潜客意向", "selectIntentModel": "请选择意向Model", "selectIntentVariant": "请选择意向配置", "selectIntentColor": "请选择意向颜色", "followUpRecord": "跟进记录", "currentAdvisor": "当前顾问", "followUpMethod": "跟进方式", "selectFollowUpMethod": "请选择跟进方式", "followUpTime": "跟进时间", "selectFollowUpTime": "请选择跟进时间", "intentionLevelPlaceholder": "请选择", "nextFollowUpTimePlaceholder": "系统会根据意向级别自动计算", "followUpDetails": "跟进情况", "followUpDetailsPlaceholder": "请描述跟进情况...", "inputFollowUpDetailsRequired": "请填写跟进情况", "selectFollowUpMethodRequired": "请选择跟进方式", "selectFollowUpTimeRequired": "请选择跟进时间", "selectIntentionLevelRequiredFollowUp": "请选择意向级别", "selectNextFollowUpTimeRequired": "请选择下次跟进时间", "selectIntentionModelRequired": "请选择意向Model", "selectIntentionVariantRequired": "请选择意向配置", "selectIntentionColorRequired": "请选择意向颜色", "changeAdvisorModalTitle": "变更顾问", "currentAdvisorId": "当前顾问工号", "currentAdvisorName": "当前顾问名字", "changeAdvisorId": "变更顾问工号", "changeAdvisorName": "变更顾问名字", "selectNewAdvisor": "请选择新的销售顾问", "changeReason": "重新分配顾问原因", "inputChangeReason": "请输入重新分配顾问原因", "newAdvisorCannotBeSame": "新顾问不能与当前顾问相同", "changeFailed": "变更失败", "selectNewAdvisorRequired": "请选择新的销售顾问", "inputChangeReasonRequired": "请输入重新分配顾问原因", "markNoIntentionConfirm": "标记无意向确认", "markTime": "标记时间", "noIntentionReason": "无意向原因", "selectNoIntentionReason": "请选择无意向原因", "detailedDescription": "详细说明", "detailedDescriptionPlaceholder": "请详细描述无意向原因...", "inputDetailedDescriptionRequired": "请输入详细说明", "markNoIntentionSubmitSuccess": "标记无意向申请提交成功，等待审核", "formValidationFailed": "表单验证失败或提交失败", "prospectDetails": "潜客详情", "prospectSource": "潜客来源", "idDocumentType": "身份证件类别", "idDocumentNumber": "身份证件号", "address": "地址", "testDriveRecord": "试驾记录", "driver": "试驾人", "model": "车型", "time": "时间", "feedback": "反馈", "noTestDriveRecord": "暂无试驾记录", "changeLog": "变更日志", "changeContent": "变更内容", "originalInfo": "原始信息", "changedInfo": "变更后信息", "operator": "操作人", "operationTime": "操作时间", "noFollowUpRecord": "暂无跟进记录", "noChangeLog": "暂无变更日志", "testDriveManagement": "试驾管理", "testDriveList": "试驾登记列表", "testDriveRegistration": "试驾登记", "testDriveDetail": "试驾详情", "testDriveEdit": "编辑试驾", "testDriveCreate": "登记试驾单", "testDriveNo": "试驾单号", "testDriveTime": "试驾时间", "testDriveModel": "试驾车型", "testDriveVariant": "试驾配置", "testDrivePerson": "试驾人", "testDrivePersonPhone": "试驾人手机号", "testDrivePersonIdCard": "试驾人证件号", "testDrivePersonLicense": "试驾人驾照号", "testDriveStartMileage": "试驾开始里程数", "testDriveEndMileage": "试驾结束里程数", "testDriveStartTime": "试驾开始时间", "testDriveEndTime": "试驾结束时间", "testDriveFeedback": "试驾反馈", "testDriveEntryTime": "试驾单录入时间", "searchProspect": "搜索潜客", "prospectSearch": "快捷搜索潜客", "testDriveInfo": "试驾信息", "salesConsultant": "销售顾问", "pleaseSelectModel": "请选择车型", "pleaseSelectVariant": "请选择配置", "pleaseEnterDriverName": "请输入试驾人姓名", "pleaseEnterDriverPhone": "请输入试驾人手机号", "pleaseSelectIdType": "请选择证件类别", "pleaseEnterIdNumber": "请输入试驾人证件号", "pleaseEnterLicenseNumber": "请输入试驾人驾照号", "pleaseEnterStartMileage": "请输入开始里程数", "pleaseEnterEndMileage": "请输入结束里程数", "pleaseSelectStartTime": "请选择试驾开始时间", "pleaseSelectEndTime": "请选择试驾结束时间", "pleaseEnterFeedback": "请输入试驾反馈", "autoFillAfterSearch": "搜索后自动填入", "autoGenerateOnSave": "保存时自动生成", "belongingConsultant": "潜客所属顾问", "defaultFromProspect": "默认带入潜客信息，可更改", "registrationSuccess": "登记成功", "updateSuccess": "更新试驾单成功", "searchProspectFirst": "请搜索并选择潜客", "needSalesConsultant": "潜客需要有所属销售顾问", "endMileageError": "结束里程数不能小于开始里程数", "endTimeError": "结束时间必须晚于开始时间", "pleaseEnterProspectName": "请输入潜客名称搜索", "pleaseEnterProspectPhone": "输入手机号搜索", "searchSuccess": "潜客信息加载成功", "noMatchingProspect": "未找到匹配的潜客信息", "pleaseEnterNameOrPhone": "请输入潜客名称或手机号进行搜索", "syncSuccess": "同步成功", "errorMessage": "错误消息", "recordCount": "记录数量", "syncTime": "同步时间", "No": "编号", "prospects": {"title": "潜客管理", "salesProspectManagement": "销售潜客管理", "prospectId": "潜客ID", "prospectName": "潜客名称", "prospectPhone": "潜客手机号", "sourceChannel": "来源渠道", "prospectLevel": "意向等级", "prospectStatus": "潜客状态", "intentModel": "意向车型", "intentVariant": "意向车款", "intentColor": "意向颜色", "salesAdvisorId": "销售顾问编号", "salesAdvisorName": "销售顾问", "prospectAssociatedTime": "关联潜客时间", "prospectCreationTime": "潜客创建时间", "lastFollowUpTime": "上次跟进时间", "nextFollowUpTime": "下次跟进时间", "markNoIntention": "标记无意向", "changeAdvisor": "变更顾问", "addProspect": "新增潜客", "prospectFollowUp": "潜客跟进", "prospectDetails": "潜客详情", "addProspectSuccess": "新增潜客成功", "followUpRecordAddSuccess": "跟进记录添加成功", "markNoIntentionApplySuccess": "无意向标记申请成功", "changeAdvisorSuccess": "顾问变更成功", "inputProspectId": "请输入潜客编号", "inputProspectName": "请输入潜客姓名", "inputPhoneNumber": "请输入联系电话", "prospectInfo": "潜客信息", "searchExistingLead": "搜索现有线索", "existingLeadFound": "找到现有线索", "existingLeadInfo": "系统检测到该客户已有线索记录，将基于现有线索创建潜客。", "foundMatchingLead": "找到匹配线索", "noMatchingLead": "未找到匹配线索", "noMatchingLeadMessage": "系统中没有找到匹配的线索信息，将创建为全新潜客。", "inputNameOrPhoneRequired": "请至少输入客户姓名或手机号", "nameRequired": "请输入潜客名称", "nameLength": "名称长度应在 2 到 50 个字符之间", "phoneRequired": "请输入手机号", "phoneFormat": "请输入有效的手机号码", "sourceRequired": "请选择来源渠道", "levelRequired": "请选择意向级别", "emailFormat": "请输入有效的邮箱地址", "searchCriteriaRequired": "请输入姓名或电话进行搜索", "noExistingLeadFound": "未找到现有线索，请填写以下表单创建新潜客。", "selectSourceChannel": "请选择来源渠道", "selectProspectLevel": "请选择意向级别", "selectIdType": "请选择证件类型", "inputIdNumber": "请输入证件号码", "inputEmail": "请输入邮箱地址", "inputRegion": "请输入地区", "idType": "证件类型", "idNumber": "证件号码", "email": "电子邮箱", "region": "所在地区", "changeAdvisorModalTitle": "变更销售顾问", "currentAdvisorInfo": "当前顾问信息", "currentAdvisor": "当前顾问", "noAdvisorAssigned": "未分配顾问", "changeAdvisorInfo": "变更信息", "newAdvisor": "新销售顾问", "selectNewAdvisor": "请选择新的销售顾问", "changeReason": "变更原因", "inputChangeReason": "请输入变更原因", "newAdvisorRequired": "必须选择一个新的销售顾问", "changeReasonRequired": "必须填写变更原因", "changeReasonLength": "原因说明应在 5 到 200 个字符之间", "newAdvisorCannotBeSame": "新顾问不能与当前顾问相同", "changeFailed": "变更操作失败", "markNoIntentionConfirm": "确认标记为无意向", "applicationInfo": "申请信息", "defeatReason": "标记无意向原因", "selectDefeatReason": "请选择标记无意向原因", "defeatDetails": "详细说明", "inputDefeatDetails": "请输入详细说明", "applicationTime": "申请时间", "defeatReasonRequired": "必须选择标记无意向原因", "defeatDetailsRequired": "必须填写详细说明", "defeatDetailsLength": "详细说明应在 10 到 500 个字符之间", "confirmMarkNoIntention": "确认标记", "markNoIntentionSubmitSuccess": "标记无意向申请已提交", "formValidationFailed": "表单校验失败", "followUpInfo": "跟进信息", "followUpType": "跟进方式", "selectFollowUpType": "请选择跟进方式", "followUpTime": "跟进时间", "selectFollowUpTime": "请选择跟进时间", "followUpContent": "跟进内容", "inputFollowUpContent": "请输入跟进内容", "followUpResult": "跟进结果", "inputFollowUpResult": "请输入跟进结果", "intentLevelAfterFollowUp": "跟进后意向", "selectIntentLevel": "请选择意向级别", "selectNextFollowUpTime": "请选择下次跟进时间", "followUpTypeRequired": "必须选择跟进方式", "followUpTimeRequired": "必须选择跟进时间", "followUpContentRequired": "必须填写跟进内容", "followUpContentLength": "跟进内容应在 10 到 500 个字符之间", "followUpResultRequired": "必须填写跟进结果", "followUpResultLength": "跟进结果应在 5 到 300 个字符之间", "intentLevelRequired": "必须选择跟进后的意向级别", "idTypePlaceholder": "请选择证件类型", "idCard": "身份证", "passport": "护照", "residencePermit": "居住证", "prospectIntention": "潜客意向", "selectIntentVariant": "请选择意向配置", "selectIntentColor": "请选择意向颜色", "followUpRecord": "跟进记录", "followUpMethod": "跟进方式", "selectFollowUpMethod": "请选择跟进方式", "intentionLevel": "意向级别", "intentionLevelPlaceholder": "请选择意向级别", "nextFollowUpTimePlaceholder": "系统将根据意向级别自动计算", "followUpDetails": "跟进详情", "followUpDetailsPlaceholder": "请描述跟进详情...", "inputFollowUpDetailsRequired": "请填写跟进详情", "selectIntentionLevelRequiredFollowUp": "请选择跟进后的意向级别", "selectNextFollowUpTimeRequired": "请选择下次跟进时间", "selectIntentionModelRequired": "请选择意向车型", "selectIntentionVariantRequired": "请选择意向配置", "selectIntentionColorRequired": "请选择意向颜色"}, "orders": {"title": "销售订单", "list": {"title": "销售订单列表", "search": {"title": "搜索条件", "orderNumber": "订单编号", "buyerName": "购车人姓名", "buyerPhone": "购车人电话", "buyerType": "购车人类型", "model": "车型", "orderStatus": "订单状态", "approvalStatus": "审批状态", "paymentStatus": "支付状态", "insuranceStatus": "保险状态", "loanApprovalStatus": "贷款审批状态", "jpjRegistrationStatus": "JPJ注册状态", "createTime": "创建时间", "ordererName": "下单人姓名", "ordererPhone": "下单人电话", "search": "搜索", "reset": "重置"}, "columns": {"orderNumber": "订单编号", "createTime": "创建时间", "customerName": "客户姓名", "customerPhone": "客户电话", "buyerName": "购车人姓名", "buyerPhone": "购车人电话", "buyerType": "购车人类型", "model": "车型", "variant": "配置", "color": "颜色", "vin": "VIN码", "paymentMethod": "付款方式", "orderStatus": "订单状态", "approvalStatus": "审批状态", "paymentStatus": "支付状态", "insuranceStatus": "保险状态", "jpjRegistrationStatus": "JPJ注册状态", "totalAmount": "总金额"}, "actions": {"view": "查看", "edit": "编辑", "delete": "删除", "submitApproval": "提交审核", "cancelOrder": "取消订单", "create": "创建订单"}}, "detail": {"title": "订单详情", "orderNumber": "订单编号", "createTime": "创建时间", "orderStatus": "订单状态", "approvalStatus": "审批状态", "paymentStatus": "支付状态", "insuranceStatus": "保险状态", "jpjRegistrationStatus": "JPJ注册状态", "customerInfo": "客户信息", "ordererName": "下单人姓名", "ordererPhone": "下单人电话", "buyerName": "购车人姓名", "buyerPhone": "购车人电话", "buyerIdType": "证件类型", "buyerIdNumber": "证件号码", "buyerEmail": "邮箱", "buyerAddress": "地址", "buyerState": "州", "buyerCity": "城市", "buyerPostcode": "邮编", "buyerType": "购车人类型", "storeInfo": "门店信息", "storeRegion": "门店区域", "storeCity": "门店城市", "storeName": "门店名称", "salesConsultantName": "销售顾问", "model": "车型", "variant": "配置", "color": "颜色", "salesSubtotal": "车辆价格", "numberPlatesFee": "车牌费", "vin": "VIN码", "vehicleInfoTab": "车辆信息", "invoicingInfoTab": "开票信息", "rightsInfoTab": "服务&权益信息", "paymentInfoTab": "付款信息", "insuranceInfoTab": "保险信息", "otrFeesTab": "OTR费用", "purchaseInfo": "购车信息"}, "edit": {"title": "编辑订单", "orderNumber": "订单编号", "saveSuccess": "保存成功", "saveFailed": "保存失败"}}, "create": {"title": "创建订单", "customerInformation": "客户信息", "dealershipInformation": "经销商信息", "vehicleInformation": "车辆信息", "invoiceInformation": "开票信息", "paymentInformation": "支付信息", "rightsSelection": "权益选择", "tabs": {"vehicle": "车辆信息", "invoice": "开票信息", "payment": "支付信息", "rights": "权益选择"}, "ordererNameLabel": "下单人姓名", "ordererPhoneLabel": "下单人电话", "buyerNameLabel": "购车人姓名", "buyerPhoneLabel": "购车人电话", "buyerIdTypeLabel": "证件类型", "buyerIdNumberLabel": "证件号码", "buyerEmailLabel": "邮箱", "buyerAddressLabel": "地址", "buyerStateLabel": "州", "buyerCityLabel": "城市", "buyerPostcodeLabel": "邮编", "buyerTypeLabel": "购车人类型", "regionLabel": "区域", "dealerCityLabel": "经销商城市", "dealershipLabel": "经销商", "salesAdvisorLabel": "销售顾问", "modelLabel": "车型", "variantLabel": "配置", "colorLabel": "颜色", "vinLabel": "车架号", "invoiceTypeLabel": "开票类型", "invoiceNameLabel": "开票名称", "invoicePhoneLabel": "开票电话", "invoiceAddressLabel": "开票地址", "paymentMethodLabel": "支付方式", "depositAmountLabel": "定金金额", "loanAmountLabel": "贷款金额", "loanTermLabel": "贷款期数", "loanApprovalStatusLabel": "贷款审批状态", "insuranceNotesLabel": "保险备注", "selectRights": "选择权益", "selectedRights": "已选权益", "rightsName": "权益名称", "rightsValue": "权益价值", "rightsDescription": "权益描述", "invoiceTypes": {"individual": "个人", "company": "企业"}, "loanTerms": {"12": "12期", "24": "24期", "36": "36期", "48": "48期", "60": "60期", "72": "72期"}, "placeholders": {"countryCode": "区号", "ordererName": "请输入下单人姓名", "ordererPhone": "请输入下单人电话", "buyerName": "请输入购车人姓名", "buyerPhone": "请输入购车人电话", "buyerIdType": "请选择证件类型", "buyerIdNumber": "请输入证件号码", "buyerEmail": "请输入邮箱", "buyerAddress": "请输入地址", "buyerState": "请输入州", "buyerCity": "请输入城市", "buyerPostcode": "请输入邮编", "buyerType": "请选择购车人类型", "region": "请选择区域", "dealerCity": "请选择经销商城市", "dealership": "请选择经销商", "salesAdvisor": "请选择销售顾问", "selectModel": "请选择车型", "selectVariant": "请选择配置", "selectColor": "请选择颜色", "vin": "请输入车架号", "selectInvoiceType": "请选择开票类型", "invoiceName": "请输入开票名称", "invoicePhone": "请输入开票电话", "invoiceAddress": "请输入开票地址", "selectPaymentMethod": "请选择支付方式", "depositAmount": "请输入定金金额", "loanAmount": "请输入贷款金额", "selectLoanTerm": "请选择贷款期数", "selectApprovalStatus": "请选择审核状态", "insuranceNotes": "请输入保险备注"}, "validation": {"ordererNameRequired": "请输入下单人姓名", "ordererPhoneRequired": "请输入下单人电话", "buyerNameRequired": "请输入购车人姓名", "buyerPhoneRequired": "请输入购车人电话", "modelRequired": "请选择车型", "colorRequired": "请选择颜色", "paymentMethodRequired": "请选择支付方式"}, "messages": {"saveSuccess": "订单创建成功", "saveFailed": "订单创建失败"}}, "orderApproval": {"salesConsultant": "SA Name", "salesConsultantPlaceholder": "请选择SA", "approvalTypeOptions": {"cancel_order": "取消审批", "modify_info": "修改审批"}, "approvalResultOptions": {"approved": "通过", "rejected": "驳回"}}, "vehicleAllocation": {"title": "车辆配车管理", "orderNumber": "订单号", "orderNumberPlaceholder": "请输入订单号", "customerName": "客户姓名", "customerNamePlaceholder": "请输入客户姓名", "customerPhone": "客户手机", "customerPhonePlaceholder": "请输入客户手机", "allocationStatus": "配车状态", "allocationStatusPlaceholder": "请选择配车状态", "allocated": "已配车", "unallocated": "未配车", "orderStatus": "订单状态", "orderStatusPlaceholder": "请选择订单状态", "vin": "VIN", "vinPlaceholder": "请输入VIN", "factoryOrderNumber": "工厂订单号", "factoryOrderNumberPlaceholder": "请输入工厂订单号", "allocationTime": "配车时间", "store": "门店", "storePlaceholder": "请选择门店", "salesConsultant": "销售顾问", "salesConsultantPlaceholder": "请选择销售顾问", "orderCreateTime": "订单创建时间", "model": "车型", "variant": "配置", "color": "颜色", "allocate": "配车", "cancel_allocate": "取消配车", "cancelAllocate": "取消配车", "records": "记录", "exportData": "导出数据", "confirmExport": "确定要导出当前筛选条件下的数据吗？", "allocationConfirm": "配车确认", "orderDetail": "订单详情", "availableVehiclesQuery": "可配车辆查询", "availableVehicles": "可配车辆列表", "warehouseName": "所在仓库", "inStockTime": "入库时间", "noVehicleSelected": "请选择一辆车进行配车", "vehicleConfigMismatch": "所选车辆的配置与订单不符，无法配车", "allocationSuccess": "配车成功", "allocationFailed": "配车失败", "confirmCancelAllocation": "确定要取消该订单的配车吗？", "cancelAllocationSuccess": "取消配车成功", "cancelAllocationFailed": "取消配车失败", "allocationRecordDetail": "配车记录详情", "allocationHistory": "配车历史", "operator": "操作人", "remarks": "备注", "orderStatuses": {"submitted": "已提交", "confirmed": "已确认", "cancel_review": "取消审核中", "cancel_approved": "取消已批准", "cancelled": "已取消", "ready_delivery": "准备交付", "delivered": "已交付"}, "operationTypes": {"allocate": "配车", "cancel_allocate": "取消配车", "view_detail": "查看详情", "system_auto_cancel": "系统自动取消"}, "processResults": {"success": "成功", "failed": "失败"}, "cancelReasonLabel": "取消原因", "cancelReasonPlaceholder": "请输入取消配车原因", "reasonRequired": "必须填写取消原因"}}