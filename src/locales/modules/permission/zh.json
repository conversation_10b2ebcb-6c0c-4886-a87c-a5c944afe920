{"store": {"title": "门店管理", "storeName": "门店名称", "storeCode": "门店编号", "parentStore": "上级门店", "storeType": "门店类型", "storeStatus": "门店状态", "storeAddress": "门店地址", "contactPerson": "联系人", "contactPhone": "联系电话", "manager": "负责人", "enterStoreName": "请输入门店名称", "enterStoreCode": "请输入门店编号", "selectParentStore": "请选择上级门店", "selectStoreType": "请选择门店类型", "selectStoreStatus": "请选择门店状态", "enterStoreAddress": "请输入门店地址", "enterContactPerson": "请输入联系人", "enterContactPhone": "请输入联系电话", "selectManager": "请选择负责人", "storeStatusNormal": "正常", "storeStatusDisabled": "禁用", "storeTypeMain": "主店", "storeTypeBranch": "分店", "storeTypeWarehouse": "仓库", "addStore": "新增门店", "editStore": "编辑门店", "deleteStore": "删除门店", "expandAll": "展开全部", "collapseAll": "折叠全部", "storeNameRequired": "请输入门店名称", "storeCodeRequired": "请输入门店编号", "storeTypeRequired": "请选择门店类型", "storeStatusRequired": "请选择门店状态", "contactPersonRequired": "请输入联系人", "contactPhoneRequired": "请输入联系电话", "addStoreSuccess": "新增门店成功", "addStoreFailed": "新增门店失败", "editStoreSuccess": "编辑门店成功", "editStoreFailed": "编辑门店失败", "deleteStoreSuccess": "删除门店成功", "deleteStoreFailed": "删除门店失败", "confirmDeleteStore": "确认删除此门店吗？", "cannotDeleteStoreWithChildren": "该门店下还有子门店，无法删除", "storeTypeHeadquarter": "总部", "province": "省份", "city": "城市", "district": "区域", "detailAddress": "详细地址", "storeProperties": "门店属性", "propertySales": "销售", "propertyAfterSales": "售后"}, "department": {"title": "部门管理", "departmentName": "部门名称", "departmentCode": "部门编号", "parentDepartment": "上级部门", "departmentLevel": "部门层级", "departmentStatus": "部门状态", "departmentType": "部门类型", "belongStore": "所属门店", "departmentHead": "部门负责人", "description": "部门描述", "enterDepartmentName": "请输入部门名称", "enterDepartmentCode": "请输入部门编号", "selectParentDepartment": "请选择上级部门", "selectDepartmentStatus": "请选择部门状态", "selectDepartmentType": "请选择部门类型", "selectBelongStore": "请选择所属门店", "selectDepartmentHead": "请选择部门负责人", "enterDescription": "请输入部门描述", "departmentStatusNormal": "正常", "departmentStatusDisabled": "禁用", "departmentTypeBusiness": "业务部门", "departmentTypeSupport": "支持部门", "departmentTypeManagement": "管理部门", "addDepartment": "新增部门", "editDepartment": "编辑部门", "deleteDepartment": "删除部门", "departmentNameRequired": "请输入部门名称", "departmentCodeRequired": "请输入部门编号", "departmentStatusRequired": "请选择部门状态", "departmentTypeRequired": "请选择部门类型", "belongStoreRequired": "请选择所属门店", "addDepartmentSuccess": "新增部门成功", "addDepartmentFailed": "新增部门失败", "editDepartmentSuccess": "编辑部门成功", "editDepartmentFailed": "编辑部门失败", "deleteDepartmentSuccess": "删除部门成功", "deleteDepartmentFailed": "删除部门失败", "confirmDeleteDepartment": "确认删除此部门吗？", "cannotDeleteDepartmentWithChildren": "该部门下还有子部门，无法删除"}, "menu": {"title": "菜单管理", "menuName": "菜单名称", "menuCode": "菜单编号", "menuType": "菜单类型", "parentMenu": "上级菜单", "menuIcon": "菜单图标", "menuPath": "菜单路径", "component": "组件路径", "permission": "权限标识", "sortOrder": "排序", "menuStatus": "菜单状态", "isVisible": "是否显示", "isCache": "是否缓存", "enterMenuName": "请输入菜单名称", "enterMenuCode": "请输入菜单编号", "selectMenuType": "请选择菜单类型", "selectParentMenu": "请选择上级菜单", "selectMenuIcon": "请选择菜单图标", "enterMenuPath": "请输入菜单路径", "enterComponent": "请输入组件路径", "enterPermission": "请输入权限标识", "enterSortOrder": "请输入排序", "selectMenuStatus": "请选择菜单状态", "menuTypeDirectory": "目录", "menuTypeMenu": "菜单", "menuTypeButton": "按钮", "menuStatusNormal": "正常", "menuStatusDisabled": "禁用", "addMenu": "新增菜单", "editMenu": "编辑菜单", "deleteMenu": "删除菜单", "refreshMenuCache": "刷新菜单缓存", "menuNameRequired": "请输入菜单名称", "menuCodeRequired": "请输入菜单编号", "menuTypeRequired": "请选择菜单类型", "menuPathRequired": "请输入菜单路径", "componentRequired": "请输入组件路径", "permissionRequired": "请输入权限标识", "sortOrderRequired": "请输入排序", "menuStatusRequired": "请选择菜单状态", "addMenuSuccess": "新增菜单成功", "addMenuFailed": "新增菜单失败", "editMenuSuccess": "编辑菜单成功", "editMenuFailed": "编辑菜单失败", "deleteMenuSuccess": "删除菜单成功", "deleteMenuFailed": "删除菜单失败", "refreshMenuCacheSuccess": "刷新菜单缓存成功", "refreshMenuCacheFailed": "刷新菜单缓存失败", "confirmDeleteMenu": "确认删除此菜单吗？", "cannotDeleteMenuWithChildren": "该菜单下还有子菜单，无法删除", "menuSide": "所属端", "selectMenuSide": "请选择所属端", "menuSideDealer": "店端", "menuSideFactory": "厂端", "menuSideRequired": "请选择所属端", "yes": "是", "no": "否"}, "role": {"title": "角色管理", "roleName": "角色名称", "roleCode": "角色编号", "roleStatus": "角色状态", "roleScope": "角色范围", "description": "角色描述", "menuPermissions": "菜单权限", "dataPermissions": "数据权限", "enterRoleName": "请输入角色名称", "enterRoleCode": "请输入角色编号", "selectRoleStatus": "请选择角色状态", "selectRoleScope": "请选择角色范围", "enterDescription": "请输入角色描述", "roleStatusNormal": "正常", "roleStatusDisabled": "禁用", "roleScopeAll": "全部数据权限", "roleScopeCustom": "自定义数据权限", "roleScopeDepartment": "部门数据权限", "roleScopeDepartmentAndBelow": "部门及以下数据权限", "roleScopeOnlyPersonal": "个人数据权限", "addRole": "新增角色", "editRole": "编辑角色", "deleteRole": "删除角色", "configMenuPermission": "配置菜单权限", "configDataPermission": "配置数据权限", "exportRole": "导出角色", "roleNameRequired": "请输入角色名称", "roleCodeRequired": "请输入角色编号", "roleSourceRequired": "请输入角色来源", "roleStatusRequired": "请选择角色状态", "roleScopeRequired": "请选择角色范围", "addRoleSuccess": "新增角色成功", "addRoleFailed": "新增角色失败", "editRoleSuccess": "编辑角色成功", "editRoleFailed": "编辑角色失败", "deleteRoleSuccess": "删除角色成功", "deleteRoleFailed": "删除角色失败", "configPermissionSuccess": "权限配置成功", "configPermissionFailed": "权限配置失败", "confirmDeleteRole": "确认删除此角色吗？", "cannotDeleteRoleInUse": "该角色正在使用中，无法删除", "expandCollapse": "展开/折叠", "selectAll": "全选/全不选", "parent-childLinkage": "父子联动", "roleSource": "角色来源", "selectRoleSource": "请选择角色来源", "roleSourceFactory": "厂端角色", "roleSourceStore": "门店角色", "configPermission": "权限设置"}, "user": {"title": "用户管理", "addUser": "新增用户", "editUser": "编辑用户", "username": "用户名", "fullName": "姓名", "phone": "手机号", "email": "邮箱", "userType": "用户类型", "userTypeFactory": "厂端", "userTypeStore": "店端", "entryDate": "入职日期", "userStatus": "用户状态", "userStatusNormal": "正常", "userStatusDisabled": "禁用", "primaryStore": "主门店", "enterUsername": "请输入用户名", "enterFullName": "请输入姓名", "enterPhone": "请输入手机号", "enterEmail": "请输入邮箱", "selectUserType": "请选择用户类型", "selectEntryDate": "请选择入职日期", "usernameRequired": "请输入用户名", "fullNameRequired": "请输入姓名", "userTypeRequired": "请选择用户类型", "entryDateRequired": "请选择入职日期", "emailFormatError": "邮箱格式不正确", "assignRoles": "分配角色", "currentUser": "当前用户", "addAssignment": "新增分配", "primaryRole": "主门店", "belongStore": "所属门店", "department": "部门", "roles": "角色", "position": "职位", "enterPosition": "请输入职位", "confirmDeleteUser": "确认删除用户 {fullName} 吗？", "editUserSuccess": "用户状态修改成功", "factoryUserRoleLimit": "厂端用户只能分配一个门店角色", "roleAssignmentIncomplete": "请完善门店和角色信息", "selectBelongStore": "请选择所属门店"}}