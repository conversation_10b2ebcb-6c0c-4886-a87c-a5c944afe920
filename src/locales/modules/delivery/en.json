{"title": "Delivery Management", "tabs": {"pending": "Pending Delivery", "completed": "Delivered"}, "search": {"customerName": "Customer Name", "customerNamePlaceholder": "Enter Customer Name", "orderNo": "Order No.", "orderNoPlaceholder": "Enter Order No.", "vin": "VIN", "vinPlaceholder": "Enter VIN"}, "table": {"orderNo": "Order No.", "customerName": "Customer Name", "customerPhone": "Customer Phone", "vehicleModel": "Vehicle Model", "vin": "VIN", "licensePlate": "License Plate", "deliveryDate": "Delivery Date", "actions": "Actions"}, "actions": {"prepare": "Prepare for Delivery", "deliver": "Confirm Delivery", "viewDetails": "View Details"}, "prepareModal": {"title": "Delivery Preparation", "pdiCheck": "PDI Check", "carWash": "Car Wash", "charging": "Charging", "documents": "Document Preparation", "other": "Other", "notes": "Notes", "notesPlaceholder": "Enter notes", "save": "Save"}, "deliverModal": {"title": "Confirm Delivery", "customerIdentity": "Customer Identity Verification", "customerIdentityPlaceholder": "Scan or enter ID number", "finalPayment": "Final Payment Confirmation", "finalPaymentAmount": "Final Payment Amount", "confirm": "Confirm Delivery"}, "messages": {"prepareSuccess": "Delivery preparation items saved successfully.", "deliverySuccess": "Vehicle delivered successfully!", "confirmDelivery": "Please confirm the customer information is correct and all vehicle preparations are complete. Are you sure you want to confirm the delivery?"}, "pageTitle": "Delivery Management", "searchTitle": "Search Criteria", "listTitle": "Delivery Order List", "deliveryNumber": "Delivery Number", "deliveryNumberPlaceholder": "Enter delivery number", "orderNumber": "Order Number", "orderNoPlaceholder": "Enter order number", "customerName": "Customer Name", "customerNamePlaceholder": "Enter customer name", "customerPhone": "Customer Phone", "customerMobilePlaceholder": "Enter customer phone", "vin": "VIN", "vinPlaceholder": "Enter VIN", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "dealerStore": "Dealer Store", "dealerStorePlaceholder": "Select dealer store", "salesConsultant": "Sales Consultant", "salesmanPlaceholder": "Enter sales consultant", "orderStatus": "Order Status", "orderStatusPlaceholder": "Select order status", "deliveryStatus": "Delivery Status", "deliveryStatusPlaceholder": "Select delivery status", "customerConfirmed": "Customer Confirmed", "customerConfirmedPlaceholder": "Select customer confirmation status", "confirmationType": "Confirmation Type", "confirmationTypePlaceholder": "Select confirmation type", "deliveryTime": "Delivery Time", "customerConfirmTime": "Customer Confirm Time", "startDate": "Start Date", "endDate": "End Date", "customerConfirmTimeStartPlaceholder": "Start Date", "customerConfirmTimeEndPlaceholder": "End Date", "invoiceTime": "Invoice Time", "actualDeliveryDate": "Actual Delivery Date", "deliveryNotes": "Delivery Notes", "signaturePhoto": "Signature Photo", "totalCount": "Total {count} records", "submitConfirm": "Submit Confirm", "deliveryConfirm": "Delivery Confirm", "orderStatusNormal": "Normal", "orderStatusCancelled": "Cancelled", "orderStatusPendingAllocation": "Pending Allocation", "orderStatusAllocating": "Allocating", "orderStatusAllocated": "Allocated", "orderStatusPendingDelivery": "Pending Delivery", "orderStatusDelivered": "Delivered", "statusPending": "Pending", "statusConfirming": "Confirming", "statusCompleted": "Completed", "confirmationTypeApp": "APP Confirmation", "confirmationTypeOffline": "Offline Confirmation", "fetchDataFailed": "Failed to fetch data", "detailNotFound": "Detail information not found", "fetchDetailFailed": "Failed to fetch detail", "printFeatureNotImplemented": "Print feature not implemented", "submitConfirmSuccess": "Submit confirmation successful", "submitConfirmFailed": "Submit confirmation failed", "deliveryConfirmSuccess": "Delivery confirmation successful", "deliveryConfirmFailed": "Delivery confirmation failed", "exportSuccess": "Export successful", "exportFailed": "Export failed", "none": "None", "detailTitle": "Delivery Details", "basicInfo": "Basic Information", "orderInfo": "Order Information", "vehicleInfo": "Vehicle Information", "deliveryInfo": "Delivery Information", "orderCreatorName": "Order Creator Name", "orderCreatorPhone": "Order Creator Phone", "customerType": "Customer Type", "idType": "ID Type", "idNumber": "ID Number", "address": "Address", "city": "City", "postcode": "Postcode", "state": "State", "orderCreateTime": "Order Create Time", "orderPaymentStatus": "Order Payment Status", "paymentMethod": "Payment Method", "warehouseName": "Warehouse Name", "productionDate": "Production Date", "entryTime": "Entry Time", "noDetailData": "No detail data available", "orderStatusPending": "Pending", "orderStatusConfirmed": "Confirmed", "statusProcessing": "Processing", "statusDelivered": "Delivered", "statusCancelled": "Cancelled", "confirmationTypeOnline": "Online Confirmation", "confirmationTypePhone": "Phone Confirmation", "deliveryConfirmTitle": "Delivery Confirmation", "orderInfoTitle": "Order Information", "deliveryInfoTitle": "Delivery Information", "deliveryTimeRequired": "Please select delivery time", "deliveryTimePlaceholder": "Please select delivery time", "customerSignatureConfirm": "Customer Signature Confirmation", "signaturePhotoRequired": "Please upload customer signature photo", "signaturePhotoFormatError": "Signature photo must be JPG/PNG format!", "signaturePhotoSizeError": "Signature photo size cannot exceed 5MB!", "signatureUploadFailed": "Signature photo upload failed", "uploadSignaturePhoto": "Click to upload customer signature photo", "uploadSignaturePhotoTip": "Supports JPG, PNG formats, file size not exceeding 5MB", "deliveryNotesLabel": "Delivery Notes", "deliveryNotesPlaceholder": "Please enter delivery notes (optional)", "submitConfirmTitle": "Submit Confirmation", "submitConfirmQuestion": "Are you sure to submit the following delivery order?", "submitConfirmNote": "After submission, the delivery status will be change, waiting for customer confirmation or sales consultant to complete delivery confirmation.", "statusPendingDelivery": "Pending Delivery", "statusPendingConfirm": "Pending Confirmation", "confirmSubmit": "Confirm Submit", "noOrderSelected": "No order selected", "deliveryNumberLabel": "Delivery Number", "orderNumberLabel": "Order Number", "customerNameLabel": "Customer Name", "customerPhoneLabel": "Phone Number", "modelLabel": "MODEL", "variantLabel": "VARIANT", "colorLabel": "COLOR", "deliveryTimeLabel": "Delivery Time", "deliveryNotesTitle": "Delivery Notes", "exportSettingsTitle": "Export Settings", "exportFormat": "Export Format", "exportFormatRequired": "Please select export format", "exportRange": "Export Range", "exportRangeRequired": "Please select export range", "exportRangeCurrentPage": "Current Page Data", "exportRangeFilteredResult": "Filtered Results", "exportRangeAllData": "All Data", "exportTimeRange": "Export Time Range"}