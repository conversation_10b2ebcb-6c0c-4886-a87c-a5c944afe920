{"salesOrderList": "Sales Order List", "title": "Sales Order List", "breadcrumb": "Customer Order Management / Sales Order List", "searchForm": "Search Conditions", "operationButtons": "Operations", "orderList": "Order List", "buyerName": "Buyer Name", "buyerNamePlaceholder": "Enter buyer name", "buyerPhone": "Buyer Phone", "buyerPhonePlaceholder": "Enter buyer phone number", "buyerType": "Buyer Type", "buyerTypePlaceholder": "Select buyer type", "model": "Model", "modelPlaceholder": "Select model", "orderNumber": "Order Number", "orderNumberPlaceholder": "Enter order number", "orderStatus": "Order Status", "orderStatusPlaceholder": "Select order status", "approvalStatus": "Approval Status", "approvalStatusPlaceholder": "Select approval status", "paymentStatus": "Payment Status", "paymentStatusPlaceholder": "Select payment status", "createTime": "Order Create Time", "createTimeRange": "Create Time Range", "insuranceStatus": "Insurance Status", "insuranceStatusPlaceholder": "Select insurance status", "loanApprovalStatus": "<PERSON>an <PERSON>al <PERSON>", "loanApprovalStatusPlaceholder": "Select loan approval status", "jpjRegistrationStatus": "JPJ Registration Status", "jpjRegistrationStatusPlaceholder": "Select JPJ registration status", "index": "Index", "ordererName": "Orderer Name", "ordererNamePlaceholder": "Enter orderer name", "ordererPhone": "Orderer Phone", "ordererPhonePlaceholder": "Enter orderer phone", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "vin": "VIN", "paymentMethod": "Payment Method", "totalAmount": "Total Amount", "pageTitle": "Sales Order List", "searchTitle": "Search Conditions", "batchOperation": "Batch Operation", "listTitle": "Sales Order List", "totalCount": "Total Count", "detailTitle": "Sales Order Detail", "statusSubmitted": "Submitted", "statusConfirmed": "Confirmed", "statusCancelPending": "Cancel Pending", "statusCancelApproved": "Cancel Approved", "statusCancelled": "Cancelled", "statusPendingDelivery": "Pending Delivery", "statusDelivered": "Delivered", "buyerTypes": {"individual": "Individual", "company": "Company"}, "models": {"AXIA": "AXIA", "BEZZA": "BEZZA", "MYVI": "MYVI"}, "orderStatuses": {"submitted": "Submitted", "confirmed": "Confirmed", "cancel_pending": "Cancel Pending", "cancel_approved": "Cancel Approved", "cancelled": "Cancelled", "pending_delivery": "Pending Delivery", "delivered": "Delivered"}, "approvalStatuses": {"pending_approval": "Pending Approval", "approved": "Approved"}, "paymentStatuses": {"pending_deposit": "Pending Deposit", "deposit_paid": "<PERSON><PERSON><PERSON><PERSON>", "pending_balance": "Pending Balance", "balance_paid": "Balance Paid", "refunding": "Refunding", "refunded": "Refunded"}, "insuranceStatuses": {"not_insured": "Not Insured", "pending": "Pending", "insuring": "Insuring", "insured": "Insured"}, "loanApprovalStatuses": {"pending_review": "Pending Review", "approved": "Approved", "rejected": "Rejected"}, "jpjRegistrationStatuses": {"pending_registration": "Pending Registration", "pending": "Pending", "registering": "Registering", "registered": "Registered", "registration_failed": "Registration Failed"}, "paymentMethods": {"full_payment": "Full Payment", "installment": "Installment"}, "viewDetail": "Detail", "editOrder": "Edit", "returnToList": "Return to List", "exportSuccess": "Export successful", "exportFailed": "Export failed", "noDataToExport": "No data to export", "confirmExportTitle": "Confirm Export", "confirmExportMessage": "Are you sure to export all order data under current filter conditions?", "orderDetail": "Order Detail", "customerInfo": "Customer Information", "storeInfo": "Store Information", "purchaseInfo": "Purchase Information", "vehicleInfoTab": "Vehicle Info", "invoicingInfoTab": "Invoicing Information", "rightsInfoTab": "Services & Rights", "paymentInfoTab": "Payment Info", "insuranceInfoTab": "Insurance Info", "otrFeesTab": "OTR Fees Information", "changeRecords": "Order Change Records", "buyerIdType": "Buyer ID Type", "buyerIdNumber": "Buyer ID Number", "buyerEmail": "Buyer Email", "buyerAddress": "Buyer Address", "buyerState": "Buyer State", "buyerCity": "Buyer City", "buyerPostcode": "Buyer Postcode", "storeRegion": "Store Region", "storeCity": "Store City", "storeName": "Store Name", "salesConsultantName": "Sales Consultant", "salesSubtotal": "Sales Subtotal (including GASA, consumption tax, sales tax)", "numberPlatesFee": "Number Plates Fee", "vinCode": "VIN Code", "accessoryInfo": "Accessory Information", "accessoryCategory": "Category", "accessoryName": "Name", "unitPrice": "Unit Price", "quantity": "Quantity", "totalPrice": "Total Price", "accessoriesTotalAmount": "Accessories Total Amount", "invoicingType": "Invoicing Type", "invoicingName": "Invoicing Name", "invoicingPhone": "Invoicing Phone", "invoicingAddress": "Invoicing Address", "rightsInfo": "Service & Rights Information", "rightCode": "Right Code", "rightName": "Name", "rightMode": "Mode", "discountAmount": "Discount Amount", "effectiveDate": "Effective Date", "expiryDate": "Expiry Date", "rightsDiscountAmount": "Rights Discount Total Amount", "loanApprovalStatusField": "<PERSON>an <PERSON>al <PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "loanAmount": "<PERSON><PERSON>", "balanceAmount": "Balance Amount", "insuranceInfo": "Insurance Information", "policyNumber": "Policy Number", "insuranceType": "Insurance Type", "insuranceCompany": "Insurance Company", "insurancePrice": "Insurance Price", "insuranceTotalAmount": "Insurance Total Amount", "insuranceNotes": "Insurance Notes", "otrFeesInfo": "On The Road Registration Fees", "ticketNumber": "Ticket Number", "feeItem": "<PERSON><PERSON>em", "feePrice": "<PERSON><PERSON>", "otrFeesTotalAmount": "OTR Fees Total Amount", "changeRecordIndex": "Index", "originalContent": "Original Content", "changedContent": "Changed Content", "operator": "Operator", "operationTime": "Operation Time", "totalInvoiceAmount": "Total Invoice Amount", "remainingAmount": "Remaining Amount", "editOrderTitle": "Sales Order Edit", "editPage": {"title": "Sales Order Edit", "lastUpdated": "Last Updated", "colors": {"white": "White", "black": "Black", "red": "Red"}, "addAccessory": "Add Accessory", "paymentMethods": {"loan": "Loan", "cash": "Cash", "bank_transfer": "Bank Transfer"}, "changeRecord": {"action": "Action", "details": "Details"}, "messages": {"addRightsSuccess": "Rights added successfully!", "rightsExist": "These rights already exist.", "confirmCancel": "Are you sure you want to cancel this order?", "orderCancelled": "Order has been cancelled.", "pushToInsuranceWip": "Push to insurance system feature is under development.", "orderSaved": "Order has been saved.", "missingOrderNo": "Missing order number, returning to list page."}}, "salesOrderEdit": {"title": "Sales Order Edit", "colorChangeNotice": "Color changes require review", "loanTermLabel": "<PERSON><PERSON> (Months)", "fullPaymentAmount": "Full Payment Amount", "placeholders": {"selectColor": "Please select a color", "selectPaymentMethod": "Please select a payment method", "selectApprovalStatus": "Please select an approval status", "selectLoanTerm": "Please select a loan term", "insuranceNotes": "Please enter insurance notes"}, "validation": {"selectColor": "Please select a color", "selectPaymentMethod": "Please select a payment method", "enterLoanAmount": "Please enter the loan amount"}, "colors": {"white": "White", "black": "Black", "silver": "Silver", "red": "Red", "blue": "Blue", "grey": "Grey"}, "loanTerms": {"12": "12 months", "24": "24 months", "36": "36 months", "48": "48 months", "60": "60 months", "72": "72 months"}, "messages": {"fetchDetailFailed": "Failed to fetch order details", "saveSuccess": "Saved successfully", "colorChangeAlert": "You have changed the vehicle color, the order will be submitted for review", "saveFailed": "Save failed"}}, "rightsSelectionDialog": {"title": "Select Rights", "rightCodePlaceholder": "Enter right code", "rightNamePlaceholder": "Enter right name", "availableRights": "Available Rights", "totalCount": "Total {count} items", "selectedRights": "Selected Rights", "selectedCount": "Selected {count} items", "noRightsSelected": "No rights selected", "totalDiscountAmount": "Total Discount Amount", "fetchRightsError": "Failed to fetch available rights", "confirmButtonText": "Confirm ({count})"}, "personalDetails": "Customer Information - Personal Details", "preferredOutlet": "Store Information - Preferred Outlet & Sales Advisor", "purchaseDetails": "Purchase Information - Purchase Details", "addRights": "Add Rights", "selectRights": "Select Rights", "rightCodeSearch": "Right Code Search", "rightNameSearch": "Right Name Search", "selectAll": "Select All", "addSelected": "Add Selected", "deleteRight": "Delete Right", "confirmDeleteRight": "Are you sure to delete this right?", "pushToInsurance": "Push to Insurance System", "insurancePushed": "Insurance Pushed", "confirmPushInsurance": "Are you sure to push to insurance system?", "pushInsuranceSuccess": "Push to insurance system successful", "pushInsuranceFailed": "Push to insurance system failed", "submitDelivery": "Submit Delivery", "confirmSubmitDelivery": "Are you sure to submit delivery application?", "submitDeliverySuccess": "Submit delivery successful", "submitDeliveryFailed": "Submit delivery failed", "deliveryConditionsNotMet": "Delivery conditions not met", "colorChangeSubmitted": "Color change submitted for review", "roadTax": "Road Tax", "registrationFee": "Registration/Transfer Fee", "ownershipClaimFee": "Ownership C<PERSON><PERSON>", "interchangeFee": "Interchange Fee", "otrFeeTotal": "Registration Fee Total", "loanTerm": "<PERSON><PERSON> (Months)", "loanTermPlaceholder": "Enter loan term", "loanAmountPlaceholder": "Enter loan amount", "orderDetailTitle": "Sales Order Detail", "backToList": "Back to List", "orderNumberLabel": "Order Number", "createTimeLabel": "Creation Time", "customerInformation": "Customer Information", "ordererNameLabel": "Orderer Name", "ordererPhoneLabel": "Orderer Phone", "buyerNameLabel": "Buyer Name", "buyerPhoneLabel": "Buyer Phone", "buyerIdTypeLabel": "Buyer ID Type", "buyerIdNumberLabel": "Buyer ID Number", "buyerEmailLabel": "Buyer Email", "buyerAddressLabel": "Buyer Address", "buyerStateLabel": "Buyer State", "buyerCityLabel": "Buyer City", "buyerPostcodeLabel": "Buyer Postcode", "buyerTypeLabel": "Buyer Type", "dealershipInformation": "Dealership Information", "regionLabel": "Region", "cityLabel": "City", "dealershipLabel": "Dealership", "salesAdvisorLabel": "Sales Advisor", "purchaseInformation": "Purchase Information", "invoiceInfoTab": "Invoice Info", "otrInfoTab": "OTR Fees", "vehicleInformation": "Vehicle Information", "modelLabel": "Model", "variantLabel": "<PERSON><PERSON><PERSON>", "colorLabel": "Color", "salesSubtotalLabel": "Sales Subtotal (incl. GASA, excise, sales tax)", "numberPlatesFeeLabel": "Number Plates Fee", "vinLabel": "VIN", "accessoriesInformation": "Accessories Information", "invoiceInformation": "Invoice Information", "invoiceTypeLabel": "Invoice Type", "invoiceNameLabel": "Invoice Name", "invoicePhoneLabel": "Invoice Phone", "invoiceAddressLabel": "Invoice Address", "rightsInformation": "Services & Rights Information", "discountPrice": "Discount Price", "rightsDiscountTotalAmount": "Rights Discount Total Amount", "paymentInformation": "Payment Information", "paymentMethodLabel": "Payment Method", "loanStatusLabel": "<PERSON>an <PERSON>al <PERSON>", "depositAmountLabel": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "loanAmountLabel": "<PERSON><PERSON>", "finalPaymentLabel": "Final Payment", "insuranceInformation": "Insurance Information", "remarksLabel": "Remarks", "otrInformation": "OTR Fee Information", "otrBillNumber": "<PERSON>", "otrFeeItem": "<PERSON><PERSON>em", "otrFeePrice": "<PERSON><PERSON>", "otrTotalAmount": "OTR Total Amount", "changeHistory": "Order Change History", "operationContent": "Operation Content", "details": "Details", "totalInvoicePrice": "Total Invoice Price", "remainingReceivable": "Remaining Receivable", "missingOrderNumber": "Missing order number, returning to list page.", "customerType": {"personal": "Personal", "company": "Company"}, "invoiceType": {"personal": "Personal", "company": "Company"}, "paymentType": {"loan": "Loan", "cash": "Cash", "bank_transfer": "Bank Transfer"}}