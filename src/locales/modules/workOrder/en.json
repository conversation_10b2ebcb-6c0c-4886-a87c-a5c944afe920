{"title": "Work Order List", "number": "Work Order No.", "priority": "Priority", "type": "Work Order Type", "isClaim": "<PERSON>", "isOutsourced": "Is Outsourced", "status": "Status", "paymentStatus": "Payment Status", "senderName": "Sender Name", "senderPhone": "Sender Phone", "licensePlate": "License Plate", "serviceAdvisor": "Service Advisor", "createdTime": "Created Time", "customerInfo": "Customer Information", "vehicleInfo": "Vehicle Information", "workOrderInfo": "Work Order Information", "appointmentCustomer": "Appointment Customer", "appointmentPhone": "Appointment Phone", "vinCode": "VIN Code", "remarks": "Remarks", "customerSource": "Customer Source", "autoFillOrManual": "Auto-fill or Manual Input", "projectSelection": "Project Selection", "suggestedProjects": "Suggested Projects", "laborDetails": "Labor Details", "addLabor": "Add Labor", "clearAll": "Clear All", "itemType": "Item Type", "itemCode": "Item Code", "itemName": "Item Name", "hasAdditional": "Has <PERSON>", "standardHours": "Standard Hours", "laborRate": "Labor Rate", "subtotal": "Subtotal", "laborTotal": "Labor Total", "laborTotalAmount": "Total Labor Amount", "partsDetails": "Parts Details", "addParts": "Add Parts", "partsName": "Parts Name", "availableStock": "Available Stock", "quantity": "Quantity", "unitPrice": "Unit Price", "partsTotal": "Parts Total", "partsTotalAmount": "Total Parts Amount", "unit": "pcs", "costSummary": "Cost Summary", "laborAmount": "Labor Amount", "partsAmount": "Parts Amount", "additionalAmount": "Additional Amount", "workOrderTotal": "Work Order Total", "saveOnly": "Save Only", "saveAndPush": "Save & Push", "edit": "Edit", "create": "Create", "detail": "Details", "basicInfo": "Basic Information", "registrationTime": "Registration Time", "settlementNumber": "Settlement No.", "startTime": "Start Time", "endTime": "End Time", "estimatedHours": "Estimated Hours", "actualHours": "Actual Hours", "qcStatus": "QC Status", "qcNumber": "QC No.", "confirmationMethod": "Confirmation Method", "confirmationTime": "Confirmation Time", "warrantyStatus": "Warranty Status", "warrantyExpiry": "<PERSON>ranty Expiry", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "balanceAmount": "Balance Amount", "operationLogs": "Operation Logs", "operationType": "Operation Type", "operator": "Operator", "operationTime": "Operation Time", "operationDescription": "Operation Description", "statistics": "Statistics", "placeholders": {"workOrderNumber": "Enter Work Order No.", "customerName": "Enter Customer Name", "customerPhone": "Enter Customer Phone", "licensePlate": "Enter License Plate", "selectServiceAdvisor": "Select Service Advisor", "selectDateRange": "Select Date Range", "senderName": "Enter Sender Name", "senderPhone": "Enter Sender Phone", "vinCode": "Enter VIN Code", "modelConfigColor": "Enter Model/Config/Color", "remarks": "Enter Remarks", "searchProject": "Search Project Name or Code"}, "priorities": {"urgent": "<PERSON><PERSON>", "normal": "Normal"}, "types": {"repair": "Repair", "maintenance": "Maintenance", "insurance": "Insurance", "claim": "<PERSON><PERSON><PERSON>"}, "customerSources": {"appointment": "Appointment", "walkin": "Walk-in", "walk_in": "Walk-in"}, "statuses": {"draft": "Draft", "pending_confirmation": "Pending Confirmation", "confirmed": "Confirmed", "pending_assignment": "Pending Assignment", "pending_start": "Pending Start", "in_progress": "In Progress", "pending_qc": "Pending QC", "pending_settlement": "Pending Settlement", "rework_required": "Rework Required", "completed": "Completed", "cancelled": "Cancelled", "additional_pending": "Additional Items Pending", "waiting_approval": "Waiting for <PERSON><PERSON><PERSON><PERSON>", "waiting_parts": "Waiting for Parts"}, "paymentStatuses": {"unpaid": "Unpaid", "deposit_paid": "<PERSON><PERSON><PERSON><PERSON>", "paid": "Paid", "refunding": "Refunding", "refunded": "Refunded", "pending": "Pending"}, "qcStatuses": {"pending": "Pending QC", "passed": "QC Passed", "failed": "QC Failed", "rework": "Rework"}, "confirmationMethods": {"phone": "Phone Confirmation", "sms": "SMS Confirmation", "wechat": "WeChat Confirmation", "onsite": "On-site Confirmation"}, "exportWorkOrder": "Export Work Orders", "modelConfigColor": "Model/Config/Color", "totalAmount": "Total Amount", "technician": "Technician", "hasAdditionalItems": "Has Additional Items", "addItem": "Add Item", "submitApproval": "Submit for Approval", "projectItems": {"oilChange": "Oil Change", "filterChange": "Filter Change", "brakepadReplacement": "<PERSON><PERSON><PERSON> Pad Replacement", "tireReplacement": "Tire Replacement", "engineDiagnosis": "Engine Diagnosis"}, "validation": {"senderNameRequired": "Please enter sender name", "senderPhoneRequired": "Please enter sender phone", "senderPhoneFormat": "Please enter valid phone number format", "licensePlateRequired": "Please enter license plate", "vinCodeRequired": "Please enter VIN code", "vinCodeLength": "VIN code must be 17 characters long", "modelConfigColorRequired": "Please enter model/config/color", "priorityRequired": "Please select work order priority", "customerSourceRequired": "Please select customer source", "workOrderTypeRequired": "Please select work order type", "atLeastOneItem": "Please add at least one labor or parts item"}, "messages": {"confirmCancel": "Are you sure you want to cancel work order {workOrderNumber}?", "enterLicensePlate": "Please enter license plate", "vehicleInfoQuerySuccess": "Vehicle information query successful", "vehicleInfoNotFound": "Vehicle information not found, please enter manually", "selectProjectFirst": "Please select a project first", "projectExists": "Project already exists, do not add duplicate", "clearAllConfirm": "Are you sure you want to clear all items?", "stockValidationFailed": "Parts stock validation failed", "saveSuccess": "Save successful", "pushSuccess": "Push successful", "loadWorkOrderFailed": "Failed to load work order details"}, "createWorkOrderModal": {"title": "Create Work Order", "inspectionInfo": "Inspection Form Info", "customerInfo": "Customer Info", "vehicleInfo": "Vehicle Info", "workOrderBasic": "Work Order Basic Info", "projectSelection": "Project Selection", "laborItems": "Labor Items", "partsItems": "Parts Items", "costSummary": "Cost Summary", "form": {"vehicleModel": "Vehicle Model", "vehicleConfig": "Configuration", "mileage": "Mileage", "vehicleAge": "Vehicle Age", "workOrderType": "Work Order Type", "priority": "Priority", "remarks": "Remarks", "laborCode": "Labor Code", "laborName": "Labor Name", "isClaim": "<PERSON><PERSON><PERSON>", "isAdded": "Added", "standardHours": "Std. Hours", "unitPrice": "Unit Price", "subtotal": "Subtotal", "laborCost": "Labor Cost", "partName": "Part Name", "availableStock": "Avail. Stock", "quantity": "Quantity", "partsCost": "Parts Cost", "totalAmount": "Total Amount"}, "workOrderTypes": {"maintenance": "Maintenance", "repair": "Repair", "claim": "<PERSON><PERSON><PERSON>"}, "priorities": {"normal": "Normal", "urgent": "<PERSON><PERSON>"}, "placeholders": {"enterRemarks": "Please enter remarks", "searchProject": "Search by project name or code...", "selectWorkOrderType": "Please select a work order type"}, "buttons": {"searchAdd": "Search & Add", "addLabor": "Add Labor", "addParts": "Add Parts", "clearAll": "Clear All", "saveDraft": "Save Draft", "createAndNotify": "Create & Notify Customer"}, "statistics": {"totalLaborHours": "Total Labor Hours", "hours": "hours", "yuan": "CNY", "totalPartsCount": "Total Parts Count", "pieces": "pcs", "km": "km", "months": "months"}, "messages": {"duplicateProject": "Project already exists. Do not add it again.", "projectAdded": "Project added successfully.", "confirmClearAll": "Are you sure you want to clear all items?", "projectRemoved": "Project removed.", "quantityExceeded": "Quantity cannot exceed available stock.", "saveDraftSuccess": "Work order draft saved successfully.", "createSuccess": "Work order created successfully.", "confirmCreateAndNotify": "Are you sure you want to create the work order and notify the customer?", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "addAtLeastOneItem": "Please add at least one labor or parts item", "useSearchForLabor": "Please use the search function to add labor items", "useSearchForParts": "Please use the search function to add parts items", "autoGeneratedRemarks": "Created based on inspection form {inspectionNo}"}, "mockData": {"oilChange": "Oil Change", "oilFilter": "Oil Filter"}}}