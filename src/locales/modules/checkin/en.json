{"checkinList": "Check-in Records", "checkinIdPlaceholder": "Enter record number", "licensePlate": "License Plate", "licensePlatePlaceholder": "Enter license plate", "vin": "VIN Number", "vinPlaceholder": "Enter VIN number", "repairPersonName": "Repair Person Name", "repairPersonNamePlaceholder": "Enter repair person name", "repairPersonPhone": "Repair Person Phone", "repairPersonPhonePlaceholder": "Enter repair person phone", "createdAt": "Created At", "createdAtPlaceholder": "Select create date range", "export": "Export", "createRecord": "Create Record", "id": "No.", "checkinId": "Record No.", "vehicleModel": "Vehicle Model", "vehicleConfiguration": "Configuration", "color": "Color", "mileage": "Mileage", "mileagePlaceholder": "Enter mileage", "mileageUnit": "km", "serviceAdvisor": "Service Advisor", "relatedRepairOrderId": "Related RO No.", "serviceType": "Service Type", "notes": "Notes", "viewDetails": "View Details", "edit": "Edit", "delete": "Delete", "createRepairOrder": "Create RO", "addCheckinRecord": "Add Check-in Record", "editCheckinRecord": "Edit Check-in Record", "vehicleInfoNotFound": "No vehicle info found", "vehicleInfoAutoFill": "Auto-filled or manual entry", "vehicleAge": "Vehicle Age (Months)", "ownerInfo": "Owner Info", "serviceTypePlaceholder": "Select service type", "notesPlaceholder": "Enter notes", "save": "Save", "cancel": "Cancel", "serviceTypeOptions": {"repair": "Repair", "maintenance": "Maintenance", "inspection": "Inspection", "paint": "Paint"}, "repairOrderAlreadyExists": "Repair order already exists for this check-in record.", "repairOrderCreatedSuccess": "Repair order created successfully!", "confirmCreateRepairOrder": "Are you sure to create a repair order for record {checkinId}?", "vehicleInfo": "Vehicle Information", "customerInfo": "Customer Information", "ownerPhone": "Owner Phone", "updatedAt": "Updated At", "notFound": "Record not found"}