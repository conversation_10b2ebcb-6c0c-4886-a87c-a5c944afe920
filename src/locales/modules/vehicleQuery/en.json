{"title": "Vehicle Query", "search": {"vin": "VIN", "vinPlaceholder": "Enter VIN", "licensePlate": "License Plate", "licensePlatePlaceholder": "Enter License Plate", "customerName": "Customer Name", "customerNamePlaceholder": "Enter Customer Name", "customerPhone": "Customer Phone", "customerPhonePlaceholder": "Enter Customer Phone"}, "table": {"vin": "VIN", "licensePlate": "License Plate", "customerName": "Customer Name", "customerPhone": "Customer Phone", "vehicleModel": "Vehicle Model", "engineNo": "Engine No.", "registrationDate": "Registration Date", "lastMaintenanceDate": "Last Maintenance Date", "lastMaintenanceMileage": "Last Maintenance Mileage", "actions": "Actions"}, "actions": {"viewDetails": "View Details"}, "details": {"title": "Vehicle Details", "vin": "VIN", "licensePlate": "License Plate", "customerName": "Customer Name", "customerPhone": "Customer Phone", "vehicleModel": "Vehicle Model", "engineNo": "Engine No.", "registrationDate": "Registration Date", "lastMaintenanceDate": "Last Maintenance Date", "lastMaintenanceMileage": "Last Maintenance Mileage (km)", "insuranceExpireDate": "Insurance Expiration", "qualityAssuranceExpireDate": "QA Expiration", "color": "Color", "purchaseDate": "Purchase Date", "close": "Close"}, "vin": "VIN", "vinPlaceholder": "Enter VIN", "factoryOrderNo": "Factory Order No.", "factoryOrderNoPlaceholder": "Enter Factory Order No.", "warehouseName": "Warehouse Name", "warehouseNamePlaceholder": "Select Warehouse", "model": "Model", "modelPlaceholder": "Select Model", "variant": "<PERSON><PERSON><PERSON>", "variantPlaceholder": "Select Model First", "color": "Color", "colorPlaceholder": "Select Variant First", "lockStatus": "Lock Status", "lockStatusPlaceholder": "Select Lock Status", "locked": "Locked", "unlocked": "Unlocked", "invoiceStatus": "Invoice Status", "invoiceStatusPlaceholder": "Select Invoice Status", "invoiced": "Invoiced", "notInvoiced": "Not Invoiced", "invoiceDate": "Invoice Date", "storageDate": "Storage Date", "productionDate": "Production Date", "stockStatus": "Stock Status", "deliveryStatus": "Delivery Status", "deliveryDate": "Delivery Date", "stockStatusOptions": {"inStock": "In Stock", "allocated": "Allocated", "inTransit": "In Transit", "transferred": "Transferred"}, "lockStatusOptions": {"locked": "Locked", "unlocked": "Unlocked"}, "invoiceStatusOptions": {"invoiced": "Invoiced", "notInvoiced": "Not Invoiced"}, "deliveryStatusOptions": {"delivered": "Delivered", "notDelivered": "Not Delivered"}, "detailDialogTitle": "Vehicle Details", "exportDialogTitle": "Export Data", "exportFormat": "Export Format", "exportFormatPlaceholder": "Select Export Format", "excel": "Excel", "csv": "CSV", "exportScope": "Export <PERSON>", "exportCurrentPage": "Current Page Data", "exportAllData": "All Data", "exportSuccess": "Export {format} file successfully!", "fetchWarehouseFailed": "Failed to fetch warehouse information", "fetchConfigFailed": "Failed to fetch vehicle configuration"}