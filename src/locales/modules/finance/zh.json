{"wholeVehicleCollection": {"title": "整车收款管理", "search": {"orderNumber": "订单号", "buyerName": "购车人姓名", "buyerPhone": "购车人手机号", "orderStatus": "订单状态", "paymentStatus": "付款状态", "dateRange": "下单时间", "canInvoice": "可开票", "searchButton": "搜索", "resetButton": "重置", "exportButton": "导出"}, "table": {"orderNumber": "订单号", "buyerInfo": "购车人信息", "buyerName": "购车人姓名", "buyerPhone": "购车人手机号", "dealerInfo": "经销商信息", "dealerStoreName": "经销商门店", "salesConsultantName": "销售顾问", "vehicleInfo": "车辆信息", "vin": "车架号", "model": "车型", "variant": "版本", "color": "颜色", "orderCreateTime": "下单时间", "orderStatus": "订单状态", "paymentStatus": "付款状态", "priceInfo": "价格信息", "vehicleSalesPrice": "车辆销售价", "insuranceAmount": "保险费", "otrAmount": "OTR费用", "discountAmount": "优惠金额", "totalAmount": "总金额", "paidAmount": "已付金额", "unpaidAmount": "未付金额", "loanAmount": "贷款金额", "invoiceInfo": "开票信息", "canInvoice": "可开票", "invoiceTime": "开票时间", "invoiceNumber": "发票号", "actions": "操作", "viewDetail": "查看详情", "paymentOperation": "收退款操作"}, "status": {"orderStatus": {"submitted": "已提交", "cancelReviewing": "取消审核中", "cancelApproved": "取消审核通过", "cancelled": "已取消", "confirmed": "已确认", "pendingReview": "待审核", "reviewed": "已审核", "pendingDelivery": "待交车", "delivered": "已交车"}, "paymentStatus": {"pendingDeposit": "待支付定金", "depositPaid": "已支付定金", "refunding": "退款中", "refunded": "退款完成", "pendingFinalPayment": "待支付尾款", "finalPaymentPaid": "已支付尾款"}, "canInvoice": {"yes": "是", "no": "否"}}, "pagination": {"total": "共 {total} 条", "pageSize": "每页显示", "items": "条"}, "messages": {"loadSuccess": "数据加载成功", "loadError": "数据加载失败", "exportSuccess": "导出成功", "exportError": "导出失败", "noData": "暂无数据", "confirmDelete": "确认删除此记录吗？", "deleteSuccess": "删除成功", "deleteError": "删除失败"}}, "orderDetail": {"title": "订单详情", "tabs": {"basicInfo": "基本信息", "paymentRecords": "收退款记录"}, "basicInfo": {"orderInfo": "订单信息", "orderId": "订单ID", "orderNumber": "订单号", "orderCreateTime": "下单时间", "orderStatus": "订单状态", "paymentStatus": "付款状态", "paymentMethod": "付款方式", "loanAmount": "贷款金额", "canInvoice": "可开票", "invoiceTime": "开票时间", "invoiceNumber": "发票号", "ordererInfo": "下单人信息", "ordererName": "下单人姓名", "ordererPhone": "下单人手机号", "buyerInfo": "购车人信息", "buyerName": "购车人姓名", "buyerPhone": "购车人手机号", "buyerIdType": "证件类型", "buyerIdNumber": "证件号码", "buyerEmail": "邮箱", "buyerAddress": "地址信息", "buyerState": "州", "buyerCity": "城市", "buyerPostcode": "邮编", "buyerFullAddress": "详细地址", "dealerInfo": "经销商信息", "dealerRegion": "经销商区域", "dealerCity": "经销商城市", "dealerStoreName": "经销商门店", "salesConsultantInfo": "销售顾问信息", "salesConsultantName": "销售顾问姓名", "salesConsultantPhone": "销售顾问手机号", "salesConsultantEmail": "销售顾问邮箱", "vehicleInfo": "车辆信息", "model": "车型", "variant": "版本", "color": "颜色", "vin": "车架号", "warehouseName": "仓库名称", "productionDate": "生产日期", "options": "选装配置", "optionsTotalPrice": "选装配置总价", "priceInfo": "价格信息", "salesSubtotal": "销售小计", "salesSubtotalIncludeGASA": "销售小计（含GASA）", "salesSubtotalIncludeGASADesc": "包含政府代理服务费", "consumptionTax": "消费税", "consumptionTaxDesc": "政府征收的消费税", "salesTax": "销售税", "salesTaxDesc": "政府征收的销售税", "numberPlatesFee": "车牌费", "numberPlatesFeeDesc": "车牌注册费用", "optionsPrice": "选装配置价格", "optionsPriceDesc": "所有选装配置的总价", "vehicleSalesPriceSubtotal": "车辆销售价小计", "vehicleSalesPriceSubtotalDesc": "车辆基础价格加上所有税费和配置", "accessoriesTotalAmount": "配件总金额", "vehicleSalesPrice": "车辆销售价", "insuranceAmount": "保险费", "insuranceAmountDesc": "车辆保险费用", "otrAmount": "OTR费用", "otrAmountDesc": "上路费用", "orderDiscountAmount": "订单优惠金额", "orderDiscountAmountDesc": "订单总优惠金额", "orderTotalAmount": "订单总金额", "orderTotalAmountDesc": "订单最终总金额", "orderPaidAmount": "已付金额", "orderPaidAmountDesc": "客户已支付的金额", "orderUnpaidAmount": "未付金额", "orderUnpaidAmountDesc": "客户尚未支付的金额", "discountAmount": "优惠金额", "totalAmount": "总金额", "paidAmount": "已付金额", "unpaidAmount": "未付金额"}, "paymentRecords": {"title": "收退款记录", "addRecord": "添加记录", "table": {"paymentRecordNumber": "记录编号", "businessType": "业务类型", "transactionNumber": "交易流水号", "channel": "渠道", "amount": "金额", "paymentType": "付款类型", "arrivalTime": "到账时间", "remark": "备注", "dataSource": "数据来源", "createTime": "创建时间", "creator": "创建人", "actions": "操作", "delete": "删除"}, "businessType": {"payment": "收款", "refund": "退款"}, "addForm": {"title": "添加收退款记录", "businessType": "业务类型", "transactionNumber": "交易流水号", "channel": "渠道", "amount": "金额", "paymentType": "付款类型", "arrivalTime": "到账时间", "remark": "备注", "submitButton": "提交", "cancelButton": "取消"}, "messages": {"addSuccess": "添加成功", "addError": "添加失败", "deleteSuccess": "删除成功", "deleteError": "删除失败", "confirmDelete": "确认删除此记录吗？"}}}, "paymentOperation": {"title": "收退款操作", "orderInfo": "订单信息", "orderNumber": "订单号", "buyerName": "购车人姓名", "totalAmount": "订单总金额", "paidAmount": "已付金额", "unpaidAmount": "未付金额", "operationForm": "操作表单", "businessType": "业务类型", "transactionNumber": "交易流水号", "channel": "渠道", "amount": "金额", "paymentType": "付款类型", "arrivalTime": "到账时间", "remark": "备注", "submitButton": "提交", "cancelButton": "取消", "businessTypes": {"payment": "收款", "refund": "退款"}, "channels": {"bankTransfer": "银行转账", "onlinePayment": "在线支付", "cash": "现金", "check": "支票"}, "paymentTypes": {"deposit": "定金", "finalPayment": "尾款", "fullPayment": "全款"}, "messages": {"submitSuccess": "操作成功", "submitError": "操作失败", "amountRequired": "请输入金额", "transactionNumberRequired": "请输入交易流水号", "arrivalTimeRequired": "请选择到账时间"}}}