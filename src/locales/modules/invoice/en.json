{"title": "Invoice Management", "invoiceNumber": "Invoice Number", "invoiceNumberPlaceholder": "Enter invoice number", "customerName": "Customer Name", "customerNamePlaceholder": "Enter customer name", "customerPhone": "Customer Phone", "customerPhonePlaceholder": "Enter customer phone", "customerEmail": "Customer <PERSON><PERSON>", "customerEmailPlaceholder": "Enter customer email", "orderNumber": "Order Number", "orderNumberPlaceholder": "Enter order number", "vin": "VIN Code", "vinPlaceholder": "Enter VIN code", "salesType": "Sales Type", "salesStore": "Sales Store", "salesConsultant": "Sales Consultant", "invoiceDate": "Invoice Date", "invoiceDateRange": "Invoice Date Range", "batchPrint": "Batch Print", "export": "Export", "detail": "Detail", "print": "Print", "email": "Email", "log": "Log", "invoiceAmount": "Invoice Amount", "createdTime": "Created Time", "customerAddress": "Customer Address", "customerState": "Customer State", "customerCity": "Customer City", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "paymentMethod": "Payment Method", "financeCompany": "Finance Company", "loanAmount": "<PERSON><PERSON>", "emailConfirm": "Email Confirmation", "confirmSendEmail": "Confirm to send email to customer?", "exportConfig": "Export Configuration", "exportFormat": "Export Format", "exportScope": "Export <PERSON>", "currentPage": "Current Page", "allData": "All Data", "filteredData": "Filtered Data", "operationLog": "Operation Log", "operationType": "Operation Type", "operator": "Operator", "operationTime": "Operation Time", "operationResult": "Operation Result", "remark": "Remark", "printSuccess": "Print successful", "emailSentSuccess": "<PERSON>ail sent successfully", "exportSuccess": "Export successful", "batchPrintSuccess": "Batch print successful", "pleaseSelectRecords": "Please select records to operate", "invoiceDetail": "Invoice Detail", "basicInfo": "Invoice Basic Information", "customerInfo": "Customer Detailed Information", "vehicleInfo": "Vehicle Detailed Information", "financeInfo": "Finance Information Details", "insuranceInfo": "Insurance Information Details", "priceStructure": "Price Structure Details", "receipts": "Receipt Details Information", "otrFees": "OTR Fees Details", "companyName": "Company Name", "companyAddress": "Company Address", "companyInfo": "Company Information", "gstNumber": "GST Number", "sstNumber": "SST Number", "contactPhone": "Contact Phone", "contactEmail": "Contact Email", "state": "State", "city": "City", "postcode": "Postcode", "deliveryNumber": "Delivery Number", "salesConsultantId": "Sales Consultant ID", "tinNumber": "TIN Number", "modelCode": "Model Code", "modelDescription": "Model Description", "engineNumber": "Engine Number", "chassisNumber": "<PERSON><PERSON><PERSON> Number", "engineCapacity": "Engine Capacity", "engineDisplacement": "Engine Displacement", "vehicleRegistrationDate": "Vehicle Registration Date", "transmission": "Transmission", "loanTerm": "<PERSON>an <PERSON>", "months": "Months", "interestRate": "Interest Rate", "monthlyPayment": "Monthly Payment", "financeType": "Finance Type", "loanPeriod": "Loan Period", "insuranceCompany": "Insurance Company", "agentCode": "Agent Code", "policyNumber": "Policy Number", "policyDate": "Policy Date", "issueDate": "Issue Date", "insuranceAmount": "Insurance Amount", "vehiclePrice": "Vehicle Sales Price", "vehicleSalesPrice": "Vehicle Sales Price", "licensePlateFee": "License Plate Fee", "adjustmentAmount": "Adjustment Amount", "accessories": "Accessory Details", "optionalAccessories": "Optional Accessories", "category": "Category", "accessoryName": "Accessory Name", "unitPrice": "Unit Price", "quantity": "Quantity", "totalPrice": "Total Price", "totalAccessoryAmount": "Total Accessory Amount", "subtotal": "Subtotal", "otrRegistrationFees": "OTR Registration Fees", "billNumber": "<PERSON>", "feeItem": "<PERSON><PERSON>em", "feePrice": "<PERSON><PERSON>", "effectiveDate": "Effective Date", "expiryDate": "Expiry Date", "totalOtrFeeAmount": "Total OTR Fee Amount", "insurancePremium": "Insurance Premium", "totalSalesPrice": "Total Sales Price", "invoiceNetValue": "Invoice Net Value", "priceStructureDetails": "Price Structure Details", "receiptDetails": "Receipt Details", "businessType": "Business Type", "serialNumber": "Serial Number", "channel": "Channel", "collectionType": "Collection Type", "arrivalTime": "Arrival Time", "feeType": "Fee Type", "description": "Description", "amount": "Amount", "receiptNumber": "Receipt Number", "paymentDate": "Payment Date", "bankName": "Bank Name", "accountNumber": "Account Number", "status": "Status", "invoiceStatuses": {"issued": "Issued", "printed": "Printed", "sent": "<PERSON><PERSON>"}, "operationTypes": {"view_detail": "View Detail", "print": "Print", "allocate": "Allocate"}, "operationResults": {"success": "Success", "failed": "Failed"}, "commonFields": {"paginationLayout": "total, sizes, prev, pager, next, jumper", "total": "Total", "add": "Add", "optional": "(Optional)", "required": "Required", "selectPlaceholder": "Please select", "inputPlaceholder": "Please enter", "all": "All", "yes": "Yes", "no": "No", "loading": "Loading...", "items": "items", "page": "page", "operationSuccessful": "Operation successful", "operationFailed": "Operation failed", "pleaseInput": "Please input", "unknown": "Unknown", "vehicleAge": "Vehicle age", "months": "months", "good": "Good", "needsAttention": "Needs attention", "bad": "Bad", "notApplicable": "Not applicable"}, "operationRemarks": {"viewDetail": "View invoice detail", "printSuccess": "Invoice printed successfully"}, "messages": {"fetchListFailed": "Failed to fetch invoice list", "fetchStoreListFailed": "Failed to fetch store list", "fetchConsultantListFailed": "Failed to fetch consultant list", "fetchDetailFailed": "Failed to fetch invoice detail", "printFailed": "Print failed", "batchPrintFailed": "Batch print failed", "fetchInvoiceInfoFailed": "Failed to fetch invoice information", "emailSendFailed": "Email send failed", "exportFailed": "Export failed"}, "salesTypeOptions": {"PHP": "PHP", "CASH": "Cash", "FINANCE": "Finance"}}