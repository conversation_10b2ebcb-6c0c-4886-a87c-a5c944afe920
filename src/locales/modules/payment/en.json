{"title": "Vehicle Payment Management", "orderNumber": "Order Number", "buyerName": "Buyer Name", "buyerPhone": "Buyer Phone", "dealerStore": "Dealer Store", "salesConsultant": "Sales Consultant", "vin": "VIN", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "orderCreateTime": "Order Create Time", "orderStatus": "Order Status", "paymentStatus": "Payment Status", "vehicleSalesPrice": "Vehicle Sales Price", "insuranceAmount": "Insurance Amount", "insuranceAmountDesc": "Insurance Amount Desc", "otrAmount": "OTR Amount", "otrAmountDesc": "OTR Amount Desc", "discountAmount": "Discount Amount", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "unpaidAmount": "Unpaid Amount", "loanAmount": "<PERSON><PERSON>", "canInvoice": "Can Invoice", "invoiceTime": "Invoice Time", "invoiceNumber": "Invoice Number", "createTime": "Create Time", "updateTime": "Update Time", "paymentOperation": "Payment Operation", "orderDetail": "Order Detail", "paymentRecords": "Payment History", "addPaymentRecord": "Add Payment Record", "enterOrderNumber": "Enter order number", "enterBuyerName": "Enter buyer name", "enterBuyerPhone": "Enter buyer phone", "selectOrderStatus": "Select order status", "selectPaymentStatus": "Select payment status", "selectCanInvoice": "Select can invoice", "selectDateRange": "Select date range", "orderStatusSubmitted": "Submitted", "orderStatusCancelPending": "Cancel Pending", "orderStatusCancelApproved": "Cancel Approved", "orderStatusCancelled": "Cancelled", "orderStatusConfirmed": "Confirmed", "orderStatusPendingReview": "Pending Review", "orderStatusReviewed": "Reviewed", "orderStatusPendingDelivery": "Pending Delivery", "orderStatusDelivered": "Delivered", "paymentStatusPendingDeposit": "Pending Deposit", "paymentStatusDepositPaid": "<PERSON><PERSON><PERSON><PERSON>", "paymentStatusRefunding": "Refunding", "paymentStatusRefunded": "Refunded", "paymentStatusPendingFinal": "Pending Final Payment", "paymentStatusFullyPaid": "<PERSON>y Paid", "businessType": "Business Type", "transactionNumber": "Transaction Number", "channel": "Channel", "amount": "Amount", "paymentType": "Payment Type", "arrivalTime": "Arrival Time", "remark": "Remark", "creator": "Creator", "dataSource": "Data Source", "payment": "Payment", "refund": "Refund", "channelAPP": "APP", "channelBankCard": "Bank Card", "channelTransfer": "Transfer", "paymentTypeBookFee": "Book Fee", "paymentTypeLoan": "Loan", "paymentTypeFinal": "Final Payment", "dataSourceManual": "Manual Entry", "dataSourceApp": "APP Push", "paymentMethodFull": "Full Payment", "paymentMethodLoan": "Loan", "transactionNumberRequired": "Transaction number is required", "transactionNumberExists": "Transaction number already exists", "amountRequired": "Amount is required", "amountInvalid": "Please enter valid amount", "arrivalTimeRequired": "Arrival time is required", "operationNotAllowed": "Operation not allowed in current status", "appDataCannotDelete": "APP data cannot be deleted", "confirmDelete": "Confirm to delete this record?", "deleteSuccess": "Delete successful", "addSuccess": "Add successful", "orderInfo": "Order Information", "customerInfo": "Customer Information", "personalDetails": "Personal Details", "ordererInfo": "Orderer Information", "buyerInfo": "Buyer Information", "ordererName": "Orderer Name", "ordererPhone": "Orderer Phone", "buyerIdType": "ID Type", "buyerIdNumber": "ID Number", "buyerEmail": "Buyer Email", "buyerAddress": "Buyer Address", "buyerState": "Buyer State", "buyerCity": "Buyer City", "buyerPostcode": "Buyer Postcode", "storeInfo": "Store Information", "preferredOutletSalesAdvisor": "Preferred Outlet & Sales Advisor", "dealerRegion": "Dealer Region", "dealerCity": "Dealer City", "salesConsultantPhone": "Phone", "salesConsultantEmail": "Email", "vehicleInfo": "Vehicle Information", "options": "Options", "warehouseName": "Warehouse", "productionDate": "Production Date", "priceInfo": "Price Information", "salesSubtotal": "Sales Subtotal (Including GASA)", "consumptionTax": "Consumption Tax", "salesTax": "Sales Tax", "numberPlatesFee": "Number Plates Fee", "optionsPrice": "Options Price", "vehicleSalesSubtotal": "Vehicle Sales Subtotal", "otrAmountDetail": "OTR Amount", "paymentRecordNumber": "Payment Record Number", "totalPayment": "Total Payment Amount", "totalRefund": "Total Refund Amount", "netPayment": "Net Payment Amount", "recordCount": "Record Count", "enterTransactionNumber": "Enter transaction number", "enterAmount": "Enter amount", "enterRemark": "Enter remark", "selectBusinessType": "Select business type", "selectChannel": "Select channel", "selectPaymentType": "Select payment type", "selectArrivalTime": "Select arrival time"}