{"common": {"confirm": "确定", "cancel": "取消", "search": "查询", "actions": "操作", "currencySymbol": "RM ", "operationSuccessful": "操作成功！", "operationFailed": "操作失败！", "reLoginPrompt": "登录已过期，请重新登录。", "warning": "警告", "noPermission": "您没有操作权限。", "networkError": "网络错误，请稍后重试。", "badRequest": "请求错误。", "unauthorized": "未授权，请重新登录。", "forbidden": "拒绝访问。", "notFound": "请求地址出错，资源未找到。", "requestTimeout": "请求超时。", "serverError": "服务器内部错误。", "notImplemented": "服务未实现。", "badGateway": "网关错误。", "serviceUnavailable": "服务不可用。", "gatewayTimeout": "网关超时。", "unknownError": "未知错误。", "noResponse": "服务器没有响应。", "requestSetupError": "请求配置错误。", "reset": "重置", "operations": "操作", "yes": "是", "no": "否", "hour": "小时", "hours": "小时", "confirmDelete": "确定删除 '{item}' 吗？", "languageChanged": "语言切换成功！", "edit": "编辑", "delete": "删除", "startDate": "开始日期", "endDate": "结束日期", "confirmSave": "确定保存吗？", "confirmAdd": "确定新增吗？", "exporting": "正在导出...", "noData": "暂无数据", "query": "查询", "queryItems": "查询项", "sequence": "序号", "detail": "详情", "to": "至", "fetchFailed": "获取数据失败", "exportReport": "导出报表", "exportingReport": "报表导出中...", "pleaseSelectOne": "请选择一条数据", "pleaseSelect": "请选择", "upload": "上传", "good": "良好", "needsAttention": "需要关注", "bad": "差", "notApplicable": "不适用", "all": "全部", "noDataTip": "当前查询条件下没有找到相关数据", "close": "关闭", "confirmExport": "确认导出", "success": "成功", "paginationLayout": "total, sizes, prev, pager, next, jumper", "add": "新增", "optional": "（可选）", "required": "（必填）", "selectPlaceholder": "请选择", "inputPlaceholder": "请输入", "loading": "加载中...", "items": "条", "page": "页", "assign": "分配", "submitConfirm": "提交确认", "recall": "撤回", "print": "打印", "customerConfirm": "客户确认", "createWorkOrder": "创建工单", "export": "导出", "save": "保存", "clear": "清除", "uploadSuccessful": "上传成功", "pleaseSign": "请先签名", "pleaseUploadSignature": "请先上传签名", "tip": "提示", "confirmSubmitConfirm": "确定要提交确认吗？", "confirmRecall": "确定要撤回吗？", "confirmPrint": "确定要打印吗？", "operationCanceled": "操作已取消", "index": "序号", "saveConfig": "保存配置", "remove": "删除", "notice": "提示"}, "menu": {"home": "DMS看板", "about": "关于", "customerDetail": "客户详情", "checkinList": "到店登记列表", "appointmentDashboard": "预约看板", "appointmentManagement": "预约管理", "quotaManagement": "预约限量管理", "salesOrder": "销售订单", "salesOrderEdit": "销售订单编辑", "salesOrderDetail": "销售订单详情", "inventoryReport": "库存信息", "inventoryReportHQ": "库存报表HQ", "partManagement": "零件管理", "partsManagementHQ": "零件管理HQ", "partsReceipt": "零件收货", "partsReceiptQuery": "零件收货查询", "partArchives": "零件档案", "partConfiguration": "零件配置", "inspectionForm": "环检单管理", "workOrder": "工单管理", "dispatchManagement": "派工管理", "workAssignmentDashboard": "派工看板", "vehicleQuery": "车辆查询", "vehicleAllocation": "车辆配车管理", "orderApprovalManagement": "订单审批管理", "deliveryManagement": "交车管理", "vehicleRegistration": "车辆登记", "invoiceManagement": "发票管理", "leadPoolManagement": "线索池管理", "orderStatisticsManagement": "订单统计管理", "factoryOrderManagement": "厂端订单管理", "potentialCustomerDefeatApprovalManagement": "潜客战败审批管理", "testDriveRegistration": "到店试驾登记", "wholeVehicleCollectionManagement": "整车收款管理", "salesProspect": "销售潜客管理", "factoryProspect": "厂端潜客池管理", "defeatAudit": "战败审核", "addProspect": "新增潜客", "testDriveList": "试驾登记列表", "workOrderApproval": "工单审批", "testDriveReport": "试驾报表", "order": "订单管理"}, "customer": {"detailViewTitle": "客户详情"}, "login": {"title": "登录", "username": "用户名", "password": "密码", "loginButton": "登录", "notFound": "记录未找到", "ownerPhone": "车主手机号", "confirmCreateRepairOrder": "确定为登记单 {checkinId} 创建环检单吗？"}, "sales": {"vehicleList": "车辆列表", "salesOrderList": "销售订单列表", "id": "ID", "vin": "车辆识别码", "model": "车型", "brand": "品牌", "color": "颜色", "price": "价格", "statusLabel": "状态", "manufactureDate": "制造日期", "engineNumber": "发动机号", "buyerName": "购车人", "buyerNamePlaceholder": "请输入购车人姓名", "buyerPhone": "购车人手机号", "buyerPhonePlaceholder": "请输入购车人手机号", "buyerType": "购车人类别", "selectBuyerType": "请选择购车人类别", "individual": "个人", "company": "公司", "selectModel": "请选择车型", "orderNumber": "订单号", "orderNumberPlaceholder": "请输入订单号", "orderStatus": "订单状态", "selectOrderStatus": "请选择订单状态", "approvalStatus": "订单审核状态", "selectApprovalStatus": "请选择订单审核状态", "paymentStatus": "订单支付状态", "selectPaymentStatus": "请选择订单支付状态", "insuranceStatus": "投保状态", "selectInsuranceStatus": "请选择投保状态", "loanApprovalStatus": "贷款审核状态", "selectLoanApprovalStatus": "请选择贷款审核状态", "jpjRegistrationStatus": "JPJ车辆注册状态", "selectJpjRegistrationStatus": "请选择JPJ车辆注册状态", "createTime": "订单创建时间", "ordererName": "下单人", "ordererNamePlaceholder": "请输入下单人姓名", "ordererPhone": "下单人手机号", "ordererPhonePlaceholder": "请输入下单人手机号", "variant": "<PERSON><PERSON><PERSON>", "paymentMethod": "支付方式", "totalPrice": "订单总金额", "orderAmount": "订单金额", "status": {"in_stock": "在库", "sold": "已售", "reserved": "已预定"}, "models": {"AXIA": "AXIA", "BEZZA": "BEZZA", "MYVI": "MYVI"}, "addVehicle": "新增车辆", "editVehicle": "编辑车辆", "deleteVehicle": "删除车辆", "vinPlaceholder": "请输入车辆识别码", "modelPlaceholder": "请输入车型", "brandPlaceholder": "请输入品牌", "statusPlaceholder": "请选择状态", "colorPlaceholder": "请输入颜色", "pricePlaceholder": "请输入价格", "manufactureDatePlaceholder": "请选择制造日期", "engineNumberPlaceholder": "请输入发动机号", "selectStatus": "请选择状态"}, "checkin": {"checkinList": "到店登记列表", "checkinId": "登记单号", "checkinIdPlaceholder": "请输入登记单号", "licensePlate": "车牌号", "licensePlatePlaceholder": "请输入车牌号", "vin": "VIN码", "vinPlaceholder": "请输入VIN码", "repairPersonName": "送修人名称", "repairPersonNamePlaceholder": "请输入送修人名称", "repairPersonPhone": "送修人手机号", "repairPersonPhonePlaceholder": "请输入送修人手机号", "createdAt": "创建时间", "createdAtPlaceholder": "请选择创建日期范围", "export": "导出", "createRecord": "创建登记单", "id": "序号", "vehicleModel": "车型", "vehicleConfiguration": "配置", "color": "颜色", "mileage": "里程数", "mileagePlaceholder": "请输入里程数", "mileageUnit": "公里", "serviceAdvisor": "服务顾问", "relatedRepairOrderId": "关联环检单号", "serviceType": "服务类型", "notes": "备注", "viewDetails": "查看详情", "edit": "编辑", "delete": "删除", "createRepairOrder": "创建环检单", "addCheckinRecord": "新增到店登记单", "editCheckinRecord": "编辑到店登记单", "vehicleInfoNotFound": "未找到相关车辆信息", "vehicleInfoAutoFill": "自动填充或手动填写", "vehicleAge": "车龄(月)", "ownerInfo": "车主信息", "serviceTypePlaceholder": "请选择服务类型", "notesPlaceholder": "请输入备注", "save": "保存", "cancel": "取消", "serviceTypeOptions": {"repair": "维修", "maintenance": "保养", "inspection": "检测", "paint": "喷漆"}, "repairOrderAlreadyExists": "该到店登记单已存在关联环检单。", "repairOrderCreatedSuccess": "环检单创建成功！", "notFound": "记录未找到", "ownerPhone": "车主手机号", "updatedAt": "更新时间"}, "afterSales": {"appointmentManagement": "预约管理", "breadcrumb": "售后服务 / 预约管理", "status": "预约状态", "selectStatus": "请选择预约状态", "serviceType": "服务类型", "selectServiceType": "请选择服务类型", "serviceAdvisor": "服务顾问", "selectServiceAdvisor": "请选择服务顾问", "appointmentNo": "预约单号", "appointmentNoPlaceholder": "请输入预约单号", "customerName": "客户姓名", "customerNamePlaceholder": "请输入客户姓名", "customerPhone": "客户手机号", "customerPhonePlaceholder": "请输入客户手机号", "createTime": "创建日期", "createTimePlaceholder": "请选择创建日期", "export": "导出", "createAppointment": "新增预约", "serialNumber": "序号", "licensePlate": "车牌号", "vehicleModel": "车型", "vehicleColor": "颜色", "expectedArrivalTime": "预计到店日期", "actualArrivalTime": "实际到店日期", "operations": "操作", "viewDetails": "查看详情", "edit": "编辑", "cancelAppointment": "取消预约", "checkIn": "到店", "statuses": {"not_arrived": "未到店", "arrived": "已到店", "cancelled": "已取消", "no_show": "未履约", "pending_payment": "待支付"}, "serviceTypes": {"maintenance": "保养", "repair": "维修"}, "confirmCancel": "确定取消此预约吗？", "appointmentDetail": "预约详情", "appointmentId": "预约单号", "appointmentTime": "预约日期", "customerInfo": "客户信息", "vehicleInfo": "车辆信息", "mileage": "里程数", "appointmentInfo": "预约信息", "store": "门店", "technician": "技师", "serviceContent": "服务内容", "paymentInfo": "支付信息", "paymentStatus": "支付状态", "paymentAmount": "支付金额", "paymentOrderNumber": "支付流水号", "paymentStatuses": {"paid": "已支付", "unpaid": "未支付", "refunded": "已退款"}, "viewMore": "查看更多"}, "orderApproval": {"pageTitle": "订单审批", "pendingTab": "待审批", "approvedTab": "已审批", "searchTitle": "筛选条件", "approvalType": "审批类型", "approvalTypePlaceholder": "请选择审批类型", "cancelOrderApproval": "取消订单审批", "modifyOrderApproval": "修改订单审批", "vehicleColorModificationApproval": "车辆颜色修改审批", "orderNumber": "订单编号", "orderNumberPlaceholder": "请输入订单编号", "submittedBy": "提交人", "submittedByPlaceholder": "请输入提交人姓名", "submissionDate": "提交日期", "submissionDatePlaceholder": "请选择提交日期", "approvalStatus": "审批状态", "approvalStatusPlaceholder": "请选择审批状态", "approvedBy": "审批人", "approvedByPlaceholder": "请输入审批人姓名", "approvalDate": "审批日期", "approvalDatePlaceholder": "请选择审批日期", "export": "导出", "pendingApprovalList": "待审批列表", "approvedList": "已审批列表", "serialNumber": "序号", "approvalContent": "审批内容", "currentStatus": "当前状态", "actions": "操作", "viewDetail": "查看详情", "approve": "批准", "reject": "驳回", "reasonsForRejection": "拒绝原因", "reasonsForRejectionPlaceholder": "请输入拒绝原因", "confirmApproval": "确认审批", "confirmReject": "确认拒绝", "approvalDetail": "审批详情", "detailTitle": "审批详情", "approvalForm": "审批表单", "approvalRecord": "审批记录", "type": "类型", "submitter": "提交人", "submissionTime": "提交时间", "submissionTimeEnd": "提交结束时间", "pendingInitialReview": "待初审", "pendingFinalReview": "待终审", "aboutToTimeout": "即将超时", "timeoutRejected": "超时驳回", "approver": "审批人", "approvalTime": "审批时间", "remarks": "备注", "rejectionReason": "拒绝原因", "status": {"pending": "待审批", "approved": "已审批", "rejected": "已拒绝"}, "selectApprovalType": "请选择审批类型", "selectApprovalStatus": "请选择审批状态", "approvalHistory": "审核历史", "applicationReason": "申请原因", "fetchDataFailed": "获取审核列表失败", "orderDetailNotImplemented": "订单详情功能暂未实现", "approveSuccess": "批准成功", "rejectSuccess": "驳回操作成功", "batchOperationSuccess": "批量操作成功", "batchOperationFailed": "批量操作失败", "review": "审核", "exportSuccess": "导出成功", "exportFailed": "导出失败", "selectedCount": "已选择 {count} 项", "cancelReason": "取消原因", "changeDetails": "变更内容", "changedField": "变更字段", "originalValue": "原始数据", "newValue": "变更后数据", "approvalInfo": "审批信息", "approvalComment": "审批备注"}, "vehicleAllocation": {"title": "车辆配车管理", "orderNumber": "订单号", "orderNumberPlaceholder": "请输入订单号", "customerName": "客户姓名", "customerNamePlaceholder": "请输入客户姓名", "customerPhone": "客户手机", "customerPhonePlaceholder": "请输入客户手机", "allocationStatus": "配车状态", "allocationStatusPlaceholder": "请选择配车状态", "allocated": "已配车", "unallocated": "未配车", "orderStatus": "订单状态", "orderStatusPlaceholder": "请选择订单状态", "vin": "VIN", "vinPlaceholder": "请输入VIN", "factoryOrderNumber": "工厂订单号", "factoryOrderNumberPlaceholder": "请输入工厂订单号", "allocationTime": "配车时间", "salesConsultant": "销售顾问", "salesConsultantPlaceholder": "请选择销售顾问", "orderCreateTime": "订单创建时间", "model": "车型", "variant": "配置", "color": "颜色", "allocate": "配车", "cancelAllocate": "取消配车", "records": "记录", "exportData": "导出数据", "confirmExport": "确定要导出当前筛选条件下的数据吗？", "allocationConfirm": "配车确认", "orderDetail": "订单详情", "availableVehiclesQuery": "可配车辆查询", "availableVehicles": "可配车辆列表", "warehouseName": "所在仓库", "inStockTime": "入库时间", "noVehicleSelected": "请选择一辆车进行配车", "vehicleConfigMismatch": "所选车辆的配置与订单不符，无法配车", "allocationSuccess": "配车成功", "allocationFailed": "配车失败", "confirmCancelAllocation": "确定要取消该订单的配车吗？", "cancelAllocationSuccess": "取消配车成功", "cancelAllocationFailed": "取消配车失败", "allocationRecordDetail": "配车记录详情", "allocationHistory": "配车历史", "operator": "操作人", "remarks": "备注", "orderStatuses": {"submitted": "已提交", "confirmed": "已确认", "cancel_review": "取消审核中", "cancel_approved": "取消已批准", "cancelled": "已取消", "ready_delivery": "准备交付", "delivered": "已交付"}, "operationTypes": {"allocate": "配车", "cancel_allocate": "取消配车", "view_detail": "查看详情", "system_auto_cancel": "系统自动取消"}, "processResults": {"success": "成功", "failed": "失败"}, "cancelReasonLabel": "取消原因", "cancelReasonPlaceholder": "请输入取消配车原因", "reasonRequired": "必须填写取消原因"}, "workAssignment": {"pageTitle": "工单分配", "workOrderNo": "工单号", "taskName": "任务名称", "assignee": "分配人", "status": {"draft": "草稿", "pendingConfirm": "待确认", "confirmed": "已确认", "pendingAssignment": "待分配", "assigned": "已分配", "inProgress": "进行中", "pendingQc": "待质检", "pendingSettle": "待结算", "completed": "已完成", "cancelled": "已取消"}, "createTime": "创建时间", "addWorkAssignment": "新增工单分配", "editWorkAssignment": "编辑工单分配", "workAssignmentDetails": "工单分配详情", "taskNamePlaceholder": "请输入任务名称", "assigneePlaceholder": "请输入分配人姓名", "statusPlaceholder": "请选择状态", "confirmAdd": "确定新增工单分配吗？", "confirmEdit": "确定修改工单分配吗？", "statusOptions": {"pending": "待处理", "inProgress": "处理中", "completed": "已完成", "cancelled": "已取消"}, "dashboard": {"title": "派工看板", "autoRefresh": "自动刷新", "pendingAssignment": "待派工", "assignedOrders": "已分配", "inProgressOrders": "进行中", "completedOrders": "已完成", "availableTechnicians": "可用技师", "averageEfficiency": "平均效率", "utilizationRate": "利用率", "averageWaitingTime": "平均等待时间", "technicianSchedule": "技师工作安排", "selectDate": "选择日期", "today": "今天", "tomorrow": "明天", "thisWeek": "本周", "workload": "工作负荷", "available": "空闲", "orderStatusDistribution": "工单状态分布", "technicianWorkload": "技师工作负荷", "workloadPercentage": "负荷百分比", "workOrderDetail": "工单详情"}, "management": {"title": "派工管理"}, "common": {"refresh": "刷新", "search": "搜索", "reset": "重置", "confirm": "确认", "cancel": "取消", "detail": "详情", "hours": "小时", "minutes": "分钟", "unassigned": "未分配", "notScheduled": "未安排", "actions": "操作"}, "type": {"maintenance": "保养", "repair": "维修", "insurance": "保险"}, "priority": {"normal": "普通", "urgent": "紧急"}, "actions": {"assign": "分配", "reassign": "重新分配", "checkIn": "开工", "checkOut": "完工"}, "placeholder": {"enterWorkOrderNo": "请输入工单编号", "selectStatus": "请选择状态", "selectType": "请选择类型", "selectPriority": "请选择优先级", "selectTechnician": "请选择技师"}, "dialog": {"assignTitle": "分配工单", "reassignTitle": "重新分配工单"}, "confirmMessages": {"checkIn": "确定要开工此工单吗？", "checkOut": "确定要完工此工单吗？"}, "messages": {"loadDataFailed": "加载数据失败", "assignSuccess": "分配成功", "assignFailed": "分配失败", "reassignSuccess": "重新分配成功", "reassignFailed": "重新分配失败", "checkInSuccess": "开工成功", "checkInFailed": "开工失败", "checkOutSuccess": "完工成功", "checkOutFailed": "完工失败"}}, "invoice": {"title": "发票管理", "invoiceNumber": "发票编号", "invoiceNumberPlaceholder": "请输入发票编号", "customerName": "购车人姓名", "customerNamePlaceholder": "请输入购车人姓名", "customerPhone": "购车人手机号", "customerPhonePlaceholder": "请输入购车人手机号", "customerEmail": "购车人邮箱", "customerEmailPlaceholder": "请输入购车人邮箱", "customerAddress": "购车人地址", "customerState": "购车人州", "customerCity": "购车人城市", "orderNumber": "订单编号", "orderNumberPlaceholder": "请输入订单编号", "vin": "车辆识别代号", "vinPlaceholder": "请输入车辆识别代号", "salesType": "销售类型", "salesStore": "销售门店", "salesConsultant": "销售顾问", "invoiceDate": "开票日期", "invoiceDateRange": "开票日期范围", "batchPrint": "批量打印", "export": "导出", "detail": "详情", "print": "打印", "email": "邮件", "log": "日志", "invoiceAmount": "发票总额", "createdTime": "创建时间", "model": "车型系列", "variant": "车型配置版本", "color": "车辆颜色", "paymentMethod": "客户付款方式", "financeCompany": "贷款金融公司", "loanAmount": "贷款金额", "emailConfirm": "邮件发送确认", "confirmSendEmail": "确认发送邮件到客户邮箱？", "exportConfig": "导出配置", "exportFormat": "导出格式", "exportScope": "导出范围", "currentPage": "当前页", "allData": "全部数据", "filteredData": "筛选结果", "operationLog": "操作日志", "operationType": "操作类型", "operator": "操作人", "operationTime": "操作时间", "operationResult": "操作结果", "remark": "备注", "printSuccess": "打印成功", "emailSentSuccess": "邮件发送成功", "exportSuccess": "导出成功", "batchPrintSuccess": "批量打印成功", "pleaseSelectRecords": "请选择要操作的记录", "invoiceDetail": "发票详情", "basicInfo": "发票基本信息", "customerInfo": "客户详细信息", "vehicleInfo": "车辆详细信息", "financeInfo": "金融信息详情", "insuranceInfo": "保险信息详情", "priceStructure": "价格结构明细", "receipts": "收据明细信息", "otrFees": "OTR费用明细", "companyName": "公司名称", "companyAddress": "公司地址", "gstNumber": "GST编号", "sstNumber": "SST编号", "engineNumber": "发动机号", "chassisNumber": "底盘号", "engineCapacity": "发动机排量", "transmission": "变速箱", "loanTerm": "贷款期限", "months": "个月", "interestRate": "利率", "monthlyPayment": "月供", "insuranceCompany": "保险公司", "agentCode": "代理编码", "policyNumber": "保险单号", "policyDate": "出单日期", "insuranceAmount": "保险费用", "vehiclePrice": "车辆销售价", "adjustmentAmount": "调整金额", "accessories": "选配件明细", "category": "类别", "accessoryName": "配件名称", "unitPrice": "单价", "quantity": "数量", "totalPrice": "总价", "feeType": "费用类型", "description": "费用说明", "amount": "金额", "receiptNumber": "票据单号", "effectiveDate": "生效日期", "paymentDate": "付款日期", "bankName": "银行名称", "accountNumber": "账户号码", "status": "状态", "invoiceStatuses": {"issued": "已开票", "printed": "已打印", "sent": "已发送"}, "operationTypes": {"view_detail": "查看详情", "print": "打印", "allocate": "配车"}, "operationResults": {"success": "成功", "failed": "失败"}, "commonFields": {"paginationLayout": "total, sizes, prev, pager, next, jumper", "total": "总计", "add": "新增", "optional": "（可选）", "required": "必填项", "selectPlaceholder": "请选择", "inputPlaceholder": "请输入", "all": "全部", "yes": "是", "no": "否", "loading": "加载中...", "items": "条", "page": "页", "operationSuccessful": "操作成功", "operationFailed": "操作失败", "pleaseInput": "请输入", "unknown": "未知", "vehicleAge": "车龄", "months": "月", "good": "良好", "needsAttention": "需要注意", "bad": "不良", "notApplicable": "不适用"}, "operationRemarks": {"viewDetail": "查看发票详情", "printSuccess": "打印发票成功"}, "messages": {"fetchListFailed": "获取发票列表失败", "fetchStoreListFailed": "获取门店列表失败", "fetchConsultantListFailed": "获取销售顾问列表失败", "fetchDetailFailed": "获取发票详情失败", "printFailed": "打印失败", "batchPrintFailed": "批量打印失败", "fetchInvoiceInfoFailed": "获取发票信息失败", "emailSendFailed": "邮件发送失败", "exportFailed": "导出失败"}, "salesTypeOptions": {"PHP": "PHP", "CASH": "现金", "FINANCE": "贷款"}}, "parts": {"title": "零件管理", "partNo": "零件号", "partName": "零件名称", "partNamePlaceholder": "请输入零件名称", "partNumber": "零件编号", "partNumberPlaceholder": "请输入零件编号", "requisitionNumber": "叫料单号", "requisitionNumberPlaceholder": "输入框（全模糊查询）", "purchaseOrderNumber": "采购单号", "purchaseOrderNumberPlaceholder": "输入框（全模糊查询）", "supplierName": "供应商名称", "supplierNamePlaceholder": "可输入筛选下拉框", "requisitionDate": "叫料日期", "requisitionDateRange": "叫料日期区间", "arrivalTimeRange": "到货日期区间", "requisitionStatus": "叫料单状态", "selectRequisitionStatus": "请选择叫料单状态", "statusSubmitted": "已提交", "statusApproved": "通过", "statusRejected": "驳回", "statusShipped": "已发货", "statusPartialReceived": "部分收货", "statusReceived": "已收货", "statusCancelled": "已作废", "inventoryStatus": "库存状态", "selectInventoryStatus": "请选择库存状态", "statusNormal": "正常", "statusBelowSafetyStock": "低于安全库存", "newRequisition": "新建叫料清单", "exportReport": "导出报表", "partScrap": "零件报损", "scrapRecord": "报损记录", "newRequisitionForm": {"partName": "零件名称", "partNumber": "零件号", "quantity": "数量", "storeName": "门店名称", "expectedArrivalTime": "期望到货日期"}, "partScrapForm": {"partName": "零件名称", "partNumber": "零件号", "quantity": "数量", "scrapReason": "报损原因"}, "scrapRecordForm": {"partName": "零件名称", "partNamePlaceholder": "可输入筛选下拉框", "partNumber": "零件编号", "partNumberPlaceholder": "可输入筛选下拉框", "scrapDate": "报损日期", "scrapSource": "报损来源", "selectScrapSource": "请选择报损来源", "sourceReceipt": "收货", "sourceRepair": "维修", "sourceOther": "其它", "receiptTime": "收货时间", "scrapQuantity": "报损数量", "scrapReason": "报损原因详情"}}, "partManagementHQ": {"title": "零件管理HQ", "partName": "零件名称", "partNamePlaceholder": "可输入筛选下拉框", "partNumber": "零件编号", "partNumberPlaceholder": "可输入筛选下拉框", "requisitionNumber": "叫料单号", "requisitionNumberPlaceholder": "输入框（全模糊查询）", "purchaseOrderNumber": "采购单号", "purchaseOrderNumberPlaceholder": "输入框（全模糊查询）", "supplierName": "供应商名称", "supplierNamePlaceholder": "可输入筛选下拉框", "requisitionDateRange": "叫料日期区间", "arrivalTimeRange": "到货日期区间", "requisitionStatus": "叫料单状态", "selectRequisitionStatus": "请选择叫料单状态", "statusSubmitted": "已提交", "statusApproved": "通过", "statusRejected": "驳回", "statusShipped": "已发货", "statusReceived": "已收货", "statusCancelled": "已作废", "inventoryStatus": "库存状态", "selectInventoryStatus": "请选择库存状态", "statusNormal": "正常", "statusBelowSafetyStock": "低于安全库存", "exportReport": "导出报表", "approve": "审批", "approvalResult": "审批结果", "selectApprovalResult": "请选择审批结果", "rejectionReason": "驳回原因", "rejectionReasonRequired": "请输入驳回原因", "enterRejectionReason": "请输入驳回原因，字数限制1000", "requisitionDetails": "叫料单明细", "quantity": "数量", "unit": "单位", "expectedArrivalTime": "预计到货时间", "approvalSuccess": "审批成功！", "approvalResultRequired": "审批结果必选"}, "partsReceipt": {"title": "零件收货（店端）", "partName": "零件名称", "partNamePlaceholder": "请输入零件名称", "partNumber": "零件编号", "partNumberPlaceholder": "请输入零件编号", "supplierName": "供应商名称", "supplierNamePlaceholder": "请输入供应商名称", "requisitionNumber": "叫料单号", "requisitionNumberPlaceholder": "请输入叫料单号", "purchaseOrderNumber": "采购单号", "purchaseOrderNumberPlaceholder": "请输入采购单号", "deliveryOrderNumber": "到货单号", "deliveryOrderNumberPlaceholder": "请输入到货单号", "receipt": "收货", "generateReceiptOrder": "生成收货单", "invalidateReceiptOrder": "作废收货单", "receiptOrderNumber": "收货单号", "partQuantity": "零件数量", "unit": "单位", "receiptDialogTitle": "零件收货", "receivedQuantity": "收货数量", "damagedQuantity": "破损数量", "damageReason": "破损情况", "damageReasonPlaceholder": "请输入破损情况", "selectItemForReceipt": "请选择要收货的零件。", "damageReasonRequired": "破损数量不为0时，破损情况必填。", "receiptSuccess": "收货成功！", "generateReceiptOrderDialogTitle": "生成收货单", "receiptTime": "收货时间", "receiptTimePlaceholder": "请选择收货时间", "quantity": "数量", "printReceiptOrder": "打印收货单", "selectItemToPrint": "请选择要打印收货单的零件。", "generateReceiptOrderSuccess": "收货单生成成功！", "invalidateReceiptOrderDialogTitle": "作废收货单", "receiptOrderStatus": "收货单状态", "receiptOrderStatusPlaceholder": "请选择状态", "statusEffective": "生效", "statusInvalidated": "作废", "generationDate": "生成日期", "confirmInvalidateReceiptOrder": "是否作废此单据？", "invalidateReceiptOrderSuccess": "收货单作废成功！", "receiptOrderDetailTitle": "收货单明细"}, "inventory": {"title": "库存信息（店端）", "partName": "零件名称", "partNamePlaceholder": "请输入零件名称", "partNumber": "零件编号", "partNumberPlaceholder": "请输入零件编号", "supplierName": "供应商名称", "supplierNamePlaceholder": "请输入供应商名称", "inventoryStatus": "库存状态", "inventoryStatusPlaceholder": "请选择库存状态", "totalInventory": "库存总数", "availableInventory": "可用库存数", "lockedInventory": "锁定库存数", "defectiveCount": "残次品数", "pendingCount": "待收数", "safetyStock": "安全库存数", "status": {"normal": "正常", "belowSafety": "低于安全库存"}}, "partsReceiptQuery": {"title": "零件收货查询", "queryDimension": "查询维度", "queryDimensionPlaceholder": "请选择查询维度", "byReceiptOrder": "按收货单", "byReceiptDetail": "按收货明细", "partName": "零件名称", "partNamePlaceholder": "请输入零件名称", "partNumber": "零件编号", "partNumberPlaceholder": "请输入零件编号", "supplierName": "供应商名称", "supplierNamePlaceholder": "请输入供应商名称", "requisitionNumber": "叫料单号", "requisitionNumberPlaceholder": "请输入叫料单号", "purchaseOrderNumber": "采购单号", "purchaseOrderNumberPlaceholder": "请输入采购单号", "deliveryOrderNumber": "送货单号", "deliveryOrderNumberPlaceholder": "请输入送货单号", "receiptOrderNumber": "收货单号", "receiptOrderNumberPlaceholder": "请输入收货单号", "generateDate": "生成日期", "orderStatus": "单据状态", "status": {"active": "有效", "invalid": "已作废"}, "viewDetails": "查看明细", "details": "明细", "quantity": "数量", "unit": "单位", "receiptTime": "收货时间", "searchFailed": "查询失败", "queryDimensionRequired": "请选择查询维度"}, "partsArchives": {"title": "零件档案", "partName": "零件名称", "partNumber": "零件编码", "supplierName": "供应商名称", "partNamePlaceholder": "请输入零件名称", "partNumberPlaceholder": "请输入零件编码", "supplierNamePlaceholder": "请输入供应商名称", "unit": "单位", "purchasePrice": "采购单价"}, "partsConfiguration": {"title": "零件配置", "partName": "零件名称", "partNumber": "零件编码", "supplierName": "供应商名称", "partNamePlaceholder": "请输入零件名称", "partNumberPlaceholder": "请输入零件编码", "supplierNamePlaceholder": "请输入供应商名称", "unit": "单位", "safetyStock": "安全库存", "configure": "配置", "configurationModalTitle": "零件配置"}, "order": {"orderManagement": "厂端订单管理", "orderOverview": "订单概览统计", "filterConditions": "筛选条件", "orderNumber": "订单编号", "orderNumberPlaceholder": "请输入订单编号", "storeName": "门店", "storeNamePlaceholder": "请选择门店", "model": "车型", "modelPlaceholder": "请选择车型", "variant": "配置", "variantPlaceholder": "请先选择车型", "orderStatus": "订单状态", "orderStatusPlaceholder": "请选择订单状态", "paymentStatus": "支付状态", "paymentStatusPlaceholder": "请选择支付状态", "createDateRange": "下单日期", "createDateRangePlaceholder": "请选择下单日期范围", "exportExcel": "导出Excel", "customerName": "下单人", "customerPhone": "下单人手机号", "buyerName": "购车人", "buyerPhone": "购车人手机号", "buyerType": "购车人类别", "vin": "VIN", "paymentMethod": "付款方式", "loanApprovalStatus": "贷款审核状态", "approvalStatus": "订单审核状态", "insuranceStatus": "投保状态", "jpjRegistrationStatus": "JPJ车辆注册状态", "createTime": "订单创建时间", "color": "颜色", "monthlyOrderCount": "本月订单数量", "todayOrderCount": "今日订单数量", "topStore": "订单最多门店", "hotModel": "最热销车型", "pendingDeliveryCount": "待交车订单", "growth": "环比增长", "thisMonthOrders": "本月订单量", "orderStatuses": {"pending_deposit": "待支付定金", "confirmed": "已确认", "cancelled": "已取消", "pending_delivery": "待交车", "completed": "已完成"}, "paymentStatuses": {"pending_deposit": "待支付定金", "deposit_paid": "定金已付", "pending_final": "待支付尾款", "fully_paid": "已全款"}, "paymentMethods": {"full_payment": "全款", "installment": "分期"}, "approvalStatuses": {"pending": "待审批", "approved": "已审批-通过", "rejected": "已审批-驳回"}, "insuranceStatuses": {"pending": "未投保", "completed": "投保完成", "failed": "投保失败"}, "jpjRegistrationStatuses": {"pending": "待登记", "completed": "已登记", "failed": "登记失败"}, "editOrder": "编辑订单", "orderDetail": "订单详情", "personalDetails": "客户信息", "preferredOutletSalesAdvisor": "购车门店信息", "purchaseDetails": "购车信息", "vehicleInfo": "车辆信息", "invoiceInfo": "开票信息", "serviceRightsInfo": "服务&权益信息", "paymentInfo": "支付信息", "insuranceInfo": "保险信息", "otrFeesInfo": "OTR费用信息", "addRights": "新增权益", "pushToInsurance": "推送至保险系统", "submitDelivery": "提交交车", "backToList": "返回列表", "save": "保存", "salesSubtotal": "销售小计", "numberPlatesFee": "车牌费", "totalAmount": "整车开票价", "deposit": "定金", "finalPayment": "尾款金额", "remainingReceivable": "剩余应收", "loanAmount": "贷款金额", "loanTerm": "贷款期数", "accessoryTotalAmount": "选配件总金额", "rightsTotalDiscount": "权益优惠总金额", "insuranceTotalAmount": "保险总金额", "otrFeesTotalAmount": "登记费用总金额", "accessories": "选配件信息", "accessoryCode": "配件代码", "accessoryName": "配件名称", "accessoryCategory": "类别", "accessoryPrice": "配件单价", "accessoryQuantity": "数量", "accessoryTotalPrice": "总价", "rights": "权益列表", "rightCode": "权益代码", "rightName": "权益名称", "rightMode": "权益模式", "rightDiscountAmount": "优惠金额", "rightEffectiveDate": "生效日期", "rightExpiryDate": "终止日期", "deleteRight": "删除", "policyNumber": "保单号", "insuranceType": "保险类型", "insuranceCompany": "保险公司", "insuranceAmount": "保险价格", "insuranceEffectiveDate": "生效日期", "insuranceExpiryDate": "到期日期", "insuranceNotes": "备注", "otrTicketNumber": "票据单号", "otrFeeItem": "收费项目", "otrFeePrice": "收费价格", "otrEffectiveDate": "生效日期", "otrExpiryDate": "到期日期", "buyerIdType": "购车人身份证件类别", "buyerIdNumber": "购车人身份证件号", "buyerEmail": "购车人邮箱", "buyerAddress": "购车人地址", "buyerState": "购车人所在州", "buyerCity": "购车人所在城市", "buyerPostalCode": "购车人所在地邮编", "storeState": "所在州", "storeCity": "所在城市", "salesConsultant": "销售顾问", "invoicingType": "开票", "invoicingName": "开票名称", "invoicingPhone": "开票电话", "invoicingAddress": "开票地址", "confirmColorChange": "您已更改车辆颜色，是否提交审核？", "confirmSubmitDelivery": "确定要提交交车吗？", "deliveryConditionsNotMet": "交车前置条件不满足", "selectRights": "选择权益", "availableRights": "可用权益", "selectAll": "全选", "addSelectedRights": "添加"}, "vehicleQuery": {"title": "车辆查询", "vin": "车架号", "vinPlaceholder": "请输入车架号", "factoryOrderNo": "工厂订单号", "factoryOrderNoPlaceholder": "请输入工厂订单号", "warehouseName": "仓库名称", "warehouseNamePlaceholder": "请选择仓库", "model": "车型", "modelPlaceholder": "请选择车型", "variant": "配置", "variantPlaceholder": "请先选择车型", "color": "颜色", "colorPlaceholder": "请先选择配置", "lockStatus": "锁定状态", "lockStatusPlaceholder": "请选择锁定状态", "locked": "已锁定", "unlocked": "未锁定", "invoiceStatus": "开票状态", "invoiceStatusPlaceholder": "请选择开票状态", "invoiced": "已开票", "notInvoiced": "未开票", "invoiceDate": "开票时间", "storageDate": "入库时间", "productionDate": "生产时间", "stockStatus": "库存状态", "deliveryStatus": "交车状态", "deliveryDate": "交车时间", "stockStatusOptions": {"inStock": "在库", "allocated": "配车", "inTransit": "在途", "transferred": "调拨"}, "lockStatusOptions": {"locked": "已锁定", "unlocked": "未锁定"}, "invoiceStatusOptions": {"invoiced": "已开票", "notInvoiced": "未开票"}, "deliveryStatusOptions": {"delivered": "已交车", "notDelivered": "未交车"}, "detailDialogTitle": "车辆详情", "exportDialogTitle": "导出数据", "exportFormat": "导出格式", "exportFormatPlaceholder": "请选择导出格式", "excel": "Excel", "csv": "CSV", "exportScope": "导出范围", "exportCurrentPage": "当前页数据", "exportAllData": "全部数据", "exportSuccess": "导出{format}文件成功！", "fetchWarehouseFailed": "获取仓库信息失败", "fetchConfigFailed": "获取车型配置失败"}, "vehicleRegistration": {"title": "车辆登记管理", "orderNumber": "订单编号", "orderNumberPlaceholder": "请输入订单编号", "customerName": "客户姓名", "customerNamePlaceholder": "请输入客户姓名", "customerPhone": "客户手机号", "customerPhonePlaceholder": "请输入客户手机号", "registrationStatus": "登记状态", "registrationStatusAll": "全部状态", "registrationStatusPending": "待登记", "registrationStatusProcessing": "登记中", "registrationStatusSuccess": "登记成功", "registrationStatusFailed": "登记失败", "vin": "VIN", "vinPlaceholder": "请输入VIN", "insuranceStatus": "投保状态", "insuranceStatusAll": "全部状态", "insuranceStatusInsured": "已投保", "insuranceStatusNotInsured": "未投保", "pushTimeRange": "推送时间范围", "salesAdvisor": "销售顾问", "salesAdvisorAll": "全部销售顾问", "export": "导出", "loadingData": "数据加载中...", "serialNumber": "序号", "vehicleModel": "车型", "vehicleColor": "车身颜色", "insuranceCompany": "保险公司", "pushTime": "推送时间", "registrationFee": "登记费用", "operations": "操作", "pushRegistration": "推送登记", "viewDetails": "查看详情", "pushConfirmTitle": "确认推送登记", "pushConfirmContent": "您确定要推送此车辆进行登记吗？", "confirmPush": "确认推送", "retryPushTitle": "重新推送登记", "failureReason": "失败原因", "retryPushContent": "您确定要重新推送此车辆进行登记吗？", "confirmRetry": "确认重新推送", "orderDetailsTitle": "订单详情", "orderBasicInfo": "订单基本信息", "orderStatus": "订单状态", "createTime": "创建时间", "customerInfo": "购车人信息", "idType": "证件类型", "idNumber": "证件号码", "email": "邮箱", "address": "地址", "city": "城市", "state": "州/省", "postcode": "邮政编码", "vehicleInfo": "车辆信息", "engineNumber": "发动机号", "modelCode": "型号代码", "variant": "版本", "productionYear": "生产年份", "manufactureDate": "制造日期", "insuranceInfo": "保险信息", "policyNumber": "保单号", "insurancePeriod": "保险期限", "insuranceDate": "投保日期", "insuranceFee": "保险费用", "jpjInfo": "JPJ登记信息", "certificateNumber": "证书编号", "completionTime": "完成时间", "operator": "操作员", "retryPush": "重新推送", "feeDetails": "费用明细", "feeType": "费用类型", "feeAmount": "费用金额", "operationLogs": "操作日志", "operationTime": "操作时间", "operationType": "操作类型", "operationResult": "操作结果", "remark": "备注", "pushSuccess": "推送成功！", "pushFailed": "推送失败！", "retrySuccess": "重新推送成功！", "retryFailed": "重新推送失败！", "exportSuccess": "导出成功！", "exportFailed": "导出失败！"}, "delivery": {"pageTitle": "交车管理", "searchTitle": "筛选查询", "deliveryNumber": "交车单号", "deliveryNumberPlaceholder": "请输入交车单号", "orderNumber": "订单编号", "orderNoPlaceholder": "请输入订单编号", "customerName": "购车人名称", "customerNamePlaceholder": "请输入购车人名称", "customerPhone": "购车人手机号", "customerMobilePlaceholder": "请输入手机号", "vin": "VIN", "vinPlaceholder": "请输入VIN", "orderStatus": "订单状态", "orderStatusPlaceholder": "请选择订单状态", "orderStatusPendingAllocation": "待分配", "orderStatusAllocating": "分配中", "orderStatusAllocated": "已分配", "orderStatusPendingDelivery": "待交车", "orderStatusDelivered": "已交车", "dealerStore": "门店", "dealerStorePlaceholder": "请选择门店", "salesConsultant": "销售顾问", "salesmanPlaceholder": "请输入销售顾问", "deliveryStatus": "交车状态", "deliveryStatusPlaceholder": "请选择交车状态", "statusPending": "待交车", "statusConfirming": "待确认", "statusCompleted": "已交车", "customerConfirmed": "客户是否确认", "customerConfirmedPlaceholder": "请选择客户是否确认", "confirmationType": "客户确认类型", "confirmationTypePlaceholder": "请选择客户确认类型", "confirmationTypeApp": "APP", "confirmationTypeOffline": "线下", "deliveryTime": "交车时间", "startDate": "开始日期", "deliveryTimeEnd": "结束日期", "invoiceTimeStart": "开票开始时间", "invoiceTimeEnd": "开票结束时间", "listTitle": "交车单列表", "totalCount": "共 {count} 条", "model": "车型", "variant": "配置", "color": "颜色", "actualDeliveryDate": "实际交车日期", "deliveryNotes": "交车备注", "signaturePhoto": "签字照片", "submitConfirm": "提交确认", "deliveryConfirm": "交车确认", "confirmDeliveryTitle": "确认交车", "confirmDeliveryContent": "您确定要确认此交车单吗？", "submitConfirmSuccess": "提交确认成功！", "submitConfirmFailed": "提交确认失败！", "deliveryConfirmSuccess": "交车确认成功！", "deliveryConfirmFailed": "交车确认失败！", "fetchDataFailed": "获取交车单数据失败！", "detailNotFound": "未找到交车单详情！", "fetchDetailFailed": "获取交车单详情失败！", "noDetailData": "暂无详情数据", "printFeatureNotImplemented": "打印功能暂未实现！", "exportSuccess": "导出成功！", "exportFailed": "导出失败！", "orderStatusNormal": "正常", "orderStatusCancelled": "已取消"}, "payment": {"title": "整车收款管理", "orderNumber": "订单编号", "buyerName": "购车人名称", "buyerPhone": "购车人手机号", "dealerStore": "订车门店", "salesConsultant": "销售顾问", "vin": "VIN", "model": "Model", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "orderCreateTime": "订单创建时间", "orderStatus": "订单状态", "paymentStatus": "订单支付状态", "vehicleSalesPrice": "车辆销售价格", "insuranceAmount": "保险金额", "otrAmount": "OTR金额", "discountAmount": "权益优惠价格", "totalAmount": "总金额", "paidAmount": "订单已支付金额", "unpaidAmount": "订单未支付金额", "loanAmount": "贷款金额", "canInvoice": "是否可开票", "invoiceTime": "开票时间", "invoiceNumber": "发票号", "createTime": "创建时间", "updateTime": "更新时间", "paymentOperation": "收退款操作", "orderDetail": "销售订单详情", "paymentRecords": "收退款历史记录", "addPaymentRecord": "添加收退款信息", "enterOrderNumber": "请输入订单编号", "enterBuyerName": "请输入购车人名称", "enterBuyerPhone": "请输入购车人手机号", "selectOrderStatus": "请选择订单状态", "selectPaymentStatus": "请选择订单支付状态", "selectCanInvoice": "请选择是否可开票", "selectDateRange": "请选择时间范围", "orderStatusSubmitted": "已提交", "orderStatusCancelPending": "取消审核中", "orderStatusCancelApproved": "取消审核通过", "orderStatusCancelled": "已取消", "orderStatusConfirmed": "已确认", "orderStatusPendingReview": "待审核", "orderStatusReviewed": "已审核", "orderStatusPendingDelivery": "待交车", "orderStatusDelivered": "已交车", "paymentStatusPendingDeposit": "待付定金", "paymentStatusDepositPaid": "定金已付", "paymentStatusRefunding": "退款中", "paymentStatusRefunded": "已退款", "paymentStatusPendingFinal": "待支付尾款", "paymentStatusFullyPaid": "已支付尾款", "businessType": "业务类型", "transactionNumber": "流水号", "channel": "渠道", "amount": "金额", "paymentType": "收款类型", "arrivalTime": "到账时间", "remark": "备注", "creator": "创建人", "dataSource": "数据来源", "payment": "收款", "refund": "退款", "channelAPP": "APP", "channelBankCard": "银行卡", "channelTransfer": "转账", "paymentTypeBookFee": "Book Fee", "paymentTypeLoan": "贷款", "paymentTypeFinal": "尾款", "dataSourceManual": "手动录入", "dataSourceApp": "APP推送", "paymentMethodFull": "全款", "paymentMethodLoan": "贷款", "transactionNumberRequired": "请输入流水号", "transactionNumberExists": "流水号已存在", "amountRequired": "请输入金额", "amountInvalid": "请输入有效金额", "arrivalTimeRequired": "请选择到账时间", "operationNotAllowed": "当前状态不允许此操作", "appDataCannotDelete": "APP数据不可删除", "confirmDelete": "确认删除该记录？", "deleteSuccess": "删除成功", "addSuccess": "添加成功", "orderInfo": "订单基础信息", "customerInfo": "客户信息", "personalDetails": "客户信息 - Personal Details", "ordererInfo": "下单人信息", "buyerInfo": "购车人信息", "ordererName": "下单人姓名", "ordererPhone": "下单人手机号", "buyerIdType": "购车人身份证件类别", "buyerIdNumber": "购车人身份证件号", "buyerEmail": "购车人邮箱", "buyerAddress": "购车人地址", "buyerState": "购车人所在州", "buyerCity": "购车人所在城市", "buyerPostcode": "购车人所在地邮编", "storeInfo": "购车门店信息", "preferredOutletSalesAdvisor": "Preferred Outlet & Sales Advisor", "dealerRegion": "所在地区", "dealerCity": "所在城市", "salesConsultantPhone": "联系电话", "salesConsultantEmail": "邮箱地址", "vehicleInfo": "车辆详细信息", "options": "选装件", "warehouseName": "仓库", "productionDate": "生产日期", "priceInfo": "订单价格详细信息", "salesSubtotal": "销售小计(包含GASA、消费税、销售税)", "consumptionTax": "消费税", "salesTax": "销售税", "numberPlatesFee": "车牌费", "optionsPrice": "选装包价格", "vehicleSalesSubtotal": "车辆销售价格小计", "otrAmountDetail": "OTR金额", "paymentRecordNumber": "收退款单号", "totalPayment": "总收款金额", "totalRefund": "总退款金额", "netPayment": "净收款金额", "recordCount": "记录总数", "enterTransactionNumber": "请输入流水号", "enterAmount": "请输入金额", "enterRemark": "请输入备注信息", "selectBusinessType": "请选择业务类型", "selectChannel": "请选择渠道", "selectPaymentType": "请选择收款类型", "selectArrivalTime": "请选择到账时间", "customerDetailInfo": "客户详细信息", "paymentMethod": "支付方式", "pageTitle": "销售订单列表", "searchTitle": "搜索条件", "batchOperation": "批量操作", "listTitle": "销售订单列表", "totalCount": "总数", "detailTitle": "销售订单详情", "statusSubmitted": "已提交", "statusConfirmed": "已确认", "statusCancelPending": "取消待处理", "statusCancelApproved": "取消已批准", "statusCancelled": "已取消", "statusPendingDelivery": "待交付", "statusDelivered": "已交付", "viewDetail": "详情", "editOrder": "编辑", "returnToList": "返回列表", "exportSuccess": "导出成功", "exportFailed": "导出失败", "noDataToExport": "没有数据可导出", "confirmExportTitle": "确认导出", "confirmExportMessage": "确定要导出当前筛选条件下的所有订单数据吗？", "purchaseInfo": "购车信息", "vehicleInfoTab": "车辆信息", "invoicingInfoTab": "开票信息", "rightsInfoTab": "服务&权益信息", "paymentInfoTab": "支付信息", "insuranceInfoTab": "保险信息", "otrFeesTab": "OTR费用信息", "changeRecords": "订单变更记录", "dealerAndSalesInfo": "门店与销售信息", "salesConsultantInfo": "销售顾问信息", "vehicleDetailInfo": "车辆详细信息", "warehouse": "仓库", "optionsTotalPrice": "选装件总价", "priceDetailInfo": "订单价格详细信息", "salesSubtotalIncludeGASA": "销售小计(包含GASA)", "salesSubtotalIncludeGASADesc": "含各种税费", "consumptionTaxDesc": "税费明细", "salesTaxDesc": "税费明细", "numberPlatesFeeDesc": "车牌相关费用", "optionsPriceDesc": "选装件总价", "storeRegion": "所在地区", "storeCity": "所在城市", "storeName": "购车门店", "salesConsultantName": "销售顾问", "vinCode": "VIN码", "accessoryInfo": "选配件信息", "accessoryCategory": "类别", "accessoryName": "配件名称", "unitPrice": "配件单价", "quantity": "数量", "totalPrice": "总价", "accessoriesTotalAmount": "选配件总金额", "invoicingType": "开票类型", "invoicingName": "开票名称", "invoicingPhone": "开票电话", "invoicingAddress": "开票地址", "rightsInfo": "服务&权益信息", "rightCode": "权益代码", "rightName": "权益名称", "rightMode": "权益模式", "effectiveDate": "权益生效日期", "expiryDate": "权益终止日期", "rightsDiscountAmount": "权益优惠总金额", "loanApprovalStatusField": "贷款资质审核状态", "depositAmount": "定金金额", "balanceAmount": "尾款金额", "insuranceInfo": "保单信息", "policyNumber": "保单号", "insuranceType": "保险类型", "insuranceCompany": "保险公司", "insurancePrice": "保险价格", "insuranceTotalAmount": "保险总金额", "insuranceNotes": "备注", "otrFeesInfo": "On The Road登记费用", "ticketNumber": "票据单号", "feeItem": "收费项目", "feePrice": "收费价格", "otrFeesTotalAmount": "OTR费用总金额", "changeRecordIndex": "序号", "originalContent": "原始内容", "changedContent": "变更后内容", "operator": "操作人", "operationTime": "操作时间", "totalInvoiceAmount": "整车开票价", "remainingAmount": "剩余应收", "editOrderTitle": "销售订单编辑", "preferredOutlet": "购车门店信息 - Preferred Outlet & Sales Advisor", "purchaseDetails": "购车信息 - Purchase Details", "addRights": "新增权益", "selectRights": "选择权益", "rightCodeSearch": "权益代码搜索", "rightNameSearch": "权益名称搜索", "selectAll": "全选", "addSelected": "添加", "deleteRight": "删除权益", "confirmDeleteRight": "确定要删除此权益吗？", "pushToInsurance": "推送至保险系统", "insurancePushed": "已推送", "confirmPushInsurance": "确定要推送至保险系统吗？", "pushInsuranceSuccess": "推送保险系统成功", "pushInsuranceFailed": "推送保险系统失败", "submitDelivery": "提交交车", "confirmSubmitDelivery": "确定要提交交车申请吗？", "submitDeliverySuccess": "提交交车成功", "submitDeliveryFailed": "提交交车失败", "deliveryConditionsNotMet": "交车条件不满足", "colorChangeNotice": "您已更改车辆颜色，是否提交审核？", "colorChangeSubmitted": "颜色变更已提交审核", "roadTax": "路税", "registrationFee": "注册/过户费", "ownershipClaimFee": "所有权索赔费", "interchangeFee": "咨询费", "otrFeeTotal": "登记费用总金额", "loanTerm": "贷款期数（月）", "loanTermPlaceholder": "请输入贷款期数", "loanAmountPlaceholder": "请输入贷款金额", "buyerTypeIndividual": "个人", "buyerTypeCompany": "公司", "approvalStatusPending": "待审批", "approvalStatusApproved": "已审批", "paymentStatusPendingBalance": "待付尾款", "paymentStatusBalancePaid": "尾款已付", "insuranceStatusNotInsured": "未投保", "insuranceStatusInsuring": "投保中", "insuranceStatusInsured": "已投保", "loanApprovalStatusPending": "待审核", "loanApprovalStatusApproved": "已通过", "loanApprovalStatusRejected": "已驳回", "jpjStatusPending": "待注册", "jpjStatusRegistering": "注册中", "jpjStatusRegistered": "已注册", "jpjStatusFailed": "注册失败", "paymentMethodInstallment": "分期", "batchOperationTip": "请至少选择一条记录进行批量操作", "buyerTypes": {"individual": "个人", "company": "公司"}}, "quotaManagement": {"pageTitle": "预约限量管理", "storeName": "门店名称", "storeCode": "门店编号", "permissionTip": "您当前查看的是默认门店的预约限量配置。如需配置其他门店，请联系管理员。", "configuredListTitle": "已配置限量列表", "addNewQuota": "新增预约限量", "editQuotaTitle": "编辑预约限量", "addNewQuotaTitle": "新增预约限量", "emptyState": "暂未配置预约限量。点击\"新增预约限量\"进行配置。", "unsavedChangesWarning": "您有未保存的更改，确定要关闭吗？", "dateMustBeFutureOrToday": "配置日期不能早于今天", "selectDateFirst": "请先选择日期", "timeSlotExceedsOperatingHours": "时段设置超过营业时间范围（8:00-18:00）", "table": {"configDate": "配置日期", "timeSlotCount": "时段数量", "totalQuota": "总限量", "bookedQuantity": "已预约数量", "lastUpdateTime": "最后更新时间", "operations": "操作", "edit": "编辑", "expired": "已过期"}, "modal": {"selectDateTitle": "选择日期", "dateLabel": "配置日期", "datePlaceholder": "请选择配置日期", "existingConfig": "已有配置", "dateTip": "配置日期不能早于今天", "timeSlotConfigTitle": "时段配置", "addTimeSlot": "添加时段", "noTimeSlots": "尚未添加时段", "clickAddPrompt": "点击\"添加时段\"开始配置", "timeSlot": "时段", "startTime": "开始时间", "startTimePlaceholder": "请选择开始时间", "endTime": "结束时间", "endTimePlaceholder": "请选择结束时间", "quota": "限量", "quotaPlaceholder": "请输入限量", "configDescriptionTitle": "配置说明", "configDescription": {"item1": "每个时段的限量表示该时段最多可预约的车辆数量。", "item2": "时段之间不能重叠，结束时间必须晚于开始时间。", "item3": "建议根据门店实际接待能力设置合理的限量。", "item4": "所有时段限量之和即为当天的总限量。"}}, "validation": {"dateRequired": "请选择配置日期", "atLeastOneTimeSlot": "至少需要添加一个时段", "timeRequired": "请设置时段的开始和结束时间", "startBeforeEnd": "开始时间必须早于结束时间", "quotaPositive": "限量必须大于0", "timeSlotOverlap": "时段 {slot1} 与 {slot2} 时间重叠，请调整"}, "summary": {"selectDate": "请选择日期", "addTimeSlot": "请添加时段", "timeSlotsUnit": "个时段", "totalQuota": "总限量"}}, "salesOrderManagement": {"title": "销售订单列表", "breadcrumb": "客户订单管理 / 销售订单列表", "searchForm": "搜索条件", "operationButtons": "操作", "orderList": "订单列表", "buyerName": "购车人", "buyerNamePlaceholder": "请输入购车人姓名", "buyerPhone": "购车人手机号", "buyerPhonePlaceholder": "请输入购车人手机号", "buyerType": "购车人类别", "buyerTypePlaceholder": "请选择购车人类别", "model": "车型", "modelPlaceholder": "请选择车型", "orderNumber": "订单号", "orderNumberPlaceholder": "请输入订单号", "orderStatus": "订单状态", "orderStatusPlaceholder": "请选择订单状态", "approvalStatus": "订单审核状态", "approvalStatusPlaceholder": "请选择订单审核状态", "paymentStatus": "订单支付状态", "paymentStatusPlaceholder": "请选择订单支付状态", "createTime": "订单创建时间", "createTimeRange": "订单创建时间范围", "insuranceStatus": "投保状态", "insuranceStatusPlaceholder": "请选择投保状态", "loanApprovalStatus": "贷款审核状态", "loanApprovalStatusPlaceholder": "请选择贷款审核状态", "jpjRegistrationStatus": "JPJ车辆注册状态", "jpjRegistrationStatusPlaceholder": "请选择JPJ车辆注册状态", "index": "序号", "ordererName": "下单人", "ordererPhone": "下单人手机号", "variant": "<PERSON><PERSON><PERSON>", "color": "Color", "vin": "VIN", "paymentMethod": "支付方式", "totalAmount": "总金额", "pageTitle": "销售订单列表", "searchTitle": "搜索条件", "batchOperation": "批量操作", "listTitle": "销售订单列表", "totalCount": "总数", "detailTitle": "销售订单详情", "statusSubmitted": "已提交", "statusConfirmed": "已确认", "statusCancelPending": "取消待处理", "statusCancelApproved": "取消已批准", "statusCancelled": "已取消", "statusPendingDelivery": "待交付", "statusDelivered": "已交付", "buyerTypes": {"individual": "个人", "company": "公司"}, "models": {"AXIA": "AXIA", "BEZZA": "BEZZA", "MYVI": "MYVI"}, "orderStatuses": {"submitted": "已提交", "confirmed": "已确认", "cancel_pending": "取消审核中", "cancel_approved": "取消审核通过", "cancelled": "已取消", "pending_delivery": "待交车", "delivered": "已交车"}, "approvalStatuses": {"pending_approval": "待审批", "approved": "已审批"}, "paymentStatuses": {"pending_deposit": "待支付定金", "deposit_paid": "已支付定金", "pending_balance": "待支付尾款", "balance_paid": "已支付尾款", "refunding": "退款中", "refunded": "退款完成"}, "insuranceStatuses": {"not_insured": "未投保", "insuring": "投保中", "insured": "投保完成"}, "loanApprovalStatuses": {"pending_review": "待审批", "approved": "已通过", "rejected": "已驳回"}, "jpjRegistrationStatuses": {"pending_registration": "待注册", "registering": "注册中", "registered": "已注册", "registration_failed": "注册失败"}, "paymentMethods": {"full_payment": "全款", "installment": "分期"}, "viewDetail": "详情", "editOrder": "编辑", "returnToList": "返回列表", "exportSuccess": "导出成功", "exportFailed": "导出失败", "noDataToExport": "没有数据可导出", "confirmExportTitle": "确认导出", "confirmExportMessage": "确定要导出当前筛选条件下的所有订单数据吗？", "orderDetail": "销售订单详情", "customerInfo": "客户信息", "storeInfo": "购车门店信息", "purchaseInfo": "购车信息", "vehicleInfoTab": "车辆信息", "invoicingInfoTab": "开票信息", "rightsInfoTab": "服务&权益信息", "paymentInfoTab": "支付信息", "insuranceInfoTab": "保险信息", "otrFeesTab": "OTR费用信息", "changeRecords": "订单变更记录", "buyerIdType": "购车人身份证件类别", "buyerIdNumber": "购车人身份证件号", "buyerEmail": "购车人邮箱", "buyerAddress": "购车人地址", "buyerState": "购车人所在州", "buyerCity": "购车人所在城市", "buyerPostcode": "购车人所在地邮编", "storeRegion": "所在地区", "storeCity": "所在城市", "storeName": "购车门店", "salesConsultantName": "销售顾问", "salesSubtotal": "销售小计(包含GASA、消费税、销售税)", "numberPlatesFee": "车牌费", "vinCode": "VIN码", "accessoryInfo": "选配件信息", "accessoryCategory": "类别", "accessoryName": "配件名称", "unitPrice": "配件单价", "quantity": "数量", "totalPrice": "总价", "accessoriesTotalAmount": "选配件总金额", "invoicingType": "开票类型", "invoicingName": "开票名称", "invoicingPhone": "开票电话", "invoicingAddress": "开票地址", "rightsInfo": "服务&权益信息", "rightCode": "权益代码", "rightName": "权益名称", "rightMode": "权益模式", "discountAmount": "权益优惠价格", "effectiveDate": "权益生效日期", "expiryDate": "权益终止日期", "rightsDiscountAmount": "权益优惠总金额", "loanApprovalStatusField": "贷款资质审核状态", "depositAmount": "定金金额", "loanAmount": "贷款金额", "balanceAmount": "尾款金额", "insuranceInfo": "保单信息", "policyNumber": "保单号", "insuranceType": "保险类型", "insuranceCompany": "保险公司", "insurancePrice": "保险价格", "insuranceTotalAmount": "保险总金额", "insuranceNotes": "备注", "otrFeesInfo": "On The Road登记费用", "ticketNumber": "票据单号", "feeItem": "收费项目", "feePrice": "收费价格", "otrFeesTotalAmount": "OTR费用总金额", "changeRecordIndex": "序号", "originalContent": "原始内容", "changedContent": "变更后内容", "operator": "操作人", "operationTime": "操作时间", "totalInvoiceAmount": "整车开票价", "remainingAmount": "剩余应收", "editOrderTitle": "销售订单编辑", "personalDetails": "客户信息 - Personal Details", "preferredOutlet": "购车门店信息 - Preferred Outlet & Sales Advisor", "purchaseDetails": "购车信息 - Purchase Details", "addRights": "新增权益", "selectRights": "选择权益", "rightCodeSearch": "权益代码搜索", "rightNameSearch": "权益名称搜索", "selectAll": "全选", "addSelected": "添加", "deleteRight": "删除权益", "confirmDeleteRight": "确定要删除此权益吗？", "pushToInsurance": "推送至保险系统", "insurancePushed": "已推送", "confirmPushInsurance": "确定要推送至保险系统吗？", "pushInsuranceSuccess": "推送保险系统成功", "pushInsuranceFailed": "推送保险系统失败", "submitDelivery": "提交交车", "confirmSubmitDelivery": "确定要提交交车申请吗？", "submitDeliverySuccess": "提交交车成功", "submitDeliveryFailed": "提交交车失败", "deliveryConditionsNotMet": "交车条件不满足", "colorChangeNotice": "您已更改车辆颜色，是否提交审核？", "colorChangeSubmitted": "颜色变更已提交审核", "roadTax": "路税", "registrationFee": "注册/过户费", "ownershipClaimFee": "所有权索赔费", "interchangeFee": "咨询费", "otrFeeTotal": "登记费用总金额", "loanTerm": "贷款期数（月）", "loanTermPlaceholder": "请输入贷款期数", "loanAmountPlaceholder": "请输入贷款金额", "loanApprovalStatusPending": "待审核", "loanApprovalStatusApproved": "审核通过", "loanApprovalStatusRejected": "审核驳回"}, "workOrderApproval": {"title": "工单审核", "subtitle": "工单审批管理", "searchCard": "筛选条件", "operationCard": "功能操作", "tableCard": "审批列表", "pendingTab": "待审批", "completedTab": "已审批", "allTab": "全部", "approvalNo": "审批单号", "approvalType": "审批类型", "approvalStatus": "审批状态", "approvalResult": "审批结果", "submitter": "提交人", "submitTime": "提交时间", "workOrderNo": "工单编号", "requestReason": "申请原因", "customerInfo": "客户信息", "licensePlate": "车牌号", "store": "门店", "currentLevel": "当前审批级别", "overtimeStatus": "超时状态", "approvalTime": "审批时间", "approver": "审批人", "approvalRemark": "审批备注", "claimApproval": "索赔审批", "cancelOrderApproval": "取消工单审批", "pending": "待审核", "completed": "已审核", "approved": "审批通过", "rejected": "审批驳回", "firstLevel": "一级审批", "secondLevel": "二级审批", "approvalNoPlaceholder": "请输入审批单号", "submitterPlaceholder": "请输入提交人姓名", "workOrderNoPlaceholder": "请输入工单编号", "customerInfoPlaceholder": "请输入客户姓名或手机号", "licensePlatePlaceholder": "请输入车牌号", "selectApprovalType": "请选择审批类型", "selectApprovalStatus": "请选择审批状态", "selectApprovalLevel": "请选择审批级别", "selectOvertimeStatus": "请选择超时状态", "selectStore": "请选择门店", "selectTimeRange": "请选择时间范围", "submitTimeStart": "提交开始时间", "submitTimeEnd": "提交结束时间", "approve": "审核", "viewDetail": "查看详情", "operationLog": "操作日志", "approvalAction": "审批操作", "approvalRemarkPlaceholder": "请填写审批备注（驳回时必填）", "approvalRemarkRequired": "驳回时必须填写审批备注", "approvalSuccess": "审批操作成功", "approvalFailed": "审批操作失败", "exportSuccess": "导出成功", "exportFailed": "导出失败", "confirmApproval": "确认审批", "approvalDetail": "审批详情", "basicInfo": "基础信息", "vehicleInfo": "车辆信息", "claimDetails": "索赔内容详情", "workOrderInfo": "工单信息", "approvalHistory": "审批历史", "claimLabor": "索赔工时", "claimParts": "索赔零件", "totalClaimAmount": "索赔总金额", "laborCode": "工时代码", "laborName": "工时名称", "standardLabor": "标准工时", "laborPrice": "工时单价", "partCode": "零件编码", "partName": "零件名称", "quantity": "数量", "unitPrice": "单价", "claimAmount": "索赔金额", "customerName": "客户姓名", "customerPhone": "联系电话", "senderName": "送修人名称", "senderPhone": "送修人手机号", "vin": "VIN码", "model": "车型", "configuration": "配置", "color": "颜色", "mileage": "里程数", "vehicleAge": "车龄", "repairTime": "送修时间", "saleTime": "销售时间", "workOrderStatus": "工单状态", "paymentStatus": "支付状态", "estimatedStartTime": "预计开工时间", "assignedTechnician": "分配技师", "cancelReason": "取消原因", "firstApprover": "一级审批人", "firstApprovalTime": "一级审批时间", "firstApprovalResult": "一级审批结果", "firstApprovalRemark": "一级审批备注", "secondApprover": "二级审批人", "secondApprovalTime": "二级审批时间", "secondApprovalResult": "二级审批结果", "secondApprovalRemark": "二级审批备注", "operationContent": "操作内容", "operator": "操作人", "operationTime": "操作时间", "operationRemark": "操作备注", "systemOperation": "系统操作", "submitClaimApproval": "提交索赔审批", "firstLevelApprovalPass": "一级审批通过", "firstLevelApprovalReject": "一级审批驳回", "secondLevelApprovalPass": "二级审批通过", "secondLevelApprovalReject": "二级审批驳回", "submitCancelRequest": "提交取消申请", "cancelApprovalPass": "取消审批通过", "cancelApprovalReject": "取消审批驳回", "hours": "小时", "yuan": "元", "pieces": "个", "months": "个月", "km": "公里", "draft": "草稿", "waitingApproval": "等待审批", "confirmed": "已确认", "cancelled": "已取消", "pendingPayment": "待支付", "paid": "已支付", "technician": "技师", "serviceAdvisor": "服务顾问", "technicianManager": "技师经理", "factoryManager": "厂端管理人员", "allStores": "全部门店", "allTypes": "全部类型", "allStatus": "全部状态", "allLevels": "全部级别", "allOvertimeStatus": "全部超时状态", "records": "条记录", "currentPage": "第", "totalPages": "共", "pages": "页", "itemsPerPage": "每页显示", "goToPage": "跳转至第", "pageNumber": "页", "statistics": "统计信息", "totalApprovals": "总审批数", "pendingApprovals": "待审批数", "completedApprovals": "已审批数", "overtimeApprovals": "超时审批数", "approvalPassRate": "审批通过率", "refreshData": "刷新数据", "columnSettings": "列设置", "tableView": "表格视图", "cardView": "卡片视图", "advancedFilter": "高级筛选", "collapseFilter": "收起筛选", "expandFilter": "展开筛选", "noPermission": "无权限操作", "dataLoading": "数据加载中...", "networkError": "网络连接失败", "serverError": "服务器错误", "dataError": "数据格式错误", "permissionDenied": "权限不足", "dataExpired": "数据已过期，请刷新页面", "retryRequest": "重试请求", "reportError": "报告错误", "feedbackChannel": "意见反馈", "approvalTimeout": "审批超时提醒", "approvalNotification": "审批状态通知", "resultNotification": "审批结果通知", "systemMessage": "系统消息", "emailNotification": "邮件通知", "smsNotification": "短信通知", "notificationHistory": "通知历史", "enableNotification": "启用通知", "disableNotification": "禁用通知", "notificationSettings": "通知设置"}}