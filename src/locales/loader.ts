/**
 * 国际化模块加载器
 * 用于动态加载和合并各模块的国际化文件
 */

interface LocaleMessages {
  [key: string]: unknown;
}

interface ModuleImporter {
  (): Promise<{ default: LocaleMessages }>;
}

interface ModuleLoaders {
  [moduleName: string]: {
    zh: ModuleImporter;
    en: ModuleImporter;
  };
}

// 七个核心模块的导入函数
const moduleLoaders: ModuleLoaders = {
  common: {
    zh: () => import('./modules/common/zh.json'),
    en: () => import('./modules/common/en.json'),
  },
  sales: {
    zh: () => import('./modules/sales/zh.json'),
    en: () => import('./modules/sales/en.json'),
  },
  afterSales: {
    zh: () => import('./modules/afterSales/zh.json'),
    en: () => import('./modules/afterSales/en.json'),
  },
  parts: {
    zh: () => import('./modules/parts/zh.json'),
    en: () => import('./modules/parts/en.json'),
  },
  finance: {
    zh: () => import('./modules/finance/zh.json'),
    en: () => import('./modules/finance/en.json'),
  },
  base: {
    zh: () => import('./modules/base/zh.json'),
    en: () => import('./modules/base/en.json'),
  },
  quotaManagement: {
    zh: () => import('./modules/quotaManagement/zh.json'),
    en: () => import('./modules/quotaManagement/en.json'),
  },
  menu: {
    zh: () => import('./modules/menu/zh.json'),
    en: () => import('./modules/menu/en.json'),
  },
};

/**
 * 加载指定语言的所有模块
 * @param locale 语言代码 ('zh' | 'en')
 * @returns 合并后的语言包
 */
export async function loadLocaleMessages(locale: 'zh' | 'en'): Promise<LocaleMessages> {
  const messages: LocaleMessages = {};

  // 并行加载所有模块
  const loadPromises = Object.entries(moduleLoaders).map(async ([moduleName, loaders]) => {
    try {
      const moduleData = await loaders[locale]();
      return { moduleName, data: moduleData.default };
    } catch (error) {
      console.warn(`Failed to load ${locale} locale for module ${moduleName}:`, error);
      return { moduleName, data: {} };
    }
  });

  const results = await Promise.all(loadPromises);

  // 合并所有模块的数据
  results.forEach(({ moduleName, data }) => {
    messages[moduleName] = data;
  });

  return messages;
}

/**
 * 加载特定模块的语言包
 * @param modules 需要加载的模块列表
 * @param locale 语言代码
 * @returns 指定模块的语言包
 */
export async function loadModuleMessages(
  modules: string[],
  locale: 'zh' | 'en'
): Promise<LocaleMessages> {
  const messages: LocaleMessages = {};

  const loadPromises = modules.map(async (moduleName) => {
    const loaders = moduleLoaders[moduleName];
    if (!loaders) {
      console.warn(`Module ${moduleName} not found in loaders`);
      return { moduleName, data: {} };
    }

    try {
      const moduleData = await loaders[locale]();
      return { moduleName, data: moduleData.default };
    } catch (error) {
      console.warn(`Failed to load ${locale} locale for module ${moduleName}:`, error);
      return { moduleName, data: {} };
    }
  });

  const results = await Promise.all(loadPromises);

  // 合并指定模块的数据
  results.forEach(({ moduleName, data }) => {
    messages[moduleName] = data;
  });

  return messages;
}

/**
 * 热重载特定模块的语言包
 * @param moduleName 模块名称
 * @param locale 语言代码
 * @returns 模块的语言包
 */
export async function reloadModule(moduleName: string, locale: 'zh' | 'en'): Promise<LocaleMessages> {
  const loaders = moduleLoaders[moduleName];
  if (!loaders) {
    throw new Error(`Module ${moduleName} not found`);
  }

  try {
    const moduleData = await loaders[locale]();
    return { [moduleName]: moduleData.default };
  } catch (error) {
    console.error(`Failed to reload module ${moduleName} for locale ${locale}:`, error);
    throw error;
  }
}

/**
 * 获取所有可用的模块列表
 * @returns 模块名称数组
 */
export function getAvailableModules(): string[] {
  return Object.keys(moduleLoaders);
}

/**
 * 注册新的模块加载器
 * @param moduleName 模块名称
 * @param loaders 加载器对象
 */
export function registerModule(moduleName: string, loaders: { zh: ModuleImporter; en: ModuleImporter }): void {
  moduleLoaders[moduleName] = loaders;
  console.log(`Module ${moduleName} registered successfully`);
}
