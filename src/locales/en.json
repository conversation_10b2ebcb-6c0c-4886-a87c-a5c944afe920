{
  "common": {
    "confirm": "Confirm",
    "cancel": "Cancel",
    "void": "Void",
    "search": "Search",
    "operationSuccessful": "Operation successful",
    "operationFailed": "Operation failed!",
    "reLoginPrompt": "Login expired, please re-login.",
    "warning": "Warning",
    "noPermission": "You have no permission for this operation.",
    "networkError": "Network error, please try again later.",
    "badRequest": "Bad Request.",
    "unauthorized": "Unauthorized, please re-login.",
    "forbidden": "Access Denied.",
    "notFound": "Request address error, resource not found.",
    "requestTimeout": "Request timed out.",
    "serverError": "Internal server error.",
    "notImplemented": "Service not implemented.",
    "badGateway": "Bad Gateway.",
    "serviceUnavailable": "Service Unavailable.",
    "gatewayTimeout": "Gateway Timeout.",
    "unknownError": "Unknown error.",
    "noResponse": "No response from server.",
    "requestSetupError": "Request setup error.",
    "reset": "Reset",
    "operations": "Operations",
    "yes": "Yes",
    "no": "No",
    "hour": "Hour",
    "hours": "Hours",
    "confirmDelete": "Are you sure to delete '{item}'?",
    "languageChanged": "Language changed successfully!",
    "edit": "Edit",
    "delete": "Delete",
    "startDate": "Start Date",
    "endDate": "End Date",
    "confirmSave": "Are you sure to save?",
    "confirmAdd": "Are you sure to add?",
    "exporting": "Exporting...",
    "noData": "No Data",
    "query": "Query",
    "queryItems": "Query Items",
    "sequence": "Sequence",
    "detail": "Detail",
    "details": "Details",
    "scrapRecord": "Scrap Record",
    "to": "to",
    "fetchFailed": "Failed to fetch data",
    "exportReport": "Export Report",
    "exportingReport": "Exporting report...",
    "pleaseSelectOne": "Please select one item",
    "pleaseSelectData": "Please select data",
    "upload": "Upload",
    "good": "Good",
    "needsAttention": "Needs Attention",
    "bad": "Bad",
    "notApplicable": "N/A",
    "all": "All",
    "noDataTip": "No relevant data available, please try adjusting the filter conditions",
    "close": "Close",
    "confirmExport": "Confirm Export",
    "success": "Success",
    "paginationLayout": "total, sizes, prev, pager, next, jumper",
    "add": "Add",
    "loading": "Loading...",
    "total": "Total",
    "items": "items",
    "page": "page",
    "required": "Required",
    "pleaseSelect": "Please Select",
    "pleaseInput": "Please Input",
    "unknown": "Unknown",
    "vehicleAge": "Vehicle Age",
    "months": "months",
    "createWorkOrder": "Create Work Order",
    "submitConfirm": "Submit Confirmation",
    "assign": "Assign",
    "print": "Print",
    "recall": "Recall",
    "customerConfirm": "Customer Confirmation",
    "export": "Export",
    "refresh": "Refresh",
    "saveConfig": "Save Configuration",
    "remove": "Remove",
    "notice": "Notice",
    "optional": "(Optional)",
    "selectPlaceholder": "Please select",
    "inputPlaceholder": "Please input",
    "clear": "Clear",
    "uploadSuccessful": "Upload Successful",
    "pleaseSign": "Please sign first",
    "pleaseUploadSignature": "Please upload signature first",
    "tip": "Tip",
    "confirmSubmitConfirm": "Are you sure to submit confirmation?",
    "confirmRecall": "Are you sure to recall?",
    "confirmPrint": "Are you sure to print?",
    "operationCanceled": "Operation Canceled",
    "index": "Index",
    "save": "Save",
    "submit": "Submit",
    "submitConfirm": "Submit Confirmation",
    "fillAllRequired": "Please fill in all required fields!",
    "unknown": "Unknown"
  },
  "login": {
    "title": "Login",
    "username": "Username",
    "password": "Password",
    "loginButton": "Login",
    "notFound": "Record not found",
    "ownerPhone": "Owner Phone",
    "confirmCreateRepairOrder": "Are you sure to create a repair order for record {checkinId}?"
  },
  "sales": {
    "vehicleList": "Vehicle List",
    "salesOrderList": "Sales Order List",
    "id": "ID",
    "vin": "VIN",
    "model": "Model",
    "brand": "Brand",
    "color": "Color",
    "price": "Price",
    "statusLabel": "Status",
    "manufactureDate": "Manuf. Date",
    "engineNumber": "Engine No.",
    "buyerName": "Buyer Name",
    "buyerNamePlaceholder": "Enter buyer name",
    "buyerPhone": "Buyer Phone",
    "buyerPhonePlaceholder": "Enter buyer phone",
    "buyerType": "Buyer Type",
    "selectBuyerType": "Select buyer type",
    "individual": "Individual",
    "company": "Company",
    "selectModel": "Select model",
    "orderNumber": "Order Number",
    "orderNumberPlaceholder": "Enter order number",
    "orderStatus": "Order Status",
    "selectOrderStatus": "Select order status",
    "approvalStatus": "Approval Status",
    "selectApprovalStatus": "Select approval status",
    "paymentStatus": "Payment Status",
    "selectPaymentStatus": "Select payment status",
    "insuranceStatus": "Insurance Status",
    "selectInsuranceStatus": "Select insurance status",
    "loanApprovalStatus": "Loan Approval Status",
    "selectLoanApprovalStatus": "Select loan approval status",
    "jpjRegistrationStatus": "JPJ Registration Status",
    "selectJpjRegistrationStatus": "Select JPJ registration status",
    "createTime": "Create Time",
    "ordererName": "Orderer Name",
    "ordererNamePlaceholder": "Enter orderer name",
    "ordererPhone": "Orderer Phone",
    "ordererPhonePlaceholder": "Enter orderer phone",
    "variant": "Variant",
    "paymentMethod": "Payment Method",
    "totalPrice": "Total Price",
    "orderAmount": "Order Amount",
    "status": {
      "in_stock": "In Stock",
      "sold": "Sold",
      "reserved": "Reserved"
    },
    "models": {
      "AXIA": "AXIA",
      "BEZZA": "BEZZA",
      "MYVI": "MYVI"
    },
    "addVehicle": "Add Vehicle",
    "editVehicle": "Edit Vehicle",
    "vinPlaceholder": "Enter VIN",
    "modelPlaceholder": "Enter Model",
    "brandPlaceholder": "Enter Brand",
    "statusPlaceholder": "Select Status",
    "colorPlaceholder": "Enter Color",
    "pricePlaceholder": "Enter Price",
    "manufactureDatePlaceholder": "Select Date",
    "engineNumberPlaceholder": "Enter Engine No.",
    "selectStatus": "Please select status",
    "deleteVehicle": "Delete Vehicle",
    "orderDetail": "Order Detail",
    "orderCreateTime": "Order Create Time",
    "accessoriesInfo": "Accessories Information",
    "missingOrderNumber": "Missing order number, will return to list page",
    "fetchDetailError": "Failed to fetch order details, please try again",
    "vin": "VIN Code",
    "deposit": "Deposit Amount",
    "finalPayment": "Final Payment",
    "totalAmount": "Total Invoice Amount",
    "remainingReceivable": "Remaining Receivable",
    "returnToList": "Return",
          "rightsInfoTab": "Service & Rights Information",
      "remarks": "Remarks",
      "rightsTotalDiscount": "Rights Total Discount",
      "buyerTypes": {
        "individual": "Individual",
        "company": "Company"
      },
      "paymentMethods": {
        "full_payment": "Full Payment",
        "installment": "Installment"
      },
      "loanApprovalStatuses": {
        "pending_review": "Pending Review",
        "approved": "Approved",
        "rejected": "Rejected"
      },
      "orderStatuses": {
        "submitted": "Submitted",
        "confirmed": "Confirmed",
        "cancel_pending": "Cancel Pending",
        "cancel_approved": "Cancel Approved",
        "cancelled": "Cancelled",
        "pending_delivery": "Pending Delivery",
        "delivered": "Delivered"
      },
      "approvalStatuses": {
        "pending_approval": "Pending Approval",
        "approved": "Approved"
      },
      "paymentStatuses": {
        "pending_deposit": "Pending Deposit",
        "deposit_paid": "Deposit Paid",
        "pending_balance": "Pending Balance",
        "balance_paid": "Balance Paid",
        "refunding": "Refunding",
        "refunded": "Refunded"
      },
      "insuranceStatuses": {
        "not_insured": "Not Insured",
        "insuring": "Insuring",
        "insured": "Insured"
      },
      "jpjRegistrationStatuses": {
        "pending_registration": "Pending Registration",
        "registering": "Registering",
        "registered": "Registered",
        "registration_failed": "Registration Failed"
      }
    },
  "customer": {
    "detailViewTitle": "Customer Details"
  },
  "checkin": {
    "checkinList": "Check-in Records",
    "checkinIdPlaceholder": "Enter record number",
    "licensePlate": "License Plate",
    "licensePlatePlaceholder": "Enter license plate",
    "vin": "VIN Number",
    "vinPlaceholder": "Enter VIN number",
    "repairPersonName": "Repair Person Name",
    "repairPersonNamePlaceholder": "Enter repair person name",
    "repairPersonPhone": "Repair Person Phone",
    "repairPersonPhonePlaceholder": "Enter repair person phone",
    "createdAt": "Created At",
    "createdAtPlaceholder": "Select create date range",
    "export": "Export",
    "createRecord": "Create Record",
    "id": "No.",
    "checkinId": "Record No.",
    "vehicleModel": "Vehicle Model",
    "vehicleConfiguration": "Configuration",
    "color": "Color",
    "mileage": "Mileage",
    "mileagePlaceholder": "Enter mileage",
    "mileageUnit": "km",
    "serviceAdvisor": "Service Advisor",
    "relatedRepairOrderId": "Related RO No.",
    "serviceType": "Service Type",
    "notes": "Notes",
    "viewDetails": "View Details",
    "edit": "Edit",
    "delete": "Delete",
    "createRepairOrder": "Create RO",
    "addCheckinRecord": "Add Check-in Record",
    "editCheckinRecord": "Edit Check-in Record",
    "vehicleInfoNotFound": "No vehicle info found",
    "vehicleInfoAutoFill": "Auto-filled or manual entry",
    "vehicleAge": "Vehicle Age (Months)",
    "ownerInfo": "Owner Info",
    "serviceTypePlaceholder": "Select service type",
    "notesPlaceholder": "Enter notes",
    "save": "Save",
    "cancel": "Cancel",
    "serviceTypeOptions": {
        "repair": "Repair",
        "maintenance": "Maintenance",
        "inspection": "Inspection",
        "paint": "Paint"
    },
    "repairOrderAlreadyExists": "Repair order already exists for this check-in record.",
    "repairOrderCreatedSuccess": "Repair order created successfully!",
    "confirmCreateRepairOrder": "Are you sure to create a repair order for record {checkinId}?",
    "vehicleInfo": "Vehicle Information",
    "customerInfo": "Customer Information",
    "ownerPhone": "Owner Phone",
    "updatedAt": "Updated At",
    "notFound": "Record not found"
  },
  "afterSales": {
    "appointmentManagement": "Appointment Management",
    "breadcrumb": "After Sales / Appointment Management",
    "status": "Appointment Status",
    "selectStatus": "Select Appointment Status",
    "serviceType": "Service Type",
    "selectServiceType": "Select Service Type",
    "serviceAdvisor": "Service Advisor",
    "selectServiceAdvisor": "Select Service Advisor",
    "appointmentNo": "Appointment No.",
    "appointmentNoPlaceholder": "Enter appointment no.",
    "customerName": "Customer Name",
    "customerNamePlaceholder": "Enter customer name",
    "customerPhone": "Customer Phone",
    "customerPhonePlaceholder": "Enter customer phone",
    "createTime": "Create Time",
    "createTimePlaceholder": "Select create time",
    "export": "Export",
    "createAppointment": "Create Appointment",
    "serialNumber": "No.",
    "licensePlate": "License Plate",
    "vehicleModel": "Vehicle Model",
    "vehicleColor": "Color",
    "expectedArrivalTime": "Expected Arrival Time",
    "actualArrivalTime": "Actual Arrival Time",
    "operations": "Operations",
    "viewDetails": "View Details",
    "edit": "Edit",
    "cancelAppointment": "Cancel Appointment",
    "checkIn": "Check-in",
    "statuses": {
      "not_arrived": "Not Arrived",
      "arrived": "Arrived",
      "cancelled": "Cancelled",
      "no_show": "No Show",
      "pending_payment": "Pending Payment"
    },
    "serviceTypes": {
      "maintenance": "Maintenance",
      "repair": "Repair"
    },
    "confirmCancel": "Are you sure to cancel this appointment?",
    "appointmentDetail": "Appointment Detail",
    "appointmentId": "Appointment ID",
    "appointmentTime": "Appointment Date",
    "customerInfo": "Customer Info",
    "vehicleInfo": "Vehicle Info",
    "mileage": "Mileage",
    "appointmentInfo": "Appointment Info",
    "store": "Store",
    "technician": "Technician",
    "serviceContent": "Service Content",
    "paymentInfo": "Payment Info",
    "paymentStatus": "Payment Status",
    "paymentAmount": "Payment Amount",
    "paymentOrderNumber": "Payment Order No.",
    "paymentStatuses": {
      "paid": "Paid",
      "unpaid": "Unpaid",
      "refunded": "Refunded"
    },
    "viewMore": "View More"
  },
  "orderApproval": {
    "pageTitle": "Order Approval",
    "pendingTab": "To be approved",
    "approvedTab": "Approved",
    "searchTitle": "Filter Conditions",
    "approvalType": "Approval Type",
    "approvalTypePlaceholder": "Please select approval type",
    "cancelOrderApproval": "Cancel Order Approval",
    "modifyOrderApproval": "Modify Order Approval",
    "vehicleColorModificationApproval": "Vehicle Color Modification Approval",
    "orderNumber": "Order Number",
    "orderNumberPlaceholder": "Please enter order number",
    "submittedBy": "Submitted by",
    "submittedByPlaceholder": "Please enter submitter name",
    "submissionTime": "Submission Time",
    "submissionTimeEnd": "Submission Time End",
    "startDate": "Start Date",
    "endDate": "End Date",
    "approvalStatus": "Approval Status",
    "approvalStatusPlaceholder": "Please select approval status",
    "pendingInitialReview": "Pending Initial Review",
    "pendingFinalReview": "Pending Final Review",
    "aboutToTimeout": "About to Timeout",
    "approvalResult": "Approval Result",
    "approvalResultPlaceholder": "Please select approval result",
    "approved": "Approved",
    "rejected": "Rejected",
    "timeoutRejected": "Timeout Rejected",
    "store": "Store",
    "storePlaceholder": "Please select store",
    "serialNumber": "Serial Number",
    "approvalNumber": "Approval Number",
    "remainingTime": "Remaining Time",
    "approvalTime": "Approval Time",
    "approvedBy": "Approved By",
    "approve": "Approve",
    "reject": "Reject",
    "approvalHistory": "Approval History",
    "totalCount": "Total {count} records",
    "selectedCount": "Selected {count} records",
    "batchApprove": "Batch Approve",
    "batchReject": "Batch Reject",
    "days": "days",
    "minutes": "minutes",
    "timeout": "Timeout",
    "urgent": "Urgent",
    "aboutTimeout": "About Timeout",
    "highPriority": "High Priority",
    "initialReview": "Initial Review",
    "finalReview": "Final Review",
    "pendingApprovalList": "Pending Approval List",
    "approvedList": "Approved List",
    "fetchDataFailed": "Failed to fetch data",
    "orderDetailNotImplemented": "Order detail function not implemented yet",
    "approveSuccess": "Approval successful",
    "rejectSuccess": "Rejection successful",
    "operationFailed": "Operation failed",
    "batchOperationSuccess": "Batch operation successful",
    "batchOperationFailed": "Batch operation failed",
    "exportSuccess": "Export successful",
    "exportFailed": "Export failed"
  },
  "invoice": {
    "title": "Invoice Management",
    "invoiceNumber": "Invoice Number",
    "invoiceNumberPlaceholder": "Enter invoice number",
    "customerName": "Customer Name",
    "customerNamePlaceholder": "Enter customer name",
    "customerPhone": "Customer Phone",
    "customerPhonePlaceholder": "Enter customer phone",
    "customerEmail": "Customer Email",
    "customerEmailPlaceholder": "Enter customer email",
    "orderNumber": "Order Number",
    "orderNumberPlaceholder": "Enter order number",
    "vin": "VIN Code",
    "vinPlaceholder": "Enter VIN code",
    "salesType": "Sales Type",
    "salesStore": "Sales Store",
    "salesConsultant": "Sales Consultant",
    "invoiceDate": "Invoice Date",
    "invoiceDateRange": "Invoice Date Range",
    "batchPrint": "Batch Print",
    "export": "Export",
    "detail": "Detail",
    "print": "Print",
    "email": "Email",
    "log": "Log",
    "invoiceAmount": "Invoice Amount",
    "createdTime": "Created Time",
    "customerAddress": "Customer Address",
    "customerState": "Customer State",
    "customerCity": "Customer City",
    "model": "Model",
    "variant": "Variant",
    "color": "Color",
    "paymentMethod": "Payment Method",
    "financeCompany": "Finance Company",
    "loanAmount": "Loan Amount",
    "emailConfirm": "Email Confirmation",
    "confirmSendEmail": "Confirm to send email to customer?",
    "exportConfig": "Export Configuration",
    "exportFormat": "Export Format",
    "exportScope": "Export Scope",
    "currentPage": "Current Page",
    "allData": "All Data",
    "filteredData": "Filtered Data",
    "operationLog": "Operation Log",
    "operationType": "Operation Type",
    "operator": "Operator",
    "operationTime": "Operation Time",
    "operationResult": "Operation Result",
    "operationDescription": "Operation Description",
    "errorMessage": "Error Message",
    "remark": "Remark",
    "printSuccess": "Print successful",
    "emailSentSuccess": "Email sent successfully",
    "exportSuccess": "Export successful",
    "batchPrintSuccess": "Batch print successful",
    "pleaseSelectRecords": "Please select records to operate",
    "invoiceDetail": "Invoice Detail",
    "basicInfo": "Invoice Basic Information",
    "customerInfo": "Customer Detailed Information",
    "vehicleInfo": "Vehicle Detailed Information",
    "financeInfo": "Finance Information Details",
    "insuranceInfo": "Insurance Information Details",
    "priceStructure": "Price Structure Details",
    "receipts": "Receipt Details Information",
    "otrFees": "OTR Fees Details",
    "companyName": "Company Name",
    "companyAddress": "Company Address",
    "gstNumber": "GST Number",
    "sstNumber": "SST Number",
    "engineNumber": "Engine Number",
    "chassisNumber": "Chassis Number",
    "engineCapacity": "Engine Capacity",
    "transmission": "Transmission",
    "loanTerm": "Loan Term",
    "months": "Months",
    "interestRate": "Interest Rate",
    "monthlyPayment": "Monthly Payment",
    "insuranceCompany": "Insurance Company",
    "agentCode": "Agent Code",
    "policyNumber": "Policy Number",
    "policyDate": "Policy Date",
    "insuranceAmount": "Insurance Amount",
    "vehiclePrice": "Vehicle Sales Price",
    "adjustmentAmount": "Adjustment Amount",
    "accessories": "Accessory Details",
    "category": "Category",
    "accessoryName": "Accessory Name",
    "unitPrice": "Unit Price",
    "quantity": "Quantity",
    "totalPrice": "Total Price",
    "feeType": "Fee Type",
    "description": "Description",
    "amount": "Amount",
    "receiptNumber": "Receipt Number",
    "effectiveDate": "Effective Date",
    "paymentDate": "Payment Date",
    "bankName": "Bank Name",
    "accountNumber": "Account Number",
    "status": "Status",
    "invoiceStatuses": {
      "issued": "Issued",
      "printed": "Printed",
      "sent": "Sent"
    },
    "operationTypes": {
      "view": "View Detail",
      "print": "Print"
    },
    "operationResults": {
      "success": "Success",
      "failed": "Failed"
    },
    "operationRemarks": {
      "viewDetail": "View invoice detail",
      "printSuccess": "Invoice printed successfully"
    },
    "messages": {
      "fetchListFailed": "Failed to fetch invoice list",
      "fetchStoreListFailed": "Failed to fetch store list",
      "fetchConsultantListFailed": "Failed to fetch consultant list",
      "fetchDetailFailed": "Failed to fetch invoice detail",
      "printFailed": "Print failed",
      "batchPrintFailed": "Batch print failed",
      "fetchInvoiceInfoFailed": "Failed to fetch invoice information",
      "emailSendFailed": "Email send failed",
      "exportFailed": "Export failed"
    },
    "salesTypeOptions": {
      "PHP": "PHP",
      "CASH": "Cash",
      "FINANCE": "Finance"
    },
    "defaultValues": {
      "companyName": "PERODUA SALES SDN BHD",
      "companyAddress": "Company Address",
      "gstNumber": "001821038956",
      "sstNumber": "B16-1808-31036508",
      "contactPhone": "03-79474600",
      "contactEmail": "<EMAIL>",
      "salesConsultantId": "500598",
      "tinNumber": "EI0000000010",
      "modelCode": "MODELX",
      "modelDescription": "Sedan",
      "engineNumber": "2NR3D31119",
      "chassisNumber": "PM2M8D6SD02448917",
      "engineCapacity": "1496 cc",
      "fuelType": "Petrol",
      "transmission": "Automatic",
      "year": "2025",
      "vehicleRegistrationDate": "09-MAY-2025",
      "creator": "System Administrator",
      "updater": "System Administrator",
      "updateTime": "2024-01-15 11:00:00",
      "financeType": "Finance",
      "loanTerm": 108,
      "insuranceCompany": "TAKAFUL IKHLAS GENERAL BERHAD",
      "agentCode": "TI",
      "policyNumber": "MMP25635488",
      "policyDate": "08-MAY-2025",
      "vehiclePrice": 47898.33,
      "licensePlateFee": 0.00,
      "totalAccessoryAmount": 2800.00,
      "subtotal": 60015.55,
      "totalOtrFeeAmount": 320.00,
      "insurancePremium": 2179.10,
      "totalSalesPrice": 62484.65,
      "adjustmentAmount": 0.00,
      "invoiceNetValue": 62484.65
    },
    "mockData": {
      "accessories": [
        { "category": "Interior", "name": "Leather Seats" },
        { "category": "Exterior", "name": "Sport Wheels" }
      ],
      "otrFees": [
        { "billNumber": "OTR001", "feeItem": "Road Tax" },
        { "billNumber": "OTR002", "feeItem": "Registration Fee" }
      ],
      "receipts": [
        { "receiptNumber": "R001", "businessType": "Sales", "serialNumber": "LS001", "channel": "Bank Transfer", "collectionType": "Deposit", "remark": "Deposit Payment" },
        { "receiptNumber": "R002", "businessType": "Sales", "serialNumber": "LS002", "channel": "Loan", "collectionType": "Balance", "remark": "Loan Arrival" }
      ]
    },
    "detailLabels": {
      "companyInfo": "Company Information",
      "contactPhone": "Contact Phone",
      "contactEmail": "Contact Email",
      "postcode": "Postcode",
      "state": "State",
      "city": "City",
      "deliveryNumber": "Delivery Number",
      "salesConsultantId": "Sales Consultant ID",
      "tinNumber": "TIN Number",
      "modelCode": "Model Code",
      "modelDescription": "Model Description",
      "fuelType": "Fuel Type",
      "year": "Year",
      "vehicleRegistrationDate": "Vehicle Registration Date",
      "creator": "Creator",
      "updater": "Updater",
      "updateTime": "Update Time",
      "financeType": "Finance Type",
      "issueDate": "Issue Date",
      "priceStructureDetails": "Price Structure Details",
      "vehicleSalesPrice": "Vehicle Sales Price",
      "licensePlateFee": "License Plate Fee",
      "optionalAccessories": "Optional Accessories",
      "totalAccessoryAmount": "Total Accessory Amount",
      "subtotal": "Subtotal",
      "otrRegistrationFees": "OTR Registration Fees",
      "billNumber": "Bill Number",
      "feeItem": "Fee Item",
      "feePrice": "Amount",
      "expiryDate": "Expiry Date",
      "totalOtrFeeAmount": "Total OTR Fee Amount",
      "totalSalesPrice": "Total Sales Price",
      "invoiceNetValue": "Invoice Net Value",
      "receiptDetails": "Receipt Details",
      "businessType": "Business Type",
      "serialNumber": "Serial Number",
      "channel": "Channel",
      "collectionType": "Collection Type",
      "arrivalTime": "Arrival Time"
    }
  },
  "inspectionForm": {
    "title": "Inspection Form Management",
    "form": {
      "inspectionNo": "Inspection Form No.",
      "inspectionStatus": "Inspection Status",
      "licensePlateNo": "License Plate No.",
      "repairmanName": "Repairman Name",
      "technician": "Technician",
      "repairmanPhone": "Repairman Phone No.",
      "createTime": "Creation Time"
    },
    "status": {
      "pending": "Pending Inspection",
      "inProgress": "Inspecting",
      "pendingConfirm": "Pending Confirmation",
      "confirmed": "Confirmed",
      "undefined": "Unknown Status"
    },
    "table": {
      "index": "Index",
      "inspectionNo": "Inspection Form No.",
      "inspectionStatus": "Inspection Status",
      "repairmanName": "Repairman Name",
      "repairmanPhone": "Repairman Phone No.",
      "licensePlateNo": "License Plate No.",
      "modelConfig": "Model Configuration",
      "color": "Color",
      "serviceAdvisor": "Service Advisor",
      "technician": "Technician",
      "registerType": "Registration Type",
      "serviceType": "Service Type",
      "customerConfirmTime": "Customer Confirmation Time",
      "createTime": "Creation Time",
      "updateTime": "Update Time"
    },
    "assignInspectionForm": {
      "title": "Assign Inspection Form",
      "inspectionNo": "Inspection Form No.",
      "licensePlateNo": "License Plate No.",
      "repairmanName": "Repairman Name",
      "registerType": "Registration Type",
      "serviceType": "Service Type",
      "technician": "Technician",
      "selectTechnicianPlaceholder": "Please select technician"
    },
    "inspectionFormDetailEdit": {
      "titleEdit": "Edit Inspection Form",
      "titleDetail": "Inspection Form Details",
      "clientInfo": "Client Information",
      "reserveName": "Reservist Name",
      "reservePhone": "Reservist Phone",
      "repairmanName": "Repairman Name",
      "repairmanPhone": "Repairman Phone",
      "remark": "Remark",
      "vehicleInfo": "Vehicle Information",
      "licensePlateNo": "License Plate No.",
      "vin": "VIN No.",
      "modelConfig": "Model Configuration",
      "color": "Color",
      "mileage": "Mileage",
      "vehicleAge": "Vehicle Age",
      "inspectionFormInfo": "Inspection Form Information",
      "inspectionStatus": "Inspection Form Status",
      "registerType": "Registration Type",
      "servicePackageName": "Service Package Name",
      "createTime": "Creation Time",
      "serviceAdvisor": "Service Advisor",
      "technician": "Technician",
      "customerConfirmTime": "Customer Confirmation Time",
      "customerConfirmImage": "Customer Confirmation Image",
      "inspectionContentList": "Inspection Content List",
      "parkingAreaRecord": "Parking Area Record",
      "waitingArea": "Waiting Area",
      "leavingArea": "Leaving Area",
      "parkingZone": "Parking Zone",
      "dashboardInspection": "Dashboard Inspection",
      "mileageRecord": "Mileage/Odometer",
      "batteryLevel": "Battery Level Check",
      "remainingRange": "Remaining Range Display",
      "energyConsumption": "Energy Consumption Display",
      "functionalityCheck": "Functionality Check",
      "gaugesIndicators": "Gauges & Indicators",
      "checkFunction": "Check Function",
      "batteryStatusIndicator": "Battery Status Indicator",
      "chargingStatusIndicator": "Charging Status Indicator",
      "energyConsumptionFunction": "Energy Consumption Display Function",
      "airConditioningSystem": "Air Conditioning System",
      "heatPumpCheck": "Heat Pump Check",
      "batteryPreheating": "Battery Pre-heating",
      "wiperWasher": "Wiper & Washer",
      "checkVisualCondition": "Check Visual Condition",
      "infotainmentSystem": "Infotainment System",
      "chargingStationNavigation": "Charging Station Navigation",
      "energyMonitoring": "Energy Monitoring",
      "otaUpdateStatus": "OTA Update Status",
      "warningLightsCheck": "Warning Lights Check",
      "batterySystem": "Battery System",
      "motorSystem": "Motor System",
      "chargingSystem": "Charging System",
      "highVoltageSystem": "High Voltage System",
      "coolingSystem": "Cooling System",
      "regenerativeBraking": "Regenerative Braking",
      "insulationMonitoring": "Insulation Monitoring",
      "abs": "ABS",
      "eps": "EPS Power Steering",
      "exteriorInspection": "Exterior Inspection",
      "bodyExteriorInspection": "Body Exterior Inspection",
      "frontCheck": "Front Check",
      "rearCheck": "Rear Check",
      "leftSideCheck": "Left Side Check",
      "rightSideCheck": "Right Side Check",
      "roofViewCheck": "Roof View Check",
      "chargingPortCover": "Charging Port Cover",
      "electricSystemInspection": "Electric System Inspection",
      "highVoltageBatteryCheck": "High Voltage Battery Check",
      "batteryPackVisualInspection": "Battery Pack Visual Inspection",
      "noDamage": "No Damage",
      "noLeakage": "No Leakage",
      "mountingBoltsSecure": "Mounting Bolts Secure",
      "batteryCoolingSystem": "Battery Cooling System",
      "coolantLevel": "Coolant Level",
      "coolingFanFunction": "Cooling Fan Function",
      "radiatorCleanliness": "Radiator Cleanliness",
      "highVoltageCableInspection": "High Voltage Cable Inspection",
      "insulationIntegrity": "Insulation Integrity",
      "connectorSealing": "Connector Sealing",
      "fixingClipsStatus": "Fixing Clips Status",
      "driveMotor": "Drive Motor",
      "operatingNoiseCheck": "Operating Noise Check",
      "vibrationCheck": "Vibration Check",
      "temperatureMonitoring": "Temperature Monitoring",
      "motorController": "Motor Controller",
      "visualInspection": "Visual Inspection",
      "heatDissipationSystem": "Heat Dissipation System",
      "connectionHarness": "Connection Harness",
      "chargingSystemCheck": "Charging System Check",
      "onboardCharger": "Onboard Charger",
      "functionTest": "Function Test",
      "heatDissipationCheck": "Heat Dissipation Check",
      "chargingInterface": "Charging Interface",
      "contactCleanliness": "Contact Cleanliness",
      "lockingMechanism": "Locking Mechanism",
      "sealingPerformance": "Sealing Performance",
      "tireWearInspection": "Tire Wear Inspection",
      "tyreThreadCheck": "Tyre Tread Depth",
      "checkDeviation": "Check Deviation",
      "lowRollingResistanceTyreStatus": "Low Rolling Resistance Tyre Status",
      "tirePosition": "Tire Position",
      "frontRight": "Front Right",
      "frontLeft": "Front Left",
      "rearRight": "Rear Right",
      "rearLeft": "Rear Left",
      "tirePressureMonitoring": "Tire Pressure Monitoring",
      "tpmsFunctionCheck": "TPMS Function Check",
      "tyrePressureStandardConfirmation": "Tyre Pressure Standard Confirmation",
      "temperatureMonitoringFunction": "Temperature Monitoring Function",
      "customerSelfDescribedProblem": "Customer Self-Described Problem",
      "attentionRequired": "Attention Required",
      "notGood": "Not Good"
    },
    "customerConfirm": {
      "title": "Customer Confirmation",
      "inspectionNo": "Inspection Form No.",
      "licensePlateNo": "License Plate No.",
      "repairmanName": "Repairman Name",
      "customerConfirmTime": "Customer Confirmation Time",
      "selectDatePlaceholder": "Select Date",
      "uploadedFiles": "Uploaded Images",
      "clickToUploadImage": "Click to Upload Image",
      "pleaseSelectConfirmTime": "Please select customer confirmation time.",
      "pleaseUploadFile": "Please upload at least one image."
    }
  },
  "menu": {
    "home": "DMS Dashboard",
    "customer-detail": "Customer Detail",
    "customerDetail": "Customer Detail",
    "checkin-list": "Check-in List",
    "checkinList": "Check-in List",
    "appointment-dashboard": "Appointment Dashboard",
    "appointmentDashboard": "Appointment Dashboard",
    "appointment-management": "Appointment Management",
    "appointmentManagement": "Appointment Management",
    "quota-management": "Quota Management",
    "quotaManagement": "Quota Management",
    "sales-order": "Sales Order",
    "salesOrder": "Sales Order",
    "sales-order-management": "Sales Order Management",
    "inventory-report": "Inventory Report",
    "inventoryReport": "Inventory Report",
    "inventory-report-hq": "Inventory Report HQ",
    "inventoryReportHQ": "Inventory Report HQ",
    "part-management": "Part Management",
    "partManagement": "Part Management",
    "parts-management-hq": "Parts Management HQ",
    "partsManagementHQ": "Parts Management HQ",
    "parts-receipt": "Parts Receipt",
    "partsReceipt": "Parts Receipt",
    "parts-receipt-query": "Parts Receipt Query",
    "partsReceiptQuery": "Parts Receipt Query",
    "part-archives": "Part Archives",
    "partArchives": "Part Archives",
    "part-configuration": "Part Configuration",
    "partConfiguration": "Part Configuration",
    "inspection-form": "Inspection Form Management",
    "inspectionForm": "Inspection Form Management",
    "work-order": "Work Order Management",
    "workOrder": "Work Order Management",
    "dispatch-management": "Dispatch Management",
    "dispatchManagement": "Dispatch Management",
    "work-assignment-dashboard": "Work Assignment Dashboard",
    "workAssignmentDashboard": "Work Assignment Dashboard",
    "approval": "Order Approval",
    "vehicle-query": "Vehicle Query",
    "vehicleQuery": "Vehicle Query",
    "vehicle-allocation": "Vehicle Allocation Management",
    "vehicleAllocation": "Vehicle Allocation Management",
    "order-management": "Order Management",
    "order-approval-management": "Order Approval Management",
    "orderApprovalManagement": "Order Approval Management",
    "delivery-management": "Delivery Management",
    "deliveryManagement": "Delivery Management",
    "order-vehicle-assignment-management": "Order Vehicle Assignment Management",
    "vehicle-registration": "Vehicle Registration",
    "vehicleRegistration": "Vehicle Registration",
    "invoice-management": "Invoice Management",
    "invoiceManagement": "Invoice Management",
    "lead-pool-management": "Lead Pool Management",
    "leadPoolManagement": "Lead Pool Management",
    "test-drive-report": "Test Drive Report",
    "order-statistics-management": "Order Statistics Management",
    "orderStatisticsManagement": "Order Statistics Management",
    "factory-order-management": "Factory Order Management",
    "factoryOrderManagement": "Factory Order Management",
    "potential-customer-management": "Potential Customer Management",
    "potential-customer-defeat-approval-management": "Potential Customer Defeat Approval Management",
    "potentialCustomerDefeatApprovalManagement": "Potential Customer Defeat Approval Management",
    "test-drive-registration": "Test Drive Registration",
    "testDriveRegistration": "Test Drive Registration",
    "test-drive-list": "Test Drive Registration List",
    "testDriveList": "Test Drive List",
    "whole-vehicle-collection-management": "Whole Vehicle Collection Management",
    "wholeVehicleCollectionManagement": "Whole Vehicle Collection Management",
    "salesProspect": "Sales Prospect",
    "factoryProspect": "Factory Prospect",
    "defeatAudit": "Defeat Audit",
    "addProspect": "Add Prospect",
    "workOrderApproval": "Work Order Approval",
    "testDriveReport": "Test Drive Report",
    "order": "Order"
  },
  "inventoryReport": {
    "title": "Inventory Information (Store)",
    "description": "Report page displaying total inventory, available inventory, locked inventory, defective products, and pending receipts for the store.",
    "partName": "Part Name",
    "partNamePlaceholder": "Enter part name",
    "partNumber": "Part Number",
    "partNumberPlaceholder": "Enter part number",
    "supplierName": "Supplier Name",
    "supplierNamePlaceholder": "Enter supplier name",
    "requisitionDate": "Requisition Date",
    "requisitionDateRange": "Requisition Date Range",
    "inventoryStatus": "Inventory Status",
    "inventoryStatusPlaceholder": "Select inventory status",
    "statusNormal": "Normal",
    "statusBelowSafety": "Below Safety Stock",
    "serialNumber": "Serial Number",
    "totalInventory": "Total Inventory",
    "availableInventory": "Available Inventory",
    "lockedInventory": "Locked Inventory",
    "defectiveProducts": "Defective Products",
    "pendingReceipt": "Pending Receipt",
    "safetyStock": "Safety Stock"
  },
  "inventoryReportHQ": {
    "title": "Inventory Report HQ",
    "description": "Report page displaying total inventory, available inventory, locked inventory, defective products, and pending receipts for HQ.",
    "partName": "Part Name",
    "partNamePlaceholder": "Enter part name",
    "partNumber": "Part Number",
    "partNumberPlaceholder": "Enter part number",
    "supplierName": "Supplier Name",
    "supplierNamePlaceholder": "Enter supplier name",
    "storeName": "Store Name",
    "storeNamePlaceholder": "Enter store name",
    "inventoryStatus": "Inventory Status",
    "inventoryStatusPlaceholder": "Select inventory status",
    "statusNormal": "Normal",
    "statusBelowSafety": "Below Safety Stock",
    "serialNumber": "Serial Number",
    "totalInventory": "Total Inventory",
    "availableInventory": "Available Inventory",
    "lockedInventory": "Locked Inventory",
    "defectiveProducts": "Defective Products",
    "pendingReceipt": "Pending Receipt",
    "safetyStock": "Safety Stock"
  },
  "partManagement": {
    "title": "Part Management",
    "partName": "Part Name",
    "partNamePlaceholder": "Type to filter dropdown",
    "partNumber": "Part Number",
    "partNumberPlaceholder": "Type to filter dropdown",
    "requisitionNumber": "Requisition No.",
    "requisitionNumberPlaceholder": "Full fuzzy search",
    "purchaseOrderNumber": "Purchase Order No.",
    "purchaseOrderNumberPlaceholder": "Full fuzzy search",
    "documentNumber": "Requisition No.",
    "documentType": "Document Type",
    "selectDocumentType": "Select Document Type",
    "documentTypeRequisition": "Requisition",
    "documentTypeScrap": "Scrap Record",
    "documentTypePicking": "Pick List",
    "documentGenerateDate": "Document Generate Date",
    "documentStatus": "Document Status",
    "supplierName": "Supplier Name",
    "supplierNamePlaceholder": "Type to filter dropdown",
    "requisitionDate": "Requisition Date",
    "requisitionDateRange": "Requisition Date Range",
    "arrivalTimeRange": "Arrival Time Range",
    "requisitionStatus": "Requisition Status",
    "selectRequisitionStatus": "Select Requisition Status",
    "statusSubmitted": "Submitted",
    "statusApproved": "Approved",
    "statusRejected": "Rejected",
    "statusShipped": "Shipped",
    "statusPartialShipped": "Partial Shipped",
    "statusPartialReceived": "Partial Received",
    "statusReceived": "Received",
    "statusCancelled": "Cancelled",
    "inventoryStatus": "Inventory Status",
    "selectInventoryStatus": "Select Inventory Status",
    "statusNormal": "Normal",
    "statusBelowSafetyStock": "Below Safety Stock",
    "newRequisition": "New Requisition",
    "exportReport": "Export Report",
    "partScrap": "Part Scrap",
    "scrapRecord": "Scrap Record",
    "viewMaterialOrder": "View Pick List",
    "workOrderNumber": "Work Order No.",
    "workOrderCreateDate": "Work Order Create Date",
    "workOrderDetail": "Detail",
    "printMaterialOrder": "Print Material Order",
    "workOrderDetailTitle": "Work Order Detail",
    "generateDate": "Generate Date",
    "partsListTitle": "Parts List",
    "partCode": "Part Code",
    "quantity": "Quantity",
    "completePicking": "Complete Picking",
    "confirmCompletePicking": "Confirm to complete picking for work order {workOrderNumber}?",
    "completePickingSuccess": "Work order {workOrderNumber} picking completed!",
    "returnPicking": "Return Picking",
    "returnPickingTitle": "Return Picking Operation",
    "confirmReturnPicking": "Confirm to return work order {workOrderNumber} to pending pick status?",
    "returnPickingSuccess": "Work order {workOrderNumber} has been returned to pending pick status!",
    "statusVoided": "Voided",
    "partScrapForm": {
      "partNamePlaceholder": "Please enter part name",
      "partNumberPlaceholder": "Please enter part number",
      "selectScrapSource": "Please select scrap source",
      "scrapSource": "Scrap Source",
      "sourceReceipt": "Receipt",
      "sourceRepair": "Repair",
      "sourceOther": "Other"
    },
    "workOrderStatus": {
      "label": "Work Order Status",
      "pendingPick": "Pending Pick",
      "picked": "Picked",
      "outOfStock": "Out of Stock"
    },
    "outOfStockCannotPrint": "Cannot print pick list for out of stock work orders",
    "newRequisitionForm": {
      "partName": "Part Name",
      "partNumber": "Part Number",
      "quantity": "Quantity",
      "storeName": "Store Name",
      "expectedArrivalTime": "Expected Delivery Time",
      "partNameRequired": "Part name is required",
      "partNumberRequired": "Part number is required",
      "expectedArrivalTimeRequired": "Expected delivery time is required"
    },
    "partScrapForm": {
      "partName": "Part Name",
      "partNumber": "Part Number",
      "partNamePlaceholder": "Please enter part name",
      "partNumberPlaceholder": "Please enter part number",
      "quantity": "Quantity",
      "scrapReason": "Scrap Reason",
      "scrapSource": "Scrap Source",
      "selectScrapSource": "Please select scrap source",
      "sourceReceipt": "Receipt",
      "sourceRepair": "Repair",
      "sourceOther": "Other",
      "scrapImages": "Scrap Images",
      "damageDetail": "Damage Detail",
      "pleaseSelectApprovalTypeFirst": "Please select approval type first",
      "documentNumber": "Document Number",
      "documentNumberPlaceholder": "Input box (fuzzy search)",
      "documentDateRange": "Document Generation Date Range",
      "documentStatus": "Document Status",
      "storeNamePlaceholder": "Filterable dropdown",
      "switchedToRequisitionApproval": "Switched to requisition approval mode",
      "switchedToDamageApproval": "Switched to damage approval mode",
      "noFilterResults": "No data found matching the filter criteria",
      "clearFilters": "Clear Filters",
      "scrapImagesRequired": "Please upload scrap images",
      "imageUploadTip": "Scrap images are required. Support jpg, png format, max 5MB per image, up to 5 images",
      "imageTypeError": "Only image files are allowed!",
      "imageSizeError": "Image size cannot exceed 5MB!",
      "noImages": "No images",
      "partNameRequired": "Please enter part name",
      "partNumberRequired": "Please enter part number",
      "quantityRequired": "Please enter quantity",
      "scrapReasonRequired": "Please enter scrap reason",
      "scrapSourceRequired": "Please select scrap source",
      "addItemsPrompt": "Please add scrap items first"
    },
    "scrapRecordForm": {
      "scrapOrderNumber": "Scrap Order No.",
      "scrapOrderNumberPlaceholder": "Enter scrap order number",
      "partName": "Part Name",
      "partNamePlaceholder": "Type to filter dropdown",
      "partNumber": "Part Number",
      "partNumberPlaceholder": "Type to filter dropdown",
      "scrapDate": "Scrap Date",
      "scrapSource": "Scrap Source",
      "selectScrapSource": "Select Scrap Source",
      "sourceReceipt": "Receipt",
      "sourceRepair": "Repair",
      "sourceOther": "Other",
      "status": "Document Status",
      "selectStatus": "Select Document Status",
      "statusSubmitted": "Submitted",
      "statusApproved": "Approved",
      "statusRejected": "Rejected",
      "statusVoided": "Voided",
      "confirmVoidOrder": "Are you sure you want to void scrap order '{orderNumber}'?",
      "voidOrderSuccess": "Scrap order voided successfully",
      "voidOrderFailed": "Failed to void scrap order",
      "ship": "Ship",
      "shipParts": "Ship Parts",
      "shipPartially": "Ship Partially",
      "shipCompletely": "Ship Completely",
      "shippedQuantity": "Shipped Quantity",
      "shippingStatus": "Shipping Status",
      "notShipped": "Not Shipped",
      "partiallyShipped": "Partially Shipped",
      "fullyShipped": "Fully Shipped",
      "shipSuccess": "Shipped successfully",
      "shipFailed": "Failed to ship",
      "statusVoided": "Voided",
      "confirmVoidOrder": "Are you sure you want to void scrap order '{orderNumber}'?",
      "voidOrderSuccess": "Scrap order voided successfully",
      "voidOrderFailed": "Failed to void scrap order",
      "scrapQuantity": "Scrap Quantity",
      "totalQuantity": "Total Quantity",
      "itemCount": "Item Count",
      "orderDetails": "Order Details",
      "scrapReason": "Scrap Reason Details",
      "scrapReasonDetail": "Scrap Reason Details",
      "scrapReasonText": "Reason Description",
      "scrapImages": "Scrap Images",
      "deliveryOrderNumber": "Delivery Order No."
    },
    "rejectedRequisitionResubmitted": "Rejected requisition resubmitted successfully, a new requisition has been created",
    "rejectedRequisitionConfirmMessage": "This modification will create a new requisition. The original requisition will remain in rejected status. Do you want to continue?",
    "requisitionUpdated": "Requisition updated successfully"
  },
  "receiptManagement": {
    "title": "Receipt Management",
    "receiptOrderNumber": "Receipt Order No.",
    "receiptOrderNumberPlaceholder": "Enter receipt order no.",
    "supplierName": "Supplier Name",
    "supplierNamePlaceholder": "Enter supplier name",
    "receiptStatus": "Receipt Status",
    "receiptStatusPlaceholder": "Select receipt status",
    "statusPending": "Pending Receipt",
    "statusReceived": "Received",
    "statusCancelled": "Cancelled",
    "receiptDateRange": "Receipt Date Range",
    "deliveryDateRange": "Delivery Date Range",
    "newReceiptOrder": "New Receipt Order",
    "exportReport": "Export Report",
    "newReceiptOrderForm": {
      "supplierName": "Supplier Name",
      "receiptDate": "Receipt Date",
      "deliveryDate": "Delivery Date",
      "partName": "Part Name",
      "partNumber": "Part Number",
      "quantity": "Quantity",
      "unitPrice": "Unit Price",
      "totalPrice": "Total Price"
    },
    "receiptOrderDetailTitle": "Receipt Order Details",
    "invalidateReceiptOrderSuccess": "Receipt order invalidated successfully!"
  },
  "financialReport": {
    "title": "Financial Report",
    "reportType": "Report Type",
    "reportDateRange": "Report Date Range",
    "totalRevenue": "Total Revenue",
    "totalExpense": "Total Expense",
    "netProfit": "Net Profit",
    "reportTypes": {
      "daily": "Daily",
      "monthly": "Monthly",
      "quarterly": "Quarterly",
      "yearly": "Yearly"
    }
  },
  "customerManagement": {
    "title": "Customer Management",
    "customerList": "Customer List",
    "customerName": "Customer Name",
    "customerPhone": "Customer Phone",
    "customerAddress": "Customer Address",
    "addCustomer": "Add Customer",
    "editCustomer": "Edit Customer",
    "customerNamePlaceholder": "Enter customer name",
    "customerPhonePlaceholder": "Enter customer phone",
    "customerAddressPlaceholder": "Enter customer address",
    "customerMobilePlaceholder": "Enter phone number",
    "vin": "VIN",
    "vinPlaceholder": "Enter VIN",
    "orderStatus": "Order Status",
    "orderStatusPlaceholder": "Select order status",
    "orderStatusPendingAllocation": "Pending Allocation",
    "orderStatusAllocating": "Allocating",
    "orderStatusAllocated": "Allocated",
    "orderStatusPendingDelivery": "Pending Delivery",
    "orderStatusDelivered": "Delivered",
    "dealerStore": "Store",
    "dealerStorePlaceholder": "Select store",
    "salesConsultant": "Sales Consultant",
    "salesConsultantPlaceholder": "Enter sales consultant",
    "deliveryStatus": "Delivery Status",
    "deliveryStatusPlaceholder": "Select delivery status",
    "statusPending": "Pending Delivery",
    "statusConfirming": "Pending Confirmation",
    "statusCompleted": "Delivered",
    "customerConfirmed": "Customer Confirmed",
    "customerConfirmedPlaceholder": "Select customer confirmed status",
    "confirmationType": "Confirmation Type",
    "confirmationTypePlaceholder": "Select confirmation type",
    "confirmationTypeApp": "APP",
    "confirmationTypeOffline": "Offline",
    "deliveryTime": "Delivery Time",
    "deliveryTimePlaceholder": "Select delivery time",
    "customerConfirmTime": "Customer Confirmation Time",
    "customerConfirmTimePlaceholder": "Select customer confirmation time",
    "invoiceTime": "Invoice Time",
    "invoiceTimePlaceholder": "Select invoice time",
    "listTitle": "Delivery Order List",
    "totalCount": "Total {count} items",
    "submitConfirm": "Submit Confirmation",
    "deliveryConfirm": "Delivery Confirmation",
    "submitConfirmSuccess": "Submit confirmation successful!",
    "submitConfirmFailed": "Submit confirmation failed!",
    "deliveryConfirmSuccess": "Delivery confirmation successful!",
    "deliveryConfirmFailed": "Delivery confirmation failed!",
    "fetchDataFailed": "Failed to fetch delivery order data!",
    "printFeatureNotImplemented": "Print feature not implemented!",
    "exportSuccess": "Export successful!",
    "exportFailed": "Export failed!",
    "orderStatusNormal": "Normal",
    "orderStatusCancelled": "Cancelled",
    "signaturePhoto": "Signature Photo",
    "deliveryNotes": "Delivery Notes",
    "orderCreatorName": "Order Creator Name",
    "orderCreatorPhone": "Order Creator Phone",
    "basicInfo": "Basic Information",
    "orderInfo": "Order Information",
    "vehicleInfo": "Vehicle Information",
    "deliveryInfo": "Delivery Information",
    "orderCreateTime": "Order Create Time",
    "orderNumber": "Order Number",
    "customerName": "Customer Name",
    "customerPhone": "Customer Phone",
    "customerType": "Customer Type",
    "idType": "ID Type",
    "idNumber": "ID Number",
    "address": "Address",
    "city": "City",
    "postcode": "Postcode",
    "state": "State",
    "orderPaymentStatus": "Order Payment Status",
    "paymentMethod": "Payment Method",
    "invoiceTime": "Invoice Time",
    "dealerStore": "Store",
    "salesConsultant": "Sales Consultant",
    "vin": "VIN",
    "model": "Model",
    "variant": "Variant",
    "color": "Color",
    "warehouseName": "Warehouse Name",
    "productionDate": "Production Date",
    "entryTime": "Entry Time",
    "deliveryNumber": "Delivery Number",
    "deliveryStatus": "Delivery Status",
    "customerConfirmed": "Customer Confirmed",
    "confirmationType": "Confirmation Type",
    "deliveryTime": "Delivery Time",
    "signaturePhoto": "Signature Photo",
    "confirmationTypeOnline": "Online",
    "confirmationTypeOffline": "Offline",
    "confirmationTypePhone": "Phone",
    "statusPending": "Pending",
    "statusProcessing": "Processing",
    "statusDelivered": "Delivered",
    "statusCancelled": "Cancelled",
    "orderStatusPending": "Pending",
    "orderStatusConfirmed": "Confirmed",
    "orderStatusCancelled": "Cancelled",
    "detailNotFound": "Delivery order detail not found!",
    "fetchDetailFailed": "Failed to fetch delivery order detail!",
    "noDetailData": "No detail data available",
    "confirmSubmitDeliveryOrder": "Are you sure you want to submit the following delivery order?",
    "deliveryNumber": "Delivery Number",
    "orderNumber": "Order Number",
    "customerName": "Customer Name",
    "customerPhone": "Phone Number",
    "deliveryStatusChangeNote": "After submission, the delivery status will change from \"{from}\" to \"{to}\", waiting for customer confirmation or sales advisor to complete delivery confirmation."
  },
  "payment": {
    "title": "Vehicle Payment Management",
    "orderNumber": "Order Number",
    "buyerName": "Buyer Name",
    "buyerPhone": "Buyer Phone",
    "dealerStore": "Dealer Store",
    "salesConsultant": "Sales Consultant",
    "vin": "VIN",
    "model": "Model",
    "variant": "Variant",
    "color": "Color",
    "orderCreateTime": "Order Create Time",
    "orderStatus": "Order Status",
    "paymentStatus": "Payment Status",
    "vehicleSalesPrice": "Vehicle Sales Price",
    "insuranceAmount": "Insurance Amount",
    "otrAmount": "OTR Amount",
    "discountAmount": "Discount Amount",
    "totalAmount": "Total Amount",
    "paidAmount": "Paid Amount",
    "unpaidAmount": "Unpaid Amount",
    "loanAmount": "Loan Amount",
    "canInvoice": "Can Invoice",
    "invoiceTime": "Invoice Time",
    "invoiceNumber": "Invoice Number",
    "createTime": "Create Time",
    "updateTime": "Update Time",
    "paymentOperation": "Payment Operation",
    "orderDetail": "Order Detail",
    "paymentRecords": "Payment History",
    "addPaymentRecord": "Add Payment Record",
    "enterOrderNumber": "Enter order number",
    "enterBuyerName": "Enter buyer name",
    "enterBuyerPhone": "Enter buyer phone",
    "selectOrderStatus": "Select order status",
    "selectPaymentStatus": "Select payment status",
    "selectCanInvoice": "Select can invoice",
    "selectDateRange": "Select date range",
    "orderStatusSubmitted": "Submitted",
    "orderStatusCancelPending": "Cancel Pending",
    "orderStatusCancelApproved": "Cancel Approved",
    "orderStatusCancelled": "Cancelled",
    "orderStatusConfirmed": "Confirmed",
    "orderStatusPendingReview": "Pending Review",
    "orderStatusReviewed": "Reviewed",
    "orderStatusPendingDelivery": "Pending Delivery",
    "orderStatusDelivered": "Delivered",
    "paymentStatusPendingDeposit": "Pending Deposit",
    "paymentStatusDepositPaid": "Deposit Paid",
    "paymentStatusRefunding": "Refunding",
    "paymentStatusRefunded": "Refunded",
    "paymentStatusPendingFinal": "Pending Final Payment",
    "paymentStatusFullyPaid": "Fully Paid",
    "businessType": "Business Type",
    "transactionNumber": "Transaction Number",
    "channel": "Channel",
    "amount": "Amount",
    "paymentType": "Payment Type",
    "arrivalTime": "Arrival Time",
    "remark": "Remark",
    "creator": "Creator",
    "dataSource": "Data Source",
    "payment": "Payment",
    "refund": "Refund",
    "channelAPP": "APP",
    "channelBankCard": "Bank Card",
    "channelTransfer": "Transfer",
    "paymentTypeBookFee": "Book Fee",
    "paymentTypeLoan": "Loan",
    "paymentTypeFinal": "Final Payment",
    "dataSourceManual": "Manual Entry",
    "dataSourceApp": "APP Push",
    "paymentMethodFull": "Full Payment",
    "paymentMethodLoan": "Loan",
    "transactionNumberRequired": "Transaction number is required",
    "transactionNumberExists": "Transaction number already exists",
    "amountRequired": "Amount is required",
    "amountInvalid": "Please enter valid amount",
    "arrivalTimeRequired": "Arrival time is required",
    "operationNotAllowed": "Operation not allowed in current status",
    "appDataCannotDelete": "APP data cannot be deleted",
    "confirmDelete": "Confirm to delete this record?",
    "deleteSuccess": "Delete successful",
    "addSuccess": "Add successful",
    "orderInfo": "Order Information",
    "customerInfo": "Customer Information",
    "personalDetails": "Personal Details",
    "ordererInfo": "Orderer Information",
    "buyerInfo": "Buyer Information",
    "ordererName": "Orderer Name",
    "ordererPhone": "Orderer Phone",
    "buyerIdType": "ID Type",
    "buyerIdNumber": "ID Number",
    "buyerEmail": "Buyer Email",
    "buyerAddress": "Buyer Address",
    "buyerState": "Buyer State",
    "buyerCity": "Buyer City",
    "buyerPostcode": "Buyer Postcode",
    "storeInfo": "Store Information",
    "preferredOutletSalesAdvisor": "Preferred Outlet & Sales Advisor",
    "dealerRegion": "Dealer Region",
    "dealerCity": "Dealer City",
    "salesConsultantPhone": "Phone",
    "salesConsultantEmail": "Email",
    "vehicleInfo": "Vehicle Information",
    "options": "Options",
    "warehouseName": "Warehouse",
    "productionDate": "Production Date",
    "priceInfo": "Price Information",
    "salesSubtotal": "Sales Subtotal (Including GASA)",
    "consumptionTax": "Consumption Tax",
    "salesTax": "Sales Tax",
    "numberPlatesFee": "Number Plates Fee",
    "optionsPrice": "Options Price",
    "vehicleSalesSubtotal": "Vehicle Sales Subtotal",
    "otrAmountDetail": "OTR Amount",
    "paymentRecordNumber": "Payment Record Number",
    "totalPayment": "Total Payment Amount",
    "totalRefund": "Total Refund Amount",
    "netPayment": "Net Payment Amount",
    "recordCount": "Record Count",
    "enterTransactionNumber": "Enter transaction number",
    "enterAmount": "Enter amount",
    "enterRemark": "Enter remark",
    "selectBusinessType": "Select business type",
    "selectChannel": "Select channel",
    "selectPaymentType": "Select payment type",
    "selectArrivalTime": "Select arrival time"
  },
  "factoryOrder": {
    "title": "Factory Order Management",
    "statisticsTitle": "Order Overview Statistics",
    "searchTitle": "Filter Conditions",
    "actionTitle": "Action Area",
    "listTitle": "Order List",
    "monthlyOrderCount": "Monthly Order Count",
    "dailyOrderCount": "Daily Order Count",
    "monthlyGrowthRate": "Compared to last month",
    "dailyGrowthRate": "Compared to yesterday",
    "topDealer": "Top Dealer by Orders",
    "topVehicle": "Best Selling Vehicle",
    "pendingDelivery": "Pending Delivery Orders",
    "priorityProcessing": "Priority Processing Required",
    "refreshStatistics": "Refresh Data",
    "lastUpdate": "Last Update Time",
    "dealerName": "Dealer",
    "dealerNamePlaceholder": "Select dealer",
    "model": "Model",
    "modelPlaceholder": "Select model",
    "variant": "Variant",
    "variantPlaceholder": "Select variant",
    "orderStatus": "Order Status",
    "orderStatusPlaceholder": "Select order status",
    "paymentStatus": "Payment Status",
    "paymentStatusPlaceholder": "Select payment status",
    "orderDate": "Order Date",
    "orderDateStart": "Start Date",
    "orderDateEnd": "End Date",
    "orderNumber": "Order Number",
    "orderNumberPlaceholder": "Enter order number",
    "serialNumber": "No.",
    "dealerNameColumn": "Dealer",
    "creationTime": "Order Creation Time",
    "ordererName": "Orderer Name",
    "ordererPhone": "Orderer Phone",
    "buyerName": "Buyer Name",
    "buyerPhone": "Buyer Phone",
    "buyerCategory": "Buyer Category",
    "vin": "VIN",
    "paymentMethod": "Payment Method",
    "loanApprovalStatus": "Loan Approval Status",
    "orderApprovalStatus": "Order Approval Status",
    "insuranceStatus": "Insurance Status",
    "jpjRegistrationStatus": "JPJ Registration Status",
    "operations": "Operations",
    "details": "Details",
    "exportExcel": "Export Excel",
    "exportSuccess": "Export successful",
    "exportFailed": "Export failed",
    "refreshSuccess": "Data refreshed",
    "refreshFailed": "Data refresh failed",
    "detailTitle": "Factory Order Details",
    "customerInfo": "Customer Information",
    "personalDetails": "Personal Details",
    "storeInfo": "Store Information",
    "preferredOutletSalesAdvisor": "Preferred Outlet & Sales Advisor",
    "purchaseDetails": "Purchase Information",
    "purchaseDetailsTitle": "Purchase Details",
    "vehicleInfoTab": "Vehicle Information",
    "invoiceInfoTab": "Invoice Information",
    "benefitsInfoTab": "Benefits Information",
    "paymentInfoTab": "Payment Information",
    "insuranceInfoTab": "Insurance Information",
    "otrFeesTab": "OTR Fees",
    "changeRecordsTab": "Change Records",
    "ordererNameField": "Orderer Name",
    "ordererPhoneField": "Orderer Phone",
    "buyerNameField": "Buyer Name",
    "buyerPhoneField": "Buyer Phone",
    "buyerIdType": "ID Type",
    "buyerIdNumber": "ID Number",
    "buyerEmail": "Buyer Email",
    "buyerAddress": "Buyer Address",
    "buyerState": "Buyer State",
    "buyerCity": "Buyer City",
    "buyerPostcode": "Buyer Postcode",
    "dealerRegion": "Dealer Region",
    "dealerCity": "Dealer City",
    "salesConsultant": "Sales Consultant",
    "salesSubtotal": "Sales Subtotal (Including GASA, Consumption Tax, Sales Tax)",
    "numberPlatesFee": "Number Plates Fee",
    "accessoryInfo": "Accessory Information",
    "accessoryCategory": "Category",
    "accessoryName": "Accessory Name",
    "accessoryUnitPrice": "Unit Price",
    "accessoryQuantity": "Quantity",
    "accessoryTotalPrice": "Total Price",
    "accessoriesTotalAmount": "Accessories Total Amount",
    "invoiceType": "Invoice Type",
    "invoiceName": "Invoice Name",
    "invoicePhone": "Invoice Phone",
    "invoiceAddress": "Invoice Address",
    "benefitsInfo": "Benefits Information",
    "benefitCode": "Benefit Code",
    "benefitName": "Benefit Name",
    "benefitMode": "Benefit Mode",
    "discountPrice": "Discount Price",
    "effectiveDate": "Effective Date",
    "expirationDate": "Expiration Date",
    "benefitsTotalAmount": "Benefits Total Amount",
    "loanApprovalStatusField": "Loan Approval Status",
    "depositAmount": "Deposit Amount",
    "loanAmount": "Loan Amount",
    "balanceAmount": "Balance Amount",
    "insuranceInfo": "Insurance Information",
    "policyNumber": "Policy Number",
    "insuranceType": "Insurance Type",
    "insuranceCompany": "Insurance Company",
    "effectiveDateField": "Effective Date",
    "expirationDateField": "Expiration Date",
    "insurancePrice": "Insurance Price",
    "insurancesTotalAmount": "Insurance Total Amount",
    "insuranceNotes": "Notes",
    "otrFeesInfo": "On The Road Registration Fees",
    "ticketNumber": "Ticket Number",
    "feeItem": "Fee Item",
    "feePrice": "Fee Price",
    "otrFeesTotalAmount": "OTR Fees Total Amount",
    "changeRecordsInfo": "Order Change Records",
    "originalContent": "Original Content",
    "changedContent": "Changed Content",
    "operator": "Operator",
    "operationTime": "Operation Time",
    "vehicleInvoicePrice": "Vehicle Invoice Price",
    "remainingReceivable": "Remaining Receivable",
    "backToList": "Back",
    "statusPending": "Pending",
    "statusConfirmed": "Confirmed",
    "statusInProduction": "In Production",
    "statusCompleted": "Completed",
    "statusCancelled": "Cancelled",
    "statusPendingDelivery": "Pending Delivery",
    "paymentStatusPendingDeposit": "Pending Deposit",
    "paymentStatusDepositPaid": "Deposit Paid",
    "paymentStatusPendingBalance": "Pending Balance",
    "paymentStatusFullyPaid": "Fully Paid",
    "loanStatusNoNeed": "No Need",
    "loanStatusPending": "Pending",
    "loanStatusApproved": "Approved",
    "loanStatusRejected": "Rejected",
    "approvalStatusPending": "Pending Approval",
    "approvalStatusApproved": "Approved",
    "approvalStatusRejected": "Rejected",
    "insuranceStatusPending": "Pending Insurance",
    "insuranceStatusCompleted": "Insured",
    "jpjStatusPending": "Pending Registration",
    "jpjStatusCompleted": "Registered",
    "categoryPersonal": "Personal",
    "categoryBusiness": "Business",
    "invoiceTypePersonal": "Personal",
    "invoiceTypeBusiness": "Company"
  },
  "workAssignment": {
    "title": "Work Assignment",
    "breadcrumb": "After Sales / Work Assignment",
    "searchForm": "Filter Conditions",
    "columnConfig": "Column Settings",
    "exportData": "Export Data",
    "workOrderId": "Work Order ID",
    "priority": "Priority",
    "workOrderType": "Work Order Type",
    "customerName": "Customer Name",
    "licensePlate": "License Plate",
    "serviceAdvisor": "Service Advisor",
    "assignmentStatus": "Assignment Status",
    "assignedTechnician": "Assigned Technician",
    "creationTime": "Creation Time",
    "estimatedWorkHours": "Estimated Work Hours",
    "waitingDuration": "Waiting Duration",
    "totalAmount": "Work Order Amount",
    "priorities": {
      "urgent": "Urgent",
      "normal": "Normal"
    },
    "workOrderTypes": {
      "repair": "Repair",
      "maintenance": "Maintenance",
      "insurance": "Insurance"
    },
    "assignmentStatuses": {
      "pending_assign": "Pending Assign",
      "assigned": "Assigned"
    }
  },
  "quota": {
    "pageTitle": "Appointment Quota Management",
    "storeName": "Store Name",
    "storeCode": "Store Code",
    "permissionTip": "You are currently viewing the appointment quota configuration for the default store. Please contact the administrator to configure other stores.",
    "configuredListTitle": "Configured Quota List",
    "addNewQuota": "Add New Appointment Quota",
    "table": {
      "configDate": "Config Date",
      "timeSlotCount": "Time Slot Count",
      "totalQuota": "Total Quota",
      "bookedQuantity": "Booked Quantity",
      "lastUpdateTime": "Last Update Time",
      "expired": "Expired"
    },
    "emptyState": "No appointment quota configured yet. Click 'Add New Appointment Quota' to configure.",
    "unsavedChangesWarning": "You have unsaved changes, are you sure you want to close?",
    "dateMustBeFutureOrToday": "Date must be today or in the future",
    "selectDateFirst": "Please select a date first",
    "validation": {
      "dateRequired": "Date cannot be empty",
      "atLeastOneTimeSlot": "Please add at least one time slot",
      "timeRequired": "Time slot start/end time cannot be empty",
      "startBeforeEnd": "Time slot start time must be before end time",
      "quotaPositive": "Quota must be a positive integer",
      "timeSlotOverlap": "Time slot {slot1} overlaps with {slot2}, please adjust"
    },
    "messages": {
      "savingConfig": "Saving configuration..."
    },
    "summary": {
      "selectDate": "Please select date",
      "addTimeSlot": "Please add time slot",
      "timeSlotsUnit": " time slots",
      "totalQuota": "Total Quota: "
    },
    "editQuotaTitle": "Edit Appointment Quota",
    "addNewQuotaTitle": "Add New Appointment Quota",
    "modal": {
      "selectDateTitle": "Select Date",
      "dateLabel": "Date",
      "datePlaceholder": "Select Date",
      "existingConfig": "Existing configuration for this date",
      "dateTip": "Date must be today or in the future",
      "timeSlotConfigTitle": "Time Slot Configuration",
      "addTimeSlot": "Add Time Slot",
      "noTimeSlots": "No time slots currently, please click the button above to add.",
      "clickAddPrompt": "Click the 'Add Time Slot' button to start configuring",
      "timeSlot": "Time Slot",
      "startTime": "Start Time",
      "startTimePlaceholder": "Select start time",
      "endTime": "End Time",
      "endTimePlaceholder": "Select end time",
      "quota": "Quota",
      "quotaPlaceholder": "Enter quota",
      "configDescriptionTitle": "Configuration Description",
      "configDescription": {
  "vehicleModel": {
    "title": "Vehicle Model Master Data Management",
    "model": "Model",
    "variantName": "Variant Name",
    "variantCode": "Variant Code",
    "colourName": "Colour Name",
    "colourCode": "Colour Code",
    "fmrid": "FMRID",
    "createTime": "Create Time",
    "updateTime": "Update Time",
    "syncData": "Sync Data",
    "syncLog": "Sync Log",
    "exportData": "Export Data",
    "modelPlaceholder": "Please select model",
    "variantNamePlaceholder": "Please select variant name",
    "colourNamePlaceholder": "Please select colour name",
    "fmridPlaceholder": "Please enter FMRID",
    "syncSuccess": "Data synchronized successfully",
    "exportSuccess": "Data exported successfully",
    "syncLogTitle": "Data Synchronization Log"
  }
}
        "item1": "The quota for each time slot represents the maximum number of vehicles that can be booked for that slot.",
        "item2": "Time slots must be continuous, with no gaps.",
        "item3": "Time slots cannot overlap.",
        "item4": "The total quota of all time slots will be the total quota for that day."
      }
    },
    "timeSlotExceedsOperatingHours": "Time slot end time cannot exceed 18:00"
  },
  "salesOrder": {
    "title": "Sales Order List",
    "breadcrumb": "Customer Order Management / Sales Order List",
    "searchForm": "Filter Conditions",
    "operationButtons": "Operations",
    "orderList": "Order List",
    "buyerName": "Buyer Name",
    "buyerNamePlaceholder": "Enter buyer name",
    "buyerPhone": "Buyer Phone",
    "buyerPhonePlaceholder": "Enter buyer phone",
    "buyerType": "Buyer Type",
    "buyerTypePlaceholder": "Select buyer type",
    "model": "Model",
    "modelPlaceholder": "Select model",
    "orderNumber": "Order Number",
    "orderNumberPlaceholder": "Enter order number",
    "orderStatus": "Order Status",
    "orderStatusPlaceholder": "Select order status",
    "approvalStatus": "Order Approval Status",
    "approvalStatusPlaceholder": "Select approval status",
    "paymentStatus": "Payment Status",
    "paymentStatusPlaceholder": "Select payment status",
    "createTime": "Order Creation Time",
    "createTimeRange": "Order Creation Time Range",
    "insuranceStatus": "Insurance Status",
    "insuranceStatusPlaceholder": "Select insurance status",
    "loanApprovalStatus": "Loan Approval Status",
    "loanApprovalStatusPlaceholder": "Select loan approval status",
    "jpjRegistrationStatus": "JPJ Registration Status",
    "jpjRegistrationStatusPlaceholder": "Select JPJ registration status",
    "index": "No.",
    "ordererName": "Orderer",
    "ordererPhone": "Orderer Phone",
    "variant": "Variant",
    "color": "Color",
    "vin": "VIN",
    "paymentMethod": "Payment Method",
    "paymentInfo": "Payment Info",
    "totalAmount": "Total Amount",
    "pageTitle": "Sales Order List",
    "searchTitle": "Search Conditions",
    "batchOperation": "Batch Operation",
    "listTitle": "Sales Order List",
    "totalCount": "Total Count",
    "detailTitle": "Sales Order Detail",
    "statusSubmitted": "Submitted",
    "statusConfirmed": "Confirmed",
    "statusCancelPending": "Cancel Pending",
    "statusCancelApproved": "Cancel Approved",
    "statusCancelled": "Cancelled",
    "statusPendingDelivery": "Pending Delivery",
    "statusDelivered": "Delivered",
    "buyerTypeIndividual": "Individual",
    "buyerTypeCompany": "Company",
    "approvalStatusPending": "Pending Approval",
    "approvalStatusApproved": "Approved",
    "paymentStatusPendingDeposit": "Pending Deposit",
    "paymentStatusDepositPaid": "Deposit Paid",
    "paymentStatusPendingBalance": "Pending Balance",
    "paymentStatusBalancePaid": "Balance Paid",
    "paymentStatusRefunding": "Refunding",
    "paymentStatusRefunded": "Refunded",
    "insuranceStatusNotInsured": "Not Insured",
    "insuranceStatusInsuring": "Insuring",
    "insuranceStatusInsured": "Insured",
    "loanApprovalStatusPending": "Pending Review",
    "loanApprovalStatusApproved": "Approved",
    "loanApprovalStatusRejected": "Rejected",
    "jpjStatusPending": "Pending Registration",
    "jpjStatusRegistering": "Registering",
    "jpjStatusRegistered": "Registered",
    "jpjStatusFailed": "Registration Failed",
    "paymentMethodFull": "Full Payment",
    "paymentMethodInstallment": "Installment",
    "batchOperationTip": "Please select at least one record for batch operation",
    "buyerTypes": {
      "individual": "Individual",
      "company": "Company"
    },
    "models": {
      "AXIA": "AXIA",
      "BEZZA": "BEZZA", 
      "MYVI": "MYVI"
    },
    "orderStatuses": {
      "submitted": "Submitted",
      "confirmed": "Confirmed",
      "cancel_pending": "Cancel Pending",
      "cancel_approved": "Cancel Approved",
      "cancelled": "Cancelled",
      "pending_delivery": "Pending Delivery",
      "delivered": "Delivered"
    },
    "approvalStatuses": {
      "pending_approval": "Pending Approval",
      "approved": "Approved"
    },
    "paymentStatuses": {
      "pending_deposit": "Pending Deposit",
      "deposit_paid": "Deposit Paid",
      "pending_balance": "Pending Balance",
      "balance_paid": "Balance Paid",
      "refunding": "Refunding",
      "refunded": "Refunded"
    },
    "insuranceStatuses": {
      "not_insured": "Not Insured",
      "insuring": "Insuring",
      "insured": "Insured"
    },
    "loanApprovalStatuses": {
      "pending_review": "Pending Review",
      "approved": "Approved",
      "rejected": "Rejected"
    },
    "jpjRegistrationStatuses": {
      "pending_registration": "Pending Registration",
      "registering": "Registering",
      "registered": "Registered",
      "registration_failed": "Registration Failed"
    },
    "paymentMethods": {
      "full_payment": "Full Payment",
      "installment": "Installment"
    },
    "viewDetail": "Detail",
    "editOrder": "Edit",
    "returnToList": "Return to List",
    "exportSuccess": "Export successful",
    "exportFailed": "Export failed",
    "noDataToExport": "No data to export",
    "confirmExportTitle": "Confirm Export",
    "confirmExportMessage": "Are you sure to export all order data under current filter conditions?",
    "orderDetail": "Sales Order Detail",
    "customerInfo": "Customer Information",
    "storeInfo": "Store Information",
    "purchaseInfo": "Purchase Information",
    "vehicleInfoTab": "Vehicle Information",
    "invoicingInfoTab": "Invoicing Information", 
    "rightsInfoTab": "Service & Benefits Information",
    "paymentInfoTab": "Payment Information",
    "insuranceInfoTab": "Insurance Information",
    "otrFeesTab": "OTR Fees Information",
    "changeRecords": "Order Change Records",
    "buyerIdType": "Buyer ID Type",
    "buyerIdNumber": "Buyer ID Number",
    "buyerEmail": "Buyer Email",
    "buyerAddress": "Buyer Address",
    "buyerState": "Buyer State",
    "buyerCity": "Buyer City",
    "buyerPostcode": "Buyer Postcode",
    "storeRegion": "Store Region",
    "storeCity": "Store City",
    "storeName": "Store Name",
    "salesConsultantName": "Sales Consultant",
    "salesSubtotal": "Sales Subtotal (incl. GASA, GST, Sales Tax)",
    "numberPlatesFee": "Number Plates Fee",
    "vinCode": "VIN Code",
    "accessoryInfo": "Accessories Information",
    "accessoryCategory": "Category",
    "accessoryName": "Accessory Name",
    "unitPrice": "Unit Price",
    "quantity": "Quantity",
    "totalPrice": "Total Price",
    "accessoriesTotalAmount": "Accessories Total Amount",
    "invoicingType": "Invoicing Type",
    "invoicingName": "Invoicing Name",
    "invoicingPhone": "Invoicing Phone",
    "invoicingAddress": "Invoicing Address",
    "rightsInfo": "Service & Benefits Information",
    "rightCode": "Benefit Code",
    "rightName": "Benefit Name",
    "rightMode": "Benefit Mode",
    "discountAmount": "Discount Amount",
    "effectiveDate": "Effective Date",
    "expiryDate": "Expiry Date",
    "rightsDiscountAmount": "Benefits Total Discount",
    "loanApprovalStatusField": "Loan Approval Status",
    "depositAmount": "Deposit Amount",
    "loanAmount": "Loan Amount",
    "balanceAmount": "Balance Amount",
    "insuranceInfo": "Insurance Information",
    "policyNumber": "Policy Number",
    "insuranceType": "Insurance Type",
    "insuranceCompany": "Insurance Company",
    "insurancePrice": "Insurance Price",
    "insuranceTotalAmount": "Insurance Total Amount",
    "insuranceNotes": "Notes",
    "otrFeesInfo": "On The Road Registration Fees",
    "ticketNumber": "Ticket Number",
    "feeItem": "Fee Item",
    "feePrice": "Fee Price",
    "otrFeesTotalAmount": "OTR Fees Total Amount",
    "changeRecordIndex": "No.",
    "originalContent": "Original Content",
    "changedContent": "Changed Content",
    "operator": "Operator",
    "operationTime": "Operation Time",
    "totalInvoiceAmount": "Total Invoice Amount",
    "remainingAmount": "Remaining Amount",
    "editOrderTitle": "Sales Order Edit",
    "personalDetails": "Customer Information - Personal Details",
    "preferredOutlet": "Store Information - Preferred Outlet & Sales Advisor",
    "purchaseDetails": "Purchase Information - Purchase Details",
    "addRights": "Add Benefits",
    "selectRights": "Select Benefits",
    "rightCodeSearch": "Benefit Code Search",
    "rightNameSearch": "Benefit Name Search",
    "selectAll": "Select All",
    "addSelected": "Add Selected",
    "deleteRight": "Delete Benefit",
    "confirmDeleteRight": "Are you sure to delete this benefit?",
    "pushToInsurance": "Push to Insurance System",
    "insurancePushed": "Pushed",
    "confirmPushInsurance": "Are you sure to push to insurance system?",
    "pushInsuranceSuccess": "Push to insurance system successful",
    "pushInsuranceFailed": "Push to insurance system failed",
    "submitDelivery": "Submit Delivery",
    "confirmSubmitDelivery": "Are you sure to submit delivery application?",
    "submitDeliverySuccess": "Submit delivery successful",
    "submitDeliveryFailed": "Submit delivery failed",
    "deliveryConditionsNotMet": "Delivery conditions not met",
    "colorChangeNotice": "You have changed the vehicle color, do you want to submit for approval?",
    "colorChangeSubmitted": "Color change submitted for approval",
    "roadTax": "Road Tax",
    "registrationFee": "Registration/Transfer Fee", 
    "ownershipClaimFee": "Ownership Claim Fee",
    "interchangeFee": "Interchange Fee",
    "otrFeeTotal": "Registration Fees Total",
    "loanTerm": "Loan Term (Months)",
    "loanTermPlaceholder": "Enter loan term",
    "loanAmountPlaceholder": "Enter loan amount",
    "editTitle": "Sales Order Edit",
    "rightsSelection": "Rights Selection",
    "noRightsSelected": "No rights selected",
    "colorChangeTip": "Color change requires approval",
    "colorPlaceholder": "Please select color",
    "orderSummary": "Order Summary",
    "vehicleCost": "Vehicle Cost",
    "discountAndFees": "Discounts & Fees",
    "insuranceNotesPlaceholder": "Enter insurance notes",
    "colorRequired": "Please select color",
    "paymentMethodRequired": "Please select payment method",
    "loanAmountRequired": "Please enter loan amount",
    "loanAmountExceedsTotal": "Loan amount cannot exceed total order amount",
    "loanTermRequired": "Please select loan term",
    "colorWhite": "White",
    "colorBlack": "Black",
    "colorSilver": "Silver",
    "colorRed": "Red",
    "colorBlue": "Blue",
    "colorGray": "Gray",
    "loanTerm12": "12 months",
    "loanTerm24": "24 months",
    "loanTerm36": "36 months",
    "loanTerm48": "48 months",
    "loanTerm60": "60 months",
    "loanTerm72": "72 months",
    "fetchDetailError": "Failed to fetch order details",
    "colorChangeApprovalMessage": "Color change requires approval, confirm to submit?",
    "saveError": "Save failed",
    "cancelEditConfirm": "Are you sure to cancel editing?",
    "rightsSelectionTitle": "Rights Selection",
    "availableRights": "Available Rights",
    "selectedRights": "Selected Rights",
    "totalDiscountAmount": "Total Discount Amount"
  },
  "createWorkOrderModal": {
    "title": "Create Work Order Based on Inspection Form",
    "inspectionInfo": "Related Inspection Form Information",
    "customerInfo": "Customer Information (from Inspection Form)",
    "vehicleInfo": "Vehicle Information (from Inspection Form)",
    "workOrderBasic": "Work Order Basic Information",
    "projectSelection": "Project Selection",
    "laborItems": "Labor Items",
    "partsItems": "Parts Items",
    "costSummary": "Cost Budget Summary",
    "form": {
      "workOrderType": "Work Order Type",
      "priority": "Work Order Priority",
      "remarks": "Work Order Remarks",
      "searchProject": "Search Project Name or Code",
      "projectName": "Project Name",
      "projectCode": "Project Code",
      "laborCode": "Labor Code",
      "laborName": "Labor Name",
      "partName": "Part Name",
      "partCode": "Part Code",
      "standardHours": "Standard Hours",
      "unitPrice": "Unit Price",
      "quantity": "Quantity",
      "availableStock": "Available Stock",
      "subtotal": "Subtotal",
      "isClaim": "Is Claim",
      "isAdded": "Is Added",
      "laborCost": "Labor Cost",
      "partsCost": "Parts Cost",
      "totalAmount": "Total Work Order Amount",
      "vehicleModel": "Vehicle Model",
      "vehicleConfig": "Vehicle Configuration",
      "mileage": "Mileage",
      "vehicleAge": "Vehicle Age"
    },
    "buttons": {
      "addLabor": "Add Labor",
      "addParts": "Add Parts",
      "clearAll": "Clear All",
      "createAndNotify": "Create & Notify Customer",
      "saveDraft": "Save Draft",
      "searchAdd": "Search & Add"
    },
    "placeholders": {
      "searchProject": "Search project name or code...",
      "selectWorkOrderType": "Please select work order type",
      "selectPriority": "Please select priority",
      "enterRemarks": "Please enter work order remarks",
      "enterQuantity": "Please enter quantity",
      "enterHours": "Please enter hours"
    },
    "workOrderTypes": {
      "maintenance": "Maintenance",
      "repair": "Repair",
      "claim": "Claim"
    },
    "priorities": {
      "normal": "Normal",
      "urgent": "Urgent"
    },
    "messages": {
      "createSuccess": "Work order created successfully",
      "saveDraftSuccess": "Draft saved successfully",
      "projectAdded": "Project added successfully",
      "projectRemoved": "Project removed successfully",
      "stockInsufficient": "Insufficient stock",
      "quantityExceeded": "Quantity exceeds stock",
      "duplicateProject": "Project already exists",
      "confirmClearAll": "Are you sure to clear all projects?",
      "confirmCreateAndNotify": "Are you sure to create work order and notify customer?",
      "unsavedChanges": "There are unsaved changes, are you sure to close?"
    },
    "statistics": {
      "totalLaborHours": "Total Labor Hours",
      "totalPartsCount": "Total Parts Count",
      "hours": "Hours",
      "pieces": "Pieces",
      "yuan": "RMB"
    }
  },
  "technicians": {
    "technicianA": "Technician A",
    "technicianB": "Technician B",
    "technicianC": "Technician C"
  },
  "mockData": {
    "registerTypes": {
      "appointment": "Appointment",
      "walkIn": "Walk-in"
    },
    "serviceTypes": {
      "maintenance": "Maintenance",
      "repair": "Repair"
    },
    "colors": {
      "white": "White",
      "black": "Black",
      "blue": "Blue",
      "gray": "Gray"
    },
    "names": {
      "zhangSan": "Zhang San",
      "liHua": "Li Hua",
      "wangDaChui": "Wang Dachui",
      "linYiYi": "Lin Yiyi",
      "liSi": "Li Si",
      "wangWu": "Wang Wu",
      "zhaoLiu": "Zhao Liu",
      "qianQi": "Qian Qi",
      "sunBa": "Sun Ba",
      "zhouJiu": "Zhou Jiu",
      "wuShi": "Wu Shi"
    },
    "carModels": {
      "modelY2023Long": "Model Y 2023 Long Range",
      "model32022Standard": "Model 3 2022 Standard Range",
      "modelX2024Performance": "Model X 2024 Performance",
      "modelY2023Performance": "Model Y 2023 Performance"
    }
  },
  "quotaManagement": {
    "pageTitle": "Appointment Quota Management",
    "storeName": "Store Name",
    "storeCode": "Store Code",
    "permissionTip": "You are currently viewing the appointment quota configuration for the default store. Please contact the administrator to configure other stores.",
    "configuredListTitle": "Configured Quota List",
    "addNewQuota": "Add New Quota",
    "editQuotaTitle": "Edit Quota",
    "addNewQuotaTitle": "Add New Quota",
    "emptyState": "No appointment quota configured yet. Click \"Add New Quota\" to configure.",
    "unsavedChangesWarning": "You have unsaved changes, are you sure to close?",
    "dateMustBeFutureOrToday": "Configuration date cannot be earlier than today",
    "selectDateFirst": "Please select date first",
    "timeSlotExceedsOperatingHours": "Time slot setting exceeds operating hours (8:00-18:00)",
    "table": {
      "configDate": "Config Date",
      "timeSlotCount": "Time Slot Count",
      "totalQuota": "Total Quota",
      "bookedQuantity": "Booked Quantity",
      "lastUpdateTime": "Last Update Time",
      "operations": "Operations",
      "edit": "Edit",
      "expired": "Expired"
    },
    "modal": {
      "selectDateTitle": "Select Date",
      "dateLabel": "Config Date",
      "datePlaceholder": "Please select config date",
      "existingConfig": "Existing Config",
      "dateTip": "Config date cannot be earlier than today",
      "timeSlotConfigTitle": "Time Slot Configuration",
      "addTimeSlot": "Add Time Slot",
      "noTimeSlots": "No time slots added yet",
      "clickAddPrompt": "Click \"Add Time Slot\" to start configuration",
      "timeSlot": "Time Slot",
      "startTime": "Start Time",
      "startTimePlaceholder": "Please select start time",
      "endTime": "End Time",
      "endTimePlaceholder": "Please select end time",
      "quota": "Quota",
      "quotaPlaceholder": "Please enter quota",
      "configDescriptionTitle": "Configuration Description",
      "configDescription": {
        "item1": "The quota of each time slot represents the maximum number of vehicles that can be booked in that time slot.",
        "item2": "Time slots cannot overlap, and the end time must be later than the start time.",
        "item3": "It is recommended to set a reasonable quota based on the actual reception capacity of the store.",
        "item4": "The sum of all time slot quotas is the total quota for the day."
      }
    },
    "validation": {
      "dateRequired": "Please select config date",
      "atLeastOneTimeSlot": "At least one time slot is required",
      "timeRequired": "Please set the start and end time of the time slot",
      "startBeforeEnd": "Start time must be earlier than end time",
      "quotaPositive": "Quota must be greater than 0",
      "timeSlotOverlap": "Time slot {slot1} overlaps with {slot2}, please adjust"
    },
    "summary": {
      "selectDate": "Please select date",
      "addTimeSlot": "Please add time slot",
      "timeSlotsUnit": "time slots",
      "totalQuota": "Total Quota"
    }
  },
  "salesOrderManagement": {
    "title": "Sales Order List",
    "breadcrumb": "Customer Order Management / Sales Order List",
    "searchForm": "Search Conditions",
    "operationButtons": "Operations",
    "orderList": "Order List",
    "buyerName": "Buyer Name",
    "buyerNamePlaceholder": "Enter buyer name",
    "buyerPhone": "Buyer Phone",
    "buyerPhonePlaceholder": "Enter buyer phone",
    "buyerType": "Buyer Type",
    "buyerTypePlaceholder": "Select buyer type",
    "model": "Model",
    "modelPlaceholder": "Select model",
    "orderNumber": "Order Number",
    "orderNumberPlaceholder": "Enter order number",
    "orderStatus": "Order Status",
    "orderStatusPlaceholder": "Select order status",
    "approvalStatus": "Approval Status",
    "approvalStatusPlaceholder": "Select approval status",
    "paymentStatus": "Payment Status",
    "paymentStatusPlaceholder": "Select payment status",
    "createTime": "Create Time",
    "createTimeRange": "Create Time Range",
    "insuranceStatus": "Insurance Status",
    "insuranceStatusPlaceholder": "Select insurance status",
    "loanApprovalStatus": "Loan Approval Status",
    "loanApprovalStatusPlaceholder": "Select loan approval status",
    "jpjRegistrationStatus": "JPJ Registration Status",
    "jpjRegistrationStatusPlaceholder": "Select JPJ registration status",
    "index": "Index",
    "ordererName": "Orderer Name",
    "ordererPhone": "Orderer Phone",
    "variant": "Variant",
    "color": "Color",
    "vin": "VIN",
    "paymentMethod": "Payment Method",
    "totalAmount": "Total Amount",
    "pageTitle": "Sales Order List",
    "searchTitle": "Search Conditions",
    "batchOperation": "Batch Operation",
    "listTitle": "Sales Order List",
    "totalCount": "Total Count",
    "detailTitle": "Sales Order Detail",
    "statusSubmitted": "Submitted",
    "statusConfirmed": "Confirmed",
    "statusCancelPending": "Cancel Pending",
    "statusCancelApproved": "Cancel Approved",
    "statusCancelled": "Cancelled",
    "statusPendingDelivery": "Pending Delivery",
    "statusDelivered": "Delivered",
    "buyerTypes": {
      "individual": "Individual",
      "company": "Company"
    },
    "models": {
      "AXIA": "AXIA",
      "BEZZA": "BEZZA",
      "MYVI": "MYVI"
    },
    "orderStatuses": {
      "submitted": "Submitted",
      "confirmed": "Confirmed",
      "cancel_pending": "Cancel Pending",
      "cancel_approved": "Cancel Approved",
      "cancelled": "Cancelled",
      "pending_delivery": "Pending Delivery",
      "delivered": "Delivered"
    },
    "approvalStatuses": {
      "pending_approval": "Pending Approval",
      "approved": "Approved"
    },
    "paymentStatuses": {
      "pending_deposit": "Pending Deposit",
      "deposit_paid": "Deposit Paid",
      "pending_balance": "Pending Balance",
      "balance_paid": "Balance Paid",
      "refunding": "Refunding",
      "refunded": "Refunded"
    },
    "insuranceStatuses": {
      "not_insured": "Not Insured",
      "insuring": "Insuring",
      "insured": "Insured"
    },
    "loanApprovalStatuses": {
      "pending_review": "Pending Review",
      "approved": "Approved",
      "rejected": "Rejected"
    },
    "jpjRegistrationStatuses": {
      "pending_registration": "Pending Registration",
      "registering": "Registering",
      "registered": "Registered",
      "registration_failed": "Registration Failed"
    },
    "paymentMethods": {
      "full_payment": "Full Payment",
      "installment": "Installment"
    },
    "viewDetail": "Detail",
    "editOrder": "Edit",
    "returnToList": "Return to List",
    "exportSuccess": "Export Successful",
    "exportFailed": "Export Failed",
    "noDataToExport": "No data to export",
    "confirmExportTitle": "Confirm Export",
    "confirmExportMessage": "Are you sure to export all order data under current filter conditions?",
    "orderDetail": "Sales Order Detail",
    "customerInfo": "Customer Information",
    "storeInfo": "Store Information",
    "purchaseInfo": "Purchase Information",
    "vehicleInfoTab": "Vehicle Information",
    "invoicingInfoTab": "Invoicing Information",
    "rightsInfoTab": "Service & Rights Information",
    "paymentInfoTab": "Payment Information",
    "insuranceInfoTab": "Insurance Information",
    "otrFeesTab": "OTR Fees Information",
    "changeRecords": "Order Change Records",
    "buyerIdType": "Buyer ID Type",
    "buyerIdNumber": "Buyer ID Number",
    "buyerEmail": "Buyer Email",
    "buyerAddress": "Buyer Address",
    "buyerState": "Buyer State",
    "buyerCity": "Buyer City",
    "buyerPostcode": "Buyer Postcode",
    "storeRegion": "Store Region",
    "storeCity": "Store City",
    "storeName": "Store Name",
    "salesConsultantName": "Sales Consultant",
    "salesSubtotal": "Sales Subtotal (including GASA, consumption tax, sales tax)",
    "numberPlatesFee": "Number Plates Fee",
    "vinCode": "VIN Code",
    "accessoryInfo": "Accessory Information",
    "accessoryCategory": "Category",
    "accessoryName": "Accessory Name",
    "unitPrice": "Unit Price",
    "quantity": "Quantity",
    "totalPrice": "Total Price",
    "accessoriesTotalAmount": "Accessories Total Amount",
    "invoicingType": "Invoicing Type",
    "invoicingName": "Invoicing Name",
    "invoicingPhone": "Invoicing Phone",
    "invoicingAddress": "Invoicing Address",
    "rightsInfo": "Service & Rights Information",
    "rightCode": "Right Code",
    "rightName": "Right Name",
    "rightMode": "Right Mode",
    "discountAmount": "Discount Amount",
    "effectiveDate": "Effective Date",
    "expiryDate": "Expiry Date",
    "rightsDiscountAmount": "Rights Discount Total Amount",
    "loanApprovalStatusField": "Loan Approval Status",
    "depositAmount": "Deposit Amount",
    "loanAmount": "Loan Amount",
    "balanceAmount": "Balance Amount",
    "insuranceInfo": "Insurance Information",
    "policyNumber": "Policy Number",
    "insuranceType": "Insurance Type",
    "insuranceCompany": "Insurance Company",
    "insurancePrice": "Insurance Price",
    "insuranceTotalAmount": "Insurance Total Amount",
    "insuranceNotes": "Insurance Notes",
    "otrFeesInfo": "On The Road Registration Fees",
    "ticketNumber": "Ticket Number",
    "feeItem": "Fee Item",
    "feePrice": "Fee Price",
    "otrFeesTotalAmount": "OTR Fees Total Amount",
    "changeRecordIndex": "Index",
    "originalContent": "Original Content",
    "changedContent": "Changed Content",
    "operator": "Operator",
    "operationTime": "Operation Time",
    "totalInvoiceAmount": "Total Invoice Amount",
    "remainingAmount": "Remaining Amount",
    "editOrderTitle": "Sales Order Edit",
    "personalDetails": "Customer Information - Personal Details",
    "preferredOutlet": "Store Information - Preferred Outlet & Sales Advisor",
    "purchaseDetails": "Purchase Information - Purchase Details",
    "addRights": "Add Rights",
    "selectRights": "Select Rights",
    "rightCodeSearch": "Right Code Search",
    "rightNameSearch": "Right Name Search",
    "selectAll": "Select All",
    "addSelected": "Add Selected",
    "deleteRight": "Delete Right",
    "confirmDeleteRight": "Are you sure to delete this right?",
    "pushToInsurance": "Push to Insurance System",
    "insurancePushed": "Insurance Pushed",
    "confirmPushInsurance": "Are you sure to push to insurance system?",
    "pushInsuranceSuccess": "Push to insurance system successful",
    "pushInsuranceFailed": "Push to insurance system failed",
    "submitDelivery": "Submit Delivery",
    "confirmSubmitDelivery": "Are you sure to submit delivery application?",
    "submitDeliverySuccess": "Submit delivery successful",
    "submitDeliveryFailed": "Submit delivery failed",
    "deliveryConditionsNotMet": "Delivery conditions not met",
    "colorChangeNotice": "You have changed the vehicle color, do you want to submit for review?",
    "colorChangeSubmitted": "Color change submitted for review",
    "roadTax": "Road Tax",
    "registrationFee": "Registration/Transfer Fee",
    "ownershipClaimFee": "Ownership Claim Fee",
    "interchangeFee": "Interchange Fee",
    "otrFeeTotal": "Registration Fee Total",
    "loanTerm": "Loan Term (Months)",
    "loanTermPlaceholder": "Enter loan term",
    "loanAmountPlaceholder": "Enter loan amount",
    "orderDetailTitle": "Sales Order Detail",
    "backToList": "Back to List"
  },
  "workOrderApproval": {
    "title": "Work Order Approval",
    "subtitle": "Work Order Approval Management",
    "searchCard": "Filter Conditions",
    "operationCard": "Operations",
    "tableCard": "Approval List",
    "pendingTab": "Pending",
    "completedTab": "Completed",
    "allTab": "All",
    "approvalNo": "Approval No.",
    "approvalType": "Approval Type",
    "approvalStatus": "Approval Status",
    "approvalResult": "Approval Result",
    "submitter": "Submitter",
    "submitTime": "Submit Time",
    "workOrderNo": "Work Order No.",
    "requestReason": "Request Reason",
    "customerInfo": "Customer Info",
    "licensePlate": "License Plate",
    "store": "Store",
    "currentLevel": "Current Level",
    "overtimeStatus": "Overtime Status",
    "approvalTime": "Approval Time",
    "approver": "Approver",
    "approvalRemark": "Approval Remark",
    "claimApproval": "Claim Approval",
    "cancelOrderApproval": "Cancel Order Approval",
    "pending": "Pending",
    "completed": "Completed",
    "approved": "Approved",
    "rejected": "Rejected",
    "firstLevel": "First Level",
    "secondLevel": "Second Level",
    "approvalNoPlaceholder": "Enter approval number",
    "submitterPlaceholder": "Enter submitter name",
    "workOrderNoPlaceholder": "Enter work order number",
    "customerInfoPlaceholder": "Enter customer name or phone",
    "licensePlatePlaceholder": "Enter license plate",
    "selectApprovalType": "Select approval type",
    "selectApprovalStatus": "Select approval status",
    "selectApprovalLevel": "Select approval level",
    "selectOvertimeStatus": "Select overtime status",
    "selectStore": "Select store",
    "selectTimeRange": "Select time range",
    "submitTimeStart": "Submit start time",
    "submitTimeEnd": "Submit end time",
    "approve": "Approve",
    "viewDetail": "View Detail",
    "operationLog": "Operation Log",
    "approvalAction": "Approval Action",
    "approvalRemarkPlaceholder": "Enter approval remark (required for rejection)",
    "approvalRemarkRequired": "Approval remark is required for rejection",
    "approvalSuccess": "Approval operation successful",
    "approvalFailed": "Approval operation failed",
    "exportSuccess": "Export successful",
    "exportFailed": "Export failed",
    "confirmApproval": "Confirm Approval",
    "approvalDetail": "Approval Detail",
    "basicInfo": "Basic Information",
    "vehicleInfo": "Vehicle Information",
    "claimDetails": "Claim Details",
    "workOrderInfo": "Work Order Information",
    "approvalHistory": "Approval History",
    "claimLabor": "Claim Labor",
    "claimParts": "Claim Parts",
    "totalClaimAmount": "Total Claim Amount",
    "laborCode": "Labor Code",
    "laborName": "Labor Name",
    "standardLabor": "Standard Labor",
    "laborPrice": "Labor Price",
    "partCode": "Part Code",
    "partName": "Part Name",
    "quantity": "Quantity",
    "unitPrice": "Unit Price",
    "claimAmount": "Claim Amount",
    "customerName": "Customer Name",
    "customerPhone": "Customer Phone",
    "senderName": "Sender Name",
    "senderPhone": "Sender Phone",
    "vin": "VIN",
    "model": "Model",
    "configuration": "Configuration",
    "color": "Color",
    "mileage": "Mileage",
    "vehicleAge": "Vehicle Age",
    "repairTime": "Repair Time",
    "saleTime": "Sale Time",
    "workOrderStatus": "Work Order Status",
    "paymentStatus": "Payment Status",
    "estimatedStartTime": "Estimated Start Time",
    "assignedTechnician": "Assigned Technician",
    "cancelReason": "Cancel Reason",
    "firstApprover": "First Approver",
    "firstApprovalTime": "First Approval Time",
    "firstApprovalResult": "First Approval Result",
    "firstApprovalRemark": "First Approval Remark",
    "secondApprover": "Second Approver",
    "secondApprovalTime": "Second Approval Time",
    "secondApprovalResult": "Second Approval Result",
    "secondApprovalRemark": "Second Approval Remark",
    "operationContent": "Operation Content",
    "operator": "Operator",
    "operationTime": "Operation Time",
    "operationRemark": "Operation Remark",
    "systemOperation": "System Operation",
    "submitClaimApproval": "Submit Claim Approval",
    "firstLevelApprovalPass": "First Level Approval Pass",
    "firstLevelApprovalReject": "First Level Approval Reject",
    "secondLevelApprovalPass": "Second Level Approval Pass",
    "secondLevelApprovalReject": "Second Level Approval Reject",
    "submitCancelRequest": "Submit Cancel Request",
    "cancelApprovalPass": "Cancel Approval Pass",
    "cancelApprovalReject": "Cancel Approval Reject",
    "hours": "hours",
    "yuan": "¥",
    "pieces": "pcs",
    "months": "months",
    "km": "km",
    "draft": "Draft",
    "waitingApproval": "Waiting Approval",
    "confirmed": "Confirmed",
    "cancelled": "Cancelled",
    "pendingPayment": "Pending Payment",
    "paid": "Paid",
    "technician": "Technician",
    "serviceAdvisor": "Service Advisor",
    "technicianManager": "Technician Manager",
    "factoryManager": "Factory Manager",
    "allStores": "All Stores",
    "allTypes": "All Types",
    "allStatus": "All Status",
    "allLevels": "All Levels",
    "allOvertimeStatus": "All Overtime Status",
    "records": "records",
    "currentPage": "Page",
    "totalPages": "Total",
    "pages": "pages",
    "itemsPerPage": "Items per page",
    "goToPage": "Go to page",
    "pageNumber": "page",
    "statistics": "Statistics",
    "totalApprovals": "Total Approvals",
    "pendingApprovals": "Pending Approvals",
    "completedApprovals": "Completed Approvals",
    "overtimeApprovals": "Overtime Approvals",
    "approvalPassRate": "Approval Pass Rate",
    "refreshData": "Refresh Data",
    "columnSettings": "Column Settings",
    "tableView": "Table View",
    "cardView": "Card View",
    "advancedFilter": "Advanced Filter",
    "collapseFilter": "Collapse Filter",
    "expandFilter": "Expand Filter",
    "noPermission": "No Permission",
    "dataLoading": "Data loading...",
    "networkError": "Network connection failed",
    "serverError": "Server error",
    "dataError": "Data format error",
    "permissionDenied": "Permission denied",
    "dataExpired": "Data expired, please refresh",
    "retryRequest": "Retry Request",
    "reportError": "Report Error",
    "feedbackChannel": "Feedback",
    "approvalTimeout": "Approval timeout reminder",
    "approvalNotification": "Approval status notification",
    "resultNotification": "Approval result notification",
    "systemMessage": "System message",
    "emailNotification": "Email notification",
    "smsNotification": "SMS notification",
    "notificationHistory": "Notification history",
    "enableNotification": "Enable notification",
    "disableNotification": "Disable notification",
    "notificationSettings": "Notification settings"
  },
  "vehicleAllocation": {
    "operationTypes": {
      "view_detail": "detail",
      "print": "print",
      "allocate": "allocate"
    }
  },
  "parts": {
    "title": "Parts Management",
    "partsManagementHQ": "Parts Management HQ",
    "approvalType": "Approval Type",
    "selectApprovalType": "Please select approval type",
    "requisitionApproval": "Requisition Approval",
    "damageApproval": "Parts Damage",
    "partNo": "Part No.",
    "partName": "Part Name",
    "partNamePlaceholder": "Please enter part name",
    "partNumber": "Part Number",
    "partNumberPlaceholder": "Please enter part number",
    "requisitionNumber": "Requisition Number",
    "requisitionNumberPlaceholder": "Enter requisition number (fuzzy search)",
    "purchaseOrderNumber": "Purchase Order Number",
    "purchaseOrderNumberPlaceholder": "Enter purchase order number (fuzzy search)",
    "documentNumberHQ": "Document No.",
    "documentTypeHQ": "Document Type",
    "approvalTypeHQ": "Approval Type",
    "generateDate": "Generate Date",
    "documentStatus": "Document Status",
    "documentTypeRequisition": "Requisition",
    "documentTypeDamage": "Damage",
    "pendingRequisitionApproval": "Pending Requisition Approval",
    "pendingDamageApproval": "Pending Damage Approval",
    "supplierName": "Supplier Name",
    "supplierNamePlaceholder": "Select supplier name",
    "requisitionDate": "Requisition Date",
    "requisitionDateRange": "Requisition Date Range",
    "arrivalDateRange": "Arrival Date Range",
    "arrivalTimeRange": "Arrival Date Range",
    "requisitionStatus": "Requisition Status",
    "selectRequisitionStatus": "Please select requisition status",
    "selectStatus": "Please select status",
    "statusSubmitted": "Submitted",
    "statusApproved": "Approved",
    "statusRejected": "Rejected",
    "statusShipped": "Shipped",
    "statusPartialReceived": "Partially Received",
    "statusReceived": "Received",
    "statusCancelled": "Cancelled",
    "statusVoided": "Voided",
    "inventoryStatus": "Inventory Status",
    "selectInventoryStatus": "Please select inventory status",
    "statusNormal": "Normal",
    "statusBelowSafetyStock": "Below Safety Stock",
    "newRequisition": "New Requisition",
    "exportReport": "Export Report",
    "partScrap": "Part Scrap",
    "scrapRecord": "Scrap Record",
    "approve": "Approve",
    "approveRequisition": "Approve Requisition",
    "approvalResult": "Approval Result",
    "approvalResultRequired": "Please select approval result",
    "rejectionReason": "Rejection Reason",
    "rejectionReasonRequired": "Please enter rejection reason",
    "selectOneForApproval": "Please select one record for approval",
    "requisitionDetail": "Requisition Detail",
    "serialNumber": "Serial Number",
    "quantity": "Quantity",
    "unit": "Unit",
    "expectedArrivalTime": "Expected Arrival Time",
    "ship": "Ship",
    "shipParts": "Ship Parts",
    "shipPartially": "Ship Partially",
    "shipCompletely": "Ship Completely",
    "shippedQuantity": "Shipped Quantity",
    "shippingStatus": "Shipping Status",
    "notShipped": "Not Shipped",
    "partiallyShipped": "Partially Shipped",
    "fullyShipped": "Fully Shipped",
    "shipSuccess": "Shipped successfully",
    "shipFailed": "Failed to ship",
    "status": {
      "submitted": "Submitted",
      "approved": "Approved",
      "rejected": "Rejected",
      "shipped": "Shipped",
      "partialReceived": "Partially Received",
      "received": "Received",
      "voided": "Voided"
    }
  },
  "partsReceipt": {
    "title": "Parts Receipt (Store)",
    "partName": "Part Name",
    "partNamePlaceholder": "Enter part name",
    "partNumber": "Part Number",
    "partNumberPlaceholder": "Enter part number",
    "supplierName": "Supplier Name",
    "supplierNamePlaceholder": "Enter supplier name",
    "requisitionNumber": "Requisition Number",
    "requisitionNumberPlaceholder": "Enter requisition number",
    "purchaseOrderNumber": "Purchase Order Number",
    "purchaseOrderNumberPlaceholder": "Enter purchase order number",
    "deliveryOrderNumber": "Delivery Order Number",
    "deliveryOrderNumberPlaceholder": "Enter delivery order number",
    "receipt": "Receipt",
    "receive": "Receive",
    "receiptStatus": "Receipt Status",
    "received": "Received",
    "notReceived": "Not Received",
    "billed": "Billed",
    "generateReceiptOrder": "Generate Receipt Order",
    "invalidateReceiptOrder": "Invalidate Receipt Order",
    "receiptOrderNumber": "Receipt Order Number",
    "partQuantity": "Part Quantity",
    "unit": "Unit",
    "receiptDialogTitle": "Parts Receipt",
    "receivedQuantity": "Received Quantity",
    "receiptQuantity": "Receipt Quantity",
    "damagedQuantity": "Damaged Quantity",
    "damageReason": "Damage Reason",
    "damageDescription": "Damage Description",
    "damageReasonPlaceholder": "Enter damage reason",
    "damageDescriptionPlaceholder": "Enter damage description",
    "selectItemForReceipt": "Please select items for receipt.",
    "damageReasonRequired": "Damage description is required when damaged quantity is not 0.",
    "damageDescriptionRequired": "Damage description is required when damaged quantity is not 0.",
    "receiptSuccess": "Receipt successful!",
    "generateReceiptOrderDialogTitle": "Generate Receipt Order",
    "receiptTime": "Receipt Time",
    "receiptTimePlaceholder": "Select receipt time",
    "quantity": "Quantity",
    "printReceiptOrder": "Print Receipt Order",
    "selectItemToPrint": "Please select items to print receipt order.",
    "generateReceiptOrderSuccess": "Receipt order generated successfully!",
    "invalidateReceiptOrderDialogTitle": "Invalidate Receipt Order",
    "receiptOrderStatus": "Receipt Order Status",
    "receiptOrderStatusPlaceholder": "Select status",
    "statusActive": "Active",
    "statusInvalid": "Invalid",
    "statusEffective": "Effective",
    "statusInvalidated": "Invalidated",
    "generateDate": "Generate Date",
    "generationDate": "Generation Date",
    "confirmInvalidate": "Are you sure to invalidate this order?",
    "confirmInvalidateReceiptOrder": "Are you sure to invalidate this order?",
    "invalidateReceiptOrderSuccess": "Receipt order invalidated successfully!",
    "receiptOrderDetailTitle": "Receipt Order Details",
    "viewDetails": "View Details",
    "invalidate": "Invalidate",
    "viewReceiptOrder": "View Receipt Order"
  }
}
