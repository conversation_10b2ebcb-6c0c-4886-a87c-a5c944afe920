{
    "common": {
      "confirm": "确定",
      "cancel": "取消",
      "search": "查询",
      "yes": "是",
      "no": "否",
      "hour": "小时",
      "hours": "小时",
      "operationSuccessful": "操作成功！",
      "operationFailed": "操作失败！",
      "reLoginPrompt": "登录已过期，请重新登录。",
      "warning": "警告",
      "noPermission": "您没有操作权限。",
      "networkError": "网络错误，请稍后重试。",
      "badRequest": "请求错误。",
      "unauthorized": "未授权，请重新登录。",
      "forbidden": "拒绝访问。",
      "notFound": "请求地址出错，资源未找到。",
      "requestTimeout": "请求超时。",
      "serverError": "服务器内部错误。",
      "notImplemented": "服务未实现。",
      "badGateway": "网关错误。",
      "serviceUnavailable": "服务不可用。",
      "gatewayTimeout": "网关超时。",
      "unknownError": "未知错误。",
      "noResponse": "服务器没有响应。",
      "requestSetupError": "请求配置错误。",
      "reset": "重置",
      "operations": "操作",
      "confirmDelete": "确定删除 '{item}' 吗？",
      "languageChanged": "语言切换成功！",
      "edit": "编辑",
      "delete": "删除",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "confirmSave": "确定保存吗？",
      "confirmAdd": "确定新增吗？",
      "exporting": "正在导出...",
      "noData": "暂无数据",
      "detail": "详情",
      "assign": "分配",
      "submitConfirm": "提交确认",
      "recall": "撤回",
      "print": "打印",
      "customerConfirm": "客户确认",
      "createWorkOrder": "创建工单",
      "export": "导出",
      "to": "至",
      "pleaseSelect": "请选择",
      "all": "全部",
      "save": "保存",
      "clear": "清除",
      "uploadSuccessful": "上传成功",
      "pleaseSign": "请先签名",
      "pleaseUploadSignature": "请先上传签名",
      "tip": "提示",
      "confirmSubmitConfirm": "确定要提交确认吗？",
      "confirmRecall": "确定要撤回吗？",
      "confirmPrint": "确定要打印吗？",
      "operationCanceled": "操作已取消",
      "index": "序号",
      "saveConfig": "保存配置",
      "noDataTip": "当前查询条件下没有找到相关数据",
      "close": "关闭",
      "confirmExport": "确认导出",
      "fetchFailed": "获取数据失败",
      "success": "成功",
      "paginationLayout": "total, sizes, prev, pager, next, jumper",
      "total": "总计",
      "add": "新增",
      "optional": "（可选）",
      "required": "（必填）",
      "selectPlaceholder": "请选择",
      "inputPlaceholder": "请输入",
      "all": "全部",
      "yes": "是",
      "no": "否",
      "loading": "加载中...",
      "items": "条",
      "page": "页",
      "operationSuccessful": "操作成功",
      "operationFailed": "操作失败",
      "pleaseInput": "请输入",
      "required": "必填项",
      "yes": "是",
      "no": "否",
      "unknown": "未知",
      "vehicleAge": "车龄",
      "months": "月"
    },
    "login": {
      "title": "登录",
      "username": "用户名",
      "password": "密码",
      "loginButton": "登录",
      "notFound": "记录未找到",
      "ownerPhone": "车主手机号",
      "confirmCreateRepairOrder": "确定为登记单 {checkinId} 创建环检单吗？"
    },
    "sales": {
      "vehicleList": "车辆列表",
      "id": "ID",
      "vin": "车辆识别码",
      "model": "车型",
      "brand": "品牌",
      "color": "颜色",
      "price": "价格",
      "statusLabel": "状态",
      "manufactureDate": "制造日期",
      "engineNumber": "发动机号",
      "status": {
        "in_stock": "在库",
        "sold": "已售",
        "reserved": "已预定"
      },
      "addVehicle": "新增车辆",
      "editVehicle": "编辑车辆",
      "vinPlaceholder": "请输入车辆识别码",
      "modelPlaceholder": "请输入车型",
      "brandPlaceholder": "请输入品牌",
      "statusPlaceholder": "请选择状态",
      "colorPlaceholder": "请输入颜色",
      "pricePlaceholder": "请输入价格",
      "manufactureDatePlaceholder": "请选择制造日期",
      "engineNumberPlaceholder": "请输入发动机号"
    },
    "checkin": {
      "checkinList": "到店登记列表",
      "repairPersonName": "送修人名称",
      "repairPersonNamePlaceholder": "请输入送修人名称",
      "repairPersonPhone": "送修人手机号",
      "repairPersonPhonePlaceholder": "请输入送修人手机号",
      "createdAt": "创建时间",
      "createdAtPlaceholder": "请选择创建日期范围",
      "export": "导出",
      "createRecord": "创建登记单",
      "id": "序号",
      "checkinId": "登记单号",
      "licensePlate": "车牌号",
      "vehicleModelConfig": "车型配置",
      "color": "颜色",
      "serviceAdvisor": "服务顾问",
      "relatedRepairOrderId": "关联环检单号",
      "serviceType": "服务类型",
      "notes": "备注",
      "viewDetails": "查看详情",
      "edit": "编辑",
      "delete": "删除",
      "createRepairOrder": "创建环检单",
      "addCheckinRecord": "新增到店登记单",
      "editCheckinRecord": "编辑到店登记单",
      "licensePlatePlaceholder": "请输入车牌号",
      "vehicleInfoNotFound": "未找到相关车辆信息",
      "vehicleInfoAutoFill": "自动填充或手动填写",
      "vehicleAge": "车龄(月)",
      "ownerInfo": "车主信息",
      "serviceTypePlaceholder": "请选择服务类型",
      "notesPlaceholder": "请输入备注",
      "save": "保存",
      "cancel": "取消",
      "serviceTypeOptions": {
          "repair": "维修",
          "maintenance": "保养",
          "inspection": "检测",
          "paint": "喷漆"
      },
      "repairOrderAlreadyExists": "该到店登记单已存在关联环检单。",
      "repairOrderCreatedSuccess": "环检单创建成功！",
      "notFound": "记录未找到",
      "ownerPhone": "车主手机号"
    },
    "afterSales": {
      "appointmentManagement": "预约管理",
      "breadcrumb": "售后服务 / 预约管理",
      "status": "预约状态",
      "selectStatus": "请选择预约状态",
      "serviceType": "服务类型",
      "selectServiceType": "请选择服务类型",
      "serviceAdvisor": "服务顾问",
      "selectServiceAdvisor": "请选择服务顾问",
      "statuses": {
        "not_arrived": "未到店",
        "arrived": "已到店",
        "cancelled": "已取消",
        "no_show": "未履约",
        "pending_payment": "待支付"
      },
      "serviceTypes": {
        "maintenance": "保养",
        "repair": "维修"
      },
      "confirmCancel": "确定取消此预约吗？",
      "appointmentDetail": "预约详情",
      "appointmentId": "预约单号",
      "appointmentTime": "预约日期",
      "customerInfo": "客户信息",
      "vehicleInfo": "车辆信息",
      "licensePlate": "车牌号",
      "mileage": "里程数",
      "appointmentInfo": "预约信息",
      "store": "门店",
      "technician": "技师",
      "serviceContent": "服务内容",
      "paymentInfo": "支付信息",
      "paymentStatus": "支付状态",
      "paymentAmount": "支付金额",
      "paymentOrderNumber": "支付流水号",
      "paymentStatuses": {
        "paid": "已支付",
        "unpaid": "未支付",
        "refunded": "已退款"
      }
    },
    "orderApproval": {
      "pageTitle": "订单审核",
      "pendingTab": "待审批",
      "approvedTab": "已审批",
      "searchTitle": "筛选条件",
      "approvalType": "审批类型",
      "approvalTypePlaceholder": "请选择审批类型",
      "cancelOrderApproval": "取消订单审批",
      "modifyOrderApproval": "修改订单审批",
      "vehicleColorModificationApproval": "车辆颜色修改审批",
      "orderNumber": "订单编号",
      "orderNumberPlaceholder": "请输入订单编号",
      "submittedBy": "提交人",
      "submittedByPlaceholder": "请输入提交人姓名",
      "submissionTime": "提交时间",
      "submissionTimeEnd": "提交时间结束",
      "startDate": "开始日期",
      "endDate": "结束日期",
      "approvalStatus": "审核状态",
      "approvalStatusPlaceholder": "请选择审核状态",
      "pendingInitialReview": "待初审",
      "pendingFinalReview": "待终审",
      "aboutToTimeout": "即将超时",
      "approvalResult": "审核结果",
      "approvalResultPlaceholder": "请选择审核结果",
      "approved": "审批通过",
      "rejected": "审批驳回",
      "timeoutRejected": "超时驳回",
      "store": "门店",
      "storePlaceholder": "请选择门店",
      "serialNumber": "序号",
      "approvalNumber": "审核单号",
      "remainingTime": "剩余时间",
      "approvalTime": "审核时间",
      "approvedBy": "审核人",
      "approve": "通过",
      "reject": "驳回",
      "approvalHistory": "审核历史",
      "totalCount": "共 {count} 条记录",
      "selectedCount": "已选择 {count} 条记录",
      "batchApprove": "批量通过",
      "batchReject": "批量驳回",
      "days": "天",
      "hours": "小时",
      "minutes": "分钟",
      "timeout": "已超时",
      "urgent": "紧急处理",
            "aboutTimeout": "即将超时",
      "highPriority": "高优先级",
      "initialReview": "初审",
      "finalReview": "终审",
      "pendingApprovalList": "待审批列表",
      "approvedList": "已审批列表",
      "fetchDataFailed": "获取数据失败",
      "orderDetailNotImplemented": "订单详情功能尚未实现",
      "approveSuccess": "审核通过成功",
      "rejectSuccess": "审核驳回成功",
      "operationFailed": "操作失败",
      "batchOperationSuccess": "批量操作成功",
      "batchOperationFailed": "批量操作失败",
      "exportSuccess": "导出成功",
      "exportFailed": "导出失败"
    },
    "invoice": {
      "title": "发票管理",
      "invoiceNumber": "发票编号",
      "invoiceNumberPlaceholder": "请输入发票编号",
      "customerName": "购车人姓名",
      "customerNamePlaceholder": "请输入购车人姓名",
      "customerPhone": "购车人手机号",
      "customerPhonePlaceholder": "请输入购车人手机号",
      "customerEmail": "购车人邮箱",
      "customerEmailPlaceholder": "请输入购车人邮箱",
      "customerAddress": "购车人地址",
      "customerState": "购车人州",
      "customerCity": "购车人城市",
      "orderNumber": "订单编号",
      "orderNumberPlaceholder": "请输入订单编号",
      "vin": "车辆识别代号",
      "vinPlaceholder": "请输入车辆识别代号",
      "salesType": "销售类型",
      "salesStore": "销售门店",
      "salesConsultant": "销售顾问",
      "invoiceDate": "开票日期",
      "invoiceDateRange": "开票日期范围",
      "batchPrint": "批量打印",
      "export": "导出",
      "detail": "详情",
      "print": "打印",
      "email": "邮件",
      "log": "日志",
      "invoiceAmount": "发票总额",
      "createdTime": "创建时间",
      "model": "车型系列",
      "variant": "车型配置版本",
      "color": "车辆颜色",
      "paymentMethod": "客户付款方式",
      "financeCompany": "贷款金融公司",
      "loanAmount": "贷款金额",
      "emailConfirm": "邮件发送确认",
      "confirmSendEmail": "确认发送邮件到客户邮箱？",
      "exportConfig": "导出配置",
      "exportFormat": "导出格式",
      "exportScope": "导出范围",
      "currentPage": "当前页",
      "allData": "全部数据",
      "filteredData": "筛选结果",
      "operationLog": "操作日志",
      "operationType": "操作类型",
      "operator": "操作人",
      "operationTime": "操作时间",
      "operationResult": "操作结果",
      "remark": "备注",
      "printSuccess": "打印成功",
      "emailSentSuccess": "邮件发送成功",
      "exportSuccess": "导出成功",
      "batchPrintSuccess": "批量打印成功",
      "pleaseSelectRecords": "请选择要操作的记录",
      "invoiceDetail": "发票详情",
      "basicInfo": "发票基本信息",
      "customerInfo": "客户详细信息",
      "vehicleInfo": "车辆详细信息",
      "financeInfo": "金融信息详情",
      "insuranceInfo": "保险信息详情",
      "priceStructure": "价格结构明细",
      "receipts": "收据明细信息",
      "otrFees": "OTR费用明细",
      "companyName": "公司名称",
      "companyAddress": "公司地址",
      "gstNumber": "GST编号",
      "sstNumber": "SST编号",
      "engineNumber": "发动机号",
      "chassisNumber": "底盘号",
      "engineCapacity": "发动机排量",
      "transmission": "变速箱",
      "loanTerm": "贷款期限",
      "months": "个月",
      "interestRate": "利率",
      "monthlyPayment": "月供",
      "insuranceCompany": "保险公司",
      "agentCode": "代理编码",
      "policyNumber": "保险单号",
      "policyDate": "出单日期",
      "insuranceAmount": "保险费用",
      "vehiclePrice": "车辆销售价",
      "adjustmentAmount": "调整金额",
      "accessories": "选配件明细",
      "category": "类别",
      "accessoryName": "配件名称",
      "unitPrice": "单价",
      "quantity": "数量",
      "totalPrice": "总价",
      "feeType": "费用类型",
      "description": "费用说明",
      "amount": "金额",
      "receiptNumber": "票据单号",
      "effectiveDate": "生效日期",
      "paymentDate": "付款日期",
      "bankName": "银行名称",
      "accountNumber": "账户号码",
      "status": "状态",
      "invoiceStatuses": {
        "issued": "已开票",
        "printed": "已打印", 
        "sent": "已发送"
      },
      "operationTypes": {
        "view": "查看详情",
        "print": "打印"
      },
      "operationResults": {
        "success": "成功",
        "failed": "失败"
      },
      "operationRemarks": {
        "viewDetail": "查看发票详情",
        "printSuccess": "打印发票成功"
      },
      "messages": {
        "fetchListFailed": "获取发票列表失败",
        "fetchStoreListFailed": "获取门店列表失败",
        "fetchConsultantListFailed": "获取销售顾问列表失败",
        "fetchDetailFailed": "获取发票详情失败",
        "printFailed": "打印失败",
        "batchPrintFailed": "批量打印失败",
        "fetchInvoiceInfoFailed": "获取发票信息失败",
        "emailSendFailed": "邮件发送失败",
        "exportFailed": "导出失败"
      },
      "salesTypeOptions": {
        "PHP": "PHP",
        "CASH": "现金",
        "FINANCE": "贷款"
      }
    },
    "vehicleAllocation": {
      "title": "车辆配车管理",
      "orderNumber": "订单编号",
      "orderNumberPlaceholder": "请输入订单编号",
      "customerName": "购车人名称",
      "customerNamePlaceholder": "请输入购车人名称",
      "customerPhone": "购车人手机号",
      "customerPhonePlaceholder": "请输入购车人手机号",
      "allocationStatus": "配车状态",
      "allocationStatusPlaceholder": "请选择配车状态",
      "orderStatus": "订单状态",
      "orderStatusPlaceholder": "请选择订单状态",
      "vin": "VIN",
      "vinPlaceholder": "请输入VIN",
      "factoryOrderNumber": "工厂订单号",
      "factoryOrderNumberPlaceholder": "请输入工厂订单号",
      "allocationTime": "配车时间",
      "store": "门店",
      "storePlaceholder": "请选择门店",
      "salesConsultant": "销售顾问",
      "salesConsultantPlaceholder": "请选择销售顾问",
      "orderCreateTime": "订单创建时间",
      "model": "车型",
      "variant": "配置",
      "color": "颜色",
      "allocated": "已配车",
      "unallocated": "未配车",
      "allocate": "配车",
      "cancelAllocate": "取消配车",
      "records": "记录",
      "allocationConfirm": "配车确认",
      "confirmCancel": "确认取消",
      "confirmCancelAllocation": "是否确认取消配车？",
      "allocationRecords": "配车记录查询",
      "allocationRecordDetail": "配车记录详情",
      "orderDetail": "订单信息",
      "ordererName": "下单人名称",
      "ordererPhone": "下单人手机号",
      "customerType": "客户类型",
      "availableVehicles": "可配车辆",
      "availableVehiclesQuery": "可配车辆查询",
      "warehouseName": "仓库名称",
      "stockStatus": "库存状态",
      "lockStatus": "锁定状态",
      "inStockTime": "入库时间",
      "selectVehicle": "选择车辆",
      "allocationSuccess": "配车成功",
      "allocationFailed": "配车失败",
      "cancelAllocationSuccess": "取消配车成功",
      "exportData": "导出数据",
      "confirmExport": "确认导出当前查询条件下的车辆配车数据",
      "operationType": "操作类型",
      "operator": "操作人",
      "operationTime": "操作时间",
      "processResult": "处理结果",
      "remarks": "备注",
      "operationDetails": "操作详情",
      "totalAllocations": "总配车次数",
      "totalCancellations": "取消配车次数",
      "successRate": "配车成功率",
      "averageAllocationTime": "平均配车时长",
      "statistics": "统计信息",
      "timeline": "历史时间轴",
      "allocationHistory": "配车历史",
      "noVehicleSelected": "请选择一辆车辆",
      "vehicleConfigMismatch": "车辆配置与订单要求不匹配",
      "orderStatuses": {
        "submitted": "已提交",
        "confirmed": "已确认",
        "cancel_review": "取消审核中",
        "cancel_approved": "取消审核通过",
        "cancelled": "已取消",
        "ready_delivery": "待交车",
        "delivered": "已交车"
      },
      "stockStatuses": {
        "in_stock": "在库",
        "allocated": "已配车",
        "out_of_stock": "已出库"
      },
      "operationTypes": {
        "allocate": "配车",
        "cancel_allocate": "取消配车",
        "view_detail": "查看详情",
        "system_auto_cancel": "系统自动取消"
      },
      "processResults": {
        "success": "成功",
        "failed": "失败"
      }
    },
    "approval": {
      "title": "订单审批管理",
      "pending": "待审批",
      "approved": "已审批",
      "approvalType": "审批类型",
      "orderNumber": "订单号",
      "submitter": "提交人",
      "submitTime": "提交时间",
      "timeRange": "时间范围",
      "approvalStatus": "审批状态",
      "approvalResult": "审批结果",
      "store": "门店",
      "batchApproval": "批量审批",
      "batchReject": "批量驳回",
      "approve": "审批",
      "reject": "驳回",
      "viewDetails": "查看详情",
      "viewHistory": "查看历史",
      "export": "导出",
      "remainingTime": "剩余时间",
      "types": {
        "orderCancel": "订单取消",
        "customerInfoModify": "客户信息修改",
        "vehicleColorModify": "车辆颜色修改"
      },
      "status": {
        "pending": "待审批",
        "approved": "已通过",
        "rejected": "已驳回",
        "timeout": "审批超时"
      },
      "result": {
        "approved": "通过",
        "rejected": "驳回",
        "timeout": "超时"
      }
    },
    "inspectionForm": {
      "title": "环检单管理",
      "form": {
        "inspectionNo": "环检单号",
        "inspectionStatus": "环检状态",
        "licensePlateNo": "车牌号",
        "repairmanName": "送修人名称",
        "technician": "技师",
        "repairmanPhone": "送修人手机号",
        "createTime": "创建时间"
      },
      "status": {
        "pending": "待环检",
        "inProgress": "环检中",
        "pendingConfirm": "待确认",
        "confirmed": "已确认"
      },
      "table": {
        "index": "序号",
        "inspectionNo": "环检单号",
        "inspectionStatus": "环检状态",
        "repairmanName": "送修人名称",
        "repairmanPhone": "送修人手机号",
        "licensePlateNo": "车牌号",
        "modelConfig": "车型配置",
        "color": "颜色",
        "serviceAdvisor": "服务顾问",
        "technician": "技师",
        "registerType": "登记类型",
        "serviceType": "服务类型",
        "customerConfirmTime": "客户确认时间",
        "createTime": "创建时间",
        "updateTime": "更新时间"
      }
    },
    "assignInspectionForm": {
      "title": "分配环检单",
      "inspectionNo": "环检单号",
      "licensePlateNo": "车牌号",
      "repairmanName": "送修人名称",
      "registerType": "登记类型",
      "serviceType": "服务类型",
      "technician": "技师",
      "selectTechnicianPlaceholder": "请选择技师"
    },
    "inspectionFormDetailEdit": {
      "titleEdit": "编辑环检单",
      "titleDetail": "环检单详情",
      "clientInfo": "客户信息",
      "reserveName": "预约人名称",
      "reservePhone": "预约人手机号",
      "repairmanName": "送修人名称",
      "repairmanPhone": "送修人手机号",
      "remark": "备注",
      "vehicleInfo": "车辆信息",
      "licensePlateNo": "车牌号",
      "vin": "VIN号",
      "modelConfig": "车型配置",
      "color": "颜色",
      "vehicleAge": "车龄",
      "inspectionFormInfo": "环检单信息",
      "inspectionStatus": "环检单状态",
      "registerType": "登记类型",
      "servicePackageName": "服务包名称",
      "createTime": "创建时间",
      "serviceAdvisor": "服务顾问",
      "technician": "技师",
      "customerConfirmTime": "客户确认时间",
      "customerConfirmImage": "客户确认图片",
      "inspectionContentList": "环检内容清单",
      "parkingAreaRecord": "停车区域记录",
      "waitingArea": "等候区",
      "leavingArea": "离开区",
      "parkingZone": "停车区",
      "dashboardInspection": "仪表盘检查",
      "mileageRecord": "里程数记录",
      "batteryLevel": "电池电量检查",
      "remainingRange": "续航里程显示",
      "energyConsumption": "能耗显示",
      "functionalityCheck": "功能性检查",
      "gaugesIndicators": "仪表和指示器",
      "checkFunction": "检查功能",
      "batteryStatusIndicator": "电池状态指示",
      "chargingStatusIndicator": "充电状态指示",
      "energyConsumptionFunction": "能耗显示功能",
      "airConditioningSystem": "空调系统",
      "heatPumpCheck": "热泵功能检查",
      "batteryPreheating": "电池预加热功能",
      "wiperWasher": "雨刮和清洗器",
      "checkVisualCondition": "检查外观状况",
      "infotainmentSystem": "信息娱乐系统",
      "chargingStationNavigation": "充电站导航功能",
      "energyMonitoring": "能耗监控功能",
      "otaUpdateStatus": "OTA更新状态",
      "warningLightsCheck": "警告灯检查",
      "batterySystem": "电池系统",
      "motorSystem": "电机系统",
      "chargingSystem": "充电系统",
      "highVoltageSystem": "高压系统",
      "coolingSystem": "冷却系统",
      "regenerativeBraking": "再生制动",
      "insulationMonitoring": "绝缘监测",
      "abs": "ABS",
      "eps": "EPS电动转向",
      "exteriorInspection": "外观检查",
      "bodyExteriorInspection": "车身外观检查",
      "frontCheck": "前检查",
      "rearCheck": "后检查",
      "leftSideCheck": "左侧检查",
      "rightSideCheck": "右侧检查",
      "roofViewCheck": "车顶视图检查",
      "chargingPortCover": "充电口盖检查",
      "electricSystemInspection": "电动系统检查",
      "highVoltageBatteryCheck": "高压电池检查",
      "batteryPackVisualInspection": "电池包外观检查",
      "noDamage": "无损伤",
      "noLeakage": "无泄漏",
      "mountingBoltsSecure": "固定螺栓紧固",
      "batteryCoolingSystem": "电池冷却系统",
      "coolantLevel": "冷却液液位",
      "coolingFanFunction": "冷却风扇功能",
      "radiatorCleanliness": "散热器清洁度",
      "highVoltageCableInspection": "高压线束检查",
      "insulationIntegrity": "绝缘层完整性",
      "connectorSealing": "连接器密封性",
      "fixingClipsStatus": "固定卡扣状态",
      "motorSystemCheck": "电机系统检查",
      "driveMotor": "驱动电机",
      "operatingNoiseCheck": "运行噪音检查",
      "vibrationCheck": "震动检查",
      "temperatureMonitoring": "温度监控",
      "motorController": "电机控制器",
      "visualInspection": "外观检查",
      "heatDissipationSystem": "散热系统",
      "connectionHarness": "连接线束",
      "chargingSystemCheck": "充电系统检查",
      "onboardCharger": "车载充电器",
      "functionTest": "功能测试",
      "heatDissipationCheck": "散热检查",
      "chargingInterface": "充电接口",
      "contactCleanliness": "接触片清洁度",
      "lockingMechanism": "锁止机构",
      "sealingPerformance": "密封性能",
      "tireWearInspection": "轮胎磨损检查",
      "tyreThreadCheck": "胎纹深度",
      "checkDeviation": "磨损偏差检查",
      "lowRollingResistanceTyreStatus": "低滚阻轮胎状态",
      "tirePosition": "轮胎位置",
      "frontRight": "前右轮",
      "frontLeft": "前左轮",
      "rearRight": "后右轮",
      "rearLeft": "后左轮",
      "tirePressureMonitoring": "轮胎压力监测",
      "tpmsFunctionCheck": "TPMS功能检查",
      "tyrePressureStandardConfirmation": "胎压标准值确认",
      "temperatureMonitoringFunction": "温度监测功能",
      "customerSelfDescribedProblem": "客户自述问题",
      "good": "良好",
      "attentionRequired": "需要注意",
      "notGood": "不良",
      "notApplicable": "不适用"
    },
    "customerConfirm": {
      "title": "客户确认",
      "inspectionNo": "环检单号",
      "repairmanName": "送修人名称",
      "licensePlateNo": "车牌号",
      "customerSignature": "客户签名",
      "uploadSignature": "上传签名图片",
      "signPlaceholder": "请客户在此区域签名"
    },
    "technicians": {
      "technicianA": "技师A",
      "technicianB": "技师B",
      "technicianC": "技师C"
    },
    "mockData": {
      "registerTypes": {
        "appointment": "预约",
        "walkIn": "自然到店"
      },
      "serviceTypes": {
        "maintenance": "保养",
        "repair": "维修"
      },
      "colors": {
        "white": "白色",
        "black": "黑色",
        "blue": "蓝色",
        "gray": "灰色"
      },
      "names": {
        "zhangSan": "张三",
        "liHua": "李华",
        "wangDaChui": "王大锤",
        "linYiYi": "林依依",
        "liSi": "李四",
        "wangWu": "王五",
        "zhaoLiu": "赵六",
        "qianQi": "钱七",
        "sunBa": "孙八",
        "zhouJiu": "周九",
        "wuShi": "吴十"
      },
      "carModels": {
        "modelY2023Long": "Model Y 2023 长续航版",
        "model32022Standard": "Model 3 2022 标准续航版",
        "modelX2024Performance": "Model X 2024 高性能版",
        "modelY2023Performance": "Model Y 2023 高性能版"
      }
    },
    "messages": {
      "createWorkOrderFor": "为环检单 {inspectionNo} 创建工单",
      "exportingData": "导出数据",
      "viewDetail": "查看详情",
      "assignInspectionForm": "分配环检单",
      "editInspectionForm": "编辑环检单",
      "submitConfirm": "提交确认",
      "recall": "撤回",
      "print": "打印环检单",
      "customerConfirm": "客户确认",
      "createWorkOrder": "创建工单",
      "saveInspectionFormDetail": "保存环检单详情：",
      "assignInspectionFormData": "分配环检单：",
      "customerConfirmData": "客户确认：",
      "savingConfig": "保存配置："
    },
    "quota": {
      "pageTitle": "预约限量配置",
      "storeName": "门店名称",
      "storeCode": "门店编号",
      "permissionTip": "当前登录用户仅可配置所属门店的预约限量",
      "configuredListTitle": "已配置限量列表",
      "addNewQuota": "新增预约限量",
      "emptyState": "暂无配置数据",
      "unsavedChangesWarning": "您有未保存的更改，确定要关闭吗？",
      "dateMustBeFutureOrToday": "配置日期不能早于今天",
      "selectDateFirst": "请先选择配置日期",
      "timeSlotExceedsOperatingHours": "时间段不能超过营业时间（18:00）",
      "addNewQuotaTitle": "新增预约限量",
      "editQuotaTitle": "编辑预约限量",
      "store": {
        "name": "吉隆坡中央店",
        "code": "KL001"
      },
      "table": {
        "configDate": "配置日期",
        "timeSlotCount": "时段数量",
        "totalQuota": "总限量",
        "bookedQuantity": "已预约数量",
        "lastUpdateTime": "最后更新时间",
        "expired": "已过期"
      },
      "summary": {
        "selectDate": "请选择配置日期",
        "addTimeSlot": "请添加时间段",
        "timeSlotsUnit": "个时段",
        "totalQuota": "总限量"
      },
      "modal": {
        "selectDateTitle": "选择配置日期",
        "dateLabel": "配置日期",
        "datePlaceholder": "请选择配置日期",
        "existingConfig": "该日期已有配置",
        "dateTip": "只能配置今天及以后的日期",
        "timeSlotConfigTitle": "时段配置",
        "addTimeSlot": "添加时段",
        "noTimeSlots": "暂无时段配置",
        "clickAddPrompt": "点击",
        "timeSlot": "时段",
        "startTime": "开始时间",
        "endTime": "结束时间",
        "quota": "限量",
        "startTimePlaceholder": "请选择开始时间",
        "endTimePlaceholder": "请选择结束时间",
        "quotaPlaceholder": "请输入限量",
        "configDescriptionTitle": "配置说明",
        "configDescription": {
          "item1": "每个时段的限量数代表该时段内可预约的最大数量",
          "item2": "时段之间不能重叠，系统会自动检测冲突",
          "item3": "营业时间为 8:00-18:00，时段不能超出此范围",
          "item4": "保存后立即生效，客户可在小程序中选择对应时段预约"
        }
      },
      "validation": {
        "dateRequired": "请选择配置日期",
        "atLeastOneTimeSlot": "至少需要添加一个时段",
        "timeRequired": "请完善时段的开始和结束时间",
        "startBeforeEnd": "开始时间必须早于结束时间",
        "quotaPositive": "限量必须大于0",
        "timeSlotOverlap": "时段 {slot1} 与 {slot2} 存在时间重叠，请调整"
      }
    },
    "workAssignment": {
      "dashboard": {
        "title": "派工看板",
        "autoRefresh": "自动刷新",
        "pendingAssignment": "待派工",
        "assignedOrders": "已分配",
        "inProgressOrders": "进行中",
        "completedOrders": "已完成",
        "availableTechnicians": "可用技师",
        "averageEfficiency": "平均效率",
        "utilizationRate": "利用率",
        "averageWaitingTime": "平均等待时间",
        "technicianSchedule": "技师工作安排",
        "selectDate": "选择日期",
        "today": "今天",
        "tomorrow": "明天",
        "thisWeek": "本周",
        "workload": "工作负荷",
        "available": "空闲",
        "orderStatusDistribution": "工单状态分布",
        "technicianWorkload": "技师工作负荷",
        "workloadPercentage": "负荷百分比",
        "workOrderDetail": "工单详情"
      },
      
      "management": {
        "title": "派工管理"
      },
      
      "common": {
        "refresh": "刷新",
        "search": "搜索",
        "reset": "重置",
        "confirm": "确认",
        "cancel": "取消",
        "detail": "详情",
        "hours": "小时",
        "minutes": "分钟",
        "unassigned": "未分配",
        "notScheduled": "未安排",
        "actions": "操作"
      },
      
      "workOrder": {
        "workOrderNo": "工单编号",
        "customerName": "客户姓名",
        "vehicleInfo": "车辆信息",
        "type": "工单类型",
        "priority": "优先级",
        "status": "工单状态",
        "estimatedDuration": "预计工时",
        "createdAt": "创建时间",
        "scheduledStartTime": "预计开工时间"
      },
      
      "technician": {
        "technician": "技师",
        "assignedTechnician": "分配技师",
        "currentTechnician": "当前技师",
        "status": {
          "available": "可用",
          "busy": "忙碌",
          "onLeave": "请假",
          "training": "培训",
          "resigned": "离职"
        }
      },
      
      "type": {
        "maintenance": "保养",
        "repair": "维修",
        "insurance": "保险"
      },
      
      "priority": {
        "normal": "普通",
        "urgent": "紧急"
      },
      
      "status": {
        "draft": "草稿",
        "pendingConfirm": "待确认",
        "confirmed": "已确认",
        "pendingAssignment": "待分配",
        "assigned": "已分配",
        "inProgress": "进行中",
        "pendingQc": "待质检",
        "pendingSettle": "待结算",
        "completed": "已完成",
        "cancelled": "已取消"
      },
      
      "actions": {
        "assign": "分配",
        "reassign": "重新分配",
        "checkIn": "开工",
        "checkOut": "完工"
      },
      
      "placeholder": {
        "enterWorkOrderNo": "请输入工单编号",
        "selectStatus": "请选择状态",
        "selectType": "请选择类型",
        "selectPriority": "请选择优先级",
        "selectTechnician": "请选择技师"
      },
      
      "dialog": {
        "assignTitle": "分配工单",
        "reassignTitle": "重新分配工单"
      },
      
      "confirmMessages": {
        "checkIn": "确定要开工此工单吗？",
        "checkOut": "确定要完工此工单吗？"
      },
      
      "messages": {
        "loadDataFailed": "加载数据失败",
        "assignSuccess": "分配成功",
        "assignFailed": "分配失败",
        "reassignSuccess": "重新分配成功",
        "reassignFailed": "重新分配失败",
        "checkInSuccess": "开工成功",
        "checkInFailed": "开工失败",
        "checkOutSuccess": "完工成功",
        "checkOutFailed": "完工失败"
      }
    },
    "menu": {
      "DMS 看板": "DMS看板",
      "客户详情": "客户详情",
      "销售订单": "销售订单",
      "预约看板": "预约看板",
      "预约管理": "预约管理",
      "限量管理": "限量管理",
      "销售订单编辑": "销售订单编辑",
      "销售订单详情": "销售订单详情",
      "到店登记列表": "到店登记列表",
      "环检单管理": "环检单管理",
      "工单管理": "工单管理",
      "派工管理": "派工管理",
      "派工看板": "派工看板",
      "车辆查询": "车辆查询",
      "车辆登记": "车辆登记",
      "工单审批": "工单审批",
      "发票管理": "发票管理",
      "home": "首页",
      "customer-detail": "客户详情",
      "sales-order": "销售订单",
      "appointment-dashboard": "预约看板",
      "appointment-management": "预约管理",
      "quota-management": "预约限量管理",
      "order-edit": "销售订单编辑",
      "order-detail": "销售订单详情",
      "checkinList": "到店登记列表",
      "inspection-form": "环检单管理",
      "work-order": "工单管理",
      "work-assignment-management": "派工管理",
      "work-assignment-dashboard": "派工看板",
      "approval": "审批管理",
      "vehicle-query": "车辆查询",
      "vehicle-allocation": "车辆配车管理",
      "order-management": "订单管理",
      "lead-pool-management": "线索池管理",
      "test-drive-report": "试驾报表",
      "order-statistics-management": "订单统计管理",
      "potential-customer-management": "潜客管理",
      "potential-customer-defeat-approval-management": "潜客战败审批管理",
      "test-drive-registration": "到店试驾登记",
      "delivery-management": "交车管理"
    },
    "workOrder": {
      "title": "工单管理",
      "number": "工单号",
      "priority": "优先级",
      "type": "工单类型",
      "isClaim": "是否理赔",
      "isOutsourced": "是否外协",
      "status": "工单状态",
      "paymentStatus": "支付状态",
      "senderName": "送修人姓名",
      "senderPhone": "送修人电话",
      "licensePlate": "车牌号",
      "serviceAdvisor": "服务顾问",
      "technician": "技师",
      "createdTime": "创建时间",
      "confirmationTime": "确认时间",
      "startTime": "开始时间",
      "endTime": "结束时间",
      "totalAmount": "总金额",
      "modelConfigColor": "车型配置颜色",
      "hasAdditionalItems": "是否有追加项目",
      "create": "创建工单",
      "exportWorkOrder": "导出工单",
      "addItem": "添加项目",
      "submitApproval": "提交审批",
      "customerInfo": "客户信息",
      "vehicleInfo": "车辆信息",
      "appointmentCustomer": "预约客户",
      "appointmentPhone": "预约电话",
      "confirmationMethod": "确认方式",
      "vinCode": "VIN码",
      "warrantyStatus": "保修状态",
      "warrantyExpiry": "保修到期时间",
      "remarks": "备注",
      "customerSource": "客户来源",
      "settlementNumber": "结算单号",
      "hasAdditional": "是否有追加",
      "estimatedHours": "预估工时",
      "actualHours": "实际工时",
      "qcStatus": "质检状态",
      "qcNumber": "质检单号",
      "registrationTime": "登记时间",
      "laborTotal": "工时统计",
      "statistics": "统计",
      "laborDetails": "工时详情",
      "addLabor": "添加工时",
      "clearAll": "清空全部",
      "partsDetails": "配件详情",
      "addParts": "添加配件",
      "costSummary": "费用汇总",
      "laborAmount": "工时金额",
      "partsAmount": "配件金额",
      "additionalAmount": "追加金额",
      "workOrderTotal": "工单总额",
      "workOrderInfo": "工单信息",
      "itemType": "项目类型",
      "itemCode": "项目代码",
      "itemName": "项目名称",
      "hasAdditional": "是否追加",
      "standardHours": "标准工时",
      "laborRate": "工时单价",
      "subtotal": "小计",
      "partsName": "配件名称",
      "availableStock": "可用库存",
      "quantity": "数量",
      "unitPrice": "单价",
      "partsTotal": "配件统计",
      "partsTotalAmount": "配件总金额",
      "laborTotal": "工时统计",
      "laborTotalAmount": "工时总金额",
      "unit": "个",
      "autoFillOrManual": "自动填充或手动填写",
      "close": "关闭",
      "saveOnly": "仅保存",
      "saveAndPush": "保存并推送",
      "edit": "编辑工单",
      "projectSelection": "项目选择",
      "suggestedProjects": "推荐项目",
      "projectItems": {
        "oilChange": "更换机油",
        "filterChange": "更换机滤",
        "brakepadReplacement": "刹车片更换",
        "tireReplacement": "轮胎更换",
        "engineDiagnosis": "发动机故障检查"
      },
      "priorities": {
        "urgent": "紧急",
        "normal": "普通"
      },
      "types": {
        "repair": "维修",
        "maintenance": "保养",
        "insurance": "保险"
      },
      "statuses": {
        "draft": "草稿",
        "pending_confirmation": "待确认",
        "confirmed": "已确认",
        "pending_assignment": "待分配",
        "pending_start": "待开工",
        "in_progress": "进行中",
        "pending_qc": "待质检",
        "pending_settlement": "待结算",
        "rework_required": "需返工",
        "completed": "已完成",
        "cancelled": "已取消",
        "additional_pending": "追加待定",
        "waiting_approval": "等待审批",
        "waiting_parts": "等待配件"
      },
      "paymentStatuses": {
        "pending": "待支付",
        "deposit_paid": "已付定金",
        "paid": "已支付",
        "refunding": "退款中",
        "refunded": "已退款"
      },
      "customerSources": {
        "appointment": "预约到店",
        "walkin": "自然到店"
      },
      "confirmationMethods": {
        "phone": "电话确认",
        "sms": "短信确认",
        "wechat": "微信确认",
        "inPerson": "现场确认"
      },
      "validation": {
        "senderNameRequired": "请输入送修人姓名",
        "senderPhoneRequired": "请输入送修人电话",
        "senderPhoneFormat": "请输入正确的手机号格式",
        "licensePlateRequired": "请输入车牌号",
        "vinCodeRequired": "请输入VIN码",
        "vinCodeLength": "VIN码必须为17位",
        "modelConfigColorRequired": "请输入车型配置颜色",
        "atLeastOneItem": "至少需要添加一个工时或配件项目"
      },
      "messages": {
        "clearAllConfirm": "确定要清空全部项目吗？",
        "saveSuccess": "保存成功",
        "pushSuccess": "推送成功",
        "stockValidationFailed": "库存验证失败",
        "vehicleNotFound": "未找到车辆信息，请手动填写",
        "pleaseSelectProject": "请先选择一个项目",
        "pleaseSelectParts": "请至少选择一个零件"
      },
      "placeholders": {
        "workOrderNumber": "请输入工单号",
        "customerName": "请输入客户姓名",
        "customerPhone": "请输入客户电话",
        "licensePlate": "请输入车牌号",
        "selectServiceAdvisor": "请选择服务顾问",
        "selectDateRange": "请选择日期范围",
        "senderName": "请输入送修人姓名",
        "senderPhone": "请输入送修人电话",
        "vinCode": "请输入17位VIN码",
        "modelConfigColor": "请输入车型配置颜色",
        "remarks": "请输入备注信息",
        "searchProject": "搜索项目"
      }
    },
    "order": {
      "orderManagement": "厂端订单管理",
      "orderOverview": "订单概览统计",
      "filterConditions": "筛选条件",
      "orderNumber": "订单编号",
      "orderNumberPlaceholder": "请输入订单编号",
      "storeName": "门店",
      "storeNamePlaceholder": "请选择门店",
      "model": "车型",
      "modelPlaceholder": "请选择车型",
      "variant": "配置",
      "variantPlaceholder": "请先选择车型",
      "orderStatus": "订单状态",
      "orderStatusPlaceholder": "请选择订单状态",
      "paymentStatus": "支付状态",
      "paymentStatusPlaceholder": "请选择支付状态",
      "createDateRange": "下单日期",
      "createDateRangePlaceholder": "请选择下单日期范围",
      "exportExcel": "导出Excel",
      "customerName": "下单人",
      "customerPhone": "下单人手机号",
      "buyerName": "购车人",
      "buyerPhone": "购车人手机号",
      "buyerType": "购车人类别",
      "vin": "VIN",
      "paymentMethod": "付款方式",
      "loanApprovalStatus": "贷款审核状态",
      "approvalStatus": "订单审核状态",
      "insuranceStatus": "投保状态",
      "jpjRegistrationStatus": "JPJ车辆注册状态",
      "createTime": "订单创建时间",
      "color": "颜色",
      "monthlyOrderCount": "本月订单数量",
      "todayOrderCount": "今日订单数量",
      "topStore": "订单最多门店",
      "hotModel": "最热销车型",
      "pendingDeliveryCount": "待交车订单",
      "growth": "环比增长",
      "thisMonthOrders": "本月订单量",
      "orderStatuses": {
        "pending_deposit": "待支付定金",
        "confirmed": "已确认",
        "cancelled": "已取消",
        "pending_delivery": "待交车",
        "completed": "已完成"
      },
      "paymentStatuses": {
        "pending_deposit": "待支付定金",
        "deposit_paid": "定金已付",
        "pending_final": "待支付尾款",
        "fully_paid": "已全款"
      },
      "paymentMethods": {
        "full_payment": "全款",
        "installment": "分期"
      },
      "approvalStatuses": {
        "pending": "待审批",
        "approved": "已审批-通过",
        "rejected": "已审批-驳回"
      },
      "insuranceStatuses": {
        "pending": "未投保",
        "completed": "投保完成",
        "failed": "投保失败"
      },
      "jpjRegistrationStatuses": {
        "pending": "待登记",
        "completed": "已登记",
        "failed": "登记失败"
      },
      "editOrder": "编辑订单",
      "orderDetail": "订单详情",
      "personalDetails": "客户信息",
      "preferredOutletSalesAdvisor": "购车门店信息",
      "purchaseDetails": "购车信息",
      "vehicleInfo": "车辆信息",
      "invoiceInfo": "开票信息",
      "serviceRightsInfo": "服务&权益信息",
      "paymentInfo": "支付信息",
      "insuranceInfo": "保险信息",
      "otrFeesInfo": "OTR费用信息",
      "addRights": "新增权益",
      "pushToInsurance": "推送至保险系统",
      "submitDelivery": "提交交车",
      "backToList": "返回列表",
      "save": "保存",
      "salesSubtotal": "销售小计",
      "numberPlatesFee": "车牌费",
      "totalAmount": "整车开票价",
      "deposit": "定金",
      "finalPayment": "尾款金额",
      "remainingReceivable": "剩余应收",
      "loanAmount": "贷款金额",
      "loanTerm": "贷款期数",
      "accessoryTotalAmount": "选配件总金额",
      "rightsTotalDiscount": "权益优惠总金额",
      "insuranceTotalAmount": "保险总金额",
      "otrFeesTotalAmount": "登记费用总金额",
      "accessories": "选配件信息",
      "accessoryCode": "配件代码",
      "accessoryName": "配件名称",
      "accessoryCategory": "类别",
      "accessoryPrice": "配件单价",
      "accessoryQuantity": "数量",
      "accessoryTotalPrice": "总价",
      "rights": "权益列表",
      "rightCode": "权益代码",
      "rightName": "权益名称",
      "rightMode": "权益模式",
      "rightDiscountAmount": "优惠金额",
      "rightEffectiveDate": "生效日期",
      "rightExpiryDate": "终止日期",
      "deleteRight": "删除",
      "policyNumber": "保单号",
      "insuranceType": "保险类型",
      "insuranceCompany": "保险公司",
      "insuranceAmount": "保险价格",
      "insuranceEffectiveDate": "生效日期",
      "insuranceExpiryDate": "到期日期",
      "insuranceNotes": "备注",
      "otrTicketNumber": "票据单号",
      "otrFeeItem": "收费项目",
      "otrFeePrice": "收费价格",
      "otrEffectiveDate": "生效日期",
      "otrExpiryDate": "到期日期",
      "buyerIdType": "购车人身份证件类别",
      "buyerIdNumber": "购车人身份证件号",
      "buyerEmail": "购车人邮箱",
      "buyerAddress": "购车人地址",
      "buyerState": "购车人所在州",
      "buyerCity": "购车人所在城市",
      "buyerPostalCode": "购车人所在地邮编",
      "storeState": "所在州",
      "storeCity": "所在城市",
      "salesConsultant": "销售顾问",
      "invoicingType": "开票",
      "invoicingName": "开票名称",
      "invoicingPhone": "开票电话",
      "invoicingAddress": "开票地址",
      "confirmColorChange": "您已更改车辆颜色，是否提交审核？",
      "confirmSubmitDelivery": "确定要提交交车吗？",
      "deliveryConditionsNotMet": "交车前置条件不满足",
      "selectRights": "选择权益",
      "availableRights": "可用权益",
      "selectAll": "全选",
      "addSelectedRights": "添加"
    },
    "vehicleQuery": {
      "title": "车辆查询",
      "vin": "车架号",
      "vinPlaceholder": "请输入车架号",
      "factoryOrderNo": "工厂订单号",
      "factoryOrderNoPlaceholder": "请输入工厂订单号",
      "warehouseName": "仓库名称",
      "warehouseNamePlaceholder": "请选择仓库",
      "model": "车型",
      "modelPlaceholder": "请选择车型",
      "variant": "配置",
      "variantPlaceholder": "请先选择车型",
      "color": "颜色",
      "colorPlaceholder": "请先选择配置",
      "lockStatus": "锁定状态",
      "lockStatusPlaceholder": "请选择锁定状态",
      "locked": "已锁定",
      "unlocked": "未锁定",
      "invoiceStatus": "开票状态",
      "invoiceStatusPlaceholder": "请选择开票状态",
      "invoiced": "已开票",
      "notInvoiced": "未开票",
      "invoiceDate": "开票时间",
      "storageDate": "入库时间",
      "productionDate": "生产时间",
      "stockStatus": "库存状态",
      "deliveryStatus": "交车状态",
      "deliveryDate": "交车时间",
      "stockStatusOptions": {
        "inStock": "在库",
        "allocated": "配车",
        "inTransit": "在途",
        "transferred": "调拨"
      },
      "lockStatusOptions": {
        "locked": "已锁定",
        "unlocked": "未锁定"
      },
      "invoiceStatusOptions": {
        "invoiced": "已开票",
        "notInvoiced": "未开票"
      },
      "deliveryStatusOptions": {
        "delivered": "已交车",
        "notDelivered": "未交车"
      },
      "detailDialogTitle": "车辆详情",
      "exportDialogTitle": "导出数据",
      "exportFormat": "导出格式",
      "exportFormatPlaceholder": "请选择导出格式",
      "excel": "Excel",
      "csv": "CSV",
      "exportScope": "导出范围",
      "exportCurrentPage": "当前页数据",
      "exportAllData": "全部数据",
      "exportSuccess": "导出{format}文件成功！",
      "fetchWarehouseFailed": "获取仓库信息失败",
      "fetchConfigFailed": "获取车型配置失败"
    },
    "vehicleRegistration": {
      "title": "车辆登记管理",
      "orderNumber": "订单编号",
      "orderNumberPlaceholder": "请输入订单编号",
      "customerName": "客户姓名",
      "customerNamePlaceholder": "请输入客户姓名",
      "customerPhone": "客户手机号",
      "customerPhonePlaceholder": "请输入客户手机号",
      "registrationStatus": "登记状态",
      "registrationStatusAll": "全部状态",
      "registrationStatusPending": "待登记",
      "registrationStatusProcessing": "登记中",
      "registrationStatusSuccess": "登记成功",
      "registrationStatusFailed": "登记失败",
      "vin": "VIN",
      "vinPlaceholder": "请输入VIN",
      "insuranceStatus": "投保状态",
      "insuranceStatusAll": "全部状态",
      "insuranceStatusInsured": "已投保",
      "insuranceStatusNotInsured": "未投保",
      "pushTimeRange": "推送时间范围",
      "salesAdvisor": "销售顾问",
      "salesAdvisorAll": "全部销售顾问",
      "export": "导出",
      "loadingData": "数据加载中...",
      "serialNumber": "序号",
      "vehicleModel": "车型",
      "vehicleColor": "车身颜色",
      "insuranceCompany": "保险公司",
      "pushTime": "推送时间",
      "registrationFee": "登记费用",
      "operations": "操作",
      "pushRegistration": "推送登记",
      "viewDetails": "查看详情",
      "pushConfirmTitle": "确认推送登记",
      "pushConfirmContent": "您确定要推送此车辆进行登记吗？",
      "confirmPush": "确认推送",
      "retryPushTitle": "重新推送登记",
      "failureReason": "失败原因",
      "retryPushContent": "您确定要重新推送此车辆进行登记吗？",
      "confirmRetry": "确认重新推送",
      "orderDetailsTitle": "订单详情",
      "orderBasicInfo": "订单基本信息",
      "orderStatus": "订单状态",
      "createTime": "创建时间",
      "customerInfo": "购车人信息",
      "idType": "证件类型",
      "idNumber": "证件号码",
      "email": "邮箱",
      "address": "地址",
      "city": "城市",
      "state": "州/省",
      "postcode": "邮政编码",
      "vehicleInfo": "车辆信息",
      "engineNumber": "发动机号",
      "modelCode": "型号代码",
      "variant": "版本",
      "productionYear": "生产年份",
      "manufactureDate": "制造日期",
      "insuranceInfo": "保险信息",
      "policyNumber": "保单号",
      "insurancePeriod": "保险期限",
      "insuranceDate": "投保日期",
      "insuranceFee": "保险费用",
      "jpjInfo": "JPJ登记信息",
      "certificateNumber": "证书编号",
      "completionTime": "完成时间",
      "operator": "操作员",
      "retryPush": "重新推送",
      "feeDetails": "费用明细",
      "feeType": "费用类型",
      "feeAmount": "费用金额",
      "operationLogs": "操作日志",
      "operationTime": "操作时间",
      "operationType": "操作类型",
      "operationResult": "操作结果",
      "remark": "备注",
      "pushSuccess": "推送成功！",
      "pushFailed": "推送失败！",
      "retrySuccess": "重新推送成功！",
      "retryFailed": "重新推送失败！",
      "exportSuccess": "导出成功！",
      "exportFailed": "导出失败！"
    },
    "delivery": {
      "pageTitle": "交车管理",
      "searchTitle": "筛选查询",
      "deliveryNumber": "交车单号",
      "deliveryNumberPlaceholder": "请输入交车单号",
      "orderNumber": "订单编号",
      "orderNoPlaceholder": "请输入订单编号",
      "customerName": "购车人名称",
      "customerNamePlaceholder": "请输入购车人名称",
      "customerPhone": "购车人手机号",
      "customerMobilePlaceholder": "请输入手机号",
      "vin": "VIN",
      "vinPlaceholder": "请输入VIN",
      "orderStatus": "订单状态",
      "orderStatusPlaceholder": "请选择订单状态",
      "orderStatusPendingAllocation": "待分配",
      "orderStatusAllocating": "分配中",
      "orderStatusAllocated": "已分配",
      "orderStatusPendingDelivery": "待交车",
      "orderStatusDelivered": "已交车",
      "dealerStore": "门店",
      "dealerStorePlaceholder": "请选择门店",
      "salesConsultant": "销售顾问",
      "salesConsultantPlaceholder": "请输入销售顾问",
      "deliveryStatus": "交车状态",
      "deliveryStatusPlaceholder": "请选择交车状态",
      "statusPending": "待交车",
      "statusConfirming": "待确认",
      "statusCompleted": "已交车",
      "customerConfirmed": "客户是否确认",
      "customerConfirmedPlaceholder": "请选择客户是否确认",
      "confirmationType": "客户确认类型",
      "confirmationTypePlaceholder": "请选择客户确认类型",
      "confirmationTypeApp": "APP",
      "confirmationTypeOffline": "线下",
      "deliveryTime": "交车时间",
      "deliveryTimePlaceholder": "请选择交车时间",
      "customerConfirmTime": "客户确认时间",
      "customerConfirmTimePlaceholder": "请选择客户确认时间",
      "invoiceTime": "开票时间",
      "invoiceTimePlaceholder": "请选择开票时间",
      "listTitle": "交车单列表",
      "totalCount": "共 {count} 条",
      "submitConfirm": "提交确认",
      "deliveryConfirm": "交车确认",
      "submitConfirmSuccess": "提交确认成功！",
      "submitConfirmFailed": "提交确认失败！",
      "deliveryConfirmSuccess": "交车确认成功！",
      "deliveryConfirmFailed": "交车确认失败！",
      "fetchDataFailed": "获取交车单数据失败！",
      "printFeatureNotImplemented": "打印功能暂未实现！",
      "exportSuccess": "导出成功！",
      "exportFailed": "导出失败！",
      "orderStatusNormal": "正常",
      "orderStatusCancelled": "已取消",
      "signaturePhoto": "签字照片",
      "deliveryNotes": "交车备注"
    },
    "payment": {
      "title": "整车收款管理",
      "orderNumber": "订单编号",
      "buyerName": "购车人名称",
      "buyerPhone": "购车人手机号",
      "dealerStore": "订车门店",
      "salesConsultant": "销售顾问",
      "vin": "VIN",
      "model": "Model",
      "variant": "Variant",
      "color": "Color",
      "orderCreateTime": "订单创建时间",
      "orderStatus": "订单状态",
      "paymentStatus": "订单支付状态",
      "vehicleSalesPrice": "车辆销售价格",
      "insuranceAmount": "保险金额",
      "otrAmount": "OTR金额",
      "discountAmount": "订单优惠金额",
      "totalAmount": "订单总金额",
      "paidAmount": "订单已支付金额",
      "unpaidAmount": "订单未支付金额",
      "loanAmount": "贷款金额",
      "canInvoice": "是否可开票",
      "invoiceTime": "开票时间",
      "invoiceNumber": "发票号",
      "createTime": "创建时间",
      "updateTime": "更新时间",
      "paymentOperation": "收退款",
      "orderDetail": "订单详情",
      "paymentRecords": "收退款历史记录",
      "addPaymentRecord": "添加收退款信息",
      "enterOrderNumber": "请输入订单编号",
      "enterBuyerName": "请输入购车人名称",
      "enterBuyerPhone": "请输入购车人手机号",
      "selectOrderStatus": "请选择订单状态",
      "selectPaymentStatus": "请选择订单支付状态",
      "selectCanInvoice": "请选择是否可开票",
      "selectDateRange": "请选择时间范围",
      "orderStatusSubmitted": "已提交",
      "orderStatusCancelPending": "取消审核中",
      "orderStatusCancelApproved": "取消审核通过",
      "orderStatusCancelled": "已取消",
      "orderStatusConfirmed": "已确认",
      "orderStatusPendingReview": "待审核",
      "orderStatusReviewed": "已审核",
      "orderStatusPendingDelivery": "待交车",
      "orderStatusDelivered": "已交车",
      "paymentStatusPendingDeposit": "待支付定金",
      "paymentStatusDepositPaid": "已支付定金",
      "paymentStatusRefunding": "退款中",
      "paymentStatusRefunded": "退款完成",
      "paymentStatusPendingFinal": "待支付尾款",
      "paymentStatusFullyPaid": "已支付尾款",
      "businessType": "业务类型",
      "transactionNumber": "流水号",
      "channel": "渠道",
      "amount": "金额",
      "paymentType": "收款类型",
      "arrivalTime": "到账时间",
      "remark": "备注",
      "creator": "创建人",
      "dataSource": "数据来源",
      "payment": "收款",
      "refund": "退款",
      "channelAPP": "APP",
      "channelBankCard": "银行卡",
      "channelTransfer": "转账",
      "paymentTypeBookFee": "Book Fee",
      "paymentTypeLoan": "贷款",
      "paymentTypeFinal": "尾款",
      "dataSourceManual": "手动录入",
      "dataSourceApp": "APP推送",
      "paymentMethodFull": "全款",
      "paymentMethodLoan": "贷款",
      "transactionNumberRequired": "请输入流水号",
      "transactionNumberExists": "流水号已存在",
      "amountRequired": "请输入金额",
      "amountInvalid": "请输入有效金额",
      "arrivalTimeRequired": "请选择到账时间",
      "operationNotAllowed": "当前状态不允许此操作",
      "appDataCannotDelete": "APP数据不可删除",
      "confirmDelete": "确认删除该记录？",
      "deleteSuccess": "删除成功",
      "addSuccess": "添加成功",
      "orderInfo": "订单基础信息",
      "customerInfo": "客户详细信息",
      "personalDetails": "Personal Details",
      "ordererInfo": "下单人信息",
      "buyerInfo": "购车人信息",
      "ordererName": "下单人姓名",
      "ordererPhone": "下单人手机号",
      "buyerIdType": "身份证件类别",
      "buyerIdNumber": "身份证件号",
      "buyerEmail": "购车人邮箱",
      "buyerAddress": "购车人详细地址",
      "buyerState": "购车人所在州",
      "buyerCity": "购车人所在城市",
      "buyerPostcode": "购车人所在地邮编",
      "storeInfo": "门店与销售信息",
      "preferredOutletSalesAdvisor": "Preferred Outlet & Sales Advisor",
      "dealerRegion": "所在地区",
      "dealerCity": "所在城市",
      "salesConsultantPhone": "联系电话",
      "salesConsultantEmail": "邮箱地址",
      "vehicleInfo": "车辆详细信息",
      "options": "选装件",
      "warehouseName": "仓库",
      "productionDate": "生产日期",
      "priceInfo": "订单价格详细信息",
      "salesSubtotal": "销售小计(包含GASA)",
      "consumptionTax": "消费税",
      "salesTax": "销售税",
      "numberPlatesFee": "车牌费（Number Plates）",
      "optionsPrice": "选装包价格",
      "vehicleSalesSubtotal": "车辆销售价格小计",
      "otrAmountDetail": "OTR金额",
      "paymentRecordNumber": "收退款单号",
      "totalPayment": "总收款金额",
      "totalRefund": "总退款金额",
      "netPayment": "净收款金额",
      "recordCount": "记录总数",
      "enterTransactionNumber": "请输入流水号",
      "enterAmount": "请输入金额",
      "enterRemark": "请输入备注信息",
      "selectBusinessType": "请选择业务类型",
      "selectChannel": "请选择渠道",
      "selectPaymentType": "请选择收款类型",
      "selectArrivalTime": "请选择到账时间"
    },
    "factoryOrder": {
      "title": "厂端订单管理",
      "statisticsTitle": "订单概览统计",
      "searchTitle": "筛选条件",
      "actionTitle": "操作区域",
      "listTitle": "订单列表",
      "monthlyOrderCount": "本月订单数量",
      "dailyOrderCount": "今日订单数量",
      "monthlyGrowthRate": "较上月增长",
      "dailyGrowthRate": "较昨日增长",
      "topDealer": "订单最多门店",
      "topVehicle": "最热销车型",
      "pendingDelivery": "待交车订单",
      "priorityProcessing": "需优先处理",
      "refreshStatistics": "刷新数据",
      "lastUpdate": "最后更新时间",
      "dealerName": "门店",
      "dealerNamePlaceholder": "请选择门店",
      "model": "车型",
      "modelPlaceholder": "请选择车型",
      "variant": "配置",
      "variantPlaceholder": "请选择配置",
      "orderStatus": "订单状态",
      "orderStatusPlaceholder": "请选择订单状态",
      "paymentStatus": "支付状态",
      "paymentStatusPlaceholder": "请选择支付状态",
      "orderDate": "下单日期",
      "orderDateStart": "开始日期",
      "orderDateEnd": "结束日期",
      "orderNumber": "订单号",
      "orderNumberPlaceholder": "请输入订单号",
      "serialNumber": "序号",
      "dealerNameColumn": "门店",
      "creationTime": "订单创建时间",
      "ordererName": "下单人",
      "ordererPhone": "下单人手机号",
      "buyerName": "购车人",
      "buyerPhone": "购车人手机号",
      "buyerCategory": "购车人类别",
      "vin": "VIN",
      "paymentMethod": "付款方式",
      "loanApprovalStatus": "贷款审核状态",
      "orderApprovalStatus": "订单审核状态",
      "insuranceStatus": "投保状态",
      "jpjRegistrationStatus": "JPJ车辆注册状态",
      "operations": "操作",
      "details": "详情",
      "exportExcel": "导出Excel",
      "exportSuccess": "导出成功",
      "exportFailed": "导出失败",
      "refreshSuccess": "数据已刷新",
      "refreshFailed": "数据刷新失败",
      "detailTitle": "厂端订单详情",
      "customerInfo": "客户信息",
      "personalDetails": "Personal Details",
      "storeInfo": "购车门店信息",
      "preferredOutletSalesAdvisor": "Preferred Outlet & Sales Advisor",
      "purchaseDetails": "购车信息",
      "purchaseDetailsTitle": "Purchase Details",
      "vehicleInfoTab": "车辆信息",
      "invoiceInfoTab": "开票信息",
      "benefitsInfoTab": "服务&权益信息",
      "paymentInfoTab": "付款信息",
      "insuranceInfoTab": "保险信息",
      "otrFeesTab": "OTR费用信息",
      "changeRecordsTab": "订单变更记录",
      "ordererNameField": "下单人",
      "ordererPhoneField": "下单人手机号",
      "buyerNameField": "购车人",
      "buyerPhoneField": "购车人手机号",
      "buyerIdType": "购车人身份证件类别",
      "buyerIdNumber": "购车人身份证件号",
      "buyerEmail": "购车人邮箱",
      "buyerAddress": "购车人地址",
      "buyerState": "购车人所在州",
      "buyerCity": "购车人所在城市",
      "buyerPostcode": "购车人所在地邮编",
      "dealerRegion": "所在地区",
      "dealerCity": "所在城市",
      "salesConsultant": "销售顾问",
      "salesSubtotal": "销售小计(包含GASA、消费税、销售税)",
      "numberPlatesFee": "车牌费（Number Plates）",
      "accessoryInfo": "选配件信息",
      "accessoryCategory": "类别",
      "accessoryName": "配件名称",
      "accessoryUnitPrice": "配件单价",
      "accessoryQuantity": "数量",
      "accessoryTotalPrice": "总价",
      "accessoriesTotalAmount": "选配件总金额",
      "invoiceType": "开票类型",
      "invoiceName": "开票名称",
      "invoicePhone": "开票电话",
      "invoiceAddress": "开票地址",
      "benefitsInfo": "服务&权益信息",
      "benefitCode": "权益代码",
      "benefitName": "权益名称",
      "benefitMode": "权益模式",
      "discountPrice": "权益优惠价格",
      "effectiveDate": "权益生效日期",
      "expirationDate": "权益终止日期",
      "benefitsTotalAmount": "权益优惠总金额",
      "loanApprovalStatusField": "贷款资质审核状态",
      "depositAmount": "定金金额",
      "loanAmount": "贷款金额",
      "balanceAmount": "尾款金额",
      "insuranceInfo": "保单信息",
      "policyNumber": "保单号",
      "insuranceType": "保险类型",
      "insuranceCompany": "保险公司",
      "effectiveDateField": "生效日期",
      "expirationDateField": "到期日期",
      "insurancePrice": "保险价格",
      "insurancesTotalAmount": "保险总金额",
      "insuranceNotes": "备注",
      "otrFeesInfo": "On The Road登记费用",
      "ticketNumber": "票据单号",
      "feeItem": "收费项目",
      "feePrice": "收费价格",
      "otrFeesTotalAmount": "OTR费用总金额",
      "changeRecordsInfo": "Order Change Records",
      "originalContent": "原始内容",
      "changedContent": "变更后内容",
      "operator": "操作人",
      "operationTime": "操作时间",
      "vehicleInvoicePrice": "整车开票价",
      "remainingReceivable": "剩余应收",
      "backToList": "返回",
      "statusPending": "待确认",
      "statusConfirmed": "已确认",
      "statusInProduction": "生产中",
      "statusCompleted": "已完成",
      "statusCancelled": "已取消",
      "statusPendingDelivery": "待交车",
      "paymentStatusPendingDeposit": "待付定金",
      "paymentStatusDepositPaid": "已付定金",
      "paymentStatusPendingBalance": "待付尾款",
      "paymentStatusFullyPaid": "已全款",
      "loanStatusNoNeed": "无需审批",
      "loanStatusPending": "审批中",
      "loanStatusApproved": "已通过",
      "loanStatusRejected": "已拒绝",
      "approvalStatusPending": "待审批",
      "approvalStatusApproved": "已审批",
      "approvalStatusRejected": "已拒绝",
      "insuranceStatusPending": "待投保",
      "insuranceStatusCompleted": "已投保",
      "jpjStatusPending": "待注册",
      "jpjStatusCompleted": "已注册",
      "categoryPersonal": "个人用户",
      "categoryBusiness": "企业用户",
      "invoiceTypePersonal": "个人",
      "invoiceTypeBusiness": "公司"
    },
    "workAssignment": {
      "title": "派工管理",
      "breadcrumb": "售后服务 / 派工管理",
      "searchForm": "搜索条件",
      "columnConfig": "列设置",
      "exportData": "导出数据",
      "workOrderId": "工单编号",
      "priority": "优先级",
      "workOrderType": "工单类型",
      "customerName": "送修人姓名",
      "licensePlate": "车牌号",
      "serviceAdvisor": "服务顾问",
      "assignmentStatus": "分配状态",
      "assignedTechnician": "分配技师",
      "creationTime": "创建时间",
      "estimatedWorkHours": "预计工时",
      "waitingDuration": "等待时长",
      "totalAmount": "工单金额",
      "priorities": {
        "urgent": "紧急",
        "normal": "普通"
      },
      "workOrderTypes": {
        "repair": "维修",
        "maintenance": "保养",
        "insurance": "保险"
      },
      "assignmentStatuses": {
        "pending_assign": "待分配",
        "assigned": "已分配"
      },
      "assignTechnician": "分配技师",
      "reassignTechnician": "重新分配",
      "workOrderDetail": "工单详情",
      "technicianSelection": "技师选择",
      "assignment": "分配",
      "reassignment": "重新分配",
      "assignmentTime": "分配时间",
      "estimatedStartTime": "预计开工时间",
      "estimatedFinishTime": "预计完工时间",
      "assignmentNotes": "分配备注",
      "assignmentReason": "分配原因",
      "reassignmentReason": "重新分配原因",
      "workOrderInfo": "工单信息",
      "customerInfo": "客户信息",
      "vehicleInfo": "车辆信息",
      "serviceItems": "服务项目",
      "technicianInfo": "技师信息",
      "technicianName": "技师姓名",
      "department": "所属部门",
      "position": "职位等级",
      "specialization": "专业方向",
      "skillLevel": "技能等级",
      "currentStatus": "当前状态",
      "workLoadStatus": "负荷状态",
      "currentWorkOrders": "当前工单数",
      "totalEstimatedHours": "预计工时总量",
      "availableHours": "可用工时",
      "averageEfficiency": "平均效率",
      "qualityScore": "质量评分",
      "customerSatisfaction": "客户满意度",
      "technicianStatuses": {
        "available": "空闲",
        "busy": "忙碌",
        "on_leave": "请假",
        "training": "培训",
        "resigned": "离职"
      },
      "workloadStatuses": {
        "idle": "空闲",
        "moderate": "适中",
        "busy": "繁忙",
        "overloaded": "超负荷"
      },
      "appointmentTime": "预约时间",
      "totalWorkOrders": "总工单数",
      "pendingAssignment": "待派工",
      "assignedOrders": "已分配",
      "inProgressOrders": "进行中",
      "completedOrders": "已完成",
      "cancelledOrders": "已取消",
      "totalTechnicians": "总技师数",
      "availableTechnicians": "可用技师数",
      "busyTechnicians": "繁忙技师数",
      "overloadedTechnicians": "超负荷技师数",
      "averageAssignmentTime": "平均分配时间",
      "averageWaitingTime": "平均等待时间",
      "onTimeCompletionRate": "准时完工率",
      "reassignmentRate": "重新分配率",
      "utilizationRate": "人员利用率",
      "workOrderStatuses": {
        "draft": "草稿",
        "pending_confirm": "待确认",
        "confirmed": "已确认",
        "pending_assign": "待分配",
        "pending_start": "待开工",
        "in_progress": "进行中",
        "pending_qc": "待质检",
        "pending_settle": "待结算",
        "completed": "已完成",
        "cancelled": "已取消"
      },
      "confirmAssign": "确定分配给 {technicianName} 吗？",
      "confirmReassign": "确定重新分配给 {technicianName} 吗？",
      "assignmentSuccess": "派工成功",
      "reassignmentSuccess": "重新分配成功",
      "pleaseSelectTechnician": "请选择技师",
      "estimatedTimeRequired": "预计时间必填",
      "assignmentNotesRequired": "分配备注必填",
      "reassignmentReasonRequired": "重新分配原因必填",
      "technicianSchedule": "技师排程",
      "workload": "工作负荷",
      "scheduleConflict": "时间冲突",
      "noAvailableSlot": "无可用时间段"
    },
    "quota": {
      "quotaManagement": "配额管理",
      "pageTitle": "预约限量管理",
      "storeName": "门店名称",
      "storeCode": "门店编号",
      "permissionTip": "您当前查看的是默认门店的预约限量配置。如需配置其他门店，请联系管理员。",
      "configuredListTitle": "已配置限量列表",
      "addNewQuota": "新增预约限量",
      "editQuotaTitle": "编辑预约限量",
      "addNewQuotaTitle": "新增预约限量",
      "emptyState": "暂未配置预约限量。点击"新增预约限量"进行配置。",
      "unsavedChangesWarning": "您有未保存的更改，确定要关闭吗？",
      "dateMustBeFutureOrToday": "配置日期不能早于今天",
      "selectDateFirst": "请先选择日期",
      "timeSlotExceedsOperatingHours": "时段设置超过营业时间范围（8:00-18:00）",
      "table": {
        "configDate": "配置日期",
        "timeSlotCount": "时段数量",
        "totalQuota": "总限量",
        "bookedQuantity": "已预约数量",
        "lastUpdateTime": "最后更新时间",
        "operations": "操作",
        "edit": "编辑",
        "expired": "已过期"
      },
      "modal": {
        "selectDateTitle": "选择日期",
        "dateLabel": "配置日期",
        "datePlaceholder": "请选择配置日期",
        "existingConfig": "已有配置",
        "dateTip": "配置日期不能早于今天",
        "timeSlotConfigTitle": "时段配置",
        "addTimeSlot": "添加时段",
        "noTimeSlots": "尚未添加时段",
        "clickAddPrompt": "点击"添加时段"开始配置",
        "timeSlot": "时段",
        "startTime": "开始时间",
        "startTimePlaceholder": "请选择开始时间",
        "endTime": "结束时间",
        "endTimePlaceholder": "请选择结束时间",
        "quota": "限量",
        "quotaPlaceholder": "请输入限量",
        "configDescriptionTitle": "配置说明",
        "configDescription": {
          "item1": "每个时段的限量表示该时段最多可预约的车辆数量。",
          "item2": "时段之间不能重叠，结束时间必须晚于开始时间。",
          "item3": "建议根据门店实际接待能力设置合理的限量。",
          "item4": "所有时段限量之和即为当天的总限量。"
        }
      },
      "validation": {
        "dateRequired": "请选择配置日期",
        "atLeastOneTimeSlot": "至少需要添加一个时段",
        "timeRequired": "请设置时段的开始和结束时间",
        "startBeforeEnd": "开始时间必须早于结束时间",
        "quotaPositive": "限量必须大于0",
        "timeSlotOverlap": "时段 {slot1} 与 {slot2} 时间重叠，请调整"
      },
      "summary": {
        "selectDate": "请选择日期",
        "addTimeSlot": "请添加时段",
        "timeSlotsUnit": "个时段",
        "totalQuota": "总限量"
      }
    },
    "salesOrder": {
      "title": "销售订单列表",
      "breadcrumb": "客户订单管理 / 销售订单列表",
      "searchForm": "搜索条件",
      "operationButtons": "操作",
      "orderList": "订单列表",
      "buyerName": "购车人",
      "buyerNamePlaceholder": "请输入购车人姓名",
      "buyerPhone": "购车人手机号",
      "buyerPhonePlaceholder": "请输入购车人手机号",
      "buyerType": "购车人类别",
      "buyerTypePlaceholder": "请选择购车人类别",
      "model": "车型",
      "modelPlaceholder": "请选择车型",
      "orderNumber": "订单号",
      "orderNumberPlaceholder": "请输入订单号",
      "orderStatus": "订单状态",
      "orderStatusPlaceholder": "请选择订单状态",
      "approvalStatus": "订单审核状态",
      "approvalStatusPlaceholder": "请选择订单审核状态",
      "paymentStatus": "订单支付状态",
      "paymentStatusPlaceholder": "请选择订单支付状态",
      "createTime": "订单创建时间",
      "createTimeRange": "订单创建时间范围",
      "insuranceStatus": "投保状态",
      "insuranceStatusPlaceholder": "请选择投保状态",
      "loanApprovalStatus": "贷款审核状态",
      "loanApprovalStatusPlaceholder": "请选择贷款审核状态",
      "jpjRegistrationStatus": "JPJ车辆注册状态",
      "jpjRegistrationStatusPlaceholder": "请选择JPJ车辆注册状态",
      "index": "序号",
      "ordererName": "下单人",
      "ordererPhone": "下单人手机号",
      "variant": "Variant",
      "color": "Color",
      "vin": "VIN",
      "paymentMethod": "支付方式",
      "totalAmount": "订单总金额",
      "buyerTypes": {
        "individual": "个人",
        "company": "公司"
      },
      "models": {
        "AXIA": "AXIA",
        "BEZZA": "BEZZA", 
        "MYVI": "MYVI"
      },
      "orderStatuses": {
        "submitted": "已提交",
        "confirmed": "已确认",
        "cancel_pending": "取消审核中",
        "cancel_approved": "取消审核通过",
        "cancelled": "已取消",
        "pending_delivery": "待交车",
        "delivered": "已交车"
      },
      "approvalStatuses": {
        "pending_approval": "待审批",
        "approved": "已审批"
      },
      "paymentStatuses": {
        "pending_deposit": "待支付定金",
        "deposit_paid": "已支付定金",
        "pending_balance": "待支付尾款",
        "balance_paid": "已支付尾款",
        "refunding": "退款中",
        "refunded": "退款完成"
      },
      "insuranceStatuses": {
        "not_insured": "未投保",
        "insuring": "投保中",
        "insured": "投保完成"
      },
      "loanApprovalStatuses": {
        "pending_review": "待审核",
        "approved": "审核通过",
        "rejected": "审核驳回"
      },
      "jpjRegistrationStatuses": {
        "pending_registration": "待登记",
        "registering": "登记中",
        "registered": "登记成功",
        "registration_failed": "登记失败"
      },
      "paymentMethods": {
        "full_payment": "全款",
        "installment": "分期"
      },
      "viewDetail": "详情",
      "editOrder": "编辑",
      "returnToList": "返回列表",
      "exportSuccess": "导出成功",
      "exportFailed": "导出失败",
      "noDataToExport": "没有数据可导出",
      "confirmExportTitle": "确认导出",
      "confirmExportMessage": "确定要导出当前筛选条件下的所有订单数据吗？",
      "orderDetail": "销售订单详情",
      "customerInfo": "客户信息",
      "storeInfo": "购车门店信息",
      "purchaseInfo": "购车信息",
      "vehicleInfoTab": "车辆信息",
      "invoicingInfoTab": "开票信息", 
      "rightsInfoTab": "服务&权益信息",
      "paymentInfoTab": "支付信息",
      "insuranceInfoTab": "保险信息",
      "otrFeesTab": "OTR费用信息",
      "changeRecords": "订单变更记录",
      "buyerIdType": "购车人身份证件类别",
      "buyerIdNumber": "购车人身份证件号",
      "buyerEmail": "购车人邮箱",
      "buyerAddress": "购车人地址",
      "buyerState": "购车人所在州",
      "buyerCity": "购车人所在城市",
      "buyerPostcode": "购车人所在地邮编",
      "storeRegion": "所在地区",
      "storeCity": "所在城市",
      "storeName": "购车门店",
      "salesConsultantName": "销售顾问",
      "salesSubtotal": "销售小计(包含GASA、消费税、销售税)",
      "numberPlatesFee": "车牌费",
      "vinCode": "VIN码",
      "accessoryInfo": "选配件信息",
      "accessoryCategory": "类别",
      "accessoryName": "配件名称",
      "unitPrice": "配件单价",
      "quantity": "数量",
      "totalPrice": "总价",
      "accessoriesTotalAmount": "选配件总金额",
      "invoicingType": "开票类型",
      "invoicingName": "开票名称",
      "invoicingPhone": "开票电话",
      "invoicingAddress": "开票地址",
      "rightsInfo": "服务&权益信息",
      "rightCode": "权益代码",
      "rightName": "权益名称",
      "rightMode": "权益模式",
      "discountAmount": "权益优惠价格",
      "effectiveDate": "权益生效日期",
      "expiryDate": "权益终止日期",
      "rightsDiscountAmount": "权益优惠总金额",
      "loanApprovalStatusField": "贷款资质审核状态",
      "depositAmount": "定金金额",
      "loanAmount": "贷款金额",
      "balanceAmount": "尾款金额",
      "insuranceInfo": "保单信息",
      "policyNumber": "保单号",
      "insuranceType": "保险类型",
      "insuranceCompany": "保险公司",
      "insurancePrice": "保险价格",
      "insuranceTotalAmount": "保险总金额",
      "insuranceNotes": "备注",
      "otrFeesInfo": "On The Road登记费用",
      "ticketNumber": "票据单号",
      "feeItem": "收费项目",
      "feePrice": "收费价格",
      "otrFeesTotalAmount": "OTR费用总金额",
      "changeRecordIndex": "序号",
      "originalContent": "原始内容",
      "changedContent": "变更后内容",
      "operator": "操作人",
      "operationTime": "操作时间",
      "totalInvoiceAmount": "整车开票价",
      "remainingAmount": "剩余应收",
      "editOrderTitle": "销售订单编辑",
      "personalDetails": "客户信息 - Personal Details",
      "preferredOutlet": "购车门店信息 - Preferred Outlet & Sales Advisor",
      "purchaseDetails": "购车信息 - Purchase Details",
      "addRights": "新增权益",
      "selectRights": "选择权益",
      "rightCodeSearch": "权益代码搜索",
      "rightNameSearch": "权益名称搜索",
      "selectAll": "全选",
      "addSelected": "添加",
      "deleteRight": "删除权益",
      "confirmDeleteRight": "确定要删除此权益吗？",
      "pushToInsurance": "推送至保险系统",
      "insurancePushed": "已推送",
      "confirmPushInsurance": "确定要推送至保险系统吗？",
      "pushInsuranceSuccess": "推送保险系统成功",
      "pushInsuranceFailed": "推送保险系统失败",
      "submitDelivery": "提交交车",
      "confirmSubmitDelivery": "确定要提交交车申请吗？",
      "submitDeliverySuccess": "提交交车成功",
      "submitDeliveryFailed": "提交交车失败",
      "deliveryConditionsNotMet": "交车条件不满足",
      "colorChangeNotice": "您已更改车辆颜色，是否提交审核？",
      "colorChangeSubmitted": "颜色变更已提交审核",
      "roadTax": "路税",
      "registrationFee": "注册/过户费", 
      "ownershipClaimFee": "所有权索赔费",
      "interchangeFee": "咨询费",
      "otrFeeTotal": "登记费用总金额",
      "loanTerm": "贷款期数（月）",
      "loanTermPlaceholder": "请输入贷款期数",
      "loanAmountPlaceholder": "请输入贷款金额"
    }
}