// 潜客管理相关的选项配置

// 地区选项
export const REGION_OPTIONS = [
  { code: 'penang', name: '槟城', nameEn: 'Penang' },
  { code: 'kl', name: '吉隆坡', nameEn: 'Kuala Lumpur' },
  { code: 'johor', name: '柔佛', nameEn: '<PERSON><PERSON>' },
  { code: 'selangor', name: '雪兰莪', nameEn: 'Selangor' },
  { code: 'sabah', name: '沙巴', nameEn: 'Sabah' },
  { code: 'sarawak', name: '砂拉越', nameEn: 'Sarawak' }
];

// 车型选项
export const VEHICLE_MODEL_OPTIONS = [
  { code: 'myvi', name: 'Myvi' },
  { code: 'axia', name: 'Axia' },
  { code: 'bezza', name: '<PERSON><PERSON>' },
  { code: 'aruz', name: 'Aruz' },
  { code: 'alza', name: '<PERSON><PERSON>' },
  { code: 'ativa', name: 'Ativa' }
];

// 车款配置选项
export const VEHICLE_VARIANT_OPTIONS = [
  { code: '1.3L_standard', name: '1.3L Standard' },
  { code: '1.3L_premium', name: '1.3L Premium' },
  { code: '1.0L_entry', name: '1.0L Entry' },
  { code: '1.5L_advance', name: '1.5L Advance' },
  { code: '1.0L_x', name: '1.0L X' },
  { code: '1.5L_av', name: '1.5L AV' }
];

// 颜色选项
export const VEHICLE_COLOR_OPTIONS = [
  { code: 'white', name: '白色', nameEn: 'White' },
  { code: 'black', name: '黑色', nameEn: 'Black' },
  { code: 'silver', name: '银色', nameEn: 'Silver' },
  { code: 'red', name: '红色', nameEn: 'Red' },
  { code: 'blue', name: '蓝色', nameEn: 'Blue' },
  { code: 'grey', name: '灰色', nameEn: 'Grey' }
];

// 证件类型选项
export const ID_TYPE_OPTIONS = [
  { code: 'idCard', name: '身份证', nameEn: 'ID Card' },
  { code: 'passport', name: '护照', nameEn: 'Passport' },
  { code: 'residencePermit', name: '居住证', nameEn: 'Residence Permit' }
];

// 跟进方式选项
export const FOLLOW_UP_METHOD_OPTIONS = [
  { code: 'phone', name: '电话', nameEn: 'Phone' },
  { code: 'wechat', name: '微信', nameEn: 'WeChat' },
  { code: 'visit_store', name: '到店面谈', nameEn: 'Store Visit' },
  { code: 'home_visit', name: '上门拜访', nameEn: 'Home Visit' },
  { code: 'email', name: '邮件', nameEn: 'Email' }
];

// 无意向原因选项
export const NO_INTENTION_REASON_OPTIONS = [
  { code: 'price_factor', name: '价格因素', nameEn: 'Price Factor' },
  { code: 'other_brand', name: '选择其他品牌', nameEn: 'Choose Other Brand' },
  { code: 'postpone_purchase', name: '暂时不购买', nameEn: 'Postpone Purchase' },
  { code: 'requirement_change', name: '需求变化', nameEn: 'Requirement Change' },
  { code: 'financial_issue', name: '资金问题', nameEn: 'Financial Issue' },
  { code: 'other_reason', name: '其他原因', nameEn: 'Other Reason' }
];

// 销售顾问选项 (示例数据，实际应从API获取)
export const SALES_ADVISOR_OPTIONS = [
  { id: 'SA001', name: '张顾问' },
  { id: 'SA002', name: '李顾问' },
  { id: 'SA003', name: '王顾问' },
  { id: 'SA004', name: '赵顾问' },
  { id: 'SA005', name: '刘顾问' },
  { id: 'SA006', name: '陈顾问' },
  { id: 'SA007', name: '杨顾问' },
  { id: 'SA008', name: '黄顾问' },
  { id: 'SA009', name: '周顾问' },
  { id: 'SA010', name: '吴顾问' }
];