import type { App } from 'vue';
import { createI18n } from 'vue-i18n';

// 导入 Element Plus UI 组件库的语言包
import enLocale from 'element-plus/dist/locale/en.mjs'; // 英文
import zhLocale from 'element-plus/dist/locale/zh-cn.mjs'; // 简体中文

// 导入模块化的语言包加载器
import { loadLocaleMessages } from '@/locales/loader';

// 获取浏览器默认语言作为初始语言
const getBrowserLanguage = () => {
  const lang = navigator.language.toLowerCase();
  if (lang.includes('zh')) return 'zh'; // 如果浏览器语言包含'zh'，则设为中文
  // if (lang.includes('ja')) return 'ja'; // 如果浏览器语言包含'ja'，则设为日文
  return 'en'; // 默认使用英文
};

// 创建 i18n 实例
const i18n = createI18n({
  legacy: false,
  locale: localStorage.getItem('lang') || getBrowserLanguage(),
  fallbackLocale: 'en',
  messages: {
    zh: { ...zhLocale }, // 初始只加载 Element Plus 的语言包
    en: { ...enLocale },
  },
  globalInjection: true,
  silentTranslationWarn: true, // 静默翻译警告，避免开发时过多提示
  silentFallbackWarn: true, // 静默回退警告
});

// 异步加载应用的语言包
export async function loadAppMessages() {
  try {
    console.log('开始加载应用语言包...');

    // 并行加载中文和英文语言包
    const [zhMessages, enMessages] = await Promise.all([
      loadLocaleMessages('zh'),
      loadLocaleMessages('en'),
    ]);

    console.log('语言包加载成功:', {
      zhModules: Object.keys(zhMessages).length,
      enModules: Object.keys(enMessages).length
    });

    // 合并语言包
    i18n.global.mergeLocaleMessage('zh', zhMessages);
    i18n.global.mergeLocaleMessage('en', enMessages);

    console.log('应用语言包加载并合并成功');
  } catch (error) {
    console.error('加载应用语言包失败:', error);
    // 即使加载失败，也不要阻止应用启动
    // 可以添加用户提示或降级处理
  }
}

// 语言切换工具函数
export async function switchLanguage(locale: 'zh' | 'en') {
  try {
    // 设置 i18n 语言
    i18n.global.locale.value = locale;

    // 保存到 localStorage
    localStorage.setItem('lang', locale);

    // 更新 HTML 的 lang 属性
    document.documentElement.lang = locale;

    console.log(`语言已切换为: ${locale}`);

    // 重新加载菜单数据
    await reloadMenuData();
  } catch (error) {
    console.error('切换语言时重新加载菜单数据失败:', error);
    // 即使重新加载菜单失败，语言切换仍然应该生效
  }
}

// 重新加载菜单数据
async function reloadMenuData() {
  try {
    // 动态导入 useAuthStore 以避免循环依赖
    const { useAuthStore } = await import('@/stores/auth');
    const authStore = useAuthStore();

    // 如果用户已登录，重新获取用户信息（包含菜单数据）
    if (authStore.isLoggedIn) {
      console.log('重新加载菜单数据...');
      await authStore.getUserInfo();
      console.log('菜单数据重新加载完成');
    }
  } catch (error) {
    console.error('重新加载菜单数据失败:', error);
    throw error;
  }
}

// 获取当前语言
export function getCurrentLanguage(): string {
  return i18n.global.locale.value;
}

// 获取语言显示名称
export function getLanguageDisplayName(locale: string): string {
  switch (locale) {
    case 'zh':
      return '简体中文';
    case 'en':
      return 'English';
    default:
      return locale;
  }
}

// 获取可用语言列表
export function getAvailableLanguages() {
  return [
    { code: 'zh', name: '简体中文' },
    { code: 'en', name: 'English' }
  ];
}

// 导出函数，用于在 main.ts 中将 i18n 实例注册到 Vue 应用中
export function setupI18n(app: App) {
  app.use(i18n);
}

// 导出全局 i18n 实例，方便在 Vue 组件外（如 Axios 拦截器）使用国际化功能
export const i18nGlobal = i18n.global;

// 导出 i18n 实例本身，供其他地方使用
export default i18n;
