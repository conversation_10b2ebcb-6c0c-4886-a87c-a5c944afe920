// 主机厂端采购管理状态管理

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import {
  getOemDashboard,
  getOemOrderList,
  getOemOrderDetail,
  approveOemOrder,
  shipOemOrder,
  batchApproveOrders,
  batchShipOrders,
  checkInventory,
  getCarriers,
  getDealers,
  getOrderStatistics,
  updateOrderPriority,
  addOrderNote,
  getOrderNotes
} from '@/api/modules/parts/purchase-oem';
import type {
  OemDashboard,
  OemPurchaseOrder,
  OemOrderDetail,
  CarrierInfo,
  InventoryCheckResult,
  OrderStatistics,
  OemOrderListParams,
  OemOrderStatus,
  ApprovalForm,
  ShipmentCreateForm
} from '@/types/parts/purchase-oem';
import { ElMessage } from 'element-plus';

export const usePurchaseOemStore = defineStore('purchase-oem', () => {
  // 状态数据
  const dashboardData = ref<OemDashboard | null>(null);
  const orderList = ref<OemPurchaseOrder[]>([]);
  const currentOrderDetail = ref<OemOrderDetail | null>(null);
  const inventoryResults = ref<InventoryCheckResult[]>([]);
  const carrierList = ref<CarrierInfo[]>([]);
  const dealerList = ref<any[]>([]);
  const orderStatistics = ref<OrderStatistics | null>(null);
  const orderNotes = ref<any[]>([]);
  const selectedOrders = ref<number[]>([]);

  // 分页信息
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });

  // 加载状态
  const loading = ref({
    dashboard: false,
    list: false,
    detail: false,
    approve: false,
    ship: false,
    inventory: false,
    statistics: false,
    batchAction: false
  });

  // 筛选条件
  const filters = ref({
    query: '',
    status: [] as OemOrderStatus[],
    dealerId: undefined as number | undefined,
    priority: undefined as string | undefined,
    dateRange: [] as string[]
  });

  // Getters
  const statusCounts = computed(() => {
    if (!dashboardData.value) return {};
    return {
      pendingApproval: dashboardData.value.pendingApprovalCount,
      approvedToday: dashboardData.value.approvedTodayCount,
      pendingShipment: dashboardData.value.pendingShipmentCount
    };
  });

  const filteredOrders = computed(() => {
    let filtered = [...orderList.value];
    
    if (filters.value.query) {
      const query = filters.value.query.toLowerCase();
      filtered = filtered.filter(order => 
        order.orderNo.toLowerCase().includes(query) ||
        order.dealerName.toLowerCase().includes(query)
      );
    }
    
    if (filters.value.status.length > 0) {
      filtered = filtered.filter(order => 
        filters.value.status.includes(order.status)
      );
    }

    if (filters.value.dealerId) {
      filtered = filtered.filter(order => 
        order.dealerId === filters.value.dealerId
      );
    }

    if (filters.value.priority) {
      filtered = filtered.filter(order => 
        order.priority === filters.value.priority
      );
    }
    
    return filtered;
  });

  const pendingApprovalOrders = computed(() => 
    orderList.value.filter(order => order.status === 'PENDING_APPROVAL')
  );

  const pendingShipmentOrders = computed(() => 
    orderList.value.filter(order => order.status === 'PENDING_SHIPMENT')
  );

  // Actions

  /**
   * 获取仪表盘数据
   */
  const fetchDashboard = async () => {
    loading.value.dashboard = true;
    try {
      const response = await getOemDashboard();
      dashboardData.value = response.data;
    } catch (error) {
      console.error('Failed to fetch dashboard:', error);
      ElMessage.error('获取仪表盘数据失败');
    } finally {
      loading.value.dashboard = false;
    }
  };

  /**
   * 获取订单列表
   */
  const fetchOrderList = async (params: OemOrderListParams) => {
    loading.value.list = true;
    try {
      const response = await getOemOrderList(params);
      orderList.value = response.data.data;
      pagination.value = {
        page: response.data.pagination.currentPage,
        pageSize: response.data.pagination.pageSize,
        total: response.data.pagination.totalCount,
        totalPages: response.data.pagination.totalPages
      };
    } catch (error) {
      console.error('Failed to fetch order list:', error);
      ElMessage.error('获取订单列表失败');
    } finally {
      loading.value.list = false;
    }
  };

  /**
   * 获取订单详情
   */
  const fetchOrderDetail = async (orderId: number) => {
    loading.value.detail = true;
    try {
      const response = await getOemOrderDetail({ orderId });
      currentOrderDetail.value = response.data;
    } catch (error) {
      console.error('Failed to fetch order detail:', error);
      ElMessage.error('获取订单详情失败');
    } finally {
      loading.value.detail = false;
    }
  };

  /**
   * 审核订单
   */
  const approveOrder = async (approvalData: ApprovalForm) => {
    loading.value.approve = true;
    try {
      await approveOemOrder(approvalData);
      ElMessage.success(approvalData.isApproved ? '订单审核通过' : '订单已驳回');
      
      // 更新本地订单状态
      const order = orderList.value.find(o => o.orderId === approvalData.orderId);
      if (order) {
        order.status = approvalData.isApproved ? 'APPROVED' : 'REJECTED';
        order.auditorId = approvalData.auditorId;
        order.auditedAt = new Date().toISOString();
        order.auditRemarks = approvalData.remarks;
      }
    } catch (error) {
      console.error('Failed to approve order:', error);
      ElMessage.error('审核订单失败');
      throw error;
    } finally {
      loading.value.approve = false;
    }
  };

  /**
   * 执行发货
   */
  const shipOrder = async (shipmentData: ShipmentCreateForm) => {
    loading.value.ship = true;
    try {
      const params = {
        orderId: shipmentData.purchaseOrderId,
        carrier: shipmentData.carrier,
        trackingNumber: shipmentData.trackingNumber,
        remarks: shipmentData.remarks,
        items: shipmentData.items.map(item => ({
          partId: item.partId,
          shippedQuantity: item.shippedQuantity
        }))
      };
      
      const response = await shipOemOrder(params);
      ElMessage.success('发货成功');
      
      // 更新本地订单状态
      const order = orderList.value.find(o => o.orderId === shipmentData.purchaseOrderId);
      if (order) {
        // 检查是否全部发货
        const totalShipped = shipmentData.items.reduce((sum, item) => sum + item.shippedQuantity, 0);
        const totalOrdered = currentOrderDetail.value?.items.reduce((sum, item) => sum + item.orderQuantity, 0) || 0;
        
        order.status = totalShipped >= totalOrdered ? 'SHIPPED_ALL' : 'PARTIALLY_SHIPPED';
      }
      
      return response.data;
    } catch (error) {
      console.error('Failed to ship order:', error);
      ElMessage.error('发货失败');
      throw error;
    } finally {
      loading.value.ship = false;
    }
  };

  /**
   * 批量审核订单
   */
  const batchApprove = async (orderIds: number[], isApproved: boolean, remarks?: string) => {
    loading.value.batchAction = true;
    try {
      const params = {
        orderIds,
        isApproved,
        remarks,
        auditorId: 9001 // 从用户信息获取
      };
      
      const response = await batchApproveOrders(params);
      ElMessage.success(`批量${isApproved ? '通过' : '驳回'}完成：成功 ${response.data.successCount} 个，失败 ${response.data.failureCount} 个`);
      
      // 更新本地订单状态
      orderIds.forEach(orderId => {
        const order = orderList.value.find(o => o.orderId === orderId);
        if (order) {
          order.status = isApproved ? 'APPROVED' : 'REJECTED';
          order.auditorId = 9001;
          order.auditedAt = new Date().toISOString();
          order.auditRemarks = remarks;
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Failed to batch approve:', error);
      ElMessage.error('批量审核失败');
      throw error;
    } finally {
      loading.value.batchAction = false;
    }
  };

  /**
   * 批量发货
   */
  const batchShip = async (orderIds: number[], carrier: string, shippingDate: string, remarks?: string) => {
    loading.value.batchAction = true;
    try {
      const params = {
        orderIds,
        carrier,
        shippingDate,
        remarks
      };
      
      const response = await batchShipOrders(params);
      ElMessage.success(`批量发货完成：成功 ${response.data.successCount} 个，失败 ${response.data.failureCount} 个`);
      
      return response.data;
    } catch (error) {
      console.error('Failed to batch ship:', error);
      ElMessage.error('批量发货失败');
      throw error;
    } finally {
      loading.value.batchAction = false;
    }
  };

  /**
   * 检查库存
   */
  const checkPartInventory = async (partIds: string[]) => {
    loading.value.inventory = true;
    try {
      const response = await checkInventory({ partIds });
      inventoryResults.value = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to check inventory:', error);
      ElMessage.error('检查库存失败');
      throw error;
    } finally {
      loading.value.inventory = false;
    }
  };

  /**
   * 获取承运商列表
   */
  const fetchCarriers = async () => {
    try {
      const response = await getCarriers();
      carrierList.value = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch carriers:', error);
      throw error;
    }
  };

  /**
   * 获取经销商列表
   */
  const fetchDealers = async (params?: any) => {
    try {
      const response = await getDealers(params);
      dealerList.value = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch dealers:', error);
      throw error;
    }
  };

  /**
   * 获取统计数据
   */
  const fetchStatistics = async (params: {
    startDate: string;
    endDate: string;
    dealerId?: number;
    groupBy: 'day' | 'week' | 'month';
  }) => {
    loading.value.statistics = true;
    try {
      const response = await getOrderStatistics(params);
      orderStatistics.value = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch statistics:', error);
      ElMessage.error('获取统计数据失败');
      throw error;
    } finally {
      loading.value.statistics = false;
    }
  };

  /**
   * 更新订单优先级
   */
  const updatePriority = async (orderId: number, priority: string, reason?: string) => {
    try {
      await updateOrderPriority({ orderId, priority: priority as any, reason });
      ElMessage.success('优先级更新成功');
      
      // 更新本地订单
      const order = orderList.value.find(o => o.orderId === orderId);
      if (order) {
        order.priority = priority as any;
      }
    } catch (error) {
      console.error('Failed to update priority:', error);
      ElMessage.error('更新优先级失败');
      throw error;
    }
  };

  /**
   * 添加订单备注
   */
  const addNote = async (orderId: number, note: string, isInternal: boolean) => {
    try {
      const response = await addOrderNote({ orderId, note, isInternal });
      ElMessage.success('备注添加成功');
      return response.data;
    } catch (error) {
      console.error('Failed to add note:', error);
      ElMessage.error('添加备注失败');
      throw error;
    }
  };

  /**
   * 获取订单备注
   */
  const fetchOrderNotes = async (orderId: number) => {
    try {
      const response = await getOrderNotes(orderId);
      orderNotes.value = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch notes:', error);
      throw error;
    }
  };

  /**
   * 设置筛选条件
   */
  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    filters.value = { ...filters.value, ...newFilters };
  };

  /**
   * 重置筛选条件
   */
  const resetFilters = () => {
    filters.value = {
      query: '',
      status: [],
      dealerId: undefined,
      priority: undefined,
      dateRange: []
    };
  };

  /**
   * 设置分页
   */
  const setPagination = (page: number, pageSize: number) => {
    pagination.value.page = page;
    pagination.value.pageSize = pageSize;
  };

  /**
   * 设置选中的订单
   */
  const setSelectedOrders = (orderIds: number[]) => {
    selectedOrders.value = orderIds;
  };

  /**
   * 清空当前订单详情
   */
  const clearCurrentOrderDetail = () => {
    currentOrderDetail.value = null;
  };

  return {
    // 状态
    dashboardData,
    orderList,
    currentOrderDetail,
    inventoryResults,
    carrierList,
    dealerList,
    orderStatistics,
    orderNotes,
    selectedOrders,
    pagination,
    loading,
    filters,
    
    // 计算属性
    statusCounts,
    filteredOrders,
    pendingApprovalOrders,
    pendingShipmentOrders,
    
    // 方法
    fetchDashboard,
    fetchOrderList,
    fetchOrderDetail,
    approveOrder,
    shipOrder,
    batchApprove,
    batchShip,
    checkPartInventory,
    fetchCarriers,
    fetchDealers,
    fetchStatistics,
    updatePriority,
    addNote,
    fetchOrderNotes,
    setFilters,
    resetFilters,
    setPagination,
    setSelectedOrders,
    clearCurrentOrderDetail
  };
});