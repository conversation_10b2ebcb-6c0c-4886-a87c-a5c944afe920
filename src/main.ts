import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
import './assets/main.css'
import './assets/styles/image-viewer-fix.css'

import App from './App.vue'
import { setupI18n, loadAppMessages } from './plugins/i18n'
import router from './router'
import { useAuthStore } from '@/stores/auth'
import permissionPlugin from './plugins/permission'

async function bootstrap() {
  const app = createApp(App)

  // 设置 i18n
  setupI18n(app)

  // 异步加载应用语言包
  await loadAppMessages()

  const pinia = createPinia()
  app.use(pinia)
  app.use(router)
  app.use(ElementPlus)
  app.use(permissionPlugin)

  // 初始化认证状态
  const authStore = useAuthStore()
  await authStore.initAuth()

  app.mount('#app')
}

// 启动应用
bootstrap().catch(console.error)
