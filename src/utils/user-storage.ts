import type { UserInfo } from '@/api/modules/auth'

const USER_INFO_KEY = 'userInfo'
const TOKEN_KEY = 'token'
const REFRESH_TOKEN_KEY = 'refreshToken'

/**
 * 用户信息本地存储工具类
 */
export class UserStorage {
  /**
   * 保存用户信息到本地存储
   */
  static setUserInfo(userInfo: UserInfo): void {
    try {
      if(!userInfo){
        console.log('userInfo is null')
        localStorage.removeItem('userInfo');
        return
      }
      localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
      console.log('✅ 用户信息已保存到本地存储:', userInfo.username)

    } catch (error) {
      console.error('❌ 保存用户信息到本地存储失败:', error)
    }
  }

  /**
   * 从本地存储获取用户信息
   */
  static getUserInfo(): UserInfo | null {
    try {
      const stored = localStorage.getItem(USER_INFO_KEY)
      if (stored) {
        const userInfo = JSON.parse(stored) as UserInfo
        console.log('userInfo', userInfo)
        console.log('✅ 从本地存储获取用户信息:', userInfo.username)
        return userInfo
      }
      return null
    } catch (error) {
      console.error('❌ 从本地存储获取用户信息失败:', error)
      // 清除损坏的数据
      localStorage.removeItem(USER_INFO_KEY)
      return null
    }
  }

  /**
   * 保存Token到本地存储
   */
  static setToken(token: string): void {
    try {
      localStorage.setItem(TOKEN_KEY, token)
      console.log('✅ Token已保存到本地存储')
    } catch (error) {
      console.error('❌ 保存Token到本地存储失败:', error)
    }
  }

  /**
   * 从本地存储获取Token
   */
  static getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY)
  }

  /**
   * 保存RefreshToken到本地存储
   */
  static setRefreshToken(refreshToken: string): void {
    try {
      localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
      console.log('✅ RefreshToken已保存到本地存储')
    } catch (error) {
      console.error('❌ 保存RefreshToken到本地存储失败:', error)
    }
  }

  /**
   * 从本地存储获取RefreshToken
   */
  static getRefreshToken(): string | null {
    return localStorage.getItem(REFRESH_TOKEN_KEY)
  }

  /**
   * 清除所有用户相关数据
   */
  static clearAll(): void {
    try {
      localStorage.removeItem(USER_INFO_KEY)
      localStorage.removeItem(TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)
      console.log('✅ 所有用户数据已从本地存储清除')
    } catch (error) {
      console.error('❌ 清除本地存储失败:', error)
    }
  }

  /**
   * 检查是否有用户信息
   */
  static hasUserInfo(): boolean {
    return !!this.getUserInfo()
  }

  /**
   * 检查是否有Token
   */
  static hasToken(): boolean {
    return !!this.getToken()
  }

  /**
   * 获取用户信息摘要（用于调试）
   */
  static getUserInfoSummary(): string {
    const userInfo = this.getUserInfo()
    if (!userInfo) {
      return '无用户信息'
    }
    return `${userInfo.username} (${userInfo.realName}) - 角色: ${userInfo.roles.join(', ')}`
  }

  /**
   * 获取存储状态摘要（用于调试）
   */
  static getStorageSummary(): Record<string, unknown> {
    return {
      hasToken: this.hasToken(),
      hasUserInfo: this.hasUserInfo(),
      tokenLength: this.getToken()?.length || 0,
      userInfoSummary: this.getUserInfoSummary(),
      storageKeys: {
        token: !!localStorage.getItem(TOKEN_KEY),
        refreshToken: !!localStorage.getItem(REFRESH_TOKEN_KEY),
        userInfo: !!localStorage.getItem(USER_INFO_KEY)
      }
    }
  }
}

export default UserStorage
