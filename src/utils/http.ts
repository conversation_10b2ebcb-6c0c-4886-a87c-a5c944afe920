import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { config } from '@/config';
import { i18nGlobal } from '@/plugins/i18n';

// 语言代码映射
const getLanguageCode = (locale: string): string => {
  switch (locale) {
    case 'zh':
      return 'zh_CN';
    case 'en':
      return 'en_US';
    default:
      return 'zh_CN';
  }
};

// 创建axios实例
const service = axios.create({
  baseURL: config.apiBaseUrl,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
});

// 请求拦截器
service.interceptors.request.use(
  (requestConfig) => {
    // 添加token到请求头
    const token = localStorage.getItem(config.tokenKey);
    if (token && requestConfig.headers) {
      requestConfig.headers.Authorization = `Bearer ${token}`;
    }

    // 添加语言信息
    const currentLocale = i18nGlobal.locale.value;
    const langCode = getLanguageCode(currentLocale);

    // 方式1：添加到请求参数中
    if (requestConfig.method === 'get' || requestConfig.method === 'delete') {
      // GET和DELETE请求添加到URL参数
      if (!requestConfig.params) {
        requestConfig.params = {};
      }
      requestConfig.params.lang = langCode;
    } else {
      // POST、PUT、PATCH请求添加到URL参数
      if (!requestConfig.params) {
        requestConfig.params = {};
      }
      requestConfig.params.lang = langCode;
    }

    // 方式2：添加到请求头中
    if (requestConfig.headers) {
      requestConfig.headers['Accept-Language'] = langCode;
    }

    // 打印请求信息（开发环境）
    if (import.meta.env.DEV) {
      console.log('🚀 API Request:', {
        url: requestConfig.url,
        method: requestConfig.method?.toUpperCase(),
        headers: requestConfig.headers,
        params: requestConfig.params,
        data: requestConfig.data
      });
    }

    return requestConfig;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data;

    // 打印响应信息（开发环境）
    if (import.meta.env.DEV) {
      console.log('📩 API Response:', {
        url: response.config.url,
        status: response.status,
        data: res
      });
    }

    // 如果是blob类型，直接返回
    if (response.config.responseType === 'blob') {
      return response;
    }

    // 检查返回状态码 - 支持多种成功码格式
    if (res.code && res.code !== 200 && res.code !== '200' && res.code !== 0 && res.code !== '0') {
      const errorMessage = res.message || '请求错误';

      // 特定错误码处理
      if (res.code === 401 || res.code === '401') {
        // Token失效，清除本地存储并提示重新登录
        handleTokenExpired();
        return Promise.reject(new Error('登录已过期，请重新登录'));
      } else if (res.code === 403 || res.code === '403') {
        ElMessage.error('您没有操作权限');
      } else {
        ElMessage.error(errorMessage);
      }

      return Promise.reject(new Error(errorMessage));
    }

    return res;
  },
  (error) => {
    console.error('❌ API Error:', error);

    let message = '请求出错';

    if (error.response) {
      const status = error.response.status;
      switch (status) {
        case 400:
          message = '请求参数错误';
          break;
        case 401:
          message = '未授权访问';
          handleTokenExpired();
          break;
        case 403:
          message = '没有操作权限';
          break;
        case 404:
          message = '请求资源未找到';
          break;
        case 408:
          message = '请求超时';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        case 502:
          message = '网关错误';
          break;
        case 503:
          message = '服务不可用';
          break;
        case 504:
          message = '网关超时';
          break;
        default:
          message = `请求失败: ${status}`;
      }
    } else if (error.request) {
      message = '网络连接失败，请检查网络';
    } else {
      message = error.message || '请求配置错误';
    }

    ElMessage.error(message);
    return Promise.reject(error);
  }
);

// 处理Token过期
const handleTokenExpired = () => {
  // 清除本地存储的token
  localStorage.removeItem(config.tokenKey);
  localStorage.removeItem(config.refreshTokenKey);

  // 提示用户并跳转到登录页
  ElMessageBox.confirm(
    '登录已过期，请重新登录',
    '提示',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 跳转到登录页
    window.location.href = '/login';
  }).catch(() => {
    // 用户取消，也跳转到登录页
    window.location.href = '/login';
  });
};

// 封装HTTP方法 - 添加更好的类型支持
export const http = {
  // GET请求
  get<P = Record<string, unknown>, R = unknown>(
    url: string,
    params?: P,
    config?: AxiosRequestConfig
  ): Promise<R> {
    return service.get(url, { params, ...config });
  },

  // POST请求
  post<D = Record<string, unknown>, R = unknown>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig
  ): Promise<R> {
    return service.post(url, data, config);
  },

  // PUT请求
  put<D = Record<string, unknown>, R = unknown>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig
  ): Promise<R> {
    return service.put(url, data, config);
  },

  // DELETE请求
  delete<P = Record<string, unknown>, R = unknown>(
    url: string,
    params?: P,
    config?: AxiosRequestConfig
  ): Promise<R> {
    return service.delete(url, { params, ...config });
  },

  // PATCH请求
  patch<D = Record<string, unknown>, R = unknown>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig
  ): Promise<R> {
    return service.patch(url, data, config);
  },

  // 上传文件
  upload<R = unknown>(
    url: string,
    formData: FormData,
    config?: AxiosRequestConfig
  ): Promise<R> {
    return service.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      }
    });
  },

  // 下载文件
  download(
    url: string,
    params?: Record<string, unknown>,
    filename?: string
  ): Promise<void> {
    return service.get(url, {
      params,
      responseType: 'blob'
    }).then((response: AxiosResponse) => {
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    });
  }
};

// 默认导出axios实例，供特殊情况使用
export default service;
