// 日期工具函数

import dayjs from 'dayjs';

/**
 * 格式化日期时间
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export const formatDateTime = (
  date: string | Date | null | undefined,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  if (!date) return '-';
  return dayjs(date).format(format);
};

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认为 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (
  date: string | Date | null | undefined,
  format: string = 'YYYY-MM-DD'
): string => {
  if (!date) return '-';
  return dayjs(date).format(format);
};

/**
 * 格式化时间
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认为 'HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export const formatTime = (
  date: string | Date | null | undefined,
  format: string = 'HH:mm:ss'
): string => {
  if (!date) return '-';
  return dayjs(date).format(format);
};

/**
 * 获取相对时间
 * @param date 日期字符串或Date对象
 * @returns 相对时间字符串
 */
export const getRelativeTime = (date: string | Date | null | undefined): string => {
  if (!date) return '-';
  return dayjs(date).fromNow();
};

/**
 * 判断是否为今天
 * @param date 日期字符串或Date对象
 * @returns 是否为今天
 */
export const isToday = (date: string | Date | null | undefined): boolean => {
  if (!date) return false;
  return dayjs(date).isSame(dayjs(), 'day');
};

/**
 * 判断是否为昨天
 * @param date 日期字符串或Date对象
 * @returns 是否为昨天
 */
export const isYesterday = (date: string | Date | null | undefined): boolean => {
  if (!date) return false;
  return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day');
};

/**
 * 获取日期范围
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 日期范围数组
 */
export const getDateRange = (
  startDate: string | Date,
  endDate: string | Date
): string[] => {
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const dates: string[] = [];
  
  let current = start;
  while (current.isBefore(end) || current.isSame(end)) {
    dates.push(current.format('YYYY-MM-DD'));
    current = current.add(1, 'day');
  }
  
  return dates;
};

/**
 * 计算两个日期之间的天数差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差
 */
export const getDaysDiff = (
  startDate: string | Date,
  endDate: string | Date
): number => {
  return dayjs(endDate).diff(dayjs(startDate), 'day');
};

/**
 * 获取当前时间戳
 * @returns 时间戳
 */
export const getCurrentTimestamp = (): number => {
  return dayjs().valueOf();
};

/**
 * 时间戳转日期
 * @param timestamp 时间戳
 * @param format 格式化模板
 * @returns 格式化后的日期字符串
 */
export const timestampToDate = (
  timestamp: number,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  return dayjs(timestamp).format(format);
};

/**
 * 日期转时间戳
 * @param date 日期字符串或Date对象
 * @returns 时间戳
 */
export const dateToTimestamp = (date: string | Date): number => {
  return dayjs(date).valueOf();
};
