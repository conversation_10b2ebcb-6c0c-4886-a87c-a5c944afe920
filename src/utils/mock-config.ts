import { config } from '@/config'

/**
 * 统一的Mock配置管理
 * 所有API模块都应该通过这个工具来判断是否使用Mock数据
 */
export class MockConfig {
  /**
   * 获取Mock API开关状态
   * 统一从config中获取，避免在各个文件中重复定义
   */
  static get enabled(): boolean {
    return config.useMockApi
  }

  /**
   * 开发环境专用：打印Mock状态
   */
  static logStatus(moduleName: string): void {
    if (import.meta.env.DEV) {
      console.log(`[Mock Config] ${moduleName}: ${this.enabled ? '✅ 使用Mock数据' : '❌ 使用真实API'}`)
    }
  }

  /**
   * 条件执行Mock或真实API
   * @param mockFn Mock数据处理函数
   * @param realApiFn 真实API调用函数
   * @param moduleName 模块名称（用于日志）
   */
  static async execute<T>(
    mockFn: () => Promise<T>,
    realApiFn: () => Promise<T>,
    moduleName?: string
  ): Promise<T> {
    if (moduleName) {
      this.logStatus(moduleName)
    }

    return this.enabled ? await mockFn() : await realApiFn()
  }

  /**
   * 同步版本的条件执行
   */
  static executeSync<T>(
    mockFn: () => T,
    realApiFn: () => T,
    moduleName?: string
  ): T {
    if (moduleName) {
      this.logStatus(moduleName)
    }

    return this.enabled ? mockFn() : realApiFn()
  }
}

/**
 * 简化的Mock配置常量
 * 用于向后兼容现有代码
 */
export const USE_MOCK_API = MockConfig.enabled

/**
 * 便捷的Mock执行函数
 */
export const mockOrReal = MockConfig.execute

export default MockConfig
