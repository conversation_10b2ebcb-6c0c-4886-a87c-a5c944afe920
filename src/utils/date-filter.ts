// 日期过滤和处理工具类
import { ProspectLevel } from '@/types/prospective-customer.d'

/**
 * 格式化日期
 * @param date 日期
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date): string => {
  if (!date) return ''
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const pad = (n: number) => n.toString().padStart(2, '0')

  const year = d.getUTCFullYear()
  const month = pad(d.getUTCMonth() + 1)
  const day = pad(d.getUTCDate())
  const hour = pad(d.getUTCHours())
  const minute = pad(d.getUTCMinutes())

  return `${year}-${month}-${day} ${hour}:${minute}`;
}

/**
 * 判断日期是否逾期
 * @param date 日期
 * @returns 是否逾期
 */
export const isOverdue = (date: string | Date): boolean => {
  if (!date) return false
  const targetDate = new Date(date)
  const now = new Date()
  return targetDate < now
}

/**
 * 判断日期是否为今天
 * @param date 日期
 * @returns 是否为今天
 */
export const isToday = (date: string | Date): boolean => {
  if (!date) return false
  const targetDate = new Date(date)
  const today = new Date()
  return targetDate.toDateString() === today.toDateString()
}

/**
 * 将日期转换为字符串
 * @param date 日期
 * @returns 日期字符串
 */
export const dateToString = (date: Date | string | null): string => {
  if (!date) return ''
  if (typeof date === 'string') return date
  return date.toISOString()
}

/**
 * 根据意向级别计算下次跟进时间
 * @param currentTime 当前跟进时间
 * @param level 意向级别 (支持枚举值和字典编码)
 * @returns 下次跟进时间
 */
export const calculateNextFollowUpTime = (currentTime: Date | string, level: ProspectLevel | string): Date => {
  const current = new Date(currentTime)

  // 支持字典编码和枚举值的映射
  const dayMapping: Record<string, number> = {
    // 枚举值
    [ProspectLevel.H]: 1, // H级：1天后
    [ProspectLevel.A]: 3, // A级：3天后
    [ProspectLevel.B]: 7, // B级：7天后
    [ProspectLevel.C]: 14, // C级：14天后
    // 字典编码
    '01160001': 1, // H级：1天后
    '01160002': 3, // A级：3天后
    '01160003': 7, // B级：7天后
    '01160004': 14 // C级：14天后
  }

  const daysToAdd = dayMapping[level] || 7
  const nextTime = new Date(current)
  nextTime.setDate(nextTime.getDate() + daysToAdd)
  return nextTime
}
