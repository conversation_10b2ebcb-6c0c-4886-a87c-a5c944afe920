// dms-frontend/src/api/index.ts
import axios, { type AxiosError, type AxiosInstance, type AxiosResponse, type InternalAxiosRequestConfig } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
// import { useRouter } from 'vue-router'; // 如果你想在拦截器中用 useRouter，需要确保上下文正确
import { i18nGlobal } from '@/plugins/i18n'; // 导入国际化实例

// 获取环境变量中的 API Base URL
const baseURL = import.meta.env.VITE_APP_BASE_API as string;

// 创建 Axios 实例
const service: AxiosInstance = axios.create({
  baseURL: baseURL, // API 的 base_url，来自环境变量
  timeout: 10000, // 请求超时时间 (10秒)
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在发送请求之前做些什么
    // 例如：添加 Token
    const token = localStorage.getItem('token'); // 假设 Token 存储在 localStorage
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加语言信息
    const currentLocale = i18nGlobal.locale.value;
    const langCode = getLanguageCode(currentLocale);

    // 方式1：添加到请求参数中
    if (config.method === 'get' || config.method === 'delete') {
      // GET和DELETE请求添加到URL参数
      if (!config.params) {
        config.params = {};
      }
      config.params.lang = langCode;
    } else {
      // POST、PUT、PATCH请求添加到URL参数
      if (!config.params) {
        config.params = {};
      }
      config.params.lang = langCode;
    }

    // 方式2：添加到请求头中
    if (config.headers) {
      config.headers['Accept-Language'] = langCode;
    }

    return config;
  },
  (error: AxiosError) => {
    // 对请求错误做些什么
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);




// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 对响应数据做些什么
    const res = response.data;

    // 假设后端返回的数据结构为 { code: "0", message: '成功', result: {} }
    // 或 { code: "10001", message: '认证失败', result: null }
    if (res.code !== "0" && res.code !== "200") { // 根据后端实际的成功码调整
      // 业务错误处理
      const { t } = i18nGlobal; // 获取国际化 t 函数
      let errorMessage = res.message || t('common.operationFailed'); // 默认操作失败
      // 常见错误码处理 (这里可以根据后端定义更详细地扩展)
      if (res.code === "401" || res.code === "10001") { // 认证失败或 Token 失效
        ElMessageBox.confirm(
          t('common.reLoginPrompt'), // "登录已过期，请重新登录"
          t('common.warning'), // "警告"
          {
            confirmButtonText: t('common.confirm'), // "确定"
            cancelButtonText: t('common.cancel'), // "取消"
            type: 'warning'
          }
        ).then(() => {
          // 清除 Token 并跳转到登录页
          localStorage.removeItem('token');
          // 这里如果是在组件外部，直接使用 window.location.href 进行跳转
          window.location.href = '/login'; // 强制刷新并跳转到登录页
        }).catch(() => {
          // 用户点击取消，不处理
        });
        return Promise.reject(new Error(errorMessage));
      } else if (res.code === "403" || res.code === "10002") { // 无权限
        errorMessage = t('common.noPermission'); // "您没有操作权限"
        ElMessage.error(errorMessage); // 显示权限错误提示
      } else {
        // 其他业务错误
        ElMessage.error(errorMessage);
      }

      return Promise.reject(new Error(errorMessage));
    } else {
      // 成功响应
      // 如果后端成功码是 0 或 200，并且有 message，也可以选择性地显示成功提示
      // if (res.message && res.message !== 'OK') { // 避免显示通用的"OK"
      //   ElMessage.success(res.message);
      // }
      return res; // 返回整个响应对象，包含 result 字段
    }
  },
  (error: AxiosError) => {
    // 对响应错误做些什么（HTTP 状态码非 2xx 的情况，例如 404, 500）
    const { t } = i18nGlobal;
    let message = t('common.networkError'); // 默认网络错误

    if (error.response) {
      const status = error.response.status;
      switch (status) {
        case 400: message = t('common.badRequest'); break;
        case 401: message = t('common.unauthorized');
                  localStorage.removeItem('token');
                  window.location.href = '/login'; // 未授权，强制跳转登录页
                  break;
        case 403: message = t('common.forbidden'); break;
        case 404: message = t('common.notFound'); break;
        case 408: message = t('common.requestTimeout'); break;
        case 500: message = t('common.serverError'); break;
        case 501: message = t('common.notImplemented'); break;
        case 502: message = t('common.badGateway'); break;
        case 503: message = t('common.serviceUnavailable'); break;
        case 504: message = t('common.gatewayTimeout'); break;
        default: message = t('common.unknownError');
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应（网络断开或服务器无响应）
      console.warn('No response received:', error.request);
      message = t('common.noResponse');
    } else {
      // 在设置请求时发生了一些事情，触发了错误
      console.error('Error during request setup:', error.message);
      message = t('common.requestSetupError');
    }
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

// 语言代码映射
const getLanguageCode = (locale: string): string => {
  switch (locale) {
    case 'zh':
      return 'zh_CN';
    case 'en':
      return 'en_US';
    default:
      return 'zh_CN';
  }
};

export default service;

// 导出 request 别名以保持兼容性
export const request = service;
