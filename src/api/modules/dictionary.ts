import request from '@/api';

// 数据字典项接口
export interface DictionaryItem {
  code: string;
  name: string;
}

// API响应接口
export interface DictionaryResponse {
  success: boolean;
  message: string;
  code: string;
  traceId: string;
  result: DictionaryItem[];
  timestamp: number;
}

/**
 * 获取指定类型的数据字典
 * @param type 字典类型编码
 * @returns Promise<DictionaryItem[]>
 */
export const getDictionaryList = async (type: string): Promise<DictionaryItem[]> => {
  try {
    const response = await request.get<any, DictionaryResponse>(`/basic/dic/list`, {
      params: { type }
    });

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取字典数据失败');
  } catch (error) {
    console.error(`获取字典数据失败 (${type}):`, error);
    throw error;
  }
};

/**
 * 批量获取多个类型的数据字典
 * @param dictionaryTypes 字典类型编码数组
 * @returns Promise<Record<string, DictionaryItem[]>>
 */
export const getBatchDictionaryList = async (dictionaryTypes: string[]): Promise<Record<string, DictionaryItem[]>> => {
  try {
    const promises = dictionaryTypes.map(type =>
      getDictionaryList(type).then(result => ({ [type]: result }))
    );

    const results = await Promise.all(promises);
    return results.reduce((acc, curr) => ({ ...acc, ...curr }), {});
  } catch (error) {
    console.error('批量获取字典数据失败:', error);
    throw error;
  }
};
