import request from '../index';
import type {
  Invoice,
  InvoiceSearchParams,
  PaginationResponse,
  ApiResponse,
  InvoiceDetail,
  InvoiceOperationLog
} from '@/types/invoice';

// Mock数据导入
import { mockInvoiceList } from '@/mock/data/invoice';
// 导入国际化实例
import { i18nGlobal } from '@/plugins/i18n';

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Invoice Module')

// 从国际化配置获取默认值
const getDefaultValues = () => {
  const { t } = i18nGlobal;
  return {
    companyName: t('invoice.defaultValues.companyName'),
    companyAddress: t('invoice.defaultValues.companyAddress'),
    gstNumber: t('invoice.defaultValues.gstNumber'),
    sstNumber: t('invoice.defaultValues.sstNumber'),
    contactPhone: t('invoice.defaultValues.contactPhone'),
    contactEmail: t('invoice.defaultValues.contactEmail'),
    salesConsultantId: t('invoice.defaultValues.salesConsultantId'),
    tinNumber: t('invoice.defaultValues.tinNumber'),
    modelCode: t('invoice.defaultValues.modelCode'),
    modelDescription: t('invoice.defaultValues.modelDescription'),
    engineNumber: t('invoice.defaultValues.engineNumber'),
    chassisNumber: t('invoice.defaultValues.chassisNumber'),
    engineCapacity: t('invoice.defaultValues.engineCapacity'),
    fuelType: t('invoice.defaultValues.fuelType'),
    transmission: t('invoice.defaultValues.transmission'),
    year: t('invoice.defaultValues.year'),
    vehicleRegistrationDate: t('invoice.defaultValues.vehicleRegistrationDate'),
    creator: t('invoice.defaultValues.creator'),
    updater: t('invoice.defaultValues.updater'),
    updateTime: t('invoice.defaultValues.updateTime'),
    financeType: t('invoice.defaultValues.financeType'),
    loanTerm: Number(t('invoice.defaultValues.loanTerm')),
    insuranceCompany: t('invoice.defaultValues.insuranceCompany'),
    agentCode: t('invoice.defaultValues.agentCode'),
    policyNumber: t('invoice.defaultValues.policyNumber'),
    policyDate: t('invoice.defaultValues.policyDate'),
    vehiclePrice: Number(t('invoice.defaultValues.vehiclePrice')),
    licensePlateFee: Number(t('invoice.defaultValues.licensePlateFee')),
    totalAccessoryAmount: Number(t('invoice.defaultValues.totalAccessoryAmount')),
    subtotal: Number(t('invoice.defaultValues.subtotal')),
    totalOtrFeeAmount: Number(t('invoice.defaultValues.totalOtrFeeAmount')),
    insurancePremium: Number(t('invoice.defaultValues.insurancePremium')),
    totalSalesPrice: Number(t('invoice.defaultValues.totalSalesPrice')),
    adjustmentAmount: Number(t('invoice.defaultValues.adjustmentAmount')),
    invoiceNetValue: Number(t('invoice.defaultValues.invoiceNetValue'))
  };
};

// 从国际化配置获取模拟数据
const getMockData = () => {
  const { t } = i18nGlobal;
  return {
    accessories: [
      {
        category: t('invoice.mockData.accessories.0.category'),
        name: t('invoice.mockData.accessories.0.name'),
        unitPrice: 1500,
        quantity: 1,
        totalPrice: 1500
      },
      {
        category: t('invoice.mockData.accessories.1.category'),
        name: t('invoice.mockData.accessories.1.name'),
        unitPrice: 1300,
        quantity: 1,
        totalPrice: 1300
      }
    ],
    otrFees: [
      {
        billNumber: t('invoice.mockData.otrFees.0.billNumber'),
        feeItem: t('invoice.mockData.otrFees.0.feeItem'),
        price: 120,
        effectiveDate: '2025-05-09',
        expiryDate: '2026-05-08'
      },
      {
        billNumber: t('invoice.mockData.otrFees.1.billNumber'),
        feeItem: t('invoice.mockData.otrFees.1.feeItem'),
        price: 200,
        effectiveDate: '2025-05-09',
        expiryDate: '2025-05-09'
      }
    ],
    receipts: [
      {
        receiptNumber: t('invoice.mockData.receipts.0.receiptNumber'),
        businessType: t('invoice.mockData.receipts.0.businessType'),
        serialNumber: t('invoice.mockData.receipts.0.serialNumber'),
        channel: t('invoice.mockData.receipts.0.channel'),
        amount: 30000,
        collectionType: t('invoice.mockData.receipts.0.collectionType'),
        arrivalTime: '2024-01-10 09:00:00',
        remark: t('invoice.mockData.receipts.0.remark')
      },
      {
        receiptNumber: t('invoice.mockData.receipts.1.receiptNumber'),
        businessType: t('invoice.mockData.receipts.1.businessType'),
        serialNumber: t('invoice.mockData.receipts.1.serialNumber'),
        channel: t('invoice.mockData.receipts.1.channel'),
        amount: 32484.65,
        collectionType: t('invoice.mockData.receipts.1.collectionType'),
        arrivalTime: '2024-01-15 10:00:00',
        remark: t('invoice.mockData.receipts.1.remark')
      }
    ]
  };
};

// 通用API响应接口
interface ApiResponseData<T = unknown> {
  code: string;
  message: string;
  result: T;
}

// 获取发票列表
export const getInvoiceList = (params: InvoiceSearchParams): Promise<PaginationResponse<Invoice>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredData = [...mockInvoiceList];

        // 应用筛选条件
        if (params.invoiceNumber) {
          filteredData = filteredData.filter(item =>
            item.invoiceNumber.includes(params.invoiceNumber!)
          );
        }
        if (params.customerName) {
          filteredData = filteredData.filter(item =>
            item.customerName.includes(params.customerName!)
          );
        }
        if (params.customerPhone) {
          filteredData = filteredData.filter(item =>
            item.customerPhone.includes(params.customerPhone!)
          );
        }
        if (params.customerEmail) {
          filteredData = filteredData.filter(item =>
            item.customerEmail.includes(params.customerEmail!)
          );
        }
        if (params.orderNumber) {
          filteredData = filteredData.filter(item =>
            item.orderNumber.includes(params.orderNumber!)
          );
        }
        if (params.vin) {
          filteredData = filteredData.filter(item =>
            item.vin.includes(params.vin!)
          );
        }
        if (params.salesType) {
          filteredData = filteredData.filter(item =>
            item.paymentMethod === params.salesType
          );
        }
        if (params.salesStore) {
          filteredData = filteredData.filter(item =>
            item.salesStore === params.salesStore
          );
        }
        if (params.salesConsultant) {
          filteredData = filteredData.filter(item =>
            item.salesConsultant === params.salesConsultant
          );
        }
        if (params.invoiceDateStart && params.invoiceDateEnd) {
          filteredData = filteredData.filter(item => {
            const invoiceDate = new Date(item.invoiceDate);
            const startDate = new Date(params.invoiceDateStart!);
            const endDate = new Date(params.invoiceDateEnd!);
            return invoiceDate >= startDate && invoiceDate <= endDate;
          });
        }

        // 分页处理
        const total = filteredData.length;
        const start = (params.pageNum - 1) * params.pageSize;
        const end = start + params.pageSize;
        const list = filteredData.slice(start, end);

        resolve({
          list,
          total,
          pageNum: params.pageNum,
          pageSize: params.pageSize,
          pages: Math.ceil(total / params.pageSize)
        });
      }, 500);
    });
  } else {
    return request.get('/invoices', { params }).then((response: any) => {
      console.log(response);
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '获取发票列表失败');
    });
  }
};

// 获取发票详情
export const getInvoiceDetail = (id: number): Promise<InvoiceDetail> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const invoice = mockInvoiceList.find(item => item.id === id);
        if (invoice) {
          // 获取国际化配置的默认值和模拟数据
          const defaultValues = getDefaultValues();
          const mockData = getMockData();

          // 模拟添加 InvoiceDetail 特有的详细数据
          const detailInvoice: InvoiceDetail = {
            ...invoice,
            ...defaultValues,
            deliveryNumber: `DEL ${invoice.invoiceNumber ? invoice.invoiceNumber.slice(-6) : ''}`,
            modelCode: invoice.modelCode || defaultValues.modelCode,
            modelDescription: invoice.modelDescription || defaultValues.modelDescription,
            fuelType: invoice.fuelType || defaultValues.fuelType,
            transmission: invoice.transmission || defaultValues.transmission,
            year: invoice.year || defaultValues.year,
            creator: invoice.creator || defaultValues.creator,
            updater: invoice.updater || defaultValues.updater,
            updateTime: invoice.updateTime || defaultValues.updateTime,
            interestRate: invoice.interestRate || 0,
            monthlyPayment: invoice.monthlyPayment || 0,
            insuranceAmount: invoice.insuranceAmount || invoice.loanAmount || 0,
            vehiclePrice: invoice.vehiclePrice || defaultValues.vehiclePrice,
            licensePlateFee: invoice.licensePlateFee || defaultValues.licensePlateFee,
            accessories: invoice.accessories || mockData.accessories,
            totalAccessoryAmount: invoice.totalAccessoryAmount || defaultValues.totalAccessoryAmount,
            subtotal: invoice.subtotal || defaultValues.subtotal,
            otrFees: invoice.otrFees || mockData.otrFees,
            totalOtrFeeAmount: invoice.totalOtrFeeAmount || defaultValues.totalOtrFeeAmount,
            insurancePremium: invoice.insurancePremium || defaultValues.insurancePremium,
            totalSalesPrice: invoice.totalSalesPrice || defaultValues.totalSalesPrice,
            adjustmentAmount: invoice.adjustmentAmount || defaultValues.adjustmentAmount,
            invoiceNetValue: invoice.invoiceNetValue || defaultValues.invoiceNetValue,
            receipts: invoice.receipts || mockData.receipts
          };
          resolve(detailInvoice);
        } else {
          reject(new Error('发票不存在'));
        }
      }, 300);
    });
  } else {
    return request.get(`/invoices/${id}`).then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '获取发票详情失败');
    });
  }
};

// 打印发票
export const printInvoice = (id: number): Promise<ApiResponse> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '发票打印成功',
          data: null
        });
      }, 1000);
    });
  } else {
    return request.post(`/invoices/${id}/print`).then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '打印发票失败');
    });
  }
};

// 批量打印发票
export const batchPrintInvoices = (ids: number[]): Promise<ApiResponse> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: `成功打印 ${ids.length} 张发票`,
          data: null
        });
      }, 2000);
    });
  } else {
    return request.post('/invoices/batch-print', { ids }).then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '批量打印发票失败');
    });
  }
};

// 发送发票邮件
export const sendInvoiceEmail = (id: number, recipientEmail: string): Promise<ApiResponse> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '发票邮件发送成功',
          data: null
        });
      }, 1500);
    });
  } else {
    return request.post(`/invoices/${id}/email`, { recipientEmail }).then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '发送邮件失败');
    });
  }
};

// 导出发票数据
export const exportInvoiceData = (params: any): Promise<ApiResponse> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '数据导出成功',
          data: {
            downloadUrl: 'https://example.com/invoices-export.xlsx'
          }
        });
      }, 3000);
    });
  } else {
    return request.post('/invoices/export', params).then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '导出数据失败');
    });
  }
};

// 获取门店列表
export const getStoreList = (): Promise<{ label: string; value: string }[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          { label: '门店1', value: 'store1' },
          { label: '门店2', value: 'store2' },
          { label: '门店3', value: 'store3' }
        ]);
      }, 200);
    });
  } else {
    return request.get('/master-data/dealers').then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '获取门店列表失败');
    });
  }
};

// 获取销售顾问列表
export const getConsultantList = (storeId?: string): Promise<{ label: string; value: string }[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const consultants = storeId === 'store1'
          ? [{ label: '张三', value: 'zhangsan' }, { label: '李四', value: 'lisi' }]
          : storeId === 'store2'
          ? [{ label: '王五', value: 'wangwu' }, { label: '赵六', value: 'zhaoliu' }]
          : [{ label: '钱七', value: 'qianqi' }, { label: '孙八', value: 'sunba' }];
        resolve(consultants);
      }, 200);
    });
  } else {
    return request.get('/master-data/sales-consultants', {
      params: storeId ? { storeId } : {}
    }).then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '获取销售顾问列表失败');
    });
  }
};

// 获取发票操作日志
export const getInvoiceOperationLogs = (invoiceId: number): Promise<InvoiceOperationLog[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟操作日志数据
        const mockLogs: InvoiceOperationLog[] = [
          {
            id: 1,
            operationType: 'VIEW_DETAIL',
            operator: '张三',
            operationTime: '2024-01-15 10:30:00',
            operationDescription: '查看发票详细信息',
            operationResult: 'SUCCESS',
            errorMessage: ''
          },
          {
            id: 2,
            operationType: 'PRINT',
            operator: '李四',
            operationTime: '2024-01-15 14:20:00',
            operationDescription: '打印发票PDF文件',
            operationResult: 'SUCCESS',
            errorMessage: ''
          },
          {
            id: 3,
            operationType: 'EMAIL_SEND',
            operator: '王五',
            operationTime: '2024-01-16 09:15:00',
            operationDescription: '向客户发送发票邮件',
            operationResult: 'FAILED',
            errorMessage: '邮箱地址无效'
          },
          {
            id: 4,
            operationType: 'EXPORT_DATA',
            operator: '赵六',
            operationTime: '2024-01-16 16:45:00',
            operationDescription: '导出发票数据为Excel格式',
            operationResult: 'SUCCESS',
            errorMessage: ''
          }
        ];
        resolve(mockLogs);
      }, 500);
    });
  } else {
    return request.get(`/invoices/${invoiceId}/operation-logs`).then((response: any) => {
      if (response && (response.code === "0" || response.code === "200")) {
        return response.result;
      }
      throw new Error(response.message || '获取操作日志失败');
    });
  }
};
