import request from '@/api';
import type { PaginationResponse, PartsSearchParameters, PartsListItem, PartsDetail, ApprovePartsPayload } from '@/types/module.d';
import { mockPartsList, mockPartsDetail, mockApproveParts } from '@/mock/data/parts';
import { shipPartialItems, shipAllItems } from '@/mock/data/partManagement';
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Parts Module')

/**
 * 获取零件列表
 * @param params 搜索参数
 */
export const getPartsList = (params: PartsSearchParameters): Promise<PaginationResponse<PartsListItem>> => {
  if (USE_MOCK_API) {
    return mockPartsList(params);
  } else {
    return request.get<any, PaginationResponse<PartsListItem>>('/parts/list', { params });
  }
};

/**
 * 获取零件详情
 * @param id 零件ID
 */
export const getPartsDetail = (id: string | number): Promise<PartsDetail> => {
  if (USE_MOCK_API) {
    return mockPartsDetail(id);
  } else {
    return request.get<any, PartsDetail>(`/parts/detail/${id}`);
  }
};

/**
 * 审批叫料单
 * @param payload 审批数据
 */
export const approveParts = (payload: ApprovePartsPayload): Promise<void> => {
  if (USE_MOCK_API) {
    return mockApproveParts(payload);
  } else {
    return request.post<any, void>('/parts/approve', payload);
  }
};

/**
 * 部分发货
 * @param requisitionNumber 叫料单号
 * @param shipmentDetails 发货详情
 */
export const shipPartsPartially = (
  requisitionNumber: string,
  shipmentDetails: Array<{partNumber: string, shippedQuantity: number}>
): Promise<any> => {
  if (USE_MOCK_API) {
    return Promise.resolve(shipPartialItems(requisitionNumber, shipmentDetails));
  } else {
    return request.post<any, any>('/parts/ship-partially', { requisitionNumber, shipmentDetails });
  }
};

/**
 * 完整发货
 * @param requisitionNumber 叫料单号
 */
export const shipPartsCompletely = (requisitionNumber: string): Promise<any> => {
  if (USE_MOCK_API) {
    return Promise.resolve(shipAllItems(requisitionNumber));
  } else {
    return request.post<any, any>('/parts/ship-completely', { requisitionNumber });
  }
};
