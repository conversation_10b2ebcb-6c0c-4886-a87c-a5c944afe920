import request from '@/api/index';
import type {
  QualityCheckListParams,
  QualityCheckListItem,
  QualityCheckDetail,
  QualityCheckSubmitForm,
  QualityCheckAuditForm,
  QualityCheckReworkForm,
  QualityCheckResponse,
  PaginationResponse,
  WorkOrderType
} from '@/types/module.d';
import {
  generateQualityCheckList,
  generateQualityCheckDetail,
  getQualityCheckTemplateData
} from '@/mock/data/qualityCheck';

const USE_MOCK_API = import.meta.env.VITE_APP_USE_MOCK_API === 'true';

/**
 * 获取质检单列表
 */
export const getQualityCheckList = (params: QualityCheckListParams): Promise<PaginationResponse<QualityCheckListItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const pageSize = params.pageSize || 20;
        const page = params.page || 1;
        const allData = generateQualityCheckList(100); // 生成100条数据
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const list = allData.slice(startIndex, endIndex);

        const result: PaginationResponse<QualityCheckListItem> = {
          list,
          total: allData.length,
          page,
          pageSize
        };
        resolve(result);
      }, 300);
    });
  } else {
    return request.get<any, PaginationResponse<QualityCheckListItem>>('/quality-check/list', { params });
  }
};

/**
 * 获取质检单详情
 */
export const getQualityCheckDetail = (id: string): Promise<QualityCheckDetail> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const detail = generateQualityCheckDetail(id);
        resolve(detail);
      }, 200);
    });
  } else {
    return request.get<any, QualityCheckDetail>(`/quality-check/detail/${id}`);
  }
};

/**
 * 提交质检结果
 */
export const submitQualityCheck = (data: QualityCheckSubmitForm): Promise<QualityCheckResponse> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '质检结果提交成功'
        });
      }, 500);
    });
  } else {
    return request.post<any, QualityCheckResponse>('/quality-check/submit', data);
  }
};

/**
 * 审核质检结果
 */
export const auditQualityCheck = (data: QualityCheckAuditForm): Promise<QualityCheckResponse> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: data.auditResult === 'passed' ? '质检审核通过' : '质检审核不通过，已返工'
        });
      }, 500);
    });
  } else {
    return request.post<any, QualityCheckResponse>('/quality-check/audit', data);
  }
};

/**
 * 处理返工
 */
export const processRework = (data: QualityCheckReworkForm): Promise<QualityCheckResponse> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '返工处理完成，已重新提交质检'
        });
      }, 600);
    });
  } else {
    return request.post<any, QualityCheckResponse>('/quality-check/rework', data);
  }
};

/**
 * 获取质检项目模板
 */
export const getQualityCheckTemplate = (workOrderType: string): Promise<any[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const template = getQualityCheckTemplateData(workOrderType as WorkOrderType);
        resolve(template);
      }, 200);
    });
  } else {
    return request.get<any, any[]>(`/quality-check/template/${workOrderType}`);
  }
};

/**
 * 获取质检统计信息
 */
export const getQualityCheckStats = (): Promise<any> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          todayTotal: 15,
          todayPassed: 12,
          todayFailed: 3,
          weekTotal: 87,
          weekPassed: 75,
          weekFailed: 12,
          passRate: 86.2
        });
      }, 300);
    });
  } else {
    return request.get<any, any>('/quality-check/stats');
  }
};

/**
 * 导出质检单数据
 */
export const exportQualityCheckData = (params: QualityCheckListParams): Promise<Blob> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 创建一个简单的CSV内容作为Mock
        const csvContent = '质检单号,工单号,技师,状态,创建时间\nQC202401001,WO202401001,张师傅,已完成,2024-01-15\n';
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        resolve(blob);
      }, 1000);
    });
  } else {
    return request.get<any, Blob>('/quality-check/export', {
      params,
      responseType: 'blob'
    });
  }
};

// 导出API对象
export const qualityCheckApi = {
  getQualityCheckList,
  getQualityCheckDetail,
  submitQualityCheck,
  auditQualityCheck,
  processRework,
  getQualityCheckTemplate,
  getQualityCheckStats,
  exportQualityCheckData
};
