import { http } from '@/utils/http'

// 登录请求参数类型
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应数据类型
export interface LoginResponse {
  token: string
  accessToken: string
  refreshToken?: string
  userInfo: {
    id: string
    username: string
    realName: string
    email?: string
    phone?: string
    avatar?: string
    roles: string[]
    permissions: string[]
  }
}

// 菜单信息类型
export interface MenuInfo {
  menuId: number
  menuCode: string
  menuName: string
  parentId: number | null
  menuType: string
  menuSide: string | null
  menuIcon: string | null
  menuPath: string | null
  component: string | null
  permission: string | null
  sortOrder: number
  isVisible: number | null
}

// 用户信息类型
export interface UserInfo {
  id: string
  username: string
  realName: string
  email?: string
  phone?: string
  userType?: string
  avatar?: string
  roles: string[]
  permissions: string[]
  menus?: MenuInfo[]
}

// API响应类型
export interface ApiResponse<T> {
  code: number | string
  message: string
  result: T
  timestamp: number
}

// 真实API可能的响应格式类型
interface RealApiResponse {
  // 标准格式
  code?: number | string
  message?: string
  result?: unknown
  data?: unknown
  timestamp?: number

  // 直接token格式
  token?: string
  accessToken?: string
  access_token?: string
  refreshToken?: string
  refresh_token?: string

  // 用户信息相关
  userInfo?: unknown
  user?: unknown
  userId?: string
  id?: string
  username?: string
  realName?: string
  name?: string
  email?: string
  phone?: string
  avatar?: string
  roles?: string[]
  permissions?: string[]

  // 成功标识
  success?: boolean | string

  // JWT相关
  authToken?: string
  jwt?: string

  // 其他可能的字段
  [key: string]: unknown
}

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Auth Module')

// Mock 数据
const mockUsers = [
  {
    id: '1',
    username: 'admin',
    password: '123456',
    realName: '管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    avatar: '',
    roles: ['admin'],
    permissions: ['*:*:*']
  },
  {
    id: '2',
    username: 'user',
    password: '123456',
    realName: '普通用户',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: '',
    roles: ['user'],
    permissions: ['system:user:view']
  }
]

/**
 * 用户登录
 */
export const login = (data: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      console.log('🔐 Mock登录API被调用，参数:', data)

      setTimeout(() => {
        const user = mockUsers.find(u => u.username === data.username && u.password === data.password)
        console.log('🔍 查找用户结果:', user ? '找到用户' : '用户不存在')

        if (user) {
          const token = `mock_token_${user.id}_${Date.now()}`
          const response: ApiResponse<LoginResponse> = {
            code: '200',
            message: '登录成功',
            result: {
              token,
              refreshToken: `refresh_${token}`,
              userInfo: {
                id: user.id,
                username: user.username,
                realName: user.realName,
                email: user.email,
                phone: user.phone,
                avatar: user.avatar,
                roles: user.roles,
                permissions: user.permissions
              }
            },
            timestamp: Date.now()
          }
          console.log('✅ Mock登录成功，返回数据:', response)
          resolve(response)
        } else {
          console.log('❌ Mock登录失败：用户名或密码错误')
          reject({
            code: '400',
            message: '用户名或密码错误',
            result: null,
            timestamp: Date.now()
          })
        }
      }, 1000) // 模拟网络延迟
    })
  }

    // 真实API调用 - 添加调试和类型处理
  return http.post<LoginRequest, RealApiResponse>('/auth/login', data)
    .then(response => {

      let finalResponse: ApiResponse<LoginResponse>

      // 处理API响应格式
      if (response.code === '200' && response.success) {
        finalResponse = response as ApiResponse<LoginResponse>
      }else{
        finalResponse = {
          code: response.code || '500',
          message: response.message || '登录失败',
          result: response.result,
          timestamp: Date.now()
        }
      }

      console.log('✅ 最终处理的响应数据:', finalResponse)
      console.log('🔑 提取的token:', finalResponse.result.accessToken)
      console.log('🔄 提取的refreshToken:', finalResponse.result.refreshToken)

      return finalResponse
    })
    .catch(error => {
      console.error('🌐 真实API调用失败:', error)
      let finalResponse: ApiResponse<LoginResponse>
      finalResponse = {
        code: error.response.data.code || '500',
        message: error.response.data.message || '登录失败',
        result: null,
        timestamp: Date.now()
      }
      return finalResponse
    })
}

/**
 * 用户退出登录
 */
export const logout = (): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: '200',
          message: '退出成功',
          result: undefined,
          timestamp: Date.now()
        })
      }, 300)
    })
  }

  return http.post('/auth/logout')
}

/**
 * 获取当前用户信息
 */
export const getUserInfo = (): Promise<ApiResponse<UserInfo>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const token = localStorage.getItem('token')
        if (token && token.startsWith('mock_token_')) {
          const userId = token.split('_')[2]
          const user = mockUsers.find(u => u.id === userId)

          if (user) {
            resolve({
              code: '200',
              message: '获取用户信息成功',
              result: {
                id: user.id,
                username: user.username,
                realName: user.realName,
                email: user.email,
                phone: user.phone,
                avatar: user.avatar,
                roles: user.roles,
                permissions: user.permissions
              },
              timestamp: Date.now()
            })
          } else {
            reject({
              code: '401',
              message: '用户信息获取失败',
              result: null,
              timestamp: Date.now()
            })
          }
        } else {
          reject({
            code: '401',
            message: '未登录或登录已过期',
            result: null,
            timestamp: Date.now()
          })
        }
      }, 300)
    })
  }

  // 真实API调用
  console.log('🌐 调用真实用户信息API')
  return http.post<void, ApiResponse<UserInfo>>('/auth/user-info')
    .then(response => {
      console.log('🌐 真实用户信息API响应:', response)

      // 确保响应格式正确
      if (response && typeof response === 'object') {
        // 如果响应没有包装在标准格式中，尝试处理
        if (!response.code && !response.result) {
          const wrappedResponse: ApiResponse<UserInfo> = {
            code: '200',
            message: '获取用户信息成功',
            result: response as unknown as UserInfo,
            timestamp: Date.now()
          }
          console.log('🔄 包装真实用户信息API响应:', wrappedResponse)
          return wrappedResponse
        }

        return response
      }

      throw new Error('真实用户信息API响应格式错误')
    })
    .catch(error => {
      console.error('🌐 真实用户信息API调用失败:', error)
      throw error
    })
}

/**
 * 刷新Token
 */
export const refreshToken = (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newToken = `mock_token_refresh_${Date.now()}`
        resolve({
          code: '200',
          message: 'Token刷新成功',
          result: {
            token: newToken,
            refreshToken: `refresh_${newToken}`
          },
          timestamp: Date.now()
        })
      }, 300)
    })
  }

  return http.post('/auth/refresh-token', { refreshToken })
}
