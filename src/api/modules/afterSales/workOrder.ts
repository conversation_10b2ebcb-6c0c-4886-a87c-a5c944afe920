// src/api/modules/afterSales/workOrder.ts

import request from '@/api';
import type { 
  WorkOrderSearchParams, 
  WorkOrderPageResponse, 
  WorkOrderDetail,
  WorkOrderFormData,
  WorkOrderStatus
} from '@/types/afterSales/workOrder.d.ts';
import { 
  getWorkOrderList as getMockWorkOrderList,
  getWorkOrderDetail as getMockWorkOrderDetail,
  createWorkOrder as createMockWorkOrder,
  updateWorkOrder as updateMockWorkOrder,
  deleteWorkOrder as deleteMockWorkOrder,
  changeWorkOrderStatus as changeMockWorkOrderStatus
} from '@/mock/data/afterSales/workOrder';

const USE_MOCK_API_TEMP = true;

export const getWorkOrderList = (params: WorkOrderSearchParams): Promise<WorkOrderPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockWorkOrderList(params);
  }
  return request.get<any, WorkOrderPageResponse>('/after-sales/work-orders', { params });
};

export const getWorkOrderDetail = (workOrderId: string): Promise<WorkOrderDetail> => {
  if (USE_MOCK_API_TEMP) {
    return getMockWorkOrderDetail(workOrderId);
  }
  return request.get<any, WorkOrderDetail>(`/after-sales/work-orders/${workOrderId}`);
};

export const createWorkOrder = (data: WorkOrderFormData): Promise<{ success: boolean; workOrderId: string }> => {
  if (USE_MOCK_API_TEMP) {
    return createMockWorkOrder(data);
  }
  return request.post<any, { success: boolean; workOrderId: string }>('/after-sales/work-orders', data);
};

export const updateWorkOrder = (workOrderId: string, data: Partial<WorkOrderFormData>): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return updateMockWorkOrder(workOrderId, data);
  }
  return request.put<any, { success: boolean }>(`/after-sales/work-orders/${workOrderId}`, data);
};

export const deleteWorkOrder = (workOrderId: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return deleteMockWorkOrder(workOrderId);
  }
  return request.delete<any, { success: boolean }>(`/after-sales/work-orders/${workOrderId}`);
};

export const changeWorkOrderStatus = (workOrderId: string, newStatus: WorkOrderStatus, reason?: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return changeMockWorkOrderStatus(workOrderId, newStatus, reason);
  }
  return request.patch<any, { success: boolean }>(`/after-sales/work-orders/${workOrderId}/status`, {
    status: newStatus,
    reason
  });
};
