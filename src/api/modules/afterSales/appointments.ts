// src/api/modules/afterSales/appointments.ts

import request from '@/api';
import type { AppointmentListParams, AppointmentPageResponse, AppointmentDetail } from '@/types/afterSales/appointments.d.ts';
import { getAppointmentList as getMockAppointmentList } from '@/mock/data/afterSales/appointments';
import { USE_MOCK_API } from '@/utils/mock-config';

const USE_MOCK_API_TEMP = true;

export const getAppointments = (params: AppointmentListParams): Promise<AppointmentPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockAppointmentList(params);
  }
  return request.get<any, AppointmentPageResponse>('/after-sales/appointments', { params });
};

export const getAppointmentDetail = (id: string): Promise<AppointmentDetail> => {
  if (USE_MOCK_API_TEMP) {
    // Mock implementation for appointment detail
    return new Promise((resolve) => {
      setTimeout(() => {
        // This would typically fetch from the mock data and enhance it
        // For now, we'll return a basic structure that matches the interface
        const mockDetail: AppointmentDetail = {
          id,
          licensePlate: '京A12345',
          reservationContactName: '张三',
          reservationContactPhone: '13800138001',
          serviceContactName: '张三',
          serviceContactPhone: '13800138001',
          appointmentTime: '2024-01-10',
          timeSlot: '09:00-10:00',
          serviceType: 'maintenance',
          status: 'arrived',
          vin: 'WBAKF91070FX12345',
          model: '奔驰C级',
          variant: 'C200L 运动版',
          color: '曜岩黑金属漆',
          mileage: 25000,
          createdAt: '2024-01-08 14:30:00',
          productionDate: '2022-03-15',
          store: {
            id: 'store_001',
            name: '北京朝阳店',
            address: '北京市朝阳区工体北路甲6号'
          },
          paymentStatus: 'paid',
          paymentAmount: 1500,
          paymentOrderNumber: 'PAY_' + Date.now(),
          maintenancePackage: {
            id: 'mp_001',
            code: 'PKG001',
            name: '基础保养套餐',
            totalAmount: 950,
            laborItems: [
              {
                code: 'L001',
                name: '更换机油机滤',
                standardHours: 1.0,
                unitPrice: 200,
                subtotal: 200
              },
              {
                code: 'L002',
                name: '全车检查',
                standardHours: 0.5,
                unitPrice: 100,
                subtotal: 50
              }
            ],
            partsItems: [
              {
                code: 'P001',
                name: '宝马原厂机油',
                quantity: 5,
                unit: 'L',
                unitPrice: 80,
                subtotal: 400
              },
              {
                code: 'P002',
                name: '原厂机油滤芯',
                quantity: 1,
                unit: '个',
                unitPrice: 150,
                subtotal: 150
              }
            ]
          }
        };
        resolve(mockDetail);
      }, 300);
    });
  }
  return request.get<any, AppointmentDetail>(`/after-sales/appointments/${id}`);
};

export const createQualityInspection = (appointmentId: string, data: any): Promise<any> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          qualityInspectionId: 'QI_' + Date.now(),
          message: '环检单创建成功'
        });
      }, 1000);
    });
  }
  return request.post<any, any>(`/after-sales/appointments/${appointmentId}/quality-inspection`, data);
};
