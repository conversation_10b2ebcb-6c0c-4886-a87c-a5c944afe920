// src/api/modules/afterSales/quota.ts

import request from '@/api';
import type { 
  QuotaSearchParams, 
  QuotaPageResponse, 
  QuotaConfigRequest,
  TimeSlot,
  StoreInfo 
} from '@/types/afterSales/quota.d.ts';
import { 
  getQuotaList as getMockQuotaList,
  getQuotaTimeSlots as getMockQuotaTimeSlots,
  saveQuotaConfig as saveMockQuotaConfig,
  getStoreInfo as getMockStoreInfo
} from '@/mock/data/afterSales/quota';
import { USE_MOCK_API } from '@/utils/mock-config';

const USE_MOCK_API_TEMP = true;

export const getQuotaList = (params: QuotaSearchParams): Promise<QuotaPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockQuotaList(params);
  }
  return request.get<any, QuotaPageResponse>('/after-sales/quota/list', { params });
};

export const getQuotaTimeSlots = (date: string): Promise<TimeSlot[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockQuotaTimeSlots(date);
  }
  return request.get<any, TimeSlot[]>(`/after-sales/quota/time-slots/${date}`);
};

export const saveQuotaConfig = (config: QuotaConfigRequest): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API_TEMP) {
    return saveMockQuotaConfig(config);
  }
  return request.post<any, { success: boolean; message: string }>('/after-sales/quota/config', config);
};

export const getStoreInfo = (): Promise<StoreInfo> => {
  if (USE_MOCK_API_TEMP) {
    return getMockStoreInfo();
  }
  return request.get<any, StoreInfo>('/after-sales/quota/store-info');
};
