// src/api/modules/afterSales/inspection.ts

import request from '@/api';
import type {
  InspectionSearchParams,
  InspectionPageResponse
} from '@/types/afterSales/inspection.d.ts';
import {
  getInspectionList as getMockInspectionList,
  submitForConfirm as submitMockForConfirm,
  recallInspection as recallMockInspection,
  customerConfirm as customerMockConfirm,
  createWorkOrder as createMockWorkOrder,
  getPrintableInspection as getMockPrintableInspection
} from '@/mock/data/afterSales/inspection';

const USE_MOCK_API_TEMP = true;

export const getInspectionList = (params: InspectionSearchParams): Promise<InspectionPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockInspectionList(params);
  }
  return request.get<any, InspectionPageResponse>('/after-sales/inspection/list', { params });
};



export const submitForConfirm = (inspectionNo: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return submitMockForConfirm(inspectionNo);
  }
  return request.post<any, { success: boolean }>(`/after-sales/inspection/${inspectionNo}/submit`);
};

export const recallInspection = (inspectionNo: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return recallMockInspection(inspectionNo);
  }
  return request.post<any, { success: boolean }>(`/after-sales/inspection/${inspectionNo}/recall`);
};

export const customerConfirm = (inspectionNo: string, confirmTime: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return customerMockConfirm(inspectionNo, confirmTime);
  }
  return request.post<any, { success: boolean }>(`/after-sales/inspection/${inspectionNo}/confirm`, {
    confirmTime
  });
};

export const createWorkOrder = (workOrderData: {
  inspectionNo: string;
  serviceType: string;
  priority: string;
  description: string;
  recommendedItems: Array<{ id: number; name: string; selected: boolean }>;
}): Promise<{ success: boolean; workOrderNo?: string }> => {
  if (USE_MOCK_API_TEMP) {
    return createMockWorkOrder(workOrderData);
  }
  return request.post<any, { success: boolean; workOrderNo?: string }>('/after-sales/inspection/create-work-order', workOrderData);
};

export const getPrintableInspection = (inspectionNo: string): Promise<{ html: string }> => {
  if (USE_MOCK_API_TEMP) {
    return getMockPrintableInspection(inspectionNo);
  }
  return request.get<any, { html: string }>(`/after-sales/inspection/${inspectionNo}/printable`);
};
