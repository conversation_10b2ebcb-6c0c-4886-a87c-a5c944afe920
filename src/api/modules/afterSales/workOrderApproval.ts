import request from '@/api';
import type {
  PendingApprovalListItem,
  CompletedApprovalListItem,
  WorkOrderApprovalListParams,
  ApprovalPageResponse,
  ClaimApprovalDetail,
  CancelApprovalDetail,
  WorkOrderApprovalRequest,
  WorkOrderApprovalResult,
  ExportApprovalParams,
  ApprovalStatistics
} from '@/types/afterSales/workOrderApproval';
import {
  getMockPendingApprovalList,
  getMockCompletedApprovalList,
  getMockClaimApprovalDetail,
  getMockCancelApprovalDetail,
  submitMockApproval,
  exportMockApprovalData,
  getMockApprovalStatistics
} from '@/mock/data/afterSales/workOrderApproval';
import { USE_MOCK_API } from '@/utils/mock-config';

const USE_MOCK_API_TEMP = true;
/**
 * 获取待审批列表
 */
export const getPendingApprovalList = (
  params: WorkOrderApprovalListParams
): Promise<ApprovalPageResponse<PendingApprovalListItem>> => {
  if (USE_MOCK_API_TEMP) {
    return getMockPendingApprovalList(params);
  }
  return request.get<any, ApprovalPageResponse<PendingApprovalListItem>>(
    '/after-sales/work-order-approval/pending',
    { params }
  );
};

/**
 * 获取已审批列表
 */
export const getCompletedApprovalList = (
  params: WorkOrderApprovalListParams
): Promise<ApprovalPageResponse<CompletedApprovalListItem>> => {
  if (USE_MOCK_API_TEMP) {
    return getMockCompletedApprovalList(params);
  }
  return request.get<any, ApprovalPageResponse<CompletedApprovalListItem>>(
    '/after-sales/work-order-approval/completed',
    { params }
  );
};

/**
 * 获取索赔审批详情
 */
export const getClaimApprovalDetail = (approvalNo: string): Promise<ClaimApprovalDetail> => {
  if (USE_MOCK_API_TEMP) {
    return getMockClaimApprovalDetail(approvalNo);
  }
  return request.get<any, ClaimApprovalDetail>(
    `/after-sales/work-order-approval/claim/${approvalNo}`
  );
};

/**
 * 获取取消审批详情
 */
export const getCancelApprovalDetail = (approvalNo: string): Promise<CancelApprovalDetail> => {
  if (USE_MOCK_API_TEMP) {
    return getMockCancelApprovalDetail(approvalNo);
  }
  return request.get<any, CancelApprovalDetail>(
    `/after-sales/work-order-approval/cancel/${approvalNo}`
  );
};

/**
 * 提交审批
 */
export const submitApproval = (data: WorkOrderApprovalRequest): Promise<WorkOrderApprovalResult> => {
  if (USE_MOCK_API_TEMP) {
    return submitMockApproval(data);
  }
  return request.post<any, WorkOrderApprovalResult>(
    '/after-sales/work-order-approval/submit',
    data
  );
};

/**
 * 导出审批数据
 */
export const exportApprovalData = (params: ExportApprovalParams): Promise<Blob> => {
  if (USE_MOCK_API_TEMP) {
    return exportMockApprovalData(params);
  }
  return request.get<any, Blob>(
    '/after-sales/work-order-approval/export',
    { 
      params,
      responseType: 'blob'
    }
  );
};

/**
 * 获取审批统计
 */
export const getApprovalStatistics = (): Promise<ApprovalStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockApprovalStatistics();
  }
  return request.get<any, ApprovalStatistics>(
    '/after-sales/work-order-approval/statistics'
  );
};

/**
 * 批量审批
 */
export const batchApproval = (data: {
  approvalNos: string[];
  approvalResult: 'approved' | 'rejected';
  approvalRemark?: string;
  rejectionReason?: string;
}): Promise<WorkOrderApprovalResult> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '批量审批成功'
        });
      }, 1000);
    });
  }
  return request.post<any, WorkOrderApprovalResult>(
    '/after-sales/work-order-approval/batch',
    data
  );
};

/**
 * 撤回审批
 */
export const withdrawApproval = (approvalNo: string, reason: string): Promise<WorkOrderApprovalResult> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '撤回成功'
        });
      }, 500);
    });
  }
  return request.post<any, WorkOrderApprovalResult>(
    `/after-sales/work-order-approval/withdraw/${approvalNo}`,
    { reason }
  );
};

/**
 * 获取审批历史
 */
export const getApprovalHistory = (approvalNo: string): Promise<any[]> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const historyData = [
          {
            id: '1',
            action: 'submit',
            actionTime: '2024-12-10 09:30:00',
            operator: '张三',
            operatorRole: '服务顾问',
            comments: '提交索赔审批申请',
            status: 'submitted'
          },
          {
            id: '2',
            action: 'first_level_review',
            actionTime: '2024-12-10 14:30:00',
            operator: '李四',
            operatorRole: '技师经理',
            comments: '一级审批通过',
            status: 'approved'
          }
        ];
        resolve(historyData);
      }, 300);
    });
  }
  return request.get<any, any[]>(`/after-sales/work-order-approval/history/${approvalNo}`);
};

/**
 * 获取审批配置
 */
export const getApprovalConfig = (): Promise<any> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          timeoutHours: 24,
          reminderHours: 2,
          autoEscalation: true,
          escalationHours: 48,
          approvalLevels: [
            { level: 'first_level', name: '一级审批', roles: ['technician_manager'] },
            { level: 'second_level', name: '二级审批', roles: ['factory_manager'] }
          ]
        });
      }, 300);
    });
  }
  return request.get<any, any>('/after-sales/work-order-approval/config');
};

/**
 * 更新审批配置
 */
export const updateApprovalConfig = (config: any): Promise<WorkOrderApprovalResult> => {
  if (USE_MOCK_API_TEMP) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '配置更新成功'
        });
      }, 500);
    });
  }
  return request.put<any, WorkOrderApprovalResult>(
    '/after-sales/work-order-approval/config',
    config
  );
};
