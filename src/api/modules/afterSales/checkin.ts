// src/api/modules/afterSales/checkin.ts

import request from '@/api';
import type {
  CheckinListParams,
  CheckinPageResponse,
  VehicleInfo,
  CheckinFormData,
  CancelCheckinParams,
  OwnerIcQueryParams
} from '@/types/afterSales/checkin.d.ts';
import { 
  getCheckinList as getMockCheckinList,
  queryVehicleInfo as queryMockVehicleInfo,
  addCheckinRecord as addMockCheckinRecord,
  updateCheckinRecord as updateMockCheckinRecord,
  deleteCheckinRecord as deleteMockCheckinRecord,
  createRelatedRepairOrder as createMockRelatedRepairOrder
} from '@/mock/data/afterSales/checkin';
import { USE_MOCK_API } from '@/utils/mock-config';

const USE_MOCK_API_TEMP = true;

export const getCheckinList = (params: CheckinListParams): Promise<CheckinPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockCheckinList(params);
  }
  return request.get<any, CheckinPageResponse>('/after-sales/checkin/list', { params });
};

export const queryVehicleInfo = (licensePlate: string): Promise<VehicleInfo | null> => {
  if (USE_MOCK_API_TEMP) {
    return queryMockVehicleInfo(licensePlate);
  }
  return request.get<any, VehicleInfo | null>(`/after-sales/checkin/vehicle-info/${licensePlate}`);
};

// 通过车主IC查询车辆信息
export const queryVehicleInfoByOwnerIc = (ownerIc: string): Promise<VehicleInfo | null> => {
  if (USE_MOCK_API_TEMP) {
    return queryMockVehicleInfo(ownerIc); // Mock中暂时复用同一个函数
  }
  return request.get<any, VehicleInfo | null>(`/after-sales/checkin/vehicle-info-by-ic/${ownerIc}`);
};

export const addCheckinRecord = (data: CheckinFormData): Promise<{ success: boolean; checkinId: string }> => {
  if (USE_MOCK_API_TEMP) {
    return addMockCheckinRecord(data);
  }
  return request.post<any, { success: boolean; checkinId: string }>('/after-sales/checkin', data);
};

export const updateCheckinRecord = (checkinId: string, data: Partial<CheckinFormData>): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return updateMockCheckinRecord(checkinId, data);
  }
  return request.put<any, { success: boolean }>(`/after-sales/checkin/${checkinId}`, data);
};

export const deleteCheckinRecord = (checkinId: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    return deleteMockCheckinRecord(checkinId);
  }
  return request.delete<any, { success: boolean }>(`/after-sales/checkin/${checkinId}`);
};

export const createRelatedRepairOrder = (checkinId: string): Promise<{ success: boolean; repairOrderId: string }> => {
  if (USE_MOCK_API_TEMP) {
    return createMockRelatedRepairOrder(checkinId);
  }
  return request.post<any, { success: boolean; repairOrderId: string }>(`/after-sales/checkin/${checkinId}/repair-order`);
};

// 取消登记
export const cancelCheckin = (checkinId: string, params: CancelCheckinParams): Promise<{ success: boolean }> => {
  if (USE_MOCK_API_TEMP) {
    // Mock实现
    return Promise.resolve({ success: true });
  }
  return request.post<any, { success: boolean }>(`/api/aftersales/checkin/${checkinId}/cancel`, params);
};
