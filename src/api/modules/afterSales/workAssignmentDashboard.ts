// src/api/modules/afterSales/workAssignmentDashboard.ts

import request from '@/api';
import type { 
  DashboardStatistics,
  TechnicianSchedule,
  WorkloadData,
  StatusDistribution,
  TechnicianPerformance,
  OrderFlowData,
  DepartmentWorkload,
  DashboardFilters
} from '@/types/afterSales/workAssignmentDashboard.d.ts';
import { 
  getDashboardStatistics as getMockDashboardStatistics,
  getTechnicianSchedules as getMockTechnicianSchedules,
  getWorkloadData as getMockWorkloadData,
  getStatusDistribution as getMockStatusDistribution,
  getTechnicianPerformance as getMockTechnicianPerformance,
  getOrderFlowData as getMockOrderFlowData,
  getDepartmentWorkload as getMockDepartmentWorkload
} from '@/mock/data/afterSales/workAssignmentDashboard';

const USE_MOCK_API_TEMP = true;

export const getDashboardStatistics = (): Promise<DashboardStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockDashboardStatistics();
  }
  return request.get<any, DashboardStatistics>('/after-sales/work-assignment/dashboard/statistics');
};

export const getTechnicianSchedules = (filters: DashboardFilters): Promise<TechnicianSchedule[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianSchedules(filters);
  }
  return request.get<any, TechnicianSchedule[]>('/after-sales/work-assignment/dashboard/schedules', { params: filters });
};

export const getWorkloadData = (): Promise<WorkloadData[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockWorkloadData();
  }
  return request.get<any, WorkloadData[]>('/after-sales/work-assignment/dashboard/workload');
};

export const getStatusDistribution = (): Promise<StatusDistribution[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockStatusDistribution();
  }
  return request.get<any, StatusDistribution[]>('/after-sales/work-assignment/dashboard/status-distribution');
};

export const getTechnicianPerformance = (): Promise<TechnicianPerformance[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianPerformance();
  }
  return request.get<any, TechnicianPerformance[]>('/after-sales/work-assignment/dashboard/performance');
};

export const getOrderFlowData = (): Promise<OrderFlowData[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockOrderFlowData();
  }
  return request.get<any, OrderFlowData[]>('/after-sales/work-assignment/dashboard/order-flow');
};

export const getDepartmentWorkload = (): Promise<DepartmentWorkload[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockDepartmentWorkload();
  }
  return request.get<any, DepartmentWorkload[]>('/after-sales/work-assignment/dashboard/department-workload');
};
