// src/api/modules/afterSales/dashboard.ts

import request from '@/api';
import type { DashboardSearchParams, DashboardResponse } from '@/types/afterSales/dashboard.d.ts';
import { getDashboardData as getMockDashboardData } from '@/mock/data/afterSales/dashboard';
import { USE_MOCK_API } from '@/utils/mock-config';

const USE_MOCK_API_TEMP = true;

export const getDashboardData = (params: DashboardSearchParams): Promise<DashboardResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockDashboardData(params);
  }
  return request.get<any, DashboardResponse>('/after-sales/dashboard', { params });
};
