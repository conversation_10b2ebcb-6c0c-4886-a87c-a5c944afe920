import request from '../index'
import type {
  SalesOrderListItem,
  SalesOrderListParams,
  SalesOrderDetail,
  SaveOrderRequest,
  PushInsuranceRequest,
  SubmitDeliveryRequest,
  AvailableRight,
  RightSearchParams,
  PaginationResponse,
  ApiResult,
  ApiPageResult
} from '@/types/module.d'
import { mockSalesOrderList, mockSalesOrderDetail, mockAvailableRights } from '@/mock/data/salesOrder'
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Order Module')

/**
 * 获取销售订单列表
 * @param params 查询参数
 * @returns 分页的销售订单列表
 */
export const getSalesOrderList = (params: SalesOrderListParams): Promise<PaginationResponse<SalesOrderListItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredData = [...mockSalesOrderList]

        // 筛选逻辑
        if (params.buyerName) {
          filteredData = filteredData.filter(item => item.buyerName.includes(params.buyerName!))
        }
        if (params.buyerPhone) {
          filteredData = filteredData.filter(item => item.buyerPhone.includes(params.buyerPhone!))
        }
        if (params.buyerType) {
          filteredData = filteredData.filter(item => item.buyerType === params.buyerType)
        }
        if (params.model) {
          filteredData = filteredData.filter(item => item.model.includes(params.model!))
        }
        if (params.orderNumber) {
          filteredData = filteredData.filter(item => item.orderNumber.includes(params.orderNumber!))
        }
        if (params.orderStatus) {
          filteredData = filteredData.filter(item => item.orderStatus === params.orderStatus)
        }
        if (params.approvalStatus) {
          filteredData = filteredData.filter(item => item.approvalStatus === params.approvalStatus)
        }
        if (params.paymentStatus) {
          filteredData = filteredData.filter(item => item.paymentStatus === params.paymentStatus)
        }
        if (params.insuranceStatus) {
          filteredData = filteredData.filter(item => item.insuranceStatus === params.insuranceStatus)
        }
        if (params.loanApprovalStatus) {
          filteredData = filteredData.filter(item => item.loanApprovalStatus === params.loanApprovalStatus)
        }
        if (params.jpjRegistrationStatus) {
          filteredData = filteredData.filter(item => item.jpjRegistrationStatus === params.jpjRegistrationStatus)
        }
        if (params.createTimeStart && params.createTimeEnd) {
          filteredData = filteredData.filter(item => {
            const createTime = new Date(item.createTime)
            return createTime >= new Date(params.createTimeStart!) && createTime <= new Date(params.createTimeEnd!)
          })
        }

        // 分页
        const page = params.page || 1
        const pageSize = params.pageSize || 10
        const total = filteredData.length
        const startIndex = (page - 1) * pageSize
        const endIndex = startIndex + pageSize
        const list = filteredData.slice(startIndex, endIndex)

        resolve({
          list,
          total,
          page,
          pageSize
        })
      }, 500)
    })
  } else {
    // 根据P03 API契约调整参数映射
    const apiParams = {
      pageNum: params.page || 1,
      pageSize: params.pageSize || 10,
      customerName: params.buyerName,
      customerPhone: params.buyerPhone,
      customerType: params.buyerType,
      model: params.model,
      orderNo: params.orderNumber,
      orderStatus: params.orderStatus,
      approvalStatus: params.approvalStatus,
      paymentStatus: params.paymentStatus,
      createTimeStart: params.createTimeStart,
      createTimeEnd: params.createTimeEnd,
      insuranceStatus: params.insuranceStatus,
      loanStatus: params.loanApprovalStatus,
      jpjRegistrationStatus: params.jpjRegistrationStatus
    }

    return request.get<any, ApiResult<ApiPageResult<SalesOrderListItem>>>('/sales/orders', { params: apiParams })
      .then(response => {
        // 根据P03 API契约调整响应数据结构
        if (response && response.result) {
          return {
            list: response.result.records || [],
            total: response.result.total || 0,
            page: apiParams.pageNum,
            pageSize: apiParams.pageSize
          }
        }
        return {
          list: [],
          total: 0,
          page: apiParams.pageNum,
          pageSize: apiParams.pageSize
        }
      })
  }
}

/**
 * 获取销售订单详情
 * @param orderNo 订单号（根据P03 API契约调整为orderNo）
 * @returns 订单详情
 */
export const getSalesOrderDetail = (orderNo: string): Promise<SalesOrderDetail> => {
  console.log('getSalesOrderDetail', USE_MOCK_API)
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockSalesOrderDetail)
      }, 300)
    })
  } else {
    return request.get<any, ApiResult<SalesOrderDetail>>(`/sales/orders/${orderNo}`)
      .then(response => {
        // 根据P03 API契约调整响应数据结构
        if (response && response.result) {
          return response.result
        }
        throw new Error('订单详情获取失败')
      })
  }
}

/**
 * 保存订单（编辑）
 * @param orderData 订单数据
 * @returns 保存结果
 */
export const saveSalesOrder = (orderData: SaveOrderRequest): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 更新mock数据中的订单信息
        if (mockSalesOrderDetail.orderNumber === orderData.orderNo) {
          // 更新字段
          if (orderData.color) mockSalesOrderDetail.color = orderData.color
          if (orderData.paymentMethod) mockSalesOrderDetail.paymentMethod = orderData.paymentMethod
          if (orderData.loanAmount !== undefined) mockSalesOrderDetail.loanAmount = orderData.loanAmount
          if (orderData.loanApprovalStatus) mockSalesOrderDetail.loanApprovalStatus = orderData.loanApprovalStatus
          if (orderData.insuranceNotes) mockSalesOrderDetail.insuranceNotes = orderData.insuranceNotes
        }

        // 模拟颜色变更需要审核的逻辑
        if (orderData.color) {
          resolve({
            success: true,
            message: '您已更改车辆颜色，已提交审核'
          })
        } else {
          resolve({
            success: true,
            message: '订单保存成功'
          })
        }
      }, 800)
    })
  } else {
    return request.put<any, ApiResult<null>>(`/sales/orders/${orderData.orderNo}`, orderData)
      .then(response => {
        if (response && response.success) {
          return {
            success: true,
            message: response.message || '更新成功'
          }
        }
        throw new Error('订单保存失败')
      })
  }
}

/**
 * 推送至保险系统
 * @param data 推送数据
 * @returns 推送结果
 */
export const pushToInsuranceSystem = (data: PushInsuranceRequest): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '已成功推送至保险系统'
        })
      }, 1000)
    })
  } else {
    return request.post<any, ApiResult<null>>(`/sales/orders/${data.orderNo}/insurance/push`)
      .then(response => {
        if (response && response.success) {
          return {
            success: true,
            message: response.message || '推送成功'
          }
        }
        throw new Error('推送至保险系统失败')
      })
  }
}

/**
 * 提交交车
 * @param data 提交交车数据
 * @returns 提交结果
 */
export const submitDelivery = (data: SubmitDeliveryRequest): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '交车申请提交成功'
        })
      }, 800)
    })
  } else {
    return request.post<any, ApiResult<null>>(`/sales/orders/${data.orderNo}/delivery`)
      .then(response => {
        if (response && response.success) {
          return {
            success: true,
            message: response.message || '提交成功'
          }
        }
        throw new Error('提交交车失败')
      })
  }
}

/**
 * 获取可选权益列表
 * @param params 搜索参数
 * @returns 权益列表
 */
export const getAvailableRights = (params: RightSearchParams): Promise<PaginationResponse<AvailableRight>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredData = [...mockAvailableRights]

        // 筛选逻辑
        if (params.rightCode) {
          filteredData = filteredData.filter(item => item.rightCode.includes(params.rightCode!))
        }
        if (params.rightName) {
          filteredData = filteredData.filter(item => item.rightName.includes(params.rightName!))
        }

        // 分页
        const page = params.page || 1
        const pageSize = params.pageSize || 10
        const total = filteredData.length
        const startIndex = (page - 1) * pageSize
        const endIndex = startIndex + pageSize
        const list = filteredData.slice(startIndex, endIndex)

        resolve({
          list,
          total,
          page,
          pageSize
        })
      }, 300)
    })
  } else {
    // 根据P03 API契约调整参数映射 - 改为POST请求
    const apiParams = {
      pageNum: params.page || 1,
      pageSize: params.pageSize || 10,
      code: params.rightCode,
      name: params.rightName
    }

    return request.post<any, ApiResult<AvailableRight[] | ApiPageResult<AvailableRight>>>('/rights/available', apiParams)
      .then(response => {
        // 根据P03 API契约调整响应数据结构
        if (response && response.result) {
          // 检查result是否是数组（直接返回list）还是分页对象（包含list和total）
          return response.result;
        }
        return {
          records: [],
          total: 0,
          page: apiParams.pageNum,
          pageSize: apiParams.pageSize
        }
      })
  }
}

/**
 * 添加权益到订单
 * @param orderId 订单ID
 * @param rightIds 权益ID列表
 * @returns 添加结果
 */
export const addRightsToOrder = (orderId: string, rightIds: string[]): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '权益添加成功'
        })
      }, 500)
    })
  } else {
    return request.post<any, { success: boolean; message: string }>(`/sales-orders/${orderId}/rights`, { rightIds })
  }
}

/**
 * 从订单中移除权益
 * @param orderId 订单ID
 * @param rightId 权益ID
 * @returns 移除结果
 */
export const removeRightFromOrder = (orderId: string, rightId: string): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '权益移除成功'
        })
      }, 300)
    })
  } else {
    return request.delete<any, { success: boolean; message: string }>(`/sales-orders/${orderId}/rights/${rightId}`)
  }
}

/**
 * 导出销售订单数据
 * @param params 导出参数
 * @returns Excel文件流
 */
export const exportSalesOrderData = (params: SalesOrderListParams): Promise<Blob> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟导出文件
        const csvContent = 'Order Number,Buyer Name,Model,Status\n' +
          'ORD001,张三,Model A,已提交\n' +
          'ORD002,李四,Model B,已确认\n'
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
        resolve(blob)
      }, 1000)
    })
  } else {
    return request.post<any, Blob>('/sales/orders/export', params, {
      responseType: 'blob'
    })
  }
}

// 为了兼容性，添加别名函数
export const getOrderDetail = getSalesOrderDetail
export const getOrderList = getSalesOrderList
export const getOrderStats = () => {
  return Promise.resolve({
    totalOrders: 100,
    pendingOrders: 20,
    completedOrders: 80
  })
}
export const saveOrder = saveSalesOrder
export const pushInsurance = pushToInsuranceSystem
export const exportOrderData = exportSalesOrderData
