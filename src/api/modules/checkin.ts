import request from '@/api';
import { mockCheckinList } from '@/mock/data/checkin';
import { i18nGlobal } from '@/plugins/i18n';
import type { CheckinListItem, CheckinListParams, PaginationResponse } from '@/types/module.d.ts';
import { ElMessage } from 'element-plus';

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('Checkin Module')

/**
 * @description 获取到店登记单列表
 * @param params 查询参数 (CheckinListParams)
 * @returns Promise<PaginationResponse<CheckinListItem>> 到店登记单列表和总数
 */
export const getCheckinList = (params: CheckinListParams): Promise<PaginationResponse<CheckinListItem>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { page = 1, pageSize = 10, ...filter } = params;

        const filteredList = mockCheckinList.filter(item => {
          const matchesCheckinId = !filter.checkinId || item.checkinId.toLowerCase().includes(filter.checkinId.toLowerCase());
          const matchesLicensePlate = !filter.licensePlate || item.licensePlate.toLowerCase().includes(filter.licensePlate.toLowerCase());
          const matchesRepairPersonName = !filter.repairPersonName || item.repairPersonName.toLowerCase().includes(filter.repairPersonName.toLowerCase());
          const matchesRepairPersonPhone = !filter.repairPersonPhone || item.repairPersonPhone.includes(filter.repairPersonPhone);
          const matchesCreateTime = !filter.createdAtStart || !filter.createdAtEnd ||
                                     (item.createdAt >= filter.createdAtStart && item.createdAt <= filter.createdAtEnd);
          const notDeleted = !item.isDeleted; // 逻辑删除标志

          return matchesCheckinId && matchesLicensePlate && matchesRepairPersonName && matchesRepairPersonPhone && matchesCreateTime && notDeleted;
        });

        const total = filteredList.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const list = filteredList.slice(start, end);

        resolve({
          list,
          total,
          page,
          pageSize
        });
      }, 500);
    });
  } else {
    return request.get<any, PaginationResponse<CheckinListItem>>('/checkin/records', { params });
  }
};

/**
 * @description 添加新到店登记单
 * @param data 登记单数据
 * @returns Promise<boolean> 操作成功与否
 */
export const addCheckinRecord = (data: CheckinListItem): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newId = (mockCheckinList.length + 1).toString();
        mockCheckinList.push({
          ...data,
          checkinId: newId,
          createdAt: new Date().toISOString().split('T')[0],
          updatedAt: new Date().toISOString().split('T')[0],
          isDeleted: false,
          serviceType: data.serviceType || '维修'
        });
        console.log('Mock: Successfully added new checkin record:', data);
        ElMessage.success(i18nGlobal.t('common.operationSuccessful'));
        resolve(true);
      }, 300);
    });
  } else {
    return request.post<any, boolean>('/checkin/records', data);
  }
};

/**
 * @description 更新到店登记单信息
 * @param id 登记单ID
 * @param data 登记单更新数据
 * @returns Promise<boolean> 操作成功与否
 */
export const updateCheckinRecord = (id: string, data: Partial<CheckinListItem>): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockCheckinList.findIndex(v => v.checkinId === id);
        if (index !== -1) {
          mockCheckinList[index] = { ...mockCheckinList[index], ...data, updatedAt: new Date().toISOString().split('T')[0] };
          console.log('Mock: Successfully updated checkin record:', id, data);
          ElMessage.success(i18nGlobal.t('common.operationSuccessful'));
          resolve(true);
        } else {
          console.error('Mock: Checkin record not found for update:', id);
          ElMessage.error(i18nGlobal.t('common.operationFailed') + ': ' + i18nGlobal.t('checkin.notFound'));
          reject(new Error('Checkin record not found'));
        }
      }, 300);
    });
  } else {
    return request.put<any, boolean>(`/checkin/records/${id}`, data);
  }
};

/**
 * @description 逻辑删除到店登记单
 * @param id 登记单ID
 * @returns Promise<boolean> 操作成功与否
 */
export const deleteCheckinRecord = (id: string): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockCheckinList.findIndex(v => v.checkinId === id);
        if (index !== -1) {
          mockCheckinList[index].isDeleted = true; // 逻辑删除
          console.log('Mock: Successfully logically deleted checkin record:', id);
          ElMessage.success(i18nGlobal.t('common.operationSuccessful'));
          resolve(true);
        } else {
          console.error('Mock: Checkin record not found for deletion:', id);
          ElMessage.error(i18nGlobal.t('common.operationFailed') + ': ' + i18nGlobal.t('checkin.notFound'));
          reject(new Error('Checkin record not found'));
        }
      }, 300);
    });
  } else {
    return request.delete<any, boolean>(`/checkin/records/${id}`);
  }
};

/**
 * @description 创建关联环检单 (这里只模拟前端调用，实际后端可能需要更多逻辑)
 * @param checkinId 关联的到店登记单ID
 * @returns Promise<boolean> 操作成功与否
 */
export const createRelatedRepairOrder = (checkinId: string): Promise<boolean> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const index = mockCheckinList.findIndex(v => v.checkinId === checkinId);
        if (index !== -1) {
          if (mockCheckinList[index].relatedRepairOrderId) {
            ElMessage.warning(i18nGlobal.t('checkin.repairOrderAlreadyExists'));
            reject(new Error('Repair order already exists for this checkin record.'));
            return;
          }
          // 模拟生成一个新的环检单ID并关联
          const newRepairOrderId = 'RO' + (Math.floor(Math.random() * 90000) + 10000).toString();
          mockCheckinList[index].relatedRepairOrderId = newRepairOrderId;
          console.log('Mock: Successfully created related repair order:', newRepairOrderId, 'for checkin:', checkinId);
          ElMessage.success(i18nGlobal.t('checkin.repairOrderCreatedSuccess'));
          resolve(true);
        } else {
          console.error('Mock: Checkin record not found for creating repair order:', checkinId);
          ElMessage.error(i18nGlobal.t('common.operationFailed') + ': ' + i18nGlobal.t('checkin.notFound'));
          reject(new Error('Checkin record not found'));
        }
      }, 500);
    });
  } else {
    // 实际的后端接口可能需要更多数据
    return request.post<any, boolean>(`/checkin/records/${checkinId}/create-repair-order`, {});
  }
};

/**
 * @description 查询已销售车辆信息 (用于新增/编辑弹窗的车牌号查询)
 * @param licensePlate 车牌号
 * @returns Promise<any> 车辆信息，或 null
 */
export const queryVehicleInfo = (licensePlate: string): Promise<any | null> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟已销售车辆数据，这里可以根据具体业务逻辑填充更多车辆信息
        const soldVehicles = [
          {
            licensePlate: '粤B12345',
            vin: 'LSGKC53F4GA000001',
            vehicleModel: 'Model X',
            vehicleConfiguration: '2023款 Performance',
            color: 'Pearl White',
            mileage: 15000,
            deliveryDate: '2022-04-01',
            ownerInfo: '张三',
            ownerPhone: '13800138000'
          },
          {
            licensePlate: '沪A67890',
            vin: 'LSGKC53F4GA000002',
            vehicleModel: 'Model 3',
            vehicleConfiguration: '2022款 长续航版',
            color: 'Deep Blue',
            mileage: 25000,
            deliveryDate: '2022-01-15',
            ownerInfo: '李四',
            ownerPhone: '13912345678'
          },
          {
            licensePlate: '京C54321',
            vin: 'LHGCM55438A000003',
            vehicleModel: 'Civic',
            vehicleConfiguration: '2024款 1.5T CVT豪华版',
            color: 'Crystal Black',
            mileage: 8000,
            deliveryDate: '2023-05-10',
            ownerInfo: '王五',
            ownerPhone: '13787654321'
          },
          {
            licensePlate: '苏E98765',
            vin: 'JTNBH46K3A9000004',
            vehicleModel: 'Camry',
            vehicleConfiguration: '2023款 2.0L CVT豪华版',
            color: 'Silver',
            mileage: 12000,
            deliveryDate: '2022-10-20',
            ownerInfo: '赵六',
            ownerPhone: '13654321098'
          },
          {
            licensePlate: '浙G01010',
            vin: 'WBAVB13506PT00005',
            vehicleModel: 'X5',
            vehicleConfiguration: '2024款 xDrive40i M运动套装',
            color: 'Metallic Gray',
            mileage: 5000,
            deliveryDate: '2023-10-01',
            ownerInfo: '孙七',
            ownerPhone: '13509876543'
          },
        ];
        const foundVehicle = soldVehicles.find(v => v.licensePlate === licensePlate);
        if (foundVehicle) {
          // 模拟计算车龄 (以月为单位)
          const deliveryDate = new Date(foundVehicle.deliveryDate);
          const now = new Date();
          const ageInMonths = (now.getFullYear() - deliveryDate.getFullYear()) * 12 + (now.getMonth() - deliveryDate.getMonth());

          resolve({ ...foundVehicle, vehicleAge: ageInMonths });
        } else {
          resolve(null); // 未找到
        }
      }, 300);
    });
  } else {
    return request.get<any, any | null>(`/vehicles/query-by-license-plate/${licensePlate}`);
  }
};
