import request from '@/api';
import type {
  Department,
  CreateDepartmentRequest,
  UpdateDepartmentRequest,
  DepartmentSearchParams,
  DepartmentPageResponse,
  ApiResponse
} from '@/types/base/department';
import {
  getDepartmentList as getMockDepartmentList,
  getDepartmentDetail as getMockDepartmentDetail,
  addDepartment as addMockDepartment,
  updateDepartment as updateMockDepartment,
  deleteDepartment as deleteMockDepartment
} from '@/mock/data/base/department';
import { USE_MOCK_API } from '@/utils/mock-config';

/**
 * 获取部门分页列表
 * @param params 搜索参数
 * @returns 部门列表响应
 */
export const getDepartmentPage = (params: DepartmentSearchParams): Promise<ApiResponse<DepartmentPageResponse>> => {
  console.log('🌐 API调用 - getDepartmentPage，参数:', params);
  console.log('🔧 Mock API状态:', USE_MOCK_API ? '启用' : '禁用');
  console.log('🔍 查询参数详细分析:', {
    '是否有搜索条件': !!(params.departmentName || params.departmentCode || params.departmentStatus),
    '部门名称': params.departmentName || '(空)',
    '部门编码': params.departmentCode || '(空)',
    '部门状态': params.departmentStatus || '(空)',
    '部门类型': params.departmentType || '(空)',
    '分页信息': {
      current: params.current || 1,
      size: params.size || 10
    }
  });

  if (USE_MOCK_API) {
    return getMockDepartmentList(params);
  }
  // ✅ 保持与门店管理一致的API接口风格
  console.log('📡 发送请求到后端: POST /departments/page');
  console.log('📡 请求URL:', `${import.meta.env.VITE_APP_BASE_API}/departments/page`);
  console.log('📡 请求体:', JSON.stringify(params, null, 2));

  return request.post<any, ApiResponse<DepartmentPageResponse>>('/departments/page', params);
};

/**
 * 获取部门详情
 * @param id 部门ID
 * @returns 部门详情响应
 */
export const getDepartmentDetail = (id: string): Promise<ApiResponse<Department>> => {
  if (USE_MOCK_API) {
    return getMockDepartmentDetail(id);
  }
  // ✅ 保持与门店管理一致的API接口风格
  return request.get<any, ApiResponse<Department>>(`/departments/detail`, { params: { id: id } });
};

/**
 * 新增部门
 * @param data 部门数据
 * @returns 新增结果
 */
export const addDepartment = (data: CreateDepartmentRequest): Promise<ApiResponse<Department>> => {
  console.log('🔍 API模块 - addDepartment调用，数据:', data);
  console.log('🔍 API模块 - addDepartment的sortOrder:', data.sortOrder);

  if (USE_MOCK_API) {
    return addMockDepartment(data);
  }
  // ✅ 保持与门店管理一致的API接口风格
  return request.post<any, ApiResponse<Department>>('/departments/create', data);
};

/**
 * 更新部门
 * @param data 部门数据
 * @returns 更新结果
 */
export const updateDepartment = (data: UpdateDepartmentRequest): Promise<ApiResponse<Department>> => {
  console.log('🔍 API模块 - updateDepartment调用，数据:', data);
  console.log('🔍 API模块 - updateDepartment的sortOrder:', data.sortOrder);

  if (USE_MOCK_API) {
    return updateMockDepartment(data);
  }
  // ✅ 保持与门店管理一致的API接口风格
  return request.post<any, ApiResponse<Department>>('/departments/update', data);
};

/**
 * 删除部门
 * @param id 部门ID
 * @returns 删除结果
 */
export const deleteDepartment = (id: string): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return deleteMockDepartment(id);
  }
  // ✅ 保持与门店管理一致的API接口风格
  return request.get<any, ApiResponse<void>>(`/departments/delete`, { params: { id: id } });
};
