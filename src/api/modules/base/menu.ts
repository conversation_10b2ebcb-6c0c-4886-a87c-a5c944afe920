/**
 * 菜单管理API模块
 */

import request from '@/api';
import type { Menu, MenuSearchParams, MenuPageResponse, CreateMenuRequest, UpdateMenuRequest, ApiResponse } from '@/types/base/menu';

// Mock数据导入
import {
  getMenuList as getMockMenuList,
  getMenuTree as getMockMenuTree,
  getMenuDetail as getMockMenuDetail,
  addMenu as addMockMenu,
  updateMenu as updateMockMenu,
  deleteMenu as deleteMockMenu
} from '@/mock/data/base/menu';
import { USE_MOCK_API } from '@/utils/mock-config';


/**
 * 获取菜单分页列表
 * @param params 查询参数
 * @returns 菜单分页数据
 */
export const getMenuList = (params: MenuSearchParams): Promise<ApiResponse<MenuPageResponse>> => {
  if (USE_MOCK_API) {
    return getMockMenuList(params);
  }
  return request.post<any, ApiResponse<MenuPageResponse>>('/menus/page', { params });
};

/**
 * 获取菜单树
 * @returns 菜单树数据
 */
export const getMenuTree = (): Promise<ApiResponse<Menu[]>> => {
  if (USE_MOCK_API) {
    return getMockMenuTree();
  }
  return request.get<any, ApiResponse<Menu[]>>('/menus/tree');
};

/**
 * 获取菜单详情
 * @param id 菜单ID
 * @returns 菜单详情
 */
export const getMenuDetail = (id: string | number): Promise<ApiResponse<Menu>> => {
  if (USE_MOCK_API) {
    return getMockMenuDetail(String(id));
  }
  return request.get<any, ApiResponse<Menu>>(`/menus/detail`, { params: { id } });
};

/**
 * 新增菜单
 * @param data 菜单数据
 * @returns 新增结果
 */
export const addMenu = (data: CreateMenuRequest): Promise<ApiResponse<Menu>> => {
  if (USE_MOCK_API) {
    return addMockMenu(data);
  }
  return request.post<any, ApiResponse<Menu>>('/menus/create', data);
};

/**
 * 更新菜单
 * @param data 菜单数据
 * @returns 更新结果
 */
export const updateMenu = (data: UpdateMenuRequest): Promise<ApiResponse<Menu>> => {
  if (USE_MOCK_API) {
    return updateMockMenu(data);
  }
  return request.post<any, ApiResponse<Menu>>('/menus/update', data);
};

/**
 * 删除菜单
 * @param id 菜单ID
 * @returns 删除结果
 */
export const deleteMenu = (id: string | number): Promise<ApiResponse<null>> => {
  if (USE_MOCK_API) {
    return deleteMockMenu(String(id));
  }
  return request.delete<any, ApiResponse<null>>(`/menus/delete/${id}`);
};
