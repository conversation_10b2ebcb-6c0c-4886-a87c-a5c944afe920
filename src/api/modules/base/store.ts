import request from '@/api';
import type {
  StoreSearchParams,
  StorePageResponse,
  StoreItem,
  CreateStoreRequest,
  UpdateStoreRequest,
  ApiResponse,
} from '@/types/base/store';
import {
  getStoreList as getMockStoreList,
  getStoreDetail as getMockStoreDetail,
  addStore as addMockStore,
  updateStore as updateMockStore,
  deleteStore as deleteMockStore
} from '@/mock/data/base/store';
import { USE_MOCK_API } from '@/utils/mock-config';

/**
 * 获取门店列表
 * @param params 搜索参数
 * @returns 门店列表响应
 */
export const getStorePage = (params: StoreSearchParams): Promise<ApiResponse<StorePageResponse>> => {
  console.log('🌐 API调用 - getStorePage，参数:', params);
  console.log('🔧 Mock API状态:', USE_MOCK_API ? '启用' : '禁用');
  console.log('🔍 查询参数详细分析:', {
    '是否有搜索条件': !!(params.storeName || params.storeCode || params.storeStatus),
    '门店名称': params.storeName || '(空)',
    '门店编码': params.storeCode || '(空)',
    '门店状态': params.storeStatus || '(空)',
    '分页信息': {
      current: params.current || 1,
      size: params.size || 10
    }
  });

  if (USE_MOCK_API) {
    return getMockStoreList(params);
  }
  // 保持原有API接口不变
  console.log('📡 发送请求到后端: POST /stores/page');
  console.log('📡 请求URL:', `${import.meta.env.VITE_APP_BASE_API}/stores/page`);
  console.log('📡 请求体:', JSON.stringify(params, null, 2));

  return request.post<any, ApiResponse<StorePageResponse>>('/stores/page', params);
};

/**
 * 获取门店详情
 * @param id 门店ID
 * @returns 门店详情响应
 */
export const getStoreDetail = (id: string): Promise<ApiResponse<StoreItem>> => {
  if (USE_MOCK_API) {
    return getMockStoreDetail(id);
  }
  // 保持原有API接口不变
  return request.get<any, ApiResponse<StoreItem>>(`/stores/detail`, { params: { id: id } });
};

/**
 * 新增门店
 * @param data 门店数据
 * @returns 新增结果
 */
export const addStore = (data: CreateStoreRequest): Promise<ApiResponse<StoreItem>> => {
  if (USE_MOCK_API) {
    return addMockStore(data);
  }
  // 保持原有API接口不变
  return request.post<any, ApiResponse<StoreItem>>('/stores/create', data);
};

/**
 * 更新门店
 * @param data 门店数据
 * @returns 更新结果
 */
export const updateStore = (data: UpdateStoreRequest): Promise<ApiResponse<StoreItem>> => {
  if (USE_MOCK_API) {
    return updateMockStore(data);
  }
  // 保持原有API接口不变
  return request.post<any, ApiResponse<StoreItem>>('/stores/update', data);
};

/**
 * 删除门店
 * @param id 门店ID
 * @returns 删除结果
 */
export const deleteStore = (id: string): Promise<ApiResponse<void>> => {
  if (USE_MOCK_API) {
    return deleteMockStore(id);
  }
  // 保持原有API接口不变
  return request.get<any, ApiResponse<void>>(`/stores/delete`, { params: { id: id } });
};

// 注意：门店状态和门店属性字典现在通过统一的字典系统获取
// 使用 DICTIONARY_TYPES.COMMON_STATUS ('0002') 和 DICTIONARY_TYPES.STORE_PROPERTIES ('0200')
// 请在组件中使用 useDictionary 或 DictionarySelect 组件
