// 潜客管理API模块
import request from '@/api'
import type {
  PaginationResponse,
  ProspectBaseInfo,
  GetStoreProspectListRequest,
  ExistingLeadInfo,
  SearchExistingLeadRequest,
  AddStoreProspectRequest,
  RecordFollowUpRequest
} from '@/types/prospective-customer.d'
import { SourceChannel, ProspectLevel, ProspectStatus } from '@/types/prospective-customer.d'

// 获取环境变量，用于判断是否使用本地 Mock 数据
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('ProspectiveCustomer Module')

// 定义API响应结构
interface ApiResponse<T> {
  result: T;
  code: number;
  message: string;
}

/**
 * 获取潜客列表
 */
export const getProspectList = async (params: GetStoreProspectListRequest): Promise<ApiResponse<PaginationResponse<ProspectBaseInfo>>> => {
  if (USE_MOCK_API) {
    // Mock 数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData: PaginationResponse<ProspectBaseInfo> = {
          records: [
            {
              id: 1,
              customerName: '张三',
              customerPhone: '13800138001',
              sourceChannel: SourceChannel.OFFICIAL_WEBSITE,
              customerLevel: ProspectLevel.A,
              customerStatus: ProspectStatus.FOLLOWING,
              intentModel: 'Perodua Myvi',
              intentVariant: 'Premium',
              intentColor: '白色',
              salesConsultant: '李顾问',
              lastFollowUpTime: '2024-06-15 10:30:00',
              nextFollowUpTime: '2024-06-20 14:00:00',
              createTime: '2024-06-10 09:00:00'
            }
          ],
          total: 1,
          page: params.page,
          pageSize: params.pageSize
        }
        resolve({
          result: mockData,
          code: 0,
          message: '操作成功'
        });
      }, 500)
    })
  } else {
    // 过滤掉空值参数
    const filteredParams = Object.entries(params).reduce((acc, [key, value]) => {
      // 如果值不为空字符串、null或undefined，则添加到参数对象中
      if (value !== '' && value !== null && value !== undefined) {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, unknown>)

    // 直接传递过滤后的参数
    return await request.post('/prospects/store/list', filteredParams);
  }
}

/**
 * 搜索线索
 */
export const searchExistingLead = async (params: SearchExistingLeadRequest): Promise<{ data: ExistingLeadInfo | null }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockLead: ExistingLeadInfo = {
          id: 1,
          customerName: params.customerName || '张三',
          customerPhone: params.customerPhone || '13800138001',
          email: '<EMAIL>',
          sourceChannel: SourceChannel.OFFICIAL_WEBSITE
        }
        resolve({ data: mockLead })
      }, 300)
    })
  } else {
    // 过滤掉空值参数
    const filteredParams = Object.entries(params).reduce((acc, [key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        (acc as Record<string, unknown>)[key] = value
      }
      return acc
    }, {} as Record<string, unknown>)

    const response = await request.post<{ result: ExistingLeadInfo | null }>('/prospects/store/leads/search', filteredParams)
    return { data: response.result || null }
  }
}

/**
 * 新增潜客
 */
export const addStoreProspect = async (data: AddStoreProspectRequest): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Mock: 新增潜客成功', data)
        resolve()
      }, 300)
    })
  } else {
    await request.post('/prospects/store/add', data)
  }
}

/**
 * 记录跟进
 */
export const recordFollowUp = async (data: RecordFollowUpRequest): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Mock: 跟进记录保存成功', data)
        resolve()
      }, 300)
    })
  } else {
    // 过滤掉空值参数
    const filteredData = Object.entries(data).reduce((acc, [key, value]) => {
      if (value !== '' && value !== null && value !== undefined) {
        (acc as Record<string, unknown>)[key] = value
      }
      return acc
    }, {} as Record<string, unknown>)

    await request.post('/prospects/store/followUp', filteredData)
  }
}

/**
 * 提交战败申请
 */
export const applyDefeat = async (data: {
  prospectId: number;
  defeatReason: string;
  defeatDescription: string;
}): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Mock: 战败申请提交成功', data)
        resolve()
      }, 500)
    })
  } else {
    await request.post('/prospects/store/defeat/apply', data)
  }
}

/**
 * 获取潜客详情
 */
export const getProspectDetail = async (storeProspectId: number): Promise<ApiResponse<any>> => {
  if (USE_MOCK_API) {
    // Mock 数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData = {
          prospectBaseInfo: {
            storeProspectId: storeProspectId.toString(),
            globalCustomerId: '10001',
            customerName: '李四',
            customerPhone: '13900139002',
            sourceChannel: '官网',
            prospectStatus: '战败审批中',
            idType: '身份证',
            idNumber: '320123199002022345',
            email: '<EMAIL>',
            region: '槟城',
            address: '槟城市区某某路456号',
            intentModel: 'MYVI',
            intentVariant: '1.5L H CVT',
            intentColor: '蓝色',
            currentIntentLevel: 'H',
            currentSalesAdvisorId: 'SA001',
            currentSalesAdvisorName: '张顾问',
            lastFollowUpTime: '2024-01-16 14:20:00',
            nextFollowUpTime: '2024-01-22 14:00:00',
            leadAssociationTime: '2023-12-05 10:15:00'
          },
          // 试驾记录
          testDriveRecords: [
            {
              testDriveRecordId: 'TD001',
              driverName: '张三',
              phoneNumber: '13800138001',
              testDriveModel: 'MYVI 1.5L H CVT',
              testDriveTime: '2024-01-10 14:30:00',
              testDriveFeedback: '驾驶体验良好，对动力表现满意'
            }
          ],
          // 跟进记录
          followUpRecords: [
            {
              followUpRecordId: 'FU001',
              salesAdvisorName: '张顾问',
              followUpMethod: '电话',
              followUpTime: '2024-01-16 14:20:00',
              intentLevelAfterFollowUp: 'H',
              followUpDetails: '客户对车型很感兴趣，约定下周到店看车'
            },
            {
              followUpRecordId: 'FU002',
              salesAdvisorName: '张顾问',
              followUpMethod: '到店面谈',
              followUpTime: '2024-01-12 10:15:00',
              intentLevelAfterFollowUp: 'A',
              followUpDetails: '客户到店详细了解车型配置和价格，已安排试驾'
            }
          ],
          // 战败申请
          defeatApplications: [
            {
              applicationId: 'DA001',
              applicationTime: '2024-01-18 09:15:00',
              applicantName: '张顾问',
              defeatReason: '客户选择其他品牌',
              defeatDetails: '客户最终选择了竞品，价格因素是主要原因',
              auditStatus: '审核中',
              auditTime: null,
              auditorName: null,
              auditComments: null
            }
          ],
          // 变更日志
          changeLogs: [
            {
              changeLogId: 'CL001',
              changeTime: '2024-01-15 09:30:00',
              changeType: '变更销售顾问',
              operatorName: '管理员',
              originalValue: '李顾问(EMP002)',
              newValue: '张顾问(EMP001)',
              changeReason: '人员调整'
            },
            {
              changeLogId: 'CL002',
              changeTime: '2024-01-16 14:20:00',
              changeType: '更新意向级别',
              operatorName: '张顾问',
              originalValue: 'A级',
              newValue: 'H级',
              changeReason: '客户表现出强烈购买意向'
            }
          ],
          // 效果分析
          performanceAnalysis: {
            totalFollowUpCount: 12,
            mostActiveStore: '槟城中心店'
          }
        }
        resolve({
          result: mockData,
          code: 0,
          message: '操作成功'
        });
      }, 500)
    })
  } else {
    // 使用POST请求获取详情
    return await request.post('/prospects/store/detail', { storeProspectId });
  }
}

// 导出所有API方法
export const storeProspectApi = {
  getProspectList,
  searchExistingLead,
  addStoreProspect,
  recordFollowUp,
  applyDefeat,
  getProspectDetail
}
