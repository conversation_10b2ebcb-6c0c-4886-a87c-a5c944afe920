import { http } from '@/utils/http';
import type {
  FactoryOverviewStatsResponse,
  FactoryProspectListResponse,
  IntentLevelConfigResponse,
  CustomerBasicInfoResponse,
  StoreAssociationsResponse,
  FollowUpRecordsResponse,
  TestDriveRecordsResponse,
  DefeatRecordsResponse,
  ChangeHistoryResponse,
  PerformanceAnalysisResponse,
  IntentLevelConfig
} from '@/api/types/factory-prospect';

// 定义请求参数接口
interface ProspectListParams {
  leadId?: string;
  storeId?: string | number;
  storeCount?: string | number;
  registrationTimeStart?: string;
  registrationTimeEnd?: string;
  viewType?: 'all' | 'cross_store' | 'defeated' | 'converted';
  pageNum: number;
  pageSize: number;
}

// 定义统计概览请求参数接口
interface StatsParams {
  storeId?: string | number;
  startDate?: string;
  endDate?: string;
}

// 客户详情相关请求参数接口
interface CustomerDetailParams {
  globalCustomerId: string;
}

// 获取工厂潜客统计概览数据
export function getFactoryOverviewStats(params: StatsParams = {}) {
  return http.post<unknown, { result: FactoryOverviewStatsResponse }>('/factory/prospects/overview/stats', params);
}

// 获取工厂潜客列表数据
export function getFactoryProspectList(params: ProspectListParams) {
  return http.post<unknown, { result: FactoryProspectListResponse }>('/factory/prospects/list', params);
}

// 导出工厂潜客数据
export function exportFactoryProspectData(params: ProspectListParams) {
  return http.post<unknown, Blob>('/factory/prospects/export', params, {
    responseType: 'blob'
  });
}

// 获取意向级别配置
export function getIntentLevelConfig() {
  return http.get<unknown, IntentLevelConfigResponse>('/factory/prospects/config/intent-level');
}

// 保存意向级别配置
export function saveIntentLevelConfig(data: Record<string, IntentLevelConfig>) {
  return http.post<unknown, { success: boolean; message: string }>('/factory/prospects/config/intent-level/save', data);
}

// 获取客户基本信息
export function getCustomerBasicInfo(params: CustomerDetailParams) {
  return http.post<unknown, CustomerBasicInfoResponse>('/factory/prospects/detail/basicInfo', params);
}

// 获取门店关联信息
export function getStoreAssociations(params: CustomerDetailParams) {
  return http.post<unknown, StoreAssociationsResponse>('/factory/prospects/detail/storeAssociations', params);
}

// 获取跟进记录
export function getFollowUpRecords(params: CustomerDetailParams) {
  return http.post<unknown, FollowUpRecordsResponse>('/factory/prospects/detail/followUpRecords', params);
}

// 获取试驾记录
export function getTestDriveRecords(params: CustomerDetailParams) {
  return http.post<unknown, TestDriveRecordsResponse>('/factory/prospects/detail/testDriveRecords', params);
}

// 获取战败记录
export function getDefeatRecords(params: CustomerDetailParams) {
  return http.post<unknown, DefeatRecordsResponse>('/factory/prospects/detail/defeatRecords', params);
}

// 获取变更历史
export function getChangeHistory(params: CustomerDetailParams) {
  return http.post<unknown, ChangeHistoryResponse>('/factory/prospects/detail/changeHistory', params);
}

// 获取绩效分析
export function getPerformanceAnalysis(params: CustomerDetailParams) {
  return http.post<unknown, PerformanceAnalysisResponse>('/factory/prospects/detail/performanceAnalysis', params);
}
