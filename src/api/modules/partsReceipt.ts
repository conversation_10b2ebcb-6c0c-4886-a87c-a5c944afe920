import request from '@/api/index';
import { i18nGlobal } from '@/plugins/i18n';
import {
  PartsReceiptListItem,
  PartsReceiptSearchParams,
  PaginationResponse,
  ReceiptItem,
  GenerateReceiptOrderSearchParams,
  GenerateReceiptOrderListItem,
  InvalidateReceiptOrderSearchParams,
  InvalidateReceiptOrderListItem,
  ReceiptOrderDetailItem
} from '@/types/partsReceipt';
import {
  mockGetPartsReceiptList,
  mockReceiveParts,
  mockGenerateReceiptOrder,
  mockInvalidateReceiptOrder,
  mockGetReceiptOrderDetail
} from '@/mock/data/partsReceipt';

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('PartsReceipt Module')

/**
 * 获取零件收货列表
 * @param params 查询参数
 */
export const getPartsReceiptList = (params: PartsReceiptSearchParams): Promise<PaginationResponse<PartsReceiptListItem>> => {
  if (USE_MOCK_API) {
    return mockGetPartsReceiptList(params);
  } else {
    return request.get<any, PaginationResponse<PartsReceiptListItem>>('/parts-receipt/list', { params });
  }
};

/**
 * 执行收货操作
 * @param items 收货明细列表
 */
export const receiveParts = (items: ReceiptItem[]): Promise<any> => {
  if (USE_MOCK_API) {
    return mockReceiveParts(items);
  } else {
    return request.post<any, any>('/parts-receipt/receive', items);
  }
};

/**
 * 生成收货单
 * @param items 勾选的收货明细，用于生成收货单
 */
export const generateReceiptOrder = (items: GenerateReceiptOrderListItem[]): Promise<any> => {
  if (USE_MOCK_API) {
    return mockGenerateReceiptOrder(items);
  } else {
    return request.post<any, any>('/parts-receipt/generate-order', items);
  }
};

/**
 * 作废收货单
 * @param receiptOrderNumber 收货单号
 */
export const invalidateReceiptOrderApi = (receiptOrderNumber: string): Promise<any> => {
  if (USE_MOCK_API) {
    return mockInvalidateReceiptOrder(receiptOrderNumber);
  } else {
    return request.post<any, any>(`/parts-receipt/invalidate-order/${receiptOrderNumber}`);
  }
};

/**
 * 获取收货单明细
 * @param receiptOrderNumber 收货单号
 */
export const getReceiptOrderDetail = (receiptOrderNumber: string): Promise<PaginationResponse<ReceiptOrderDetailItem>> => {
  if (USE_MOCK_API) {
    return mockGetReceiptOrderDetail(receiptOrderNumber);
  } else {
    return request.get<any, PaginationResponse<ReceiptOrderDetailItem>>(`/parts-receipt/order-detail/${receiptOrderNumber}`);
  }
};
