import request from '@/api';
import type {
  GetTestDriveListRequest,
  TestDriveRecord,
  CreateTestDriveDto,
  UpdateTestDriveDto,
  ExportTestDriveListRequest,
  SearchLeadsRequest,
  Lead,
  PageResult,
  ApiResponse
} from '@/types/sales/testDrive';

export const testDriveApi = {
  /**
   * @description: 获取试驾列表
   */
  getTestDriveList: (params: GetTestDriveListRequest): Promise<ApiResponse<PageResult<TestDriveRecord>>> => {
    return request({
      url: '/sales/test-drive/list',
      method: 'post',
      data: params
    });
  },

  /**
   * @description: 创建试驾单
   */
  createTestDrive: (data: CreateTestDriveDto): Promise<ApiResponse<null>> => {
    return request({
      url: '/sales/test-drive/create',
      method: 'post',
      data
    });
  },

  /**
   * @description: 更新试驾单
   */
  updateTestDrive: (data: UpdateTestDriveDto): Promise<ApiResponse<null>> => {
    return request({
      url: '/sales/test-drive/update',
      method: 'post',
      data
    });
  },

  /**
   * @description: 导出试驾列表
   */
  exportTestDriveList: (params: ExportTestDriveListRequest): Promise<Blob> => {
    return request({
      url: '/sales/test-drive/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    });
  },

  /**
   * @description: 获取试驾详情
   */
  getTestDriveDetail: (testDriveNo: string): Promise<ApiResponse<TestDriveRecord>> => {
    return request({
      url: `/sales/test-drive/detail/${testDriveNo}`,
      method: 'get'
    });
  },

  /**
   * @description: 搜索潜客线索
   */
  searchLeads: (params: SearchLeadsRequest): Promise<ApiResponse<Lead[]>> => {
    const payload = {
      ...params,
      searchType: 2
    };
    return request({
      url: '/prospects/store/leads/search',
      method: 'post',
      data: payload
    });
  }
};