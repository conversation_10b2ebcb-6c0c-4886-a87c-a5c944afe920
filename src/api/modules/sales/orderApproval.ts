import request from '@/api';
import type {
  OrderApprovalSearchParams,
  OrderApprovalPageResponse,
  ApprovalActionRequest,
  ApprovalDetail,
  ApprovalHistoryItem,
  ExportApprovalParams
} from '@/types/sales/orderApproval';
import { getOrderApprovalListMock } from '@/mock/data/sales/orderApproval';
import { USE_MOCK_API } from '@/utils/mock-config';

/**
 * 获取订单审批列表
 */
export const getOrderApprovalList = (
  params: OrderApprovalSearchParams
): Promise<OrderApprovalPageResponse> => {
  if (USE_MOCK_API) {
    return getOrderApprovalListMock(params);
  } else {
    // 标准MyBatisPlus分页接口
    return request.post<any, OrderApprovalPageResponse>(
      '/approval/order/list',
      params
    );
  }
};

/**
 * 获取审批详情
 */
export const getApprovalDetail = (approvalId: string): Promise<ApprovalDetail> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟审批详情数据
        const mockDetail: ApprovalDetail = {
          id: approvalId,
          approvalNo: `AP${approvalId.slice(-6)}`,
          approvalType: 'cancel_order',
          orderNo: `ORD${approvalId.slice(-8)}`,
          submitterName: '张三',
          submitTime: '2024-01-15 10:00:00',
          reason: '客户因个人原因需要取消订单',
          approvalResult: 'approved',
          approvalTime: '2024-01-15 14:30:00',
          approvalUserName: '李经理',
          dealerId: 'dealer_001',
          orderDetails: {
            customerName: '王五',
            vehicleModel: 'Model A',
            totalAmount: 150000
          },
          approvalHistory: [
            {
              id: '1',
              action: 'submit',
              actionTime: '2024-01-15 10:00:00',
              operator: '张三',
              operatorRole: '销售顾问',
              comments: '客户申请取消订单',
              status: 'submitted'
            },
            {
              id: '2',
              action: 'approve',
              actionTime: '2024-01-15 14:30:00',
              operator: '李经理',
              operatorRole: '销售经理',
              comments: '同意取消，已联系客户确认',
              status: 'approved'
            }
          ]
        };
        resolve(mockDetail);
      }, 300);
    });
  } else {
    return request.get<any, ApprovalDetail>(`/approval/order/${approvalId}/detail`);
  }
};

/**
 * 审批操作（批准/拒绝）
 */
export const submitApprovalAction = (data: ApprovalActionRequest): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Mock approval action:', data);
        resolve();
      }, 1000);
    });
  } else {
    return request.post<any, void>('/approval/order/submit', data);
  }
};

/**
 * 获取审批历史
 */
export const getApprovalHistory = (approvalId: string): Promise<ApprovalHistoryItem[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockHistory: ApprovalHistoryItem[] = [
          {
            id: '1',
            action: 'submit',
            actionTime: '2024-01-15 10:00:00',
            operator: '张三',
            operatorRole: '销售顾问',
            comments: '提交审批申请',
            status: 'submitted'
          },
          {
            id: '2',
            action: 'review',
            actionTime: '2024-01-15 14:30:00',
            operator: '李经理',
            operatorRole: '销售经理',
            comments: '审批通过',
            status: 'approved'
          }
        ];
        resolve(mockHistory);
      }, 300);
    });
  } else {
    return request.get<any, ApprovalHistoryItem[]>(`/sales/order-approval/${approvalId}/history`);
  }
};

/**
 * 导出审批数据
 */
export const exportApprovalData = (params: ExportApprovalParams): Promise<Blob> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟导出文件
        const csvContent = 'ID,审批单号,审批类型,订单编号,提交人,提交时间\n1,AP000001,取消订单,ORD00000001,张三,2024-01-15 10:00:00';
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        resolve(blob);
      }, 1000);
    });
  } else {
    return request.post<any, Blob>('/sales/order-approval/export', params, {
      responseType: 'blob'
    });
  }
};
