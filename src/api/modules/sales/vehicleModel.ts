import request from '@/api';
import type { VehicleModelSearchParams, VehicleModelPageResponse, SyncLogItem, SyncResponse } from '@/types/sales/vehicleModel';
import { getMockVehicleModelList, getMockSyncResponse, getMockSyncLogs } from '@/mock/data/sales/vehicleModel';
import { USE_MOCK_API } from '@/utils/mock-config';
import type { ApiResult } from '@/types/module';

// ✅ 标准化API响应处理
export const getVehicleModelList = async (params: VehicleModelSearchParams): Promise<VehicleModelPageResponse> => {
  if (true || USE_MOCK_API) {
    return getMockVehicleModelList(params);
  } else {
    // ✅ 标准响应处理：使用response.result获取业务数据
    const response = await request.post<any, ApiResult<VehicleModelPageResponse>>('/vehicle-models/list', params);
    return response.result;
  }
};

// 同步车型数据
export const syncVehicleModelData = async (): Promise<SyncResponse> => {
  if (true ||  USE_MOCK_API) {
    return getMockSyncResponse();
  } else {
    const response = await request.post<any, ApiResult<SyncResponse>>('/vehicle-models/sync');
    return response.result;
  }
};

// 获取同步日志
export const getSyncLogs = async (): Promise<SyncLogItem[]> => {
  if (true ||  USE_MOCK_API) {
    return getMockSyncLogs();
  } else {
    const response = await request.get<any, ApiResult<SyncLogItem[]>>('/vehicle-models/sync-logs');
    return response.result;
  }
};

// 导出车型数据
export const exportVehicleModelData = async (params: VehicleModelSearchParams): Promise<Blob> => {
  if (USE_MOCK_API) {
    // Mock导出功能
    return new Promise((resolve) => {
      setTimeout(() => {
        const csvContent = 'ID,车型,版本名称,版本代码,颜色名称,颜色代码,FMRID,创建时间,更新时间\n';
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        resolve(blob);
      }, 1000);
    });
  } else {
    const response = await request.post('/vehicle-models/export', params, {
      responseType: 'blob'
    });
    return response.data;
  }
};
