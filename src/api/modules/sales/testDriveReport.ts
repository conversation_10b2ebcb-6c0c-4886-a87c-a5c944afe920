import request from '@/api';
import type { TestDriveSearchParams, TestDrivePageResponse, TestDriveStats, TestDriveItem } from '@/types/sales/testDriveReport';
import { getTestDriveList, getTestDriveStats, getTestDriveDetailMock } from '@/mock/data/sales/testDriveReport';
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config';

// 初始化时打印Mock状态
MockConfig.logStatus('Test Drive Report Module');

/**
 * 获取试驾报表列表
 * @param params 搜索参数
 */
export const getTestDriveReportList = (params: TestDriveSearchParams): Promise<TestDrivePageResponse> => {
  if (USE_MOCK_API) {
    return getTestDriveList(params);
  } else {
    return request.post<any, TestDrivePageResponse>('/sales/test-drive-report/list', params);
  }
};

/**
 * 获取试驾统计数据
 */
export const getTestDriveReportStats = (): Promise<{ result: TestDriveStats }> => {
  if (USE_MOCK_API) {
    return getTestDriveStats();
  } else {
    return request.get<any, { result: TestDriveStats }>('/sales/test-drive-report/statistics');
  }
};

/**
 * 获取试驾详情
 * @param testDriveNo 试驾单号
 */
export const getTestDriveDetail = (testDriveNo: string): Promise<{ result: TestDriveItem }> => {
  if (USE_MOCK_API) {
    return getTestDriveDetailMock(testDriveNo);
  } else {
    return request.get<any, { result: TestDriveItem }>(`/sales/test-drive/detail/${testDriveNo}`);
  }
};

/**
 * 导出试驾报表
 * @param params 搜索参数
 */
export const exportTestDriveReport = (params: TestDriveSearchParams): Promise<Blob> => {
  return request.post<any, Blob>('/sales/test-drive-report/export', params, { responseType: 'blob' });
};
