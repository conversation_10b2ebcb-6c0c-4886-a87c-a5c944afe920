import request from '@/api';
import type {
  FactoryOrderSearchParams,
  FactoryOrderPageResponse,
  FactoryOrderDetail,
  FactoryOrderStatistics,
  ExportFactoryOrderParams,
  ExportFactoryOrderResponse
} from '@/types/sales/factoryOrderManagement';
import {
  getFactoryOrderList as getMockFactoryOrderList,
  getFactoryOrderDetail as getMockFactoryOrderDetail,
  getFactoryOrderStatistics as getMockFactoryOrderStatistics,
  exportFactoryOrderData as getMockExportFactoryOrderData,
  refreshStatistics as getMockRefreshStatistics
} from '@/mock/data/sales/factoryOrderManagement';
import { USE_MOCK_API, MockConfig } from '@/utils/mock-config';

// 初始化时打印Mock状态
MockConfig.logStatus('Factory Order Management Module');

/**
 * 获取工厂订单列表
 * @param params 搜索参数
 */
export const getFactoryOrderList = (params: FactoryOrderSearchParams): Promise<FactoryOrderPageResponse> => {
  if (USE_MOCK_API) {
    return getMockFactoryOrderList(params);
  } else {
    return request.post<any, FactoryOrderPageResponse>('/factory/orders/list', params);
  }
};

/**
 * 获取工厂订单详情
 * @param orderNumber 订单号
 */
export const getFactoryOrderDetail = (orderNumber: string): Promise<{ result: FactoryOrderDetail }> => {
  if (USE_MOCK_API) {
    return getMockFactoryOrderDetail(orderNumber);
  } else {
    return request.get<any, { result: FactoryOrderDetail }>(`/factory/orders/${orderNumber}`);
  }
};

/**
 * 获取工厂订单统计数据
 */
export const getOrderStatistics = (): Promise<{ result: FactoryOrderStatistics }> => {
  if (USE_MOCK_API) {
    return getMockFactoryOrderStatistics();
  } else {
    return request.get<any, { result: FactoryOrderStatistics }>('/factory/orders/stats');
  }
};

/**
 * 导出工厂订单数据
 * @param params 导出参数
 */
export const exportFactoryOrderData = (params: ExportFactoryOrderParams): Promise<ExportFactoryOrderResponse> => {
  if (USE_MOCK_API) {
    return getMockExportFactoryOrderData(params);
  } else {
    return request.post<any, ExportFactoryOrderResponse>('/sales/factory-order/export', params);
  }
};

/**
 * 刷新统计数据
 */
export const refreshStatistics = (): Promise<FactoryOrderStatistics> => {
  if (USE_MOCK_API) {
    return getMockRefreshStatistics();
  } else {
    return request.get<any, FactoryOrderStatistics>('/sales/factory-order/refresh-statistics');
  }
};

/**
 * 获取工厂订单统计数据（兼容旧版本API名称）
 */
export const getFactoryOrderStatistics = getOrderStatistics;
