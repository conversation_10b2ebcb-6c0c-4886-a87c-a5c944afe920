// 交付管理API模块
// 支持Mock数据和真实API切换

import request from '@/api';
import type {
  DeliverySearchParams,
  DeliveryPageResponse,
  DeliveryListItem,
  SubmitConfirmRequest,
  DeliveryConfirmRequest,
  ExportSettings
} from '@/types/sales/delivery';
import { getDeliveryListMock, getDeliveryDetailMock } from '@/mock/data/sales/delivery';
import { USE_MOCK_API } from '@/utils/mock-config';

// 获取交付列表 - 标准MyBatisPlus分页接口
export const getDeliveryList = (
  params: DeliverySearchParams
): Promise<DeliveryPageResponse> => {
  if (USE_MOCK_API) {
    return getDeliveryListMock(params);
  } else {
    // 真实API调用 - 使用GET方法传递查询参数
    return request.get<any, DeliveryPageResponse>(
      '/delivery/orders',
      { params }
    );
  }
};

// 获取交付详情
export const getDeliveryDetail = (
  deliveryNumber: string
): Promise<DeliveryListItem | null> => {
  if (USE_MOCK_API) {
    return getDeliveryDetailMock(deliveryNumber);
  } else {
    return request.get<any, DeliveryListItem>(
      `/delivery/orders/${deliveryNumber}`
    );
  }
};

// 提交确认
export const submitConfirm = (
  data: SubmitConfirmRequest
): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Mock: 提交确认成功', data);
        resolve();
      }, 1000);
    });
  } else {
    return request.post<any, void>(
      '/sales/delivery/submit-confirm',
      data
    );
  }
};

// 交车确认
export const deliveryConfirm = (
  data: DeliveryConfirmRequest
): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Mock: 交车确认成功', data);
        resolve();
      }, 1000);
    });
  } else {
    return request.post<any, void>(
      '/sales/delivery/delivery-confirm',
      data
    );
  }
};

// 上传签字照片
export const uploadSignaturePhoto = (
  file: File,
  deliveryNumber?: string,
  deliveryTime?: string
): Promise<{ url: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockUrl = `https://example.com/signatures/${Date.now()}_${file.name}`;
        console.log('Mock: 签字照片上传成功', mockUrl);
        resolve({ url: mockUrl });
      }, 1500);
    });
  } else {
    const formData = new FormData();
    formData.append('file', file);
    if (deliveryNumber) {
      formData.append('deliveryNumber', deliveryNumber);
    }
    if (deliveryTime) {
      formData.append('deliveryTime', deliveryTime);
    }
    return request.put<any, { url: string }>(
      '/delivery/orders/'+deliveryNumber+'/submit-confirm',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );
  }
};



// 导出交付数据
export const exportDeliveryData = (
  exportSettings: ExportSettings,
  searchParams: DeliverySearchParams
): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Mock: 导出交付数据成功', { exportSettings, searchParams });
        // 模拟文件下载
        const link = document.createElement('a');
        link.href = 'data:text/plain;charset=utf-8,Mock Export Data';
        link.download = `delivery_export_${Date.now()}.${exportSettings.format}`;
        link.click();
        resolve();
      }, 2000);
    });
  } else {
    return request.post<any, void>(
      '/sales/delivery/export',
      {
        exportSettings,
        searchParams
      },
      {
        responseType: 'blob'
      }
    );
  }
};

// 批量操作接口（预留）
export const batchUpdateDeliveryStatus = (
  deliveryNumbers: string[],
  status: string
): Promise<void> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Mock: 批量更新交付状态成功', { deliveryNumbers, status });
        resolve();
      }, 1000);
    });
  } else {
    return request.post<any, void>(
      '/sales/delivery/batch-update-status',
      {
        deliveryNumbers,
        status
      }
    );
  }
};
