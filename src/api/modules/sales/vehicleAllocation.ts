import request from '@/api/index';
import type {
  VehicleAllocationOrderItem,
  VehicleAllocationOrderParams,
  AvailableVehicle,
  AvailableVehicleParams,
  AllocationConfirmParams,
  CancelAllocationParams,
  AllocationRecord,
  AllocationRecordParams,
  OrderAllocationTimelineItem,
  AllocationStatistics,
  PaginationResponse,
  ExportDataParams
} from '@/types/sales/vehicleAllocation';

import {
  mockVehicleAllocationOrders,
  mockAvailableVehicles,
  mockAllocationRecords,
  mockOrderAllocationTimelines,
  mockAllocationStatistics,
  mockStoreOptions,
  mockSalesConsultantOptions,
  mockAllocationStatusOptions,
  mockOrderStatusOptions,
  mockOperationTypeOptions
} from '@/mock/data/sales/vehicleAllocation';

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('VehicleAllocation Module')

/**
 * 1. 查询订单配车列表
 * P03契约: GET /vehicle-allocations
 */
export const getVehicleAllocationOrderList = async (params: VehicleAllocationOrderParams): Promise<PaginationResponse<VehicleAllocationOrderItem>> => {

  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredOrders = [...mockVehicleAllocationOrders];

        // 应用筛选条件
        if (params.orderNumber) {
          filteredOrders = filteredOrders.filter(order =>
            order.orderNumber.includes(params.orderNumber!)
          );
        }
        if (params.customerName) {
          filteredOrders = filteredOrders.filter(order =>
            order.customerName.includes(params.customerName!)
          );
        }
        if (params.customerPhone) {
          filteredOrders = filteredOrders.filter(order =>
            order.customerPhone.includes(params.customerPhone!)
          );
        }
        if (params.allocationStatus) {
          filteredOrders = filteredOrders.filter(order =>
            order.allocationStatus === params.allocationStatus
          );
        }
        if (params.orderStatus) {
          filteredOrders = filteredOrders.filter(order =>
            order.orderStatus === params.orderStatus
          );
        }
        if (params.vin) {
          filteredOrders = filteredOrders.filter(order =>
            order.vin.includes(params.vin!)
          );
        }
        if (params.factoryOrderNumber) {
          filteredOrders = filteredOrders.filter(order =>
            order.factoryOrderNumber.includes(params.factoryOrderNumber!)
          );
        }
        if (params.store) {
          filteredOrders = filteredOrders.filter(order =>
            order.store === params.store
          );
        }
        if (params.salesConsultant) {
          filteredOrders = filteredOrders.filter(order =>
            order.salesConsultant === params.salesConsultant
          );
        }

        // 分页处理
        const startIndex = (params.page - 1) * params.pageSize;
        const endIndex = startIndex + params.pageSize;
        const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

        resolve({
          data: paginatedOrders,
          total: filteredOrders.length,
          page: params.page,
          pageSize: params.pageSize
        });
      }, 800);
    });
  } else {
    // P03契约接口路径: /vehicle-allocations
    const response = await request.get('/vehicle-allocations', { params });
    return {
      data: response.result.records || response.result,
      total: response.result.total || 0,
      page: params.page,
      pageSize: params.pageSize
    };
  }
};

/**
 * 2. 查询可配车辆列表
 * P03契约: POST /vehicle-inventories/available-vehicles
 */
export const getAvailableVehicles = async (params: AvailableVehicleParams): Promise<AvailableVehicle[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredVehicles = [...mockAvailableVehicles];

        // 应用筛选条件
        if (params.vin) {
          filteredVehicles = filteredVehicles.filter(vehicle =>
            vehicle.vin.includes(params.vin!)
          );
        }
        if (params.factoryOrderNumber) {
          filteredVehicles = filteredVehicles.filter(vehicle =>
            vehicle.factoryOrderNumber.includes(params.factoryOrderNumber!)
          );
        }
        if (params.orderNumber) {
          // 根据订单号获取订单配置要求，然后筛选匹配的车辆
          const order = mockVehicleAllocationOrders.find(o => o.orderNumber === params.orderNumber);
          if (order) {
            filteredVehicles = filteredVehicles.filter(vehicle =>
              vehicle.model === order.model &&
              vehicle.variant === order.variant &&
              vehicle.color === order.color
            );
          }
        }

        // 只返回在库且未锁定的车辆
        filteredVehicles = filteredVehicles.filter(vehicle =>
          vehicle.stockStatus === 'in_stock' && !vehicle.lockStatus
        );

        resolve(filteredVehicles);
      }, 600);
    });
  } else {
    // P03契约接口路径: POST /vehicle-inventories/available-vehicles
    const response = await request.post('/vehicle-inventories/available-vehicles', params);
    return response.result;
  }
};

/**
 * 3. 执行订单配车
 * P03契约: POST /vehicle-allocations
 */
export const confirmAllocation = async (params: AllocationConfirmParams): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '配车成功'
        });
      }, 1000);
    });
  } else {
    // P03契约接口路径: /vehicle-allocations
    const response = await request.post('/vehicle-allocations', params);
    return {
      success: response.result?.success || response.success || true,
      message: response.result?.message || response.message || response.msg || '配车成功'
    };
  }
};

/**
 * 4. 取消订单配车
 * P03契约: DELETE /vehicle-allocations/{orderNumber}
 */
export const cancelAllocation = async (params: CancelAllocationParams): Promise<{ success: boolean; message: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const order = mockVehicleAllocationOrders.find(o => o.orderNumber === params.orderNumber);

        if (!order) {
          reject(new Error('订单不存在'));
          return;
        }
        if (order.allocationStatus === 'unallocated') {
          reject(new Error('订单未配车'));
          return;
        }

        resolve({
          success: true,
          message: '取消配车成功'
        });
      }, 800);
    });
  } else {
    // P03契约接口路径: DELETE /vehicle-allocations/{orderNumber}
    // 将reason作为请求体发送
    const { orderNumber, reason } = params;
    const response = await request.delete(`/vehicle-allocations/${orderNumber}`, {
      data: { reason }
    });
    return {
      success: response.result?.success || response.success || true,
      message: response.result?.message || response.message || response.msg || '取消配车成功'
    };
  }
};

/**
 * 5. 查询配车记录时间轴
 * P03契约: GET /allocation-records/{orderNumber}
 */
export const getOrderAllocationTimeline = async (orderNumber: string): Promise<OrderAllocationTimelineItem[]> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const timeline = mockOrderAllocationTimelines[orderNumber] || [];
        resolve(timeline);
      }, 500);
    });
  } else {
    // P03契约接口路径: /allocation-records/{orderNumber}
    const response = await request.get(`/allocation-records/timeline/${orderNumber}`);
    return response.result.timeline || response.result;
  }
};

/**
 * 6. 导出配车数据
 * P03契约: POST /vehicle-allocations/export
 */
export const exportAllocationData = async (params: ExportDataParams): Promise<{ success: boolean; message: string; downloadUrl?: string }> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '导出成功',
          downloadUrl: '/mock-download/vehicle-allocation-export.xlsx'
        });
      }, 1500);
    });
  } else {
    // P03契约接口路径: /vehicle-allocations/export
    const response = await request.post('/vehicle-allocations/export', params);
    return {
      success: response.result?.success || response.success || true,
      message: response.result?.message || response.message || response.msg || '导出成功',
      downloadUrl: response.result?.downloadUrl || response.downloadUrl
    };
  }
};

/**
 * 7. 获取销售顾问选项
 * P03契约: GET /sales-consultants
 */
export const getSalesConsultantOptions = async (storeId?: string): Promise<Array<{label: string, value: string}>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 处理分组数据结构
        const allConsultants = Object.values(mockSalesConsultantOptions).flat();
        let consultants = allConsultants;
        if (storeId) {
          // 根据门店筛选销售顾问
          consultants = allConsultants.filter(consultant =>
            consultant.storeId === storeId
          );
        }
        resolve(consultants.map(c => ({ label: c.label, value: c.value })));
      }, 300);
    });
  } else {
    // P03契约接口路径: /sales-consultants
    const params = storeId ? { storeId } : {};
    const response = await request.get('/sales-consultants', { params });
    return response.data.map((item: { label?: string; consultant_name?: string; value?: string; id?: number }) => ({
      label: item.label || item.consultant_name || '',
      value: item.value || item.id?.toString() || ''
    }));
  }
};

/**
 * 8. 获取订单状态选项
 * P03契约: GET /order-statuses
 */
export const getOrderStatusOptions = async (): Promise<Array<{label: string, value: string}>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockOrderStatusOptions);
      }, 300);
    });
  } else {
    // P03契约接口路径: /order-statuses
    const response = await request.get('/order-statuses');
    return response.data;
  }
};

// 新增: 获取门店选项 (支持级联筛选)
export const getStoreOptions = async (): Promise<Array<{label: string, value: string}>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockStoreOptions);
      }, 300);
    });
  } else {
    // 门店选项接口
    const response = await request.get('/stores');
    return response.data.map((item: any) => ({
      label: item.store_name || item.label,
      value: item.id?.toString() || item.value
    }));
  }
};

// 保持现有的其他函数以保证兼容性
export const getAllocationRecords = async (params: AllocationRecordParams): Promise<PaginationResponse<AllocationRecord>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredRecords = [...mockAllocationRecords];

        // 应用筛选条件
        if (params.orderNumber) {
          filteredRecords = filteredRecords.filter(record =>
            record.orderNumber.includes(params.orderNumber!)
          );
        }
        if (params.operationType) {
          filteredRecords = filteredRecords.filter(record =>
            record.operationType === params.operationType
          );
        }
        if (params.operator) {
          filteredRecords = filteredRecords.filter(record =>
            record.operator.includes(params.operator!)
          );
        }

        // 分页处理
        const startIndex = (params.page - 1) * params.pageSize;
        const endIndex = startIndex + params.pageSize;
        const paginatedRecords = filteredRecords.slice(startIndex, endIndex);

        resolve({
          data: paginatedRecords,
          total: filteredRecords.length,
          page: params.page,
          pageSize: params.pageSize
        });
      }, 700);
    });
  } else {
    const response = await request.get('/allocation-records', { params });
    return {
      data: response.data.list || response.data,
      total: response.data.total || 0,
      page: params.page,
      pageSize: params.pageSize
    };
  }
};

export const getAllocationStatistics = async (): Promise<AllocationStatistics> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockAllocationStatistics);
      }, 500);
    });
  } else {
    const response = await request.get('/allocation-statistics');
    return response.data;
  }
};

export const getAllocationStatusOptions = async (): Promise<Array<{label: string, value: string}>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockAllocationStatusOptions);
      }, 300);
    });
  } else {
    const response = await request.get('/allocation-status-options');
    return response.data;
  }
};

export const getOperationTypeOptions = async (): Promise<Array<{label: string, value: string}>> => {
  if (USE_MOCK_API) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockOperationTypeOptions);
      }, 300);
    });
  } else {
    const response = await request.get('/operation-type-options');
    return response.data;
  }
};
