// 经销商端采购管理API接口

// 基础API
import request from '@/api';
// 类型定义
import type {
  PurchaseOrder,
  PurchaseOrderItem,
  ShipmentOrder,
  PartForSelection,
  CreatePurchaseOrderRequest,
  UpdatePurchaseOrderRequest,
  PurchaseOrderListQuery,
  DealerDashboardStats,
  ReceiptRequest
} from '@/types/parts/purchase-dealer';
import type { PaginationResult, ApiResult } from '@/types/common';
// Mock数据
import { mockDealerPurchaseData } from '@/mock/data/parts/purchase-dealer';
// 工具配置

const USE_MOCK_API= true;

/**
 * 获取经销商端采购订单仪表盘统计数据
 * @param {number} dealerId 经销商ID
 * @returns {Promise<DealerDashboardStats>} 仪表盘统计数据
 */
export const getDashboardStats = async (dealerId?: number): Promise<DealerDashboardStats> => {
  if (USE_MOCK_API) {
    return Promise.resolve(mockDealerPurchaseData.dashboardStats);
  }
  
  const response = await request.get<any, ApiResult<DealerDashboardStats>>('/api/purchase-dealer/dashboard', {
    params: { dealerId }
  });
  return response.result;
};

/**
 * 获取经销商端采购订单列表
 * @param {PurchaseOrderListQuery} params 查询参数
 * @returns {Promise<PaginationResult<PurchaseOrder>>} 分页订单列表
 */
export const getPurchaseOrderList = async (params: PurchaseOrderListQuery): Promise<PaginationResult<PurchaseOrder>> => {
  if (USE_MOCK_API) {
    const { page = 1, size = 10, status, orderNo, warehouseId } = params;
    let filteredOrders = mockDealerPurchaseData.orders;
    
    if (status) {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }
    if (orderNo) {
      filteredOrders = filteredOrders.filter(order => order.orderNo.includes(orderNo));
    }
    if (warehouseId) {
      filteredOrders = filteredOrders.filter(order => order.warehouseId === warehouseId);
    }
    
    const start = (page - 1) * size;
    const end = start + size;
    
    return Promise.resolve({
      list: filteredOrders.slice(start, end),
      total: filteredOrders.length,
      page,
      size
    });
  }
  
  const response = await request.get<any, ApiResult<PaginationResult<PurchaseOrder>>>('/api/purchase-dealer/orders', { params });
  return response.result;
};

/**
 * 获取采购订单详情
 * @param {string} id 订单ID
 * @returns {Promise<PurchaseOrder>} 订单详情
 */
export const getPurchaseOrderDetail = async (id: string): Promise<PurchaseOrder> => {
  if (USE_MOCK_API) {
    const order = mockDealerPurchaseData.orders.find(o => o.id === id);
    if (!order) {
      throw new Error('Order not found');
    }
    return Promise.resolve(order);
  }
  
  const response = await request.get<any, ApiResult<PurchaseOrder>>(`/api/purchase-dealer/orders/${id}`);
  return response.result;
};

/**
 * 创建采购订单
 * @param {CreatePurchaseOrderRequest} data 创建订单请求数据
 * @returns {Promise<PurchaseOrder>} 创建的订单信息
 */
export const createPurchaseOrder = async (data: CreatePurchaseOrderRequest): Promise<PurchaseOrder> => {
  if (USE_MOCK_API) {
    const newOrder: PurchaseOrder = {
      id: Date.now().toString(),
      orderNo: `PO${Date.now()}`,
      status: 'draft',
      warehouseId: data.warehouseId,
      warehouseName: 'Mock Warehouse',
      items: data.items.map(item => ({
        ...item,
        id: Date.now().toString() + Math.random().toString(),
        amount: item.quantity * item.unitPrice
      })),
      totalAmount: data.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0),
      itemCount: data.items.length,
      remark: data.remark,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    };
    return Promise.resolve(newOrder);
  }
  
  const response = await request.post<any, ApiResult<PurchaseOrder>>('/api/purchase-dealer/orders', data);
  return response.result;
};

/**
 * 更新采购订单
 * @param {string} id 订单ID
 * @param {UpdatePurchaseOrderRequest} data 更新订单请求数据
 * @returns {Promise<PurchaseOrder>} 更新后的订单信息
 */
export const updatePurchaseOrder = async (id: string, data: UpdatePurchaseOrderRequest): Promise<PurchaseOrder> => {
  if (USE_MOCK_API) {
    const existingOrder = mockDealerPurchaseData.orders.find(o => o.id === id);
    if (!existingOrder) {
      throw new Error('Order not found');
    }
    
    const updatedOrder: PurchaseOrder = {
      ...existingOrder,
      ...data,
      items: data.items?.map(item => ({
        ...item,
        id: item.id || Date.now().toString() + Math.random().toString(),
        amount: item.quantity * item.unitPrice
      })) || existingOrder.items,
      totalAmount: data.items?.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0) || existingOrder.totalAmount,
      itemCount: data.items?.length || existingOrder.itemCount,
      updateTime: new Date().toISOString()
    };
    return Promise.resolve(updatedOrder);
  }
  
  const response = await request.put<any, ApiResult<PurchaseOrder>>(`/api/purchase-dealer/orders/${id}`, data);
  return response.result;
};

/**
 * 删除采购订单
 * @param {string} id 订单ID
 * @returns {Promise<void>} 删除结果
 */
export const deletePurchaseOrder = async (id: string): Promise<void> => {
  if (USE_MOCK_API) {
    return Promise.resolve();
  }
  
  const response = await request.delete<any, ApiResult<void>>(`/api/purchase-dealer/orders/${id}`);
  return response.result;
};

/**
 * 提交订单审批
 * @param {string} id 订单ID
 * @returns {Promise<void>} 提交结果
 */
export const submitOrderForApproval = async (id: string): Promise<void> => {
  if (USE_MOCK_API) {
    return Promise.resolve();
  }
  
  const response = await request.post<any, ApiResult<void>>(`/api/purchase-dealer/orders/${id}/submit`);
  return response.result;
};

/**
 * 取消采购订单
 * @param {string} id 订单ID
 * @param {string} reason 取消原因
 * @returns {Promise<void>} 取消结果
 */
export const cancelPurchaseOrder = async (id: string, reason: string): Promise<void> => {
  if (USE_MOCK_API) {
    return Promise.resolve();
  }
  
  const response = await request.post<any, ApiResult<void>>(`/api/purchase-dealer/orders/${id}/cancel`, { reason });
  return response.result;
};

/**
 * 获取可选配件列表
 * @param {object} params 查询参数
 * @param {string} params.keyword 关键词
 * @param {string} params.categoryId 分类ID
 * @param {number} params.page 页码
 * @param {number} params.size 每页数量
 * @returns {Promise<PaginationResult<PartForSelection>>} 可选配件分页列表
 */
export const getAvailableParts = async (params: {
  keyword?: string;
  categoryId?: string;
  page?: number;
  size?: number;
}): Promise<PaginationResult<PartForSelection>> => {
  if (USE_MOCK_API) {
    const { page = 1, size = 10, keyword, categoryId } = params;
    let filteredParts = mockDealerPurchaseData.availableParts;
    
    if (keyword) {
      filteredParts = filteredParts.filter(part => 
        part.partCode.includes(keyword) || part.partName.includes(keyword)
      );
    }
    if (categoryId) {
      filteredParts = filteredParts.filter(part => part.categoryId === categoryId);
    }
    
    const start = (page - 1) * size;
    const end = start + size;
    
    return Promise.resolve({
      list: filteredParts.slice(start, end),
      total: filteredParts.length,
      page,
      size
    });
  }
  
  const response = await request.get<any, ApiResult<PaginationResult<PartForSelection>>>('/api/purchase-dealer/parts', { params });
  return response.result;
};

/**
 * 获取仓库列表
 * @returns {Promise<Array<{ id: string; name: string }>>} 仓库列表
 */
export const getWarehouses = async (): Promise<Array<{ id: string; name: string }>> => {
  if (USE_MOCK_API) {
    return Promise.resolve(mockDealerPurchaseData.warehouses);
  }
  
  const response = await request.get<any, ApiResult<Array<{ id: string; name: string }>>>('/api/purchase-dealer/warehouses');
  return response.result;
};

/**
 * 获取发货单列表
 * @param {string} purchaseOrderId 采购订单ID
 * @returns {Promise<ShipmentOrder[]>} 发货单列表
 */
export const getShipmentOrders = async (purchaseOrderId: string): Promise<ShipmentOrder[]> => {
  if (USE_MOCK_API) {
    return Promise.resolve(mockDealerPurchaseData.shipmentOrders.filter(s => s.purchaseOrderId === purchaseOrderId));
  }
  
  const response = await request.get<any, ApiResult<ShipmentOrder[]>>(`/api/purchase-dealer/orders/${purchaseOrderId}/shipments`);
  return response.result;
};

/**
 * 确认收货
 * @param {ReceiptRequest} data 收货确认数据
 * @returns {Promise<void>} 确认结果
 */
export const confirmReceipt = async (data: ReceiptRequest): Promise<void> => {
  if (USE_MOCK_API) {
    return Promise.resolve();
  }
  
  const response = await request.post<any, ApiResult<void>>('/api/purchase-dealer/receipt', data);
  return response.result;
};

/**
 * 批量删除采购订单
 * @param {string[]} ids 订单ID列表
 * @returns {Promise<void>} 删除结果
 */
export const batchDeleteOrders = async (ids: string[]): Promise<void> => {
  if (USE_MOCK_API) {
    return Promise.resolve();
  }
  
  const response = await request.delete<any, ApiResult<void>>('/api/purchase-dealer/orders/batch', { data: { ids } });
  return response.result;
};

/**
 * 批量提交审批
 * @param {string[]} ids 订单ID列表
 * @returns {Promise<void>} 提交结果
 */
export const batchSubmitForApproval = async (ids: string[]): Promise<void> => {
  if (USE_MOCK_API) {
    return Promise.resolve();
  }
  
  const response = await request.post<any, ApiResult<void>>('/api/purchase-dealer/orders/batch-submit', { ids });
  return response.result;
};

/**
 * 导出订单数据
 * @param {PurchaseOrderListQuery} params 查询参数
 * @returns {Promise<Blob>} 导出文件数据
 */
export const exportOrderData = async (params: PurchaseOrderListQuery): Promise<Blob> => {
  if (USE_MOCK_API) {
    // 模拟导出文件
    const data = 'Order No,Status,Amount\nPO001,Draft,1000\nPO002,Approved,2000';
    return Promise.resolve(new Blob([data], { type: 'text/csv' }));
  }
  
  const response = await request.get('/api/purchase-dealer/orders/export', { 
    params,
    responseType: 'blob'
  });
  return response as unknown as Blob;
};

/**
 * 获取订单状态统计
 * @returns {Promise<Record<string, number>>} 状态统计数据
 */
export const getOrderStatusStats = async (): Promise<Record<string, number>> => {
  if (USE_MOCK_API) {
    const stats: Record<string, number> = {};
    mockDealerPurchaseData.orders.forEach(order => {
      stats[order.status] = (stats[order.status] || 0) + 1;
    });
    return Promise.resolve(stats);
  }
  
  const response = await request.get<any, ApiResult<Record<string, number>>>('/api/purchase-dealer/orders/stats');
  return response.result;
};

/**
 * 搜索采购订单
 * @param {string} keyword 搜索关键词
 * @returns {Promise<PurchaseOrder[]>} 搜索结果列表
 */
export const searchOrders = async (keyword: string): Promise<PurchaseOrder[]> => {
  if (USE_MOCK_API) {
    const filteredOrders = mockDealerPurchaseData.orders.filter(order =>
      order.orderNo.includes(keyword) || 
      order.warehouseName.includes(keyword)
    );
    return Promise.resolve(filteredOrders);
  }
  
  const response = await request.get<any, ApiResult<PurchaseOrder[]>>('/api/purchase-dealer/orders/search', {
    params: { keyword }
  });
  return response.result;
};

/**
 * 获取订单操作历史
 * @param {string} id 订单ID
 * @returns {Promise<Array<{action: string; operator: string; time: string; remark?: string;}>>} 操作历史列表
 */
export const getOrderHistory = async (id: string): Promise<Array<{
  action: string;
  operator: string;
  time: string;
  remark?: string;
}>> => {
  if (USE_MOCK_API) {
    return Promise.resolve([
      {
        action: '创建订单',
        operator: '张三',
        time: '2024-01-01 10:00:00',
        remark: '初始创建'
      },
      {
        action: '提交审批',
        operator: '张三',
        time: '2024-01-01 11:00:00'
      }
    ]);
  }
  
  const response = await request.get<any, ApiResult<Array<{
    action: string;
    operator: string;
    time: string;
    remark?: string;
  }>>>(`/api/purchase-dealer/orders/${id}/history`);
  return response.result;
};