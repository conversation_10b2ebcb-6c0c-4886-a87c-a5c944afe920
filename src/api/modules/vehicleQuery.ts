import request from '@/api/index';
import type { PaginationResponse, VehicleListItem, VehicleSearchParams, VehicleConfiguration, WarehouseInfo } from '@/types/vehicleQuery';
import { getMockVehicleList, getMockVehicleConfiguration, getMockWarehouseInfo } from '@/mock/data/vehicleQuery';

import { USE_MOCK_API, MockConfig } from '@/utils/mock-config'

// 初始化时打印Mock状态
MockConfig.logStatus('VehicleQuery Module')

/**
 * 获取车辆列表
 * @param params
 */
export const getVehicleList = (params: VehicleSearchParams & { pageNum: number; pageSize: number }): Promise<PaginationResponse<VehicleListItem>> => {
  if (USE_MOCK_API) {
    return getMockVehicleList(params);
  } else {
    return request.get<any, PaginationResponse<VehicleListItem>>('/vehicle-inventories', { params });
  }
};

/**
 * 获取车辆配置信息（Model, Variant, Color 级联数据）
 */
export const getVehicleConfiguration = (): Promise<VehicleConfiguration[]> => {
  if (USE_MOCK_API) {
    return getMockVehicleConfiguration();
  } else {
    // 假设真实API返回的数据结构与Mock数据一致
    return request.get<any, VehicleConfiguration[]>('/master-data/vehicle-variants');
  }
};

/**
 * 获取仓库信息
 */
export const getWarehouseInfo = (): Promise<WarehouseInfo[]> => {
  if (USE_MOCK_API) {
    return getMockWarehouseInfo();
  } else {
    // 假设真实API返回的数据结构与Mock数据一致
    return request.get<any, WarehouseInfo[]>('/master-data/warehouses');
  }
};
