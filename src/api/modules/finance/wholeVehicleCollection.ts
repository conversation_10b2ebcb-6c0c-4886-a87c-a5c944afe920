import request from '@/api';
import type {
  WholeVehicleCollectionSearchParams,
  WholeVehicleCollectionPageResponse,
  OrderDetailInfo,
  AddPaymentRecordForm,
  ApiResponse
} from '@/types/finance/wholeVehicleCollection';

// Mock数据导入
import {
  getWholeVehicleCollectionListMock,
  getOrderDetailMock,
  addPaymentRecordMock,
  deletePaymentRecordMock
} from '@/mock/data/finance/wholeVehicleCollection';
import { USE_MOCK_API } from '@/utils/mock-config';

/**
 * 获取整车收款管理列表
 * @param params 搜索参数（使用MyBatisPlus标准分页参数pageNum）
 * @returns 分页数据
 */
export const getWholeVehicleCollectionList = async (
  params: WholeVehicleCollectionSearchParams
): Promise<ApiResponse<WholeVehicleCollectionPageResponse>> => {
  // 根据配置使用Mock数据或真实API
  if (USE_MOCK_API) {
    return getWholeVehicleCollectionListMock(params);
  }

  // 生产环境调用真实API（保持原有接口不变）
  return request({
    url: '/payment/order/list',
    method: 'POST',
    data: {
      ...params,
      // 确保使用MyBatisPlus标准分页参数
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 20
    }
  });
};

/**
 * 获取订单详情信息
 * @param orderId 订单ID
 * @returns 订单详情
 */
export const getOrderDetail = async (orderId: string): Promise<ApiResponse<OrderDetailInfo>> => {
  // 根据配置使用Mock数据或真实API
  if (USE_MOCK_API) {
    return getOrderDetailMock(orderId);
  }

  // 生产环境调用真实API（保持原有接口不变）
  return request({
    url: `/payment/order/detail`,
    method: 'POST',
    data: {
      orderId
    }
  });
};

/**
 * 添加收退款记录
 * @param orderId 订单ID
 * @param record 收退款记录信息
 * @returns 操作结果
 */
export const addPaymentRecord = async (
  orderId: string,
  record: AddPaymentRecordForm
): Promise<ApiResponse<boolean>> => {
  // 根据配置使用Mock数据或真实API
  if (USE_MOCK_API) {
    return addPaymentRecordMock(orderId, record);
  }

  // 生产环境调用真实API（保持原有接口不变）
  return request({
    url: `payment/record/${orderId}/create`,
    method: 'POST',
    data: record
  });
};

/**
 * 删除收退款记录
 * @param recordId 记录ID
 * @returns 操作结果
 */
export const deletePaymentRecord = async (recordId: string): Promise<ApiResponse<boolean>> => {
  // 根据配置使用Mock数据或真实API
  if (USE_MOCK_API) {
    return deletePaymentRecordMock(recordId);
  }

  // 生产环境调用真实API（保持原有接口不变）
  return request({
    url: `/api/finance/payment-record/${recordId}`,
    method: 'DELETE'
  });
};

/**
 * 导出整车收款数据
 * @param params 搜索参数
 * @returns 导出结果
 */
export const exportWholeVehicleCollectionData = async (
  params: WholeVehicleCollectionSearchParams
): Promise<ApiResponse<string>> => {
  // 生产环境调用真实API（保持原有接口不变）
  return request({
    url: '/api/finance/whole-vehicle-collection/export',
    method: 'POST',
    data: params,
    responseType: 'blob'
  });
};

/**
 * 批量操作整车收款记录
 * @param orderIds 订单ID列表
 * @param operation 操作类型
 * @returns 操作结果
 */
export const batchOperateWholeVehicleCollection = async (
  orderIds: string[],
  operation: string
): Promise<ApiResponse<boolean>> => {
  // 生产环境调用真实API（保持原有接口不变）
  return request({
    url: '/api/finance/whole-vehicle-collection/batch-operate',
    method: 'POST',
    data: {
      orderIds,
      operation
    }
  });
};
