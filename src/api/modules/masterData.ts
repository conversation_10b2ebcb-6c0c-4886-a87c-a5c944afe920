import request from '@/api';

// 主数据项接口
export interface MasterDataItem {
  id: string;
  code: string;
  name: string;
  nameEn?: string;
  nameMs?: string;
  isActive?: boolean;
  sort?: number;
}

// 车型数据接口
export interface VehicleModel extends MasterDataItem {
  modelCode: string;
  modelName: string;
  brand: string;
  category: string;
}

// 车型配置接口
export interface VehicleVariant extends MasterDataItem {
  modelId: string;
  variantCode: string;
  variantName: string;
  engineType: string;
  transmission: string;
  price: number;
}

// 车辆颜色接口
export interface VehicleColor extends MasterDataItem {
  colorCode: string;
  colorName: string;
  hexCode: string;
  isMetallic: boolean;
}

// 门店信息接口
export interface Store extends MasterDataItem {
  storeCode: string;
  storeName: string;
  dealerId: string;
  dealerName: string;
  address: string;
  phone: string;
  manager: string;
}

// 经销商信息接口
export interface Dealer extends MasterDataItem {
  dealerCode: string;
  dealerName: string;
  region: string;
  address: string;
  phone: string;
  email: string;
}

// 销售顾问接口
export interface SalesConsultant extends MasterDataItem {
  userId: string;
  userName: string;
  employeeNo: string;
  storeId: string;
  storeName: string;
  phone: string;
  email: string;
}

// 服务顾问接口
export interface ServiceAdvisor extends MasterDataItem {
  userId: string;
  userName: string;
  employeeNo: string;
  storeId: string;
  storeName: string;
  phone: string;
  email: string;
  specialties: string[];
}

// 技师接口
export interface Technician extends MasterDataItem {
  userId: string;
  userName: string;
  employeeNo: string;
  storeId: string;
  storeName: string;
  level: string;
  specialties: string[];
  certifications: string[];
}

// 仓库接口
export interface Warehouse extends MasterDataItem {
  warehouseCode: string;
  warehouseName: string;
  storeId: string;
  storeName: string;
  type: string; // 'main' | 'sub' | 'virtual'
  address: string;
}

// 供应商接口
export interface Supplier extends MasterDataItem {
  supplierCode: string;
  supplierName: string;
  type: string;
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
}

// 零件名称接口
export interface PartsName extends MasterDataItem {
  partCode: string;
  partName: string;
  category: string;
  brand: string;
  specification: string;
  unit: string;
}

// API响应接口
export interface MasterDataResponse<T> {
  success: boolean;
  message: string;
  code: string;
  traceId: string;
  result: T[];
  timestamp: number;
}

/**
 * 获取车型列表
 * @returns Promise<VehicleModel[]>
 */
export const getVehicleModelList = async (): Promise<VehicleModel[]> => {
  try {
    const response = await request.get<any, MasterDataResponse<VehicleModel>>('/master-data/vehicle-models');

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取车型数据失败');
  } catch (error) {
    console.error('获取车型数据失败:', error);
    throw error;
  }
};

/**
 * 获取车型配置列表
 * @param modelId 车型ID，可选
 * @returns Promise<VehicleVariant[]>
 */
export const getVehicleVariantList = async (modelId?: string): Promise<VehicleVariant[]> => {
  try {
    const params = modelId ? { modelId } : {};
    const response = await request.get<any, MasterDataResponse<VehicleVariant>>('/master-data/vehicle-variants', {
      params
    });

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取车型配置数据失败');
  } catch (error) {
    console.error('获取车型配置数据失败:', error);
    throw error;
  }
};

/**
 * 获取车辆颜色列表
 * @param modelId 车型ID，可选
 * @returns Promise<VehicleColor[]>
 */
export const getVehicleColorList = async (modelId?: string): Promise<VehicleColor[]> => {
  try {
    const params = modelId ? { modelId } : {};
    const response = await request.get<any, MasterDataResponse<VehicleColor>>('/master-data/vehicle-colors', {
      params
    });

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取车辆颜色数据失败');
  } catch (error) {
    console.error('获取车辆颜色数据失败:', error);
    throw error;
  }
};

/**
 * 获取门店列表
 * @param dealerId 经销商ID，可选
 * @returns Promise<Store[]>
 */
export const getStoreList = async (dealerId?: string): Promise<Store[]> => {
  try {
    const params = dealerId ? { dealerId } : {};
    const response = await request.get<any, MasterDataResponse<Store>>('/master-data/dealers', {
      params
    });

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取门店数据失败');
  } catch (error) {
    console.error('获取门店数据失败:', error);
    throw error;
  }
};

/**
 * 获取经销商列表
 * @returns Promise<Dealer[]>
 */
export const getDealerList = async (): Promise<Dealer[]> => {
  try {
    const response = await request.get<any, MasterDataResponse<Dealer>>('/master-data/dealers');

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取经销商数据失败');
  } catch (error) {
    console.error('获取经销商数据失败:', error);
    throw error;
  }
};

/**
 * 获取销售顾问列表
 * @param storeId 门店ID，可选
 * @returns Promise<SalesConsultant[]>
 */
export const getSalesConsultantList = async (storeId?: string): Promise<SalesConsultant[]> => {
  try {
    const params = storeId ? { storeId } : {};
    const response = await request.get<any, MasterDataResponse<SalesConsultant>>('/master-data/sales-consultants', {
      params
    });

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取销售顾问数据失败');
  } catch (error) {
    console.error('获取销售顾问数据失败:', error);
    throw error;
  }
};

/**
 * 获取服务顾问列表
 * @param storeId 门店ID，可选
 * @returns Promise<ServiceAdvisor[]>
 */
export const getServiceAdvisorList = async (): Promise<ServiceAdvisor[]> => {
  try {
    const response = await request.get<any, MasterDataResponse<ServiceAdvisor>>('/master-data/sales-consultants');

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取服务顾问数据失败');
  } catch (error) {
    console.error('获取服务顾问数据失败:', error);
    throw error;
  }
};

/**
 * 获取技师列表
 * @param storeId 门店ID，可选
 * @returns Promise<Technician[]>
 */
export const getTechnicianList = async (): Promise<Technician[]> => {
  try {
    const response = await request.get<any, MasterDataResponse<Technician>>('/master-data/technicians');

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取技师数据失败');
  } catch (error) {
    console.error('获取技师数据失败:', error);
    throw error;
  }
};

/**
 * 获取仓库列表
 * @param storeId 门店ID，可选
 * @returns Promise<Warehouse[]>
 */
export const getWarehouseList = async (storeId?: string): Promise<Warehouse[]> => {
  try {
    const params = storeId ? { storeId } : {};
    const response = await request.get<any, MasterDataResponse<Warehouse>>('/master-data/warehouses', {
      params
    });

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取仓库数据失败');
  } catch (error) {
    console.error('获取仓库数据失败:', error);
    throw error;
  }
};

/**
 * 获取供应商列表
 * @returns Promise<Supplier[]>
 */
export const getSupplierList = async (): Promise<Supplier[]> => {
  try {
    const response = await request.get<any, MasterDataResponse<Supplier>>('/master-data/suppliers');

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取供应商数据失败');
  } catch (error) {
    console.error('获取供应商数据失败:', error);
    throw error;
  }
};

/**
 * 获取零件名称列表
 * @param category 零件类别，可选
 * @returns Promise<PartsName[]>
 */
export const getPartsNameList = async (category?: string): Promise<PartsName[]> => {
  try {
    const params = category ? { category } : {};
    const response = await request.get<any, MasterDataResponse<PartsName>>('/master-data/parts-names', {
      params
    });

    if (response.success && response.result) {
      return response.result;
    }

    throw new Error(response.message || '获取零件名称数据失败');
  } catch (error) {
    console.error('获取零件名称数据失败:', error);
    throw error;
  }
};
