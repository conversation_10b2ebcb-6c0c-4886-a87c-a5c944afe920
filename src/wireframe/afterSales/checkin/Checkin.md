# 到店登记管理 - 线框图

## 页面概述
到店登记管理是售后模块的核心功能页面，用于管理客户车辆到店登记信息，包括车辆信息、客户信息、服务类型等。页面支持登记信息的增删改查、导出等操作。

## 页面结构

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                      到店登记管理                                                   │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘

┌─ 筛选条件 ─────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                                 │
│  ┌──登记编号────┐ ┌──车牌号──────┐ ┌──送修人姓名───┐ ┌──送修人电话───┐                           │
│  │[           ]│ │[           ]│ │[           ]│ │[           ]│                           │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘                           │
│                                                                                                 │
│  ┌──登记时间────────────────────┐                                                              │
│  │[ 开始日期 ] - [ 结束日期   ]│                                                              │
│  └───────────────────────────────┘                                                              │
│                                                                              [搜索] [重置]    │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘

┌─ 操作区 ───────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                   [+ 新增登记] [↓ 导出]        │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘

┌─ 数据表格 ─────────────────────────────────────────────────────────────────────────────────────────┐
│┌──┬─────┬──────┬──────────┬──────┬──────────┬────┬────┬──────┬──────┬──────┬────────┬──────┬────────┬────────┬──────────┐│
││序│登记 │车牌号│  VIN码   │车型  │  车辆配置  │颜色│里程│送修人│联系  │服务  │维修工单 │服务  │创建时间 │更新时间 │   操作   ││
││号│编号 │      │          │      │           │    │数  │姓名  │电话  │顾问  │  编号   │类型  │        │        │          ││
│├──┼─────┼──────┼──────────┼──────┼──────────┼────┼────┼──────┼──────┼──────┼────────┼──────┼────────┼────────┼──────────┤│
││1 │CK001│京A123│ABCD123...│Model1│  高配版   │白色│2万km│张三  │138..│李顾问│   -    │维修  │2024-01 │2024-01 │查看 编辑 ││
││  │     │     │          │      │           │    │     │      │      │      │        │      │        │        │创建工单  ││
│├──┼─────┼──────┼──────────┼──────┼──────────┼────┼────┼──────┼──────┼──────┼────────┼──────┼────────┼────────┼──────────┤│
││2 │CK002│京B456│EFGH456...│Model2│  标准版   │黑色│5万km│王五  │139..│赵顾问│RO12345 │维修  │2024-01 │2024-01 │查看 编辑 ││
││  │     │     │          │      │           │    │     │      │      │      │        │      │        │        │删除      ││
│├──┼─────┼──────┼──────────┼──────┼──────────┼────┼────┼──────┼──────┼──────┼────────┼──────┼────────┼────────┼──────────┤│
││3 │CK003│京C789│IJKL789...│Model3│  豪华版   │红色│1万km│赵六  │137..│孙顾问│   -    │维修  │2024-01 │2024-01 │查看 编辑 ││
││  │     │     │          │      │           │    │     │      │      │      │        │      │        │        │创建工单  ││
│└──┴─────┴──────┴──────────┴──────┴──────────┴────┴────┴──────┴──────┴──────┴────────┴──────┴────────┴────────┴──────────┘│
└─────────────────────────────────────────────────────────────────────────────────────────────────┘

┌─ 分页器 ───────────────────────────────────────────────────────────────────────────────────────────┐
│                                      共 120 条 每页显示 [10▼] 条  ◀ 1 2 3 4 5 ▶  跳转 [  ] 页 GO │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
```

## 功能区域说明

### 1. 页面标题
- 显示"到店登记管理"作为页面主标题
- 采用H1样式，居左对齐

### 2. 筛选条件区域 (CheckinSearchForm.vue)
- **布局**: 4列网格布局 (el-col :span="6")
- **字段**:
  - 登记编号: 文本输入框，支持模糊搜索
  - 车牌号: 文本输入框，支持模糊搜索  
  - 送修人姓名: 文本输入框，支持模糊搜索
  - 送修人电话: 文本输入框，支持模糊搜索
  - 登记时间: 日期范围选择器，支持开始和结束日期
- **操作按钮**: 搜索、重置，右对齐

### 3. 操作区域
- **新增登记**: 主要按钮，打开新增登记弹窗
- **导出**: 普通按钮，导出当前筛选结果

### 4. 数据表格区域 (CheckinTable.vue)
- **表格列** (共16列):
  1. 序号 (index)
  2. 登记编号 (checkinId)
  3. 车牌号 (licensePlate)
  4. VIN码 (vin)
  5. 车型 (vehicleModel)
  6. 车辆配置 (vehicleConfiguration)
  7. 颜色 (color)
  8. 里程数 (mileage) - 显示单位"km"
  9. 送修人姓名 (repairPersonName)
  10. 联系电话 (repairPersonPhone)
  11. 服务顾问 (serviceAdvisor)
  12. 维修工单编号 (relatedRepairOrderId) - 无关联显示"-"
  13. 服务类型 (serviceType)
  14. 创建时间 (createdAt)
  15. 更新时间 (updatedAt)
  16. 操作列 (固定右侧，宽度280px)

- **操作按钮**:
  - 查看详情: 所有记录都有
  - 编辑: 所有记录都有
  - 删除: 仅未关联维修工单的记录显示
  - 创建维修单: 仅未关联维修工单的记录显示

### 5. 分页器
- Element Plus标准分页组件
- 支持每页条数选择: [10, 20, 50, 100]
- 显示总条数、页码跳转等功能

## 交互说明

### 搜索筛选
- 支持多条件组合搜索
- 日期范围选择支持快捷选择
- 重置按钮清空所有筛选条件

### 表格操作
- 表格数据支持loading状态
- 空数据状态显示"暂无数据"
- 行操作按钮根据数据状态动态显示

### 状态管理
- 已关联维修工单的记录不能删除
- 已关联维修工单的记录不能创建新的维修单
- 所有记录都支持查看详情和编辑

## 相关组件
- CheckinSearchForm.vue - 搜索表单组件
- CheckinTable.vue - 数据表格组件  
- CheckinFormDialog.vue - 新增/编辑弹窗组件
- CheckinDetailDialog.vue - 详情查看弹窗组件

## 数据流转
1. 页面加载时获取登记列表数据
2. 搜索操作触发列表刷新
3. 新增/编辑操作完成后刷新列表
4. 删除操作需要确认，完成后刷新列表
5. 创建维修单操作完成后刷新列表状态