# 到店登记表单弹窗 - 线框图

## 组件概述
新增/编辑登记弹窗用于创建新的到店登记记录或编辑现有记录。弹窗宽度600px，采用分区块表单布局，支持车牌号查询车辆信息自动填充功能。

## 弹窗结构

```
弹窗: 新增/编辑到店登记弹窗
┌────────────────────── 新增到店登记 / 编辑到店登记 ──────────────────────┐
│                                                                    │
│  ┌──车牌号查询────────────┐                      车辆信息自动填充      │
│  │[车牌号        ] [🔍]  │                                          │
│  └──────────────────────┘                                          │
│                                                                    │
│  ┌─ 车辆信息 ────────────────────────────────────────────────────┐  │
│  │  ┌─车牌号─────┐ ┌─VIN码──────┐                                │  │
│  │  │[        ] │ │[         ] │                                │  │
│  │  └───────────┘ └────────────┘                                │  │
│  │  ┌─车型───────┐ ┌─车辆配置────┐                                │  │
│  │  │[        ] │ │[         ] │                                │  │
│  │  └───────────┘ └────────────┘                                │  │
│  │  ┌─颜色───────┐ ┌─里程数──────┐                                │  │
│  │  │[        ] │ │[     ] km │                                │  │
│  │  └───────────┘ └────────────┘                                │  │
│  │  ┌─车龄───────┐                                              │  │
│  │  │[        ] │ (自动计算)                                    │  │
│  │  └───────────┘                                              │  │
│  └──────────────────────────────────────────────────────────────┘  │
│                                                                    │
│  ┌─ 客户信息 ────────────────────────────────────────────────────┐  │
│  │  ┌─送修人姓名──┐ ┌─送修人电话──┐                                │  │
│  │  │[*必填    ] │ │[*必填    ] │                                │  │
│  │  └────────────┘ └────────────┘                                │  │
│  │  ┌─服务顾问────┐                                              │  │
│  │  │[当前用户  ] │ (只读)                                       │  │
│  │  └────────────┘                                              │  │
│  └──────────────────────────────────────────────────────────────┘  │
│                                                                    │
│  ┌─服务类型─────────────────────────────────────────────────────┐    │
│  │[维修           ] (固定值)                                    │    │
│  └─────────────────────────────────────────────────────────────┘    │
│                                                                    │
│  ┌─备注信息─────────────────────────────────────────────────────┐    │
│  │┌─────────────────────────────────────────────────────────┐  │    │
│  ││                                                         │  │    │
│  ││                                                         │  │    │
│  │└─────────────────────────────────────────────────────────┘  │    │
│  └─────────────────────────────────────────────────────────────┘    │
│                                                                    │
│                                                    [取消] [保存]    │
└────────────────────────────────────────────────────────────────────┘
```

## 布局规范

### 弹窗属性
- **标题**: 新增模式"新增到店登记" / 编辑模式"编辑到店登记"
- **宽度**: 600px
- **表单布局**: label-position="top" (标签在顶部)

### 区域划分
1. **车牌号查询区**: 顶部查询工具
2. **车辆信息区**: 卡片容器，2列网格布局
3. **客户信息区**: 卡片容器，2列网格布局
4. **服务类型**: 单独表单项
5. **备注信息**: 多行文本输入

## 功能区域详解

### 1. 车牌号查询区
- **布局**: 1行，左侧输入框(18列) + 右侧提示文字(6列)
- **输入框**: 带搜索按钮的输入框
- **功能**: 
  - 支持回车键触发查询
  - 编辑模式下如果已有登记编号则禁用
- **提示文字**: "车辆信息自动填充"

### 2. 车辆信息区 (el-card)
- **标题**: "车辆信息"
- **布局**: 2列网格布局 (el-col :span="12")
- **字段列表**:
  - 车牌号 (licensePlate) - 可能只读
  - VIN码 (vin) - 可能只读
  - 车型 (vehicleModel) - 可能只读
  - 车辆配置 (vehicleConfiguration) - 可能只读
  - 颜色 (color) - 可能只读
  - 里程数 (mileage) - 数字输入，带"km"后缀
  - 车龄 (vehicleAge) - 只读，自动计算

**只读规则**: 编辑模式下且有登记编号时，车辆信息字段变为只读

### 3. 客户信息区 (el-card)
- **标题**: "客户信息"
- **布局**: 2列网格布局
- **字段列表**:
  - 送修人姓名 (repairPersonName) - *必填*
  - 送修人电话 (repairPersonPhone) - *必填*
  - 服务顾问 (serviceAdvisor) - *必填* (只读，默认当前用户)

### 4. 服务类型
- **字段**: serviceType
- **默认值**: "维修"
- **状态**: 只读 (disabled)
- **验证**: 必填

### 5. 备注信息
- **字段**: notes
- **类型**: 多行文本 (textarea)
- **行数**: 3行
- **验证**: 非必填

## 交互行为说明

### 车牌号查询功能
1. 输入车牌号后点击搜索或按回车
2. 调用车辆信息查询API
3. 成功时自动填充车辆相关字段
4. 失败时清空车辆信息并提示

### 表单验证规则
- **送修人姓名**: 必填验证
- **送修人电话**: 必填验证  
- **服务顾问**: 必填验证
- **服务类型**: 必填验证

### 数据初始化
- **新增模式**: 
  - 清空所有字段
  - 服务顾问设为当前用户
  - 服务类型设为"维修"
- **编辑模式**:
  - 填充现有记录数据
  - 车辆信息可能设为只读

### 提交处理
1. 表单验证通过后
2. 移除客户端计算字段(如vehicleAge)
3. 触发submit事件传递表单数据
4. 父组件处理API调用

## 技术实现要点

### 组件引用
```typescript
import { 
  ElDialog, ElForm, ElFormItem, ElInput, ElButton, 
  ElCard, ElRow, ElCol, ElText, ElMessage 
} from 'element-plus'
```

### 数据结构
```typescript
interface Props {
  visible: boolean;
  isEdit: boolean;
  recordData: CheckinListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', data: CheckinFormData): void;
}
```

### 响应式数据
```typescript
const dialogForm = reactive<CheckinFormData>({
  licensePlate: '',
  vin: '',
  vehicleModel: '',
  vehicleConfiguration: '',
  color: '',
  mileage: undefined,
  vehicleAge: undefined,
  repairPersonName: '',
  repairPersonPhone: '',
  serviceAdvisor: '',
  relatedRepairOrderId: null,
  serviceType: '维修',
  notes: '',
  isDeleted: false
})
```

## 样式规范
- 弹窗宽度固定600px
- 卡片容器有标题和内边距
- 表单项垂直间距20px
- 按钮右对齐，间距10px
- 输入框样式统一