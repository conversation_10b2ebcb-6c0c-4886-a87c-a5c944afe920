# 到店登记数据表格 - 线框图

## 组件概述
数据表格组件用于展示到店登记列表数据，支持加载状态、空数据状态、分页功能和行操作。表格包含16个字段列和操作列。

## 表格结构

```
┌─ 数据表格 ─────────────────────────────────────────────────────────────────────────────────────────┐
│┌──┬─────┬──────┬──────────┬──────┬──────────┬────┬────┬──────┬──────┬──────┬────────┬──────┬────────┬────────┬──────────┐│
││序│登记 │车牌号│  VIN码   │车型  │  车辆配置  │颜色│里程│送修人│联系  │服务  │维修工单 │服务  │创建时间 │更新时间 │   操作   ││
││号│编号 │      │          │      │           │    │数  │姓名  │电话  │顾问  │  编号   │类型  │        │        │          ││
│├──┼─────┼──────┼──────────┼──────┼──────────┼────┼────┼──────┼──────┼──────┼────────┼──────┼────────┼────────┼──────────┤│
││1 │CK001│京A123│ABCD123...│Model1│  高配版   │白色│2万km│张三  │138..│李顾问│   -    │维修  │2024-01 │2024-01 │查看 编辑 ││
││  │     │     │          │      │           │    │     │      │      │      │        │      │        │        │创建工单  ││
│├──┼─────┼──────┼──────────┼──────┼──────────┼────┼────┼──────┼──────┼──────┼────────┼──────┼────────┼────────┼──────────┤│
││2 │CK002│京B456│EFGH456...│Model2│  标准版   │黑色│5万km│王五  │139..│赵顾问│RO12345 │维修  │2024-01 │2024-01 │查看 编辑 ││
││  │     │     │          │      │           │    │     │      │      │      │        │      │        │        │删除      ││
│├──┼─────┼──────┼──────────┼──────┼──────────┼────┼────┼──────┼──────┼──────┼────────┼──────┼────────┼────────┼──────────┤│
││3 │CK003│京C789│IJKL789...│Model3│  豪华版   │红色│1万km│赵六  │137..│孙顾问│   -    │维修  │2024-01 │2024-01 │查看 编辑 ││
││  │     │     │          │      │           │    │     │      │      │      │        │      │        │        │创建工单  ││
│└──┴─────┴──────┴──────────┴──────┴──────────┴────┴────┴──────┴──────┴──────┴────────┴──────┴────────┴────────┴──────────┘│
└─────────────────────────────────────────────────────────────────────────────────────────────────┘

┌─ 分页器 ───────────────────────────────────────────────────────────────────────────────────────────┐
│                                      共 120 条 每页显示 [10▼] 条  ◀ 1 2 3 4 5 ▶  跳转 [  ] 页 GO │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
```

## 表格规范

### 容器属性
- **组件**: el-card (CSS类: table-card)
- **表格**: el-table
- **属性**: border, v-loading, style="width: 100%"
- **空数据文本**: "暂无数据"

### 列配置详解

#### 1. 序号列
- **类型**: index
- **标题**: "序号" 
- **宽度**: 60px

#### 2-15. 数据列
| 序号 | 字段名 | 标题 | 最小宽度 | 特殊处理 |
|------|--------|------|----------|----------|
| 2 | checkinId | 登记编号 | 120px | - |
| 3 | licensePlate | 车牌号 | 120px | - |
| 4 | vin | VIN码 | 140px | - |
| 5 | vehicleModel | 车型 | 120px | - |
| 6 | vehicleConfiguration | 车辆配置 | 150px | - |
| 7 | color | 颜色 | 80px | - |
| 8 | mileage | 里程数 | 100px | 格式化显示 |
| 9 | repairPersonName | 送修人姓名 | 120px | - |
| 10 | repairPersonPhone | 送修人电话 | 120px | - |
| 11 | serviceAdvisor | 服务顾问 | 100px | - |
| 12 | relatedRepairOrderId | 维修工单编号 | 140px | 空值显示"-" |
| 13 | serviceType | 服务类型 | 100px | - |
| 14 | createdAt | 创建时间 | 120px | - |
| 15 | updatedAt | 更新时间 | 120px | - |

#### 16. 操作列
- **标题**: "操作"
- **宽度**: 280px (固定)
- **位置**: fixed="right" (固定右侧)

## 数据格式化

### 里程数格式化
```typescript
// 第8列 mileage
<template #default="scope">
  {{ scope.row.mileage ? `${scope.row.mileage} km` : '-' }}
</template>
```

### 维修工单编号格式化
```typescript  
// 第12列 relatedRepairOrderId
<template #default="scope">
  {{ scope.row.relatedRepairOrderId || '-' }}
</template>
```

## 操作按钮

### 按钮配置
1. **查看详情**
   - 类型: primary + link
   - 图标: View
   - 条件: 所有记录都显示
   - 点击: handleViewDetails

2. **编辑**
   - 类型: primary + link  
   - 图标: Edit
   - 条件: 所有记录都显示
   - 点击: handleEditRecord

3. **删除**
   - 类型: danger + link
   - 图标: Delete
   - 条件: v-if="!scope.row.relatedRepairOrderId" (未关联维修工单)
   - 点击: handleDeleteRecord

4. **创建维修单**
   - 类型: success + link
   - 图标: Plus
   - 条件: v-if="!scope.row.relatedRepairOrderId" (未关联维修工单)
   - 点击: handleCreateRepairOrder

### 操作按钮逻辑
- 已关联维修工单的记录: 只显示"查看详情"和"编辑"
- 未关联维修工单的记录: 显示全部4个操作按钮

## 分页器配置

### 分页属性
- **组件**: el-pagination
- **当前页**: pagination.page
- **每页条数**: pagination.pageSize  
- **总条数**: pagination.total
- **每页条数选项**: [10, 20, 50, 100]
- **布局**: "total, sizes, prev, pager, next, jumper"
- **样式**: small=false, background=true

### 分页事件
- **页码变化**: @current-change="handlePageChange"
- **每页条数变化**: @size-change="handlePageSizeChange"

## 数据接口

### Props接口
```typescript
interface Props {
  checkinList: CheckinListItem[];  // 表格数据
  loading: boolean;                // 加载状态
  pagination: {                    // 分页信息
    page: number;
    pageSize: number;
    total: number;
  };
}
```

### Emits接口
```typescript
interface Emits {
  (e: 'view-details', row: CheckinListItem): void;
  (e: 'edit-record', row: CheckinListItem): void;
  (e: 'delete-record', row: CheckinListItem): void;
  (e: 'create-repair-order', row: CheckinListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}
```

## 状态处理

### 加载状态
- 表格支持 v-loading 指令
- 显示加载遮罩和旋转图标

### 空数据状态
- empty-text="暂无数据"
- 无数据时显示空状态提示

### 错误处理
- 数据加载失败时保持空状态
- 操作失败时不影响表格显示

## 样式规范

### CSS类定义
```scss
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap;  // 防止文字换行
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;  // 分页器右对齐
}
```

### 表格样式
- 带边框表格 (border)
- 表头和单元格不换行
- 分页器右对齐
- 卡片容器有下边距

## 响应式处理
- 表格支持横向滚动
- 操作列固定在右侧
- 最小宽度确保内容可读性

## 技术实现要点

### 组件引用
```typescript
import { ElCard, ElTable, ElTableColumn, ElButton, ElPagination } from 'element-plus'
import { View, Edit, Delete, Plus } from '@element-plus/icons-vue'
```

### 事件处理
- 所有操作按钮都通过emit传递事件到父组件
- 分页事件直接更新分页状态
- 支持键盘操作和无障碍访问