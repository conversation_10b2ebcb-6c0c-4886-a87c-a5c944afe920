# 到店登记搜索表单 - 线框图

## 组件概述
搜索表单组件用于提供到店登记列表的筛选功能，支持多种条件组合搜索。采用卡片容器布局，标签位置在顶部。

## 组件结构

```
┌─ 筛选条件 ─────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                                 │
│  ┌──登记编号────┐ ┌──车牌号──────┐ ┌──送修人姓名───┐ ┌──送修人电话───┐                           │
│  │[           ]│ │[           ]│ │[           ]│ │[           ]│                           │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘                           │
│                                                                                                 │
│  ┌──登记时间────────────────────┐                                                              │
│  │[ 开始日期 ] - [ 结束日期   ]│                                                              │
│  └───────────────────────────────┘                                                              │
│                                                                              [搜索] [重置]    │
└─────────────────────────────────────────────────────────────────────────────────────────────────┘
```

## 布局规范

### 容器属性
- **组件**: el-card
- **CSS类**: mb-20 search-card
- **表单布局**: label-position="top" (标签在顶部)

### 网格布局
- **第一行**: 4列等分 (el-col :span="6")
- **第二行**: 1列 + 空白列 (el-col :span="6" + :span="18")  
- **第三行**: 按钮区域 (el-col :span="24")

## 字段详解

### 第一行字段 (4列布局)
1. **登记编号** (checkinId)
   - 组件: el-input
   - 占位符: "请输入登记编号"
   - 功能: clearable (可清空)

2. **车牌号** (licensePlate)  
   - 组件: el-input
   - 占位符: "请输入车牌号"
   - 功能: clearable

3. **送修人姓名** (repairPersonName)
   - 组件: el-input  
   - 占位符: "请输入送修人姓名"
   - 功能: clearable

4. **送修人电话** (repairPersonPhone)
   - 组件: el-input
   - 占位符: "请输入送修人电话"  
   - 功能: clearable

### 第二行字段 (1列 + 空白)
5. **登记时间** (createdAt)
   - 组件: el-date-picker
   - 类型: daterange (日期范围)
   - 分隔符: "-"
   - 开始占位符: "开始日期"
   - 结束占位符: "结束日期"
   - 值格式: "YYYY-MM-DD"
   - 功能: clearable
   - 占用: 6列
   - 右侧空白: 18列

### 第三行操作按钮
- **搜索按钮**: 
  - 类型: primary
  - 图标: Search
  - 位置: 右对齐
- **重置按钮**:
  - 类型: default
  - 位置: 右对齐

## 数据绑定

### Props接口
```typescript
interface Props {
  searchParams: CheckinListParams;
  dateRange: [string, string] | null;
}
```

### Emits接口
```typescript
interface Emits {
  (e: 'update:searchParams', value: CheckinListParams): void;
  (e: 'update:dateRange', value: [string, string] | null): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}
```

### 搜索参数结构
```typescript
interface CheckinListParams {
  checkinId: string;        // 登记编号
  licensePlate: string;     // 车牌号
  repairPersonName: string; // 送修人姓名
  repairPersonPhone: string;// 送修人电话
  createdAtStart: string;   // 开始时间
  createdAtEnd: string;     // 结束时间
}
```

## 交互行为

### 搜索操作
- 点击搜索按钮触发 'search' 事件
- 父组件接收事件并执行搜索逻辑
- 重置分页到第一页

### 重置操作  
- 点击重置按钮触发 'reset' 事件
- 清空所有搜索条件
- 清空日期范围选择
- 重置分页到第一页

### 数据更新
- 单个字段更新通过 updateSearchParams 方法
- 日期范围更新通过 updateDateRange 方法
- 支持 v-model 双向绑定

## 样式规范

### CSS类定义
```scss
.search-form {
  .el-form-item {
    margin-right: 20px;
    margin-bottom: 15px;
    &:last-child {
      margin-right: 0;
    }
  }
  .search-buttons {
    margin-bottom: 15px;
    .el-button {
      margin-left: 10px;
    }
  }
}

.buttons-col {
  text-align: right;
  :deep(.el-form-item__content) {
    justify-content: flex-end;
  }
}
```

### 间距规范
- 表单项右边距: 20px
- 表单项下边距: 15px
- 按钮左边距: 10px
- 卡片下边距: 20px

## 响应式设计
- 支持不同屏幕尺寸下的栅格布局
- 按钮区域始终右对齐
- 表单项自动换行处理

## 技术实现要点

### 组件引用
```typescript
import { 
  ElCard, ElForm, ElFormItem, ElInput, 
  ElDatePicker, ElButton, ElRow, ElCol 
} from 'element-plus'
import { Search } from '@element-plus/icons-vue'
```

### 国际化支持
- 所有文本使用 useModuleI18n('afterSales.checkin')
- 占位符文本支持多语言
- 按钮文本支持多语言