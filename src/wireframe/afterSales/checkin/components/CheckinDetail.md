# 到店登记详情弹窗 - 线框图

## 组件概述
详情查看弹窗用于展示到店登记记录的完整信息，采用只读形式的描述列表布局，宽度为500px。

## 弹窗结构

```
弹窗: 到店登记详情查看
┌──────────────────────── 到店登记详情 ────────────────────────┐
│                                                            │
│  ┌─────────────────────────────────────────────────────┐    │
│  │登记编号        │ CK001                              │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │车牌号          │ 京A12345                           │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │VIN码           │ ABCD123456789                      │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │车型            │ Model1                             │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │车辆配置        │ 高配版                              │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │颜色            │ 白色                               │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │里程数          │ 20000 km                           │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │车龄            │ 24 个月                            │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │送修人姓名      │ 张三                               │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │送修人电话      │ 13812345678                        │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │服务顾问        │ 李顾问                              │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │关联维修工单    │ -                                  │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │服务类型        │ 维修                               │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │创建时间        │ 2024-01-01 10:00                   │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │更新时间        │ 2024-01-01 10:00                   │    │
│  ├─────────────────┼───────────────────────────────────┤    │
│  │备注            │ 客户反映发动机异响                  │    │
│  └─────────────────┴───────────────────────────────────┘    │
│                                                            │
│                                                    [关闭]  │
└────────────────────────────────────────────────────────────┘
```

## 布局规范

### 弹窗属性
- **标题**: "到店登记详情"
- **宽度**: 500px
- **关闭方式**: 点击关闭按钮或遮罩层关闭

### 内容布局
- **组件**: Element Plus el-descriptions
- **列数**: 单列布局 (:column="1")
- **样式**: 带边框 (border)

## 字段列表

### 完整字段映射 (16个字段)
1. **登记编号** (checkinId) - 必显示
2. **车牌号** (licensePlate) - 必显示
3. **VIN码** (vin) - 必显示
4. **车型** (vehicleModel) - 必显示
5. **车辆配置** (vehicleConfiguration) - 必显示
6. **颜色** (color) - 必显示
7. **里程数** (mileage) - 显示格式: "数值 km"，无数据显示"-"
8. **车龄** (vehicleAge) - 显示格式: "数值 个月"，无数据显示"-"
9. **送修人姓名** (repairPersonName) - 必显示
10. **送修人电话** (repairPersonPhone) - 必显示
11. **服务顾问** (serviceAdvisor) - 必显示
12. **关联维修工单** (relatedRepairOrderId) - 无关联显示"-"
13. **服务类型** (serviceType) - 必显示
14. **创建时间** (createdAt) - 必显示
15. **更新时间** (updatedAt) - 必显示
16. **备注** (notes) - 无备注显示"-"

## 数据处理规则

### 空值处理
- 里程数无数据: 显示"-"
- 车龄无数据: 显示"-"  
- 关联维修工单无数据: 显示"-"
- 备注无数据: 显示"-"

### 格式化规则
- 里程数: `${value} km`
- 车龄: `${value} 个月`
- 时间格式: YYYY-MM-DD HH:mm

## 交互行为

### 弹窗控制
- 通过 v-model:visible 控制显示/隐藏
- 支持ESC键关闭
- 支持点击遮罩层关闭

### 数据绑定
- 通过 recordData 属性接收当前记录数据
- 支持响应式数据更新

## 技术实现要点

### 组件引用
```typescript
import { ElDialog, ElDescriptions, ElDescriptionsItem } from 'element-plus'
```

### 数据结构
```typescript
interface Props {
  visible: boolean;
  recordData: CheckinListItem | null;
}
```

## 样式规范
- 弹窗宽度固定500px
- 描述列表采用单列布局
- 字段标签与内容左右对齐
- 按钮区域右对齐