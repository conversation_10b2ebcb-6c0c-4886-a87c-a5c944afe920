/**
 * 权限管理系统类型定义
 * 包含门店、部门、菜单、角色、用户组、用户等相关类型
 */

// ================================
// 基础通用类型
// ================================

/** 基础实体接口 */
export interface BaseEntity {
  /** 主键ID */
  id: string
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
  /** 创建人 */
  creator?: string
  /** 更新人 */
  updater?: string
  /** 备注 */
  remark?: string
}

/** 状态枚举 */
export type EntityStatus = 'normal' | 'disabled'

/** 树形结构接口 */
export interface TreeNode {
  /** 节点ID */
  id: string
  /** 父节点ID */
  parentId?: string
  /** 子节点 */
  children?: TreeNode[]
  /** 是否有子节点 */
  hasChildren?: boolean
}

/** 分页查询参数基础接口 */
export interface PageQueryParams {
  /** 当前页 */
  current?: number
  /** 每页大小 */
  size?: number
}

// ================================
// 门店管理相关类型
// ================================

/**
 * 门店信息
 */
export interface Store {
  id: string;
  storeCode: string;
  storeName: string;
  storeStatus: 'active' | 'inactive';
  createTime: string;
  // 修改 storeType 以包含 'headquarter'
  storeType?: 'main' | 'branch' | 'warehouse' | 'headquarter';
  manager?: string;
  contactPerson?: string;
  contactPhone?: string;
  // 移除 storeAddress，添加新的地址字段
  province?: string; // 省
  city?: string; // 市
  district?: string; // 区
  detailAddress?: string; // 详细地址
  storeProperties?: string[];
  remark?: string;
  // 添加父级ID和子级列表
  parentId?: string;
  children?: Store[];
}

/**
 * 门店查询参数
 */
export interface StoreQueryParams extends PageQueryParams {
  storeName?: string;
  storeCode?: string;
  storeStatus?: 'active' | 'inactive' | '';
}

/**
 * 新增门店请求
 */
export interface CreateStoreRequest {
  storeCode: string;
  storeName: string;
  storeStatus: '00020001' | '00020002';
  storeType: 'main' | 'branch' | 'warehouse' | 'headquarter';
  manager?: string;
  contactPerson?: string;
  contactPhone?: string;
  // 同样修改地址字段
  province?: string;
  city?: string;
  district?: string;
  detailAddress?: string;
  storeProperties?: string[];
  remark?: string;
  // 添加父级ID
  parentId?: string;
}

/**
 * 更新门店请求
 */
export type UpdateStoreRequest = Partial<CreateStoreRequest>;


// ================================
// 部门管理相关类型
// ================================

/** 部门类型枚举 */
export type DepartmentType = 'business' | 'support' | 'management'

/** 部门接口 */
export interface Department extends BaseEntity, TreeNode {
  /** 部门名称 */
  departmentName: string
  /** 部门编号 */
  departmentCode: string
  /** 上级部门ID */
  parentId?: string
  /** 部门层级 */
  departmentLevel?: number
  /** 部门状态 */
  departmentStatus: EntityStatus
  /** 部门类型 */
  departmentType: DepartmentType
  /** 部门负责人 */
  departmentHead?: string
  /** 部门描述 */
  description?: string
  /** 子部门 */
  children?: Department[]
}

/** 部门创建请求 */
export interface CreateDepartmentRequest {
  departmentName: string
  departmentCode: string
  parentId?: string
  departmentStatus: EntityStatus
  departmentType: DepartmentType
  departmentHead?: string
  description?: string
  remark?: string
}

/** 部门更新请求 */
export interface UpdateDepartmentRequest extends CreateDepartmentRequest {
  id: string
}

/** 部门查询参数 */
export interface DepartmentQueryParams extends PageQueryParams {
  departmentName?: string
  departmentCode?: string
  departmentType?: DepartmentType
  departmentStatus?: EntityStatus
  parentId?: string
}

// ================================
// 菜单管理相关类型
// ================================

/** 菜单类型枚举 */
export type MenuType = 'directory' | 'menu' | 'button'

/** 菜单所属端枚举 */
export type MenuSide = 'dealer' | 'factory'

/** 菜单接口 */
export interface Menu extends BaseEntity, TreeNode {
  /** 菜单名称 */
  menuName: string
  /** 菜单编号 */
  menuCode: string
  /** 菜单类型 */
  menuType: MenuType
  /** 菜单所属端 */
  menuSide: MenuSide
  /** 上级菜单ID */
  parentId?: string
  /** 菜单图标 */
  menuIcon?: string
  /** 菜单路径 */
  menuPath?: string
  /** 组件路径 */
  component?: string
  /** 权限标识 */
  permission?: string
  /** 排序 */
  sortOrder: number
  /** 菜单状态 */
  menuStatus: EntityStatus
  /** 是否显示 */
  isVisible: number
  /** 是否缓存 */
  isCache: boolean
  /** 子菜单 */
  children?: Menu[]
}

/** 菜单创建请求 */
export interface CreateMenuRequest {
  menuName: string
  menuCode: string
  menuType: MenuType
  menuSide: MenuSide
  parentId?: string
  menuIcon?: string
  menuPath?: string
  component?: string
  permission?: string
  sortOrder: number
  menuStatus: EntityStatus
  isVisible: number
  isCache: boolean
  remark?: string
}

/** 菜单更新请求 */
export interface UpdateMenuRequest extends CreateMenuRequest {
  id: string
}

/** 菜单查询参数 */
export interface MenuQueryParams extends PageQueryParams{
  menuName?: string
  menuCode?: string
  menuType?: MenuType
  menuStatus?: EntityStatus
  menuSide?: MenuSide
  parentId?: string
}

// ================================
// 角色管理相关类型
// ================================

/** 数据权限范围枚举 */
export type DataScope = 'all' | 'custom' | 'department' | 'departmentAndBelow' | 'onlyPersonal'

/** 角色接口 */
export interface Role extends BaseEntity {
  /** 角色名称 */
  roleName: string
  /** 角色编号 */
  roleCode: string
  roleSource: string
  /** 角色状态 */
  roleStatus: EntityStatus
  /** 数据权限范围 */
  roleScope: DataScope
  /** 角色描述 */
  description?: string
  /** 菜单权限ID列表 */
  menuIds?: string[]
  /** 数据权限部门ID列表 */
  deptIds?: string[]
}

/** 角色创建请求 */
export interface CreateRoleRequest {
  roleName: string
  roleCode: string
  roleStatus: EntityStatus
  roleScope: DataScope
  description?: string
  remark?: string,
  roleSource: 'factory' | 'store'
  storeId?: string
}

/** 角色更新请求 */
export interface UpdateRoleRequest extends CreateRoleRequest {
  id: string
}

/** 角色查询参数 */
export interface RoleQueryParams {
  roleName?: string
  roleCode?: string
  roleStatus?: EntityStatus
  roleScope?: DataScope
  roleSource?: 'factory' | 'store'
  storeId?: string
  current?: number
  size?: number
}

/** 角色权限配置请求 */
export interface RolePermissionRequest {
  roleId: string
  menuIds: string[]
}

/** 角色数据权限配置请求 */
export interface RoleDataPermissionRequest {
  roleId: string
  dataScope: DataScope
  deptIds?: string[]
}



// ================================
// 用户管理相关类型
// ================================

/** 用户类型枚举： 'factory' - 厂端用户, 'store' - 店端用户 */
export type UserType = 'factory' | 'store';

/** 用户状态枚举 */
export type UserStatus = 'normal' | 'disabled' | 'locked'

/** 用户接口 */
export interface User extends BaseEntity {
  /** 用户名 */
  username: string
  /** 姓名 */
  fullName: string
  /** 邮箱 */
  email: string
  /** 手机号 */
  phone: string
  /** 用户状态 */
  userStatus: UserStatus
  /** 用户类型：factory-厂端用户，store-店端用户 */
  userType: UserType
  /** 主要所属门店ID (用于展示，实际权限由storeRoles决定) */
  primaryStoreId?: string
  /** 主要所属门店名称 */
  primaryStoreName?: string
  /** 入职日期 */
  entryDate: string
  /** 最后登录时间 */
  lastLoginTime?: string
  /** 头像 */
  avatar?: string
  /** 用户所属的所有门店角色关系 */
  storeRoles?: UserStoreRole[]
}

/** 用户门店角色关系 */
export interface UserStoreRole {
  /** 关系ID (前端生成或后端返回) */
  id: string
  /** 用户ID */
  userId: string
  /** 门店ID */
  storeId: string
  /** 门店名称 */
  storeName?: string
  /** 部门ID */
  departmentId: string
  /** 部门名称 */
  departmentName?: string
  /** 职位 */
  position: string
  /** 角色ID (单个角色，兼容旧版本) */
  roleId?: string
  /** 角色名称 (单个角色，兼容旧版本) */
  roleName?: string
  /** 角色ID数组 (新版本支持多角色) */
  roleIds?: string[]
  /** 角色名称数组 (新版本支持多角色) */
  roleNames?: string[]
  /** 是否是主要门店/角色 */
  isPrimary: boolean
  /** 创建时间 */
  createTime?: string
}

/** 用户创建请求 */
export interface CreateUserRequest {
  username: string
  fullName: string
  email: string
  phone: string
  userStatus: UserStatus
  userType: UserType
  entryDate: string
  password?: string
  remark?: string
}

/** 用户更新请求 */
export interface UpdateUserRequest extends CreateUserRequest {
  id: string
}

/** 用户查询参数 */
export interface UserQueryParams {
  username?: string
  fullName?: string
  email?: string
  phone?: string
  userStatus?: UserStatus
  userType?: UserType
  departmentId?: string
  storeId?: string
  position?: string
  entryDateStart?: string
  entryDateEnd?: string
}

/** 用户密码重置请求 */
export interface ResetPasswordRequest {
  userId: string
  newPassword?: string
}

/** 用户权限分配请求 */
export interface UserPermissionAssignRequest {
  userId: string
  storeRoles: Array<{
    storeId: string
    departmentId: string
    position: string
    roleId: string
    isPrimary: boolean
  }>
}

/** 用户有效权限 */
export interface UserEffectivePermission {
  /** 权限名称 */
  name: string
  /** 权限标识 */
  permission?: string
  /** 子权限 */
  children?: UserEffectivePermission[]
}

// ================================
// API响应相关类型
// ================================

/** 基础API响应 */
export interface ApiResponse<T = any> {
  /** 响应码 */
  code: string | number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  result: T
  /** 时间戳 */
  timestamp: number
}

/** 分页响应 */
export interface PageResponse<T = any> {
  /** 数据列表 */
  records: T[]
  /** 当前页 */
  current: number
  /** 每页大小 */
  size: number
  /** 总数 */
  total: number
  /** 总页数 */
  pages: number
}

/** 分页请求参数 */
export interface PageRequest {
  /** 当前页 */
  current: number
  /** 每页大小 */
  size: number
}

// ================================
// 下拉选项相关类型
// ================================

/** 下拉选项接口 */
export interface SelectOption {
  /** 选项值 */
  value: string | number
  /** 选项标签 */
  label: string
  /** 是否禁用 */
  disabled?: boolean
  /** 子选项 */
  children?: SelectOption[]
}

/** 门店选项 */
export interface StoreOption extends SelectOption {
  id :  string | number
  storeName: string
  /** 门店类型 */
  storeType: StoreType
  /** 门店状态 */
  storeStatus: EntityStatus
}

/** 部门选项 */
export interface DepartmentOption extends SelectOption {
  /** 所属门店ID */
  storeId: string
  /** 部门类型 */
  departmentType: DepartmentType
}

/** 角色选项 */
export interface RoleOption extends SelectOption {
  /** 角色类型 */
  roleType: RoleType
  /** 数据权限范围 */
  roleScope: DataScope
}

// ================================
// 权限控制相关类型
// ================================

/** 权限按钮 */
export interface PermissionButton {
  /** 按钮标识 */
  key: string
  /** 按钮名称 */
  name: string
  /** 权限标识 */
  permission: string
  /** 按钮类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  /** 是否显示 */
  visible?: boolean
}

/** 操作日志 */
export interface OperationLog extends BaseEntity {
  /** 操作类型 */
  operationType: string
  /** 操作模块 */
  operationModule: string
  /** 操作内容 */
  operationContent: string
  /** 操作用户ID */
  operatorId: string
  /** 操作用户名 */
  operatorName: string
  /** 操作IP */
  operationIp: string
  /** 操作状态 */
  operationStatus: 'success' | 'failed'
  /** 错误信息 */
  errorMessage?: string
}

// ================================
// 导入导出相关类型
// ================================

/** 导入结果 */
export interface ImportResult {
  /** 总数 */
  total: number
  /** 成功数 */
  success: number
  /** 失败数 */
  failed: number
  /** 错误详情 */
  errors?: Array<{
    row: number
    message: string
  }>
}

/** 导出参数 */
export interface ExportParams {
  /** 导出字段 */
  fields?: string[]
  /** 查询条件 */
  queryParams?: Record<string, any>
  /** 导出格式 */
  format?: 'xlsx' | 'csv'
}

// ================================
// 表单验证相关类型
// ================================

/** 表单验证规则 */
export interface FormRule {
  /** 是否必填 */
  required?: boolean
  /** 验证消息 */
  message?: string
  /** 触发方式 */
  trigger?: 'blur' | 'change'
  /** 数据类型 */
  type?: 'string' | 'number' | 'email' | 'url' | 'date'
  /** 最小值/长度 */
  min?: number
  /** 最大值/长度 */
  max?: number
  /** 正则表达式 */
  pattern?: RegExp
  /** 自定义验证函数 */
  validator?: (rule: any, value: any, callback: any) => void
}

/** 表单验证规则集合 */
export type FormRules = Record<string, FormRule[]>

export default {}
