export interface ApprovalListParams {
  approvalNo?: string;
  approvalType?: string;
  approvalStatus?: string;
  submitTimeRange?: string[];
  submitter?: string;
  orderNo?: string;
  storeId?: string;
  customerInfo?: string;
  currentPage?: number;
  pageSize?: number;
}

export interface ApprovalListItem {
  id: string;
  approvalNo: string;
  approvalType: string;
  submitter: string;
  submitterNo: string;
  submitTime: string;
  orderNo: string;
  requestReason: string;
  overtimeStatus?: string;
  approvalResult?: string;
  approvalRemark?: string;
  approvalTime?: string;
  currentLevel?: string; // Add this if it's part of the API response
}

export interface PaginationResponse<T> {
  data: T[];
  total: number;
  currentPage: number;
  pageSize: number;
}

export interface StoreItem {
  id: string;
  name: string;
} 