// dms-frontend/src/types/module.d.ts

// ==================== API响应结构类型定义 ====================

// P03 API契约统一响应结构
export interface ApiResult<T = unknown> {
  success: boolean;
  message: string;
  code: string;
  traceId: string;
  result: T;
  timestamp: number;
}

// P03 API契约分页响应结构
export interface ApiPageResult<T> {
  list: T[];
  total: number;
}

// ==================== 基础类型定义 ====================

// 示例：车辆列表项的接口定义 (请根据你的后端 API 文档来定义)
export interface VehicleListItem {
  id: string; // 车辆ID
  vin: string; // 车辆识别码
  model: string; // 车型
  brand: string; // 品牌
  color: string; // 颜色
  price: number; // 价格
  status: 'in_stock' | 'sold' | 'reserved'; // 车辆状态，使用联合类型定义枚举值
  manufactureDate: string; // 制造日期，通常是 YYYY-MM-DD 格式
  engineNumber: string; // 发动机号
  // ... 你可以根据实际业务需求添加更多字段
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
}

// 示例：车辆列表查询参数的接口定义
export interface VehicleListParams {
  page?: number; // 当前页码，可选
  pageSize?: number; // 每页数量，可选
  vin?: string; // 车辆识别码，模糊查询
  model?: string; // 车型，模糊查询
  brand?: string; // 品牌，精确查询
  color?: string; // 颜色，精确查询
  status?: 'in_stock' | 'sold' | 'reserved'; // 状态过滤
  // ... 你可以根据实际业务需求添加更多查询条件
}

// 通用的分页响应结构接口
export interface PaginationResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 示例：用户登录请求参数
export interface LoginParams {
  username: string;
  password: string;
}

// 示例：用户登录响应数据
export interface LoginResponse {
  token: string;
  refreshToken: string;
  userId: string;
  username: string;
  roles: string[]; // 用户角色列表
  permissions: string[]; // 用户权限点列表
  // ... 更多用户相关信息
}

// 到店登记单列表项的接口定义
export interface CheckinListItem {
  checkinId: string; // 登记单号
  licensePlate: string; // 车牌号
  vin: string; // 车辆识别号
  vehicleModel: string; // 车型
  vehicleConfiguration: string; // 车型配置
  color: string; // 颜色
  mileage?: number; // 里程数(公里)
  repairPersonName: string; // 送修人名称
  repairPersonPhone: string; // 送修人手机号
  serviceAdvisor: string; // 服务顾问
  relatedRepairOrderId: string | null; // 关联环检单号 (可为空)
  serviceType: string; // 服务类型，例如"维修"
  createdAt: string; // 创建时间
  updatedAt: string; // 更新时间
  notes?: string; // 备注 (可选)
  isDeleted: boolean; // 逻辑删除标志
  // 以下是新增/编辑弹窗中可能需要展示，但列表页可能不直接显示的字段，或需要根据车牌号查询后带出的字段
  vehicleAge?: number; // 车龄 (计算字段，以月为单位)
  deliveryDate?: string; // 交车时间 (用于车龄计算)
}

// 到店登记单列表查询参数的接口定义
export interface CheckinListParams {
  page?: number; // 当前页码，可选
  pageSize?: number; // 每页数量，可选
  checkinId?: string; // 登记单号，精确查询
  licensePlate?: string; // 车牌号，精确查询
  repairPersonName?: string; // 送修人名称，模糊查询
  repairPersonPhone?: string; // 送修人手机号，模糊查询
  createdAtStart?: string; // 创建时间起始，YYYY-MM-DD
  createdAtEnd?: string; // 创建时间结束，YYYY-MM-DD
}

// ==================== 零件管理相关类型定义 ====================

declare module '@/types/module' {
  // For Parts Management HQ
  export interface PartsSearchParameters {
    approvalType?: string;
    storeName?: string;
    partName?: string;
    partNumber?: string;
    requisitionNumber?: string;
    purchaseOrderNumber?: string;
    supplierName?: string;
    requisitionDateRange?: [string, string];
    arrivalDateRange?: [string, string];
    requisitionStartDate?: string;
    requisitionEndDate?: string;
    arrivalStartDate?: string;
    arrivalEndDate?: string;
    requisitionStatus?: string;
    stockStatus?: string;
    page: number;
    pageSize: number;
  }

  export interface PartsSubItem {
    partName: string;
    partNumber: string;
    quantity: number;
    unit: string;
    callMaterialStatus: string;
    callMaterialDate: string;
    expectedArrivalTime: string;
    supplierName: string;
  }

  export interface PartsListItem {
    id: string | number;
    serialNumber: number;
    requisitionNumber: string;
    purchaseOrderNumber: string;
    requisitionDate: string;
    requisitionStatus: 'submitted' | 'approved' | 'rejected' | 'shipped' | 'partialShipped' | 'received' | 'voided';
  }

  export interface PartsDetailItem {
    id: string | number;
    serialNumber?: number;
    partName: string;
    partNumber: string;
    quantity: number;
    unit: string;
    requisitionStatus: 'submitted' | 'approved' | 'rejected' | 'shipped' | 'partialShipped' | 'received' | 'voided';
    requisitionDate: string;
    expectedArrivalTime: string;
    supplierName: string;
  }

  export interface PartsDetail {
    id: string | number;
    requisitionNumber: string;
    purchaseOrderNumber: string;
    requisitionDate: string;
    items: PartsDetailItem[];
  }

  export interface ApprovalForm {
    callMaterialOrderNumber: string;
    callMaterialDate: string;
    approvalResult: string;
    rejectionReason: string;
    parts: PartsSubItem[];
  }

  export interface ApprovePartsPayload {
    id: string | number;
    approvalResult: 'approved' | 'rejected';
    rejectionReason?: string;
  }
}

// ==================== 派工管理相关类型定义 ====================

// 工单状态枚举
export type WorkOrderStatus =
  | 'draft'           // 草稿
  | 'pending_confirm' // 待确认
  | 'confirmed'       // 已确认
  | 'pending_assign'  // 待分配
  | 'pending_start'   // 待开工
  | 'in_progress'     // 进行中
  | 'pending_qc'      // 待质检
  | 'pending_settle'  // 待结算
  | 'completed'       // 已完成
  | 'cancelled';      // 已取消

// 工单类型枚举
export type WorkOrderType = 'maintenance' | 'repair' | 'claim';

// 工单优先级枚举
export type WorkOrderPriority = 'normal' | 'urgent';

// 分配状态枚举
export type AssignmentStatus = 'pending_assign' | 'assigned';

// 技师状态枚举
export type TechnicianStatus = 'available' | 'busy' | 'on_leave' | 'training' | 'resigned';

// 技师工作负荷状态枚举
export type WorkloadStatus = 'idle' | 'moderate' | 'busy' | 'overloaded';

// 工单列表项接口
export interface WorkOrderListItem {
  workOrderId: string; // 工单编号
  workOrderType: WorkOrderType; // 工单类型
  priority: WorkOrderPriority; // 优先级
  status: WorkOrderStatus; // 工单状态
  customerName: string; // 送修人姓名
  customerPhone: string; // 送修人手机号
  licensePlate: string; // 车牌号
  vehicleModel: string; // 车型
  vinNumber: string; // VIN号
  serviceAdvisorId: string; // 服务顾问ID
  serviceAdvisorName: string; // 服务顾问姓名
  estimatedWorkHours: number; // 预计工时
  totalAmount: number; // 工单总金额
  assignedTechnicianId?: string; // 分配技师ID
  assignedTechnicianName?: string; // 分配技师姓名
  assignmentTime?: string; // 分配时间
  estimatedStartTime?: string; // 预计开工时间
  estimatedFinishTime?: string; // 预计完工时间
  actualStartTime?: string; // 实际开工时间
  actualFinishTime?: string; // 实际完工时间
  assignmentNotes?: string; // 分配备注
  waitingDuration?: number; // 等待时长(分钟)
  hasWarrantyClaim: boolean; // 是否涉及索赔
  hasOutsourcing: boolean; // 是否涉及委外
  hasAdditionalItems: boolean; // 是否有增项
  isUrgent: boolean; // 是否紧急
  creationTime: string; // 创建时间
  updateTime: string; // 更新时间
  mainServiceItems: string[]; // 主要服务项目
  customerRequirements?: string; // 客户特殊要求
}

// 工单筛选参数接口
export interface WorkOrderListParams {
  page?: number; // 当前页码
  pageSize?: number; // 每页数量
  workOrderId?: string; // 工单编号
  priority?: WorkOrderPriority; // 优先级
  workOrderType?: WorkOrderType; // 工单类型
  customerName?: string; // 送修人姓名
  licensePlate?: string; // 车牌号
  serviceAdvisorId?: string; // 服务顾问
  assignmentStatus?: AssignmentStatus; // 分配状态
  assignedTechnicianId?: string; // 分配技师
  creationTimeStart?: string; // 创建时间起始
  creationTimeEnd?: string; // 创建时间结束
  estimatedWorkHoursMin?: number; // 预计工时最小值
  estimatedWorkHoursMax?: number; // 预计工时最大值
  waitingDurationMax?: number; // 等待时长上限
  hasWarrantyClaim?: boolean; // 是否涉及索赔
  status?: WorkOrderStatus; // 工单状态
}

// 技师信息接口
export interface TechnicianInfo {
  technicianId: string; // 技师ID
  employeeNumber: string; // 工号
  technicianName: string; // 技师姓名
  department: string; // 所属部门
  position: string; // 职位等级
  specialization: string; // 专业方向
  skillLevel: string; // 技能等级
  currentStatus: TechnicianStatus; // 当前状态
  workLoadStatus: WorkloadStatus; // 负荷状态
  currentWorkOrders: number; // 当前工单数量
  totalEstimatedHours: number; // 预计工时总量
  availableHours: number; // 可用工时
  workStartTime: string; // 工作开始时间
  workEndTime: string; // 工作结束时间
  breakStartTime: string; // 休息开始时间
  breakEndTime: string; // 休息结束时间
  totalCompletedOrders: number; // 历史完成工单数
  averageEfficiency: number; // 平均效率
  qualityScore: number; // 质量评分
  customerSatisfaction: number; // 客户满意度
  certifications?: string[]; // 认证资质
  phoneNumber?: string; // 联系电话
  hireDate?: string; // 入职时间
}

// 派工记录接口
export interface WorkAssignmentRecord {
  assignmentId: string; // 派工记录ID
  workOrderId: string; // 工单ID
  technicianId: string; // 技师ID
  assignmentType: 'initial' | 'reassign'; // 分配类型
  assignmentStatus: string; // 分配状态
  assignedBy: string; // 分配人ID
  assignedByName: string; // 分配人姓名
  assignmentTime: string; // 分配时间
  assignmentReason?: string; // 分配原因
  assignmentNotes?: string; // 分配备注
  estimatedStartTime: string; // 预计开工时间
  estimatedFinishTime: string; // 预计完工时间
  estimatedWorkHours: number; // 预计工时
  actualStartTime?: string; // 实际开工时间
  actualFinishTime?: string; // 实际完工时间
  actualWorkHours?: number; // 实际工时
  workEfficiency?: number; // 工作效率
  previousAssignmentId?: string; // 前一次分配记录ID
  reassignmentReason?: string; // 重新分配原因
  reassignmentTime?: string; // 重新分配时间
}

// 派工分配请求参数接口
export interface AssignWorkOrderRequest {
  workOrderId: string; // 工单ID
  technicianId: string; // 技师ID
  estimatedStartTime: string; // 预计开工时间
  estimatedFinishTime: string; // 预计完工时间
  assignmentNotes?: string; // 分配备注
  assignmentReason?: string; // 分配原因
}

// 重新分配请求参数接口
export interface ReassignWorkOrderRequest {
  workOrderId: string; // 工单ID
  newTechnicianId: string; // 新技师ID
  reassignmentReason: string; // 重新分配原因
  estimatedStartTime: string; // 预计开工时间
  estimatedFinishTime: string; // 预计完工时间
  assignmentNotes?: string; // 分配备注
  impactAssessment?: string; // 影响评估
}

// 派工统计数据接口
export interface AssignmentStatistics {
  statsDate: string; // 统计日期
  totalOrders: number; // 今日总工单数
  pendingAssignment: number; // 待派工数量
  assignedOrders: number; // 已分配数量
  inProgressOrders: number; // 进行中数量
  completedOrders: number; // 已完成数量
  cancelledOrders: number; // 已取消数量
  totalTechnicians: number; // 总技师数
  availableTechnicians: number; // 可用技师数
  busyTechnicians: number; // 繁忙技师数
  overloadedTechnicians: number; // 超负荷技师数
  averageAssignmentTime: number; // 平均分配时间
  averageWaitingTime: number; // 平均等待时间
  onTimeCompletionRate: number; // 准时完工率
  reassignmentRate: number; // 重新分配率
  totalEstimatedHours: number; // 总预计工时
  totalActualHours: number; // 总实际工时
  averageEfficiency: number; // 平均效率
  utilizationRate: number; // 人员利用率
}

// 技师时间轴工单块接口
export interface TechnicianTimelineItem {
  workOrderId: string; // 工单编号
  customerName: string; // 客户姓名
  licensePlate: string; // 车牌号
  workOrderType: WorkOrderType; // 工单类型
  priority: WorkOrderPriority; // 优先级
  estimatedStartTime: string; // 预计开工时间
  estimatedFinishTime: string; // 预计完工时间
  estimatedWorkHours: number; // 预计工时
  status: WorkOrderStatus; // 工单状态
  isConflict?: boolean; // 是否存在时间冲突
}

// 技师工作安排接口
export interface TechnicianSchedule {
  technicianInfo: TechnicianInfo; // 技师基本信息
  workItems: TechnicianTimelineItem[]; // 工作项目列表
  availableSlots: Array<{ startTime: string; endTime: string }>; // 空闲时间段
  workloadPercentage: number; // 工作负荷百分比
  totalScheduledHours: number; // 已安排工时
  remainingHours: number; // 剩余可用工时
}

// ==================== 交车管理相关类型定义 ====================

// 交车状态枚举
export type DeliveryStatus = 'pending_delivery' | 'pending_confirm' | 'delivered';

// 客户确认类型枚举
export type ConfirmationType = 'app' | 'offline';

// 订单状态枚举
export type OrderStatus = 'pending_allocation' | 'allocating' | 'allocated' | 'pending_delivery' | 'delivered' | 'normal' | 'cancelled';

// 用户角色枚举
export type UserRole = 'sales_consultant' | 'sales_manager' | 'regional_manager';

// 交车单列表项接口
export interface DeliveryOrderItem {
  deliveryId: string; // 交车单主键ID
  deliveryNumber: string; // 交车单号 (格式：DEL+YYYYMMDD+序号)
  orderId: string; // 关联订单ID
  orderNumber: string; // 订单编号
  customerName: string; // 购车人名称
  customerPhone: string; // 购车人手机号
  customerType?: string; // 客户类型
  idType?: string; // 证件类型
  idNumber?: string; // 证件号
  customerAddress?: string; // 地址
  customerCity?: string; // 城市
  customerPostcode?: string; // 邮编
  customerState?: string; // 州
  orderCreatorName?: string; // 下单人名称
  orderCreatorPhone?: string; // 下单人手机号
  warehouseName?: string; // 仓库名称
  productionDate?: string; // 生产日期
  entryTime?: string; // 入库时间
  vin: string; // 车辆识别代号
  model: string; // 车型系列
  variant: string; // 车型配置版本
  color: string; // 车辆颜色
  vehiclePrice?: number; // 车辆价格
  dealerStore: string; // 门店名称
  salesConsultant: string; // 销售顾问姓名
  orderStatus: OrderStatus; // 订单状态
  orderAmount?: number; // 订单金额
  paymentMethod?: string; // 支付方式
  orderPaymentStatus?: string; // 订单支付状态
  orderTime?: string; // 下单时间
  paymentTime?: string; // 支付时间
  invoiceTime?: string; // 开票时间
  plannedDeliveryDate?: string; // 计划交车时间
  deliveryStatus: DeliveryStatus; // 交车状态
  customerConfirmed: boolean; // 客户是否确认
  confirmationType?: ConfirmationType; // 客户确认类型
  customerConfirmTime?: string; // 客户确认时间
  deliveryTime?: string; // 实际交车时间
  deliveryNotes?: string; // 交车备注信息
  signaturePhoto?: string; // 客户签字照片路径
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  creator: string; // 创建人
  updater: string; // 更新人
  // 操作权限相关
  canEdit?: boolean; // 是否可编辑
  canConfirm?: boolean; // 是否可确认
}

// 交车单筛选参数接口
export interface DeliveryOrderParams {
  page?: number; // 当前页码
  pageSize?: number; // 每页数量
  deliveryNumber?: string; // 交车单号
  orderNumber?: string; // 订单编号
  customerName?: string; // 购车人名称
  customerPhone?: string; // 购车人手机号
  vin?: string; // VIN
  model?: string; // 车型系列
  variant?: string; // 车型配置版本
  color?: string; // 车辆颜色
  orderStatus?: OrderStatus; // 订单状态
  dealerStore?: string; // 门店
  salesConsultant?: string; // 销售顾问
  deliveryStatus?: DeliveryStatus; // 交车状态
  customerConfirmed?: boolean; // 客户确认状态
  confirmationType?: ConfirmationType; // 客户确认类型
  deliveryTimeRange?: [string, string]; // 交车时间范围
  customerConfirmTimeRange?: [string, string]; // 客户确认时间范围
}

// 提交确认请求参数
export interface SubmitConfirmRequest {
  deliveryNumber: string; // 交车单号
}

// 交车确认请求参数
export interface DeliveryConfirmRequest {
  deliveryNumber: string; // 交车单号
  deliveryTime: string; // 交车时间
  deliveryNotes?: string; // 交车备注
  signaturePhoto?: string; // 客户签字照片路径（这里实际是上传后的路径）
}

// 客户签字照片信息
export interface CustomerSignature {
  signatureId: string; // 签字照片主键ID
  deliveryNumber: string; // 关联交车单号
  originalFilename: string; // 原始文件名称
  storedFilename: string; // 存储文件名称
  filePath: string; // 服务器存储路径
  fileSize: number; // 文件大小（字节）
  fileFormat: string; // 文件格式
  fileHash: string; // 文件MD5哈希值
  uploadTime: string; // 上传时间
  uploader: string; // 上传人（销售顾问）
}

// 交车操作日志
export interface DeliveryOperationLog {
  logId: string; // 日志主键ID
  deliveryNumber: string; // 关联交车单号
  operationType: string; // 操作类型
  beforeStatus?: string; // 操作前状态
  afterStatus?: string; // 操作后状态
  operator: string; // 操作人
  operationSource: 'dms_system' | 'app' | 'system_auto'; // 操作来源
  operationTime: string; // 操作时间
  operationDescription?: string; // 操作详细说明
  ipAddress?: string; // 操作来源IP地址
  confirmationDetails?: Record<string, unknown>; // APP确认详情
}

// 导出设置参数
export interface ExportSettings {
  format: 'excel' | 'pdf' | 'csv'; // 导出格式
  range: 'current_page' | 'all_data' | 'filtered_result'; // 导出范围
  timeRange?: {
    start: string;
    end: string;
  }; // 时间范围
}

// ==================== 订单审核管理相关类型定义 ====================

// 审核类型枚举
export type ApprovalType = 'cancel_order' | 'modify_info';

// 审核状态枚举
export type ApprovalStatus =
  | 'pending_initial'    // 待初审
  | 'pending_final'      // 待终审
  | 'about_timeout'      // 即将超时
  | 'approved'           // 审批通过
  | 'rejected'           // 审批驳回
  | 'timeout_rejected';  // 超时驳回

// 审核结果枚举
export type ApprovalResult = 'approved' | 'rejected';

// 订单审核列表项接口
export interface ApprovalListItem {
  id: string; // 审核记录ID
  approvalNumber: string; // 审核单号
  approvalType: ApprovalType; // 审核类型
  orderNumber: string; // 订单编号
  submittedBy: string; // 提交人
  submissionTime: string; // 提交时间
  remainingTime: string; // 剩余时间
  approvalStatus: ApprovalStatus; // 审核状态
  approvalResult?: ApprovalResult; // 审核结果
  approvalTime?: string; // 审核时间
  approvalLevel: number; // 审核级别（1-一级审核，2-二级审核）
  approvalComments?: string; // 审核意见
  rejectionReason?: string; // 驳回原因
  approvedBy?: string; // 审核人
  storeId: string; // 门店ID
  storeName: string; // 门店名称
  applicationReason: string; // 申请原因
  timeoutAt: string; // 超时时间
  isSecondReview: boolean; // 是否为二次审核
  parentRecordId?: string; // 父审核记录ID
  priorityLevel: number; // 优先级（1-高优先级，2-普通优先级）
  autoTimeoutJobId?: string; // 自动超时任务ID
  modificationReason?: string;
  cancelReason?: string;
  modifiedFields?: Array<{
    field: string;
    oldValue: any;
    newValue: any;
  }>;
  // Relationships
  order?: OrderSummary;
  submitter?: UserSummary;
  // 业务数据
  businessData?: {
    changeType?: string; // 变更类型
    originalValue?: string; // 原始值
    newValue?: string; // 新值
    reason?: string; // 变更原因
  };
  originalCustomerInfo?: CustomerInfoChange;
  modifiedCustomerInfo?: CustomerInfoChange;
  originalVehicleInfo?: VehicleInfoChange;
  modifiedVehicleInfo?: VehicleInfoChange;
}

// 订单审核列表查询参数接口
export interface ApprovalListParams {
  page?: number; // 当前页码
  pageSize?: number; // 每页数量
  status?: 'pending' | 'approved'; // Tab状态
  approvalType?: string; // 审核类型
  orderNumber?: string; // 订单编号
  submittedBy?: string; // 提交人
  submissionTimeStart?: string; // 提交时间起始
  submissionTimeEnd?: string; // 提交时间结束
  approvalStatus?: string; // 审核状态（待审批Tab）
  approvalResult?: string; // 审核结果（已审批Tab）
  storeId?: string; // 门店ID
}

// 审核操作请求接口
export interface ApprovalActionRequest {
  approvalId: string; // 审核记录ID
  result: 'approved' | 'rejected'; // 审核结果
  comments?: string; // 审核意见
  reason?: string; // 驳回原因（驳回时必填）
  approverId: string; // 审核人ID
}

// 批量审核请求接口
export interface BatchApprovalRequest {
  approvalIds: string[]; // 审核记录ID列表
  result: 'approved' | 'rejected'; // 审核结果
  comments?: string; // 统一审核意见
  reason?: string; // 统一驳回原因（驳回时必填）
  approverId: string; // 审核人ID
}

// 审核历史记录接口
export interface ApprovalHistoryItem {
  id: string; // 历史记录ID
  action: 'submit' | 'initial_review' | 'final_review' | 'timeout'; // 操作类型
  actionTime: string; // 操作时间
  operator: string; // 操作人
  operatorRole: string; // 操作人角色
  comments?: string; // 操作意见
  status: string; // 操作后状态
}

// 客户信息变更详情接口
export interface CustomerInfoChange {
  customerName?: string;
  customerPhone?: string;
  idType?: string;
  idNumber?: string;
  address?: string;
}

// 车辆信息变更详情接口
export interface VehicleInfoChange {
  model?: string;
  color?: string;
  vin?: string;
}

export interface OrderInfoChange {
  changeId: string; // 变更记录ID
  approvalRecordId: string; // 关联审核记录ID
  changedField: string; // 变更字段
  originalValue: string; // 原始值
  newValue: string; // 新值
  changeType: string; // 变更类型
  operatorId: string; // 操作人ID
  changedAt: string; // 变更时间
}

// 整车收款管理相关类型定义
export interface OrderPaymentItem {
  orderId: string
  orderNumber: string
  buyerName: string
  buyerPhone: string
  dealerStoreName: string
  salesConsultantName: string
  vin: string
  model: string
  variant: string
  color: string
  orderCreateTime: string
  orderStatus: string
  paymentStatus: string
  vehicleSalesPrice: number
  insuranceAmount: number
  otrAmount: number
  discountAmount: number
  totalAmount: number
  paidAmount: number
  unpaidAmount: number
  loanAmount?: number
  canInvoice: boolean
  invoiceTime?: string
  invoiceNumber?: string
  createTime: string
  updateTime: string
}

export interface OrderPaymentListParams {
  orderNumber?: string
  buyerName?: string
  buyerPhone?: string
  orderStatus?: string
  paymentStatus?: string
  startDate?: string
  endDate?: string
  canInvoice?: boolean
  page: number
  pageSize: number
}

export interface OrderPaymentRecord {
  paymentRecordId: string
  paymentRecordNumber: string
  orderId: string
  businessType: '收款' | '退款'
  transactionNumber: string
  channel: 'APP' | '银行卡' | '转账'
  amount: number
  paymentType: '尾款' | '定金' | '全款'
  arrivalTime: string
  remark?: string
  dataSource: '手动录入' | 'APP推送'
  isDeletable: boolean
  createTime: string
  creator: string
}

export interface OrderDetailInfo {
  // 订单基础信息
  orderId: string
  orderNumber: string
  orderCreateTime: string
  orderStatus: string
  paymentStatus: string
  paymentMethod: '全款' | '贷款'
  loanAmount?: number
  canInvoice: boolean
  invoiceTime?: string
  invoiceNumber?: string
  salesSubtotal?: number; // 新增
  consumptionTax?: number; // 新增
  salesTax?: number; // 新增
  numberPlatesFee?: number; // 新增
  accessoriesTotalAmount?: number; // 新增
  loanApprovalStatus?: string; // 新增
  loanBank?: string; // 新增

  // 客户信息
  ordererName: string
  ordererPhone: string
  buyerName: string
  buyerPhone: string
  buyerIdType: string
  buyerIdNumber: string
  buyerEmail: string
  buyerAddress: string
  buyerState: string
  buyerCity: string
  buyerPostcode: string

  // 门店信息
  dealerRegion: string
  dealerCity: string
  dealerStoreName: string
  salesConsultantName: string
  salesConsultantPhone?: string
  salesConsultantEmail?: string

  // 车辆信息
  vin: string
  model: string
  variant: string
  color: string
  options: Array<{ name: string; price: number; }>; // 修改：从string[]改为对象数组
  warehouseName: string
  productionDate: string

  // 价格信息
  vehicleSalesPrice: number
  insuranceAmount: number
  otrAmount: number
  discountAmount: number
  totalAmount: number
  paidAmount: number
  unpaidAmount: number

  // 收退款历史
  paymentRecords: OrderPaymentRecord[]
}

export interface AddPaymentRecordForm {
  businessType: '收款' | '退款'
  transactionNumber: string
  channel: 'APP' | '银行卡' | '转账'
  amount: number
  paymentType: '尾款' | '定金' | '全款'
  arrivalTime: string
  remark?: string
}

// ==================== 厂端订单统计管理相关类型定义 ====================

// 厂端订单统计指标
export interface OrderStatistics {
  monthlyOrderCount: number; // 本月订单数量
  dailyOrderCount: number; // 今日订单数量
  monthlyGrowthRate: number; // 月环比增长率
  dailyGrowthRate: number; // 日环比增长率
  topDealers: Array<{
    dealerName: string; // 门店名称
    orderCount: number; // 订单数量
  }>; // 订单最多门店
  topVehicles: Array<{
    model: string; // 车型
    variant: string; // 配置
    salesCount: number; // 销量
  }>; // 最热销车型
  pendingDeliveryCount: number; // 待交车订单数量
  lastUpdateTime: string; // 数据最后更新时间
}

// 厂端订单列表项
export interface FactoryOrderListItem {
  id: string; // 订单ID
  orderNo?: string; // 订单编号（新）
  orderNumber?: string; // 订单编号（旧）
  storeName?: string; // 门店名称（新）
  dealerName?: string; // 门店名称（旧）
  createTime?: string; // 订单创建时间（新）
  creationTime?: string; // 订单创建时间（旧）
  customerName?: string; // 客户姓名（新）
  customerPhone?: string; // 客户手机号（新）
  customerType?: string; // 客户类型（新）
  ordererName?: string; // 下单人（脱敏，旧）
  ordererPhone?: string; // 下单人手机号（脱敏，旧）
  buyerName?: string; // 购车人（脱敏，旧）
  buyerPhone?: string; // 购车人手机号（脱敏，旧）
  buyerCategory?: string; // 购车人类别（旧）
  model: string; // 车型Model
  variant: string; // 车型配置Variant
  color: string; // 车辆颜色
  vin: string; // VIN号（脱敏）
  paymentMethod: string; // 付款方式
  loanStatus?: string; // 贷款状态（新）
  loanApprovalStatus?: string; // 贷款审核状态（旧）
  orderStatus: string; // 订单状态
  approvalStatus?: string; // 审批状态（新）
  orderApprovalStatus?: string; // 订单审核状态（旧）
  paymentStatus: string; // 支付状态
  insuranceStatus: string; // 投保状态
  jpjRegistrationStatus: string; // JPJ车辆注册状态
}

// 厂端订单查询参数
export interface FactoryOrderListParams {
  pageNum?: number; // 当前页码
  pageSize?: number; // 每页数量
  dealerName?: string; // 门店筛选
  model?: string; // 车型筛选
  variant?: string; // 配置筛选
  orderStatus?: string; // 订单状态
  paymentStatus?: string; // 支付状态
  orderDateStart?: string; // 下单日期起始
  orderDateEnd?: string; // 下单日期结束
  orderNumber?: string; // 订单号搜索
}

// 厂端订单详情
export interface FactoryOrderDetail {
  // 订单基础信息
  orderNo: string; // 订单编号
  customerId: number; // 客户ID
  salesmanId: number; // 销售员ID
  storeId: number; // 门店ID
  createTime: string; // 创建时间

  // 销售员信息
  salesman?: string; // 销售员姓名
  salesmanPhone?: string; // 销售员电话

  // 客户信息
  customerName: string; // 客户姓名
  customerPhone: string; // 客户手机号
  idType: string; // 证件类型
  idNumber: string; // 证件号码
  email: string; // 邮箱
  address: string; // 地址
  state: string; // 州
  city: string; // 城市
  zipCode: string; // 邮编
  customerType: string; // 客户类型

  // 门店信息
  region?: string; // 区域
  dealerCity?: string; // 经销商城市
  dealerName?: string; // 经销商名称
  salesAdvisorName?: string; // 销售顾问姓名

  // 车辆信息
  model: string; // 车型
  variant: string; // 配置
  color: string; // 颜色
  vehiclePrice: number; // 车辆价格
  numberPlatesFee: number; // 牌照费
  vin: string; // VIN码

  // 配件信息
  accessories?: any[]; // 配件列表
  totalAccessoryAmount?: number; // 配件总金额

  // 开票信息
  invoiceType: string; // 开票类型
  invoiceName: string; // 开票名称
  invoicePhone: string; // 开票电话
  invoiceAddress: string; // 开票地址

  // 权益信息
  rights: any[]; // 权益列表
  totalRightsDiscountAmount: number; // 权益折扣总金额

  // 付款信息
  paymentMethod: string; // 付款方式
  loanStatus: string; // 贷款状态
  depositAmount: number; // 定金金额
  loanAmount: number; // 贷款金额
  loanTerm: number; // 贷款期限
  finalPaymentAmount: number; // 尾款金额

  // 保险信息
  policies: Array<{
    policyNumber: string; // 保单号
    insuranceType: string; // 保险类型
    insuranceCompany: string; // 保险公司
    price: number; // 价格
    effectiveDate: string; // 生效日期
    expiryDate: string; // 到期日期
  }>;
  totalInsuranceAmount: number; // 保险总金额
  remarks: string; // 备注

  // OTR费用信息
  otrFees?: any[]; // OTR费用列表
  totalOtrAmount?: number; // OTR费用总金额

  // 订单变更记录
  changeRecords: Array<{
    orderId: number; // 订单ID
    originalContent: string; // 原始内容
    changedContent: string; // 变更后内容
    createdAt: string; // 创建时间
    createdBy: string; // 创建人
  }>;

  // 价格信息
  totalInvoicePrice: number; // 开票总价格
  remainingAmount?: number; // 剩余金额
  remainingReceivableAmount: number; // 剩余应收金额

  // 状态信息
  orderStatus: string; // 订单状态
  paymentStatus: string; // 支付状态
  approvalStatus: string; // 审批状态
  insuranceStatus: string; // 保险状态
  jpjRegistrationStatus: string; // JPJ注册状态
  allocationStatus: string; // 分配状态
  allocationTime?: string; // 分配时间
  allocatedVehicleId?: number; // 分配车辆ID
}

// 厂端订单导出请求
export interface FactoryOrderExportRequest {
  orderNumbers?: string[]; // 订单编号列表（批量导出时）
  searchParams?: FactoryOrderListParams; // 筛选条件（当前筛选结果导出时）
  exportType: 'selected' | 'current_page' | 'all_filtered'; // 导出类型
}

// ==================== 销售订单管理相关类型定义 ====================

// 销售订单状态枚举
export type SalesOrderStatus =
  | 'submitted'
  | 'confirmed'
  | 'cancel_pending'
  | 'cancel_approved'
  | 'canceled'
  | 'pending_delivery'
  | 'delivered';

// 订单支付状态枚举
export type SalesOrderPaymentStatus =
  | 'pending_deposit'
  | 'deposit_paid'
  | 'pending_final_payment'
  | 'balance_paid'
  | 'refunding'
  | 'refund_completed';

// 订单审核状态枚举
export type SalesOrderApprovalStatus = 'pending_approval' | 'approved' | 'rejected';

// 贷款审核状态枚举
export type LoanApprovalStatus = 'pending' | 'approved' | 'rejected' | 'pending_review';

// 投保状态枚举
export type InsuranceStatus = 'not_insured' | 'pending' | 'completed' | 'insured';

// JPJ注册状态枚举
export type JPJRegistrationStatus =
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'registered'
  | 'pending_registration';

// 购车人类别枚举
export type BuyerType = 'individual' | 'company';

// 支付方式枚举
export type PaymentMethod = 'installment' | 'full_payment';

// 销售订单列表项接口
export interface SalesOrderListItem {
  id: string;
  orderNo: string;
  createTime: string;
  ordererName: string;
  ordererPhone: string;
  buyerName: string;
  buyerPhone: string;
  customerType: BuyerType;
  model: string;
  variant: string;
  color: string;
  vin: string;
  paymentMethod: PaymentMethod;
  totalAmount: number;
  loanApprovalStatus?: LoanApprovalStatus;
  orderStatus: SalesOrderStatus;
  approvalStatus: SalesOrderApprovalStatus;
  paymentStatus: SalesOrderPaymentStatus;
  insuranceStatus: InsuranceStatus;
  jpjRegistrationStatus: JPJRegistrationStatus;
}

// 销售订单查询参数接口
export interface SalesOrderListParams {
  page?: number;
  pageSize?: number;
  buyerName?: string;
  buyerPhone?: string;
  customerPhone?: string;
  buyerType?: BuyerType;
  model?: string;
  orderNumber?: string;
  orderStatus?: SalesOrderStatus;
  approvalStatus?: SalesOrderApprovalStatus;
  paymentStatus?: SalesOrderPaymentStatus;
  insuranceStatus?: InsuranceStatus;
  loanApprovalStatus?: LoanApprovalStatus;
  jpjRegistrationStatus?: JPJRegistrationStatus;
  createTimeStart?: string;
  createTimeEnd?: string;
}

// 选配件信息接口
export interface SalesOrderAccessory {
  id: string;
  category: string;
  accessoryName: string;
  unitPrice: number;
  quantity: number;
  totalPrice: number;
}

// 权益信息接口
export interface SalesOrderRight {
  id: string;
  rightCode: string;
  rightName: string;
  rightMode: string;
  discountAmount: number;
  effectiveDate: string;
  expiryDate: string;
}

// 保险信息接口
export interface SalesOrderInsurance {
  id: string;
  policyNumber: string;
  insuranceType: string;
  insuranceCompany: string;
  effectiveDate: string;
  expiryDate: string;
  insurancePrice: number;
}

// OTR费用信息接口
export interface SalesOrderOTRFee {
  id: string;
  ticketNumber: string;
  feeItem: string;
  feePrice: number;
  effectiveDate: string;
  expiryDate: string;
}

// 订单变更记录接口
export interface SalesOrderChangeRecord {
  id: string;
  originalContent: string;
  changedContent: string;
  operator: string;
  operationTime: string;
}

// 销售订单详情接口
export interface SalesOrderDetail {
  id: string;
  orderNo: string;
  createTime: string;
  ordererName: string;
  ordererPhone: string;
  buyerName: string;
  buyerPhone: string;
  buyerIdType: string;
  buyerIdNumber: string;
  buyerEmail: string;
  buyerAddress: string;
  buyerState: string;
  buyerCity: string;
  buyerPostcode: string;
  customerType: BuyerType;
  storeRegion: string;
  storeCity: string;
  storeName: string;
  salesConsultantName: string;
  model: string;
  variant: string;
  color: string;
  salesSubtotal: number;
  numberPlatesFee: number;
  vin: string;
  accessories: SalesOrderAccessory[];
  accessoriesTotalAmount: number;
  invoicingType: string;
  invoicingName: string;
  invoicingPhone: string;
  invoicingAddress: string;
  rights: SalesOrderRight[];
  rightsDiscountAmount: number;
  paymentMethod: PaymentMethod;
  loanApprovalStatus?: LoanApprovalStatus;
  depositAmount: number;
  loanAmount?: number;
  balanceAmount: number;
  insuranceList: SalesOrderInsurance[];
  insuranceTotalAmount: number;
  insuranceNotes?: string;
  otrFees: SalesOrderOTRFee[];
  otrFeesTotalAmount: number;
  changeRecords: SalesOrderChangeRecord[];
  orderStatus: SalesOrderStatus;
  approvalStatus: SalesOrderApprovalStatus;
  paymentStatus: SalesOrderPaymentStatus;
  insuranceStatus: InsuranceStatus;
  jpjRegistrationStatus: JPJRegistrationStatus;
  totalInvoiceAmount: number;
  remainingAmount: number;
}

// 可选权益接口
export interface AvailableRight {
  id: string;
  rightCode: string;
  rightName: string;
  rightMode: string;
  discountAmount: number;
  effectiveDate: string;
  expiryDate: string;
}

// 权益搜索参数接口
export interface RightSearchParams {
  rightCode?: string;
  rightName?: string;
  page?: number;
  pageSize?: number;
}

// 保存订单请求接口
export interface SaveOrderRequest {
  orderNo: string;
  color?: string;
  selectedRights?: string[];
  paymentMethod?: PaymentMethod;
  loanAmount?: number;
  loanTerm?: number; // 贷款期数（月）
  loanStatus?: LoanApprovalStatus; // 贷款审核状态
  // 保险备注
  loanTerm?: number;
  loanApprovalStatus?: LoanApprovalStatus;
  insuranceNotes?: string;
}

// 推送保险请求接口
export interface PushInsuranceRequest {
  orderNo: string;
}

// 提交交车请求接口
export interface SubmitDeliveryRequest {
  orderNo: string;
}

// 售后模块相关的类型定义

// 服务顾问接口
export interface ServiceAdvisor {
  id: string;
  name: string;
  avatar?: string;
}

// 技师接口
export interface Technician {
  id: string;
  name: string;
  avatar?: string;
}

// 质量检测参数接口
export interface QualityInspectionParams {
  appointmentId: string;
  serviceContactName: string;
  serviceContactPhone: string;
}

// ==================== 质检管理相关类型定义 ====================

// 质检状态枚举
export type QualityCheckStatus =
  | 'pending_check'      // 待质检
  | 'checking'           // 质检中
  | 'pending_review'     // 质检待审批
  | 'passed'             // 质检通过
  | 'rework';            // 返工

// 审核结果枚举
export type QualityCheckAuditResult = 'passed' | 'rework';

// 质检项目类型枚举
export type QualityCheckItemType = 'BOOLEAN' | 'NUMERIC' | 'TEXT';

// 质检项目检查结果枚举
export type QualityCheckResult = 'PASS' | 'FAIL';

// 质检单基础信息
export interface QualityCheckOrder {
  id: string; // 主键ID
  qualityCheckNo: string; // 质检单编号，格式：QC+YYYYMMDD+序号
  workOrderNo: string; // 关联工单编号
  workOrderType: WorkOrderType; // 工单类型：保养、维修、保险
  status: QualityCheckStatus; // 质检状态
  technicianId: string; // 负责技师ID
  technicianName: string; // 技师姓名
  storeId: string; // 门店ID
  storeName: string; // 门店名称
  isClaimRelated: boolean; // 是否涉及索赔
  isOutsourceRelated: boolean; // 是否涉及委外
  startTime?: string; // 开工时间
  finishTime?: string; // 完工时间
  estimatedHours?: number; // 预计工时
  actualHours?: number; // 实际工时
  submitTime?: string; // 提交时间
  auditTime?: string; // 审核时间
  auditResult?: QualityCheckAuditResult; // 审核结果
  auditorId?: string; // 审核人ID
  auditorName?: string; // 审核人姓名
  reworkReason?: string; // 返工原因
  reworkRequirement?: string; // 返工要求
  reworkStartTime?: string; // 返工开始时间
  reworkFinishTime?: string; // 返工完成时间
  auditRemark?: string; // 审核备注
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
}

// 质检项目实体
export interface QualityCheckItem {
  id: string; // 主键ID
  qualityCheckId: string; // 质检单ID
  categoryCode: string; // 检查大类代码，如：BRAKE_SYSTEM
  categoryName: string; // 检查大类名称，如：制动系统检查
  itemCode: string; // 检查项代码，如：BRAKE_PEDAL_TRAVEL
  itemName: string; // 检查项名称，如：制动踏板行程
  itemType: QualityCheckItemType; // 检查项类型：BOOLEAN、NUMERIC、TEXT
  checkResult?: QualityCheckResult; // 检查结果：PASS/FAIL（BOOLEAN类型）
  numericValue?: number; // 数值结果（NUMERIC类型）
  textValue?: string; // 文本结果（TEXT类型）
  standardValue?: string; // 标准值说明
  unit?: string; // 数值单位，如：mm、bar、℃
  isRequired: boolean; // 是否必填
  sortOrder: number; // 排序顺序
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
}

// 工时详情
export interface QualityCheckLaborHourDetail {
  id: string; // 主键ID
  workOrderNo: string; // 工单编号
  laborCode: string; // 工时代码
  laborName: string; // 工时名称
  laborType: 'maintenance' | 'repair' | 'claim'; // 工时类型：保养、维修、索赔
  isOutsource: boolean; // 是否委外
  isAdditional: boolean; // 是否为增项
  standardHours: number; // 标准工时
  actualHours?: number; // 实际工时
  unitPrice?: number; // 工时单价
}

// 零件详情
export interface QualityCheckPartsDetail {
  id: string; // 主键ID
  workOrderNo: string; // 工单编号
  partCode: string; // 零件代码
  partName: string; // 零件名称
  partType: 'maintenance' | 'repair' | 'claim'; // 零件类型：保养、维修、索赔
  isAdditional: boolean; // 是否为增项
  quantity: number; // 零件数量
  unitPrice?: number; // 零件单价
  totalAmount?: number; // 总金额
}

// 客户车辆信息
export interface QualityCheckCustomerVehicleInfo {
  // 客户信息
  appointmentCustomerName?: string; // 预约人姓名
  appointmentCustomerPhone?: string; // 预约人手机号
  serviceCustomerName: string; // 送修人姓名
  serviceCustomerPhone: string; // 送修人手机号

  // 车辆信息
  plateNumber: string; // 车牌号
  vin: string; // VIN码
  vehicleModel: string; // 车型
  vehicleConfig?: string; // 配置
  vehicleColor?: string; // 颜色
  mileage?: number; // 里程数
  vehicleAge?: number; // 车龄
}

// 质检操作日志
export interface QualityCheckOperationLog {
  id: string; // 主键ID
  qualityCheckId: string; // 质检单ID
  qualityCheckNo: string; // 质检单编号
  operationType: 'edit' | 'submit_review' | 'approve_pass' | 'rework'; // 操作类型
  operatorId: string; // 操作人ID
  operatorName: string; // 操作人姓名
  operatorRole: 'technician' | 'technician_manager' | 'system'; // 操作人角色
  operationContent: string; // 操作内容描述
  beforeStatus?: QualityCheckStatus; // 操作前状态
  afterStatus?: QualityCheckStatus; // 操作后状态
  operationTime: string; // 操作时间
  ipAddress?: string; // 操作IP地址
  userAgent?: string; // 用户代理信息
}

// 质检单列表项
export interface QualityCheckListItem {
  id: string; // 主键ID
  qualityCheckNo: string; // 质检单编号
  status: QualityCheckStatus; // 质检状态
  workOrderNo: string; // 工单编号
  workOrderType: WorkOrderType; // 工单类型
  isClaimRelated: boolean; // 是否涉及索赔
  isOutsourceRelated: boolean; // 是否涉及委外
  serviceCustomerName: string; // 送修人姓名
  serviceCustomerPhone: string; // 送修人手机号
  plateNumber: string; // 车牌号
  vehicleModel: string; // 车型
  vehicleConfig?: string; // 配置
  vehicleColor?: string; // 颜色
  technicianName: string; // 技师姓名
  startTime?: string; // 开工时间
  finishTime?: string; // 完工时间
  estimatedHours?: number; // 预计工时
  actualHours?: number; // 实际工时
  reworkReason?: string; // 返工原因
  reworkRequirement?: string; // 返工要求
  reworkStartTime?: string; // 返工开始时间
  reworkFinishTime?: string; // 返工完成时间
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
}

// 质检单列表查询参数
export interface QualityCheckListParams {
  page?: number; // 当前页码
  pageSize?: number; // 每页数量
  workOrderNo?: string; // 工单编号
  qualityCheckNo?: string; // 质检单编号
  status?: QualityCheckStatus | QualityCheckStatus[]; // 质检状态
  workOrderType?: WorkOrderType; // 工单类型
  serviceCustomerName?: string; // 送修人姓名
  serviceCustomerPhone?: string; // 送修人手机号
  plateNumber?: string; // 车牌号
  technicianId?: string; // 技师ID
  technicianName?: string; // 技师姓名
  createTimeStart?: string; // 创建时间起始
  createTimeEnd?: string; // 创建时间结束
  createTimeRange?: string[]; // 创建时间范围（前端使用）
}

// 质检单详细信息
export interface QualityCheckDetail {
  // 基础信息
  qualityCheck: QualityCheckOrder;

  // 客户车辆信息
  customerVehicleInfo: QualityCheckCustomerVehicleInfo;

  // 工时详情列表
  laborHourDetails: QualityCheckLaborHourDetail[];

  // 零件详情列表
  partsDetails: QualityCheckPartsDetail[];

  // 质检项目列表
  checkItems: QualityCheckItem[];

  // 操作日志
  operationLogs: QualityCheckOperationLog[];
}

// 质检项目分类表单
export interface QualityCheckCategoryForm {
  categoryCode: string; // 检查大类代码
  categoryName: string; // 检查大类名称
  items: {
    itemCode: string; // 检查项代码
    itemName: string; // 检查项名称
    itemType: QualityCheckItemType; // 检查项类型
    checkResult?: QualityCheckResult; // 检查结果
    numericValue?: number; // 数值结果
    textValue?: string; // 文本结果
    standardValue?: string; // 标准值说明
    unit?: string; // 数值单位
    isRequired: boolean; // 是否必填
  }[];
}

// 质检项目表单项
export interface QualityCheckItemForm {
  id: string; // 项目ID
  qualityCheckId: string; // 质检单ID
  itemCode: string; // 检查项代码
  itemType: QualityCheckItemType; // 检查项类型
  checkResult?: QualityCheckResult; // 检查结果
  numericValue?: number; // 数值结果
  textValue?: string; // 文本结果
  isRequired: boolean; // 是否必填
}

// 质检提交表单
export interface QualityCheckSubmitForm {
  qualityCheckId: string; // 质检单ID
  actualHours?: number; // 实际工时
  remarks?: string; // 备注
  checkItems: QualityCheckItemForm[]; // 质检项目列表
  isDraft?: boolean; // 是否为草稿
}

// 质检审核表单
export interface QualityCheckAuditForm {
  qualityCheckId: string; // 质检单ID
  auditResult: QualityCheckAuditResult; // 审核结果
  reworkReason?: string; // 返工原因（返工时必填）
  reworkRequirement?: string; // 返工要求（返工时必填）
  auditRemark?: string; // 审核备注
}

// 返工处理表单
export interface QualityCheckReworkForm {
  qualityCheckId: string; // 质检单ID
  reworkStartTime?: string; // 返工开始时间
  reworkFinishTime?: string; // 返工完成时间
}

// 质检响应数据接口
export interface QualityCheckResponse {
  success: boolean;
  message: string;
  data?: unknown;
}

// 预约统计数据接口
export interface AppointmentStatistics {
  totalAppointments: number;
  notArrived: number;
  arrived: number;
  cancelled: number;
  noShow: number;
  pendingPayment: number;
  arrivalRate: number;
}

// 售后预约详情接口，通常比列表项包含更多信息
export interface AppointmentDetail extends AppointmentListItem {
  store: {
    id: string;
    name: string;
    address: string;
  };
  paymentStatus?: 'paid' | 'unpaid' | 'refunded';
  paymentAmount?: number;
  paymentOrderNumber?: string;
}

export interface AppointmentListItem {
  id: string;
  licensePlate: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  mileage: number;
  reservationContactName: string;
  reservationContactPhone: string;
  serviceContactName: string;
  serviceContactPhone: string;
  appointmentTime: string;
  timeSlot: string;
  serviceType: 'maintenance' | 'repair';
  status: 'not_arrived' | 'arrived' | 'cancelled' | 'no_show' | 'pending_payment';
  customerDescription?: string;
  createdAt: string;
  updatedAt: string;
  serviceAdvisor?: {
    id: string;
    name: string;
  };
  technician?: {
    id: string;
    name: string;
  };
  qualityInspectionId?: string;
  inspectionCreated?: boolean;
  productionDate?: string;
}

// 售后预约列表查询参数的接口定义
export interface AppointmentListParams {
  appointmentId?: string;
  licensePlate?: string;
  reservationPhone?: string;
  servicePhone?: string;
  dateRange?: [string, string];
  status?: 'not_arrived' | 'arrived' | 'cancelled' | 'no_show' | 'pending_payment';
  serviceType?: 'maintenance' | 'repair';
  serviceAdvisorId?: string;
  technicianId?: string;
  page?: number;
  pageSize?: number;
}

// ==================== 环检单管理相关类型定义 ====================

// 环检单状态枚举
export type InspectionFormStatus =
  | 'pending'         // 待处理
  | 'inProgress'      // 进行中
  | 'pendingConfirm'  // 待确认
  | 'confirmed';      // 已确认

// 登记类型枚举
export type RegisterType = 'appointment' | 'walkIn'; // 预约 | 到店

// 服务类型枚举
export type ServiceType = 'maintenance' | 'repair'; // 保养 | 维修

// 环检单列表项接口
export interface InspectionFormListItem {
  inspectionNo: string;
  inspectionStatus: InspectionFormStatus;
  repairmanName: string;
  repairmanPhone: string;
  licensePlateNo: string;
  vehicleModel: string;
  vehicleConfig: string;
  color: string;
  mileage: number;
  vehicleAge: number;
  serviceAdvisor: string;
  technician: string;
  registerType: string;
  serviceType: string;
  customerConfirmTime: string;
  createTime: string;
  updateTime: string;
  inspectionContent?: any // Temporarily allow any for content
}

export interface InspectionFormDetail extends InspectionFormListItem {
  inspectionContent: any // Define a proper type later
}

// 环检单搜索参数接口
export interface InspectionFormSearchParams {
  inspectionNo: string;
  inspectionStatus: string;
  licensePlateNo: string;
  repairmanName: string;
  technician: string;
  repairmanPhone: string;
  createTimeRange: (Date | null)[] | null;
}

// 工单创建表单相关类型
export interface WorkOrderCreateForm {
  inspectionFormData: InspectionFormListItem;
  workOrderType: WorkOrderType;
  priority: WorkOrderPriority;
  remarks: string;
  laborItems: LaborItem[];
  partsItems: PartsItem[];
}

// 工时项目接口
export interface LaborItem {
  laborId: string;
  laborCode: string;
  laborName: string;
  type: string;
  isClaim: boolean;
  isAdded: boolean;
  standardHours: number;
  unitPrice: number;
  subtotal: number;
}

// 零件项目接口
export interface PartsItem {
  partId: string;
  partCode: string;
  partName: string;
  isClaim: boolean;
  isAdded: boolean;
  availableStock: number;
  quantity: number;
  unitPrice: number;
  subtotal: number;
}

// 费用统计接口
export interface CostSummary {
  laborCost: number;
  partsCost: number;
  totalAmount: number;
}

// 项目搜索接口
export interface ProjectSearchItem {
  projectId: string;
  projectCode: string;
  projectName: string;
  projectType: string;
  standardHours?: number;
  unitPrice: number;
  availableStock?: number;
}

// ==================== 工单审批相关类型定义 ====================

// 审批类型枚举
export type WorkOrderApprovalType = 'claim_approval' | 'cancel_approval'; // 索赔审批 | 取消工单审批

// 审批状态枚举
export type WorkOrderApprovalStatus = 'pending_review' | 'reviewed'; // 待审核 | 已审核

// 审批结果枚举
export type WorkOrderApprovalResult = 'approved' | 'rejected'; // 审批通过 | 审批驳回

// 审批级别枚举
export type ApprovalLevel = 'first_level' | 'second_level'; // 一级审批 | 二级审批

// 超时状态枚举
export type TimeoutStatus = 'normal' | 'about_to_timeout' | 'timeout'; // 正常 | 即将超时 | 已超时

// 用户角色枚举（审批相关）
export type ApprovalUserRole = 'service_advisor' | 'technician_manager' | 'factory_manager'; // 服务顾问 | 技师经理 | 厂端管理人员

// 索赔类型枚举
export type ClaimType = 'labor_claim' | 'parts_claim'; // 工时索赔 | 零件索赔

// 审批申请实体
export interface WorkOrderApprovalRequest {
  approvalNo: string; // 审批单号
  approvalType: WorkOrderApprovalType; // 审批类型
  orderId: string; // 工单ID
  requestReason: string; // 申请原因
  approvalStatus: WorkOrderApprovalStatus; // 审批状态
  approvalResult?: WorkOrderApprovalResult; // 审批结果
  currentLevel: ApprovalLevel; // 当前审批级别
  submitterId: string; // 提交人ID
  submitterName: string; // 提交人姓名
  submitTime: string; // 提交时间
  timeoutStatus: TimeoutStatus; // 超时状态
  remainingTime?: string; // 剩余时间描述
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
}

// 审批过程实体
export interface WorkOrderApprovalProcess {
  processId: string; // 过程ID
  approvalNo: string; // 审批单号
  approvalLevel: ApprovalLevel; // 审批级别
  approverId: string; // 审批人ID
  approverName: string; // 审批人姓名
  approvalTime?: string; // 审批时间
  approvalResult?: WorkOrderApprovalResult; // 审批结果
  approvalRemark?: string; // 审批备注
  isOvertime: boolean; // 是否超时
  createTime: string; // 创建时间
}

// 索赔内容实体
export interface WorkOrderClaimContent {
  claimId: string; // 索赔ID
  orderId: string; // 工单ID
  claimType: ClaimType; // 索赔类型
  itemCode: string; // 项目编码
  itemName: string; // 项目名称
  quantity: number; // 数量
  unitPrice: number; // 单价
  claimAmount: number; // 索赔金额
  createTime: string; // 创建时间
}

// 工单信息（审批相关字段）
export interface WorkOrderApprovalInfo {
  orderId: string; // 工单ID
  orderNo: string; // 工单编号
  orderStatus: WorkOrderStatus; // 工单状态
  paymentStatus: string; // 支付状态
  claimApprovalStatus?: WorkOrderApprovalStatus; // 索赔审批状态
  claimApprovalResult?: WorkOrderApprovalResult; // 索赔审批结果
  currentApprovalLevel?: ApprovalLevel; // 当前审批级别
  lastApprovalTime?: string; // 最后审批时间
}

// 客户信息
export interface CustomerApprovalInfo {
  customerId: string; // 客户ID
  customerName: string; // 客户姓名
  phone: string; // 联系电话
  senderName: string; // 送修人名称
  senderPhone: string; // 送修人手机号
}

// 车辆信息
export interface VehicleApprovalInfo {
  vehicleId: string; // 车辆ID
  licensePlate: string; // 车牌号
  vin: string; // VIN码
  model: string; // 车型
  configuration: string; // 配置
  color: string; // 颜色
  saleTime?: string; // 销售时间
  mileage: number; // 里程数
  vehicleAge: number; // 车龄（月）
  serviceTime: string; // 送修时间
}

// 门店信息
export interface StoreApprovalInfo {
  storeId: string; // 门店ID
  storeCode: string; // 门店代码
  storeName: string; // 门店名称
  region: string; // 区域
}

// 审批列表项（待审批）
export interface PendingApprovalListItem {
  id: string; // 序号
  approvalNo: string; // 审批单号
  approvalType: WorkOrderApprovalType; // 审批类型
  submitterName: string; // 提交人
  submitTime: string; // 提交时间
  orderNo: string; // 工单编号
  requestReason: string; // 申请原因
  timeoutStatus: TimeoutStatus; // 超时状态
  remainingTime?: string; // 剩余时间
  customerName: string; // 客户姓名
  licensePlate: string; // 车牌号
  vehicleModel: string; // 车型
  storeName: string; // 门店名称
  currentLevel: ApprovalLevel; // 当前审批级别
}

// 审批列表项（已审批）
export interface CompletedApprovalListItem {
  id: string; // 序号
  approvalNo: string; // 审批单号
  approvalType: WorkOrderApprovalType; // 审批类型
  submitterName: string; // 提交人
  submitTime: string; // 提交时间
  orderNo: string; // 工单编号
  requestReason: string; // 申请原因
  approvalResult: WorkOrderApprovalResult; // 审批结果
  approvalRemark: string; // 审批备注
  approvalTime: string; // 审批时间
  approverName: string; // 审批人
  customerName: string; // 客户姓名
  licensePlate: string; // 车牌号
  vehicleModel: string; // 车型
  storeName: string; // 门店名称
}

// 审批查询参数
export interface WorkOrderApprovalListParams {
  page?: number; // 当前页码
  pageSize?: number; // 每页数量
  approvalNo?: string; // 审批单号
  approvalType?: WorkOrderApprovalType; // 审批类型
  approvalStatus?: WorkOrderApprovalStatus; // 审批状态
  submitterName?: string; // 提交人
  orderNo?: string; // 工单编号
  submitTimeStart?: string; // 提交时间起始
  submitTimeEnd?: string; // 提交时间结束
  storeId?: string; // 门店ID（厂端管理人员可见）
  customerName?: string; // 客户姓名
}

// 车型主数据相关类型
export interface VehicleModelMasterItem {
  id: string
  model: string
  variantName: string
  variantCode: string
  colourName: string
  colourCode: string
  fmrid: string
  createTime: string
  updateTime: string
}

export interface VehicleModelSearchParams {
  model: string
  variantName: string
  colourName: string
  fmrid: string
}

export interface SyncLogItem {
  id: string
  syncTime: string
  status: 'success' | 'failed'
  recordCount: number
  errorMessage?: string
}

// 审批详情（索赔审批）
export interface ClaimApprovalDetail {
  // 基础信息
  approvalNo: string; // 审批单号
  approvalType: WorkOrderApprovalType; // 审批类型
  submitterName: string; // 提交人
  submitTime: string; // 提交时间
  orderNo: string; // 工单编号
  requestReason: string; // 申请原因

  // 客户信息
  customerInfo: CustomerApprovalInfo;

  // 车辆信息
  vehicleInfo: VehicleApprovalInfo;

  // 索赔内容
  claimLaborList: WorkOrderClaimContent[]; // 索赔工时列表
  claimPartsList: WorkOrderClaimContent[]; // 索赔零件列表
  claimLaborTotal: number; // 索赔工时总额
  claimPartsTotal: number; // 索赔零件总额
  claimTotalAmount: number; // 索赔总金额

  // 审批状态
  approvalStatus: WorkOrderApprovalStatus; // 审批状态
  approvalResult?: WorkOrderApprovalResult; // 审批结果
  currentLevel: ApprovalLevel; // 当前审批级别

  // 审批历史
  approvalProcessList: WorkOrderApprovalProcess[]; // 审批过程记录
}

// 审批详情（取消审批）
export interface CancelApprovalDetail {
  // 基础信息
  approvalNo: string; // 审批单号
  approvalType: WorkOrderApprovalType; // 审批类型
  submitterName: string; // 提交人
  submitTime: string; // 提交时间
  orderNo: string; // 工单编号
  cancelReason: string; // 取消原因

  // 客户信息
  customerInfo: CustomerApprovalInfo;

  // 车辆信息
  vehicleInfo: VehicleApprovalInfo;

  // 工单状态信息
  currentOrderStatus: WorkOrderStatus; // 当前工单状态
  estimatedStartTime?: string; // 预计开工时间
  assignedTechnicianName?: string; // 分配技师

  // 审批状态
  approvalStatus: WorkOrderApprovalStatus; // 审批状态
  approvalResult?: WorkOrderApprovalResult; // 审批结果

  // 审批历史
  approvalProcessList: WorkOrderApprovalProcess[]; // 审批过程记录
}

// 审批操作请求
export interface WorkOrderApprovalActionRequest {
  approvalNo: string; // 审批单号
  approvalResult: WorkOrderApprovalResult; // 审批结果
  approvalRemark: string; // 审批备注
  approverId: string; // 审批人ID
}

// 操作日志项
export interface ApprovalOperationLog {
  logId: string; // 日志ID
  operationType: string; // 操作类型
  operator: string; // 操作人
  operationTime: string; // 操作时间
  operationRemark: string; // 操作备注
}

// 审批统计信息
export interface ApprovalStatistics {
  totalPending: number; // 待审批总数
  totalCompleted: number; // 已审批总数
  overtimeCount: number; // 超时数量
  approvalRate: number; // 审批通过率
  averageApprovalTime: number; // 平均审批时间（小时）
}

// 售后结算管理相关类型定义
export interface SettlementListItem {
  id: string
  settlementNo: string // 结算单号
  workOrderNo: string // 工单号
  workOrderType: 'maintenance' | 'repair' | 'warranty' // 工单类型：保养、维修、索赔
  settlementStatus: 'pre_settlement' | 'pending_settlement' | 'completed' | 'cancelled' // 结算单状态
  paymentStatus: 'pending' | 'deposit_paid' | 'fully_paid' | 'refunding' | 'refunded' // 支付状态
  customerName: string // 送修人姓名
  customerPhone: string // 送修人手机号
  vehiclePlate: string // 车牌号
  vehicleModel: string // 车型
  vehicleConfig: string // 配置
  vehicleColor: string // 颜色
  technician: string // 技师
  serviceAdvisor: string // 服务顾问
  totalAmount: number // 结算单金额
  paidAmount: number // 已付金额
  payableAmount: number // 应付金额
  createdAt: string // 创建时间
  inspectionStatus: string // 质检状态
}

export interface SettlementListParams {
  settlementNo?: string
  workOrderNo?: string
  settlementStatus?: string
  paymentStatus?: string
  workOrderType?: string
  customerName?: string
  customerPhone?: string
  vehiclePlate?: string
  technician?: string
  serviceAdvisor?: string
  createdAtStart?: string
  createdAtEnd?: string
  page: number
  pageSize: number
}

export interface SettlementDetail {
  id: string
  settlementNo: string // 结算单号
  workOrderNo: string // 工单号
  workOrderType: 'maintenance' | 'repair' | 'warranty' // 工单类型
  settlementStatus: 'pre_settlement' | 'pending_settlement' | 'completed' | 'cancelled' // 结算单状态
  paymentStatus: 'pending' | 'deposit_paid' | 'fully_paid' | 'refunding' | 'refunded' // 支付状态
  createdAt: string // 创建时间
  serviceAdvisor: string // 服务顾问

  // 客户信息
  customerName: string // 送修人姓名
  customerPhone: string // 送修人手机号

  // 车辆信息
  vehiclePlate: string // 车牌号
  vin: string // VIN号
  vehicleModel: string // 车型
  vehicleConfig: string // 配置
  vehicleColor: string // 颜色
  vehicleAge: string // 车龄
  mileage: string // 里程数

  // 服务包信息
  servicePackage?: {
    packageCode: string // 服务包编码
    packageName: string // 服务包名称
  }

  // 工时费用明细
  laborItems: SettlementLaborItem[]

  // 零件费用明细
  partItems: SettlementPartItem[]

  // 费用汇总
  laborTotalAmount: number // 工时总金额
  laborReceivableAmount: number // 工时应收金额
  partTotalAmount: number // 零件总金额
  partReceivableAmount: number // 零件应收金额
  warrantyAmount: number // 索赔金额
  totalAmount: number // 结算总金额
  packageRightsDeduction: number // 套餐权益抵扣
  receivableTotal: number // 应收总计
  payableAmount: number // 应付金额

  // 支付信息
  paidAmount: number // 已付金额
  discountAmount: number // 优惠金额
  remarks: string // 备注

  // 操作日志
  operationLogs: OperationLog[]
}

export interface SettlementLaborItem {
  laborCode: string // 工时编码
  laborName: string // 工时项目名称
  standardHours: number // 标准工时
  unitPrice: number // 工时单价
  laborType: string // 工时类型
  subtotal: number // 小计
  receivableAmount: number // 应收
  remarks: string // 备注
  isPackageItem: boolean // 是否套餐内项目
}

export interface SettlementPartItem {
  partCode: string // 零件编码
  partName: string // 零件名称
  quantity: number // 零件数量
  unitPrice: number // 零件单价
  partType: string // 零件类型
  subtotal: number // 小计
  receivableAmount: number // 应收
  remarks: string // 备注
  isPackageItem: boolean // 是否套餐内项目
}

export interface OperationLog {
  operation: string // 操作内容
  operator: string // 操作人
  operatedAt: string // 操作时间
}

// 收退款相关类型定义
export interface PaymentRecord {
  id: string
  paymentNo: string // 收退款单号
  businessType: '收款' | '退款' // 业务类型：收款、退款
  transactionNo: string // 流水号
  paymentMethod: 'cash' | 'pos' | 'bank_transfer' | 'wechat' | 'alipay' // 收款方式
  amount: number // 金额
  paymentType: '尾款' | '定金' | '全款' // 收款类型：尾款、定金、全款
  paymentTime: string // 收款时间
  remarks: string // 备注
  createdAt: string // 创建时间
}

export interface PaymentForm {
  businessType: '收款' | '退款'
  transactionNo: string
  paymentMethod: 'cash' | 'pos' | 'bank_transfer' | 'wechat' | 'alipay'
  amount: number | null
  paymentType: '尾款' | '定金' | '全款'
  paymentTime: string
  remarks: string
}

export interface PaymentManagementData {
  settlementInfo: {
    settlementNo: string
    workOrderNo: string
    settlementStatus: string
    paymentStatus: string
    payableAmount: number
    paidAmount: number
  }
  customerInfo: {
    customerName: string
    customerPhone: string
    vehiclePlate: string
    vehicleModel: string
  }
  paymentRecords: PaymentRecord[]
}


