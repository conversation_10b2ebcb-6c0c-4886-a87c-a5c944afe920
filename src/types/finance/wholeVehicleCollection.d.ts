// 整车收款管理类型定义

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 整车收款列表项
export interface WholeVehicleCollectionItem {
  orderId: string;
  orderNumber: string;
  buyerName: string;
  buyerPhone: string;
  dealerStoreName: string;
  salesConsultantName: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  orderCreateTime: string;
  orderStatus: string;
  paymentStatus: string;
  vehicleSalesPrice: number;
  insuranceAmount: number;
  otrAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  unpaidAmount: number;
  loanAmount: number;
  canInvoice: boolean;
  invoiceTime?: string;
  invoiceNumber?: string;
  createTime: string;
  updateTime: string;
}

// 整车收款搜索参数
export interface WholeVehicleCollectionSearchParams extends PageParams {
  orderNumber?: string;
  buyerName?: string;
  buyerPhone?: string;
  orderStatus?: string;
  paymentStatus?: string;
  startDate?: string;
  endDate?: string;
  canInvoice?: boolean;
}

// 整车收款分页响应
export interface WholeVehicleCollectionPageResponse extends PageResponse<WholeVehicleCollectionItem> {}

// 订单详情信息
export interface OrderDetailInfo {
  // 订单基本信息
  orderId: string;
  orderNumber: string;
  orderCreateTime: string;
  orderStatus: string;
  paymentStatus: string;
  paymentMethod: string;
  loanAmount: number;
  canInvoice: boolean;
  invoiceTime?: string;
  invoiceNumber?: string;

  // 下单人信息
  ordererName: string;
  ordererPhone: string;

  // 购车人信息
  buyerName: string;
  buyerPhone: string;
  buyerIdType: string;
  buyerIdNumber: string;
  buyerEmail: string;
  buyerState: string;
  buyerCity: string;
  buyerPostcode: string;
  buyerAddress: string;

  // 经销商信息
  dealerRegion: string;
  dealerCity: string;
  dealerStoreName: string;

  // 销售顾问信息
  salesConsultantName: string;
  salesConsultantPhone?: string;
  salesConsultantEmail?: string;

  // 车辆信息
  model: string;
  variant: string;
  color: string;
  vin: string;
  warehouseName: string;
  productionDate: string;
  options?: Array<{
    name: string;
    price: number;
  }>;

  // 价格信息
  salesSubtotal?: number;
  consumptionTax?: number;
  salesTax?: number;
  numberPlatesFee?: number;
  accessoriesTotalAmount?: number;
  vehicleSalesPrice: number;
  insuranceAmount: number;
  otrAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  unpaidAmount: number;

  // 收退款记录
  paymentRecords: PaymentRecord[];
}

// 收退款记录
export interface PaymentRecord {
  paymentRecordId: string;
  paymentRecordNumber: string;
  orderId: string;
  businessType: '收款' | '退款';
  transactionNumber: string;
  channel: string;
  amount: number;
  paymentType: string;
  arrivalTime: string;
  remark?: string;
  dataSource: string;
  isDeletable?: boolean;
  createTime: string;
  creator: string;
}

// 添加收退款记录表单
export interface AddPaymentRecordForm {
  businessType: '收款' | '退款';
  transactionNumber: string;
  channel: string;
  amount: number;
  paymentType: string;
  arrivalTime: string;
  remark?: string;
}

// API响应类型
export interface ApiResponse<T> {
  code: string | number;
  message: string;
  result: T;
  timestamp?: number;
}

// 用户角色类型
export type UserRole = 'finance_manager' | 'payment_operator' | 'sales_manager';
