
import type { PaginationParams, PaginationResponse } from '@/types/common/api';

// 1. 库存概览统计
export interface InventoryDashboard {
  totalSkuCount: number;
  shortageCount: number;
  warningCount: number;
  occupiedValue: number;
  trends: {
    skuGrowthRate: number;
    trendDirection: 'UP' | 'DOWN' | 'STABLE';
  };
}

// 2. 库存列表查询
export type StockStatus = 'SHORTAGE' | 'WARNING' | 'NORMAL' | 'OVERSTOCKED';

export interface InventorySearchParams extends PaginationParams {
  storeId: number;
  category?: string;
  partCode?: string;
  partName?: string;
  stockStatus?: StockStatus[];
}

export interface InventoryItem {
  inventoryId: number;
  stockStatus: StockStatus;
  partCode: string;
  partName: string;
  brand: string;
  specification: string;
  currentStock: number;
  occupiedStock: number;
  damagedStock: number;
  safetyStock: number;
  availableStock: number;
  warehouseName: string;
  lastCheckTime: string;
}

export type InventoryListResponse = PaginationResponse<InventoryItem>;

// 3. 库存详情查询
export interface PartInfo {
  partCode: string;
  partName: string;
  specification: string;
  unit: string;
  retailPrice: number;
  purchasePrice: number;
}

export interface InventoryInfo {
  currentStock: number;
  availableStock: number;
  occupiedStock: number;
  damagedStock: number;
  safetyStock: number;
  maximumStock: number;
  stockStatus: StockStatus;
  shelfNumber: string;
  lastCheckTime: string;
  checkPerson: string;
}

export interface InventoryDetail {
  partInfo: PartInfo;
  inventoryInfo: InventoryInfo;
}

// 4. 库存趋势数据
export interface InventoryTrend {
  safetyStockLine: number;
  trendData: {
    date: string;
    stock: number;
  }[];
}

// 5. 库存调整
export type AdjustType = 'INCREASE' | 'DECREASE' | 'SET_TO';
export type AdjustReason = 'INVENTORY_CHECK' | 'SYSTEM_CORRECTION' | 'OTHER';

export interface InventoryAdjustParams {
  inventoryId: number;
  adjustType: AdjustType;
  quantity: number;
  reason: AdjustReason;
  remark?: string;
  operator: string;
}

export interface InventoryAdjustResult {
  adjustmentId: number;
  beforeStock: number;
  afterStock: number;
}

// 6. 批量补货申请
export interface ReplenishmentItem {
  inventoryId: number;
  requestQuantity: number;
}

export interface BatchReplenishmentParams {
  storeId: number;
  inventoryItems: ReplenishmentItem[];
  applicant: string;
  remark?: string;
}

export interface BatchReplenishmentResult {
  orderNo: string;
  totalItems: number;
}
