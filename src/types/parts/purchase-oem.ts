// 主机厂端采购管理类型定义

// 采购订单状态枚举（主机厂视角）
export type OemOrderStatus = 
  | 'PENDING_APPROVAL'      // 待审核
  | 'APPROVED'              // 已审核
  | 'REJECTED'              // 已驳回
  | 'PENDING_SHIPMENT'      // 待发货
  | 'PARTIALLY_SHIPPED'     // 部分发货
  | 'SHIPPED_ALL'           // 全部发货
  | 'CANCELLED';            // 已取消

// 主机厂端采购订单信息
export interface OemPurchaseOrder {
  orderId: number;
  orderNo: string;
  dealerId: number;
  dealerName: string;
  dealerContact: string;
  dealerPhone: string;
  warehouseId: number;
  warehouseName: string;
  status: OemOrderStatus;
  totalAmount: number;
  remarks?: string;
  creatorId: number;
  creatorName: string;
  createdAt: string;
  auditorId?: number;
  auditorName?: string;
  auditedAt?: string;
  auditRemarks?: string;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
}

// 主机厂端采购订单明细
export interface OemPurchaseOrderItem {
  itemId: number;
  purchaseOrderId: number;
  partId: string;
  partCode: string;
  partName: string;
  brand: string;
  specification: string;
  unit: string;
  purchasePrice: number;
  orderQuantity: number;
  shippedQuantity: number;
  factoryStock: number;      // 厂区库存
  availableStock: number;    // 可发货数量
  status: 'SUFFICIENT' | 'INSUFFICIENT' | 'OUT_OF_STOCK';
}

// 主机厂仪表盘数据
export interface OemDashboard {
  pendingApprovalCount: number;      // 待我审批
  approvedTodayCount: number;        // 今日已审
  pendingShipmentCount: number;      // 待发货订单
  last7DaysTotalAmount: number;      // 7日内申请总额
}

// 发货单创建数据
export interface ShipmentCreateForm {
  purchaseOrderId: number;
  shippingDate: string;
  carrier: string;
  trackingNumber: string;
  remarks?: string;
  items: ShipmentCreateItem[];
}

export interface ShipmentCreateItem {
  purchaseOrderItemId: number;
  partId: string;
  shippedQuantity: number;
}

// 审批表单数据
export interface ApprovalForm {
  orderId: number;
  isApproved: boolean;
  remarks?: string;
  auditorId: number;
}

// 主机厂端订单详情（包含完整信息）
export interface OemOrderDetail {
  orderInfo: OemPurchaseOrder;
  items: OemPurchaseOrderItem[];
  shipments?: ShipmentInfo[];
  statusHistory?: OrderStatusHistory[];
}

// 发货信息
export interface ShipmentInfo {
  shipmentId: number;
  shipmentNo: string;
  shippingDate: string;
  carrier: string;
  trackingNumber: string;
  remarks?: string;
  status: string;
  items: ShipmentInfoItem[];
}

export interface ShipmentInfoItem {
  partId: string;
  partCode: string;
  partName: string;
  unit: string;
  shippedQuantity: number;
}

// 订单状态历史
export interface OrderStatusHistory {
  id: number;
  orderId: number;
  fromStatus: string;
  toStatus: string;
  operatorId: number;
  operatorName: string;
  operatedAt: string;
  remarks?: string;
}

// 承运商信息
export interface CarrierInfo {
  carrierId: string;
  carrierName: string;
  contactPhone: string;
  serviceArea: string[];
}

// API请求参数类型
export interface OemOrderListParams {
  page: number;
  size: number;
  query?: string;
  status?: OemOrderStatus[];
  dealerId?: number;
  priority?: string;
  startDate?: string;
  endDate?: string;
}

export interface OemOrderDetailParams {
  orderId: number;
}

export interface OemApprovalParams {
  orderId: number;
  isApproved: boolean;
  remarks?: string;
  auditorId: number;
}

export interface OemShipmentParams {
  orderId: number;
  carrier: string;
  trackingNumber: string;
  remarks?: string;
  items: {
    partId: string;
    shippedQuantity: number;
  }[];
}

export interface OemInventoryCheckParams {
  partIds: string[];
}

// 库存检查结果
export interface InventoryCheckResult {
  partId: string;
  partCode: string;
  partName: string;
  factoryStock: number;
  reservedStock: number;
  availableStock: number;
  status: 'SUFFICIENT' | 'INSUFFICIENT' | 'OUT_OF_STOCK';
}

// 批量操作类型
export interface BatchApprovalParams {
  orderIds: number[];
  isApproved: boolean;
  remarks?: string;
  auditorId: number;
}

export interface BatchShipmentParams {
  orderIds: number[];
  carrier: string;
  shippingDate: string;
  remarks?: string;
}

// 分页响应类型
export interface PaginationResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
  };
}

// API响应类型
export interface ApiResponse<T> {
  code: number;
  message?: string;
  data: T;
}

// 统计数据类型
export interface OrderStatistics {
  totalOrders: number;
  totalAmount: number;
  averageAmount: number;
  byStatus: Record<OemOrderStatus, number>;
  byPriority: Record<string, number>;
  trends: {
    daily: StatisticPoint[];
    weekly: StatisticPoint[];
    monthly: StatisticPoint[];
  };
}

export interface StatisticPoint {
  date: string;
  count: number;
  amount: number;
}