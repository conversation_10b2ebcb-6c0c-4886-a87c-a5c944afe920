// src/types/afterSales/workOrder.d.ts

// 工单状态枚举
export type WorkOrderStatus =
  | 'draft'                 // 草稿
  | 'pending_confirmation'  // 待客户确认
  | 'confirmed'            // 已确认
  | 'in_progress'          // 进行中
  | 'completed'            // 已完成
  | 'cancelled'            // 已取消
  | 'rejected';            // 客户拒绝

// 工单优先级枚举
export type WorkOrderPriority = 'normal' | 'urgent'; // 普通 | 紧急

// 客户来源枚举
export type CustomerSource = 'appointment' | 'walk_in'; // 预约客户 | 自然到店客户

// 工单类型枚举
export type WorkOrderType = 'repair' | 'maintenance' | 'claim'; // 维修 | 保养 | 保险

// 付款状态枚举
export type PaymentStatus = 'unpaid' | 'partial' | 'paid'; // 未付款 | 部分付款 | 已付款

// 工单列表项接口
export interface WorkOrderListItem {
  workOrderId: string;
  workOrderNumber: string;
  status: WorkOrderStatus;
  priority: WorkOrderPriority;
  customerSource: CustomerSource;
  workOrderType: WorkOrderType;
  customerName: string;
  customerPhone: string;
  licensePlate: string;
  vehicleModel: string;
  vehicleVin: string;
  serviceAdvisor: string;
  technician: string;
  estimatedAmount: number;
  actualAmount: number;
  paymentStatus: PaymentStatus;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  description: string;
  notes?: string;
}

// 工单详情接口
export interface WorkOrderDetail extends WorkOrderListItem {
  serviceItems: ServiceItem[];
  partItems: PartItem[];
  laborItems: LaborItem[];
  customerSignature?: string;
  technicianNotes?: string;
}

// 服务项目接口
export interface ServiceItem {
  id: string;
  name: string;
  description: string;
  price: number;
  quantity: number;
  total: number;
}

// 配件项目接口
export interface PartItem {
  id: string;
  partNumber: string;
  partName: string;
  brand: string;
  price: number;
  quantity: number;
  total: number;
  supplier?: string;
}

// 工时项目接口
export interface LaborItem {
  id: string;
  operation: string;
  description: string;
  standardHours: number;
  actualHours: number;
  hourlyRate: number;
  total: number;
  technician: string;
}

// 工单搜索参数接口
export interface WorkOrderSearchParams {
  workOrderNumber?: string;
  status?: WorkOrderStatus;
  priority?: WorkOrderPriority;
  customerSource?: CustomerSource;
  workOrderType?: WorkOrderType;
  customerName?: string;
  customerPhone?: string;
  licensePlate?: string;
  serviceAdvisor?: string;
  technician?: string;
  createdAtStart?: string;
  createdAtEnd?: string;
  page?: number;
  pageSize?: number;
}

// 工单分页响应接口
export interface WorkOrderPageResponse {
  list: WorkOrderListItem[];
  total: number;
}

// 工单表单数据接口
export interface WorkOrderFormData extends Omit<WorkOrderListItem, 'workOrderId' | 'workOrderNumber' | 'createdAt' | 'updatedAt'> {
  workOrderId?: string;
  serviceItems?: ServiceItem[];
  partItems?: PartItem[];
  laborItems?: LaborItem[];
}

// 状态变更表单接口
export interface StatusChangeForm {
  workOrderId: string;
  newStatus: WorkOrderStatus;
  reason?: string;
  notes?: string;
}
