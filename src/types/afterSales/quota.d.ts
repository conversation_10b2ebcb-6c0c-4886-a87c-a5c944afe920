// src/types/afterSales/quota.d.ts

export interface TimeSlot {
  id: number;
  start: string;
  end: string;
  quota: number;
}

export interface QuotaConfig {
  id: number;
  configDate: string;
  timeSlotCount: number;
  totalQuota: number;
  bookedQuantity: number;
  lastUpdateTime: string;
  isExpired: boolean;
}

export interface QuotaSearchParams {
  page?: number;
  pageSize?: number;
  startDate?: string;
  endDate?: string;
  storeId?: string;
}

export interface QuotaPageResponse {
  list: QuotaConfig[];
  total: number;
}

export interface QuotaConfigRequest {
  date: string;
  timeSlots: Omit<TimeSlot, 'id'>[];
}

export interface StoreInfo {
  id: string;
  name: string;
  code: string;
}

export interface QuotaValidationResult {
  isValid: boolean;
  message?: string;
}
