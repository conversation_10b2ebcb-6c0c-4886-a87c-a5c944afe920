// src/types/afterSales/workAssignment.d.ts

// 工单状态枚举
export type WorkOrderStatus =
  | 'pending_assign'    // 待分配
  | 'pending_start'     // 待开始
  | 'in_progress'       // 进行中
  | 'completed'         // 已完成
  | 'cancelled'         // 已取消
  | 'on_hold';          // 暂停

// 工单类型枚举
export type WorkOrderType = 'maintenance' | 'repair' | 'inspection' | 'insurance'; // 保养 | 维修 | 检查 | 保险

// 工单优先级枚举
export type WorkOrderPriority = 'low' | 'normal' | 'high' | 'urgent'; // 低 | 普通 | 高 | 紧急

// 技师状态枚举
export type TechnicianStatus = 'available' | 'busy' | 'offline'; // 空闲 | 忙碌 | 离线

// 工单列表项接口
export interface WorkOrderListItem {
  workOrderId: string;
  workOrderNo: string;
  customerName: string;
  customerPhone: string;
  licensePlate: string;
  vehicleModel: string;
  vehicleVin: string;
  status: WorkOrderStatus;
  workOrderType: WorkOrderType;
  priority: WorkOrderPriority;
  serviceAdvisorId: string;
  serviceAdvisorName: string;
  assignedTechnicianId?: string;
  assignedTechnicianName?: string;
  estimatedDuration: number; // 预计工时（分钟）
  estimatedStartTime?: string;
  actualStartTime?: string;
  actualEndTime?: string;
  creationTime: string;
  updateTime: string;
  description: string;
  notes?: string;
}

// 技师信息接口
export interface TechnicianInfo {
  technicianId: string;
  technicianName: string;
  technicianCode: string;
  status: TechnicianStatus;
  skillLevel: number; // 技能等级 1-5
  specialties: string[]; // 专长领域
  currentWorkload: number; // 当前工作负荷（百分比）
  maxWorkload: number; // 最大工作负荷（百分比）
  workingHours: {
    start: string; // 工作开始时间
    end: string;   // 工作结束时间
  };
  contactInfo: {
    phone: string;
    email?: string;
  };
  department: string;
  position: string;
  hireDate: string;
  isActive: boolean;
}

// 工单搜索参数接口
export interface WorkOrderListParams {
  workOrderId?: string;
  workOrderNo?: string;
  customerName?: string;
  licensePlate?: string;
  status?: WorkOrderStatus;
  workOrderType?: WorkOrderType;
  priority?: WorkOrderPriority;
  serviceAdvisorId?: string;
  assignedTechnicianId?: string;
  creationTimeStart?: string;
  creationTimeEnd?: string;
  estimatedStartTimeStart?: string;
  estimatedStartTimeEnd?: string;
  page?: number;
  pageSize?: number;
}

// 工单分页响应接口
export interface WorkOrderPageResponse {
  list: WorkOrderListItem[];
  total: number;
  page: number;
  pageSize: number;
}

// 分配工单请求接口
export interface AssignWorkOrderRequest {
  workOrderId: string;
  technicianId: string;
  estimatedStartTime?: string;
  estimatedDuration?: number;
  notes?: string;
}

// 重新分配工单请求接口
export interface ReassignWorkOrderRequest {
  workOrderId: string;
  fromTechnicianId: string;
  toTechnicianId: string;
  reason: string;
  estimatedStartTime?: string;
  notes?: string;
}

// 分配结果接口
export interface AssignmentResult {
  success: boolean;
  message: string;
  workOrderId: string;
  assignedTechnicianId: string;
  estimatedStartTime?: string;
}

// 技师工作负荷接口
export interface TechnicianWorkload {
  technicianId: string;
  technicianName: string;
  currentOrders: number;
  totalWorkload: number; // 总工作负荷（分钟）
  availableCapacity: number; // 可用容量（分钟）
  workloadPercentage: number; // 工作负荷百分比
}

// 派工统计接口
export interface WorkAssignmentStatistics {
  totalOrders: number;
  pendingAssignment: number;
  assignedOrders: number;
  inProgressOrders: number;
  completedOrders: number;
  averageAssignmentTime: number; // 平均分配时间（分钟）
  technicianUtilization: number; // 技师利用率（百分比）
}
