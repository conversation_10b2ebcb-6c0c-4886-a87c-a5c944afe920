// src/types/afterSales/workAssignmentDashboard.d.ts

// 看板统计数据接口
export interface DashboardStatistics {
  totalOrders: number;
  pendingAssignment: number;
  assignedOrders: number;
  inProgressOrders: number;
  completedOrders: number;
  averageAssignmentTime: number; // 平均分配时间（分钟）
  technicianUtilization: number; // 技师利用率（百分比）
  todayCompletedOrders: number;
  todayNewOrders: number;
}

// 技师排班信息接口
export interface TechnicianSchedule {
  technicianInfo: {
    technicianId: string;
    technicianName: string;
    technicianCode: string;
    department: string;
    skillLevel: number;
    specialties: string[];
    avatar?: string;
  };
  workingHours: {
    start: string;
    end: string;
    breakStart?: string;
    breakEnd?: string;
  };
  currentStatus: 'available' | 'busy' | 'break' | 'offline';
  assignedOrders: AssignedOrderInfo[];
  workloadPercentage: number;
  availableCapacity: number; // 可用容量（分钟）
  totalCapacity: number; // 总容量（分钟）
  efficiency: number; // 工作效率（百分比）
}

// 分配的工单信息接口
export interface AssignedOrderInfo {
  workOrderId: string;
  workOrderNo: string;
  customerName: string;
  licensePlate: string;
  vehicleModel: string;
  workOrderType: 'maintenance' | 'repair' | 'inspection' | 'insurance';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  estimatedDuration: number; // 预计工时（分钟）
  scheduledStartTime: string;
  scheduledEndTime: string;
  actualStartTime?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'delayed';
  progress: number; // 进度百分比
}

// 工作负荷数据接口
export interface WorkloadData {
  technicianId: string;
  technicianName: string;
  currentWorkload: number; // 当前工作负荷（分钟）
  maxCapacity: number; // 最大容量（分钟）
  utilizationRate: number; // 利用率（百分比）
  assignedOrdersCount: number;
  averageOrderDuration: number; // 平均工单时长（分钟）
  efficiency: number; // 效率评分
}

// 状态分布数据接口
export interface StatusDistribution {
  status: string;
  count: number;
  percentage: number;
  color: string;
}

// 实时更新配置接口
export interface RealTimeConfig {
  enabled: boolean;
  interval: number; // 更新间隔（秒）
  lastUpdateTime: string;
  autoRefresh: boolean;
}

// 看板筛选参数接口
export interface DashboardFilters {
  date: string;
  department?: string;
  technicianId?: string;
  workOrderType?: string;
  priority?: string;
}

// 图表配置接口
export interface ChartConfig {
  theme: 'light' | 'dark';
  animation: boolean;
  responsive: boolean;
  height: number;
  width?: number;
}

// 看板设置接口
export interface DashboardSettings {
  layout: 'grid' | 'list';
  chartConfig: ChartConfig;
  realTimeConfig: RealTimeConfig;
  displayOptions: {
    showStatistics: boolean;
    showCharts: boolean;
    showSchedule: boolean;
    showRealTimeUpdates: boolean;
  };
  refreshInterval: number;
}

// 技师绩效数据接口
export interface TechnicianPerformance {
  technicianId: string;
  technicianName: string;
  completedOrdersToday: number;
  completedOrdersWeek: number;
  completedOrdersMonth: number;
  averageCompletionTime: number; // 平均完成时间（分钟）
  qualityScore: number; // 质量评分
  customerSatisfaction: number; // 客户满意度
  onTimeCompletionRate: number; // 按时完成率
}

// 工单流转数据接口
export interface OrderFlowData {
  hour: string;
  newOrders: number;
  assignedOrders: number;
  completedOrders: number;
  cancelledOrders: number;
}

// 部门工作负荷接口
export interface DepartmentWorkload {
  departmentId: string;
  departmentName: string;
  totalTechnicians: number;
  activeTechnicians: number;
  totalOrders: number;
  completedOrders: number;
  averageUtilization: number;
  efficiency: number;
}
