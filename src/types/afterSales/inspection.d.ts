// src/types/afterSales/inspection.d.ts

// 环检单状态枚举
export type InspectionStatus =
  | 'pending'         // 待环检
  | 'in_progress'     // 环检中
  | 'pending_confirm' // 待确认
  | 'confirmed';      // 已确认

// 登记类型枚举
export type RegisterType = 'appointment' | 'walk_in'; // 预约 | 到店

// 服务类型枚举
export type ServiceType = 'maintenance' | 'repair'; // 保养 | 维修

// 环检单列表项接口
export interface InspectionListItem {
  inspectionNo: string;
  inspectionStatus: InspectionStatus;
  repairmanName: string;
  repairmanPhone: string;
  licensePlateNo: string;
  vehicleModel: string;
  vehicleConfig: string;
  color: string;
  mileage: number;
  vehicleAge: number;
  serviceAdvisor: string;
  registerType: RegisterType;
  serviceType: ServiceType;
  confirmChannel?: string; // 确认渠道：线上确认/线下确认
  customerConfirmTime: string;
  createTime: string;
  updateTime: string;
  inspectionContent?: InspectionContent;
}

// 环检单详情接口
export interface InspectionDetail extends InspectionListItem {
  inspectionContent: any; // 检查内容详情
}

// 环检单搜索参数接口
export interface InspectionSearchParams {
  inspectionNo?: string;
  inspectionStatus?: string;
  licensePlateNo?: string;
  repairmanName?: string;
  repairmanPhone?: string;
  serviceAdvisor?: string; // 新增服务顾问筛选
  createTimeRange?: [string, string] | null;
  page?: number;
  pageSize?: number;
}

// 环检单分页响应接口
export interface InspectionPageResponse {
  list: InspectionListItem[];
  total: number;
}



// 客户确认表单接口
export interface CustomerConfirmForm {
  inspectionNo: string;
  confirmTime: string;
  customerSignature?: string;
  remarks?: string;
}

// 创建工单请求数据接口
export interface CreateWorkOrderRequest {
  inspectionNo: string;
  serviceType: string;
  priority: string;
  description: string;
  recommendedItems: RecommendedItem[];
}

// 推荐维修项目接口
export interface RecommendedItem {
  id: number;
  name: string;
  selected: boolean;
}

// 创建工单响应接口
export interface CreateWorkOrderResponse {
  success: boolean;
  workOrderNo?: string;
}

// 工单优先级枚举
export type WorkOrderPriority = 'low' | 'medium' | 'high' | 'urgent';

// 检查结果枚举
export type CheckResult = 'good' | 'attention' | 'poor' | 'not_applicable';

// 停车区域记录接口
export interface ParkingAreaRecord {
  waitingArea?: string;    // 等候区
  leavingArea?: string;    // 离开区
  parkingPosition?: string; // 停车区位置
}

// 仪表盘检查接口
export interface DashboardCheck {
  mileageRecord?: number;     // 里程数记录
  batteryLevel?: string;      // 电池电量
  drivingRange?: number;      // 续航里程
  energyConsumption?: number; // 能耗显示
}

// 检查项目接口
export interface CheckItem {
  result: CheckResult;
  photos?: string[];  // 照片附件URL数组
  notes?: string;     // 备注
}

// 功能性检查接口
export interface FunctionalCheck {
  instrumentsAndIndicators?: CheckItem; // 仪表和指示器
  airConditioningSystem?: CheckItem;    // 空调系统
  wipersAndWashers?: CheckItem;         // 雨刮和清洗器
  infotainmentSystem?: CheckItem;       // 信息娱乐系统
}

// 警告灯检查接口
export interface WarningLightCheck {
  batterySystem?: CheckItem;  // 电池系统
  motorSystem?: CheckItem;    // 电机系统
  chargingSystem?: CheckItem; // 充电系统
}

// 外观检查接口
export interface AppearanceCheck {
  frontCheck?: CheckItem;     // 前检查
  rearCheck?: CheckItem;      // 后检查
  leftCheck?: CheckItem;      // 左检查
  rightCheck?: CheckItem;     // 右检查
  roofCheck?: CheckItem;      // 车顶检查
  chargingPortCover?: CheckItem; // 充电口盖
}

// 电动系统检查接口
export interface ElectricSystemCheck {
  highVoltageBattery?: CheckItem; // 高压电池检查
  motorSystemCheck?: CheckItem;   // 电机系统检查
  chargingSystemCheck?: CheckItem; // 充电系统检查
}

// 轮胎检查接口
export interface TireCheck {
  treadDepth?: {
    frontRight?: number; // 前右胎纹深度(mm)
    frontLeft?: number;  // 前左胎纹深度(mm)
    rearRight?: number;  // 后右胎纹深度(mm)
    rearLeft?: number;   // 后左胎纹深度(mm)
  };
  tirePressure?: {
    frontRight?: number; // 前右轮胎压力(bar)
    frontLeft?: number;  // 前左轮胎压力(bar)
    rearRight?: number;  // 后右轮胎压力(bar)
    rearLeft?: number;   // 后左轮胎压力(bar)
  };
  tpmsFunction?: CheckItem; // TPMS功能检查
}

// 结构化环检内容接口
export interface InspectionContent {
  parkingAreaRecord?: ParkingAreaRecord;   // 停车区域记录
  dashboardCheck?: DashboardCheck;         // 仪表盘检查
  functionalCheck?: FunctionalCheck;       // 功能性检查
  warningLightCheck?: WarningLightCheck;   // 警告灯检查
  appearanceCheck?: AppearanceCheck;       // 外观检查
  electricSystemCheck?: ElectricSystemCheck; // 电动系统检查
  tireCheck?: TireCheck;                   // 轮胎检查
  customerIssues?: string;                 // 客户自述问题
}
