// 结算管理相关类型定义

// 基础类型定义
export type SettlementStatus = 'pre_settlement' | 'pending_settlement' | 'completed' | 'cancelled';
export type PaymentStatus = 'pending' | 'deposit_paid' | 'fully_paid' | 'refunding' | 'refunded';
export type WorkOrderType = 'maintenance' | 'repair' | 'warranty';
export type PaymentMethod = 'cash' | 'pos' | 'wechat' | 'alipay' | 'bank_transfer';

// 结算单列表项接口
export interface SettlementListItem {
  id: string;
  settlementNo: string;
  workOrderNo: string;
  workOrderType: WorkOrderType;
  settlementStatus: SettlementStatus;
  paymentStatus: PaymentStatus;
  customerName: string;
  customerPhone: string;
  vehiclePlate: string;
  vehicleModel: string;
  vehicleConfig: string;
  vehicleColor: string;
  technician: string;
  serviceAdvisor: string;
  totalAmount: number;
  paidAmount: number;
  payableAmount: number;
  createdAt: string;
  inspectionStatus: string;
}

// 搜索参数接口
export interface SettlementSearchParams {
  page: number;
  pageSize: number;
  settlementNo?: string;
  workOrderNo?: string;
  settlementStatus?: SettlementStatus;
  paymentStatus?: PaymentStatus;
  customerName?: string;
  vehiclePlate?: string;
  technician?: string;
  serviceAdvisor?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  amountMin?: number;
  amountMax?: number;
}

// 工时明细接口
export interface SettlementLaborItem {
  id: string;
  laborCode: string;
  laborName: string;
  standardHours: number;
  actualHours: number;
  unitPrice: number;
  totalAmount: number;
  receivableAmount: number;
  warrantyAmount: number;
  discountAmount: number;
  remarks: string;
}

// 零件明细接口
export interface SettlementPartItem {
  id: string;
  partCode: string;
  partName: string;
  specification: string;
  unit: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  receivableAmount: number;
  warrantyAmount: number;
  discountAmount: number;
  remarks: string;
}

// 操作日志接口
export interface OperationLog {
  id: string;
  operationType: string;
  operationTime: string;
  operator: string;
  operationContent: string;
  remarks: string;
}

// 结算详情接口
export interface SettlementDetail {
  id: string;
  settlementNo: string;
  workOrderNo: string;
  workOrderType: WorkOrderType;
  settlementStatus: SettlementStatus;
  paymentStatus: PaymentStatus;
  createdAt: string;
  serviceAdvisor: string;
  
  // 客户信息
  customerName: string;
  customerPhone: string;
  
  // 车辆信息
  vehiclePlate: string;
  vin: string;
  vehicleModel: string;
  vehicleConfig: string;
  vehicleColor: string;
  vehicleAge: string;
  mileage: string;
  
  // 服务包信息
  servicePackage?: {
    packageCode: string;
    packageName: string;
  };
  
  // 费用明细
  laborItems: SettlementLaborItem[];
  partItems: SettlementPartItem[];
  
  // 费用汇总
  laborTotalAmount: number;
  laborReceivableAmount: number;
  partTotalAmount: number;
  partReceivableAmount: number;
  warrantyAmount: number;
  totalAmount: number;
  packageRightsDeduction: number;
  receivableTotal: number;
  payableAmount: number;
  paidAmount: number;
  discountAmount: number;
  remarks: string;
  
  // 操作日志
  operationLogs: OperationLog[];
}

// 收退款记录接口
export interface PaymentRecord {
  id: string;
  paymentNo: string;
  businessType: '收款' | '退款';
  transactionNo: string;
  paymentMethod: PaymentMethod;
  amount: number;
  paymentType: string;
  paymentTime: string;
  remarks: string;
  createdAt: string;
}

// 收退款表单接口
export interface PaymentForm {
  settlementId: string;
  businessType: '收款' | '退款';
  paymentMethod: PaymentMethod;
  amount: number;
  paymentType: string;
  remarks?: string;
}

// 统计信息接口
export interface SettlementStatistics {
  todayTotal: number;
  todayCompleted: number;
  todayAmount: number;
  weekTotal: number;
  weekCompleted: number;
  weekAmount: number;
  monthTotal: number;
  monthCompleted: number;
  monthAmount: number;
  completionRate: number;
  avgSettlementAmount: number;
  monthlyTrend: MonthlyTrendItem[];
}

// 月度趋势项接口
export interface MonthlyTrendItem {
  month: string;
  total: number;
  completed: number;
  amount: number;
  completionRate: number;
}

// 分页响应接口
export interface PaginationResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
}

// 导出参数接口
export interface ExportSettlementParams extends SettlementSearchParams {
  exportType: 'all' | 'selected';
  exportFormat: 'excel' | 'pdf';
  selectedIds?: string[];
}

// 批量操作接口
export interface BatchSettlementOperation {
  settlementIds: string[];
  operation: 'push' | 'cancel' | 'complete';
  remarks?: string;
}

// 结算选项接口
export interface SettlementOption {
  label: string;
  value: string;
}
