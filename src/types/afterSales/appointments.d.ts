// src/types/afterSales/appointments.d.ts

export type AppointmentStatus =
  | 'pending_support'  // 待支持
  | 'scheduled'        // 已预约
  | 'arrived'          // 已到店
  | 'cancelled'        // 已取消
  | 'no_show';         // 未履约

export type JobType = 'maintenance' | 'repair';

export interface LaborItem {
  code: string;
  name: string;
  standardHours: number;
  unitPrice: number;
  subtotal: number;
}

export interface PartsItem {
  code: string;
  name: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  subtotal: number;
}

export interface MaintenancePackage {
  id: string;
  code: string;
  name: string;
  totalAmount: number;
  laborItems: LaborItem[];
  partsItems: PartsItem[];
}

export interface AppointmentListItem {
  id: string;
  licensePlate: string;
  reservationContactName: string;
  reservationContactPhone: string;
  serviceContactName: string;
  serviceContactPhone: string;
  appointmentTime: string;
  timeSlot: string;
  serviceType: 'maintenance' | 'repair';
  status: AppointmentStatus;
  serviceAdvisor?: { id: string; name: string };
  qualityInspectionId?: string;
  createdAt: string;
  inspectionCreated?: boolean;
  customerDescription?: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  mileage?: number;
  productionDate?: string;
}

export interface AppointmentDetail extends AppointmentListItem {
  store: {
    id: string;
    name: string;
    address: string;
  };
  paymentStatus?: 'paid' | 'unpaid' | 'refunded';
  paymentAmount?: number;
  paymentOrderNumber?: string;
  paymentMethod?: 'online' | 'store';
  maintenancePackage?: MaintenancePackage;
}

export interface AppointmentListParams {
  appointmentId?: string;
  licensePlate?: string;
  reservationPhone?: string;
  servicePhone?: string;
  dateRange?: [string, string];
  status?: AppointmentStatus;
  serviceType?: JobType; // 保持字段名为serviceType但含义为预约类型
  serviceAdvisorId?: string;
  technicianId?: string;
  page?: number;
  pageSize?: number;
}

export interface AppointmentPageResponse {
  list: AppointmentListItem[];
  total: number;
}
