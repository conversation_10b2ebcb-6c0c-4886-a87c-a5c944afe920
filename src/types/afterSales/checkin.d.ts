// src/types/afterSales/checkin.d.ts

export interface CheckinListItem {
  checkinId: string;
  status: 'NORMAL' | 'CANCELLED'; // 登记单状态：正常、已取消
  licensePlate: string;
  vin: string;
  vehicleModel: string;
  vehicleConfiguration: string;
  color: string;
  mileage?: number;
  vehicleAge?: string; // 车龄，如"24月"
  repairPersonName: string;
  repairPersonPhone: string;
  serviceAdvisor: string;
  inspectionOrderNo?: string | null; // 环检单号（原关联维修单号）
  serviceType: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  cancelReason?: string; // 取消原因
  isDeleted: boolean;
}

export interface CheckinListParams {
  checkinId?: string;
  licensePlate?: string;
  repairPersonName?: string;
  repairPersonPhone?: string;
  ownerIc?: string; // 车主IC
  status?: 'NORMAL' | 'CANCELLED'; // 登记单状态筛选
  createdAtStart?: string;
  createdAtEnd?: string;
  page?: number;
  pageSize?: number;
}

export interface CheckinPageResponse {
  list: CheckinListItem[];
  total: number;
}

export interface VehicleInfo {
  licensePlate: string;
  vin?: string;
  vehicleModel?: string;
  vehicleConfiguration?: string;
  color?: string;
  mileage?: number;
  vehicleAge?: string; // 修改为string类型，与CheckinListItem保持一致
}

export interface CheckinFormData extends Omit<CheckinListItem, 'checkinId' | 'createdAt' | 'updatedAt'> {
  checkinId?: string;
}

// 取消登记参数
export interface CancelCheckinParams {
  cancelReason: string; // 取消原因，必填
}

// 车主IC查询参数
export interface OwnerIcQueryParams {
  ownerIc: string;
}
