/**
 * 菜单管理相关类型定义
 */

/** 菜单信息 */
export interface Menu {
  id: number | string;
  menuName: string;
  menuCode: string;
  parentId?: number | string | null;
  menuType: string;        // "directory" | "menu" | "button"
  menuStatus: string;      // "normal" | "disabled"
  menuIcon?: string | null;
  menuPath?: string | null;
  component?: string | null;
  sortOrder?: number;
  isVisible: number;       // 0: 隐藏, 1: 显示
  permission?: string;
  menuSide?: string;       // "factory" | "dealer"
  isCache?: number;        // 0: 不缓存, 1: 缓存
  createdAt?: string | null;
  children?: Menu[] | null;
}

/** 菜单搜索参数 */
export interface MenuSearchParams {
  menuName?: string;
  menuStatus?: string;
  menuType?: string;
  current?: number;
  size?: number;
}

/** 菜单分页响应 */
export interface MenuPageResponse {
  records: Menu[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

/** 新增菜单请求 */
export interface CreateMenuRequest {
  menuName: string;
  menuCode: string;
  parentId?: string | number;
  menuType: string;
  menuStatus: string;
  menuIcon?: string;
  menuPath?: string;
  component?: string;
  sortOrder?: number;
  isVisible: number;
  permission?: string;
  menuSide?: string;
  isCache?: number;
}

/** 更新菜单请求 */
export interface UpdateMenuRequest extends CreateMenuRequest {
  id: string | number;
}

/** 菜单树选项 */
export interface MenuTreeOption {
  id: string | number;
  menuName: string;
  children?: MenuTreeOption[];
}

/** 菜单表单数据 */
export interface MenuFormData {
  parentId: string | number | null;
  menuType: string;
  menuName: string;
  menuCode: string;
  menuIcon?: string;
  menuPath?: string;
  component?: string;
  permission?: string;
  menuStatus: string;
  isVisible: number;
  sortOrder?: number;
  menuSide?: string;
  isCache?: number;
}

/** API响应格式 */
export interface ApiResponse<T> {
  code: number | string;
  message: string;
  result: T;
  success: boolean;
  timestamp: number;
  traceId: string;
}
