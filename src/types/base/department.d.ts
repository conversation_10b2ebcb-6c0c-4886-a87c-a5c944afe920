/**
 * 部门管理相关类型定义
 * 参考门店管理的类型定义风格
 */

// ================================
// MyBatisPlus分页参数（与门店管理保持一致）
// ================================

/** MyBatisPlus分页参数 */
export interface PageParams {
  /** 当前页码，从1开始 */
  current?: number;
  /** 每页条数 */
  size?: number;
}

/** MyBatisPlus标准分页响应 */
export interface PageResponse<T> {
  /** 数据列表 */
  records: T[];
  /** 总条数 */
  total: number;
  /** 当前页码 */
  current: number;
  /** 每页条数 */
  size: number;
  /** 总页数 */
  pages: number;
}

// ================================
// 部门管理相关类型
// ================================

/** 部门信息 */
export interface Department {
  id: string;
  departmentCode: string;
  departmentName: string;
  parentId?: string | null;      // 上级部门ID
  /** ✅ 部门类型：使用数据字典编码 DEPARTMENT_TYPE (0202) */
  departmentType: string;
  /** ✅ 部门状态：使用数据字典编码 COMMON_STATUS (0002) */
  departmentStatus: string;
  departmentHead?: string;       // 部门负责人
  sortOrder?: number | null;     // 排序
  description?: string;          // 部门描述
  createdAt?: string | null;     // 创建时间
  children?: Department[] | null; // 子部门
}

/** 部门搜索参数（继承分页参数） */
export interface DepartmentSearchParams extends PageParams {
  departmentName?: string;       // 部门名称
  departmentCode?: string;       // 部门编码
  /** ✅ 部门状态：使用数据字典 COMMON_STATUS (0002) */
  departmentStatus?: string;
  /** ✅ 部门类型：使用数据字典 0202 */
  departmentType?: string;
}

/** 部门分页响应 */
export interface DepartmentPageResponse extends PageResponse<Department> {}

/** 新增部门请求 */
export interface CreateDepartmentRequest {
  departmentCode: string;
  departmentName: string;
  /** ✅ 部门类型：使用数据字典编码 DEPARTMENT_TYPE (0202) */
  departmentType: string;
  /** ✅ 部门状态：使用数据字典编码 COMMON_STATUS (0002) */
  departmentStatus: string;
  departmentHead?: string;
  sortOrder?: number;
  description?: string;
  parentId?: string;
}

/** 更新部门请求 */
export interface UpdateDepartmentRequest extends CreateDepartmentRequest {
  id: string;
}

// ================================
// API响应格式（与门店管理保持一致）
// ================================

/** API响应格式 */
export interface ApiResponse<T> {
  code: number | string;
  message: string;
  result: T;
  success: boolean;
  timestamp: number;
  traceId: string;
}
