// 发票管理相关类型定义

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 发票基本信息接口
export interface InvoiceItem {
  id: string;
  invoiceNumber: string;        // 发票编号 522102-XXXXXXX
  invoiceDate: string;          // 开票日期
  orderNumber: string;          // 订单编号
  customerName: string;         // 购车人姓名
  customerPhone: string;        // 购车人手机号
  customerEmail: string;        // 购车人邮箱
  customerAddress: string;      // 购车人地址
  customerState?: string;       // 州
  customerCity?: string;        // 城市
  customerPostcode?: string;    // 邮编
  vin: string;                  // VIN码
  model: string;                // 车型
  variant: string;              // 变型
  color: string;                // 颜色
  salesStore: string;           // 销售门店
  salesConsultant: string;      // 销售顾问
  paymentMethod: string;        // 付款方式 (现金/贷款)
  financeCompany?: string;      // 金融公司
  loanAmount: number;           // 贷款金额
  invoiceAmount: number;        // 发票总额
  createdTime: string;          // 创建时间
  status: string;               // 发票状态
  salesType?: string;           // 销售类型
}

// 发票详细信息接口
export interface InvoiceDetail extends InvoiceItem {
  // 发票基本信息
  invoiceCompany: string;       // 开票公司名称
  companyAddress: string;       // 公司地址
  gstNumber: string;            // GST编号
  sstNumber: string;            // SST编号
  contactPhone: string;         // 联系电话
  contactEmail: string;         // 联系邮箱

  // 客户详细信息
  deliveryNumber: string;       // 交车单编号
  salesConsultantId: string;    // 销售顾问ID

  // 车辆详细信息
  tinNumber: string;            // TIN编号
  modelCode: string;            // 车型代码
  modelDescription: string;     // 车型描述
  engineNumber: string;         // 发动机号
  chassisNumber: string;        // 底盘号
  engineDisplacement: string;   // 发动机排量
  vehicleRegistrationDate: string; // 车辆登记日期
  creator: string;              // 创建人
  updater: string;              // 更新人
  updateTime: string;           // 更新时间

  // 金融信息详情
  financeType: string;          // 金融方式
  loanPeriod: string;           // 贷款期限

  // 保险信息详情
  insuranceCompany: string;     // 保险公司
  agentCode: string;            // 代理编码
  policyNumber: string;         // 保单号
  issueDate: string;            // 出单日期
  insuranceAmount: number;      // 保险费用

  // 价格结构明细
  vehicleSalesPrice: number;    // 车辆销售价
  licensePlateFee: number;      // 车牌费用
  accessories: AccessoryItem[]; // 选配件明细
  accessoryAmount: number;      // 选配件总金额
  otrFees: OTRFeeItem[];       // OTR费用明细
  otrAmount: number;           // OTR费用总金额
  adjustmentAmount: number;     // 调整金额
  invoiceNetValue: number;      // 发票净值

  // 收据明细信息
  receipts: ReceiptItem[];      // 收据明细
}

// 选配件明细
export interface AccessoryItem {
  specification: string;       // 规格分类
  accessoryName: string;       // 配件名称
  unitPrice: number;           // 单价
  quantity: number;            // 数量
  amount: number;              // 总价
}

// OTR费用明细
export interface OTRFeeItem {
  feeCode: string;             // 费用编码
  feeType: string;             // 费用类型
  taxAmount: number;           // 费用金额
  effectiveDate: string;       // 生效日期
  expiryDate: string;          // 失效日期
}

// 收据明细
export interface ReceiptItem {
  receiptNumber: string;       // 收据编号
  receiptType: string;         // 业务类型
  receiptNo: string;           // 流水号
  paymentChannel: string;      // 渠道
  paidAmount: number;          // 金额
  collectionType: string;      // 收款类型
  arrivalTime: string;         // 到账时间
  remarks: string;             // 备注
}

// 发票搜索参数
export interface InvoiceSearchParams extends PageParams {
  invoiceNumber?: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  orderNumber?: string;
  vin?: string;
  salesType?: string;
  salesStore?: string;
  salesConsultant?: string;
  invoiceDateStart?: string;
  invoiceDateEnd?: string;
}

// 发票分页响应
export interface InvoicePageResponse extends PageResponse<InvoiceItem> {}

// 操作日志接口
export interface InvoiceOperationLog {
  id: string;
  operationType: string;        // 操作类型
  operator: string;             // 操作人
  operationTime: string;        // 操作时间
  operationDescription: string; // 操作描述
  operationResult: string;      // 操作结果
  errorMessage?: string;        // 错误信息
}

// 导出配置
export interface ExportConfig {
  format: 'excel' | 'pdf' | 'csv';
  scope: 'current' | 'all' | 'filtered';
  fields?: string[];
  searchParams?: InvoiceSearchParams;
}

// API响应类型
export interface ApiResponse<T> {
  code: string | number;
  message: string;
  result: T;
  timestamp: number;
}

// 门店选项
export interface StoreOption {
  code: string;
  dealerName: string;
}

// 销售顾问选项
export interface ConsultantOption {
  code: string;
  name: string;
  storeCode?: string;
}
