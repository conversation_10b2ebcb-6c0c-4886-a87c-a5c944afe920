// 工厂订单管理相关类型定义

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 工厂订单搜索参数
export interface FactoryOrderSearchParams extends PageParams {
  dealerName?: string;
  model?: string;
  variant?: string;
  orderStatus?: string;
  approvalStatus?: string;
  paymentStatus?: string;
  loanStatus?: string;
  insuranceStatus?: string;
  jpjRegistrationStatus?: string;
  orderNumber?: string;
  orderDateStart?: string;
  orderDateEnd?: string;
}

// 工厂订单列表项
export interface FactoryOrderListItem {
  id: string;
  orderNo: string;
  storeName: string;
  model: string;
  variant: string;
  color: string;
  orderStatus: string;
  approvalStatus: string;
  paymentStatus: string;
  loanStatus: string;
  insuranceStatus: string;
  jpjRegistrationStatus: string;
  orderDate: string;
  deliveryDate?: string;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  customerName: string;
  customerPhone: string;
  customerType: string;
  salesConsultant: string;
  remarks?: string;
  createTime: string;
  updateTime: string;
  vin?: string;
  paymentMethod?: string;
}

// 工厂订单详情
export interface FactoryOrderDetail extends FactoryOrderListItem {
  customerInfo: {
    name: string;
    phone: string;
    email?: string;
    address?: string;
    icNumber: string;
    customerType: string;
  };
  vehicleInfo: {
    model: string;
    variant: string;
    color: string;
    vin?: string;
    engineNumber?: string;
    chassisNumber?: string;
  };
  paymentInfo: {
    totalAmount: number;
    paidAmount: number;
    remainingAmount: number;
    paymentMethod: string;
    paymentRecords: PaymentRecord[];
  };
  loanInfo?: {
    loanAmount: number;
    loanBank: string;
    loanStatus: string;
    approvalDate?: string;
  };
  insuranceInfo?: {
    insuranceCompany: string;
    insuranceType: string;
    insuranceAmount: number;
    insuranceStatus: string;
  };
  jpjInfo?: {
    registrationNumber?: string;
    registrationStatus: string;
    registrationDate?: string;
  };
}

// 支付记录
export interface PaymentRecord {
  id: string;
  amount: number;
  paymentMethod: string;
  paymentDate: string;
  transactionNumber?: string;
  remarks?: string;
}

// 工厂订单统计
export interface FactoryOrderStatistics {
  monthlyOrderCount: number;
  dailyOrderCount: number;
  monthlyGrowthRate: number;
  dailyGrowthRate: number;
  topStoreName?: string;
  topStoreOrderCount?: number;
  topModelName?: string;
  topModelCount?: number;
  pendingDeliveryCount: number;
  lastUpdateTime: string;
  topDealers?: Array<{
    name: string;
    count: number;
  }>;
  topVehicles?: Array<{
    model: string;
    count: number;
  }>;
}

// 工厂订单分页响应
export interface FactoryOrderPageResponse {
  result: PageResponse<FactoryOrderListItem>;
}

// 导出参数
export interface ExportFactoryOrderParams {
  searchParams: FactoryOrderSearchParams;
  exportType: 'all' | 'all_filtered' | 'current_page';
}

// 导出响应
export interface ExportFactoryOrderResponse {
  downloadUrl: string;
  fileName: string;
}

// 兼容旧版本的类型别名
export type OrderStatistics = FactoryOrderStatistics;
export type FactoryOrderListParams = FactoryOrderSearchParams;
