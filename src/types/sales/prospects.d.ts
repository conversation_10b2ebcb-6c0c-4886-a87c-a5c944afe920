// 潜客管理模块类型定义

// 潜客基本信息接口
export interface ProspectItem {
  id: string;
  name: string;
  phoneNumber: string;
  sourceChannel: string;
  currentIntentLevel: string;
  prospectStatus: string;
  intentModel?: string;
  intentVariant?: string;
  intentColor?: string;
  currentSalesAdvisorId?: string;
  currentSalesAdvisorName?: string;
  leadAssociationTime?: string;
  lastFollowUpTime?: string;
  nextFollowUpTime?: string;
}

// 搜索参数接口
export interface ProspectSearchParams {
  prospectId?: string;
  customerName?: string;
  customerPhone?: string;
  countryCode?: string;
  phoneNumber?: string;
  sourceChannel?: string;
  customerLevel?: string;
  customerStatus?: string;
  salesAdvisorId?: string;
  isToday?: boolean;
  page?: number;
  pageSize?: number;
}

// 分页响应接口
export interface ProspectPageResponse {
  list: ProspectItem[];
  total: number;
}

// 新增潜客请求参数
export interface CreateProspectRequest {
  customerName: string;
  customerPhone: string;
  sourceChannel: string;
  intentLevel: string;
  salesAdvisorId?: string;
  intentModel?: string;
  intentVariant?: string;
  intentColor?: string;
  email?: string;
  region?: string;
  address?: string;
  idType?: string;
  idNumber?: string;
}

// 跟进记录接口
export interface FollowUpRecord {
  id: string;
  prospectId: string;
  followUpType: string;
  followUpTime: string;
  nextFollowUpTime?: string;
  followUpContent: string;
  followUpResult: string;
  intentLevelAfterFollowUp?: string;
  salesAdvisorName?: string;
}

// 新增跟进记录请求参数
export interface CreateFollowUpRequest {
  prospectId: string;
  followUpType: string;
  followUpTime: string;
  nextFollowUpTime?: string;
  followUpContent: string;
  followUpResult: string;
  intentLevelAfterFollowUp?: string;
}

// 分配顾问请求参数
export interface AssignAdvisorRequest {
  prospectId: string;
  newAdvisorId: string;
  reason?: string;
}

// 标记无意向请求参数
export interface MarkNoIntentionRequest {
  prospectId: string;
  reason: string;
  description?: string;
}

// 潜客详情信息接口
export interface ProspectDetailInfo {
  prospectBaseInfo: ProspectItem & {
    globalCustomerId?: string;
    email?: string;
    region?: string;
    address?: string;
    idType?: string;
    idNumber?: string;
  };
  testDriveRecords: TestDriveRecord[];
  followUpRecords: FollowUpRecord[];
  defeatApplications: DefeatApplication[];
  changeLogs: ChangeLog[];
  performanceAnalysis: PerformanceAnalysis;
}

// 试驾记录接口
export interface TestDriveRecord {
  testDriveRecordId: string;
  driverName: string;
  phoneNumber: string;
  testDriveModel: string;
  testDriveTime: string;
  testDriveFeedback?: string;
}

// 战败申请接口
export interface DefeatApplication {
  applicationId: string;
  applicationTime: string;
  applicantName: string;
  defeatReason: string;
  defeatDetails: string;
  auditStatus: string;
  auditTime?: string;
  auditorName?: string;
  auditComments?: string;
}

// 变更日志接口
export interface ChangeLog {
  changeLogId: string;
  changeTime: string;
  changeType: string;
  operatorName: string;
  originalValue: string;
  newValue: string;
  changeReason: string;
}

// 效果分析接口
export interface PerformanceAnalysis {
  totalFollowUpCount: number;
  mostActiveStore: string;
}

// 现有线索信息接口（用于搜索重复客户）
export interface ExistingLeadInfo {
  id: number;
  customerName: string;
  customerPhone: string;
  email?: string;
  sourceChannel: string;
}

// 搜索现有线索请求参数
export interface SearchExistingLeadRequest {
  customerName?: string;
  customerPhone?: string;
}
