// 交付管理模块类型定义
// 基于MyBatisPlus分页标准和数据字典规范

export interface DeliveryListItem {
  deliveryId: string;
  deliveryNumber: string;
  orderId: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  dealerStore: string;
  salesConsultant: string;
  orderStatus: string;
  deliveryStatus: string;
  customerConfirmed: boolean;
  confirmationType?: string;
  invoiceTime: string;
  deliveryTime?: string;
  customerConfirmTime?: string;
  deliveryNotes?: string;
  signaturePhoto?: string;
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
}

// 标准MyBatisPlus分页搜索参数
export interface DeliverySearchParams {
  pageNum?: number;        // 标准MyBatisPlus参数
  pageSize?: number;       // 标准MyBatisPlus参数
  deliveryNumber?: string;
  orderNumber?: string;
  customerName?: string;
  customerPhone?: string;
  vin?: string;
  model?: string;
  variant?: string;
  color?: string;
  orderStatus?: string;
  dealerStore?: string;
  salesConsultant?: string;
  deliveryStatus?: string;
  customerConfirmed?: boolean;
  confirmationType?: string;
  deliveryTimeRange?: [string, string];
  customerConfirmTimeRange?: [string, string];
}

// 标准MyBatisPlus分页响应
export interface DeliveryPageResponse {
  records: DeliveryListItem[];  // 标准MyBatisPlus响应
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

// 提交确认请求
export interface SubmitConfirmRequest {
  deliveryNumber: string;
  confirmationType: string;
  confirmationNotes?: string;
}

// 交车确认请求
export interface DeliveryConfirmRequest {
  deliveryNumber: string;
  deliveryTime: string;
  deliveryNotes?: string;
  signaturePhoto?: File;
}

// 导出设置
export interface ExportSettings {
  format: 'excel' | 'pdf';
  fields: string[];
  dateRange?: [string, string];
}

// 状态枚举类型
export type DeliveryStatus = 'pending_delivery' | 'pending_confirm' | 'delivered';
export type OrderStatus = 'normal' | 'cancelled' | 'pending_allocation' | 'allocating' | 'allocated' | 'pending_delivery' | 'delivered';
export type ConfirmationType = 'app' | 'offline';
export type UserRole = 'sales_consultant' | 'sales_manager' | 'admin';

// 字典类型
export type DictionaryType = string;
