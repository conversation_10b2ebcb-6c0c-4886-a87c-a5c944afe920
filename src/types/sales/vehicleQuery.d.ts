// 车辆查询相关类型定义

export interface VehicleQueryItem {
  id: string;
  factoryOrderNo: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  fmrId: string;
  warehouseName: string;
  stockStatus: string;        // 使用字符串类型，支持数据字典编码
  lockStatus: string;         // 使用字符串类型，支持数据字典编码
  invoiceStatus: string;      // 使用字符串类型，支持数据字典编码
  deliveryStatus: string;     // 使用字符串类型，支持数据字典编码
  invoiceDate: string;
  deliveryDate: string;
  storageDate: string;
  productionDate: string;
}

export interface VehicleQuerySearchParams {
  pageNum?: number;           // MyBatisPlus标准分页参数
  pageSize?: number;          // MyBatisPlus标准分页参数
  vin?: string;
  factoryOrderNo?: string;
  warehouseName?: string;
  model?: string;
  variant?: string;
  color?: string;
  fmrId?: string;
  stockStatus?: string;       // 支持数据字典编码
  lockStatus?: string;        // 支持数据字典编码
  invoiceStatus?: string;     // 支持数据字典编码
  deliveryStatus?: string;    // 支持数据字典编码
  invoiceDateStart?: string;
  invoiceDateEnd?: string;
  storageDateStart?: string;
  storageDateEnd?: string;
  productionDateStart?: string;
  productionDateEnd?: string;
}

export interface VehicleQueryPageResponse {
  result: {
    records: VehicleQueryItem[];  // MyBatisPlus标准响应结构
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
  };
}

// 车辆详情类型（与列表项相同）
export interface VehicleQueryDetail extends VehicleQueryItem {}

// 车辆配置相关类型
export interface VehicleVariant {
  variant: string;
  colorOptions: string[];
}

export interface VehicleConfiguration {
  model: string;
  modelName: string;
  code: string;
  variants: VehicleVariant[];
}

// 仓库信息类型
export interface WarehouseInfo {
  warehouseName: string;
  code: string;
  warehouseLocation?: string;
  warehouseStatus?: string;
}

// 导出表单类型
export interface ExportForm {
  format: 'excel' | 'csv';
  scope: 'current' | 'all';
}
