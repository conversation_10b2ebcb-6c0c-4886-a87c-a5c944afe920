// 车型主数据管理相关类型定义

export interface VehicleModelMasterItem {
  id: string;
  model: string;
  variantName: string;
  variantCode: string;
  colourName: string;
  colourCode: string;
  fmrid: string;
  createTime: string;
  updateTime: string;
}

// ✅ 修正为MyBatisPlus标准分页参数
export interface VehicleModelSearchParams {
  pageNum?: number;        // ✅ 标准MyBatisPlus参数
  pageSize?: number;       // ✅ 标准MyBatisPlus参数
  model?: string;
  variantName?: string;
  colourName?: string;
  fmrid?: string;
}

// ✅ 标准MyBatisPlus分页响应
export interface VehicleModelPageResponse {
  records: VehicleModelMasterItem[];  // ✅ 标准MyBatisPlus响应
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

// 同步日志相关类型
export interface SyncLogItem {
  id: string;
  syncTime: string;
  status: 'success' | 'failed';
  recordCount: number;
  errorMessage?: string;
}

// 同步操作响应
export interface SyncResponse {
  success: boolean;
  message: string;
}

// 导出参数
export interface ExportParams extends VehicleModelSearchParams {
  // 可以扩展导出特定的参数
}
