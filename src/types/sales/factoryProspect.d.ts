// Factory Prospect 厂端潜客池管理类型定义

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  current: number;     // 当前页码
  size: number;    // 每页条数
  pages: number;       // 总页数
}

// 厂端潜客搜索参数
export interface FactoryProspectSearchParams extends PageParams {
  leadId: string;
  storeId: string;
  storeCount: 'single' | 'multi' | '';  // 单店、多店、全部
  registrationTimeStart: string;
  registrationTimeEnd: string;
  viewType: 'all' | 'crossStore' | 'noIntention' | 'converted';  // 修改枚举值
}

export type ProspectStatus = 'following' | 'converted' | 'no_intention';

// 厂端潜客列表项
export interface FactoryProspectListItem {
  globalCustomerId: string;
  leadId: string;
  customerName: string;
  phoneNumber: string;  // 厂端无需脱敏，直接显示完整手机号
  associatedStoreCount: number;
  registrationTime: string;  // 新增注册时间字段
  prospectStatus: ProspectStatus;
  lastFollowUpTime?: string;
  createTime: string;
  updateTime: string;
}

// 厂端潜客统计
export interface FactoryProspectStatistics {
  totalLeadCount: number;
  hLevelProspectCount: number;
  monthlyDealProspectCount: number;  // 原 monthlyConversionProspectCount
  crossStoreCustomerCount: number;
  totalLeadCountVsLastMonth: number;
  hLevelProspectCountVsLastMonth: number;
  monthlyConversionRate: string;
  crossStoreCustomerRatio: string;
}

// 门店关联记录
export interface StoreAssociationRecord {
  storeId: string;
  storeName: string;
  leadAssociationTime: string;
  associationReason: string;
  currentSalesAdvisor: string;
  currentIntentLevel: string;
  lastFollowUpTime: string;
}

// 跟进记录
export interface FollowUpRecord {
  id: string;
  storeName: string;
  salesAdvisor: string;
  followUpTime: string;
  followUpMethod: string;
  followUpContent: string;
  intentLevel: string;
  nextFollowUpTime: string;
}

// 试驾记录
export interface TestDriveRecord {
  id: string;
  storeName: string;
  salesAdvisor: string;
  testDriveTime: string;
  vehicleModel: string;
  duration: number;
  customerFeedback: string;
}

// 战败记录
export interface DefeatRecord {
  markTime: string;        // 原 applyTime
  storeName: string;
  markerName: string;      // 原 applicantName
  defeatReason: string;
}

// 变更历史记录
export interface ChangeHistoryRecord {
  changeTime: string;
  storeName: string;
  changeType: string;
  operatorName: string;
  originalValue: string;
  newValue: string;
}

// 厂端潜客详情
export interface FactoryProspectDetail extends FactoryProspectListItem {
  customerInfo: {
    name: string;
    phone: string;
    email?: string;
    idType: string;
    idNumber: string;
    registerTime: string;
    registerSource: string;
    currentStatus: string;
  };
  storeAssociations: {
    associatedStoreCount: number;
    storeRecords: StoreAssociationRecord[];
  };
  followUpRecords: FollowUpRecord[];
  testDriveRecords: TestDriveRecord[];
  defeatRecords: DefeatRecord[];
  changeHistory: ChangeHistoryRecord[];
}

// 厂端潜客分页响应
export interface FactoryProspectPageResponse {
  result: PageResponse<FactoryProspectListItem>;
}

// 厂端潜客统计响应
export interface FactoryProspectStatisticsResponse {
  result: FactoryProspectStatistics;
}

// 厂端潜客详情响应
export interface FactoryProspectDetailResponse {
  result: FactoryProspectDetail;
}

// 选择框选项类型
export interface SelectOption {
  code: string;
  name: string;
}

// 客户基本信息响应
export interface CustomerBasicInfoResponse {
  globalCustomerId: string;
  customerName: string;
  customerPhone: string;
  idType: string;
  idNumber: string;
  email: string;
  registrationTime: string;
  sourceChannel: string;
  currentStatus: string;
}

// 门店关联响应
export interface StoreAssociationsResponse {
  associatedStoreCount: number;
  storeAssociationRecords: StoreAssociationRecord[];
}

// 跟进记录响应
export interface FollowUpRecordsResponse {
  totalCount: number;
  earliestTime: string;
  latestTime: string;
  followUpRecords: FollowUpRecord[];
}

// 试驾记录响应
export interface TestDriveRecordsResponse {
  totalCount: number;
  testDriveRecords: TestDriveRecord[];
}

// 战败记录响应
export interface DefeatRecordsResponse {
  totalCount: number;
  defeatRecords: DefeatRecord[];
}

// 变更历史响应
export interface ChangeHistoryResponse {
  totalCount: number;
  changeHistoryRecords: ChangeHistoryRecord[];
}

// 绩效分析响应
export interface PerformanceAnalysisResponse {
  totalFollowUpCount: number;
  totalAssociatedStoreCount: number;
  mostActiveStoreName: string;
  mostActiveStoreFollowUpCount: number;
}

// 兼容旧版本的类型（用于渐进式迁移）
export interface FactoryOverviewStatsResponse extends FactoryProspectStatistics {}
export interface FactoryProspectItem extends FactoryProspectListItem {}
