// 车辆登记相关类型定义

// MyBatisPlus分页参数
export interface PageParams {
  pageNum?: number;    // 当前页码，从1开始
  pageSize?: number;   // 每页条数
}

// MyBatisPlus标准分页响应
export interface PageResponse<T> {
  records: T[];        // 数据列表
  total: number;       // 总条数
  pageNum: number;     // 当前页码
  pageSize: number;    // 每页条数
  pages: number;       // 总页数
}

// 车辆登记列表项
export interface VehicleRegistrationItem {
  id: string;
  orderNo: string;
  customerName: string;
  customerPhone: string;
  vin: string;
  vehicleModel: string;
  vehicleColor: string;
  insuranceStatus: string;
  companyName: string;
  status: string;
  lastPushTime: string;
  registrationFee: number;
  salesAdvisorName: string;
  createdAt: string;
}

// 车辆登记搜索参数（继承分页参数）
export interface VehicleRegistrationSearchParams extends PageParams {
  orderNumber?: string;
  customerName?: string;
  customerPhone?: string;
  registrationStatus?: string;
  vin?: string;
  insuranceStatus?: string;
  salesAdvisor?: string;
  pushTimeStart?: string;
  pushTimeEnd?: string;
}

// 车辆登记分页响应
export interface VehicleRegistrationPageResponse extends PageResponse<VehicleRegistrationItem> {}

// 费用明细
export interface FeeDetail {
  id: number;
  registrationId: number;
  feeTypeDisplay: string;
  amount: number;
  createdAt: string;
  updatedAt: string;
}

// 操作日志
export interface OperationLog {
  id: number;
  registrationId: number;
  operationTime: string;
  operationType: string;
  operationTypeName: string;
  operationSource: string;
  operatorId: number;
  operatorName: string;
  ipAddress: string | null;
  result: string;
  resultName: string;
  beforeStatus: string | null;
  beforeStatusName: string | null;
  afterStatus: string | null;
  afterStatusName: string | null;
  remark: string;
  createdAt: string;
}

// 车辆登记详情（扁平化结构，匹配真实API）
export interface VehicleRegistrationDetail {
  // 基本信息
  id: number;
  status: string;
  statusName: string;
  createdAt: string;
  updatedAt: string;
  completionTime: string | null;
  lastPushTime: string | null;
  pushCount: number;
  maxPushCount: number | null;
  failureReason: string | null;
  registrationFee: number;
  certificateNumber: string | null;
  jpjCertificateNumber: string | null;
  jpjReferenceNumber: string | null;
  jpjResponseData: string | null;
  version: string | null;

  // 订单信息
  orderId: number;
  orderNumber: string;
  orderStatus: string;
  paymentStatus: string;

  // 客户信息
  customerName: string;
  customerPhone: string;
  customerIdType: string;
  customerIdNumber: string;
  customerEmail: string;
  customerAddress: string;
  customerCity: string;
  customerState: string;
  customerPostcode: string;
  customerType: string;

  // 车辆信息
  vin: string;
  engineNo: string | null;
  vehicleModel: string;
  vehicleVariant: string;
  vehicleColor: string;
  otrAmount: number | null;
  totalAmount: number | null;

  // 保险信息
  insuranceNumber: string | null;
  insuranceCompany: string | null;
  insuranceStatus: string | null;
  insuranceStartDate: string | null;
  insuranceEndDate: string | null;

  // 费用信息
  feeDetails: FeeDetail[];
  totalFeeAmount: number;

  // 操作日志
  operationLogs: OperationLog[];

  // 状态标识
  canPush: boolean;
  canRetry: boolean;
  isCompleted: boolean;

  // 操作员信息
  operatorId: number | null;
  operatorName: string | null;
  createdBy: string;
  updatedBy: string;
}

// API响应类型
export interface ApiResponse<T> {
  code: string | number;
  message: string;
  result: T;
  timestamp: number;
}

// 用户角色类型
export type UserRole = 'vehicle_registration_officer' | 'sales_advisor' | 'manager';

// 销售顾问选项
export interface SalesAdvisorOption {
  value: string;
  label: string;
}
