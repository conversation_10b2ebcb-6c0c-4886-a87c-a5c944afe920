// 订单审批管理类型定义
import type { DictionaryType } from '@/constants/dictionary';

// 审批类型
export type ApprovalType = 'cancel_order' | 'modify_info' | 'price_adjustment';

// 审批结果
export type ApprovalResult = 'approved' | 'rejected' | 'pending';

// 用户角色
export type UserRole = 'sales_manager' | 'regional_manager' | 'sales_consultant';

// 搜索参数接口（标准MyBatisPlus分页）
export interface OrderApprovalSearchParams {
  pageNum?: number;        // ✅ 标准MyBatisPlus参数
  pageSize?: number;       // ✅ 标准MyBatisPlus参数
  approvalType?: string;
  orderNo?: string;
  submitterName?: string;
  submitTimeStart?: string;
  submitTimeEnd?: string;
  approvalResult?: string;
  dealerId?: string;
  salesConsultantId?: string; // 销售顾问ID
  status?: 'pending' | 'approved';
}

// 审批列表项接口
export interface OrderApprovalListItem {
  id: string;
  approvalNo: string;           // 审批单号
  approvalType: ApprovalType;   // 审批类型
  orderNo: string;              // 订单编号
  submitterName: string;        // 提交人
  submitTime: string;           // 提交时间
  reason: string;               // 申请原因
  approvalResult?: ApprovalResult; // 审批结果（已审批时有值）
  approvalTime?: string;        // 审批时间（已审批时有值）
  approvalUserName?: string;    // 审批人（已审批时有值）
  dealerId: string;             // 门店ID
  comments?: string;           // 审批意见/备注
  salesConsultantId?: string; // 销售顾问ID
  salesConsultantName?: string; // 销售顾问姓名
}

// 标准MyBatisPlus分页响应
export interface OrderApprovalPageResponse {
  records: OrderApprovalListItem[];  // ✅ 标准MyBatisPlus响应
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

// 审批操作请求
export interface ApprovalActionRequest {
  approvalId: string;
  action: 'approve' | 'reject';
  comments?: string;
}

// 审批详情
export interface ApprovalDetail extends OrderApprovalListItem {
  orderDetails?: any;           // 订单详情信息
  approvalHistory?: ApprovalHistoryItem[];  // 审批历史
}

// 审批历史项
export interface ApprovalHistoryItem {
  id: string;
  action: string;
  actionTime: string;
  operator: string;
  operatorRole: string;
  comments?: string;
  status: string;
}

// 导出参数
export interface ExportApprovalParams {
  approvalType?: string;
  orderNo?: string;
  submitterName?: string;
  submitTimeStart?: string;
  submitTimeEnd?: string;
  approvalResult?: string;
  dealerId?: string;
  status?: 'pending' | 'approved';
  exportFormat?: 'excel' | 'csv';
}

// 门店信息
export interface Store {
  id: string;
  name: string;
  code?: string;
}
