// 试驾报表相关类型定义

export interface TestDriveItem {
  id?: string;
  testDriveNo: string;
  customerName: string;
  customerPhone: string;
  model: string;
  variant: string;
  startTime: string;
  endTime: string;
  mileage: number;
  consultantName: string;
  storeName: string;
  createTime: string;
  editable?: boolean;

  // 详情字段
  customerId?: number;
  customerIdType?: string;
  customerIdNumber?: string;
  driverName?: string;
  driverPhone?: string;
  driverIdType?: string;
  driverIdNumber?: string;
  source?: string;
  email?: string;
  driverLicenseNumber?: string;
  startMileage?: number;
  endMileage?: number;
  feedback?: string;
  consultantId?: number;
  storeId?: number;
  storeRegion?: string;
  updateTime?: string;
  
  // 后端可能返回的其他字段名
  idType?: string;
  idNumber?: string;
  sourceChannel?: string;
}

export interface TestDriveSearchParams {
  pageNum?: number;           // MyBatisPlus标准分页参数
  pageSize?: number;          // MyBatisPlus标准分页参数
  model?: string;
  variant?: number;
  storeIds?: number[];
  startTimeBegin?: string;
  startTimeEnd?: string;
}

export interface TestDrivePageResponse {
  result: {
    records: TestDriveItem[];  // MyBatisPlus标准响应结构
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
  };
}

// 试驾统计数据类型
export interface TestDriveStats {
  monthlyCount: number;
  dailyCount: number;
  topStore: string;
  topModel: string;
}

// 车型配置相关类型
export interface VehicleVariant {
  variant: string;
  variantId: number;
}

export interface VehicleModel {
  model: string;
  modelId: string;
  variants: VehicleVariant[];
}

// 门店信息类型
export interface StoreInfo {
  storeName: string;
  storeId: number;
  storeRegion?: string;
}

// 导出表单类型
export interface ExportForm {
  format: 'excel' | 'csv';
  scope: 'current' | 'all';
}
