// 发票管理相关类型定义

// 发票基本信息接口
export interface Invoice {
  id: number;
  invoiceNumber: string; // 发票编号 522102-XXXXXXX
  invoiceDate: string; // 开票日期
  orderNumber: string; // 订单编号
  customerName: string; // 购车人姓名
  customerPhone: string; // 购车人手机号
  customerEmail: string; // 购车人邮箱
  customerAddress: string; // 购车人地址
  customerState: string; // 州
  customerCity: string; // 城市
  customerPostcode: string; // 邮编
  vin: string; // VIN码
  model: string; // 车型
  variant: string; // 变型
  color: string; // 颜色
  salesStore: string; // 销售门店
  salesConsultant: string; // 销售顾问
  paymentMethod: string; // 付款方式 (PHP/现金/贷款)
  financeCompany: string; // 金融公司
  loanAmount: number; // 贷款金额
  invoiceAmount: number; // 发票总额
  createdTime: string; // 创建时间
  status: string; // 发票状态
}

// 发票详细信息接口
export interface InvoiceDetail extends Invoice {
  // 发票基本信息
  companyName: string; // 公司名称
  companyAddress: string; // 公司地址
  gstNumber: string; // GST编号
  sstNumber: string; // SST编号
  contactPhone: string; // 联系电话
  contactEmail: string; // 联系邮箱

  // 客户信息 (从 Invoice 继承，但确保包含所有，特别是新增的)
  deliveryNumber: string; // 交车单编号
  salesConsultantId: string; // 销售顾问ID

  // 车辆详细信息
  tinNumber: string; // TIN编号
  modelCode: string; // 车型代码
  modelDescription: string; // 车型描述
  engineNumber: string; // 发动机号
  chassisNumber: string; // 底盘号
  engineCapacity: string; // 发动机排量
  fuelType: string; // 燃料类型
  transmission: string; // 变速箱
  year: string; // 年款
  vehicleRegistrationDate: string; // 车辆登记日期
  creator: string; // 创建人
  updater: string; // 更新人
  updateTime: string; // 更新时间

  // 金融信息详情
  financeType: string; // 金融方式
  loanTerm: number; // 贷款期限
  interestRate: number; // 利率
  monthlyPayment: number; // 月供

  // 保险信息详情
  insuranceCompany: string; // 保险公司
  agentCode: string; // 代理编码
  policyNumber: string; // 保单号
  policyDate: string; // 出单日期
  insuranceAmount: number; // 保险费用 (原有)

  // 价格结构明细
  vehiclePrice: number; // 车辆销售价
  licensePlateFee: number; // 车牌费用
  accessories: AccessoryItem[]; // 选配件明细
  totalAccessoryAmount: number; // 选配件总金额
  subtotal: number; // 小计
  otrFees: OTRFeeItem[]; // OTR费用明细
  totalOtrFeeAmount: number; // OTR费用总金额
  insurancePremium: number; // 保险费 (新增，区别于保险信息中的 insuranceAmount)
  totalSalesPrice: number; // 销售总价
  adjustmentAmount: number; // 调整金额
  invoiceNetValue: number; // 发票净值

  // 收据明细信息
  receipts: ReceiptItem[]; // 收据明细
}

// 选配件明细项
export interface AccessoryItem {
  id?: number;
  category: string; // 类别 (Exterior/Interior)
  name: string; // 配件名称
  unitPrice: number; // 单价
  quantity: number; // 数量
  totalPrice: number; // 总价
}

// OTR费用明细项
export interface OTRFeeItem {
  id?: number;
  billNumber: string; // 票据单号
  feeItem: string; // 费用项目
  price: number; // 金额
  effectiveDate: string; // 生效日期
  expiryDate: string; // 到期日期
}

// 收据明细项
export interface ReceiptItem {
  id?: number;
  receiptNumber: string; // 收据编号
  businessType: string; // 业务类型
  serialNumber: string; // 流水号
  channel: string; // 渠道
  amount: number; // 金额
  collectionType: string; // 收款类型
  arrivalTime: string; // 到账时间
  remark: string; // 备注
}

// 发票查询参数接口
export interface InvoiceSearchParams {
  invoiceNumber?: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  orderNumber?: string;
  vin?: string;
  salesType?: string;
  salesStore?: string;
  salesConsultant?: string;
  invoiceDateStart?: string;
  invoiceDateEnd?: string;
  pageNum: number;
  pageSize: number;
}

// 发票分页响应接口
export interface InvoicePageResponse {
  list: Invoice[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

// 操作日志接口
export interface InvoiceOperationLog {
  id: number; // 日志ID
  operationType: string; // 操作类型
  operator: string; // 操作人
  operationTime: string; // 操作时间
  operationDescription: string; // 操作描述
  operationResult: string; // 操作结果
  errorMessage: string; // 错误信息
}

// 操作日志查询参数
export interface InvoiceLogSearchParams {
  invoiceId: number;
  pageNum: number;
  pageSize: number;
}

// 邮件发送参数接口
export interface InvoiceEmailParams {
  invoiceId: number;
  invoiceNumber: string;
  customerName: string;
  customerEmail: string;
  invoiceAmount: number;
}

// 批量打印参数接口
export interface BatchPrintParams {
  invoiceIds: number[];
}

// 数据导出参数接口
export interface ExportParams {
  format: 'excel' | 'pdf' | 'csv'; // 导出格式
  scope: 'current' | 'all' | 'filtered'; // 导出范围
  searchParams?: InvoiceSearchParams; // 筛选条件
}

// 下拉选项接口
export interface SelectOption {
  label: string;
  value: string;
}

// 门店选项接口
export interface StoreOption extends SelectOption {
  consultants: ConsultantOption[]; // 该门店的销售顾问列表
}

// 销售顾问选项接口
export interface ConsultantOption extends SelectOption {
  storeId: string; // 所属门店ID
}

// 统一响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页响应接口
export interface PaginationResponse<T = any> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}
