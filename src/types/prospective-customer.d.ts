// 潜客管理相关类型定义

// 基础分页接口
export interface PaginationResponse<T> {
  records: T[]
  total: number
  page: number
  pageSize: number
}

// 枚举定义 - 现在使用字典编码，保留类型定义用于向后兼容
export enum SourceChannel {
  SUPER_APP = '01060001',
  STORE_CREATED = '01060002'
}

export enum ProspectLevel {
  H = '01160001',
  A = '01160002',
  B = '01160003',
  C = '01160004'
}

export enum ProspectStatus {
  NEW = '01380001',
  FOLLOWING = '01380002',
  CLOSED = '01380003',
  DEFEATED = '01380004'
}

export enum IdType {
  ID_CARD = '01140001',
  PASSPORT = '01140002',
  DRIVING_LICENSE = '01140003',
  OTHER = '01140004'
}



// 潜客基础信息
export interface ProspectBaseInfo {
  id: number
  customerName: string
  customerPhone: string
  sourceChannel: string // 使用字典编码
  customerLevel: string // 使用字典编码
  customerStatus: string // 使用字典编码
  intentModel?: string
  intentVariant?: string
  intentColor?: string
  salesConsultant?: string
  currentSalesAdvisorId?: string
  currentSalesAdvisorName?: string
  lastFollowUpTime?: string
  nextFollowUpTime?: string
  createTime: string
  idType?: string // 使用字典编码
  idNumber?: string
  email?: string
  region?: string
}

// 查询潜客列表请求参数
export interface GetStoreProspectListRequest {
  page: number
  pageSize: number
  prospectId?: string
  customerName?: string
  customerPhone?: string
  sourceChannel?: string
  customerLevel?: string
  customerStatus?: string
  isToday?: boolean
  isOverdue?: boolean
}

// 线索信息
export interface ExistingLeadInfo {
  id: number
  customerName: string
  customerPhone: string
  email?: string
  sourceChannel?: string // 使用字典编码
  idType?: string // 使用字典编码
  idNumber?: string
}

// 搜索线索请求参数
export interface SearchExistingLeadRequest {
  customerName?: string
  customerPhone?: string
}

// 新增潜客请求参数
export interface AddStoreProspectRequest {
  customerName: string
  customerPhone: string
  idType: string // 使用字典编码
  idNumber: string
  email: string
  intentionLevel: string // 使用字典编码
}

// 跟进记录请求参数
export interface RecordFollowUpRequest {
  prospectId: number
  customerName: string
  customerPhone: string
  idType: string // 使用字典编码
  idNumber: string
  email: string
  region: string
  intentionModel: string
  intentionVariant: string
  intentionColor: string
  followUpMethod: string // 使用字典编码
  followUpTime: Date | string | null
  intentionLevel: string // 使用字典编码
  nextFollowUpTime: Date | string | null
  followUpDetails: string
}
