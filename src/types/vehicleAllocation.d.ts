// 车辆配车管理相关类型定义

// 分页响应通用接口
export interface PaginationResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 分页查询通用参数
export interface PaginationParams {
  page: number;
  pageSize: number;
}

// 配车状态枚举
export type AllocationStatus = 'allocated' | 'unallocated';

// 订单状态枚举
export type OrderStatus = 'submitted' | 'confirmed' | 'cancel_review' | 'cancel_approved' | 'cancelled' | 'ready_delivery' | 'delivered';

// 库存状态枚举
export type StockStatus = 'in_stock' | 'allocated' | 'out_of_stock';

// 操作类型枚举
export type OperationType = 'allocate' | 'cancel_allocate' | 'view_detail' | 'system_auto_cancel';

// 处理结果枚举
export type ProcessResult = 'success' | 'failed';

// 车辆配车订单列表项
export interface VehicleAllocationOrderItem {
  id: number;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  store: string;
  salesConsultant: string;
  model: string;
  variant: string;
  color: string;
  factoryOrderNumber: string;
  vin: string;
  allocationStatus: AllocationStatus;
  orderStatus: OrderStatus;
  orderCreateTime: string;
  allocationTime?: string;
}

// 车辆配车订单查询参数
export interface VehicleAllocationOrderParams extends PaginationParams {
  orderNumber?: string;
  customerName?: string;
  customerPhone?: string;
  allocationStatus?: AllocationStatus;
  orderStatus?: OrderStatus;
  vin?: string;
  factoryOrderNumber?: string;
  store?: string;
  salesConsultant?: string;
  allocationTimeStart?: string;
  allocationTimeEnd?: string;
  orderCreateTimeStart?: string;
  orderCreateTimeEnd?: string;
}

// 可配车辆信息
export interface AvailableVehicle {
  id: number;
  vin: string;
  factoryOrderNumber: string;
  model: string;
  variant: string;
  color: string;
  warehouseName: string;
  stockStatus: StockStatus;
  lockStatus: boolean;
  inStockTime: string;
}

// 可配车辆查询参数（P03契约调整）
export interface AvailableVehicleParams {
  orderNumber: string; // P03契约：必需参数，用于获取订单配置要求
  vin?: string;
  factoryOrderNumber?: string;
}

// 配车确认参数
export interface AllocationConfirmParams {
  orderNumber: string;
  vin: string;
}

// 取消配车参数
export interface CancelAllocationParams {
  orderNumber: string;
  reason: string;
}

// 配车记录项
export interface AllocationRecord {
  id: number;
  recordNumber: string;
  orderNumber: string;
  customerName: string;
  vin: string;
  operationType: OperationType;
  operator: string;
  operationTime: string;
  processResult: ProcessResult;
  remarks: string;
  operationDetails: string;
}

// 配车记录查询参数
export interface AllocationRecordParams extends PaginationParams {
  orderNumber?: string;
  operationType?: OperationType;
  operator?: string; // 操作员筛选
  operationTimeStart?: string;
  operationTimeEnd?: string;
}

// 配车统计信息
export interface AllocationStatistics {
  totalAllocations: number;
  totalCancellations: number;
  successRate: number;
  averageAllocationTime: number;
}

// 订单配车详情（用于配车确认弹窗）
export interface OrderAllocationDetail {
  orderNumber: string;
  ordererName: string;
  ordererPhone: string;
  customerType: string;
  store: string;
  model: string;
  variant: string;
  color: string;
  salesConsultant: string;
  orderCreateTime: string;
  orderStatus: OrderStatus;
  allocationStatus: AllocationStatus;
}

// 单订单配车历史时间轴项
export interface OrderAllocationTimelineItem {
  id: number;
  operationType: OperationType;
  operator: string;
  operationTime: string;
  vin?: string;
  warehouseName?: string;
  processResult: ProcessResult;
  remarks: string;
  operationDetails: string;
  isSystemOperation: boolean;
}

// 导出数据参数
export interface ExportDataParams {
  searchParams: VehicleAllocationOrderParams;
  exportType: 'current_page' | 'all_results';
}

// API响应基础结构
export interface ApiResponse<T = unknown> {
  code: number;
  msg: string;
  success: boolean;
  data: T;
}

// 配车确认响应
export interface AllocationConfirmResponse {
  success: boolean;
  message: string;
  data?: {
    orderNumber: string;
    vin: string;
    allocationTime: string;
  };
}

// 取消配车响应
export interface CancelAllocationResponse {
  success: boolean;
  message: string;
  data?: {
    orderNumber: string;
    cancelTime: string;
  };
}

// 导出数据响应
export interface ExportDataResponse {
  success: boolean;
  message: string;
  downloadUrl?: string;
  fileName?: string;
}
