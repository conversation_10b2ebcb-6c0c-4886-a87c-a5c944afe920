/*
 * @Author: Your Name
 * @Date: 2024-12-01 10:00:00
 * @LastEditors: Your Name
 * @LastEditTime: 2024-12-01 10:00:00
 * @Description: 工单管理系统类型定义
 */

/**
 * 工单状态枚举
 */
export enum WorkOrderStatus {
  DRAFT = 'draft', // 草稿
  PENDING_CONFIRMATION = 'pending_confirmation', // 待客户确认
  CONFIRMED = 'confirmed', // 已确认
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
  REJECTED = 'rejected', // 客户拒绝
}

/**
 * 工单优先级枚举
 */
export enum WorkOrderPriority {
  NORMAL = 'normal', // 普通
  URGENT = 'urgent', // 紧急
}

/**
 * 客户来源枚举
 */
export enum CustomerSource {
  APPOINTMENT = 'appointment', // 预约客户
  WALK_IN = 'walk_in', // 自然到店客户
}

/**
 * 工单类型枚举
 */
export enum WorkOrderType {
  REPAIR = 'repair', // 维修
  MAINTENANCE = 'maintenance', // 保养
  CLAIM = 'claim', // 保险
}

/**
 * 付款状态枚举
 */
export enum PaymentStatus {
  UNPAID = 'unpaid', // 未支付
  PARTIALLY_PAID = 'partially_paid', // 部分支付
  PAID = 'paid', // 已支付
}

/**
 * 客户信息接口
 */
export interface CustomerInfo {
  customerId?: string;
  appointmentCustomerName?: string; // 预约客户姓名
  appointmentCustomerPhone?: string; // 预约客户手机
  repairCustomerName: string; // 送修人姓名
  repairCustomerPhone: string; // 送修人手机
}

/**
 * 车辆信息接口
 */
export interface VehicleInfo {
  vehicleId?: string;
  licensePlate: string; // 车牌号
  vin: string; // VIN号
  modelConfigColor: string; // 车型配置颜色
  remark: string; // 备注信息（来自质检单）
}

/**
 * 工时项目接口
 */
export interface LaborItem {
  laborId: string; // 工时ID
  laborCode: string; // 工时编码
  laborName: string; // 工时名称
  type: WorkOrderType; // 类型 (保养/维修/索赔)
  isClaim: boolean; // 是否索赔
  isAdded: boolean; // 是否增项
  standardHours: number; // 标准工时 (小时)
  unitPrice: number; // 工时单价 (元/小时)
  subtotal: number; // 小计
}

/**
 * 零件项目接口
 */
export interface PartsItem {
  partId: string; // 零件ID
  partName: string; // 零件名称
  isClaim: boolean; // 是否索赔
  isAdded: boolean; // 是否增项
  availableStock: number; // 可用库存
  quantity: number; // 数量
  unitPrice: number; // 零件单价 (元)
  subtotal: number; // 小计
}

/**
 * 工单总金额统计接口
 */
export interface CostSummary {
  laborCost: number; // 工时费用
  partsCost: number; // 零件费用
  totalAmount: number; // 工单总金额
}

/**
 * 操作日志接口
 */
export interface OperationLog {
  operator: string; // 操作人
  action: string; // 操作行为 (如：创建工单, 保存草稿, 推送客户)
  timestamp: string; // 操作时间
  details?: string; // 详细信息
}

/**
 * 工单详情接口
 */
export interface WorkOrder {
  workOrderId: string;
  status: WorkOrderStatus;
  priority: WorkOrderPriority;
  customerSource: CustomerSource;
  workOrderType: WorkOrderType;
  customerInfo: CustomerInfo;
  vehicleInfo: VehicleInfo;
  laborItems: LaborItem[];
  partsItems: PartsItem[];
  costSummary: CostSummary;
  paymentStatus: PaymentStatus;
  createdAt: string;
  updatedAt: string;
  serviceAdvisorId: string; // 服务顾问ID
  serviceAdvisorName: string; // 服务顾问姓名
  technicianId?: string; // 技师ID
  technicianName?: string; // 技师姓名
  operationLogs: OperationLog[];
}

/**
 * 工单列表项接口
 */
export interface WorkOrderListItem {
  work_order_id?: string;
  work_order_number?: string;
  work_order_type?: string;
  work_order_priority?: string;
  customer_source?: string;
  work_order_status?: string;
  payment_status?: string;
  qc_status?: string;
  is_claim?: boolean;
  is_outsourced?: boolean;
  has_additional_items?: boolean;
  customer_name?: string;
  sender_name: string;
  sender_phone: string;
  license_plate: string;
  model_config_color?: string;
  total_amount: number;
  service_advisor_name?: string;
  technician_name?: string;
  confirmation_time?: string;
  start_time?: string;
  end_time?: string;
  created_time: string;
  updated_time?: string;
}

/**
 * API请求参数 - 工单列表查询
 */
export interface WorkOrderListParams {
  page?: number;
  pageSize?: number;
  work_order_number?: string;
  work_order_priority?: string;
  work_order_type?: string;
  is_claim?: boolean | null;
  is_outsourced?: boolean | null;
  work_order_status?: string[];
  payment_status?: string;
  sender_name?: string;
  sender_phone?: string;
  license_plate?: string;
  service_advisor_id?: string;
  confirmation_time_start?: string;
  confirmation_time_end?: string;
}

/**
 * API请求参数 - 工单创建/编辑
 */
export interface WorkOrderCreateEditParams {
  workOrderId?: string; // 编辑时传入
  status?: WorkOrderStatus; // 草稿或待确认
  priority: WorkOrderPriority;
  customerSource: CustomerSource;
  workOrderType: WorkOrderType;
  customerInfo: CustomerInfo;
  vehicleInfo: VehicleInfo;
  laborItems: LaborItem[];
  partsItems: PartsItem[];
}

/**
 * API响应 - 工单详情
 */
export interface WorkOrderDetailsResponse {
  code: number;
  message: string;
  data: WorkOrder;
}

/**
 * API响应 - 工单列表
 */
export interface WorkOrderListResponse {
  code: number;
  message: string;
  data: {
    total: number;
    list: WorkOrderListItem[];
  };
}

/**
 * 项目选择接口
 */
export interface ProjectItem {
  projectId: string;
  projectCode: string;
  projectName: string;
  projectType: WorkOrderType; // 项目类型 (保养/维修/索赔)
  standardHours: number;
  unitPrice: number;
}

/**
 * 零件选择接口
 */
export interface PartSelectResult {
  partId: string;
  partName: string;
  partCode: string;
  unitPrice: number;
  availableStock: number;
  isClaim: boolean; // 是否索赔零件
}

/**
 * 零件选择项接口（与Mock数据字段匹配）
 */
export interface PartsSelectItem {
  parts_id: string;
  parts_code: string;
  parts_name: string;
  unit_price: number;
  available_stock: number;
  is_claim?: boolean;
}

/**
 * 用户接口 (服务顾问/技师)
 */
export interface User {
  userId: string;
  userName: string;
  role: 'serviceAdvisor' | 'technician';
}
