// 工单审批相关类型定义

// 审批类型枚举
export type ApprovalType = '索赔审批' | '取消工单审批';

// 审批状态枚举
export type ApprovalStatus = '待审核' | '已审核';

// 审批结果枚举
export type ApprovalResult = '审批通过' | '审批驳回';

// 超时状态枚举
export type OvertimeStatus = '正常' | '即将超时' | '已超时';

// 审批级别枚举
export type ApprovalLevel = '一级审批' | '二级审批';

// 用户角色枚举
export type UserRole = '技师经理' | '厂端管理人员' | '服务顾问';

// 审批申请基础信息
export interface ApprovalRequest {
  approval_id: string;
  approval_no: string;
  approval_type: ApprovalType;
  approval_status: ApprovalStatus;
  approval_result?: ApprovalResult;
  current_level: ApprovalLevel;

  // 提交信息
  submitter_id: string;
  submitter_name: string;
  submitter_role: UserRole;
  submit_time: string;

  // 工单信息
  order_id: string;
  order_no: string;

  // 客户车辆信息
  customer_id: string;
  customer_name: string;
  customer_phone: string;
  vehicle_id: string;
  license_plate: string;

  // 门店信息
  store_id: string;
  store_name: string;

  // 申请内容
  request_reason: string;
  request_description?: string;

  // 审批时间管理
  create_time: string;
  update_time: string;
  deadline_time: string;
  overtime_status: OvertimeStatus;

  // 审批备注
  approval_remark?: string;
  rejection_reason?: string;
}

// 索赔内容明细
export interface ClaimContent {
  claim_id: string;
  approval_id: string;
  claim_type: '工时费' | '零件费' | '其他费用';
  claim_item_name: string;
  claim_quantity: number;
  claim_unit_price: number;
  claim_total_amount: number;
  claim_reason: string;
  supporting_documents?: string[]; // 支撑文件URL列表
}

// 审批历史记录
export interface ApprovalHistory {
  history_id: string;
  approval_id: string;
  approver_id: string;
  approver_name: string;
  approver_role: UserRole;
  approval_level: ApprovalLevel;
  approval_result: ApprovalResult;
  approval_time: string;
  approval_remark?: string;
  rejection_reason?: string;
}

// 操作日志
export interface OperationLog {
  log_id: string;
  approval_id: string;
  operator_id: string;
  operator_name: string;
  operation_type: '提交申请' | '审批通过' | '审批驳回' | '撤回申请' | '超时提醒';
  operation_time: string;
  operation_description: string;
  operation_details?: any;
}

// 审批详情（包含所有相关信息）
export interface ApprovalDetail extends ApprovalRequest {
  claim_contents: ClaimContent[];
  approval_histories: ApprovalHistory[];
  operation_logs: OperationLog[];
}

// API请求参数类型
export interface ApprovalListParams {
  page: number;
  pageSize: number;
  approval_no?: string;
  approval_type?: ApprovalType | '';
  approval_status?: ApprovalStatus | '';
  submitter_name?: string;
  order_no?: string;
  store_id?: string;
  customer_info?: string;
  license_plate?: string;
  current_level?: ApprovalLevel | '';
  overtime_status?: OvertimeStatus | '';
  submit_time_start?: string;
  submit_time_end?: string;
}

export interface ApprovalSubmitParams {
  approval_id: string;
  approval_result: ApprovalResult;
  approval_remark?: string;
  rejection_reason?: string;
}

// API响应类型
export interface ApprovalListResponse {
  list: ApprovalRequest[];
  total: number;
  page: number;
  pageSize: number;
}

export interface ApprovalDetailResponse {
  data: ApprovalDetail;
}

export interface ApprovalSubmitResponse {
  success: boolean;
  message: string;
}

// 统计数据类型
export interface ApprovalStatistics {
  total_count: number;
  pending_count: number;
  completed_count: number;
  approved_count: number;
  rejected_count: number;
  overtime_count: number;
  today_submitted: number;
  today_processed: number;
}

// 门店信息
export interface StoreInfo {
  store_id: string;
  store_name: string;
  store_code: string;
  store_address: string;
  store_manager: string;
  store_phone: string;
}

// 用户信息
export interface UserInfo {
  user_id: string;
  user_name: string;
  user_role: UserRole;
  store_id?: string;
  store_name?: string;
  department: string;
  permissions: string[];
}
