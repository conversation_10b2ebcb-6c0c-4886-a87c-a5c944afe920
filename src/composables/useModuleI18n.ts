import { useI18n } from 'vue-i18n'

/**
 * 模块化国际化组合式 API
 * @param moduleName 模块名称
 * @returns 带模块前缀的 t 函数和其他 i18n 工具
 */
export function useModuleI18n(moduleName: string) {
  const { t: originalT, locale, ...rest } = useI18n()

  // 创建带模块前缀的 t 函数
  const t = (key: string, params?: Record<string, unknown>) => {
    const fullKey = `${moduleName}.${key}`
    return originalT(fullKey, params)
  }

  // 创建不带模块前缀的通用 t 函数（用于访问 common 模块）
  const tc = (key: string, params?: Record<string, unknown>) => {
    return originalT(`common.${key}`, params)
  }

  // 创建直接访问原始 t 函数的方法（用于特殊情况）
  const tr = (key: string, params?: Record<string, unknown>) => {
    return originalT(key, params)
  }

  return {
    t,        // 当前模块的翻译函数
    tc,       // 通用模块的翻译函数
    tr,       // 原始翻译函数
    locale,   // 当前语言
    ...rest   // 其他 i18n 功能
  }
}

/**
 * 获取特定模块的翻译函数（不使用组合式API）
 * @param moduleName 模块名称
 * @param i18nInstance i18n实例
 * @returns 翻译函数
 */
export function getModuleTranslator(moduleName: string, i18nInstance: any) {
  return (key: string, params?: Record<string, unknown>) => {
    const fullKey = `${moduleName}.${key}`
    return i18nInstance.global.t(fullKey, params)
  }
}

/**
 * 多模块国际化组合式API
 * 允许同时使用多个模块的翻译
 * @param moduleNames 模块名称数组
 * @returns 包含各模块翻译函数的对象
 */
export function useMultiModuleI18n(moduleNames: string[]) {
  const { t: originalT, locale, ...rest } = useI18n()

  const translators: Record<string, (key: string, params?: Record<string, unknown>) => string> = {}

  // 为每个模块创建翻译函数
  moduleNames.forEach(moduleName => {
    translators[moduleName] = (key: string, params?: Record<string, unknown>) => {
      const fullKey = `${moduleName}.${key}`
      return originalT(fullKey, params)
    }
  })

  // 通用翻译函数
  const tc = (key: string, params?: Record<string, unknown>) => {
    return originalT(`common.${key}`, params)
  }

  return {
    ...translators,
    tc,
    locale,
    ...rest
  }
}
