import { ref, computed, onMounted, watch } from 'vue';
import { getDictionaryList, getBatchDictionaryList } from '@/api/modules/dictionary';
import type { DictionaryItem } from '@/api/modules/dictionary';
import type { DictionaryType, DictionaryOption } from '@/constants/dictionary';
import { ElMessage } from 'element-plus';

/**
 * 单个字典类型的组合函数
 * @param dictionaryType 字典类型编码
 * @param autoLoad 是否自动加载数据，默认为 true
 * @returns 字典数据和相关方法
 */
export function useDictionary(dictionaryType: DictionaryType, autoLoad: boolean = true) {
  const loading = ref(false);
  const options = ref<DictionaryOption[]>([]);
  const error = ref<string | null>(null);

  // 获取字典数据
  const fetchDictionary = async () => {
    if (!dictionaryType) {
      console.warn('Dictionary type is required');
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      const result = await getDictionaryList(dictionaryType);
      options.value = result.map(item => ({
        code: item.code,
        name: item.name
      }));
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取字典数据失败';
      console.error(`Failed to fetch dictionary data for type ${dictionaryType}:`, err);
      ElMessage.error(`获取字典数据失败: ${error.value}`);
      options.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 刷新数据
  const refresh = () => {
    fetchDictionary();
  };

  // 根据 code 获取 name
  const getNameByCode = (code: string): string => {
    const option = options.value.find(item => item.code === code);
    return option?.name || code;
  };

  // 根据 name 获取 code
  const getCodeByName = (name: string): string => {
    const option = options.value.find(item => item.name === name);
    return option?.code || name;
  };

  // 检查是否包含指定的 code
  const hasCode = (code: string): boolean => {
    return options.value.some(item => item.code === code);
  };

  // 检查是否包含指定的 name
  const hasName = (name: string): boolean => {
    return options.value.some(item => item.name === name);
  };

  // 获取所有 code 列表
  const codes = computed(() => options.value.map(item => item.code));

  // 获取所有 name 列表
  const names = computed(() => options.value.map(item => item.name));

  // 是否有数据
  const hasData = computed(() => options.value.length > 0);

  // 自动加载数据
  if (autoLoad) {
    onMounted(() => {
      fetchDictionary();
    });
  }

  return {
    loading,
    options,
    error,
    codes,
    names,
    hasData,
    fetchDictionary,
    refresh,
    getNameByCode,
    getCodeByName,
    hasCode,
    hasName,
  };
}

/**
 * 多个字典类型的组合函数
 * @param dictionaryTypes 字典类型编码数组
 * @param autoLoad 是否自动加载数据，默认为 true
 * @returns 字典数据映射和相关方法
 */
export function useBatchDictionary(dictionaryTypes: DictionaryType[], autoLoad: boolean = true) {
  const loading = ref(false);
  const optionsMap = ref<Record<string, DictionaryOption[]>>({});
  const error = ref<string | null>(null);

  // 初始化选项映射
  dictionaryTypes.forEach(type => {
    optionsMap.value[type] = [];
  });

  // 获取批量字典数据
  const fetchBatchDictionary = async () => {
    if (!dictionaryTypes || dictionaryTypes.length === 0) {
      console.warn('Dictionary types are required');
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      const result = await getBatchDictionaryList(dictionaryTypes);
      
      // 转换数据格式
      Object.keys(result).forEach(type => {
        optionsMap.value[type] = result[type].map(item => ({
          code: item.code,
          name: item.name
        }));
      });
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取字典数据失败';
      console.error('Failed to fetch batch dictionary data:', err);
      ElMessage.error(`获取字典数据失败: ${error.value}`);
      
      // 重置所有选项
      dictionaryTypes.forEach(type => {
        optionsMap.value[type] = [];
      });
    } finally {
      loading.value = false;
    }
  };

  // 刷新数据
  const refresh = () => {
    fetchBatchDictionary();
  };

  // 获取指定类型的选项
  const getOptions = (dictionaryType: DictionaryType): DictionaryOption[] => {
    return optionsMap.value[dictionaryType] || [];
  };

  // 根据类型和 code 获取 name
  const getNameByCode = (dictionaryType: DictionaryType, code: string): string => {
    const options = getOptions(dictionaryType);
    const option = options.find(item => item.code === code);
    return option?.name || code;
  };

  // 根据类型和 name 获取 code
  const getCodeByName = (dictionaryType: DictionaryType, name: string): string => {
    const options = getOptions(dictionaryType);
    const option = options.find(item => item.name === name);
    return option?.code || name;
  };

  // 检查指定类型是否有数据
  const hasData = (dictionaryType: DictionaryType): boolean => {
    return getOptions(dictionaryType).length > 0;
  };

  // 检查所有类型是否都有数据
  const allHasData = computed(() => {
    return dictionaryTypes.every(type => hasData(type));
  });

  // 自动加载数据
  if (autoLoad) {
    onMounted(() => {
      fetchBatchDictionary();
    });
  }

  return {
    loading,
    optionsMap,
    error,
    allHasData,
    fetchBatchDictionary,
    refresh,
    getOptions,
    getNameByCode,
    getCodeByName,
    hasData,
  };
}

/**
 * 创建带有"全部"选项的字典选项
 * @param options 原始字典选项
 * @param allLabel "全部"选项的标签，默认为"全部"
 * @param allValue "全部"选项的值，默认为空字符串
 * @returns 包含"全部"选项的字典选项数组
 */
export function createOptionsWithAll(
  options: DictionaryOption[], 
  allLabel: string = '全部', 
  allValue: string = ''
): DictionaryOption[] {
  return [
    { code: allValue, name: allLabel },
    ...options
  ];
}

/**
 * 创建布尔值选项
 * @param trueLabel "是"的标签
 * @param falseLabel "否"的标签
 * @returns 布尔值选项数组
 */
export function createBooleanOptions(trueLabel: string = '是', falseLabel: string = '否'): DictionaryOption[] {
  return [
    { code: 'true', name: trueLabel },
    { code: 'false', name: falseLabel }
  ];
}

/**
 * 创建带有"全部"、"是"、"否"选项的布尔值选项
 * @param allLabel "全部"选项的标签
 * @param trueLabel "是"的标签
 * @param falseLabel "否"的标签
 * @returns 包含"全部"选项的布尔值选项数组
 */
export function createBooleanOptionsWithAll(
  allLabel: string = '全部',
  trueLabel: string = '是', 
  falseLabel: string = '否'
): DictionaryOption[] {
  return [
    { code: '', name: allLabel },
    { code: 'true', name: trueLabel },
    { code: 'false', name: falseLabel }
  ];
}
