<template>
  <div class="permission-test-container">
    <h1>权限控制测试页面</h1>

    <el-card class="test-card">
      <template #header>
        <span>用户信息</span>
      </template>
      <div class="user-info">
        <p><strong>用户名:</strong> {{ userInfo?.username }}</p>
        <p><strong>姓名:</strong> {{ userInfo?.realName }}</p>
        <p><strong>用户类型:</strong> {{ userInfo?.userType }}</p>
        <p><strong>角色:</strong> {{ userRoles.join(', ') }}</p>
        <p><strong>权限数量:</strong> {{ userPermissions.length }}</p>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>权限测试</span>
      </template>

      <h3>系统管理权限测试</h3>

      <h4>门店管理权限</h4>
      <el-button v-permission="'system:store:create'" type="primary">新增门店</el-button>
      <el-button v-permission="'system:store:update'" type="primary">编辑门店</el-button>
      <el-button v-permission="'system:store:delete'" type="danger">删除门店</el-button>

      <h4>部门管理权限</h4>
      <el-button v-permission="'system:department:create'" type="primary">新增部门</el-button>
      <el-button v-permission="'system:department:update'" type="primary">编辑部门</el-button>
      <el-button v-permission="'system:department:delete'" type="danger">删除部门</el-button>

      <h4>菜单管理权限</h4>
      <el-button v-permission="'system:menu:create'" type="primary">新增菜单</el-button>
      <el-button v-permission="'system:menu:update'" type="primary">编辑菜单</el-button>
      <el-button v-permission="'system:menu:delete'" type="danger">删除菜单</el-button>

      <h4>角色管理权限</h4>
      <el-button v-permission="'system:role:create'" type="primary">新增角色</el-button>
      <el-button v-permission="'system:role:update'" type="primary">编辑角色</el-button>
      <el-button v-permission="'system:role:delete'" type="danger">删除角色</el-button>

      <h4>用户管理权限</h4>
      <el-button v-permission="'system:user:create'" type="primary">新增用户</el-button>
      <el-button v-permission="'system:user:update'" type="primary">编辑用户</el-button>
      <el-button v-permission="'system:user:delete'" type="danger">删除用户</el-button>

      <h4>权限列表</h4>
      <el-table :data="permissionList" style="width: 100%">
        <el-table-column prop="permission" label="权限标识" />
        <el-table-column prop="hasPermission" label="是否有权限">
          <template #default="{ row }">
            <el-tag :type="row.hasPermission ? 'success' : 'danger'">
              {{ row.hasPermission ? '有权限' : '无权限' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>菜单权限测试</span>
      </template>

      <h4>菜单列表</h4>
      <el-table :data="menuList" style="width: 100%">
        <el-table-column prop="menuCode" label="菜单编码" />
        <el-table-column prop="menuName" label="菜单名称" />
        <el-table-column prop="hasMenu" label="是否有菜单权限">
          <template #default="{ row }">
            <el-tag :type="row.hasMenu ? 'success' : 'danger'">
              {{ row.hasMenu ? '有权限' : '无权限' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const userInfo = computed(() => authStore.userInfo)
const userRoles = computed(() => authStore.userRoles)
const userPermissions = computed(() => authStore.userPermissions)

// 权限测试列表
const permissionList = computed(() => {
  const permissions = [
    'system:store:create',
    'system:store:update',
    'system:store:delete',
    'system:department:create',
    'system:department:update',
    'system:department:delete',
    'system:menu:create',
    'system:menu:update',
    'system:menu:delete',
    'system:role:create',
    'system:role:update',
    'system:role:delete',
    'system:user:create',
    'system:user:update',
    'system:user:delete'
  ]

  return permissions.map(permission => ({
    permission,
    hasPermission: authStore.hasPermission(permission)
  }))
})

// 菜单测试列表
const menuList = computed(() => {
  const menus = [
    'FACTORY_SYSTEM',
    'FACTORY_USER',
    'FACTORY_ROLE',
    'FACTORY_USER_CREATE',
    'FACTORY_USER_UPDATE',
    'FACTORY_USER_DELETE',
    'FACTORY_ROLE_CREATE',
    'FACTORY_ROLE_UPDATE',
    'FACTORY_ROLE_DELETE'
  ]

  return menus.map(menuCode => ({
    menuCode,
    menuName: getMenuName(menuCode),
    hasMenu: authStore.hasMenu(menuCode)
  }))
})

const getMenuName = (menuCode: string) => {
  const menuMap: Record<string, string> = {
    'FACTORY_SYSTEM': '系统管理',
    'FACTORY_USER': '用户管理',
    'FACTORY_ROLE': '角色管理',
    'FACTORY_USER_CREATE': '新增用户',
    'FACTORY_USER_UPDATE': '编辑用户',
    'FACTORY_USER_DELETE': '删除用户',
    'FACTORY_ROLE_CREATE': '新增角色',
    'FACTORY_ROLE_UPDATE': '编辑角色',
    'FACTORY_ROLE_DELETE': '删除角色'
  }
  return menuMap[menuCode] || menuCode
}

onMounted(() => {
  console.log('权限测试页面加载完成')
  console.log('用户信息:', userInfo.value)
  console.log('用户权限:', userPermissions.value)
  console.log('用户角色:', userRoles.value)
})
</script>

<style scoped>
.permission-test-container {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.user-info p {
  margin: 8px 0;
}

h3, h4 {
  margin: 20px 0 10px 0;
  color: #303133;
}

.el-button {
  margin: 5px;
}
</style>
