<template>
  <div class="user-info-test">
    <h2>用户信息存储测试</h2>

    <el-card class="info-card">
      <template #header>
        <span>当前用户信息</span>
      </template>

      <div v-if="authStore.userInfo">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ authStore.userInfo.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ authStore.userInfo.username }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ authStore.userInfo.realName }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ authStore.userInfo.email || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ authStore.userInfo.phone || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="头像">{{ authStore.userInfo.avatar || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="角色" :span="2">
            <el-tag v-for="role in authStore.userInfo.roles" :key="role" class="mr-2">
              {{ role }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="权限" :span="2">
            <el-tag v-for="permission in authStore.userInfo.permissions" :key="permission" type="success" class="mr-2">
              {{ permission }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div v-else>
        <el-empty description="未登录或用户信息为空" />
      </div>
    </el-card>

        <el-card class="storage-card">
      <template #header>
        <span>本地存储信息</span>
      </template>

      <el-descriptions :column="1" border>
        <el-descriptions-item label="Token">
          <el-input v-model="localStorageInfo.token" readonly />
        </el-descriptions-item>
        <el-descriptions-item label="Refresh Token">
          <el-input v-model="localStorageInfo.refreshToken" readonly />
        </el-descriptions-item>
        <el-descriptions-item label="用户信息">
          <el-input v-model="localStorageInfo.userInfo" type="textarea" :rows="5" readonly />
        </el-descriptions-item>
        <el-descriptions-item label="存储状态摘要">
          <el-input v-model="localStorageInfo.summary" type="textarea" :rows="3" readonly />
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="actions-card">
      <template #header>
        <span>操作</span>
      </template>

      <el-space>
        <el-button type="primary" @click="refreshUserInfo">刷新用户信息</el-button>
        <el-button type="success" @click="updateLocalStorageInfo">更新本地存储信息</el-button>
        <el-button type="warning" @click="clearUserInfo">清除用户信息</el-button>
        <el-button type="danger" @click="logout">退出登录</el-button>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import UserStorage from '@/utils/user-storage'

const authStore = useAuthStore()

// 本地存储信息
const localStorageInfo = reactive({
  token: '',
  refreshToken: '',
  userInfo: '',
  summary: ''
})

// 更新本地存储信息显示
const updateLocalStorageInfo = () => {
  localStorageInfo.token = UserStorage.getToken() || ''
  localStorageInfo.refreshToken = UserStorage.getRefreshToken() || ''
  localStorageInfo.userInfo = JSON.stringify(UserStorage.getUserInfo(), null, 2) || ''
  localStorageInfo.summary = JSON.stringify(UserStorage.getStorageSummary(), null, 2)
}

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    await authStore.getUserInfo()
    updateLocalStorageInfo()
    ElMessage.success('用户信息刷新成功')
  } catch {
    ElMessage.error('用户信息刷新失败')
  }
}

// 清除用户信息
const clearUserInfo = () => {
  authStore.clearToken()
  updateLocalStorageInfo()
  ElMessage.success('用户信息已清除')
}

// 退出登录
const logout = async () => {
  try {
    await authStore.logout()
    updateLocalStorageInfo()
    ElMessage.success('退出登录成功')
  } catch {
    ElMessage.error('退出登录失败')
  }
}

onMounted(() => {
  updateLocalStorageInfo()
})
</script>

<style scoped>
.user-info-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.info-card,
.storage-card,
.actions-card {
  margin-bottom: 20px;
}

.mr-2 {
  margin-right: 8px;
}
</style>
