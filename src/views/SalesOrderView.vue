<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('salesOrderList') }}</h1>

    <!-- 筛选区域   -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" label-position="top" @submit.prevent="handleSearch">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('buyerName')">
              <el-input v-model="searchParams.buyerName" :placeholder="t('buyerNamePlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('buyerPhone')">
              <el-input v-model="searchParams.customerPhone" :placeholder="t('buyerPhonePlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('buyerType')">
              <el-select v-model="searchParams.buyerType" :placeholder="t('buyerTypePlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in buyerTypeOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('model')">
              <el-select v-model="searchParams.model" :placeholder="t('modelPlaceholder')" @change="handleModelChange" clearable :loading="masterDataLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="model in vehicleModelOptions"
                  :key="model.id"
                  :value="model.code"
                  :label="model.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input v-model="searchParams.orderNumber" :placeholder="t('orderNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderStatus')">
              <el-select v-model="searchParams.orderStatus" :placeholder="t('orderStatusPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in orderStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('approvalStatus')">
              <el-select v-model="searchParams.approvalStatus" :placeholder="t('approvalStatusPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in approvalStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('paymentStatus')">
              <el-select v-model="searchParams.paymentStatus" :placeholder="t('paymentStatusPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in paymentStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('insuranceStatus')">
              <el-select v-model="searchParams.insuranceStatus" :placeholder="t('insuranceStatusPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in insuranceStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('loanApprovalStatus')">
              <el-select v-model="searchParams.loanApprovalStatus" :placeholder="t('loanApprovalStatusPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in approvalStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('jpjRegistrationStatus')">
              <el-select v-model="searchParams.jpjRegistrationStatus" :placeholder="t('jpjRegistrationStatusPlaceholder')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('jpjRegistrationStatuses.pending_registration')" value="pending_registration" />
                <el-option :label="t('jpjRegistrationStatuses.registering')" value="registering" />
                <el-option :label="t('jpjRegistrationStatuses.registered')" value="registered" />
                <el-option :label="t('jpjRegistrationStatuses.registration_failed')" value="registration_failed" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('createTime')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('ordererName')">
              <el-input v-model="extraSearchParams.ordererName" :placeholder="t('ordererNamePlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('ordererPhone')">
              <el-input v-model="extraSearchParams.ordererPhone" :placeholder="t('ordererPhonePlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="exportData" :loading="exportLoading">{{ tc('export') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 订单列表区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" style="width: 100%" stripe>
        <el-table-column type="index" :label="tc('index')" width="60" />
        <el-table-column prop="orderNo" :label="t('orderNumber')" width="180" />
        <el-table-column prop="createTime" :label="t('createTime')" width="180" sortable />
        <el-table-column prop="customerName" :label="t('ordererName')" width="120" />
        <el-table-column prop="customerPhone" :label="t('ordererPhone')" width="150" />
        <el-table-column prop="customerName" :label="t('buyerName')" width="120" />
        <el-table-column prop="customerPhone" :label="t('buyerPhone')" width="150" />
        <el-table-column prop="customerType" :label="t('buyerType')" width="120">
          <template #default="{ row }">
            {{ formatBuyerType(row.customerType) }}
          </template>
        </el-table-column>
        <el-table-column prop="model" :label="t('model')" width="100" />
        <el-table-column prop="variant" :label="t('variant')" width="100" />
        <el-table-column prop="color" :label="t('color')" width="100" />
        <el-table-column prop="vin" :label="t('vin')" width="150" />
        <el-table-column prop="paymentMethod" :label="t('paymentMethod')" width="100">
          <template #default="{ row }">
            <el-tag :type="row.paymentMethod === 'full_payment' ? 'success' : 'info'">
              {{ formatPaymentMethod(row.paymentMethod) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="loanStatus" :label="t('totalPrice')" min-width="150">
          <template #default="{ row }">
            <el-tag :type="getApprovalStatusType(row.loanStatus)">
              {{ formatApprovalStatus(row.loanStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus" :label="t('orderStatus')" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.orderStatus)">{{ formatOrderStatus(row.orderStatus) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="approvalStatus" :label="t('approvalStatus')" width="120">
          <template #default="{ row }">
            <el-tag :type="getApprovalStatusType(row.approvalStatus)">
              {{ formatApprovalStatus(row.approvalStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" :label="t('paymentStatus')" width="120">
          <template #default="{ row }">
            <el-tag :type="getPaymentStatusType(row.paymentStatus)">
              {{ formatPaymentStatus(row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="insuranceStatus" :label="t('insuranceStatus')" width="100">
          <template #default="{ row }">
            <el-tag :type="getInsuranceStatusType(row.insuranceStatus)">
              {{ formatInsuranceStatus(row.insuranceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="jpjRegistrationStatus" :label="t('jpjRegistrationStatus')" width="120">
          <template #default="{ row }">
            <el-tag :type="getJpjStatusType(row.jpjRegistrationStatus)">
              {{ formatJpjStatus(row.jpjRegistrationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" :label="t('orderAmount')" width="120">
          <template #default="{ row }">
             {{ row.totalInvoicePrice?.toFixed(2) || '0.00' }}
          </template>
        </el-table-column>
        <el-table-column :label="tc('actions')" width="150" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" @click="goToDetail(row.orderNo)">{{ tc('viewDetail') }}</el-button>
            <el-button link type="primary" :icon="Edit" @click="goToEdit(row.orderNo)">{{ tc('edit') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && tableData.length === 0" class="empty-state">
        <p>{{ tc('noData') }}</p>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import {
  ElButton,
  ElCard,
  ElCol,
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElPagination,
  ElRow,
  ElSelect,
  ElTable,
  ElTableColumn,
  ElTag,
} from 'element-plus';
import { Search, Edit } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type {
  SalesOrderListItem,
  SalesOrderListParams,
  PaginationResponse
} from '@/types/module.d';
import { getSalesOrderList, exportSalesOrderData } from '@/api/modules/order';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { getVehicleModelList } from '@/api/modules/masterData';

const router = useRouter();
const { t, tc } = useModuleI18n('sales.salesOrderManagement');

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.BUYER_TYPE,
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS
]);

// 车型数据 (主数据)
const vehicleModelOptions = ref([]);
const masterDataLoading = ref(false);

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const tableData = ref<SalesOrderListItem[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);

// 日期范围选择器绑定的值
const dateRange = ref<string[]>([]);

// 搜索参数
const searchParams = reactive<SalesOrderListParams>({
  page: 1,
  pageSize: 20,
  orderNumber: '',
  buyerName: '',
  buyerPhone: '',
  customerPhone: '',
  buyerType: undefined,
  model: '',
  orderStatus: undefined,
  approvalStatus: undefined,
  paymentStatus: undefined,
  insuranceStatus: undefined,
  loanApprovalStatus: undefined,
  jpjRegistrationStatus: undefined,
  createTimeStart: '',
  createTimeEnd: ''
});

// 额外的搜索参数（不在API接口中）
const extraSearchParams = reactive({
  ordererName: '',
  ordererPhone: ''
});

// 获取字典选项的计算属性
const buyerTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.BUYER_TYPE));
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS));
const approvalStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_STATUS));
const paymentStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PAYMENT_STATUS));
const insuranceStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.INSURANCE_STATUS));

// 加载车型数据
const loadVehicleModels = async () => {
  masterDataLoading.value = true;
  try {
    vehicleModelOptions.value = await getVehicleModelList();
  } catch (error) {
    console.error('获取车型数据失败:', error);
    ElMessage.error('获取车型数据失败');
  } finally {
    masterDataLoading.value = false;
  }
};

// 获取订单列表数据
const fetchOrderList = async () => {
  loading.value = true;
  try {
    // 设置日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      searchParams.createTimeStart = dateRange.value[0];
      searchParams.createTimeEnd = dateRange.value[1];
    } else {
      searchParams.createTimeStart = '';
      searchParams.createTimeEnd = '';
    }

    // 设置分页参数
    searchParams.page = currentPage.value;
    searchParams.pageSize = pageSize.value;

    const response: PaginationResponse<SalesOrderListItem> = await getSalesOrderList(searchParams);
    tableData.value = response.list;
    total.value = response.total;
  } catch (error) {
    console.error('获取订单列表失败:', error);
    ElMessage.error(tc('operationFailed'));
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理查询按钮点击
const handleSearch = () => {
  currentPage.value = 1; // 搜索时重置回第一页
  fetchOrderList();
};

// 处理重置按钮点击
const resetSearch = () => {
  // 重置搜索参数
  Object.assign(searchParams, {
    orderNumber: '',
    buyerName: '',
    buyerPhone: '',
    customerPhone: '',
    buyerType: undefined,
    model: '',
    orderStatus: undefined,
    approvalStatus: undefined,
    paymentStatus: undefined,
    insuranceStatus: undefined,
    loanApprovalStatus: undefined,
    jpjRegistrationStatus: undefined,
    createTimeStart: '',
    createTimeEnd: ''
  });
  // 重置额外搜索参数
  Object.assign(extraSearchParams, {
    ordererName: '',
    ordererPhone: ''
  });
  dateRange.value = [];
  currentPage.value = 1;
  fetchOrderList();
};

// 处理分页页码改变
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchOrderList();
};

// 处理分页每页数量改变
const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1; // 改变每页数量时重置回第一页
  fetchOrderList();
};

// 处理导出按钮点击
const exportData = async () => {
  if (tableData.value.length === 0) {
    ElMessage.warning(tc('noDataToExport'));
    return;
  }

  exportLoading.value = true;
  try {
    const blob = await exportSalesOrderData(searchParams);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `sales_orders_${new Date().getTime()}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
    ElMessage.success(tc('exportSuccess'));
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(tc('exportFailed'));
  } finally {
    exportLoading.value = false;
  }
};

// 跳转到订单详情页
const goToDetail = (orderNumber: string) => {
  router.push({ name: 'order-detail', params: { orderNo: orderNumber } });
};

// 跳转到订单编辑页
const goToEdit = (orderNumber: string) => {
  router.push({ name: 'sales-order-edit', params: { orderNo: orderNumber } });
};

// 处理车型选择变化
const handleModelChange = () => {
  // 清空相关筛选条件
};

// 格式化显示方法 - 使用字典接口
const formatBuyerType = (type: string) => getNameByCode(DICTIONARY_TYPES.BUYER_TYPE, type) || type;
const formatOrderStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;
const formatApprovalStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.APPROVAL_STATUS, status) || status;
const formatPaymentStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, status) || status;
const formatInsuranceStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.INSURANCE_STATUS, status) || status;

// 保留的格式化方法 (非字典数据)
const formatPaymentMethod = (method: string) => {
  const methodMap: Record<string, string> = {
    'full_payment': t('paymentMethods.full_payment'),
    'installment': t('paymentMethods.installment'),
  };
  return methodMap[method] || method;
};

// JPJ注册状态格式化 (暂时保持硬编码，待后续添加字典类型)
const formatJpjStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending_registration': t('jpjRegistrationStatuses.pending_registration'),
    'registering': t('jpjRegistrationStatuses.registering'),
    'registered': t('jpjRegistrationStatuses.registered'),
    'registration_failed': t('jpjRegistrationStatuses.registration_failed'),
  };
  return statusMap[status] || status;
};

// 状态样式方法
const getStatusTag = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    'submitted': 'info',
    'confirmed': 'success',
    'pending_delivery': 'warning',
    'completed': 'success',
    'cancel_pending': 'warning',
    'cancel_approved': 'info',
    'canceled': 'danger',
    'delivered': 'success'
  };
  return typeMap[status] || 'info';
};

const getApprovalStatusType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    'approved': 'success',
    'pending_approval': 'warning',
    'pending_review': 'warning',
    'rejected': 'danger'
  };
  return typeMap[status] || 'info';
};

const getPaymentStatusType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    'fully_paid': 'success',
    'deposit_paid': 'success',
    'balance_paid': 'success',
    'pending_final_payment': 'warning',
    'pending_deposit': 'warning',
    'refunding': 'warning',
    'refund_completed': 'danger'
  };
  return typeMap[status] || 'info';
};

const getInsuranceStatusType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    'insured': 'success',
    'pending': 'warning',
    'not_insured': 'danger'
  };
  return typeMap[status] || 'info';
};

const getJpjStatusType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    'registered': 'success',
    'pending_registration': 'warning',
    'failed': 'danger'
  };
  return typeMap[status] || 'info';
};

// 监听日期范围变化
watch(dateRange, () => {
  if (dateRange.value && dateRange.value.length === 2) {
    searchParams.createTimeStart = dateRange.value[0];
    searchParams.createTimeEnd = dateRange.value[1];
  } else {
    searchParams.createTimeStart = '';
    searchParams.createTimeEnd = '';
  }
});

// 组件挂载时获取数据
onMounted(() => {
  fetchOrderList();
  loadVehicleModels();
});
</script>

<style scoped lang="scss">
// 导入 SASS 变量文件
@use '@/assets/styles/_variables.scss' as *;

/* 页面容器样式 */
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
}

/* 页面标题样式 */
.page-title {
  font-size: 28px;
  margin-bottom: 25px;
  text-align: center;
  color: #303133;
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 20px;

  .search-form {
    .el-form-item {
      margin-bottom: 18px;
    }
  }
}

/* 按钮列样式 */
.buttons-col {
  display: flex;
  justify-content: flex-end;
  gap: 10px;

  .el-button {
    margin-left: 10px;
  }
}

/* 表格卡片样式 */
.table-card {
  margin-top: 20px;

  :deep(.el-table) {
    .el-table__body td {
      white-space: nowrap;
    }
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
