<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('queryDimension')" required>
              <el-select v-model="searchParams.queryDimension" :placeholder="t('queryDimensionPlaceholder')">
                <el-option :label="t('byReceiptOrder')" value="byReceiptOrder" />
                <el-option :label="t('byReceiptDetail')" value="byReceiptDetail" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partName')">
              <el-select
                v-model="searchParams.partName"
                :placeholder="t('partNamePlaceholder')"
                filterable
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partNumber')">
              <el-select
                v-model="searchParams.partNumber"
                :placeholder="t('partNumberPlaceholder')"
                filterable
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('supplierName')">
              <el-select
                v-model="searchParams.supplierName"
                :placeholder="t('supplierNamePlaceholder')"
                filterable
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('requisitionNumber')">
              <el-input
                v-model="searchParams.requisitionNumber"
                :placeholder="t('requisitionNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('purchaseOrderNumber')">
              <el-input
                v-model="searchParams.purchaseOrderNumber"
                :placeholder="t('purchaseOrderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('deliveryOrderNumber')">
              <el-input
                v-model="searchParams.deliveryOrderNumber"
                :placeholder="t('deliveryOrderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('receiptOrderNumber')">
              <el-input
                v-model="searchParams.receiptOrderNumber"
                :placeholder="t('receiptOrderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="buttons-col">
              <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据展示区域 -->
    <el-card class="table-card">
      <!-- 按收货单列表 -->
      <el-table
        v-if="searchParams.queryDimension === 'byReceiptOrder'"
        :data="receiptOrderData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" :label="tc('sequence')" width="80" />
        <el-table-column :label="t('receiptOrderNumber')" prop="receiptOrderNumber" min-width="150" />
        <el-table-column :label="t('generateDate')" prop="generateDate" min-width="150" />
        <el-table-column :label="t('orderStatus')" prop="status" min-width="120">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ t(`status.${row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('partsReceipt.receiptStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="row.receiptTime ? 'success' : 'warning'">
              {{ row.receiptTime ? t('partsReceipt.received') : t('partsReceipt.notReceived') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('common.operations')" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="viewDetails(row)">
              {{ t('viewDetails') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 按收货明细列表 -->
      <el-table
        v-else
        :data="receiptDetailData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column type="index" :label="tc('sequence')" width="80" />
        <el-table-column :label="t('partName')" prop="partName" min-width="150" />
        <el-table-column :label="t('partNumber')" prop="partNumber" min-width="150" />
        <el-table-column :label="t('quantity')" prop="quantity" min-width="120" />
        <el-table-column :label="t('unit')" prop="unit" min-width="100" />
        <el-table-column :label="t('receiptTime')" prop="receiptTime" min-width="150" />
        <el-table-column :label="t('receiptOrderNumber')" prop="receiptOrderNumber" min-width="150" />
        <el-table-column :label="t('supplierName')" prop="supplierName" min-width="150" />
        <el-table-column :label="t('requisitionNumber')" prop="requisitionNumber" min-width="150" />
        <el-table-column :label="t('purchaseOrderNumber')" prop="purchaseOrderNumber" min-width="150" />
        <el-table-column :label="t('deliveryOrderNumber')" prop="deliveryOrderNumber" min-width="150" />
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 明细模态框 -->
    <el-dialog
      v-model="detailsDialogVisible"
      :title="t('details')"
      width="80%"
    >
      <el-form :model="detailsSearchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="t('partName')">
              <el-input v-model="detailsSearchParams.partName" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('partNumber')">
              <el-input v-model="detailsSearchParams.partNumber" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="t('supplierName')">
              <el-input v-model="detailsSearchParams.supplierName" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="buttons-col">
              <el-button type="primary" :icon="Search" @click="searchDetails">{{ tc('search') }}</el-button>
              <el-button @click="resetDetailsSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-table :data="detailsData" border style="width: 100%">
        <el-table-column type="index" :label="tc('sequence')" width="80" />
        <el-table-column :label="t('partName')" prop="partName" min-width="150" />
        <el-table-column :label="t('partNumber')" prop="partNumber" min-width="150" />
        <el-table-column :label="t('quantity')" prop="quantity" min-width="120" />
        <el-table-column :label="t('unit')" prop="unit" min-width="100" />
        <el-table-column :label="t('receiptTime')" prop="receiptTime" min-width="150" />
        <el-table-column :label="t('supplierName')" prop="supplierName" min-width="150" />
        <el-table-column :label="t('requisitionNumber')" prop="requisitionNumber" min-width="150" />
        <el-table-column :label="t('purchaseOrderNumber')" prop="purchaseOrderNumber" min-width="150" />
        <el-table-column :label="t('deliveryOrderNumber')" prop="deliveryOrderNumber" min-width="150" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const { t, tc } = useModuleI18n('partsReceiptQuery');

interface ReceiptOrder {
  id: string;
  receiptOrderNumber: string;
  generateDate: string;
  status: 'active' | 'invalid';
}

interface ReceiptDetail {
    partName: string;
    partNumber: string;
    quantity: number;
    unit: string;
    receiptTime: string;
    receiptOrderNumber: string;
    supplierName: string;
    requisitionNumber: string;
    purchaseOrderNumber: string;
    deliveryOrderNumber: string;
}

// 搜索参数
const searchParams = reactive({
  queryDimension: 'byReceiptOrder', // 默认按收货单查询
  partName: '',
  partNumber: '',
  supplierName: '',
  requisitionNumber: '',
  purchaseOrderNumber: '',
  deliveryOrderNumber: '',
  receiptOrderNumber: ''
});

// 明细搜索参数
const detailsSearchParams = reactive({
  partName: '',
  partNumber: '',
  supplierName: ''
});

// 加载状态
const loading = ref(false);

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 从localStorage加载收货记录
const loadReceiptRecords = () => {
  try {
    const records = JSON.parse(localStorage.getItem('receiptRecords') || '[]');
    return records;
  } catch (error) {
    console.error('加载收货记录失败:', error);
    return [];
  }
};

// 数据
const receiptOrderData = ref(loadReceiptRecords());

// 加载收货明细数据
const loadReceiptDetailData = () => {
  const records = loadReceiptRecords();
  const details: any[] = [];
  records.forEach((record: any) => {
    if (record.items && record.items.length > 0) {
      details.push(...record.items);
    }
  });
  return details;
};

const receiptDetailData = ref(loadReceiptDetailData());
const detailsData = ref([]);

// 模态框显示状态
const detailsDialogVisible = ref(false);

// 搜索方法
const handleSearch = async () => {
  if (!searchParams.queryDimension) {
    ElMessage.warning(t('queryDimensionRequired'));
    return;
  }

  loading.value = true;
  try {
    // 重新加载数据
    receiptOrderData.value = loadReceiptRecords();
    receiptDetailData.value = loadReceiptDetailData();

    // 根据查询维度设置总数
    if (searchParams.queryDimension === 'byReceiptOrder') {
      pagination.total = receiptOrderData.value.length;
    } else {
      pagination.total = receiptDetailData.value.length;
    }
    console.log('搜索参数:', searchParams);
    console.log('加载的收货记录:', receiptOrderData.value);
  } catch (error) {
    console.error('搜索失败:', error);
    ElMessage.error(tc('searchFailed'));
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const resetSearch = () => {
  searchParams.partName = '';
  searchParams.partNumber = '';
  searchParams.supplierName = '';
  searchParams.requisitionNumber = '';
  searchParams.purchaseOrderNumber = '';
  searchParams.deliveryOrderNumber = '';
  searchParams.receiptOrderNumber = '';
  // 重置后重新执行搜索，更新表格数据和总数
  handleSearch();
};

// 查看明细
const viewDetails = (row: any) => {
  detailsDialogVisible.value = true;
  // 加载对应收货单的明细数据
  const records = loadReceiptRecords();
  const record = records.find((r: any) => r.receiptOrderNumber === row.receiptOrderNumber);
  if (record && record.items) {
    detailsData.value = record.items;
  }
  console.log('查看明细:', row);
};

// 明细搜索
const searchDetails = () => {
  // TODO: 实现明细搜索逻辑
  console.log('明细搜索参数:', detailsSearchParams);
};

// 重置明细搜索
const resetDetailsSearch = () => {
  detailsSearchParams.partName = '';
  detailsSearchParams.partNumber = '';
  detailsSearchParams.supplierName = '';
};

// 分页方法
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  handleSearch();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  handleSearch();
};

// 页面加载时自动搜索
onMounted(() => {
  handleSearch();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

.buttons-col {
  text-align: right;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
