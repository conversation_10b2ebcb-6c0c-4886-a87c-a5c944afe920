<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${t('orderDetail.title')} [${t('wholeVehicleCollection.table.orderNumber')}: ${orderDetail?.orderNumber || '-'}]`"
    width="85vw"
    min-width="900px"
    max-width="1300px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading" class="dialog-content">
      <div v-if="orderDetail">
        <!-- 订单基本信息 -->
        <el-card class="mb-20 basic-info-card">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.orderInfo') }}</span>
          </template>
          <div class="order-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="copyToClipboard(orderDetail.orderNumber)">
                  <label>{{ t('orderDetail.basicInfo.orderNumber') }}:</label>
                  <span class="bold">{{ orderDetail.orderNumber }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.orderCreateTime') }}:</label>
                  <span>{{ orderDetail.orderCreateTime }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.orderStatus') }}:</label>
                  <el-tag :type="getOrderStatusTagType(orderDetail.orderStatus)">
                    {{ getOrderStatusLabel(orderDetail.orderStatus) }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.paymentStatus') }}:</label>
                  <el-tag :type="getPaymentStatusTagType(orderDetail.paymentStatus)">
                    {{ getPaymentStatusLabel(orderDetail.paymentStatus) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.paymentMethod') }}:</label>
                  <span>{{ orderDetail.paymentMethod }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.loanAmount') }}:</label>
                  <span :class="{'red-text': orderDetail.loanAmount}">{{ orderDetail.loanAmount ? formatCurrency(orderDetail.loanAmount) : '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 客户信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.buyerInfo') }} - Personal Details</span>
          </template>
          <div class="customer-info">
            <h3 class="sub-title">{{ t('orderDetail.basicInfo.ordererInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.ordererName') }}:</label>
                  <span>{{ orderDetail.ordererName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.ordererPhone && callPhone(orderDetail.ordererPhone)">
                  <label>{{ t('orderDetail.basicInfo.ordererPhone') }}:</label>
                  <span>{{ orderDetail.ordererPhone ? orderDetail.ordererPhone : '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <h3 class="sub-title">{{ t('orderDetail.basicInfo.buyerInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerName') }}:</label>
                  <span>{{ orderDetail.buyerName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.buyerPhone && callPhone(orderDetail.buyerPhone)">
                  <label>{{ t('orderDetail.basicInfo.buyerPhone') }}:</label>
                  <span>{{ orderDetail.buyerPhone ? orderDetail.buyerPhone : '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerIdType') }}:</label>
                  <span>{{ orderDetail.buyerIdType || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="toggleMaskIdNumber(orderDetail.buyerIdNumber)">
                  <label>{{ t('orderDetail.basicInfo.buyerIdNumber') }}:</label>
                  <span>{{ maskedIdNumber(orderDetail.buyerIdNumber) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.buyerEmail && sendEmail(orderDetail.buyerEmail)">
                  <label>{{ t('orderDetail.basicInfo.buyerEmail') }}:</label>
                  <span>{{ orderDetail.buyerEmail || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerState') }}:</label>
                  <span>{{ orderDetail.buyerState || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerCity') }}:</label>
                  <span>{{ orderDetail.buyerCity || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerPostcode') }}:</label>
                  <span>{{ orderDetail.buyerPostcode || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerFullAddress') }}:</label>
                  <span>{{ orderDetail.buyerAddress || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 门店与销售信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.dealerInfo') }} - Preferred Outlet & Sales Advisor</span>
          </template>
          <div class="dealer-sales-info">
            <h3 class="sub-title">{{ t('orderDetail.basicInfo.dealerInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.dealerRegion') }}:</label>
                  <span>{{ orderDetail.dealerRegion || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.dealerCity') }}:</label>
                  <span>{{ orderDetail.dealerCity || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.dealerStoreName') }}:</label>
                  <span>{{ orderDetail.dealerStoreName || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <h3 class="sub-title">{{ t('orderDetail.basicInfo.salesConsultantInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.salesConsultantName') }}:</label>
                  <span>{{ orderDetail.salesConsultantName || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 车辆信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.vehicleInfo') }}</span>
          </template>
          <div class="vehicle-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.model') }}:</label>
                  <span>{{ orderDetail.model || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.variant') }}:</label>
                  <span>{{ orderDetail.variant || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.color') }}:</label>
                  <span>
                    <span class="color-dot" :style="{ backgroundColor: orderDetail.color }"></span>
                    {{ orderDetail.color || '-' }}
                  </span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="copyToClipboard(orderDetail.vin)">
                  <label>{{ t('orderDetail.basicInfo.vin') }}:</label>
                  <span class="vin-text">{{ orderDetail.vin || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.warehouseName') }}:</label>
                  <span>{{ orderDetail.warehouseName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.productionDate') }}:</label>
                  <span>{{ orderDetail.productionDate || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" v-if="orderDetail.options && orderDetail.options.length > 0">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.options') }}:</label>
                  <ul class="option-list">
                    <li v-for="(option, index) in orderDetail.options" :key="index">
                      {{ option.name }} (RM {{ formatCurrency(option.price) }})
                    </li>
                    <li class="total-options-price">
                      <strong>{{ t('orderDetail.basicInfo.optionsTotalPrice') }}: RM {{ formatCurrency(Array.isArray(orderDetail.options) ? orderDetail.options.reduce((sum, item) => sum + item.price, 0) : 0) }}</strong>
                    </li>
                  </ul>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 价格详细信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.priceInfo') }}</span>
          </template>
          <div class="price-info">
            <el-table :data="priceTableData" :show-header="false" border style="width: 100%">
              <el-table-column prop="item" width="200" />
              <el-table-column prop="amount" align="right">
                <template #default="{ row }">
                  <span :class="row.class">{{ formatCurrency(row.amount) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="description" />
            </el-table>
          </div>
        </el-card>

        <!-- 收退款历史记录区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.paymentRecords.title') }}</span>
          </template>
          <div class="payment-history-info">
            <el-table :data="orderDetail.paymentRecords" style="width: 100%" border :header-cell-style="{ backgroundColor: '#fafafa', fontWeight: 'bold' }">
              <el-table-column type="index" :label="tc('index')" width="60" align="center" />
              <el-table-column prop="paymentRecordNumber" :label="t('orderDetail.paymentRecords.table.paymentRecordNumber')" min-width="150" />
              <el-table-column prop="businessType" :label="t('orderDetail.paymentRecords.table.businessType')" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.businessType === '收款' ? 'success' : 'danger'">
                    {{ row.businessType === '收款' ? '+' : '-' }}{{ row.businessType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="transactionNumber" :label="t('orderDetail.paymentRecords.table.transactionNumber')" min-width="150" />
              <el-table-column prop="channel" :label="t('orderDetail.paymentRecords.table.channel')" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getChannelType(row.channel)">
                    {{ row.channel }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="amount" :label="t('orderDetail.paymentRecords.table.amount')" width="120" align="right">
                <template #default="{ row }">
                  <span :class="row.businessType === '收款' ? 'green-text' : 'red-text'">
                    {{ formatCurrency(row.amount) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="paymentType" :label="t('orderDetail.paymentRecords.table.paymentType')" width="120" align="center" />
              <el-table-column prop="arrivalTime" :label="t('orderDetail.paymentRecords.table.arrivalTime')" width="150" align="center" />
              <el-table-column prop="remark" :label="t('orderDetail.paymentRecords.table.remark')" min-width="200" />
            </el-table>
          </div>
        </el-card>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button type="primary" @click="handleClose">{{ tc('close') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { OrderDetailInfo } from '@/types/finance/wholeVehicleCollection';
import { getOrderDetail } from '@/api/modules/finance/wholeVehicleCollection';

// Props
interface Props {
  visible: boolean;
  orderId: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: ''
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 国际化
const { t, tc } = useModuleI18n('finance');

// 数据字典
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS
]);

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const loading = ref(false);
const orderDetail = ref<OrderDetailInfo | null>(null);
const showIdNumber = ref(false);

// 监听orderId变化，获取订单详情
watch(
  () => props.orderId,
  (newOrderId) => {
    if (newOrderId && props.visible) {
      getOrderDetailData();
    }
  },
  { immediate: true }
);

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.orderId) {
      getOrderDetailData();
    }
  }
);

// 价格表格数据
const priceTableData = computed(() => {
  if (!orderDetail.value) return [];

  const detail = orderDetail.value;
  return [
    { item: t('orderDetail.basicInfo.salesSubtotalIncludeGASA'), amount: detail.salesSubtotal || 0, description: t('orderDetail.basicInfo.salesSubtotalIncludeGASADesc'), class: '' },
    { item: t('orderDetail.basicInfo.consumptionTax'), amount: detail.consumptionTax || 0, description: t('orderDetail.basicInfo.consumptionTaxDesc'), class: '' },
    { item: t('orderDetail.basicInfo.salesTax'), amount: detail.salesTax || 0, description: t('orderDetail.basicInfo.salesTaxDesc'), class: '' },
    { item: t('orderDetail.basicInfo.numberPlatesFee'), amount: detail.numberPlatesFee || 0, description: t('orderDetail.basicInfo.numberPlatesFeeDesc'), class: '' },
    { item: t('orderDetail.basicInfo.optionsPrice'), amount: detail.accessoriesTotalAmount || 0, description: t('orderDetail.basicInfo.optionsPriceDesc'), class: '' },
    { item: t('orderDetail.basicInfo.vehicleSalesPriceSubtotal'), amount: detail.vehicleSalesPrice || 0, description: t('orderDetail.basicInfo.vehicleSalesPriceSubtotalDesc'), class: 'bold-text' },
    { item: t('orderDetail.basicInfo.insuranceAmount'), amount: detail.insuranceAmount || 0, description: t('orderDetail.basicInfo.insuranceAmountDesc'), class: '' },
    { item: t('orderDetail.basicInfo.otrAmount'), amount: detail.otrAmount || 0, description: t('orderDetail.basicInfo.otrAmountDesc'), class: '' },
    { item: t('orderDetail.basicInfo.orderDiscountAmount'), amount: detail.discountAmount || 0, description: t('orderDetail.basicInfo.orderDiscountAmountDesc'), class: 'red-text' },
    { item: t('orderDetail.basicInfo.orderTotalAmount'), amount: detail.totalAmount || 0, description: t('orderDetail.basicInfo.orderTotalAmountDesc'), class: 'large-font bold-text' },
    { item: t('orderDetail.basicInfo.orderPaidAmount'), amount: detail.paidAmount || 0, description: t('orderDetail.basicInfo.orderPaidAmountDesc'), class: 'green-text bold-text' },
    { item: t('orderDetail.basicInfo.orderUnpaidAmount'), amount: detail.unpaidAmount || 0, description: t('orderDetail.basicInfo.orderUnpaidAmountDesc'), class: 'red-text bold-text' }
  ];
});

// 获取订单详情数据
const getOrderDetailData = async () => {
  if (!props.orderId) return;

  try {
    loading.value = true;
    const response = await getOrderDetail(props.orderId);

    if (response.code === '200' || response.code === 200) {
      orderDetail.value = response.result;
    } else {
      ElMessage.error(response.message || '获取订单详情失败');
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    ElMessage.error('获取订单详情失败');
  } finally {
    loading.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  orderDetail.value = null;
  showIdNumber.value = false;
};

// 格式化货币
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-MY', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// 获取订单状态标签
const getOrderStatusLabel = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;
};

// 获取订单状态标签类型
const getOrderStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': 'info',
    '取消审核中': 'warning',
    '取消审核通过': 'warning',
    '已取消': 'danger',
    '已确认': 'success',
    '待审核': 'warning',
    '已审核': 'success',
    '待交车': 'primary',
    '已交车': 'success'
  };
  return statusMap[status] || 'info';
};

// 获取付款状态标签
const getPaymentStatusLabel = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, status) || status;
};

// 获取付款状态标签类型
const getPaymentStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待支付定金': 'warning',
    '已支付定金': 'primary',
    '退款中': 'warning',
    '退款完成': 'info',
    '待支付尾款': 'warning',
    '已支付尾款': 'success'
  };
  return statusMap[status] || 'info';
};

// 获取渠道类型
const getChannelType = (channel: string) => {
  const channelMap: Record<string, string> = {
    '银行转账': 'primary',
    '在线支付': 'success',
    '现金': 'warning',
    '支票': 'info'
  };
  return channelMap[channel] || 'info';
};

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      ElMessage.success('已复制到剪贴板');
    });
  }
};

// 拨打电话
const callPhone = (phone: string) => {
  window.open(`tel:${phone}`);
};

// 发送邮件
const sendEmail = (email: string) => {
  window.open(`mailto:${email}`);
};

// 切换身份证号显示
const toggleMaskIdNumber = (idNumber: string) => {
  showIdNumber.value = !showIdNumber.value;
};

// 身份证号掩码
const maskedIdNumber = (idNumber: string) => {
  if (!idNumber) return '-';
  if (showIdNumber.value) return idNumber;
  return idNumber.replace(/(\d{6})\d{6}(\d{4})/, '$1******$2');
};
</script>

<style scoped>
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.mb-20 {
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.sub-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 10px;
  flex-shrink: 0;
}

.info-item span {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.clickable-info {
  cursor: pointer;
}

.clickable-info:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 2px 4px;
  margin: -2px -4px;
}

.bold {
  font-weight: 600;
}

.red-text {
  color: #f56c6c;
}

.green-text {
  color: #67c23a;
}

.bold-text {
  font-weight: 600;
}

.large-font {
  font-size: 16px;
}

.color-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
  border: 1px solid #dcdfe6;
}

.vin-text {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.option-list {
  margin: 0;
  padding-left: 20px;
}

.option-list li {
  margin-bottom: 5px;
}

.total-options-price {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

:deep(.el-card__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .cell) {
  padding: 8px 10px;
}
</style>
