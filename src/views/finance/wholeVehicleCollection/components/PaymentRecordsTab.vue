<template>
  <div class="payment-records-tab">
    <!-- 操作按钮 -->
    <div class="actions-bar">
      <el-button type="primary" @click="handleAddRecord">
        {{ $t('finance.orderDetail.paymentRecords.addRecord') }}
      </el-button>
    </div>

    <!-- 收退款记录表格 -->
    <el-table
      :data="paymentRecords"
      stripe
      border
      style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
    >
      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.paymentRecordNumber')"
        prop="paymentRecordNumber"
        width="150"
      />

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.businessType')"
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="row.businessType === '收款' ? 'success' : 'warning'">
            {{ row.businessType }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.transactionNumber')"
        prop="transactionNumber"
        width="150"
      />

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.channel')"
        prop="channel"
        width="120"
      />

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.amount')"
        width="120"
      >
        <template #default="{ row }">
          <span class="amount" :class="{ 'refund': row.businessType === '退款' }">
            RM {{ formatCurrency(row.amount) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.paymentType')"
        prop="paymentType"
        width="100"
      />

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.arrivalTime')"
        prop="arrivalTime"
        width="160"
      />

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.remark')"
        prop="remark"
        width="150"
        show-overflow-tooltip
      />

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.dataSource')"
        prop="dataSource"
        width="100"
      />

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.createTime')"
        prop="createTime"
        width="160"
      />

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.creator')"
        prop="creator"
        width="120"
      />

      <el-table-column
        :label="$t('finance.orderDetail.paymentRecords.table.actions')"
        width="100"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            v-if="row.isDeletable"
            type="danger"
            size="small"
            @click="handleDeleteRecord(row)"
          >
            {{ $t('finance.orderDetail.paymentRecords.table.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加收退款记录弹窗 -->
    <el-dialog
      v-model="addRecordVisible"
      :title="$t('finance.orderDetail.paymentRecords.addForm.title')"
      width="600px"
      :before-close="handleCloseAddDialog"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="120px"
      >
        <el-form-item
          :label="$t('finance.orderDetail.paymentRecords.addForm.businessType')"
          prop="businessType"
        >
          <el-select
            v-model="addForm.businessType"
            :placeholder="$t('finance.orderDetail.paymentRecords.addForm.businessType')"
            style="width: 100%"
          >
            <el-option label="收款" value="收款" />
            <el-option label="退款" value="退款" />
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('finance.orderDetail.paymentRecords.addForm.transactionNumber')"
          prop="transactionNumber"
        >
          <el-input
            v-model="addForm.transactionNumber"
            :placeholder="$t('finance.orderDetail.paymentRecords.addForm.transactionNumber')"
          />
        </el-form-item>

        <el-form-item
          :label="$t('finance.orderDetail.paymentRecords.addForm.channel')"
          prop="channel"
        >
          <el-select
            v-model="addForm.channel"
            :placeholder="$t('finance.orderDetail.paymentRecords.addForm.channel')"
            style="width: 100%"
          >
            <el-option label="银行转账" value="银行转账" />
            <el-option label="在线支付" value="在线支付" />
            <el-option label="现金" value="现金" />
            <el-option label="支票" value="支票" />
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('finance.orderDetail.paymentRecords.addForm.amount')"
          prop="amount"
        >
          <el-input-number
            v-model="addForm.amount"
            :placeholder="$t('finance.orderDetail.paymentRecords.addForm.amount')"
            :min="0.01"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item
          :label="$t('finance.orderDetail.paymentRecords.addForm.paymentType')"
          prop="paymentType"
        >
          <el-select
            v-model="addForm.paymentType"
            :placeholder="$t('finance.orderDetail.paymentRecords.addForm.paymentType')"
            style="width: 100%"
          >
            <el-option label="定金" value="定金" />
            <el-option label="尾款" value="尾款" />
            <el-option label="全款" value="全款" />
          </el-select>
        </el-form-item>

        <el-form-item
          :label="$t('finance.orderDetail.paymentRecords.addForm.arrivalTime')"
          prop="arrivalTime"
        >
          <el-date-picker
            v-model="addForm.arrivalTime"
            type="datetime"
            :placeholder="$t('finance.orderDetail.paymentRecords.addForm.arrivalTime')"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item
          :label="$t('finance.orderDetail.paymentRecords.addForm.remark')"
          prop="remark"
        >
          <el-input
            v-model="addForm.remark"
            type="textarea"
            :rows="3"
            :placeholder="$t('finance.orderDetail.paymentRecords.addForm.remark')"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="handleCloseAddDialog">
          {{ $t('finance.orderDetail.paymentRecords.addForm.cancelButton') }}
        </el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmitAdd">
          {{ $t('finance.orderDetail.paymentRecords.addForm.submitButton') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { useI18n } from 'vue-i18n';
import type { PaymentRecord, AddPaymentRecordForm } from '@/types/finance/wholeVehicleCollection';
import { addPaymentRecord, deletePaymentRecord } from '@/api/modules/finance/wholeVehicleCollection';

// Props
interface Props {
  orderId: string;
  paymentRecords: PaymentRecord[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  refresh: [];
}>();

// 国际化
const { t } = useI18n();

// 响应式数据
const addRecordVisible = ref(false);
const submitLoading = ref(false);
const addFormRef = ref<FormInstance>();

// 添加表单数据
const addForm = reactive<AddPaymentRecordForm>({
  businessType: '收款',
  transactionNumber: '',
  channel: '',
  amount: 0,
  paymentType: '',
  arrivalTime: '',
  remark: ''
});

// 表单验证规则
const addFormRules: FormRules = {
  businessType: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  transactionNumber: [
    { required: true, message: '请输入交易流水号', trigger: 'blur' }
  ],
  channel: [
    { required: true, message: '请选择渠道', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  paymentType: [
    { required: true, message: '请选择付款类型', trigger: 'change' }
  ],
  arrivalTime: [
    { required: true, message: '请选择到账时间', trigger: 'change' }
  ]
};

// 格式化货币
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-MY', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// 添加记录
const handleAddRecord = () => {
  addRecordVisible.value = true;
};

// 关闭添加弹窗
const handleCloseAddDialog = () => {
  addRecordVisible.value = false;
  addFormRef.value?.resetFields();
  Object.assign(addForm, {
    businessType: '收款',
    transactionNumber: '',
    channel: '',
    amount: 0,
    paymentType: '',
    arrivalTime: '',
    remark: ''
  });
};

// 提交添加
const handleSubmitAdd = async () => {
  if (!addFormRef.value) return;

  try {
    const valid = await addFormRef.value.validate();
    if (!valid) return;

    submitLoading.value = true;
    const response = await addPaymentRecord(props.orderId, addForm);

    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('finance.orderDetail.paymentRecords.messages.addSuccess'));
      handleCloseAddDialog();
      emit('refresh');
    } else {
      ElMessage.error(response.message || t('finance.orderDetail.paymentRecords.messages.addError'));
    }
  } catch (error) {
    console.error('添加收退款记录失败:', error);
    ElMessage.error(t('finance.orderDetail.paymentRecords.messages.addError'));
  } finally {
    submitLoading.value = false;
  }
};

// 删除记录
const handleDeleteRecord = async (record: PaymentRecord) => {
  try {
    await ElMessageBox.confirm(
      t('finance.orderDetail.paymentRecords.messages.confirmDelete'),
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const response = await deletePaymentRecord(record.paymentRecordId);

    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('finance.orderDetail.paymentRecords.messages.deleteSuccess'));
      emit('refresh');
    } else {
      ElMessage.error(response.message || t('finance.orderDetail.paymentRecords.messages.deleteError'));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除收退款记录失败:', error);
      ElMessage.error(t('finance.orderDetail.paymentRecords.messages.deleteError'));
    }
  }
};
</script>

<style scoped>
.payment-records-tab {
  padding: 20px 0;
}

.actions-bar {
  margin-bottom: 20px;
}

.amount {
  font-weight: 600;
  color: #67c23a;
}

.amount.refund {
  color: #f56c6c;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .cell) {
  padding: 8px 10px;
}
</style>
