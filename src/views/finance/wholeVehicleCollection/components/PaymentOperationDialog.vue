<template>
  <el-dialog
    v-model="visible"
    :title="`${t('paymentOperation.title')} [${t('wholeVehicleCollection.table.orderNumber')}: ${orderDetail?.orderNumber || '-'}]`"
    width="80vw"
    min-width="800px"
    max-width="1200px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading" class="dialog-content">
      <!-- 订单详细信息 -->
      <div v-if="orderDetail">
        <!-- 订单基本信息 -->
        <el-card class="mb-20 basic-info-card">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.orderInfo') }}</span>
          </template>
          <div class="order-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="copyToClipboard(orderDetail.orderNumber)">
                  <label>{{ t('orderDetail.basicInfo.orderNumber') }}:</label>
                  <span class="bold">{{ orderDetail.orderNumber }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.orderCreateTime') }}:</label>
                  <span>{{ orderDetail.orderCreateTime }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.orderStatus') }}:</label>
                  <el-tag :type="getOrderStatusTagType(orderDetail.orderStatus)">
                    {{ getOrderStatusLabel(orderDetail.orderStatus) }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.paymentStatus') }}:</label>
                  <el-tag :type="getPaymentStatusTagType(orderDetail.paymentStatus)">
                    {{ getPaymentStatusLabel(orderDetail.paymentStatus) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.paymentMethod') }}:</label>
                  <span>{{ orderDetail.paymentMethod }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.loanAmount') }}:</label>
                  <span :class="{'red-text': orderDetail.loanAmount}">{{ orderDetail.loanAmount ? formatCurrency(orderDetail.loanAmount) : '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 客户信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.buyerInfo') }} - Personal Details</span>
          </template>
          <div class="customer-info">
            <h3 class="sub-title">{{ t('orderDetail.basicInfo.ordererInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.ordererName') }}:</label>
                  <span>{{ orderDetail.ordererName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.ordererPhone && callPhone(orderDetail.ordererPhone)">
                  <label>{{ t('orderDetail.basicInfo.ordererPhone') }}:</label>
                  <span>{{ orderDetail.ordererPhone ? orderDetail.ordererPhone : '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <h3 class="sub-title">{{ t('orderDetail.basicInfo.buyerInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerName') }}:</label>
                  <span>{{ orderDetail.buyerName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.buyerPhone && callPhone(orderDetail.buyerPhone)">
                  <label>{{ t('orderDetail.basicInfo.buyerPhone') }}:</label>
                  <span>{{ orderDetail.buyerPhone ? orderDetail.buyerPhone : '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerIdType') }}:</label>
                  <span>{{ orderDetail.buyerIdType || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="toggleMaskIdNumber(orderDetail.buyerIdNumber)">
                  <label>{{ t('orderDetail.basicInfo.buyerIdNumber') }}:</label>
                  <span>{{ maskedIdNumber(orderDetail.buyerIdNumber) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.buyerEmail && sendEmail(orderDetail.buyerEmail)">
                  <label>{{ t('orderDetail.basicInfo.buyerEmail') }}:</label>
                  <span>{{ orderDetail.buyerEmail || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerState') }}:</label>
                  <span>{{ orderDetail.buyerState || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerCity') }}:</label>
                  <span>{{ orderDetail.buyerCity || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerPostcode') }}:</label>
                  <span>{{ orderDetail.buyerPostcode || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.buyerFullAddress') }}:</label>
                  <span>{{ orderDetail.buyerAddress || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 门店与销售信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.dealerInfo') }} - Preferred Outlet & Sales Advisor</span>
          </template>
          <div class="dealer-sales-info">
            <h3 class="sub-title">{{ t('orderDetail.basicInfo.dealerInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.dealerRegion') }}:</label>
                  <span>{{ orderDetail.dealerRegion || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.dealerCity') }}:</label>
                  <span>{{ orderDetail.dealerCity || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.dealerStoreName') }}:</label>
                  <span>{{ orderDetail.dealerStoreName || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <h3 class="sub-title">{{ t('orderDetail.basicInfo.salesConsultantInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.salesConsultantName') }}:</label>
                  <span>{{ orderDetail.salesConsultantName || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 车辆信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.vehicleInfo') }}</span>
          </template>
          <div class="vehicle-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.model') }}:</label>
                  <span>{{ orderDetail.model || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.variant') }}:</label>
                  <span>{{ orderDetail.variant || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.color') }}:</label>
                  <span>
                    <span class="color-dot" :style="{ backgroundColor: orderDetail.color }"></span>
                    {{ orderDetail.color || '-' }}
                  </span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="copyToClipboard(orderDetail.vin)">
                  <label>{{ t('orderDetail.basicInfo.vin') }}:</label>
                  <span class="vin-text">{{ orderDetail.vin || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.warehouseName') }}:</label>
                  <span>{{ orderDetail.warehouseName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderDetail.basicInfo.productionDate') }}:</label>
                  <span>{{ orderDetail.productionDate || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 价格详细信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.basicInfo.priceInfo') }}</span>
          </template>
          <div class="price-info">
            <el-table :data="priceTableData" :show-header="false" border style="width: 100%">
              <el-table-column prop="item" width="200" />
              <el-table-column prop="amount" align="right">
                <template #default="{ row }">
                  <span :class="row.class">{{ formatCurrency(row.amount) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="description" />
            </el-table>
          </div>
        </el-card>

        <!-- 添加收退款记录 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.paymentRecords.addRecord') }}</span>
          </template>
          <el-form :model="paymentForm" :rules="paymentRules" ref="paymentFormRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item :label="t('orderDetail.paymentRecords.addForm.businessType')" prop="businessType">
                  <el-select v-model="paymentForm.businessType" style="width: 100%">
                    <el-option label="收款" value="收款" />
                    <el-option label="退款" value="退款" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('orderDetail.paymentRecords.addForm.transactionNumber')" prop="transactionNumber">
                  <el-input
                    v-model="paymentForm.transactionNumber"
                    :placeholder="t('orderDetail.paymentRecords.addForm.transactionNumber')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('orderDetail.paymentRecords.addForm.channel')" prop="channel">
                  <el-select v-model="paymentForm.channel" style="width: 100%">
                    <el-option label="APP" value="APP" />
                    <el-option label="银行卡" value="银行卡" />
                    <el-option label="转账" value="转账" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('orderDetail.paymentRecords.addForm.amount')" prop="amount">
                  <el-input-number
                    v-model="paymentForm.amount"
                    :min="0.01"
                    :precision="2"
                    style="width: 100%"
                    :placeholder="t('orderDetail.paymentRecords.addForm.amount')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item :label="t('orderDetail.paymentRecords.addForm.paymentType')" prop="paymentType">
                  <el-select v-model="paymentForm.paymentType" style="width: 100%">
                    <el-option label="Book Fee" value="Book Fee" />
                    <el-option label="贷款" value="贷款" />
                    <el-option label="尾款" value="尾款" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('orderDetail.paymentRecords.addForm.arrivalTime')" prop="arrivalTime">
                  <el-date-picker
                    v-model="paymentForm.arrivalTime"
                    type="date"
                    :placeholder="t('orderDetail.paymentRecords.addForm.arrivalTime')"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('orderDetail.paymentRecords.addForm.remark')" prop="remark">
                  <el-input
                    v-model="paymentForm.remark"
                    type="textarea"
                    :rows="3"
                    :placeholder="t('orderDetail.paymentRecords.addForm.remark')"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item>
              <el-button type="primary" :icon="Plus" @click="handleAddPaymentRecord" :loading="submitLoading">
                {{ t('orderDetail.paymentRecords.addForm.submitButton') }}
              </el-button>
              <el-button :icon="Refresh" @click="resetPaymentForm">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 收退款记录列表 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('orderDetail.paymentRecords.title') }}</span>
          </template>
          <el-table
            :data="paymentRecords"
            style="width: 100%"
            border
            :header-cell-style="{ backgroundColor: '#fafafa', fontWeight: 'bold' }"
          >
            <el-table-column type="index" :label="tc('index')" width="60" align="center" />
            <el-table-column prop="recordNumber" :label="t('orderDetail.paymentRecords.table.paymentRecordNumber')" min-width="150" />
            <el-table-column prop="businessType" :label="t('orderDetail.paymentRecords.table.businessType')" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.businessType === '收款' ? 'success' : 'danger'">
                  {{ row.businessType === '收款' ? '+' : '-' }}{{ row.businessType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="transactionNumber" :label="t('orderDetail.paymentRecords.table.transactionNumber')" min-width="150" />
            <el-table-column prop="channel" :label="t('orderDetail.paymentRecords.table.channel')" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getChannelType(row.channel)">
                  {{ row.channel }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="amount" :label="t('orderDetail.paymentRecords.table.amount')" width="120" align="right">
              <template #default="{ row }">
                <span :class="{ 'text-success': row.businessType === '收款', 'text-danger': row.businessType === '退款' }">
                  {{ formatCurrency(row.amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="paymentType" :label="t('orderDetail.paymentRecords.table.paymentType')" width="120" align="center" />
            <el-table-column prop="arrivalTime" :label="t('orderDetail.paymentRecords.table.arrivalTime')" width="150" align="center" />
            <el-table-column prop="remark" :label="t('orderDetail.paymentRecords.table.remark')" min-width="200" />
            <el-table-column :label="tc('actions')" width="80" fixed="right" align="center">
              <template #default="{ row }">
                <el-button
                  v-if="row.isDeletable"
                  type="danger"
                  size="small"
                  link
                  :icon="Delete"
                  @click="handleDeleteRecord(row)"
                >
                  {{ tc('delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="visible = false">{{ tc('close') }}</el-button>
      </div>
    </template>


  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type {
  WholeVehicleCollectionItem,
  OrderDetailInfo,
  AddPaymentRecordForm,
  PaymentRecord
} from '@/types/finance/wholeVehicleCollection';
import {
  getOrderDetail,
  addPaymentRecord,
  deletePaymentRecord
} from '@/api/modules/finance/wholeVehicleCollection';

// Props
interface Props {
  visible: boolean;
  orderData: WholeVehicleCollectionItem | null;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderData: null
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  success: [];
}>();

// 国际化
const { t, tc } = useModuleI18n('finance');

// 数据字典
const { getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS
]);

// 响应式数据
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const loading = ref(false);
const submitLoading = ref(false);
const orderDetail = ref<OrderDetailInfo | null>(null);
const showIdNumber = ref(false);
const paymentFormRef = ref();
const paymentRecords = ref<PaymentRecord[]>([]);

// 收退款表单
const paymentForm = reactive<AddPaymentRecordForm>({
  businessType: '收款',
  transactionNumber: '',
  channel: '',
  amount: 0,
  paymentType: '',
  arrivalTime: '',
  remark: ''
});

// 表单验证规则
const paymentRules = {
  businessType: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  transactionNumber: [
    { required: true, message: '请输入交易流水号', trigger: 'blur' }
  ],
  channel: [
    { required: true, message: '请选择渠道', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ],
  paymentType: [
    { required: true, message: '请选择付款类型', trigger: 'change' }
  ],
  arrivalTime: [
    { required: true, message: '请选择到账时间', trigger: 'change' }
  ]
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.orderData) {
      getOrderDetailData();
    } else {
      resetData();
    }
  }
);

// 价格表格数据
const priceTableData = computed(() => {
  if (!orderDetail.value) return [];

  const detail = orderDetail.value;
  return [
    { item: t('orderDetail.basicInfo.salesSubtotalIncludeGASA'), amount: detail.salesSubtotal || 0, description: t('orderDetail.basicInfo.salesSubtotalIncludeGASADesc'), class: '' },
    { item: t('orderDetail.basicInfo.consumptionTax'), amount: detail.consumptionTax || 0, description: t('orderDetail.basicInfo.consumptionTaxDesc'), class: '' },
    { item: t('orderDetail.basicInfo.salesTax'), amount: detail.salesTax || 0, description: t('orderDetail.basicInfo.salesTaxDesc'), class: '' },
    { item: t('orderDetail.basicInfo.numberPlatesFee'), amount: detail.numberPlatesFee || 0, description: t('orderDetail.basicInfo.numberPlatesFeeDesc'), class: '' },
    { item: t('orderDetail.basicInfo.optionsPrice'), amount: detail.accessoriesTotalAmount || 0, description: t('orderDetail.basicInfo.optionsPriceDesc'), class: '' },
    { item: t('orderDetail.basicInfo.vehicleSalesPriceSubtotal'), amount: detail.vehicleSalesPrice || 0, description: t('orderDetail.basicInfo.vehicleSalesPriceSubtotalDesc'), class: 'bold-text' },
    { item: t('orderDetail.basicInfo.insuranceAmount'), amount: detail.insuranceAmount || 0, description: t('orderDetail.basicInfo.insuranceAmountDesc'), class: '' },
    { item: t('orderDetail.basicInfo.otrAmount'), amount: detail.otrAmount || 0, description: t('orderDetail.basicInfo.otrAmountDesc'), class: '' },
    { item: t('orderDetail.basicInfo.orderDiscountAmount'), amount: detail.discountAmount || 0, description: t('orderDetail.basicInfo.orderDiscountAmountDesc'), class: 'red-text' },
    { item: t('orderDetail.basicInfo.orderTotalAmount'), amount: detail.totalAmount || 0, description: t('orderDetail.basicInfo.orderTotalAmountDesc'), class: 'large-font bold-text' },
    { item: t('orderDetail.basicInfo.orderPaidAmount'), amount: detail.paidAmount || 0, description: t('orderDetail.basicInfo.orderPaidAmountDesc'), class: 'green-text bold-text' },
    { item: t('orderDetail.basicInfo.orderUnpaidAmount'), amount: detail.unpaidAmount || 0, description: t('orderDetail.basicInfo.orderUnpaidAmountDesc'), class: 'red-text bold-text' }
  ];
});

// 获取订单详情数据
const getOrderDetailData = async () => {
  if (!props.orderData?.orderId) return;

  try {
    loading.value = true;
    const response = await getOrderDetail(props.orderData.orderId);

    if (response.code === '200' || response.code === 200) {
      orderDetail.value = response.result;
      paymentRecords.value = response.result.paymentRecords || [];
    } else {
      ElMessage.error(response.message || '获取订单详情失败');
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    ElMessage.error('获取订单详情失败');
  } finally {
    loading.value = false;
  }
};

// 重置数据
const resetData = () => {
  orderDetail.value = null;
  showIdNumber.value = false;
  paymentRecords.value = [];
  resetPaymentForm();
};

// 重置收退款表单
const resetPaymentForm = () => {
  Object.assign(paymentForm, {
    businessType: '收款',
    transactionNumber: '',
    channel: '',
    amount: 0,
    paymentType: '',
    arrivalTime: '',
    remark: ''
  });
  paymentFormRef.value?.resetFields();
};

// 添加收退款记录（使用乐观更新）
const handleAddPaymentRecord = async () => {
  if (!paymentFormRef.value || !props.orderData?.orderId) return;

  try {
    const valid = await paymentFormRef.value.validate();
    if (!valid) return;

    submitLoading.value = true;

    // 1. 生成临时记录ID
    const tempId = `TEMP-${Date.now()}`;

    // 2. 创建乐观更新记录
    const optimisticRecord: PaymentRecord = {
      ...paymentForm,
      paymentRecordId: tempId,
      recordNumber: `TEMP-${tempId}`,
      orderId: props.orderData.orderId,
      dataSource: '手动录入',
      isDeletable: true,
      createTime: new Date().toISOString(),
      creator: '当前用户'
    };

    // 3. 立即将临时记录添加到列表顶部并更新金额
    paymentRecords.value.unshift(optimisticRecord);
    if (orderDetail.value) {
      if (optimisticRecord.businessType === '收款') {
        orderDetail.value.paidAmount += optimisticRecord.amount;
        orderDetail.value.unpaidAmount -= optimisticRecord.amount;
      } else { // 退款
        orderDetail.value.paidAmount -= optimisticRecord.amount;
        orderDetail.value.unpaidAmount += optimisticRecord.amount;
      }
    }

    const originalFormState = { ...paymentForm }; // 保存原始表单数据
    resetPaymentForm(); // 立即重置表单

    try {
      // 4. 在后台发送保存请求
      const response = await addPaymentRecord(props.orderData.orderId, originalFormState);

      if (response.code === '200' || response.code === 200) {
        // 5. 请求成功后，用后端返回的真实数据更新临时记录
        const index = paymentRecords.value.findIndex(r => r.recordNumber === `TEMP-${tempId}`);
        if (index !== -1) {
          paymentRecords.value[index] = response.result;
        }

        ElMessage.success(t('orderDetail.paymentRecords.messages.addSuccess'));
        emit('success'); // 通知父组件数据已更新
      } else {
        throw new Error(response.message || '添加失败');
      }
    } catch (error) {
      // 6. 请求失败，回滚UI更新
      ElMessage.error(t('orderDetail.paymentRecords.messages.addError'));

      // 从列表中移除临时记录
      paymentRecords.value = paymentRecords.value.filter(r => r.recordNumber !== `TEMP-${tempId}`);

      // 回滚金额
      if (orderDetail.value) {
        if (optimisticRecord.businessType === '收款') {
          orderDetail.value.paidAmount -= optimisticRecord.amount;
          orderDetail.value.unpaidAmount += optimisticRecord.amount;
        } else { // 退款
          orderDetail.value.paidAmount += optimisticRecord.amount;
          orderDetail.value.unpaidAmount -= optimisticRecord.amount;
        }
      }

      // 将失败的数据恢复到表单中，以便用户修改后重试
      Object.assign(paymentForm, originalFormState);

      console.error('添加收退款记录失败:', error);
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitLoading.value = false;
  }
};

// 删除收退款记录（使用乐观更新）
const handleDeleteRecord = async (record: PaymentRecord) => {
  // 检查是否可删除
  if (record.dataSource === 'APP推送') {
    ElMessage.warning('APP推送的记录不能删除');
    return;
  }

  try {
    await ElMessageBox.confirm(
      t('orderDetail.paymentRecords.messages.confirmDelete'),
      tc('confirm'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );

    // 1. 乐观删除：先从UI移除
    const index = paymentRecords.value.findIndex(r => r.paymentRecordId === record.paymentRecordId);
    if (index === -1) return;

    const deletedRecord = paymentRecords.value.splice(index, 1)[0];

    // 2. 更新金额
    if (orderDetail.value) {
      if (deletedRecord.businessType === '收款') {
        orderDetail.value.paidAmount -= deletedRecord.amount;
        orderDetail.value.unpaidAmount += deletedRecord.amount;
      } else { // 退款
        orderDetail.value.paidAmount += deletedRecord.amount;
        orderDetail.value.unpaidAmount -= deletedRecord.amount;
      }
    }

    try {
      // 3. 调用API删除
      const response = await deletePaymentRecord(record.paymentRecordId);

      if (response.code === '200' || response.code === 200) {
        ElMessage.success(t('orderDetail.paymentRecords.messages.deleteSuccess'));
        emit('success'); // 通知父组件刷新列表总览数据
      } else {
        throw new Error(response.message || '删除失败');
      }
    } catch (error) {
      // 4. 失败时回滚UI
      const index = paymentRecords.value.findIndex(r => r.paymentRecordId === record.paymentRecordId);
      if (index === -1) { // 避免重复添加
        paymentRecords.value.unshift(record);
      }

      if (orderDetail.value) {
        if (record.businessType === '收款') {
          orderDetail.value.paidAmount += record.amount;
          orderDetail.value.unpaidAmount -= record.amount;
        } else { // 退款
          orderDetail.value.paidAmount -= record.amount;
          orderDetail.value.unpaidAmount += record.amount;
        }
      }

      console.error('删除收退款记录失败:', error);
      ElMessage.error(t('orderDetail.paymentRecords.messages.deleteError'));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除确认失败:', error);
    }
  }
};

// 格式化货币
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-MY', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// 获取订单状态标签
const getOrderStatusLabel = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, status) || status;
};

// 获取订单状态标签类型
const getOrderStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': 'info',
    '取消审核中': 'warning',
    '取消审核通过': 'warning',
    '已取消': 'danger',
    '已确认': 'success',
    '待审核': 'warning',
    '已审核': 'success',
    '待交车': 'primary',
    '已交车': 'success'
  };
  return statusMap[status] || 'info';
};

// 获取付款状态标签
const getPaymentStatusLabel = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, status) || status;
};

// 获取付款状态标签类型
const getPaymentStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待支付定金': 'warning',
    '已支付定金': 'primary',
    '退款中': 'warning',
    '退款完成': 'info',
    '待支付尾款': 'warning',
    '已支付尾款': 'success'
  };
  return statusMap[status] || 'info';
};

// 获取渠道类型
const getChannelType = (channel: string) => {
  const channelMap: Record<string, string> = {
    '银行转账': 'primary',
    '在线支付': 'success',
    '现金': 'warning',
    '支票': 'info'
  };
  return channelMap[channel] || 'info';
};

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      ElMessage.success('已复制到剪贴板');
    });
  }
};

// 拨打电话
const callPhone = (phone: string) => {
  window.open(`tel:${phone}`);
};

// 发送邮件
const sendEmail = (email: string) => {
  window.open(`mailto:${email}`);
};

// 切换身份证号显示
const toggleMaskIdNumber = (idNumber: string) => {
  showIdNumber.value = !showIdNumber.value;
};

// 身份证号掩码
const maskedIdNumber = (idNumber: string) => {
  if (!idNumber) return '-';
  if (showIdNumber.value) return idNumber;
  return idNumber.replace(/(\d{6})\d{6}(\d{4})/, '$1******$2');
};
</script>

<style scoped>
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.mb-20 {
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sub-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 10px;
  flex-shrink: 0;
}

.info-item span {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.clickable-info {
  cursor: pointer;
}

.clickable-info:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 2px 4px;
  margin: -2px -4px;
}

.bold {
  font-weight: 600;
}

.red-text {
  color: #f56c6c;
}

.green-text {
  color: #67c23a;
}

.bold-text {
  font-weight: 600;
}

.large-font {
  font-size: 16px;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.color-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
  border: 1px solid #dcdfe6;
}

.vin-text {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 10px;
}

:deep(.el-card__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .cell) {
  padding: 8px 10px;
}
</style>
