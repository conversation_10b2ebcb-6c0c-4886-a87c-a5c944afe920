<template>
  <el-dialog
    v-model="visible"
    title="审批详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="approval-detail">
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="审批单号">
          {{ approvalDetail?.approvalNo || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="审批类型">
          {{ approvalDetail?.approvalType || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="提交人">
          {{ approvalDetail?.submitterName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">
          {{ approvalDetail?.submitTime || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <el-tag :type="getStatusTagType(approvalDetail?.status)">
            {{ approvalDetail?.status || '-' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审批时间">
          {{ approvalDetail?.approvalTime || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="审批内容" :column="1" border style="margin-top: 20px;">
        <el-descriptions-item label="申请原因">
          {{ approvalDetail?.requestReason || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="审批备注">
          {{ approvalDetail?.approvalRemark || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface ApprovalDetail {
  approvalNo: string
  approvalType: string
  submitterName: string
  submitTime: string
  status: string
  approvalTime?: string
  requestReason: string
  approvalRemark?: string
}

interface Props {
  visible: boolean
  approvalNo?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const approvalDetail = ref<ApprovalDetail | null>(null)

const handleClose = () => {
  emit('update:visible', false)
}

const getStatusTagType = (status?: string) => {
  const map: Record<string, string> = {
    '待审批': 'warning',
    '已审批': 'success',
    '已拒绝': 'danger'
  }
  return map[status || ''] || 'info'
}

// 监听审批单号变化，加载详情
watch(() => props.approvalNo, (newVal) => {
  if (newVal && props.visible) {
    loadApprovalDetail(newVal)
  }
})

const loadApprovalDetail = async (approvalNo: string) => {
  try {
    // 这里应该调用API获取审批详情
    // const detail = await getApprovalDetail(approvalNo)
    // approvalDetail.value = detail
    
    // 临时使用模拟数据
    approvalDetail.value = {
      approvalNo,
      approvalType: '订单审批',
      submitterName: '张三',
      submitTime: '2024-01-15 10:30:00',
      status: '已审批',
      approvalTime: '2024-01-15 14:20:00',
      requestReason: '客户要求修改订单配置',
      approvalRemark: '审批通过，同意修改'
    }
  } catch (error) {
    console.error('加载审批详情失败:', error)
  }
}
</script>

<style scoped lang="scss">
.approval-detail {
  .el-descriptions {
    margin-bottom: 20px;
  }
}
</style>
