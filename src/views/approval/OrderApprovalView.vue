<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('approval.orderApproval') }}</h1>

    <!-- 筛选条件区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('approval.approvalNo')">
              <el-input
                v-model="searchParams.approvalNo"
                :placeholder="t('approval.approvalNoPlaceholder')"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('approval.approvalType')">
              <el-select
                v-model="searchParams.approvalType"
                :placeholder="t('approval.approvalTypePlaceholder')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="type in approvalTypes"
                  :key="type.code"
                  :label="type.name"
                  :value="type.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('approval.approvalStatus')">
              <el-select
                v-model="searchParams.approvalStatus"
                :placeholder="t('approval.approvalStatusPlaceholder')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="status in approvalStatuses"
                  :key="status.code"
                  :label="status.name"
                  :value="status.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('approval.submitTimeRange')">
              <el-date-picker
                v-model="searchParams.submitTimeRange"
                type="datetimerange"
                :start-placeholder="t('common.startTime')"
                :end-placeholder="t('common.endTime')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('approval.submitter')">
              <el-input
                v-model="searchParams.submittedBy"
                :placeholder="t('approval.submitterPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('approval.orderNo')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('approval.orderNoPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="userRole === 'factory_manager'">
            <el-form-item :label="t('approval.store')">
              <el-select
                v-model="searchParams.storeId"
                :placeholder="t('approval.storePlaceholder')"
                clearable
              >
                <el-option
                  v-for="store in stores"
                  :key="store.id"
                  :label="store.name"
                  :value="store.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('approval.customerInfo')">
              <el-input
                v-model="searchParams.customerInfo"
                :placeholder="t('approval.customerInfoPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ t('common.search') }}
              </el-button>
              <el-button @click="resetSearch">
                {{ t('common.reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能操作区域 -->
    <el-card class="mb-20 operation-card">
      <div class="operation-header">
        <div class="operation-left">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane :label="t('approval.pendingApproval')" name="pending">
              <template #label>
                <span>{{ t('approval.pendingApproval') }}</span>
                <el-badge :value="pendingCount" class="ml-2" />
              </template>
            </el-tab-pane>
            <el-tab-pane :label="t('approval.approvedApproval')" name="approved">
              <template #label>
                <span>{{ t('approval.approvedApproval') }}</span>
                <el-badge :value="approvedCount" class="ml-2" />
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="operation-right">
          <el-button type="primary" :icon="Download" @click="handleExport">
            {{ t('common.export') }}
          </el-button>
          <el-button :icon="Refresh" @click="handleRefresh">
            {{ t('common.refresh') }}
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据列表区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        :row-class-name="getRowClassName"
        @row-click="handleRowClick"
      >
        <el-table-column type="index" :label="t('common.index')" width="60" />

        <el-table-column
          prop="approvalNo"
          :label="t('approval.approvalNo')"
          min-width="140"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewDetail(row)">
              {{ row.approvalNo }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          prop="approvalType"
          :label="t('approval.approvalType')"
          min-width="120"
        >
          <template #default="{ row }">
            <el-tag :type="getApprovalTypeTag(row.approvalType)">
              <el-icon class="mr-1">
                <component :is="getApprovalTypeIcon(row.approvalType)" />
              </el-icon>
              {{ t(`approval.type.${row.approvalType}`) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="submitter"
          :label="t('approval.submitter')"
          min-width="100"
        >
          <template #default="{ row }">
            <el-tooltip :content="t('approval.employeeNo') + ': ' + row.submitterNo">
              <span>{{ row.submitter }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column
          prop="submitTime"
          :label="t('approval.submitTime')"
          min-width="150"
        />

        <el-table-column
          prop="orderNo"
          :label="t('approval.orderNo')"
          min-width="140"
        >
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewOrder(row)">
              {{ row.orderNo }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          prop="requestReason"
          :label="t('approval.requestReason')"
          min-width="200"
          show-overflow-tooltip
        />

        <!-- 待审批列表特有字段 -->
        <template v-if="activeTab === 'pending'">
          <el-table-column
            prop="overtimeStatus"
            :label="t('approval.overtimeStatus')"
            min-width="120"
          >
            <template #default="{ row }">
              <el-tag :type="getOvertimeStatusTag(row.overtimeStatus)">
                <el-icon class="mr-1">
                  <component :is="getOvertimeStatusIcon(row.overtimeStatus)" />
                </el-icon>
                {{ t(`approval.overtime.${row.overtimeStatus}`) }}
              </el-tag>
            </template>
          </el-table-column>
        </template>

        <!-- 已审批列表特有字段 -->
        <template v-else>
          <el-table-column
            prop="approvalResult"
            :label="t('approval.approvalResult')"
            min-width="120"
          >
            <template #default="{ row }">
              <el-tag :type="getApprovalResultTag(row.approvalResult)">
                <el-icon class="mr-1">
                  <component :is="getApprovalResultIcon(row.approvalResult)" />
                </el-icon>
                {{ t(`approval.result.${row.approvalResult}`) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="approvalRemark"
            :label="t('approval.approvalRemark')"
            min-width="150"
            show-overflow-tooltip
          />

          <el-table-column
            prop="approvalTime"
            :label="t('approval.approvalTime')"
            min-width="150"
          />
        </template>

        <el-table-column
          :label="t('common.operations')"
          width="120"
          fixed="right"
        >
          <template #default="{ row }">
            <template v-if="activeTab === 'pending'">
              <el-button
                v-if="canApprove(row)"
                type="primary"
                :icon="Edit"
                link
                @click="handleApprove(row)"
              >
                {{ t('approval.approve') }}
              </el-button>
            </template>
            <template v-else>
              <el-button
                type="primary"
                :icon="View"
                link
                @click="handleViewDetail(row)"
              >
                {{ t('common.detail') }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控制 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 索赔审批弹窗 -->
    <ClaimApprovalDialog
      v-model="claimDialogVisible"
      :approval-data="currentApproval"
      @confirm="handleApprovalConfirm"
    />

    <!-- 取消工单审批弹窗 -->
    <CancelOrderDialog
      v-model="cancelDialogVisible"
      :approval-data="currentApproval"
      @confirm="handleApprovalConfirm"
    />

    <!-- 审批详情弹窗 -->
    <ApprovalDetailDialog
      v-model="detailDialogVisible"
      :approval-data="currentApproval"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Download,
  Refresh,
  Edit,
  View,
  Clock,
  Warning,
  CircleClose,
  Select,
  Promotion
} from '@element-plus/icons-vue'
import {
  getApprovalList,
  exportApprovalData,
  approveOrder,
  getStoreList
} from '@/api/modules/approval'
import ClaimApprovalDialog from './components/ClaimApprovalDialog.vue'
import CancelOrderDialog from './components/CancelOrderDialog.vue'
import ApprovalDetailDialog from './components/ApprovalDetailDialog.vue'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import type {
  ApprovalListParams as BaseApprovalListParams,
  ApprovalListItem,
  PaginationResponse
} from '@/types/module'

interface StoreItem {
  id: string
  name: string
  code: string
}

// 扩展搜索参数接口以兼容现有组件
interface ExtendedApprovalListParams extends BaseApprovalListParams {
  approvalNo?: string
  submitTimeRange?: [string, string]
  customerInfo?: string
}

const { t } = useI18n()

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.APPROVAL_TYPE,
  DICTIONARY_TYPES.APPROVAL_STATUS
])

// 定义审批类型标签映射
const approvalTypeTagMap: Record<string, string> = {
  cancel_order: 'warning',
  modify_info: 'primary',
  modify_color: 'primary'
};

// 定义审批类型图标映射
const approvalTypeIconMap: Record<string, any> = {
  cancel_order: CircleClose,
  modify_info: Edit,
  modify_color: Edit
};

// 定义超时状态标签映射
const overtimeStatusTagMap: Record<string, string> = {
  normal: 'success',
  warning: 'warning',
  overdue: 'danger'
};

// 定义超时状态图标映射
const overtimeStatusIconMap: Record<string, any> = {
  normal: Clock,
  warning: Warning,
  overdue: CircleClose
};

// 定义审批结果标签映射
const approvalResultTagMap: Record<string, string> = {
  approved: 'success',
  rejected: 'danger'
};

// 定义审批结果图标映射
const approvalResultIconMap: Record<string, any> = {
  approved: Select,
  rejected: CircleClose
};

// 用户角色 - 实际项目中从用户信息获取
const userRole = ref('technician_manager') // technician_manager | factory_manager | service_advisor

// 搜索参数
const searchParams = reactive<ExtendedApprovalListParams>({
  approvalNo: '',
  approvalType: '',
  approvalStatus: '',
  submitTimeRange: [],
  orderNumber: '',
  submittedBy: '',
  storeId: '',
  customerInfo: ''
})

// 获取字典选项
const approvalTypes = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_TYPE))
const approvalStatuses = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_STATUS))

// 门店选项
const stores = ref<StoreItem[]>([])

// 表格数据
const tableData = ref<ApprovalListItem[]>([])
const loading = ref(false)
const activeTab = ref('pending')

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 统计数据
const pendingCount = ref(0)
const approvedCount = ref(0)

// 弹窗控制
const claimDialogVisible = ref(false)
const cancelDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentApproval = ref<ApprovalListItem | null>(null)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchParams,
      approvalStatus: activeTab.value === 'pending' ? 'pending' : 'approved',
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize
    }

    const response = await getApprovalList(params)
    tableData.value = response.list
    pagination.total = response.total

    // 更新统计数据
    if (activeTab.value === 'pending') {
      pendingCount.value = response.total
    } else {
      approvedCount.value = response.total
    }
  } catch (error) {
    ElMessage.error(t('common.loadDataError'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchParams).forEach(key => {
    if (Array.isArray(searchParams[key])) {
      searchParams[key] = []
    } else {
      searchParams[key] = ''
    }
  })
  handleSearch()
}

// Tab切换
const handleTabChange = (tab: string) => {
  activeTab.value = tab
  pagination.currentPage = 1
  loadData()
}

// 导出数据
const handleExport = async () => {
  try {
    const params = {
      ...searchParams,
      approvalStatus: activeTab.value === 'pending' ? 'pending' : 'approved'
    }
    await exportApprovalData(params)
    ElMessage.success(t('common.exportSuccess'))
  } catch (error) {
    ElMessage.error(t('common.exportError'))
  }
}

// 刷新数据
const handleRefresh = () => {
  loadData()
}

// 行点击
const handleRowClick = (row: ApprovalListItem) => {
  // 可以在这里添加行点击逻辑
}

// 查看详情
const handleViewDetail = (row: ApprovalListItem) => {
  currentApproval.value = row
  detailDialogVisible.value = true
}

// 查看工单
const handleViewOrder = (row: ApprovalListItem) => {
  // 跳转到工单详情页面
  window.open(`/order/detail/${row.orderId}`, '_blank')
}

// 审批操作
const handleApprove = (row: ApprovalListItem) => {
  currentApproval.value = row

  if (row.approvalType === 'claim') {
    claimDialogVisible.value = true
  } else if (row.approvalType === 'cancel') {
    cancelDialogVisible.value = true
  }
}

// 审批确认
const handleApprovalConfirm = async (approvalData: any) => {
  try {
    await approveOrder(approvalData)
    ElMessage.success(t('approval.approvalSuccess'))

    // 关闭弹窗
    claimDialogVisible.value = false
    cancelDialogVisible.value = false

    // 刷新数据
    loadData()
  } catch (error) {
    ElMessage.error(t('approval.approvalError'))
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadData()
}

// 行样式
const getRowClassName = ({ row }: { row: ApprovalListItem }) => {
  if (row.overtimeStatus === 'overdue') {
    return 'overdue-row'
  }
  if (row.overtimeStatus === 'warning') {
    return 'warning-row'
  }
  return ''
}

// 判断是否可以审批
const canApprove = (row: ApprovalListItem) => {
  if (userRole.value === 'technician_manager') {
    return row.approvalType === 'cancel' ||
           (row.approvalType === 'claim' && row.currentLevel === 'first')
  }
  if (userRole.value === 'factory_manager') {
    return row.approvalType === 'claim' && row.currentLevel === 'second'
  }
  return false
}

// 获取审批类型标签
const getApprovalTypeTag = (type: keyof typeof approvalTypeTagMap) => {
  return approvalTypeTagMap[type] || 'info'
}

// 获取审批类型图标
const getApprovalTypeIcon = (type: keyof typeof approvalTypeIconMap) => {
  return approvalTypeIconMap[type] || Select
}

// 获取超时状态标签
const getOvertimeStatusTag = (status: keyof typeof overtimeStatusTagMap) => {
  return overtimeStatusTagMap[status] || 'success'
}

// 获取超时状态图标
const getOvertimeStatusIcon = (status: keyof typeof overtimeStatusIconMap) => {
  return overtimeStatusIconMap[status] || Clock
}

// 获取审批结果标签
const getApprovalResultTag = (result: keyof typeof approvalResultTagMap) => {
  return approvalResultTagMap[result] || 'info'
}

// 获取审批结果图标
const getApprovalResultIcon = (result: keyof typeof approvalResultIconMap) => {
  return approvalResultIconMap[result] || Select
}

// 加载门店数据
const loadStores = async () => {
  if (userRole.value === 'factory_manager') {
    try {
      const response = await getStoreList()
      stores.value = response.data
    } catch (error) {
      console.error('Load stores error:', error)
    }
  }
}

// 初始化
onMounted(() => {
  loadData()
  loadStores()
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card,
.operation-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .operation-right {
    .el-button {
      margin-left: 10px;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

// 超时行样式
:deep(.el-table) {
  .warning-row {
    background-color: #fdf6ec;
  }

  .overdue-row {
    background-color: #fef0f0;
  }

  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}

.ml-2 {
  margin-left: 8px;
}

.mr-1 {
  margin-right: 4px;
}
</style>
