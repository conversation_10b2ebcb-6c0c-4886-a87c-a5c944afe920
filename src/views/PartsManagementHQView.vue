<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('parts.partsManagementHQ') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('parts.approvalType')">
              <el-select v-model="searchParams.approvalType" :placeholder="t('parts.selectApprovalType')" clearable @change="handleApprovalTypeChange">
                <el-option :label="t('parts.requisitionApproval')" value="requisition"></el-option>
                <el-option :label="t('parts.damageApproval')" value="damage"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('parts.storeName')">
              <el-select v-model="searchParams.storeName" :placeholder="t('parts.storeNamePlaceholder')" :disabled="!searchParams.approvalType" clearable filterable>
                <el-option
                  v-for="item in storeNameOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('parts.partName')">
              <el-select v-model="searchParams.partName" :placeholder="t('parts.partNamePlaceholder')" :disabled="!searchParams.approvalType" clearable filterable>
                <el-option
                  v-for="item in partNameOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('parts.partNumber')">
              <el-select v-model="searchParams.partNumber" :placeholder="t('parts.partNumberPlaceholder')" :disabled="!searchParams.approvalType" clearable filterable>
                <el-option
                  v-for="item in partNumberOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('parts.documentNumber')">
              <el-input v-model="searchParams.requisitionNumber" :placeholder="t('parts.documentNumberPlaceholder')" :disabled="!searchParams.approvalType" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('parts.supplierName')">
              <el-select v-model="searchParams.supplierName" :placeholder="t('parts.supplierNamePlaceholder')" :disabled="!searchParams.approvalType" clearable filterable>
                <el-option
                  v-for="item in supplierNameOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('parts.documentDateRange')">
              <el-date-picker
                v-model="searchParams.requisitionDateRange"
                type="daterange"
                :range-separator="t('common.to')"
                :start-placeholder="t('common.startDate')"
                :end-placeholder="t('common.endDate')"
                :disabled="!searchParams.approvalType"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('parts.documentStatus')">
              <el-select v-model="searchParams.requisitionStatus" :placeholder="t('parts.selectStatus')" :disabled="!searchParams.approvalType" clearable>
                <el-option :label="t('parts.status.submitted')" value="submitted"></el-option>
                <el-option :label="t('parts.status.approved')" value="approved"></el-option>
                <el-option :label="t('parts.status.rejected')" value="rejected"></el-option>
                <el-option :label="t('parts.status.shipped')" value="shipped"></el-option>
                <el-option :label="t('parts.status.partialShipped')" value="partialShipped"></el-option>
                <el-option :label="t('parts.status.received')" value="received"></el-option>
                <el-option :label="t('parts.status.partialReceived')" value="partialReceived"></el-option>
                <el-option :label="t('parts.status.voided')" value="voided"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="buttons-col">
            <el-form-item>
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <div>
                  <el-button type="primary" :icon="Search" :disabled="!searchParams.approvalType" @click="handleSearch">{{ t('common.search') }}</el-button>
                  <el-button @click="resetSearch">{{ t('common.reset') }}</el-button>
                  <el-button type="success" :icon="Download" :disabled="!searchParams.approvalType || selectedRows.length === 0" @click="exportReport">{{ t('common.exportReport') }}</el-button>
                </div>
                <div class="approval-stats">
                  <el-tag type="warning" size="large" style="margin-right: 12px; cursor: pointer;" @click="handleRequisitionApprovalClick">
                    {{ t('parts.pendingRequisitionApproval') }}: {{ pendingRequisitionCount }}
                  </el-tag>
                  <el-tag type="danger" size="large" style="cursor: pointer;" @click="handleDamageApprovalClick">
                    {{ t('parts.pendingDamageApproval') }}: {{ pendingDamageCount }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据展示（表格）区域 -->
    <el-card class="table-card" v-loading="loading">
      <el-table
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <template #empty>
          <div class="empty-state">
            <el-empty :description="getEmptyText()">
              <template v-if="hasActiveFilters()" #default>
                <el-button type="primary" @click="clearFilters">{{ t('parts.clearFilters') }}</el-button>
              </template>
            </el-empty>
          </div>
        </template>
        <el-table-column type="selection" width="55" :selectable="() => true" :show-overflow-tooltip="false">
          <template #header>
            <!-- 隐藏表头的全选复选框 -->
          </template>
        </el-table-column>
        <el-table-column :label="t('parts.serialNumber')" type="index" min-width="80" />

        <!-- 叫料审批模式的列 -->
        <template v-if="searchParams.approvalType === 'requisition'">
          <el-table-column :label="t('parts.documentNumberHQ')" prop="requisitionNumber" min-width="150" />
          <el-table-column :label="t('parts.approvalTypeHQ')" prop="purchaseOrderNumber" min-width="150">
            <template #default="{ row }">
              <span v-if="shouldShowPurchaseOrderNumber(row.requisitionStatus) && row.purchaseOrderNumber">
                {{ t('parts.documentTypeRequisition') }}
              </span>
              <span v-else>{{ t('parts.documentTypeRequisition') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="t('parts.generateDate')" prop="requisitionDate" min-width="120" />
          <el-table-column :label="t('parts.storeName')" prop="storeName" min-width="120" />
          <el-table-column :label="t('parts.documentStatus')" prop="requisitionStatus" min-width="120">
            <template #default="{ row }">
              {{ row.requisitionStatus ? t(`parts.status.${row.requisitionStatus}`) : t('common.unknown') }}
            </template>
          </el-table-column>
        </template>

        <!-- 零件破损审批模式的列 -->
        <template v-else-if="searchParams.approvalType === 'damage'">
          <el-table-column :label="t('parts.documentNumberHQ')" prop="scrapOrderNumber" min-width="150" />
          <el-table-column :label="t('parts.approvalTypeHQ')" min-width="120">
            <template #default>
              {{ t('parts.documentTypeDamage') }}
            </template>
          </el-table-column>
          <el-table-column :label="t('parts.generateDate')" prop="scrapDate" min-width="120" />
          <el-table-column :label="t('parts.storeName')" prop="storeName" min-width="120" />
          <el-table-column :label="t('parts.documentStatus')" prop="status" min-width="120">
            <template #default="{ row }">
              {{ row.status ? t(`parts.status.${row.status}`) : t('common.unknown') }}
            </template>
          </el-table-column>
        </template>
        <el-table-column :label="t('common.operations')" width="140" align="center">
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button link type="primary" size="small" @click="handleDetail(row)">{{ t('common.detail') }}</el-button>
              <el-button
                link
                type="primary"
                size="small"
                :disabled="!isApproveButtonEnabled(row)"
                @click="handleApproveClick(row)"
              >{{ t('parts.approve') }}</el-button>
              <!-- 发货按钮 - 只在叫料审批模式且状态为已审批时显示 -->
              <el-button
                v-if="searchParams.approvalType === 'requisition' && row.requisitionStatus === 'approved'"
                link
                type="success"
                size="small"
                @click="handleShipClick(row)"
              >{{ t('parts.ship') }}</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      :title="searchParams.approvalType === 'damage' ? t('parts.damageDetail') : t('parts.requisitionDetail')"
      direction="rtl"
      size="50%"
      :modal="false"
    >
      <div class="drawer-content">
        <!-- 零件破损详情 -->
        <template v-if="searchParams.approvalType === 'damage' && detailData">
          <el-form :model="detailData" label-position="top">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('parts.scrapOrderNumber')">
                  <el-input v-model="detailData.scrapOrderNumber" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('parts.scrapDate')">
                  <el-input v-model="detailData.scrapDate" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="t('parts.storeName')">
                  <el-input v-model="detailData.storeName" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-divider>{{ t('parts.damageDetail') }}</el-divider>
          <el-table :data="detailData.items" style="width: 100%">
            <el-table-column :label="t('parts.serialNumber')" type="index" width="80" />
            <el-table-column :label="t('parts.partName')" prop="partName" min-width="120" />
            <el-table-column :label="t('parts.partNumber')" prop="partNumber" min-width="120" />
            <el-table-column :label="t('parts.scrapQuantity')" prop="quantity" width="100" />
            <el-table-column :label="t('parts.scrapSource')" prop="scrapSource" width="100" />
            <el-table-column :label="t('parts.scrapReason')" prop="scrapReason" min-width="200" show-overflow-tooltip />
            <el-table-column :label="t('parts.scrapImages')" width="120">
              <template #default="{ row }">
                <el-button
                  v-if="row.scrapImages && row.scrapImages.length > 0"
                  type="primary"
                  link
                  @click="showScrapImages(row.scrapImages)"
                >
                  {{ t('common.view') }}
                </el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </template>

        <!-- 叫料审批详情 -->
        <template v-else-if="detailData">
          <el-form :model="detailData" label-position="top">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="t('parts.requisitionNumber')">
                  <el-input v-model="detailData.requisitionNumber" readonly />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item :label="t('parts.requisitionDate')">
                  <el-input v-model="detailData.requisitionDate" readonly />
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider>{{ t('parts.requisitionDetail') }}</el-divider>
            <el-table :data="detailData.items" style="width: 100%">
              <el-table-column :label="t('parts.serialNumber')" type="index" width="80" />
              <el-table-column :label="t('parts.partName')" prop="partName" min-width="120" />
              <el-table-column :label="t('parts.partNumber')" prop="partNumber" min-width="120" />
              <el-table-column :label="t('parts.quantity')" prop="quantity" width="80" />
              <el-table-column :label="t('parts.unit')" prop="unit" width="80" />
              <el-table-column :label="t('parts.requisitionStatus')" prop="requisitionStatus" min-width="120">
                <template #default="{ row }">
                  {{ t(`parts.status.${row.requisitionStatus}`) }}
                </template>
              </el-table-column>
              <el-table-column :label="t('parts.requisitionDate')" prop="requisitionDate" min-width="120" />
              <el-table-column :label="t('parts.expectedArrivalTime')" prop="expectedArrivalTime" min-width="120" />
              <el-table-column label="收发状态" min-width="100" align="center">
                <template #default="{ row }">
                  <span v-if="getShipReceiveStatus(row)">{{ getShipReceiveStatus(row) }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="t('parts.supplierName')" prop="supplierName" min-width="120" />
            </el-table>
          </el-form>
        </template>
      </div>
    </el-drawer>

    <!-- 报损图片查看模态框 -->
    <el-dialog
      v-model="scrapImagesDialogVisible"
      :title="t('parts.scrapImages')"
      width="60%"
      :modal="false"
    >
      <div class="image-gallery">
        <el-image
          v-for="(image, index) in currentScrapImages"
          :key="index"
          :src="image"
          :preview-src-list="currentScrapImages"
          :initial-index="index"
          fit="cover"
          class="scrap-image"
          style="width: 150px; height: 150px; margin-right: 10px; margin-bottom: 10px; border-radius: 4px;"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="scrapImagesDialogVisible = false">{{ t('common.close') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 破损详情查看模态框 -->
    <el-dialog
      v-model="damageDetailDialogVisible"
      :title="t('parts.damageDetail')"
      width="60%"
      :modal="false"
    >
      <div class="damage-detail-content">
        <!-- 破损原因 -->
        <div class="damage-reason-section">
          <h4>{{ t('parts.scrapReason') }}</h4>
          <p>{{ damageDetailData.scrapReason || t('common.noData') }}</p>
        </div>

        <!-- 破损图片 -->
        <div v-if="damageDetailData.scrapImages && damageDetailData.scrapImages.length > 0" class="damage-images-section">
          <h4>{{ t('parts.scrapImages') }}</h4>
          <div class="image-gallery">
            <el-image
              v-for="(image, index) in damageDetailData.scrapImages"
              :key="index"
              :src="image"
              :preview-src-list="damageDetailData.scrapImages"
              :initial-index="index"
              fit="cover"
              class="scrap-image"
              style="width: 150px; height: 150px; margin-right: 10px; margin-bottom: 10px; border-radius: 4px;"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="damageDetailDialogVisible = false">{{ t('common.close') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审批模态框 -->
    <el-dialog
      v-model="approvalDialogVisible"
      :title="searchParams.approvalType === 'damage' ? t('parts.approveDamage') : t('parts.approveRequisition')"
      width="600px"
      :close-on-click-modal="false"
      :modal="false"
    >
      <el-form :model="approvalForm" label-position="top" class="dialog-form-modern">
        <el-row :gutter="20">
          <el-col :span="12" v-if="searchParams.approvalType === 'damage'">
            <el-form-item :label="t('parts.scrapOrderNumber')">
              <el-input v-model="approvalForm.scrapOrderNumber" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="searchParams.approvalType !== 'damage'">
            <el-form-item :label="t('parts.requisitionNumber')">
              <el-input v-model="approvalForm.requisitionNumber" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="searchParams.approvalType === 'damage'">
            <el-form-item :label="t('parts.scrapDate')">
              <el-input v-model="approvalForm.scrapDate" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="searchParams.approvalType !== 'damage'">
            <el-form-item :label="t('parts.requisitionDate')">
              <el-input v-model="approvalForm.requisitionDate" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="t('parts.approvalResult')" required>
              <el-select v-model="approvalForm.approvalResult" :placeholder="t('parts.selectStatus')" style="width: 100%;">
                <el-option :label="t('parts.status.approved')" value="approved"></el-option>
                <el-option :label="t('parts.status.rejected')" value="rejected"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="approvalForm.approvalResult === 'rejected'">
            <el-form-item :label="t('parts.rejectionReason')" required>
              <el-input
                v-model="approvalForm.rejectionReason"
                type="textarea"
                :rows="3"
                maxlength="1000"
                show-word-limit
                :placeholder="t('parts.rejectionReasonRequired')"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider>{{ searchParams.approvalType === 'damage' ? t('parts.damageDetail') : t('parts.requisitionDetail') }}</el-divider>

        <!-- 叫料审批详情表格 -->
        <el-table v-if="searchParams.approvalType !== 'damage'" :data="approvalDetailTableData" style="width: 100%;">
          <el-table-column :label="t('parts.serialNumber')" type="index" width="80" />
          <el-table-column :label="t('parts.partName')" prop="partName" min-width="120" />
          <el-table-column :label="t('parts.partNumber')" prop="partNumber" min-width="120" />
          <el-table-column :label="t('parts.quantity')" prop="quantity" width="80" />
          <el-table-column :label="t('parts.unit')" prop="unit" width="80" />
          <el-table-column :label="t('parts.requisitionStatus')" prop="requisitionStatus" min-width="120">
            <template #default="{ row }">
              {{ t(`parts.status.${row.requisitionStatus}`) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('parts.requisitionDate')" prop="requisitionDate" min-width="120" />
          <el-table-column :label="t('parts.expectedArrivalTime')" prop="expectedArrivalTime" min-width="120" />
          <el-table-column :label="t('parts.supplierName')" prop="supplierName" min-width="120" />
        </el-table>

        <!-- 零件破损审批详情表格 -->
        <el-table v-if="searchParams.approvalType === 'damage'" :data="approvalDetailTableData" style="width: 100%;">
          <el-table-column :label="t('parts.serialNumber')" type="index" width="80" />
          <el-table-column :label="t('parts.partName')" prop="partName" min-width="120" />
          <el-table-column :label="t('parts.partNumber')" prop="partNumber" min-width="120" />
          <el-table-column :label="t('parts.scrapQuantity')" prop="quantity" width="100" />
          <el-table-column :label="t('parts.scrapSource')" prop="scrapSource" min-width="120" />
          <el-table-column :label="t('parts.damageDetail')" width="120">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                @click="handleViewDamageDetail(row)"
              >
                {{ t('parts.damageDetail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="handleCancelApproval">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="handleApprove">{{ tc('confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { Search, Download } from '@element-plus/icons-vue';
import { fetchPartManagementData } from '@/mock/data/partManagement';
import { shipPartsPartially, shipPartsCompletely } from '@/api/modules/parts';
import type { PartsSearchParameters, PartsListItem, PartsDetail, PartsDetailItem } from '@/types/module.d';

// 定义 PartManagementItem 类型以匹配 mock 数据
interface PartManagementItem {
  id: string;
  requisitionNumber: string;
  requisitionDate: string;
  requisitionStatus: 'submitted' | 'approved' | 'rejected' | 'shipped' | 'partialReceived' | 'received' | 'cancelled' | 'voided';
  partName: string;
  partNumber: string;
  supplierName: string;
  inventoryStatus: 'normal' | 'belowSafetyStock';
  items?: any[];
  rejectionReason?: string;
}
import { mockPartArchivesData } from '@/mock/data/partArchivesData';
import { mockSupplierData } from '@/mock/data/supplierData';

const { t } = useI18n();

// 模拟的零件档案数据 (此部分已被删除或注释)
// const mockPartArchive = [
//   { partName: '轮胎', partNumber: 'T-001' },
//   { partName: '刹车片', partNumber: 'B-001' },
//   { partName: '机油滤清器', partNumber: 'F-001' },
//   { partName: '火花塞', partNumber: 'S-001' },
//   { partName: '轮胎（冬季）', partNumber: 'T-002' },
//   { partName: '刹车盘', partNumber: 'B-002' },
// ];

const partNameOptions = ref(Array.from(new Set(mockPartArchivesData.map(item => item.partName))).map(name => ({ label: name, value: name })));
const partNumberOptions = ref(Array.from(new Set(mockPartArchivesData.map(item => item.partNumber))).map(number => ({ label: number, value: number })));
// 更新供应商选项的生成逻辑，从 mockSupplierData 获取
const supplierNameOptions = ref(mockSupplierData.map(item => ({ label: item.name, value: item.name })));
// 门店名称选项
const storeNameOptions = ref<{ label: string; value: string }[]>([]);

// 搜索参数
const searchParams = reactive<PartsSearchParameters>({
  approvalType: 'requisition', // 默认选择叫料审批
  storeName: '',
  partName: '',
  partNumber: '',
  requisitionNumber: '',
  supplierName: '',
  requisitionDateRange: [],
  requisitionStatus: '',
  page: 1,
  pageSize: 10,
});

// 监听零件名称变化，自动带出零件编号
watch(() => searchParams.partName, (newVal) => {
  if (newVal) {
    const selectedPart = mockPartArchivesData.find(item => item.partName === newVal); // 使用导入的数据源
    if (selectedPart && searchParams.partNumber !== selectedPart.partNumber) {
      searchParams.partNumber = selectedPart.partNumber;
    }
  } else {
    // 如果零件名称被清空，则清空零件编号
    searchParams.partNumber = '';
  }
});

// 监听零件编号变化，自动带出零件名称
watch(() => searchParams.partNumber, (newVal) => {
  if (newVal) {
    const selectedPart = mockPartArchivesData.find(item => item.partNumber === newVal); // 使用导入的数据源
    if (selectedPart && searchParams.partName !== selectedPart.partName) {
      searchParams.partName = selectedPart.partName;
    }
  } else {
    // 如果零件编号被清空，则清空零件名称
    searchParams.partName = '';
  }
});

// 表格数据
const tableData = ref<PartsListItem[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const selectedRows = ref<PartsListItem[]>([]); // 用于存储选中的行

// 待审批数量统计
const pendingRequisitionCount = ref(0);
const pendingDamageCount = ref(0);

// 详情抽屉
const detailDrawerVisible = ref(false);
const detailData = ref<PartsDetail | null>(null);

// 报损图片查看模态框
const scrapImagesDialogVisible = ref(false);
const currentScrapImages = ref<string[]>([]);

// 破损详情查看模态框
const damageDetailDialogVisible = ref(false);
const damageDetailData = ref({
  scrapReason: '',
  scrapImages: [] as string[]
});

// 审批模态框
const approvalDialogVisible = ref(false);
const approvalForm = reactive({
  requisitionNumber: '',
  requisitionDate: '',
  scrapOrderNumber: '',
  scrapDate: '',
  approvalResult: '',
  rejectionReason: '',
});
const approvalDetailTableData = ref<PartsDetailItem[]>([]); // 审批模态框中的列表数据

// 搜索
const handleSearch = () => {
  if (!searchParams.approvalType) {
    ElMessage.warning(t('parts.pleaseSelectApprovalTypeFirst'));
    return;
  }
  currentPage.value = 1;
  fetchPartsList();
};

// 重置
const resetSearch = () => {
  Object.assign(searchParams, {
    storeName: '',
    partName: '',
    partNumber: '',
    requisitionNumber: '',
    supplierName: '',
    requisitionDateRange: [],
    requisitionStatus: '',
    page: 1,
    pageSize: 10,
  });
  handleSearch();
};

// 导出报表
const exportReport = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning(t('common.pleaseSelectData'));
    return;
  }

  console.log('导出选中的数据:', selectedRows.value);
  ElMessage.success(t('common.exportingReport'));

  // 实际导出逻辑 - 这里可以根据选中的数据生成报表
  // 可以调用API或生成Excel文件等
};

// 获取数据
const fetchPartsList = async () => {
  loading.value = true;
  try {
    const params: PartsSearchParameters = {
      ...searchParams,
      page: currentPage.value,
      pageSize: pageSize.value,
      requisitionStartDate: searchParams.requisitionDateRange && searchParams.requisitionDateRange.length === 2 ? searchParams.requisitionDateRange[0] : undefined,
      requisitionEndDate: searchParams.requisitionDateRange && searchParams.requisitionDateRange.length === 2 ? searchParams.requisitionDateRange[1] : undefined,
    };

    // Remove date range arrays if they are empty or not used for API call
    if (params.requisitionDateRange && params.requisitionDateRange.length === 0) delete params.requisitionDateRange;

    // 为破损审批模式映射参数
    if (searchParams.approvalType === 'damage') {
      // 将 requisitionStatus 映射为 status 参数用于破损审批筛选
      if (params.requisitionStatus) {
        (params as any).status = params.requisitionStatus;
        delete params.requisitionStatus; // 删除不适用的参数
      }
    }

    const response = await fetchPartManagementData(params);

    // 转换数据格式以匹配 PartsListItem 类型，数据已在后端排序
    const formattedData = response.data.map((item: any, index: number) => {
      const baseData = {
        serialNumber: index + 1 + (currentPage.value - 1) * pageSize.value,
      };

      if (searchParams.approvalType === 'damage') {
        // 零件破损审批数据映射
        return {
          ...baseData,
          id: item.id,
          scrapOrderNumber: item.scrapOrderNumber, // 单据号来自报损单号
          partName: item.partName,
          partNumber: item.partNumber,
          scrapDate: item.scrapDate, // 生成日期来自报损日期
          storeName: item.storeName || '默认门店', // 门店名称
          status: item.status, // 使用实际的破损记录状态
          quantity: item.scrapQuantity,
          scrapSource: item.scrapSource,
          scrapReason: item.scrapReason,
          scrapImages: item.scrapImages || [], // 报损图片
        } as PartsListItem;
      } else {
        // 叫料审批数据映射
        return {
          ...baseData,
          ...item,
          storeName: item.storeName || '默认门店', // 门店名称，如果没有则使用默认值
        } as PartsListItem & PartManagementItem;
      }
    });

    tableData.value = formattedData;
    total.value = response.total;
  } catch (error) {
    console.error('Failed to fetch parts list:', error);
    ElMessage.error(t('common.fetchFailed'));
  } finally {
    loading.value = false;
  }
};

// 审批类型变化处理
const handleApprovalTypeChange = (value: string) => {
  // 清空其他筛选条件
  searchParams.storeName = '';
  searchParams.partName = '';
  searchParams.partNumber = '';
  searchParams.requisitionNumber = '';
  searchParams.supplierName = '';
  searchParams.requisitionDateRange = [];
  searchParams.requisitionStatus = '';

  // 根据审批类型调整页面显示和数据筛选
  if (value === 'damage') {
    // 零件破损审批 - 从破损单数据中获取筛选选项
    console.log('切换到零件破损审批模式');
    loadDamageFilterOptions();
  } else if (value === 'requisition') {
    // 叫料审批 - 从叫料单数据中获取筛选选项
    console.log('切换到叫料审批模式');
    loadRequisitionFilterOptions();
  }

  // 如果选择了审批类型，重新获取数据和统计
  if (value) {
    fetchPartsList();
    fetchPendingCounts();
  } else {
    // 如果清空了审批类型，清空表格数据和筛选选项
    tableData.value = [];
    total.value = 0;
    clearFilterOptions();
  }
};

// 加载叫料审批筛选选项
const loadRequisitionFilterOptions = async () => {
  try {
    // 获取叫料单数据用于筛选选项
    const response = await fetchPartManagementData({
      approvalType: 'requisition',
      page: 1,
      pageSize: 1000 // 获取足够多的数据来提取筛选选项
    });

    // 从叫料单数据中提取唯一的筛选选项
    const uniquePartNames = [...new Set(response.data.map((item: any) => item.partName).filter(Boolean))];
    const uniquePartNumbers = [...new Set(response.data.map((item: any) => item.partNumber).filter(Boolean))];
    const uniqueSuppliers = [...new Set(response.data.map((item: any) => item.supplierName).filter(Boolean))];
    const uniqueStoreNames = [...new Set(response.data.map((item: any) => item.storeName).filter(Boolean))];

    // 更新筛选选项
    partNameOptions.value = uniquePartNames.map(name => ({ label: name, value: name }));
    partNumberOptions.value = uniquePartNumbers.map(number => ({ label: number, value: number }));
    supplierNameOptions.value = uniqueSuppliers.map(supplier => ({ label: supplier, value: supplier }));
    storeNameOptions.value = uniqueStoreNames.map(store => ({ label: store, value: store }));

    console.log('叫料审批筛选选项已加载');
  } catch (error) {
    console.error('加载叫料审批筛选选项失败:', error);
  }
};

// 加载破损审批筛选选项
const loadDamageFilterOptions = async () => {
  try {
    // 获取破损单数据用于筛选选项
    const response = await fetchPartManagementData({
      approvalType: 'damage',
      page: 1,
      pageSize: 1000 // 获取足够多的数据来提取筛选选项
    });

    // 从破损单数据中提取唯一的筛选选项
    const uniquePartNames = [...new Set(response.data.map((item: any) => item.partName).filter(Boolean))];
    const uniquePartNumbers = [...new Set(response.data.map((item: any) => item.partNumber).filter(Boolean))];
    const uniqueSuppliers = [...new Set(response.data.map((item: any) => item.supplierName).filter(Boolean))];
    const uniqueStoreNames = [...new Set(response.data.map((item: any) => item.storeName).filter(Boolean))];

    // 更新筛选选项
    partNameOptions.value = uniquePartNames.map(name => ({ label: name, value: name }));
    partNumberOptions.value = uniquePartNumbers.map(number => ({ label: number, value: number }));
    supplierNameOptions.value = uniqueSuppliers.map(supplier => ({ label: supplier, value: supplier }));
    storeNameOptions.value = uniqueStoreNames.map(store => ({ label: store, value: store }));

    console.log('破损审批筛选选项已加载');
  } catch (error) {
    console.error('加载破损审批筛选选项失败:', error);
  }
};

// 清空筛选选项
const clearFilterOptions = () => {
  partNameOptions.value = [];
  partNumberOptions.value = [];
  supplierNameOptions.value = [];
  storeNameOptions.value = [];
  console.log('筛选选项已清空');
};

// 分页相关
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  fetchPartsList();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  fetchPartsList();
};

// 表格行选择
const handleSelectionChange = (selection: PartsListItem[]) => {
  selectedRows.value = selection;
};

// 检查行是否被选中
const isRowSelected = (row: PartsListItem) => {
  return selectedRows.value.some(selectedRow => selectedRow.id === row.id);
};

// 检查审批按钮是否可用
const isApproveButtonEnabled = (row: PartsListItem) => {
  if (searchParams.approvalType === 'damage') {
    // 零件破损审批：检查状态是否为已提交且已勾选
    return (row as any).status === 'submitted' && isRowSelected(row);
  } else {
    // 叫料审批：检查叫料状态是否为已提交且已勾选
    return row.requisitionStatus === 'submitted' && isRowSelected(row);
  }
};

// 详情操作
const handleDetail = async (row: PartsListItem & PartManagementItem) => {
  try {
    if (searchParams.approvalType === 'damage') {
      // 零件破损详情：获取同一报损单号下的所有物料
      const response = await fetchPartManagementData({
        approvalType: 'damage',
        scrapOrderNumber: (row as any).scrapOrderNumber,
        page: 1,
        pageSize: 100 // 获取所有相关记录
      });

      // 构造详情数据
      detailData.value = {
        scrapOrderNumber: (row as any).scrapOrderNumber,
        scrapDate: (row as any).scrapDate,
        storeName: (row as any).storeName,
        items: response.data.map((item: any) => ({
          partName: item.partName,
          partNumber: item.partNumber,
          quantity: item.scrapQuantity,
          scrapSource: item.scrapSource,
          scrapReason: item.scrapReason,
          scrapImages: item.scrapImages || []
        }))
      } as any;
    } else {
      // 叫料审批详情：直接使用传入的行数据
      detailData.value = row as PartsDetail;
    }
    detailDrawerVisible.value = true;
  } catch (error) {
    console.error('Failed to fetch parts detail:', error);
    ElMessage.error(t('common.fetchFailed'));
  }
};

// 显示报损图片
const showScrapImages = (images: string[]) => {
  currentScrapImages.value = images;
  scrapImagesDialogVisible.value = true;
};

// 查看破损详情（包含破损原因和图片）
const handleViewDamageDetail = (row: any) => {
  // 设置破损详情数据
  damageDetailData.value = {
    scrapReason: row.scrapReason || '',
    scrapImages: row.scrapImages || []
  };
  damageDetailDialogVisible.value = true;
};

// 审批操作
const handleApproveClick = async (row: PartsListItem & PartManagementItem) => {
  // 清空审批结果
  approvalForm.approvalResult = '';
  approvalForm.rejectionReason = '';

  if (searchParams.approvalType === 'damage') {
    // 零件破损审批
    approvalForm.scrapOrderNumber = (row as any).scrapOrderNumber;
    approvalForm.scrapDate = (row as any).scrapDate;

    // 获取同一报损单号下的所有物料详情
    try {
      const response = await fetchPartManagementData({
        approvalType: 'damage',
        scrapOrderNumber: (row as any).scrapOrderNumber,
        page: 1,
        pageSize: 100
      });

      // 设置审批详情表格数据
      approvalDetailTableData.value = response.data.map((item: any) => ({
        partName: item.partName,
        partNumber: item.partNumber,
        quantity: item.scrapQuantity,
        scrapSource: item.scrapSource,
        scrapReason: item.scrapReason,
        scrapImages: item.scrapImages || []
      }));
    } catch (error) {
      console.error('Failed to fetch damage detail for approval:', error);
      ElMessage.error(t('common.fetchFailed'));
      return;
    }
  } else {
    // 叫料审批
    approvalForm.requisitionNumber = row.requisitionNumber;
    approvalForm.requisitionDate = row.requisitionDate;

    // 设置叫料单明细
    approvalDetailTableData.value = row.items || [];
  }

  approvalDialogVisible.value = true;
};

// 审批模态框内部获取详情（此函数不再需要，已删除）
// const handleDetailForApproval = async (id: string | number) => {
//   try {
//     const response = await fetchPartManagementData(id);
//     approvalDetailTableData.value = response.items;
//   } catch (error) {
//     console.error('Failed to fetch parts detail for approval:', error);
//     ElMessage.error(t('common.fetchFailed'));
//   }
// };

// 提交审批
const handleApprove = async () => {
  if (approvalForm.approvalResult === 'rejected' && !approvalForm.rejectionReason) {
    ElMessage.warning(t('parts.rejectionReasonRequired'));
    return;
  }
  if (!approvalForm.approvalResult) {
    ElMessage.warning(t('parts.approvalResultRequired')); // 添加一个国际化键值
    return;
  }

  try {
    // const payload: ApprovePartsPayload = { // 注释掉，因为不再模拟API交互
    //   id: selectedRows.value[0].id,
    //   approvalResult: approvalForm.approvalResult as 'approved' | 'rejected',
    // };
    // if (approvalForm.rejectionReason) {
    //   payload.rejectionReason = approvalForm.rejectionReason;
    // }

    // await fetchPartManagementData(payload); // 注释掉实际的API调用
    ElMessage.success(t('common.operationSuccessful'));
    approvalDialogVisible.value = false;
    fetchPartsList(); // 刷新列表
  } catch (error) {
    console.error('Failed to approve parts:', error);
    ElMessage.error(t('common.operationFailed'));
  }
};

// 取消审批
const handleCancelApproval = () => {
  approvalDialogVisible.value = false;
};

// 处理发货操作
const handleShipClick = async (row: any) => {
  try {
    // 调用完整发货API
    const result = await shipPartsCompletely(row.requisitionNumber);

    if (result.success) {
      ElMessage.success(t('parts.shipSuccess'));
      // 刷新列表
      await fetchPartsList();
    } else {
      ElMessage.error(result.message || t('parts.shipFailed'));
    }
  } catch (error) {
    console.error('发货失败:', error);
    ElMessage.error(t('parts.shipFailed'));
  }
};

// Helper function to check if purchase order number should be displayed
const shouldShowPurchaseOrderNumber = (status: string) => {
  // 只有已发货、部分发货、部分收货、已收货状态必须显示采购单号
  // 已审批状态有的有有的没有（在数据生成时已处理）
  return ['shipped', 'partialShipped', 'partialReceived', 'received', 'approved'].includes(status);
};

// 获取报损来源标签
const getScrapSourceLabel = (source: string) => {
  return source || '-';
};

// Helper function to get ship/receive status
const getShipReceiveStatus = (item: any) => {
  // 检查是否已收货
  if (item.isFullyReceived || (item.receivedQuantity && item.receivedQuantity >= item.quantity)) {
    return '已收货';
  }
  // 检查是否已发货
  if (item.isFullyShipped || (item.shippedQuantity && item.shippedQuantity >= item.quantity)) {
    return '已发货';
  }
  // 未发货或部分发货/收货，返回空字符串
  return '';
};

// 获取待审批数量统计
const fetchPendingCounts = async () => {
  try {
    // 获取待审批的叫料单数量
    const requisitionResponse = await fetchPartManagementData({
      approvalType: 'requisition',
      requisitionStatus: 'submitted',
      page: 1,
      pageSize: 1
    });
    pendingRequisitionCount.value = requisitionResponse.total || 0;

    // 获取待审批的零件破损申请数量
    const damageResponse = await fetchPartManagementData({
      approvalType: 'damage',
      status: 'submitted',
      page: 1,
      pageSize: 1
    });
    pendingDamageCount.value = damageResponse.total || 0;
  } catch (error) {
    console.error('获取待审批数量失败:', error);
    pendingRequisitionCount.value = 0;
    pendingDamageCount.value = 0;
  }
};

// 点击待审批叫料单统计
const handleRequisitionApprovalClick = () => {
  // 设置审批类型为叫料审批
  searchParams.approvalType = 'requisition';

  // 触发审批类型变化处理
  handleApprovalTypeChange('requisition');

  // 自动搜索待审批的叫料单
  searchParams.requisitionStatus = 'submitted';
  handleSearch();

  ElMessage.info(t('parts.switchedToRequisitionApproval'));
};

// 点击待审批破损申请统计
const handleDamageApprovalClick = () => {
  // 设置审批类型为破损审批
  searchParams.approvalType = 'damage';

  // 触发审批类型变化处理
  handleApprovalTypeChange('damage');

  // 自动搜索待审批的破损申请（这里假设破损申请的状态字段也是submitted）
  searchParams.requisitionStatus = 'submitted'; // 注意：这里可能需要根据实际的破损申请状态字段调整
  handleSearch();

  ElMessage.info(t('parts.switchedToDamageApproval'));
};

// 获取空数据提示文本
const getEmptyText = () => {
  if (!searchParams.approvalType) {
    return t('parts.pleaseSelectApprovalTypeFirst');
  }

  if (hasActiveFilters()) {
    return t('parts.noFilterResults');
  }

  return t('common.noData');
};

// 检查是否有活跃的筛选条件
const hasActiveFilters = () => {
  return searchParams.storeName ||
         searchParams.partName ||
         searchParams.partNumber ||
         searchParams.requisitionNumber ||
         searchParams.supplierName ||
         searchParams.requisitionStatus ||
         (searchParams.requisitionDateRange && searchParams.requisitionDateRange.length > 0);
};

// 清空筛选条件
const clearFilters = () => {
  Object.assign(searchParams, {
    storeName: '',
    partName: '',
    partNumber: '',
    requisitionNumber: '',
    supplierName: '',
    requisitionDateRange: [],
    requisitionStatus: '',
  });
  handleSearch();
};

// 页面加载时获取待审批统计数据，并默认加载叫料审批数据
onMounted(() => {
  // 获取待审批统计数据
  fetchPendingCounts();

  // 默认加载叫料审批的筛选选项和数据
  loadRequisitionFilterOptions();
  fetchPartsList();
});
</script>

<style scoped>
.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.scrap-image {
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.scrap-image:hover {
  transform: scale(1.05);
}

.drawer-content {
  padding: 20px;
}
</style>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: bold;
}

.mb-20 {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-right: 20px;
    margin-bottom: 15px;
    &:last-child {
      margin-right: 0;
    }
  }
}

.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.table-card {
  margin-bottom: 20px;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap; // 禁止文本换行
  }
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;

  .el-button {
    padding: 2px 6px;
    font-size: 12px;
    margin: 0;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-form-modern {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  .el-button {
    margin-left: 10px;
  }
}

.approval-stats {
  display: flex;
  align-items: center;
  gap: 12px;

  .el-tag {
    font-size: 14px;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    white-space: nowrap;
  }
}

.empty-state {
  padding: 40px 20px;

  .el-empty {
    padding: 20px 0;
  }
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.scrap-image {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
}

.damage-detail-content {
  .damage-reason-section,
  .damage-images-section {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #606266;
      line-height: 1.5;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      min-height: 40px;
    }
  }

  .damage-images-section {
    h4 {
      margin-bottom: 15px;
    }
  }
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;

  .el-button {
    padding: 2px 6px;
    font-size: 12px;
    margin: 0;
  }
}
</style>
