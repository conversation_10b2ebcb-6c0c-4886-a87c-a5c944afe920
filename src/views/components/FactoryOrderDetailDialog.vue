<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('factoryOrder.detailTitle')"
    width="90%"
    top="5vh"
    :close-on-click-modal="false"
    class="factory-order-detail-dialog"
  >
    <div v-loading="loading" style="min-height: 400px;">
      <div v-if="orderDetail" class="order-detail-content">
        <!-- 订单概要信息 -->
        <div class="order-summary">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="summary-item">
                <span class="label">{{ t('factoryOrder.orderNumber') }}：</span>
                <span class="value">{{ orderDetail.orderNo }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="summary-item">
                <span class="label">{{ t('factoryOrder.creationTime') }}：</span>
                <span class="value">{{ formatDateTime(orderDetail.createTime) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 客户信息区域 -->
        <el-card class="info-card">
          <template #header>
            <span>{{ t('factoryOrder.customerInfo') }} - {{ t('factoryOrder.personalDetails') }}</span>
          </template>

                    <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.ordererNameField') }}：</span>
                <span class="value">{{ orderDetail.customerName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.ordererPhoneField') }}：</span>
                <span class="value">{{ orderDetail.customerPhone }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.buyerNameField') }}：</span>
                <span class="value">{{ orderDetail.customerName }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.buyerPhoneField') }}：</span>
                <span class="value">{{ orderDetail.customerPhone }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.buyerIdType') }}：</span>
                <span class="value">{{ orderDetail.idType }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.buyerIdNumber') }}：</span>
                <span class="value">{{ orderDetail.idNumber }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.buyerEmail') }}：</span>
                <span class="value">{{ orderDetail.email }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.buyerState') }}：</span>
                <span class="value">{{ orderDetail.state }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.buyerCity') }}：</span>
                <span class="value">{{ orderDetail.city }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="16">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.buyerAddress') }}：</span>
                <span class="value">{{ orderDetail.address }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.buyerPostcode') }}：</span>
                <span class="value">{{ orderDetail.zipCode }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 购车门店信息 -->
        <el-card class="info-card">
          <template #header>
            <span>{{ t('factoryOrder.storeInfo') }} - {{ t('factoryOrder.preferredOutletSalesAdvisor') }}</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.region') }}：</span>
                <span class="value">{{ orderDetail.region || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.dealerCity') }}：</span>
                <span class="value">{{ orderDetail.dealerCity || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.dealerName') }}：</span>
                <span class="value">{{ orderDetail.dealerName || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="label">{{ t('factoryOrder.salesConsultant') }}：</span>
                <span class="value">{{ orderDetail.salesAdvisorName || orderDetail.salesman || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 购车信息区域 -->
        <el-card class="info-card">
          <template #header>
            <span>{{ t('factoryOrder.purchaseDetails') }} - {{ t('factoryOrder.purchaseDetailsTitle') }}</span>
          </template>

          <el-tabs v-model="activeTab" class="detail-tabs">
            <!-- 车辆信息Tab -->
            <el-tab-pane :label="t('factoryOrder.vehicleInfoTab')" name="vehicle">
              <div class="tab-content">
                <!-- 基本车辆信息 -->
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.model') }}：</span>
                      <span class="value">{{ orderDetail.model }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.variant') }}：</span>
                      <span class="value">{{ orderDetail.variant }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('common.color') }}：</span>
                      <span class="value">{{ orderDetail.color }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.numberPlatesFee') }}：</span>
                      <span class="value">RM {{ orderDetail.numberPlatesFee.toLocaleString() }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.salesSubtotal') }}：</span>
                      <span class="value">RM {{ orderDetail.vehiclePrice.toLocaleString() }}</span>
                    </div>
                  </el-col>
                </el-row>

                <!-- 选配件信息表格 -->
                <div class="section-title">{{ t('factoryOrder.accessoryInfo') }}</div>
                <el-table :data="orderDetail.accessories" border style="width: 100%; margin-bottom: 20px;">
                  <el-table-column prop="category" :label="t('factoryOrder.accessoryCategory')" />
                  <el-table-column prop="name" :label="t('factoryOrder.accessoryName')" />
                  <el-table-column prop="price" :label="t('factoryOrder.accessoryUnitPrice')">
                    <template #default="scope">
                      RM {{ scope.row.price.toLocaleString() }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="quantity" :label="t('factoryOrder.accessoryQuantity')" />
                  <el-table-column prop="total" :label="t('factoryOrder.accessoryTotalPrice')">
                    <template #default="scope">
                      RM {{ scope.row.total.toLocaleString() }}
                    </template>
                  </el-table-column>
                </el-table>

                <div class="total-amount">
                  <span class="label">{{ t('factoryOrder.accessoriesTotalAmount') }}：</span>
                  <span class="value amount">RM {{ (orderDetail.totalAccessoryAmount || 0).toLocaleString() }}</span>
                </div>
              </div>
            </el-tab-pane>

            <!-- 开票信息Tab -->
            <el-tab-pane :label="t('factoryOrder.invoiceInfoTab')" name="invoice">
              <div class="tab-content">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.invoiceType') }}：</span>
                      <span class="value">{{ orderDetail.invoiceType }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.invoiceName') }}：</span>
                      <span class="value">{{ orderDetail.invoiceName }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.invoicePhone') }}：</span>
                      <span class="value">{{ orderDetail.invoicePhone }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="18">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.invoiceAddress') }}：</span>
                      <span class="value">{{ orderDetail.invoiceAddress }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>

            <!-- 服务&权益信息Tab -->
            <el-tab-pane :label="t('factoryOrder.benefitsInfoTab')" name="benefits">
              <div class="tab-content">
                <div class="section-title">{{ t('factoryOrder.benefitsInfo') }}</div>
                <el-table :data="orderDetail.rights" border style="width: 100%; margin-bottom: 20px;">
                  <el-table-column prop="code" :label="t('factoryOrder.benefitCode')" />
                  <el-table-column prop="name" :label="t('factoryOrder.benefitName')" />
                  <el-table-column prop="mode" :label="t('factoryOrder.benefitMode')" />
                  <el-table-column prop="discountPrice" :label="t('factoryOrder.discountPrice')">
                    <template #default="scope">
                      RM {{ scope.row.discountPrice?.toLocaleString() }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="effectiveDate" :label="t('factoryOrder.effectiveDate')" />
                  <el-table-column prop="expiryDate" :label="t('factoryOrder.expirationDate')" />
                </el-table>

                <div class="total-amount">
                  <span class="label">{{ t('factoryOrder.benefitsTotalAmount') }}：</span>
                  <span class="value amount">RM {{ orderDetail.totalRightsDiscountAmount.toLocaleString() }}</span>
                </div>
              </div>
            </el-tab-pane>

            <!-- 付款信息Tab -->
            <el-tab-pane :label="t('factoryOrder.paymentInfoTab')" name="payment">
              <div class="tab-content">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.paymentMethod') }}：</span>
                      <span class="value">{{ orderDetail.paymentMethod }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.loanApprovalStatusField') }}：</span>
                      <span class="value">{{ orderDetail.loanStatus }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.depositAmount') }}：</span>
                      <span class="value">RM {{ orderDetail.depositAmount.toLocaleString() }}</span>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.loanAmount') }}：</span>
                      <span class="value">RM {{ orderDetail.loanAmount.toLocaleString() }}</span>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="info-item">
                      <span class="label">{{ t('factoryOrder.balanceAmount') }}：</span>
                      <span class="value">RM {{ orderDetail.finalPaymentAmount.toLocaleString() }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>

            <!-- 保险信息Tab -->
            <el-tab-pane :label="t('factoryOrder.insuranceInfoTab')" name="insurance">
              <div class="tab-content">
                <div class="section-title">{{ t('factoryOrder.insuranceInfo') }}</div>
                <el-table :data="orderDetail.policies" border style="width: 100%; margin-bottom: 20px;">
                  <el-table-column prop="policyNumber" :label="t('factoryOrder.policyNumber')" />
                  <el-table-column prop="insuranceType" :label="t('factoryOrder.insuranceType')" />
                  <el-table-column prop="insuranceCompany" :label="t('factoryOrder.insuranceCompany')" />
                  <el-table-column prop="effectiveDate" :label="t('factoryOrder.effectiveDateField')" />
                  <el-table-column prop="expiryDate" :label="t('factoryOrder.expirationDateField')" />
                  <el-table-column prop="price" :label="t('factoryOrder.insurancePrice')">
                    <template #default="scope">
                      RM {{ scope.row.price.toLocaleString() }}
                    </template>
                  </el-table-column>
                </el-table>

                <div class="total-amount">
                  <span class="label">{{ t('factoryOrder.insurancesTotalAmount') }}：</span>
                  <span class="value amount">RM {{ orderDetail.totalInsuranceAmount.toLocaleString() }}</span>
                </div>

                <div class="notes-section">
                  <div class="info-item">
                    <span class="label">{{ t('factoryOrder.insuranceNotes') }}：</span>
                    <span class="value">{{ orderDetail.remarks }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- OTR费用信息Tab -->
            <el-tab-pane :label="t('factoryOrder.otrFeesTab')" name="otr">
              <div class="tab-content">
                <div class="section-title">{{ t('factoryOrder.otrFeesInfo') }}</div>
                <el-table :data="orderDetail.otrFees" border style="width: 100%; margin-bottom: 20px;">
                  <el-table-column prop="invoiceNumber" :label="t('factoryOrder.ticketNumber')" />
                  <el-table-column prop="item" :label="t('factoryOrder.feeItem')" />
                  <el-table-column prop="price" :label="t('factoryOrder.feePrice')">
                    <template #default="scope">
                      RM {{ scope.row.price.toLocaleString() }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="effectiveDate" :label="t('factoryOrder.effectiveDateField')" />
                  <el-table-column prop="expiryDate" :label="t('factoryOrder.expirationDateField')" />
                </el-table>

                <div class="total-amount">
                  <span class="label">{{ t('factoryOrder.otrFeesTotalAmount') }}：</span>
                  <span class="value amount">RM {{ (orderDetail.totalOtrAmount || 0).toLocaleString() }}</span>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>

        <!-- 订单变更记录 -->
        <el-card class="info-card">
          <template #header>
            <span>{{ t('factoryOrder.changeRecordsTab') }} - {{ t('factoryOrder.changeRecordsInfo') }}</span>
          </template>

          <el-table :data="orderDetail.changeRecords" border style="width: 100%;">
<!--            <el-table-column prop="id" :label="t('factoryOrder.serialNumber')" width="80" />-->
            <el-table-column prop="originalContent" :label="t('factoryOrder.originalContent')" />
            <el-table-column prop="changedContent" :label="t('factoryOrder.changedContent')" />
            <el-table-column prop="createdBy" :label="t('factoryOrder.operator')" width="120" />
            <el-table-column prop="createdAt" :label="t('factoryOrder.operationTime')" width="160">
              <template #default="scope">
                {{ formatDateTime(scope.row.createdAt) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <!-- 底部价格栏 -->
    <div v-if="orderDetail" class="footer-bar">
      <div class="price-info">
        <span class="price-item">
          <span class="label">{{ t('factoryOrder.vehicleInvoicePrice') }}：</span>
          <span class="value">RM {{ orderDetail.totalInvoicePrice.toLocaleString() }}</span>
        </span>
        <span class="price-item">
          <span class="label">{{ t('factoryOrder.remainingReceivable') }}：</span>
          <span class="value amount">RM {{ orderDetail.remainingReceivableAmount.toLocaleString() }}</span>
        </span>
      </div>
      <el-button @click="handleClose">{{ t('factoryOrder.backToList') }}</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { FactoryOrderDetail } from '@/types/module'
import { getFactoryOrderDetail } from '@/api/modules/factoryOrder'

const { t } = useI18n()

// Props
interface Props {
  visible: boolean
  orderNumber: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const activeTab = ref('vehicle')
const orderDetail = ref<FactoryOrderDetail | null>(null)

// 监听弹窗显示
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.orderNumber) {
    loadOrderDetail()
  }
})

// 监听弹窗关闭
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal) {
    orderDetail.value = null
    activeTab.value = 'vehicle'
  }
})

// 加载订单详情
const loadOrderDetail = async () => {
  loading.value = true
  try {
    orderDetail.value = await getFactoryOrderDetail(props.orderNumber)
  } catch (error) {
    console.error('Failed to load order detail:', error)
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 格式化日期时间
const formatDateTime = (dateStr: string | undefined) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style lang="scss" scoped>
.factory-order-detail-dialog {
  .order-detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .order-summary {
    padding: 20px;
    background: #f5f7fa;
    border-radius: 4px;
    margin-bottom: 20px;

    .summary-item {
      .label {
        font-weight: bold;
        color: #303133;
      }

      .value {
        color: #606266;
      }
    }
  }

  .info-card {
    margin-bottom: 20px;

    .info-item {
      margin-bottom: 15px;

      .label {
        display: inline-block;
        width: 120px;
        font-weight: bold;
        color: #303133;
      }

      .value {
        color: #606266;

        &.amount {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }
  }

  .detail-tabs {
    .tab-content {
      padding: 20px 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
    }

    .total-amount {
      text-align: right;
      padding: 15px 0;
      border-top: 1px solid #e4e7ed;

      .label {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .value.amount {
        font-size: 18px;
        font-weight: bold;
        color: #f56c6c;
        margin-left: 10px;
      }
    }

    .notes-section {
      margin-top: 20px;
      padding: 15px;
      background: #f5f7fa;
      border-radius: 4px;
    }
  }

  .footer-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;

    .price-info {
      display: flex;
      gap: 30px;

      .price-item {
        .label {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
        }

        .value {
          font-size: 16px;
          color: #606266;
          margin-left: 10px;

          &.amount {
            color: #f56c6c;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
