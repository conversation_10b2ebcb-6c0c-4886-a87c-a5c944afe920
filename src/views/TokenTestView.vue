<template>
  <div class="token-test-container">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h3>🔐 Token传输测试</h3>
          <p>验证所有API请求是否正确携带Authorization头</p>
        </div>
      </template>

      <div class="test-section">
        <h4>当前认证状态</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="登录状态">
            <el-tag :type="authStore.isLoggedIn ? 'success' : 'danger'">
              {{ authStore.isLoggedIn ? '已登录' : '未登录' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Token状态">
            <el-tag :type="authStore.token ? 'success' : 'info'">
              {{ authStore.token ? '存在' : '不存在' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户信息">
            {{ authStore.userInfo?.realName || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户权限">
            {{ authStore.userRoles.join(', ') || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-divider />

      <div class="test-section">
        <h4>API请求测试</h4>
        <p class="test-description">
          以下测试将调用不同的API接口，验证Authorization头是否正确传输：
        </p>

        <div class="test-buttons">
          <el-button
            type="primary"
            :loading="loading.userInfo"
            @click="testGetUserInfo"
          >
            测试获取用户信息
          </el-button>

          <el-button
            type="success"
            :loading="loading.storeList"
            @click="testGetStoreList"
          >
            测试获取门店列表
          </el-button>

          <el-button
            type="warning"
            :loading="loading.menuTree"
            @click="testGetMenuTree"
          >
            测试获取菜单树
          </el-button>

          <el-button
            type="info"
            :loading="loading.roleList"
            @click="testGetRoleList"
          >
            测试获取角色列表
          </el-button>
        </div>
      </div>

      <el-divider />

      <div class="test-section">
        <h4>请求日志</h4>
        <div class="log-container">
          <div
            v-for="(log, index) in requestLogs"
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <div class="log-header">
              <el-tag
                :type="log.type === 'success' ? 'success' : log.type === 'error' ? 'danger' : 'info'"
                size="small"
              >
                {{ log.method }}
              </el-tag>
              <span class="log-time">{{ log.time }}</span>
            </div>
            <div class="log-content">
              <p><strong>URL:</strong> {{ log.url }}</p>
              <p v-if="log.headers"><strong>Headers:</strong></p>
              <pre v-if="log.headers" class="log-headers">{{ log.headers }}</pre>
              <p><strong>结果:</strong> {{ log.message }}</p>
            </div>
          </div>

          <div v-if="requestLogs.length === 0" class="no-logs">
            暂无请求日志，点击上方按钮进行测试
          </div>
        </div>
      </div>

      <el-divider />

      <div class="test-section">
        <h4>💡 说明</h4>
        <el-alert
          title="Token传输机制"
          type="info"
          :closable="false"
          show-icon
        >
          <p>1. 用户登录成功后，Token会自动保存到localStorage中</p>
          <p>2. 所有通过http工具发送的请求都会自动在请求头中添加 <code>Authorization: Bearer {token}</code></p>
          <p>3. 当Token过期或无效时，系统会自动清除Token并重定向到登录页</p>
          <p>4. 请求拦截器会在开发环境下打印请求信息到控制台，可以查看Network面板验证</p>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { getStoreList } from '@/api/modules/permission'
import { getUserInfo } from '@/api/modules/auth'

const authStore = useAuthStore()

// 加载状态
const loading = reactive({
  userInfo: false,
  storeList: false,
  menuTree: false,
  roleList: false
})

// 请求日志
interface RequestLog {
  time: string
  method: string
  url: string
  headers?: string
  message: string
  type: 'success' | 'error' | 'info'
}

const requestLogs = ref<RequestLog[]>([])

// 添加日志
const addLog = (log: Omit<RequestLog, 'time'>) => {
  requestLogs.value.unshift({
    ...log,
    time: new Date().toLocaleTimeString()
  })

  // 只保留最近10条日志
  if (requestLogs.value.length > 10) {
    requestLogs.value = requestLogs.value.slice(0, 10)
  }
}

// 测试获取用户信息
const testGetUserInfo = async () => {
  loading.userInfo = true
  try {
    const token = localStorage.getItem('token')
    addLog({
      method: 'POST',
      url: '/auth/user-info',
      headers: token ? `Authorization: Bearer ${token.substring(0, 20)}...` : '无Token',
      message: '开始请求用户信息',
      type: 'info'
    })

    const response = await getUserInfo()

    addLog({
      method: 'POST',
      url: '/auth/user-info',
      message: `请求成功: 获取到用户 ${response.result.realName}`,
      type: 'success'
    })

    ElMessage.success('用户信息获取成功')
  } catch (error: any) {
    addLog({
      method: 'POST',
      url: '/auth/user-info',
      message: `请求失败: ${error.message}`,
      type: 'error'
    })

    ElMessage.error('用户信息获取失败')
  } finally {
    loading.userInfo = false
  }
}

// 测试获取门店列表
const testGetStoreList = async () => {
  loading.storeList = true
  try {
    const token = localStorage.getItem('token')
    addLog({
      method: 'POST',
      url: '/stores/page',
      headers: token ? `Authorization: Bearer ${token.substring(0, 20)}...` : '无Token',
      message: '开始请求门店列表',
      type: 'info'
    })

    const response = await getStoreList({ current: 1, size: 10 })

    addLog({
      method: 'POST',
      url: '/stores/page',
      message: `请求成功: 获取到 ${response.result.total} 条门店数据`,
      type: 'success'
    })

    ElMessage.success('门店列表获取成功')
  } catch (error: any) {
    addLog({
      method: 'POST',
      url: '/stores/page',
      message: `请求失败: ${error.message}`,
      type: 'error'
    })

    ElMessage.error('门店列表获取失败')
  } finally {
    loading.storeList = false
  }
}

// 测试获取菜单树
const testGetMenuTree = async () => {
  loading.menuTree = true
  try {
    const token = localStorage.getItem('token')
    addLog({
      method: 'GET',
      url: '/menus/tree',
      headers: token ? `Authorization: Bearer ${token.substring(0, 20)}...` : '无Token',
      message: '开始请求菜单树',
      type: 'info'
    })

    // 模拟菜单树请求
    setTimeout(() => {
      addLog({
        method: 'GET',
        url: '/menus/tree',
        message: 'Mock请求成功: 获取到菜单树数据',
        type: 'success'
      })

      ElMessage.success('菜单树获取成功 (Mock)')
      loading.menuTree = false
    }, 1000)
  } catch (error: any) {
    addLog({
      method: 'GET',
      url: '/menus/tree',
      message: `请求失败: ${error.message}`,
      type: 'error'
    })

    ElMessage.error('菜单树获取失败')
    loading.menuTree = false
  }
}

// 测试获取角色列表
const testGetRoleList = async () => {
  loading.roleList = true
  try {
    const token = localStorage.getItem('token')
    addLog({
      method: 'POST',
      url: '/roles/page',
      headers: token ? `Authorization: Bearer ${token.substring(0, 20)}...` : '无Token',
      message: '开始请求角色列表',
      type: 'info'
    })

    // 模拟角色列表请求
    setTimeout(() => {
      addLog({
        method: 'POST',
        url: '/roles/page',
        message: 'Mock请求成功: 获取到角色列表数据',
        type: 'success'
      })

      ElMessage.success('角色列表获取成功 (Mock)')
      loading.roleList = false
    }, 1000)
  } catch (error: any) {
    addLog({
      method: 'POST',
      url: '/roles/page',
      message: `请求失败: ${error.message}`,
      type: 'error'
    })

    ElMessage.error('角色列表获取失败')
    loading.roleList = false
  }
}
</script>

<style scoped lang="scss">
.token-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  .card-header {
    h3 {
      margin: 0 0 8px 0;
      color: #2c3e50;
    }

    p {
      margin: 0;
      color: #7f8c8d;
      font-size: 14px;
    }
  }
}

.test-section {
  margin-bottom: 20px;

  h4 {
    margin: 0 0 16px 0;
    color: #34495e;
    font-size: 16px;
  }

  .test-description {
    color: #7f8c8d;
    margin-bottom: 16px;
    line-height: 1.6;
  }
}

.test-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.log-item {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #e4e7ed;
  background-color: #fff;

  &.success {
    border-left-color: #67c23a;
  }

  &.error {
    border-left-color: #f56c6c;
  }

  &.info {
    border-left-color: #409eff;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.log-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .log-time {
    font-size: 12px;
    color: #909399;
  }
}

.log-content {
  font-size: 14px;

  p {
    margin: 4px 0;
    line-height: 1.4;
  }

  strong {
    color: #2c3e50;
  }
}

.log-headers {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  margin: 8px 0;
  overflow-x: auto;
}

.no-logs {
  text-align: center;
  color: #909399;
  padding: 40px 0;
  font-style: italic;
}

.el-alert {
  :deep(.el-alert__content) {
    p {
      margin: 4px 0;
      line-height: 1.6;
    }

    code {
      background-color: rgba(64, 158, 255, 0.1);
      color: #409eff;
      padding: 2px 4px;
      border-radius: 2px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
    }
  }
}
</style>
