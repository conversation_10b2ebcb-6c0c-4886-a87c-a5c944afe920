<template>
  <div class="work-assignment-management">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <h1 class="page-title">{{ $t('workAssignment.management.title') }}</h1>
      <div class="header-actions">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          @click="refreshData"
          :loading="loading"
        >
          {{ $t('workAssignment.common.refresh') }}
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-form :model="filters" inline>
        <el-form-item :label="$t('workAssignment.workOrder.workOrderNo')">
          <el-input
            v-model="filters.workOrderId"
            :placeholder="$t('workAssignment.placeholder.enterWorkOrderNo')"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item :label="$t('workAssignment.workOrder.status')">
          <el-select
            v-model="filters.status"
            :placeholder="$t('workAssignment.placeholder.selectStatus')"
            clearable
            style="width: 150px"
            :loading="dictionaryLoading"
          >
            <el-option
              v-for="status in workOrderStatusOptions"
              :key="status.code"
              :label="status.name"
              :value="status.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('workAssignment.workOrder.type')">
          <el-select
            v-model="filters.workOrderType"
            :placeholder="$t('workAssignment.placeholder.selectType')"
            clearable
            style="width: 150px"
            :loading="dictionaryLoading"
          >
            <el-option
              v-for="type in workOrderTypeOptions"
              :key="type.code"
              :label="type.name"
              :value="type.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('workAssignment.workOrder.priority')">
          <el-select
            v-model="filters.priority"
            :placeholder="$t('workAssignment.placeholder.selectPriority')"
            clearable
            style="width: 120px"
            :loading="dictionaryLoading"
          >
            <el-option
              v-for="priority in workOrderPriorityOptions"
              :key="priority.code"
              :label="priority.name"
              :value="priority.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('workAssignment.technician.assignedTechnician')">
          <el-select
            v-model="filters.assignedTechnicianId"
            :placeholder="$t('workAssignment.placeholder.selectTechnician')"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="technician in technicianList"
              :key="technician.technicianId"
              :label="technician.technicianName"
              :value="technician.technicianId"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            {{ $t('workAssignment.common.search') }}
          </el-button>
          <el-button @click="handleReset">
            {{ $t('workAssignment.common.reset') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 工单列表 -->
    <div class="table-section">
      <el-table
        :data="workOrderList"
        v-loading="loading"
        row-key="workOrderId"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column
          prop="workOrderId"
          :label="$t('workAssignment.workOrder.workOrderNo')"
          width="150"
          fixed="left"
        />

        <el-table-column
          prop="customerName"
          :label="$t('workAssignment.workOrder.customerName')"
          width="120"
        />

        <el-table-column
          :label="$t('workAssignment.workOrder.vehicleInfo')"
          width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.licensePlate }} {{ row.vehicleModel }}
          </template>
        </el-table-column>

        <el-table-column
          prop="workOrderType"
          :label="$t('workAssignment.workOrder.type')"
          width="100"
        >
          <template #default="{ row }">
            <el-tag :type="getWorkOrderTypeTagType(row.workOrderType)">
              {{ getWorkOrderTypeLabel(row.workOrderType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="priority"
          :label="$t('workAssignment.workOrder.priority')"
          width="100"
        >
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)">
              {{ getPriorityLabel(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="status"
          :label="$t('workAssignment.workOrder.status')"
          width="120"
        >
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('workAssignment.technician.assignedTechnician')"
          width="120"
        >
          <template #default="{ row }">
            <span v-if="row.assignedTechnicianName">{{ row.assignedTechnicianName }}</span>
            <span v-else class="text-muted">{{ $t('workAssignment.common.unassigned') }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="estimatedWorkHours"
          :label="$t('workAssignment.workOrder.estimatedDuration')"
          width="120"
        >
          <template #default="{ row }">
            {{ row.estimatedWorkHours }}{{ $t('workAssignment.common.hours') }}
          </template>
        </el-table-column>

        <el-table-column
          prop="creationTime"
          :label="$t('workAssignment.workOrder.createdAt')"
          width="180"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.creationTime) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="estimatedStartTime"
          :label="$t('workAssignment.workOrder.scheduledStartTime')"
          width="180"
        >
          <template #default="{ row }">
            <span v-if="row.estimatedStartTime">{{ formatDateTime(row.estimatedStartTime) }}</span>
            <span v-else class="text-muted">{{ $t('workAssignment.common.notScheduled') }}</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('workAssignment.common.actions')"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'pending_assign'"
              type="primary"
              size="small"
              @click="handleAssign(row)"
            >
              {{ $t('workAssignment.actions.assign') }}
            </el-button>

            <el-button
              v-if="row.status === 'pending_start' || row.status === 'in_progress'"
              type="warning"
              size="small"
              @click="handleReassign(row)"
            >
              {{ $t('workAssignment.actions.reassign') }}
            </el-button>

            <el-button
              type="primary"
              size="small"
              plain
              @click="handleViewDetail(row)"
            >
              {{ $t('workAssignment.common.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 分配工单对话框 -->
    <el-dialog
      v-model="assignmentDialog.visible"
      :title="$t('workAssignment.dialog.assignTitle')"
      width="600px"
    >
      <div v-if="assignmentDialog.workOrder">
        <p><strong>{{ $t('workAssignment.workOrder.workOrderNo') }}:</strong> {{ assignmentDialog.workOrder.workOrderId }}</p>
        <p><strong>{{ $t('workAssignment.workOrder.customerName') }}:</strong> {{ assignmentDialog.workOrder.customerName }}</p>
        <p><strong>{{ $t('workAssignment.workOrder.vehicleInfo') }}:</strong> {{ assignmentDialog.workOrder.licensePlate }} {{ assignmentDialog.workOrder.vehicleModel }}</p>
      </div>
      <template #footer>
        <el-button @click="assignmentDialog.visible = false">
          {{ $t('workAssignment.common.cancel') }}
        </el-button>
        <el-button type="primary" @click="handleAssignmentConfirm">
          {{ $t('workAssignment.common.confirm') }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 重新分配对话框 -->
    <el-dialog
      v-model="reassignmentDialog.visible"
      :title="$t('workAssignment.dialog.reassignTitle')"
      width="600px"
    >
      <div v-if="reassignmentDialog.workOrder">
        <p><strong>{{ $t('workAssignment.workOrder.workOrderNo') }}:</strong> {{ reassignmentDialog.workOrder.workOrderId }}</p>
        <p><strong>{{ $t('workAssignment.workOrder.customerName') }}:</strong> {{ reassignmentDialog.workOrder.customerName }}</p>
        <p><strong>{{ $t('workAssignment.technician.currentTechnician') }}:</strong> {{ reassignmentDialog.workOrder.assignedTechnicianName || $t('workAssignment.common.unassigned') }}</p>
      </div>
      <template #footer>
        <el-button @click="reassignmentDialog.visible = false">
          {{ $t('workAssignment.common.cancel') }}
        </el-button>
        <el-button type="primary" @click="handleReassignmentConfirm">
          {{ $t('workAssignment.common.confirm') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type {
  WorkOrderListItem,
  TechnicianInfo,
  WorkOrderStatus,
  WorkOrderType,
  WorkOrderPriority,
  WorkOrderListParams
} from '@/types/module'
import * as workAssignmentApi from '@/api/modules/workAssignment'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'

const { t } = useI18n()

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.WORK_ASSIGNMENT_STATUS,
  DICTIONARY_TYPES.WORK_ORDER_TYPE,
  DICTIONARY_TYPES.WORK_ORDER_PRIORITY
])

// 响应式数据
const loading = ref(false)
const workOrderList = ref<WorkOrderListItem[]>([])
const technicianList = ref<TechnicianInfo[]>([])

// 筛选条件
const filters = reactive<WorkOrderListParams>({
  workOrderId: '',
  status: undefined,
  workOrderType: undefined,
  priority: undefined,
  assignedTechnicianId: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 对话框状态
const assignmentDialog = reactive({
  visible: false,
  workOrder: null as WorkOrderListItem | null
})

const reassignmentDialog = reactive({
  visible: false,
  workOrder: null as WorkOrderListItem | null
})

// 计算属性 - 可用技师（排除休假的）
const availableTechnicians = computed(() => {
  return technicianList.value.filter(tech =>
    tech.currentStatus === 'available' || tech.currentStatus === 'busy'
  )
})

// 获取字典选项
const workOrderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.WORK_ASSIGNMENT_STATUS))
const workOrderTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.WORK_ORDER_TYPE))
const workOrderPriorityOptions = computed(() => getOptions(DICTIONARY_TYPES.WORK_ORDER_PRIORITY))

// 获取状态标签类型
const getStatusTagType = (status: WorkOrderStatus) => {
  const typeMap: Record<WorkOrderStatus, string> = {
    'draft': 'info',
    'pending_confirm': 'warning',
    'confirmed': 'info',
    'pending_assign': 'warning',
    'pending_start': 'info',
    'in_progress': 'primary',
    'pending_qc': 'warning',
    'pending_settle': 'warning',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: WorkOrderStatus) => {
  const statusMap: Record<WorkOrderStatus, string> = {
    'draft': 'draft',
    'pending_confirm': 'pendingConfirm',
    'confirmed': 'confirmed',
    'pending_assign': 'pendingAssignment',
    'pending_start': 'assigned',
    'in_progress': 'inProgress',
    'pending_qc': 'pendingQc',
    'pending_settle': 'pendingSettle',
    'completed': 'completed',
    'cancelled': 'cancelled'
  }
  return t(`workAssignment.status.${statusMap[status]}`)
}

// 获取工单类型标签类型
const getWorkOrderTypeTagType = (type: WorkOrderType) => {
  const typeMap: Record<WorkOrderType, string> = {
    'maintenance': 'success',
    'repair': 'warning',
    'insurance': 'info'
  }
  return typeMap[type] || 'info'
}

// 获取工单类型标签文本
const getWorkOrderTypeLabel = (type: WorkOrderType) => {
  return t(`workAssignment.type.${type}`)
}

// 获取优先级标签类型
const getPriorityTagType = (priority: WorkOrderPriority) => {
  const typeMap: Record<WorkOrderPriority, string> = {
    'normal': 'info',
    'urgent': 'danger'
  }
  return typeMap[priority] || 'info'
}

// 获取优先级标签文本
const getPriorityLabel = (priority: WorkOrderPriority) => {
  return t(`workAssignment.priority.${priority}`)
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...filters,
      page: pagination.current,
      pageSize: pagination.pageSize
    }

    const [workOrderRes, technicianRes] = await Promise.all([
      workAssignmentApi.getWorkOrderList(params),
      workAssignmentApi.getTechnicianList()
    ])

    workOrderList.value = workOrderRes.list
    pagination.total = workOrderRes.total
    technicianList.value = technicianRes
  } catch (error) {
    ElMessage.error(t('workAssignment.messages.loadDataFailed'))
    console.error('Load data failed:', error)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置筛选条件
const handleReset = () => {
  Object.assign(filters, {
    workOrderId: '',
    status: undefined,
    workOrderType: undefined,
    priority: undefined,
    assignedTechnicianId: ''
  })
  pagination.current = 1
  loadData()
}

// 分页相关
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.current = page
  loadData()
}

// 分配工单
const handleAssign = (workOrder: WorkOrderListItem) => {
  assignmentDialog.workOrder = workOrder
  assignmentDialog.visible = true
}

// 重新分配工单
const handleReassign = (workOrder: WorkOrderListItem) => {
  reassignmentDialog.workOrder = workOrder
  reassignmentDialog.visible = true
}

// 查看详情
const handleViewDetail = (workOrder: WorkOrderListItem) => {
  // TODO: 实现工单详情页面
  console.log('View detail:', workOrder)
}

// 分配确认
const handleAssignmentConfirm = async () => {
  try {
    // TODO: 实现分配逻辑
    ElMessage.success(t('workAssignment.messages.assignSuccess'))
    assignmentDialog.visible = false
    loadData()
  } catch (error) {
    ElMessage.error(t('workAssignment.messages.assignFailed'))
    console.error('Assignment failed:', error)
  }
}

// 重新分配确认
const handleReassignmentConfirm = async () => {
  try {
    // TODO: 实现重新分配逻辑
    ElMessage.success(t('workAssignment.messages.reassignSuccess'))
    reassignmentDialog.visible = false
    loadData()
  } catch (error) {
    ElMessage.error(t('workAssignment.messages.reassignFailed'))
    console.error('Reassignment failed:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.work-assignment-management {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .filter-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  .table-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .pagination-wrapper {
      padding: 20px;
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #ebeef5;
    }
  }

  .text-muted {
    color: #909399;
    font-style: italic;
  }
}

:deep(.el-table) {
  .el-table__header-wrapper {
    background-color: #f5f7fa;
  }

  .el-table__row:hover {
    background-color: #f0f9ff;
  }
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 20px;
  margin-bottom: 16px;
}
</style>
