<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 筛选查询区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <!-- 第一行 -->
          <el-col :span="6">
            <el-form-item :label="t('invoiceNumber')">
              <el-input
                v-model="searchParams.invoiceNumber"
                :placeholder="t('invoiceNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerName')">
              <el-input
                v-model="searchParams.customerName"
                :placeholder="t('customerNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerPhone')">
              <el-input
                v-model="searchParams.customerPhone"
                :placeholder="t('customerPhonePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerEmail')">
              <el-input
                v-model="searchParams.customerEmail"
                :placeholder="t('customerEmailPlaceholder')"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第二行 -->
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('orderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vin')">
              <el-input
                v-model="searchParams.vin"
                :placeholder="t('vinPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesType')">
              <el-select
                v-model="searchParams.salesType"
                :placeholder="tc('pleaseSelect')"
                clearable
                style="width: 100%"
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in salesTypeOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesStore')">
              <el-select
                v-model="searchParams.salesStore"
                :placeholder="tc('pleaseSelect')"
                clearable
                style="width: 100%"
                @change="handleStoreChange"
              >
                <el-option
                  v-for="item in storeOptions"
                  :key="item.code"
                  :label="item.dealerName"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第三行 -->
          <el-col :span="6">
            <el-form-item :label="t('salesConsultant')">
              <el-select
                v-model="searchParams.salesConsultant"
                :placeholder="tc('pleaseSelect')"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in consultantOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('invoiceDate')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 预留空间 -->
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button @click="resetSearch">
                {{ tc('reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能操作区域 -->
    <el-card class="mb-20 operation-card">
      <div class="operation-buttons">
        <el-button :icon="Download" @click="showExportDialog">
          {{ t('export') }}
        </el-button>
        <el-button
          :icon="Printer"
          @click="handleBatchPrint"
          :disabled="selectedRows.length === 0"
        >
          {{ t('batchPrint') }}
        </el-button>
      </div>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <div class="table-container">
        <el-table
          ref="tableRef"
          :data="tableData"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          style="width: 100%"
          :scroll="{ x: true }"
        >
          <el-table-column type="selection" width="55" fixed="left" />
          <el-table-column :label="tc('index')" type="index" width="70" />
          <el-table-column :label="t('invoiceNumber')" prop="invoiceNumber" min-width="130" show-overflow-tooltip />
          <el-table-column :label="t('invoiceDate')" prop="invoiceDate" min-width="110" />
          <el-table-column :label="t('orderNumber')" prop="orderNumber" min-width="130" show-overflow-tooltip />
          <el-table-column :label="t('customerName')" prop="customerName" min-width="110" show-overflow-tooltip />
          <el-table-column :label="t('customerPhone')" prop="customerPhone" min-width="130" />
          <el-table-column :label="t('customerEmail')" prop="customerEmail" min-width="160" show-overflow-tooltip />
          <el-table-column :label="t('customerAddress')" prop="customerAddress" min-width="180" show-overflow-tooltip />
          <el-table-column :label="t('vin')" prop="vin" min-width="160" show-overflow-tooltip />
          <el-table-column :label="t('model')" prop="model" min-width="90" />
          <el-table-column :label="t('variant')" prop="variant" min-width="140" show-overflow-tooltip />
          <el-table-column :label="t('color')" prop="color" min-width="90" />
          <el-table-column :label="t('salesStore')" prop="salesStore" min-width="110" show-overflow-tooltip />
          <el-table-column :label="t('salesConsultant')" prop="salesConsultant" min-width="110" show-overflow-tooltip />
          <el-table-column :label="t('paymentMethod')" prop="paymentMethod" min-width="90" />
          <el-table-column :label="t('financeCompany')" prop="financeCompany" min-width="130" show-overflow-tooltip />
          <el-table-column :label="t('loanAmount')" prop="loanAmount" min-width="110" align="right">
            <template #default="{ row }">
              <span v-if="row.loanAmount > 0">RM {{ row.loanAmount }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="t('invoiceAmount')" prop="invoiceAmount" min-width="110" align="right">
            <template #default="{ row }">
              RM {{ row.invoiceAmount }}
            </template>
          </el-table-column>
          <el-table-column :label="t('createdTime')" prop="createdTime" min-width="150" />

          <!-- 操作列 -->
          <el-table-column :label="tc('operations')" min-width="280" fixed="right" class-name="operations-column">
            <template #default="{ row }">
              <div class="operation-buttons">
                <el-button type="success" :icon="View" link @click="showDetail(row)" size="small">
                  {{ t('detail') }}
                </el-button>
                <el-button type="primary" :icon="Printer" link @click="handlePrint(row)" size="small">
                  {{ t('print') }}
                </el-button>
                <el-button type="primary" :icon="Message" link @click="showEmailDialog(row)" size="small">
                  {{ t('email') }}
                </el-button>
                <el-button type="info" :icon="Clock" link @click="showLogDialog(row)" size="small">
                  {{ t('log') }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          :layout="tc('paginationLayout')"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 发票详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="t('invoiceDetail')"
      width="900px"
      :destroy-on-close="true"
    >
      <div v-if="currentInvoice" class="invoice-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>{{ t('basicInfo') }}</h3>
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('invoiceNumber') }}:</label>
                <span>{{ currentInvoice.invoiceNumber }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('invoiceDate') }}:</label>
                <span>{{ currentInvoice.invoiceDate }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('companyInfo') }}:</label>
                <span>{{ currentInvoice.invoiceCompany }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('gstNumber') }}:</label>
                <span>{{ currentInvoice.gstNumber }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('sstNumber') }}:</label>
                <span>{{ currentInvoice.sstNumber }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('contactPhone') }}:</label>
                <span>{{ currentInvoice.contactPhone }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('contactEmail') }}:</label>
                <span>{{ currentInvoice.contactEmail }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('paymentMethod') }}:</label>
                <span>{{ currentInvoice.paymentMethod }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 客户信息 -->
        <div class="detail-section">
          <h3>{{ t('customerInfo') }}</h3>
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('customerName') }}:</label>
                <span>{{ currentInvoice.customerName }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('customerPhone') }}:</label>
                <span>{{ currentInvoice.customerPhone }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('customerEmail') }}:</label>
                <span>{{ currentInvoice.customerEmail }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('customerAddress') }}:</label>
                <span>{{ currentInvoice.customerAddress }}</span>
              </div>
            </el-col>
              <el-col :span="6">
                <div class="detail-item">
                  <label>{{ t('state') }}:</label>
                  <span>{{ currentInvoice.customerState }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="detail-item">
                  <label>{{ t('city') }}:</label>
                  <span>{{ currentInvoice.customerCity}}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="detail-item">
                  <label>{{ t('postcode') }}:</label>
                  <span>{{ currentInvoice.customerPostcode}}</span>
                </div>
              </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('orderNumber') }}:</label>
                <span>{{ currentInvoice.orderNumber}}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('deliveryNumber') }}:</label>
                <span> {{ currentInvoice.deliveryNumber}}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('salesConsultantId') }}:</label>
                <span>{{ currentInvoice.salesConsultant}}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('salesConsultant') }}:</label>
                <span>{{ currentInvoice.salesConsultant }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 车辆信息 -->
        <div class="detail-section">
          <h3>{{ t('vehicleInfo') }}</h3>
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('color') }}:</label>
                <span>{{ currentInvoice.color }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('tinNumber') }}:</label>
                <span>{{ currentInvoice.tinNumber }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('sstNumber') }}:</label>
                <span>{{ currentInvoice.sstNumber }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('modelCode') }}:</label>
                <span>{{ currentInvoice.modelCode  }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('modelDescription') }}:</label>
                <span>{{ currentInvoice.modelDescription  }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('engineNumber') }}:</label>
                <span>{{ currentInvoice.engineNumber  }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('engineDisplacement') }}:</label>
                <span>{{ currentInvoice.engineDisplacement  }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('chassisNumber') }}:</label>
                <span>{{ currentInvoice.chassisNumber  }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('vehicleRegistrationDate') }}:</label>
                <span>{{ currentInvoice.vehicleRegistrationDate  }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ tc('creator') }}:</label>
                <span>{{ currentInvoice.creator }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ tc('updater') }}:</label>
                <span>{{ currentInvoice.updater  }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ tc('updateTime') }}:</label>
                <span>{{ currentInvoice.updateTime }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 金融信息 -->
        <div class="detail-section">
          <h3>{{ t('financeInfo') }}</h3>
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('financeType') }}:</label>
                <span>{{ currentInvoice.financeType }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('financeCompany') }}:</label>
                <span>{{ currentInvoice.financeCompany }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('loanAmount') }}:</label>
                <span v-if="currentInvoice.loanAmount > 0">RM {{ currentInvoice.loanAmount }}</span>
                <span v-else>-</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('loanPeriod') }}:</label>
                <span>{{ currentInvoice.loanPeriod }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 保险信息详情 -->
        <div class="detail-section">
          <h3>{{ t('insuranceInfo') }}</h3>
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('insuranceCompany') }}:</label>
                <span>{{ currentInvoice.insuranceCompany }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('agentCode') }}:</label>
                <span>{{ currentInvoice.agentCode }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('policyNumber') }}:</label>
                <span>{{ currentInvoice.policyNumber }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <label>{{ t('issueDate') }}:</label>
                <span>{{ currentInvoice.issueDate }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 价格结构明细 -->
        <div class="detail-section">
          <h3>{{ t('priceStructureDetails') }}</h3>
          <el-row :gutter="16">
            <el-col :span="12">
              <div class="detail-item">
                <label>{{ t('vehicleSalesPrice') }}:</label>
                <span>{{ currentInvoice.vehicleSalesPrice }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>{{ t('licensePlateFee') }}:</label>
                <span>{{ currentInvoice.licensePlateFee }}</span>
              </div>
            </el-col>
          </el-row>

          <h4>{{ t('optionalAccessories') }}</h4>
          <el-table :data="currentInvoice.accessories || []" border style="width: 100%; margin-bottom: 20px;">
            <el-table-column :label="t('category')" prop="specification" min-width="100" />
            <el-table-column :label="t('accessoryName')" prop="accessoryName" min-width="150" />
            <el-table-column :label="t('unitPrice')" prop="unitPrice" min-width="100" align="right">
              <template #default="{ row }">RM {{ row.unitPrice }}</template>
            </el-table-column>
            <el-table-column :label="t('quantity')" prop="quantity" min-width="80" align="center" />
            <el-table-column :label="t('totalPrice')" prop="amount" min-width="120" align="right">
              <template #default="{ row }">RM {{ row.amount }}</template>
            </el-table-column>
          </el-table>
                      <div class="detail-item text-right">
              <label>{{ t('totalAccessoryAmount') }}:</label>
              <span>RM {{ currentInvoice.accessoryAmount }}</span>
            </div>

          <div class="detail-item text-right">
            <label>{{ t('subtotal') }}:</label>
            <span>RM {{ currentInvoice.invoiceAmount }}</span>
          </div>

          <h4>{{ t('otrRegistrationFees') }}</h4>
          <el-table :data="currentInvoice.otrFees || []" border style="width: 100%; margin-bottom: 20px;">
            <el-table-column :label="t('billNumber')" prop="feeCode" min-width="120" />
            <el-table-column :label="t('feeItem')" prop="feeType" min-width="150" />
            <el-table-column :label="t('feePrice')" prop="taxAmount" min-width="100" align="right">
              <template #default="{ row }">RM {{ row.taxAmount }}</template>
            </el-table-column>
            <el-table-column :label="t('effectiveDate')" prop="effectiveDate" min-width="120" />
            <el-table-column :label="t('expiryDate')" prop="expiryDate" min-width="120" />
          </el-table>
          <div class="detail-item text-right">
            <label>{{ t('totalOtrFeeAmount') }}:</label>
            <span>RM {{ currentInvoice.otrAmount }}</span>
          </div>

          <div class="detail-item text-right">
            <label>{{ t('insurancePremium') }}:</label>
            <span>RM {{ currentInvoice.insuranceAmount }}</span>
          </div>

          <div class="detail-item text-right">
            <label>{{ t('totalSalesPrice') }}:</label>
            <span>RM {{ currentInvoice.vehicleSalesPrice }}</span>
          </div>

          <div class="detail-item text-right">
            <label>{{ t('adjustmentAmount') }}:</label>
            <span>RM {{ currentInvoice.adjustmentAmount }}</span>
          </div>

          <div class="detail-item text-right">
            <label>{{ t('invoiceNetValue') }}:</label>
            <span>RM {{ currentInvoice.invoiceNetValue }}</span>
          </div>
        </div>

        <!-- 收据明细信息 -->
        <div class="detail-section">
          <h3>{{ t('receiptDetails') }}</h3>
          <el-table :data="currentInvoice.receipts || []" border style="width: 100%;">
            <el-table-column :label="t('receiptNumber')" prop="receiptNumber" min-width="120" />
            <el-table-column :label="t('businessType')" prop="receiptType" min-width="100" />
            <el-table-column :label="t('serialNumber')" prop="receiptNo" min-width="120" />
            <el-table-column :label="t('channel')" prop="paymentChannel" min-width="100" />
            <el-table-column :label="t('amount')" prop="paidAmount" min-width="100" align="right">
              <template #default="{ row }">RM {{ row.paidAmount }}</template>
            </el-table-column>
            <el-table-column :label="t('collectionType')" prop="collectionType" min-width="100" />
            <el-table-column :label="t('arrivalTime')" prop="arrivalTime" min-width="160" />
            <el-table-column :label="t('remark')" prop="remarks" min-width="150" />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 邮件发送确认弹窗 -->
    <el-dialog
      v-model="emailDialogVisible"
      :title="t('emailConfirm')"
      width="420px"
    >
      <div v-if="currentInvoice" class="email-confirm">
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="confirm-item">
              <label>{{ t('invoiceNumber') }}:</label>
              <span>{{ currentInvoice.invoiceNumber }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="confirm-item">
              <label>{{ t('orderNumber') }}:</label>
              <span>{{ currentInvoice.orderNumber }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="confirm-item">
              <label>{{ t('customerName') }}:</label>
              <span>{{ currentInvoice.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="confirm-item">
              <label>{{ t('customerEmail') }}:</label>
              <span>{{ currentInvoice.customerEmail }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="confirm-item">
              <label>{{ t('invoiceAmount') }}:</label>
              <span class="amount">RM {{ currentInvoice.invoiceAmount }}</span>
            </div>
          </el-col>
        </el-row>
        <div class="confirm-message">
          {{ t('confirmSendEmail') }}
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="emailDialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="handleSendEmail" :loading="emailSending">
            {{ tc('confirm') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导出配置弹窗 -->
    <el-dialog
      v-model="exportDialogVisible"
      :title="t('exportConfig')"
      width="420px"
    >
      <el-form :model="exportForm" label-position="top">
        <el-form-item :label="t('exportFormat')">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="excel">Excel</el-radio>
            <el-radio label="pdf">PDF</el-radio>
            <el-radio label="csv">CSV</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('exportScope')">
          <el-radio-group v-model="exportForm.scope">
            <el-radio label="current">{{ t('currentPage') }}</el-radio>
            <el-radio label="all">{{ t('allData') }}</el-radio>
            <el-radio label="filtered">{{ t('filteredData') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="exportDialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="handleExport" :loading="exporting">
            {{ tc('confirmExport') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 操作日志弹窗 -->
    <el-dialog
      v-model="logDialogVisible"
      :title="t('operationLog')"
      width="900px"
    >
      <el-table :data="logData" v-loading="logLoading">
        <el-table-column :label="tc('index')" type="index" width="60" />
        <el-table-column :label="t('operationType')" prop="operationType" width="120">
          <template #default="{ row }">
            {{ getOperationTypeText(row.operationType) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('operator')" prop="operator" width="120" />
        <el-table-column :label="t('operationTime')" prop="operationTime" width="160" />
        <el-table-column :label="t('operationResult')" prop="operationResult" width="100">
          <template #default="{ row }">
            <el-tag :type="row.operationResult === 'SUCCESS' ? 'success' : 'danger'">
              {{ row.operationResult }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('operationResult')" prop="operationDescription" min-width="150" />
        <el-table-column :label="t('remark')" prop="errorMessage" min-width="200">
          <template #default="{ row }">
            <span v-if="row.errorMessage" class="error-message">{{ row.errorMessage }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { ElMessage } from 'element-plus';
import { Search, Download, Printer, View, Message, Clock } from '@element-plus/icons-vue';

import {
  getInvoiceList,
  getInvoiceDetail,
  printInvoice,
  batchPrintInvoices,
  sendInvoiceEmail,
  exportInvoiceData,
  getStoreList,
  getConsultantList,
  getInvoiceOperationLogs
} from '@/api/modules/invoice';
import type {
  Invoice,
  InvoiceSearchParams,
  PaginationResponse,
  InvoiceDetail,
  InvoiceOperationLog
} from '@/types/invoice';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

// 国际化
const { t } = useModuleI18n('sales.invoice');
const { t: tc } = useModuleI18n('common');

// 使用字典数据
const {
  options: salesTypeOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.SALES_TYPE);

// 响应式数据
const loading = ref(false);
const tableData = ref<Invoice[]>([]);
const selectedRows = ref<Invoice[]>([]);
const currentInvoice = ref<InvoiceDetail | null>(null);

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
});

// 搜索参数
const searchParams = reactive<InvoiceSearchParams>({
  invoiceNumber: '',
  customerName: '',
  customerPhone: '',
  customerEmail: '',
  orderNumber: '',
  vin: '',
  salesType: '',
  salesStore: '',
  salesConsultant: '',
  invoiceDateStart: '',
  invoiceDateEnd: '',
  pageNum: 1,
  pageSize: 20
});

// 日期范围
const dateRange = ref<[string, string] | null>(null);

// 下拉选项
const storeOptions = ref<{ label: string; value: string }[]>([]);
const consultantOptions = ref<{ label: string; value: string }[]>([]);

// 弹窗状态
const detailDialogVisible = ref(false);
const emailDialogVisible = ref(false);
const exportDialogVisible = ref(false);
const logDialogVisible = ref(false);

// 其他状态
const emailSending = ref(false);
const exporting = ref(false);
const logLoading = ref(false);
const logData = ref<InvoiceOperationLog[]>([]);

// 导出表单
const exportForm = reactive({
  format: 'excel',
  scope: 'current'
});

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchParams.invoiceDateStart = newVal[0];
    searchParams.invoiceDateEnd = newVal[1];
  } else {
    searchParams.invoiceDateStart = '';
    searchParams.invoiceDateEnd = '';
  }
});

// 获取发票列表
const fetchInvoiceList = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchParams,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    };

    const response: PaginationResponse<Invoice> = await getInvoiceList(params);
    tableData.value = response.records;
    pagination.total = response.total;
  } catch (error) {
    ElMessage.error('获取发票列表失败');
    console.error('获取发票列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取门店列表
const fetchStoreList = async () => {
  try {
    const stores = await getStoreList();
    storeOptions.value = stores;
  } catch (error) {
    console.error('获取门店列表失败:', error);
  }
};

// 获取销售顾问列表
const fetchConsultantList = async (storeId?: string) => {
  try {
    const consultants = await getConsultantList(storeId);
    consultantOptions.value = consultants;
  } catch (error) {
    console.error('获取销售顾问列表失败:', error);
  }
};

// 门店变化处理
const handleStoreChange = (value: string) => {
  searchParams.salesConsultant = '';
  if (value) {
    fetchConsultantList(value);
  } else {
    consultantOptions.value = [];
  }
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  fetchInvoiceList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    invoiceNumber: '',
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    orderNumber: '',
    vin: '',
    salesType: '',
    salesStore: '',
    salesConsultant: '',
    invoiceDateStart: '',
    invoiceDateEnd: '',
    pageNum: 1,
    pageSize: 20
  });
  dateRange.value = null;
  consultantOptions.value = [];
  handleSearch();
};

// 分页变化
const handlePageChange = (page: number) => {
  pagination.pageNum = page;
  fetchInvoiceList();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;
  fetchInvoiceList();
};

// 选择变化
const handleSelectionChange = (selection: Invoice[]) => {
  selectedRows.value = selection;
};

// 显示详情
const showDetail = async (row: Invoice) => {
  try {
    const fetchedInvoice = await getInvoiceDetail(row.id);
    // 获取国际化配置的默认值
    detailDialogVisible.value = true;
    currentInvoice.value = fetchedInvoice;

  } catch (error) {
    ElMessage.error('获取发票详情失败');
    console.error('获取发票详情失败:', error);
  }
};

// 打印发票
const handlePrint = async (row: Invoice) => {
  try {
    await printInvoice(row.id);
    ElMessage.success(t('printSuccess'));
  } catch (error) {
    ElMessage.error('打印失败');
    console.error('打印失败:', error);
  }
};

// 批量打印
const handleBatchPrint = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning(t('pleaseSelectRecords'));
    return;
  }

  try {
    const ids = selectedRows.value.map(row => row.id);
    await batchPrintInvoices(ids);
    ElMessage.success(t('batchPrintSuccess'));
  } catch (error) {
    ElMessage.error('批量打印失败');
    console.error('批量打印失败:', error);
  }
};

// 显示邮件弹窗
const showEmailDialog = async (row: Invoice) => {
  try {
    currentInvoice.value = await getInvoiceDetail(row.id);
    emailDialogVisible.value = true;
  } catch (error) {
    ElMessage.error('获取发票详情失败，无法发送邮件');
    console.error('获取发票详情失败:', error);
  }
};

// 发送邮件
const handleSendEmail = async () => {
  if (!currentInvoice.value) return;

  emailSending.value = true;
  try {
    await sendInvoiceEmail(currentInvoice.value.id, currentInvoice.value.customerEmail);
    ElMessage.success(t('emailSentSuccess'));
    emailDialogVisible.value = false;
  } catch (error) {
    ElMessage.error('邮件发送失败');
    console.error('邮件发送失败:', error);
  } finally {
    emailSending.value = false;
  }
};

// 显示导出弹窗
const showExportDialog = () => {
  exportDialogVisible.value = true;
};

// 导出数据
const handleExport = async () => {
  exporting.value = true;
  try {
    const params = {
      format: exportForm.format,
      scope: exportForm.scope,
      searchParams: exportForm.scope === 'filtered' ? searchParams : undefined
    };

    await exportInvoiceData(params);
    ElMessage.success(t('exportSuccess'));
    exportDialogVisible.value = false;
  } catch (error) {
    ElMessage.error('导出失败');
    console.error('导出失败:', error);
  } finally {
    exporting.value = false;
  }
};

// 获取操作类型显示文本
const getOperationTypeText = (operationType: string) => {
  if (!operationType) return operationType;

  const type = operationType.toUpperCase();
  switch (type) {
    case 'EMAIL_SEND':
      return '邮件发送';
    case 'PRINT':
      return '打印';
    case 'VIEW_DETAIL':
      return '查看详情';
    case 'EXPORT_DATA':
      return '导出数据';
    default:
      return operationType;
  }
};

// 显示日志弹窗
const showLogDialog = async (row: Invoice) => {
  try {
    logDialogVisible.value = true;
    logLoading.value = true;

    // 获取操作日志数据
    const logs = await getInvoiceOperationLogs(row.id);
    logData.value = logs;
  } catch (error) {
    ElMessage.error('获取操作日志失败');
    console.error('获取操作日志失败:', error);
  } finally {
    logLoading.value = false;
  }
};

// 页面初始化
onMounted(() => {
  fetchInvoiceList();
  fetchStoreList();
});
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card, .operation-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.operation-buttons {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

// 表格容器样式
.table-container {
  width: 100%;
  overflow-x: auto;
}

// 表格自适应样式
:deep(.el-table) {
  width: 100%;
  min-width: 100%;

  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }

  // 操作列按钮优化
  .operations-column {
    .cell {
      padding: 8px 12px;
    }
  }
}

// 操作按钮样式优化
.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;

  .el-button {
    margin: 0;
    padding: 4px 8px;
    font-size: 12px;

    &.is-link {
      padding: 4px 6px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .table-container {
    :deep(.el-table) {
      font-size: 12px;

      .el-table__cell {
        padding: 8px 6px;
      }
    }
  }

  .operation-buttons {
    .el-button {
      font-size: 11px;
      padding: 2px 4px;
    }
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }

  .table-container {
    :deep(.el-table) {
      font-size: 11px;

      .el-table__cell {
        padding: 6px 4px;
      }
    }
  }
}

.invoice-detail {
  .detail-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e8e8e8;
      color: #333;
    }

    h4 {
      margin-top: 20px;
      margin-bottom: 10px;
      color: #555;
    }

    .detail-item {
      margin-bottom: 12px;

      label {
        display: inline-block;
        min-width: 80px;
        font-weight: 600;
        color: #666;
      }

      span {
        color: #333;
      }
    }

    .text-right {
      text-align: right;
    }
  }
}

.email-confirm {
  .confirm-item {
    margin-bottom: 15px;

    label {
      display: inline-block;
      min-width: 80px;
      font-weight: 600;
      color: #666;
    }

    span {
      color: #333;

      &.amount {
        font-weight: 600;
        color: #f56c6c;
        font-size: 16px;
      }
    }
  }

  .confirm-message {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    color: #1f2328;
    text-align: center;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;

  .el-button {
    margin-left: 10px;
  }
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
}
</style>
