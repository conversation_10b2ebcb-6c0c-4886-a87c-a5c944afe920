<!--
  交付管理页面
  已完成国际化改造，使用 useModuleI18n('delivery') 和 useModuleI18n('common')
  支持中英文切换
-->
<template>
  <div class="page-container">
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('searchTitle') }}</span>
        </div>
      </template>

      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('deliveryNumber')">
              <el-input v-model="searchParams.deliveryNumber" :placeholder="t('deliveryNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input v-model="searchParams.orderNumber" :placeholder="t('orderNoPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerName')">
              <el-input v-model="searchParams.customerName" :placeholder="t('customerNamePlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerPhone')">
              <el-input v-model="searchParams.customerPhone" :placeholder="t('customerMobilePlaceholder')" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('vin')">
              <el-input v-model="searchParams.vin" :placeholder="t('vinPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderStatus')">
              <el-select v-model="searchParams.orderStatus" :placeholder="t('orderStatusPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in orderStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('dealerStore')">
              <el-select v-model="searchParams.dealerStore" :placeholder="t('dealerStorePlaceholder')" clearable>
                <!-- 这里需要根据实际数据动态渲染选项 -->
                <el-option label="门店A" value="store_a" />
                <el-option label="门店B" value="store_b" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesConsultant')">
              <el-input v-model="searchParams.salesConsultant" :placeholder="t('salesmanPlaceholder')" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('deliveryStatus')">
              <el-select v-model="searchParams.deliveryStatus" :placeholder="t('deliveryStatusPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in deliveryStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerConfirmed')">
              <el-select v-model="searchParams.customerConfirmed" :placeholder="t('customerConfirmedPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in booleanOptions"
                  :key="option.code"
                  :value="option.code === 'true'"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('confirmationType')">
              <el-select v-model="searchParams.confirmationType" :placeholder="t('confirmationTypePlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in confirmationMethodOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('deliveryTime')">
              <el-date-picker
                v-model="searchParams.deliveryTimeRange"
                type="daterange"
                range-separator="-"
                :start-placeholder="t('startDate')"
                :end-placeholder="t('endDate')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('customerConfirmTime')">
              <el-date-picker
                v-model="searchParams.customerConfirmTimeRange"
                type="daterange"
                range-separator="-"
                :start-placeholder="t('customerConfirmTimeStartPlaceholder')"
                :end-placeholder="t('customerConfirmTimeEndPlaceholder')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="18" class="search-buttons">
            <el-button type="primary" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="handleReset">{{ tc('reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operation-card mb-20">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-button v-if="canExport" type="success" @click="handleExport">{{ tc('export') }}</el-button>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('listTitle') }}</span>
          <span class="total-count">{{ t('totalCount', { count: total }) }}</span>
        </div>
      </template>

      <el-table :data="tableData" :loading="loading" border style="width: 100%">
        <el-table-column type="index" :label="tc('index')" width="80" />
        <el-table-column prop="deliveryNumber" :label="t('deliveryNumber')" width="140" />
        <el-table-column prop="orderNumber" :label="t('orderNumber')" width="140" />
        <el-table-column prop="customerName" :label="t('customerName')" width="120" />
        <el-table-column prop="customerPhone" :label="t('customerPhone')" width="130" />
        <el-table-column prop="vin" :label="t('vin')" width="180" />
        <el-table-column prop="model" :label="t('model')" width="100" />
        <el-table-column prop="variant" :label="t('variant')" width="120" />
        <el-table-column prop="color" :label="t('color')" width="80" />
        <el-table-column prop="dealerStore" :label="t('dealerStore')" width="100" />
        <el-table-column prop="salesConsultant" :label="t('salesConsultant')" width="100" />
        <el-table-column prop="orderStatus" :label="t('orderStatus')" width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderStatusTagType(row.orderStatus)">
              {{ getOrderStatusText(row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceTime" :label="t('invoiceTime')" width="150" />
        <el-table-column prop="deliveryStatus" :label="t('deliveryStatus')" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.deliveryStatus)">
              {{ getStatusText(row.deliveryStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="customerConfirmed" :label="t('customerConfirmed')" width="100">
          <template #default="{ row }">
            {{ row.customerConfirmed ? tc('yes') : tc('no') }}
          </template>
        </el-table-column>
        <el-table-column prop="confirmationType" :label="t('confirmationType')" width="100">
          <template #default="{ row }">
            {{ row.confirmationType ? getConfirmationTypeText(row.confirmationType) : 'N/A' }}
          </template>
        </el-table-column>
        <el-table-column prop="customerConfirmTime" :label="t('customerConfirmTime')" width="150" />
        <el-table-column prop="deliveryTime" :label="t('actualDeliveryDate')" width="150" />
<!--        <el-table-column prop="createTime" :label="tc('createTime')" width="150" />-->
<!--        <el-table-column prop="updateTime" :label="tc('updateTime')" width="150" />-->
<!--        <el-table-column prop="creator" :label="tc('creator')" width="100" />-->
<!--        <el-table-column prop="updater" :label="tc('updater')" width="100" />-->
        <el-table-column prop="deliveryNotes" :label="t('deliveryNotes')" width="150" show-overflow-tooltip />
        <el-table-column prop="signaturePhoto" :label="t('signaturePhoto')" width="120">
          <template #default="{ row }">
            <div v-if="row.signaturePhoto">
              <el-image
                :src="row.signaturePhoto"
                :preview-src-list="[row.signaturePhoto]"
                fit="cover"
                style="width: 40px; height: 30px; cursor: pointer;"
                :alt="t('signaturePhoto')"
              />
            </div>
            <span v-else class="text-gray-400">{{ tc('none') }}</span>
          </template>
        </el-table-column>

        <el-table-column :label="tc('actions')" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              link
              type="success"
              @click="handleDetail(row)"
              :loading="detailLoading"
            >
              {{ tc('detail') }}
            </el-button>
            <el-button link type="info" @click="handlePrint()">{{ tc('print') }}</el-button>
            <el-button
              v-if="canSubmitConfirm(row)"
              link
              type="primary"
              @click="handleSubmitConfirm(row)"
            >
              {{ t('submitConfirm') }}
            </el-button>
            <el-button
              v-if="canDeliveryConfirm(row)"
              link
              type="warning"
              @click="handleDeliveryConfirm(row)"
            >
              {{ t('deliveryConfirm') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 提交确认弹窗 -->
    <DeliverySubmitDialog
      v-model="submitDialogVisible"
      :order-data="selectedOrder"
      @confirm="handleSubmitConfirmAction"
    />

    <!-- 交车确认弹窗 -->
    <DeliveryConfirmDialog
      v-model="confirmDialogVisible"
      :order-data="selectedOrder"
      @confirm="handleDeliveryConfirmAction"
    />

    <!-- 详情弹窗 -->
    <DeliveryDetailDialog
      v-model="detailDialogVisible"
      :order-data="selectedOrder"
      :loading="detailLoading"
    />

    <!-- 导出设置弹窗 -->
    <DeliveryExportDialog
      v-model="exportDialogVisible"
      @confirm="handleExportConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { getDeliveryOrderList, getDeliveryOrderDetail, submitConfirm, deliveryConfirm, exportDeliveryData } from '@/api/modules/delivery'

import type {
  DeliveryOrderItem,
  DeliveryOrderParams,
  SubmitConfirmRequest,
  DeliveryStatus,
  OrderStatus,
  UserRole,
  ConfirmationType,
  ExportSettings
} from '@/types/module'
import DeliverySubmitDialog from './components/DeliverySubmitDialog.vue'
import DeliveryConfirmDialog from './components/DeliveryConfirmDialog.vue'
import DeliveryDetailDialog from './components/DeliveryDetailDialog.vue'
import DeliveryExportDialog from './components/DeliveryExportDialog.vue'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'

const { t } = useModuleI18n('sales.delivery')
const { tc } = useModuleI18n('common')

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.DELIVERY_STATUS,
  DICTIONARY_TYPES.CONFIRMATION_METHOD,
  DICTIONARY_TYPES.BOOLEAN_TYPE
])

// 获取字典选项
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS))
const deliveryStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.DELIVERY_STATUS))
const confirmationMethodOptions = computed(() => getOptions(DICTIONARY_TYPES.CONFIRMATION_METHOD))
const booleanOptions = computed(() => getOptions(DICTIONARY_TYPES.BOOLEAN_TYPE))

// 当前用户角色 - 实际项目中应从用户信息获取
const currentUserRole = ref<UserRole>('sales_manager') // 临时设置为 sales_manager 以显示导出按钮

// 搜索参数
const searchParams = reactive<DeliveryOrderParams>({
  deliveryNumber: '',
  orderNumber: '',
  customerName: '',
  customerPhone: '',
  vin: '',
  model: '',
  variant: '',
  color: '',
  salesConsultant: '',
  dealerStore: undefined,
  deliveryStatus: undefined,
  orderStatus: undefined,
  customerConfirmed: undefined,
  confirmationType: undefined,
  deliveryTimeRange: undefined,
  customerConfirmTimeRange: undefined
})

// 表格数据
const tableData = ref<DeliveryOrderItem[]>([])
const loading = ref(false)
const total = ref(0)

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 弹窗控制
const submitDialogVisible = ref(false)
const confirmDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const exportDialogVisible = ref(false)
const selectedOrder = ref<DeliveryOrderItem | null>(null)
const detailLoading = ref(false) // 添加详情加载状态

// 权限控制
const canExport = computed(() => currentUserRole.value === 'sales_manager')

const canSubmitConfirm = (row: DeliveryOrderItem) => {
  return row.deliveryStatus === 'pending_delivery'
}

const canDeliveryConfirm = (row: DeliveryOrderItem) => {
  return row.deliveryStatus === 'pending_confirm'
}

// 状态标签类型
const getStatusTagType = (status: DeliveryStatus) => {
  const statusMap = {
    pending_delivery: 'warning',
    pending_confirm: 'primary',
    delivered: 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: DeliveryStatus) => {
  const statusTextMap = {
    pending_delivery: t('statusPending'),
    pending_confirm: t('statusConfirming'),
    delivered: t('statusCompleted')
  }
  return statusTextMap[status] || status
}

const getOrderStatusTagType = (status: OrderStatus) => {
  const statusMap = {
    normal: 'success',
    cancelled: 'danger',
    pending_allocation: 'info',
    allocating: 'warning',
    allocated: 'primary',
    pending_delivery: 'warning',
    delivered: 'success'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status: OrderStatus) => {
  const statusTextMap: Record<string, string> = {
    normal: t('orderStatusNormal'),
    cancelled: t('orderStatusCancelled'),
    pending_allocation: t('orderStatusPendingAllocation'),
    allocating: t('orderStatusAllocating'),
    allocated: t('orderStatusAllocated'),
    pending_delivery: t('orderStatusPendingDelivery'),
    delivered: t('orderStatusDelivered')
  }
  return statusTextMap[status] || status
}

const getConfirmationTypeText = (type: ConfirmationType) => {
  const typeTextMap = {
    app: t('confirmationTypeApp'),
    offline: t('confirmationTypeOffline')
  }
  return typeTextMap[type] || type
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: DeliveryOrderParams = {
      ...searchParams,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    }

    const response = await getDeliveryOrderList(params)
    // 修复类型错误：使用 list 属性而不是 records
    tableData.value = response.records || []
    total.value = response.total
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error(t('fetchDataFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key as keyof DeliveryOrderParams] = undefined
  })
  // 确保重置时间范围数组
  searchParams.deliveryTimeRange = undefined;
  searchParams.customerConfirmTimeRange = undefined;
  pagination.currentPage = 1
  fetchData()
}

// 导出
const handleExport = () => {
  exportDialogVisible.value = true
}

// 分页
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchData()
}

// 操作方法
const handleDetail = async (row: DeliveryOrderItem) => {
  try {
    detailLoading.value = true
    // 请求后端接口获取详细数据
    const detailData = await getDeliveryOrderDetail(row.deliveryNumber)
    console.log('detailData', detailData)
    if (detailData) {
      selectedOrder.value = detailData
      detailDialogVisible.value = true
    } else {
      ElMessage.error(t('detailNotFound'))
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : t('fetchDetailFailed')
    ElMessage.error(errorMessage)
  } finally {
    detailLoading.value = false
  }
}

const handlePrint = () => {
  // 打印功能实现
  ElMessage.info(t('printFeatureNotImplemented'))
}

const handleSubmitConfirm = (row: DeliveryOrderItem) => {
  selectedOrder.value = row
  submitDialogVisible.value = true
}

const handleDeliveryConfirm = (row: DeliveryOrderItem) => {
  selectedOrder.value = row
  confirmDialogVisible.value = true
}

const handleSubmitConfirmAction = async (data: SubmitConfirmRequest) => {
  try {
    await submitConfirm(data)
    ElMessage.success(t('submitConfirmSuccess'))
    fetchData()
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : t('submitConfirmFailed')
    ElMessage.error(errorMessage)
  }
}

const handleDeliveryConfirmAction = async () => {
  try {
    // 签字照片上传已经在 DeliveryConfirmDialog 中调用了 delivery-confirm 接口
    // 这里不需要再调用 deliveryConfirm，避免重复调用
    ElMessage.success(t('deliveryConfirmSuccess'))
    fetchData()
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : t('deliveryConfirmFailed')
    ElMessage.error(errorMessage)
  }
}

const handleExportConfirm = async (exportSettings: ExportSettings) => {
  try {
    const params: DeliveryOrderParams = {
      ...searchParams,
      page: 1, // 导出通常不分页，这里可以根据导出范围调整
      pageSize: total.value // 如果是全部数据，则 pageSize 为总数
    }
    await exportDeliveryData(exportSettings, params)
    ElMessage.success(t('exportSuccess'))
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : t('exportFailed')
    ElMessage.error(errorMessage)
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.page-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.search-card, .table-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.total-count {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.search-buttons {
  text-align: right;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
