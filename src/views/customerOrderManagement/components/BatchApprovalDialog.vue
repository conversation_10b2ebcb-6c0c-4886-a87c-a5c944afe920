<template>
  <el-dialog
    v-model="currentVisible"
    :title="$t('approval.dialogs.batchApprovalTitle')"
    width="600px"
    :before-close="handleClose"
    class="batch-approval-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item :label="$t('approval.batchActions.selectedCount', { count: approvalIds.length })" v-if="approvalIds.length > 0">
        <el-tag type="info">{{ approvalIds.length }} {{ $t('approval.batchActions.selectedCount').split(' ')[1] }}</el-tag>
      </el-form-item>
      <el-form-item :label="$t('approval.approvalResult')" prop="result">
        <el-radio-group v-model="formData.result">
          <el-radio label="approved">{{ $t('approval.result.approved') }}</el-radio>
          <el-radio label="rejected">{{ $t('approval.result.rejected') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="formData.result === 'rejected'"
        :label="$t('approval.reason')"
        prop="reason"
      >
        <el-input
          v-model="formData.reason"
          type="textarea"
          :rows="3"
          :placeholder="$t('approval.placeholders.enterReason')"
        ></el-input>
      </el-form-item>
      <el-form-item :label="$t('approval.comments')" prop="comments">
        <el-input
          v-model="formData.comments"
          type="textarea"
          :rows="3"
          :placeholder="$t('approval.placeholders.enterComments')"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit(formRef)">
          {{ $t('common.confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, reactive, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { batchApprovalAction } from "@/api/modules/approval";
import type { BatchApprovalRequest } from "@/types/module";
import { useI18n } from "vue-i18n";

const props = defineProps<{
  visible: boolean;
  approvalIds: string[];
}>();

const emit = defineEmits(["update:visible", "close", "success"]);

const { t } = useI18n();

const formRef = ref<FormInstance>();
const loading = ref(false);
const formData = reactive<BatchApprovalRequest>({
  approvalIds: [],
  result: "approved",
  comments: "",
  reason: "",
  approverId: "current_user_id", // TODO: Replace with actual current user ID
});

const rules = reactive<FormRules>({
  result: [{ required: true, message: "请选择审批结果", trigger: "change" }],
  reason: [
    {
      required: true,
      message: t("approval.dialogs.reasonRequired"),
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (formData.result === "rejected" && !value) {
          callback(new Error(t("approval.dialogs.reasonRequired")));
        } else {
          callback();
        }
      },
    },
  ],
});

const currentVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      formData.approvalIds = [...props.approvalIds];
      formData.result = "approved";
      formData.comments = "";
      formData.reason = "";
    }
  }
);

const handleSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid) => {
    if (valid) {
      if (formData.approvalIds.length === 0) {
        ElMessage.warning(t("approval.dialogs.noItemsSelected"));
        return;
      }
      loading.value = true;
      try {
        await batchApprovalAction(formData);
        ElMessage.success(
          formData.result === "approved"
            ? t("approval.dialogs.batchApprovalSuccess")
            : t("approval.dialogs.batchRejectSuccess")
        );
        emit("success");
        handleClose();
      } catch (error: any) {
        ElMessage.error(t("common.operationFailed") + ": " + error.message);
      } finally {
        loading.value = false;
      }
    }
  });
};

const handleClose = () => {
  currentVisible.value = false;
  formRef.value?.resetFields();
  emit("close");
};
</script>

<style scoped>
.batch-approval-dialog .el-dialog__body {
  padding-top: 10px;
}
</style> 