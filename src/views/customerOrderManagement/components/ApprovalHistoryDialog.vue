<!--
  审批历史对话框组件
  已完成国际化改造，使用 useModuleI18n('orderApproval') 和 useModuleI18n('common')
  支持中英文切换
-->
<template>
  <el-dialog
    v-model="currentVisible"
    :title="t('dialogs.historyTitle')"
    width="700px"
    :before-close="handleClose"
    class="approval-history-dialog"
  >
    <div v-loading="loading">
      <el-table :data="historyList" border style="width: 100%" max-height="500">
        <el-table-column prop="approverName" :label="t('approver')" width="150"></el-table-column>
        <el-table-column prop="approvalTime" :label="t('approvalTime')" width="180"></el-table-column>
        <el-table-column prop="status" :label="t('approvalResult')" width="120">
          <template #default="{ row }">
            <el-tag :type="row.status === 'approved' ? 'success' : (row.status === 'rejected' || row.status === 'timeout_rejected' ? 'danger' : '')">
              {{ t(`result.${row.status === 'timeout_rejected' ? 'timeout' : row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="comments" :label="t('comments')"></el-table-column>
      </el-table>
      <div v-if="historyList.length === 0" class="empty-history-tip">{{ t('emptyHistory') }}</div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import { getApprovalHistory } from "@/api/modules/approval";
import type { ApprovalHistoryItem } from "@/types/module";
import { ElMessage } from "element-plus";
import { useModuleI18n } from "@/composables/useModuleI18n";

const props = defineProps<{
  visible: boolean;
  approvalId: string | null;
}>();

const emit = defineEmits(["update:visible", "close"]);

const { t } = useModuleI18n('sales.orderApproval');
const { tc } = useModuleI18n('common');

const loading = ref(false);
const historyList = ref<ApprovalHistoryItem[]>([]);

const currentVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

watch(
  () => props.visible,
  async (newVal) => {
    if (newVal && props.approvalId) {
      await fetchApprovalHistory(props.approvalId);
    } else if (!newVal) {
      historyList.value = [];
    }
  }
);

const fetchApprovalHistory = async (id: string) => {
  loading.value = true;
  try {
    const res = await getApprovalHistory(id);
    historyList.value = res;
  } catch (error: unknown) {
    ElMessage.error(tc("fetchFailed") + ": " + (error as Error).message);
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  currentVisible.value = false;
  emit("close");
};
</script>

<style scoped>
.approval-history-dialog .el-dialog__body {
  padding-top: 10px;
}

.empty-history-tip {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 20px 0;
}
</style>
