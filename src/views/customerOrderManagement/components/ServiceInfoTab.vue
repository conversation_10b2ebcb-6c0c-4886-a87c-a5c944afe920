<template>
  <div class="service-info-tab">
    <!-- 权益管理 -->
    <div class="rights-management">
      <div class="header">
        <el-button type="primary" @click="showRightsDialog">新增权益</el-button>
      </div>

      <!-- 权益列表 -->
      <el-table :data="props.formData.rights" style="width: 100%">
        <el-table-column prop="code" label="权益代码" width="150" />
        <el-table-column prop="name" label="权益名称" width="200" />
        <el-table-column prop="mode" label="权益模式" width="150" />
        <el-table-column prop="amount" label="优惠金额">
          <template #default="{ row }">
            RM {{ row.amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="生效日期" width="180" />
        <el-table-column prop="endDate" label="终止日期" width="180" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              link 
              type="danger" 
              @click="handleDeleteRight(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 权益优惠总金额 -->
      <div class="rights-total">
        <span>权益优惠总金额：</span>
        <span class="amount">RM {{ props.formData.rightsTotalAmount.toFixed(2) }}</span>
      </div>
    </div>

    <!-- 选择权益弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择权益"
      width="80%"
      :close-on-click-modal="false"
    >
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="权益代码">
            <el-input v-model="searchForm.code" placeholder="请输入权益代码" />
          </el-form-item>
          <el-form-item label="权益名称">
            <el-input v-model="searchForm.name" placeholder="请输入权益名称" />
          </el-form-item>
          <el-form-item>
            <el-button @click="resetSearch">重置</el-button>
            <el-button type="primary" @click="handleSearch">查询</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 权益选择表格 -->
      <el-table
        :data="availableRights"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="权益代码" width="150" />
        <el-table-column prop="name" label="权益名称" width="200" />
        <el-table-column prop="mode" label="权益模式" width="150" />
        <el-table-column prop="amount" label="权益优惠价格">
          <template #default="{ row }">
            RM {{ row.amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="权益生效日期" width="180" />
        <el-table-column prop="endDate" label="权益终止日期" width="180" />
      </el-table>

      <!-- 弹窗底部按钮 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">返回</el-button>
          <el-button type="primary" @click="handleAddRights">
            添加
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

interface Right {
  code: string;
  name: string;
  mode: string;
  amount: number;
  startDate: string;
  endDate: string;
}

interface FormData {
  rights: Right[];
  rightsTotalAmount: number;
}

const props = defineProps<{
  formData: FormData;
}>();

const emit = defineEmits<{
  'update:rights': [rights: Right[]];
}>();

// 弹窗显示控制
const dialogVisible = ref(false);

// 搜索表单
const searchForm = ref({
  code: '',
  name: '',
});

// 可选权益列表（模拟数据）
const availableRights = ref<Right[]>([
  {
    code: 'RIGHT001',
    name: '首购优惠',
    mode: '固定金额',
    amount: 1000,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
  },
  // 更多权益...
]);

// 已选择的权益
const selectedRights = ref<Right[]>([]);

// 显示权益选择弹窗
const showRightsDialog = () => {
  dialogVisible.value = true;
};

// 处理搜索
const handleSearch = () => {
  // TODO: 实现权益搜索逻辑
};

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    code: '',
    name: '',
  };
};

// 处理表格选择变化
const handleSelectionChange = (selection: Right[]) => {
  selectedRights.value = selection;
};

// 处理添加权益
const handleAddRights = () => {
  if (selectedRights.value.length === 0) {
    ElMessage.warning('请选择要添加的权益');
    return;
  }

  // 添加新权益并更新总金额
  const newRights = [...props.formData.rights, ...selectedRights.value];
  const newTotalAmount = newRights.reduce((sum, right) => sum + right.amount, 0);

  emit('update:rights', newRights);
  
  // 关闭弹窗并清空选择
  dialogVisible.value = false;
  selectedRights.value = [];
};

// 处理删除权益
const handleDeleteRight = (right: Right) => {
  const newRights = props.formData.rights.filter(r => r.code !== right.code);
  emit('update:rights', newRights);
};
</script>

<style scoped>
.service-info-tab {
  padding: 20px 0;
}

.header {
  margin-bottom: 20px;
}

.rights-total {
  margin-top: 20px;
  text-align: right;
  padding: 10px;
  background-color: #f5f7fa;

  .amount {
    font-weight: bold;
    color: #409EFF;
    margin-left: 10px;
  }
}

.search-area {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style> 