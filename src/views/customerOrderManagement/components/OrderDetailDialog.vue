<template>
  <el-dialog
    v-model="visible"
    :title="t('payment.orderDetail')"
    width="1400px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading" class="dialog-content">
      <div v-if="orderDetail">
        <!-- 订单基本信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('payment.orderBasicInfo') }}</span>
          </template>
          <div class="order-info">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="info-item">
                  <label>{{ t('payment.orderNumber') }}:</label>
                  <span>{{ orderDetail.orderNumber }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <label>{{ t('payment.orderStatus') }}:</label>
                  <el-tag :type="getOrderStatusType(orderDetail.orderStatus)">
                    {{ getOrderStatusText(orderDetail.orderStatus) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <label>{{ t('payment.paymentStatus') }}:</label>
                  <el-tag :type="getPaymentStatusType(orderDetail.paymentStatus)">
                    {{ getPaymentStatusText(orderDetail.paymentStatus) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <label>{{ t('payment.orderCreateTime') }}:</label>
                  <span>{{ orderDetail.orderCreateTime }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 客户信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('payment.customerInfo') }}</span>
          </template>
          <div class="customer-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.buyerName') }}:</label>
                  <span>{{ orderDetail.buyerName }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.buyerPhone') }}:</label>
                  <span>{{ orderDetail.buyerPhone }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.buyerIdCard') }}:</label>
                  <span>{{ orderDetail.buyerIdCard || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ t('payment.buyerAddress') }}:</label>
                  <span>{{ orderDetail.buyerAddress || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <label>{{ t('payment.buyerEmail') }}:</label>
                  <span>{{ orderDetail.buyerEmail || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="info-item">
                  <label>{{ t('payment.buyerGender') }}:</label>
                  <span>{{ orderDetail.buyerGender || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 车辆信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('payment.vehicleInfo') }}</span>
          </template>
          <div class="vehicle-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.model') }}:</label>
                  <span>{{ orderDetail.model }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.variant') }}:</label>
                  <span>{{ orderDetail.variant }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.color') }}:</label>
                  <span>{{ orderDetail.color }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.vin') }}:</label>
                  <span>{{ orderDetail.vin || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.engineNumber') }}:</label>
                  <span>{{ orderDetail.engineNumber || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.yearOfManufacture') }}:</label>
                  <span>{{ orderDetail.yearOfManufacture || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 经销商信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('payment.dealerInfo') }}</span>
          </template>
          <div class="dealer-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.dealerStore') }}:</label>
                  <span>{{ orderDetail.dealerStoreName }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.salesConsultant') }}:</label>
                  <span>{{ orderDetail.salesConsultantName }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.salesConsultantPhone') }}:</label>
                  <span>{{ orderDetail.salesConsultantPhone || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 金额信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('payment.amountInfo') }}</span>
          </template>
          <div class="amount-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.vehicleSalesPrice') }}:</label>
                  <span class="amount">{{ formatAmount(orderDetail.vehicleSalesPrice) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.insuranceAmount') }}:</label>
                  <span class="amount">{{ formatAmount(orderDetail.insuranceAmount) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.otrAmount') }}:</label>
                  <span class="amount">{{ formatAmount(orderDetail.otrAmount) }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.discountAmount') }}:</label>
                  <span class="amount discount">{{ formatAmount(orderDetail.discountAmount) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.totalAmount') }}:</label>
                  <span class="amount total">{{ formatAmount(orderDetail.totalAmount) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.loanAmount') }}:</label>
                  <span class="amount">{{ orderDetail.loanAmount ? formatAmount(orderDetail.loanAmount) : '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-divider />
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.paidAmount') }}:</label>
                  <span class="amount paid">{{ formatAmount(orderDetail.paidAmount) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.unpaidAmount') }}:</label>
                  <span class="amount unpaid">{{ formatAmount(orderDetail.unpaidAmount) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.canInvoice') }}:</label>
                  <el-tag :type="orderDetail.canInvoice ? 'success' : ''">
                    {{ orderDetail.canInvoice ? t('common.yes') : t('common.no') }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 发票信息 -->
        <el-card class="mb-20" v-if="orderDetail.invoiceNumber || orderDetail.invoiceTime">
          <template #header>
            <span class="card-title">{{ t('payment.invoiceInfo') }}</span>
          </template>
          <div class="invoice-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.invoiceNumber') }}:</label>
                  <span>{{ orderDetail.invoiceNumber || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('payment.invoiceTime') }}:</label>
                  <span>{{ orderDetail.invoiceTime || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 收退款记录 -->
        <el-card>
          <template #header>
            <span class="card-title">{{ t('payment.paymentRecordList') }}</span>
          </template>
          <el-table :data="paymentRecords" style="width: 100%">
            <el-table-column type="index" :label="t('common.index')" width="60" />
            <el-table-column prop="recordType" :label="t('payment.recordType')" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.recordType === '收款' ? 'success' : 'danger'">
                  {{ scope.row.recordType === '收款' ? t('payment.recordTypePayment') : t('payment.recordTypeRefund') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="amount" :label="t('payment.amount')" width="120" align="right">
              <template #default="scope">
                <span :class="{ 'text-success': scope.row.recordType === '收款', 'text-danger': scope.row.recordType === '退款' }">
                  {{ formatAmount(scope.row.amount) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="paymentMethod" :label="t('payment.paymentMethod')" width="100">
              <template #default="scope">
                {{ getPaymentMethodText(scope.row.paymentMethod) }}
              </template>
            </el-table-column>
            <el-table-column prop="transactionNumber" :label="t('payment.transactionNumber')" width="150" />
            <el-table-column prop="paymentDate" :label="t('payment.paymentDate')" width="160" />
            <el-table-column prop="handler" :label="t('payment.handler')" width="100" />
            <el-table-column prop="dataSource" :label="t('payment.dataSource')" width="80">
              <template #default="scope">
                <el-tag :type="scope.row.dataSource === 'APP' ? 'primary' : 'info'">
                  {{ scope.row.dataSource }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" :label="t('payment.remark')" min-width="150" />
            <el-table-column prop="createTime" :label="t('payment.createTime')" width="160" />
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">{{ t('common.close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { getOrderDetail } from '@/api/modules/payment'
import type { OrderDetailInfo, PaymentRecord } from '@/types/module'

const { t } = useI18n()

// Props
const props = defineProps<{
  modelValue: boolean
  orderId: string
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const visible = ref(false)
const orderDetail = ref<OrderDetailInfo | null>(null)
const paymentRecords = ref<PaymentRecord[]>([])

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal && props.orderId) {
      loadOrderDetail()
    }
  },
  { immediate: true }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 加载订单详情
const loadOrderDetail = async () => {
  if (!props.orderId) return

  loading.value = true
  try {
    const response = await getOrderDetail(props.orderId)
    orderDetail.value = response.orderDetail
    paymentRecords.value = response.paymentRecords
  } catch (error) {
    console.error('Failed to load order detail:', error)
    ElMessage.error(t('common.loadFailed'))
  } finally {
    loading.value = false
  }
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `RM ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 获取订单状态样式
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已确认': 'success',
    '待审核': 'warning',
    '已取消': 'danger',
    '已交车': 'success'
  }
  return statusMap[status] || 'info'
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': t('payment.orderStatusSubmitted'),
    '取消审核中': t('payment.orderStatusCancelPending'),
    '取消审核通过': t('payment.orderStatusCancelApproved'),
    '已取消': t('payment.orderStatusCancelled'),
    '已确认': t('payment.orderStatusConfirmed'),
    '待审核': t('payment.orderStatusPendingReview'),
    '已审核': t('payment.orderStatusReviewed'),
    '待交车': t('payment.orderStatusPendingDelivery'),
    '已交车': t('payment.orderStatusDelivered')
  }
  return statusMap[status] || status
}

// 获取支付状态样式
const getPaymentStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已支付尾款': 'success',
    '已支付定金': 'primary',
    '待支付定金': 'warning',
    '退款中': 'danger',
    '退款完成': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '待支付定金': t('payment.paymentStatusPendingDeposit'),
    '已支付定金': t('payment.paymentStatusDepositPaid'),
    '退款中': t('payment.paymentStatusRefunding'),
    '退款完成': t('payment.paymentStatusRefunded'),
    '待支付尾款': t('payment.paymentStatusPendingFinal'),
    '已支付尾款': t('payment.paymentStatusFullyPaid')
  }
  return statusMap[status] || status
}

// 获取支付方式文本
const getPaymentMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    '现金': t('payment.paymentMethodCash'),
    '银行转账': t('payment.paymentMethodBankTransfer'),
    '刷卡': t('payment.paymentMethodCard'),
    '支付宝': t('payment.paymentMethodAlipay'),
    '微信支付': t('payment.paymentMethodWechat')
  }
  return methodMap[method] || method
}
</script>

<style scoped>
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.card-title {
  font-weight: bold;
  color: #303133;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  
  label {
    width: 140px;
    font-weight: bold;
    color: #606266;
    flex-shrink: 0;
  }
  
  span {
    flex: 1;
    color: #303133;
  }
  
  .amount {
    font-weight: bold;
    
    &.paid {
      color: #67c23a;
    }
    
    &.unpaid {
      color: #f56c6c;
    }
    
    &.total {
      color: #409eff;
      font-size: 16px;
    }
    
    &.discount {
      color: #f56c6c;
    }
  }
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.mb-20 {
  margin-bottom: 20px;
}
</style> 