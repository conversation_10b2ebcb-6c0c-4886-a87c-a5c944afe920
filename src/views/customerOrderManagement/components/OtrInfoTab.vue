<template>
  <div class="otr-info-tab">
    <!-- OTR费用列表 -->
    <div class="otr-fees-section">
      <h3 class="section-subtitle">On The Road登记费用</h3>
      <el-table :data="props.formData.otrFees" style="width: 100%">
        <el-table-column prop="receiptNo" label="票据单号" width="180" />
        <el-table-column prop="item" label="收费项目" width="200" />
        <el-table-column prop="amount" label="收费价格">
          <template #default="{ row }">
            RM {{ row.amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="生效日期" width="180" />
        <el-table-column prop="endDate" label="到期日期" width="180" />
      </el-table>

      <!-- OTR费用总金额 -->
      <div class="otr-total">
        <span>登记费用总金额：</span>
        <span class="amount">RM {{ props.formData.otrTotalAmount.toFixed(2) }}</span>
      </div>
    </div>

    <!-- 提交交车 -->
    <div class="submit-section">
      <el-button 
        type="primary" 
        @click="handleDeliverySubmit"
        style="float: right;"
      >
        提交交车
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';

interface OtrFee {
  receiptNo: string;
  item: string;
  amount: number;
  startDate: string;
  endDate: string;
}

interface FormData {
  otrFees: OtrFee[];
  otrTotalAmount: number;
  vin?: string;
  jpjRegistrationStatus?: string;
  paymentStatus?: string;
  insuranceStatus?: string;
}

const props = defineProps<{
  formData: FormData;
}>();

const emit = defineEmits<{
  'submit-delivery': [];
}>();

// 处理提交交车
const handleDeliverySubmit = () => {
  // 验证前置条件
  if (!props.formData.vin) {
    ElMessage.error('VIN码未关联，无法提交交车');
    return;
  }

  if (props.formData.jpjRegistrationStatus !== 'completed') {
    ElMessage.error('JPJ注册未完成，无法提交交车');
    return;
  }

  if (props.formData.paymentStatus !== 'final_payment_paid') {
    ElMessage.error('尾款未支付完成，无法提交交车');
    return;
  }

  if (props.formData.insuranceStatus !== 'completed') {
    ElMessage.error('投保未完成，无法提交交车');
    return;
  }

  // 所有条件验证通过，提交交车
  emit('submit-delivery');
};
</script>

<style scoped>
.otr-info-tab {
  padding: 20px 0;
}

.section-subtitle {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0;
  color: #303133;
}

.otr-total {
  margin-top: 20px;
  text-align: right;
  padding: 10px;
  background-color: #f5f7fa;

  .amount {
    font-weight: bold;
    color: #409EFF;
    margin-left: 10px;
  }
}

.submit-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style> 