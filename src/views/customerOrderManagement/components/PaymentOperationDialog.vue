<template>
  <el-dialog
    v-model="visible"
    :title="t('payment.paymentOperation')"
    width="1200px"
    :close-on-click-modal="false"
    destroy-on-close
    :loading="loading"
  >
    <div v-loading="loading" class="dialog-content">
      <!-- 订单基本信息 -->
      <el-card class="mb-20">
        <template #header>
          <span class="card-title">{{ t('payment.orderBasicInfo') }}</span>
        </template>
        <div v-if="orderDetail" class="order-info">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('payment.orderNumber') }}:</label>
                <span>{{ orderDetail.orderNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('payment.buyerName') }}:</label>
                <span>{{ orderDetail.buyerName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('payment.buyerPhone') }}:</label>
                <span>{{ orderDetail.buyerPhone }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('payment.totalAmount') }}:</label>
                <span class="amount">{{ formatAmount(orderDetail.totalAmount) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('payment.paidAmount') }}:</label>
                <span class="amount paid">{{ formatAmount(orderDetail.paidAmount) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('payment.unpaidAmount') }}:</label>
                <span class="amount unpaid">{{ formatAmount(orderDetail.unpaidAmount) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 添加收退款记录 -->
      <el-card class="mb-20">
        <template #header>
          <span class="card-title">{{ t('payment.addPaymentRecord') }}</span>
        </template>
        <el-form :model="paymentForm" :rules="paymentRules" ref="paymentFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('payment.recordType')" prop="recordType">
                <el-select v-model="paymentForm.recordType" style="width: 100%">
                  <el-option :label="t('payment.recordTypePayment')" value="收款" />
                  <el-option :label="t('payment.recordTypeRefund')" value="退款" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('payment.amount')" prop="amount">
                <el-input-number
                  v-model="paymentForm.amount"
                  :min="0.01"
                  :precision="2"
                  style="width: 100%"
                  :placeholder="t('payment.enterAmount')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('payment.paymentMethod')" prop="paymentMethod">
                <el-select v-model="paymentForm.paymentMethod" style="width: 100%">
                  <el-option :label="t('payment.paymentMethodCash')" value="现金" />
                  <el-option :label="t('payment.paymentMethodBankTransfer')" value="银行转账" />
                  <el-option :label="t('payment.paymentMethodCard')" value="刷卡" />
                  <el-option :label="t('payment.paymentMethodAlipay')" value="支付宝" />
                  <el-option :label="t('payment.paymentMethodWechat')" value="微信支付" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('payment.transactionNumber')" prop="transactionNumber">
                <el-input
                  v-model="paymentForm.transactionNumber"
                  :placeholder="t('payment.enterTransactionNumber')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('payment.paymentDate')" prop="paymentDate">
                <el-date-picker
                  v-model="paymentForm.paymentDate"
                  type="datetime"
                  :placeholder="t('payment.selectPaymentDate')"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('payment.handler')" prop="handler">
                <el-input
                  v-model="paymentForm.handler"
                  :placeholder="t('payment.enterHandler')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item :label="t('payment.remark')" prop="remark">
            <el-input
              v-model="paymentForm.remark"
              type="textarea"
              :rows="3"
              :placeholder="t('payment.enterRemark')"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleAddPaymentRecord" :loading="submitLoading">
              {{ t('payment.addRecord') }}
            </el-button>
            <el-button @click="resetPaymentForm">{{ t('common.reset') }}</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 收退款记录列表 -->
      <el-card>
        <template #header>
          <span class="card-title">{{ t('payment.paymentRecordList') }}</span>
        </template>
        <el-table :data="paymentRecords" style="width: 100%">
          <el-table-column type="index" :label="t('common.index')" width="60" />
          <el-table-column prop="recordType" :label="t('payment.recordType')" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.recordType === '收款' ? 'success' : 'danger'">
                {{ scope.row.recordType === '收款' ? t('payment.recordTypePayment') : t('payment.recordTypeRefund') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" :label="t('payment.amount')" width="120" align="right">
            <template #default="scope">
              <span :class="{ 'text-success': scope.row.recordType === '收款', 'text-danger': scope.row.recordType === '退款' }">
                {{ formatAmount(scope.row.amount) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="paymentMethod" :label="t('payment.paymentMethod')" width="100">
            <template #default="scope">
              {{ getPaymentMethodText(scope.row.paymentMethod) }}
            </template>
          </el-table-column>
          <el-table-column prop="transactionNumber" :label="t('payment.transactionNumber')" width="150" />
          <el-table-column prop="paymentDate" :label="t('payment.paymentDate')" width="160" />
          <el-table-column prop="handler" :label="t('payment.handler')" width="100" />
          <el-table-column prop="dataSource" :label="t('payment.dataSource')" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.dataSource === 'APP' ? 'primary' : 'info'">
                {{ scope.row.dataSource }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" :label="t('payment.remark')" min-width="150" />
          <el-table-column prop="createTime" :label="t('payment.createTime')" width="160" />
          <el-table-column :label="t('common.operations')" width="100" fixed="right">
            <template #default="scope">
              <el-button
                type="danger"
                size="small"
                link
                @click="handleDeleteRecord(scope.row)"
                :disabled="scope.row.dataSource === 'APP'"
              >
                {{ t('common.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">{{ t('common.close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { getOrderDetail, addPaymentRecord, deletePaymentRecord, checkTransactionNumber } from '@/api/modules/payment'
import type { OrderDetailInfo, PaymentRecord, AddPaymentRecordForm } from '@/types/module'

const { t } = useI18n()

// Props
const props = defineProps<{
  modelValue: boolean
  orderId: string
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const visible = ref(false)
const orderDetail = ref<OrderDetailInfo | null>(null)
const paymentRecords = ref<PaymentRecord[]>([])
const paymentFormRef = ref()

// 收退款表单
const paymentForm = reactive<AddPaymentRecordForm>({
  recordType: '收款',
  amount: 0,
  paymentMethod: '银行转账',
  transactionNumber: '',
  paymentDate: '',
  handler: '',
  remark: ''
})

// 表单验证规则
const paymentRules = {
  recordType: [
    { required: true, message: t('payment.recordTypeRequired'), trigger: 'change' }
  ],
  amount: [
    { required: true, message: t('payment.amountRequired'), trigger: 'blur' },
    { type: 'number', min: 0.01, message: t('payment.amountMustBePositive'), trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: t('payment.paymentMethodRequired'), trigger: 'change' }
  ],
  transactionNumber: [
    { required: true, message: t('payment.transactionNumberRequired'), trigger: 'blur' },
    { 
      validator: async (rule: any, value: string, callback: any) => {
        if (!value) return callback()
        try {
          const exists = await checkTransactionNumber(value)
          if (exists) {
            callback(new Error(t('payment.transactionNumberExists')))
          } else {
            callback()
          }
        } catch (error) {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  paymentDate: [
    { required: true, message: t('payment.paymentDateRequired'), trigger: 'change' }
  ],
  handler: [
    { required: true, message: t('payment.handlerRequired'), trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal && props.orderId) {
      loadOrderDetail()
    }
  },
  { immediate: true }
)

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 加载订单详情
const loadOrderDetail = async () => {
  if (!props.orderId) return

  loading.value = true
  try {
    const response = await getOrderDetail(props.orderId)
    orderDetail.value = response.orderDetail
    paymentRecords.value = response.paymentRecords
    
    // 初始化表单默认值
    initPaymentForm()
  } catch (error) {
    console.error('Failed to load order detail:', error)
    ElMessage.error(t('common.loadFailed'))
  } finally {
    loading.value = false
  }
}

// 初始化表单
const initPaymentForm = () => {
  const now = new Date()
  paymentForm.paymentDate = now.toISOString().slice(0, 19).replace('T', ' ')
  paymentForm.handler = '系统管理员' // 可以从用户信息获取
}

// 重置表单
const resetPaymentForm = () => {
  paymentFormRef.value?.resetFields()
  initPaymentForm()
}

// 添加收退款记录
const handleAddPaymentRecord = async () => {
  if (!paymentFormRef.value) return

  try {
    await paymentFormRef.value.validate()
  } catch (error) {
    return
  }

  // 验证金额
  if (paymentForm.recordType === '退款' && paymentForm.amount > (orderDetail.value?.paidAmount || 0)) {
    ElMessage.error(t('payment.refundAmountExceedsPaid'))
    return
  }

  submitLoading.value = true
  try {
    await addPaymentRecord(props.orderId, paymentForm)
    ElMessage.success(t('payment.addRecordSuccess'))
    
    // 重新加载数据
    await loadOrderDetail()
    resetPaymentForm()
    emit('refresh')
  } catch (error) {
    console.error('Failed to add payment record:', error)
    ElMessage.error(t('payment.addRecordFailed'))
  } finally {
    submitLoading.value = false
  }
}

// 删除记录
const handleDeleteRecord = async (record: PaymentRecord) => {
  if (record.dataSource === 'APP') {
    ElMessage.warning(t('payment.cannotDeleteAppRecord'))
    return
  }

  try {
    await ElMessageBox.confirm(
      t('payment.confirmDeleteRecord'),
      t('common.confirm'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )

    await deletePaymentRecord(record.recordId)
    ElMessage.success(t('payment.deleteRecordSuccess'))
    
    // 重新加载数据
    await loadOrderDetail()
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete payment record:', error)
      ElMessage.error(t('payment.deleteRecordFailed'))
    }
  }
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `RM ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 获取支付方式文本
const getPaymentMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    '现金': t('payment.paymentMethodCash'),
    '银行转账': t('payment.paymentMethodBankTransfer'),
    '刷卡': t('payment.paymentMethodCard'),
    '支付宝': t('payment.paymentMethodAlipay'),
    '微信支付': t('payment.paymentMethodWechat')
  }
  return methodMap[method] || method
}
</script>

<style scoped>
.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.card-title {
  font-weight: bold;
  color: #303133;
}

.order-info {
  .info-item {
    display: flex;
    margin-bottom: 10px;
    
    label {
      width: 120px;
      font-weight: bold;
      color: #606266;
    }
    
    span {
      flex: 1;
      color: #303133;
    }
    
    .amount {
      font-weight: bold;
      
      &.paid {
        color: #67c23a;
      }
      
      &.unpaid {
        color: #f56c6c;
      }
    }
  }
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.mb-20 {
  margin-bottom: 20px;
}
</style> 