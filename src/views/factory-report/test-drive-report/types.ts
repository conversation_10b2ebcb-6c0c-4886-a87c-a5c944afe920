export interface TestDriveRecord {
  // Fields from the list API, also present in detail API
  testDriveNo: string;
  customerName: string;
  customerPhone: string;
  model: string;
  variant: string;
  startTime: string;
  endTime: string;
  mileage: number;
  consultantName: string;
  storeName: string;
  createTime: string;
  editable?: boolean;

  // Extra fields primarily from the detail API
  customerId?: number;
  customerIdType?: string;
  customerIdNumber?: string;
  driverName?: string;
  driverPhone?: string;
  driverIdType?: string;
  driverIdNumber?: string;
  source?: string;
  email?: string;
  driverLicenseNumber?: string;
  startMileage?: number;
  endMileage?: number;
  feedback?: string;
  consultantId?: number;
  storeId?: number;
  storeRegion?: string;
  updateTime?: string;
  
  // 后端可能返回的其他字段名
  idType?: string;
  idNumber?: string;
  sourceChannel?: string;
} 