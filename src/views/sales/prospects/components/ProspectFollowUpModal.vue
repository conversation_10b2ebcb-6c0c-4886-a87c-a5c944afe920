<template>
  <el-dialog
    v-model="modalVisible"
    :title="t('prospects.prospectFollowUp')"
    width="900px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="follow-up-modal">
      <!-- 跟进记录表单 -->
      <div class="form-section">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-position="top"
        >
          <!-- 潜客信息part -->
          <div class="form-group-title">{{ t('prospects.prospectInfo') }}</div>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.prospectId')" prop="prospectId">
                <el-input
                  v-model="formData.prospectId"
                  :placeholder="t('prospects.prospectId')"
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.prospectName')" prop="customerName">
                <el-input
                  v-model="formData.customerName"
                  :placeholder="t('prospects.inputProspectName')"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.prospectPhone')" prop="customerPhone">
                <el-input
                  v-model="formData.phoneNumber"
                  :placeholder="t('prospects.inputPhoneNumber')"
                  readonly
                >
                  <template #prepend>
                    <el-select
                      v-model="formData.countryCode"
                      style="width: 80px"
                      placeholder="区号"
                      disabled
                    >
                      <el-option label="+60" value="+60" />
                      <el-option label="+65" value="+65" />
                      <el-option label="+86" value="+86" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.idType')" prop="idType">
                <el-select
                  v-model="formData.idType"
                  :placeholder="t('prospects.idTypePlaceholder')"
                  style="width: 100%"
                >
                  <el-option :label="t('prospects.idCard')" value="01140001"></el-option>
                  <el-option :label="t('prospects.passport')" value="01140002"></el-option>
                  <el-option :label="t('prospects.residencePermit')" value="01140003"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.email')" prop="email">
                <el-input
                  v-model="formData.email"
                  :placeholder="t('prospects.inputEmail')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.region')" prop="region">
                <el-select
                  v-model="formData.region"
                  :placeholder="t('prospects.selectRegion')"
                  style="width: 100%"
                  :loading="dictionaryLoading"
                >
                  <el-option
                    v-for="option in regionOptions"
                    :key="option.code"
                    :label="option.name"
                    :value="option.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 潜客意向part -->
          <div class="form-group-title">{{ t('prospects.prospectIntention') }}</div>
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item :label="t('prospects.intentModel')" prop="intentionModel">
                <el-select
                  v-model="formData.intentionModel"
                  :placeholder="t('prospects.selectIntentModel')"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in modelOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item :label="t('prospects.intentVariant')" prop="intentionVariant">
              <el-select
                v-model="formData.intentionVariant"
                :placeholder="t('prospects.selectIntentVariant')"
                clearable
                filterable
              >
                <el-option
                  v-for="item in variantOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
            <el-col :span="8">
              <el-form-item :label="t('prospects.intentColor')" prop="intentionColor">
                <el-select
                  v-model="formData.intentionColor"
                  :placeholder="t('prospects.selectIntentColor')"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in colorOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 跟进记录part -->
          <div class="form-group-title">{{ t('prospects.followUpRecord') }}</div>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.salesAdvisorId')">
                <el-input
                  v-model="formData.salesAdvisorId"
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.salesAdvisorName')">
                <el-input
                  :value="advisorName"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.followUpMethod')" prop="followUpMethod">
                <el-select
                  v-model="formData.followUpMethod"
                  :placeholder="t('prospects.selectFollowUpMethod')"
                  style="width: 100%"
                  :loading="dictionaryLoading"
                >
                  <el-option
                    v-for="option in followUpMethodOptions"
                    :key="option.code"
                    :label="option.name"
                    :value="option.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.followUpTime')" prop="followUpTime">
                <el-date-picker
                  v-model="formData.followUpTime"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm"
                  :placeholder="t('prospects.selectFollowUpTime')"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospects.intentionLevel')" prop="intentionLevel">
                <el-select
                  v-model="formData.intentionLevel"
                  :placeholder="t('prospects.intentionLevelPlaceholder')"
                  style="width: 100%"
                  @change="handleLevelChange"
                >
                  <el-option
                      v-for="option in intentLevelOptions"
                      :key="option.code"
                      :label="option.name"
                      :value="option.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospects.nextFollowUpTime')" prop="nextFollowUpTime">
                <el-date-picker
                  v-model="formData.nextFollowUpTime"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm"
                  :placeholder="t('prospects.nextFollowUpTimePlaceholder')"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="!formData.intentionLevel">
              <div class="text-muted">
                <small>请先选择意向级别和跟进时间</small>
              </div>
            </el-col>
          </el-row>

          <el-form-item :label="t('prospects.followUpDetails')" prop="followUpDetails">
            <el-input
              v-model="formData.followUpDetails"
              type="textarea"
              :placeholder="t('prospects.followUpDetailsPlaceholder')"
              :rows="4"
              show-word-limit
              :maxlength="1000"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          {{ tc('save') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { addFollowUp } from '@/api/modules/sales/prospects';
import type {
  ProspectItem,
  CreateFollowUpRequest,
} from '@/types/sales/prospects';
import { calculateNextFollowUpTime } from '@/utils/date-filter'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('sales')

// 组件属性
interface Props {
  show: boolean
  prospectData: ProspectItem | null
}

// 组件事件
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  prospectData: null
})

const emit = defineEmits<Emits>()

const {
  getOptions,
  loading: batchDictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.REGION,
  DICTIONARY_TYPES.FOLLOW_UP_METHOD,
  DICTIONARY_TYPES.INTENT_LEVEL
])

const dictionaryLoading = computed(() => batchDictionaryLoading.value)

// 响应式数据
const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const advisorName = ref('当前销售顾问')

// 模态框显示状态
const modalVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = reactive({
  prospectId: '',
  customerName: '',
  countryCode: '+60',
  phoneNumber: '',
  customerPhone: '',
  idType: '01140001',
  idNumber: '',
  email: '',
  region: '',
  intentionModel: '',
  intentionVariant: '',
  intentionColor: '',
  salesAdvisorId: '',
  followUpMethod: '01390001',
  followUpTime: null,
  intentionLevel: '01160001',
  nextFollowUpTime: null,
  followUpDetails: ''
})

// 表单验证规则
const formRules: FormRules = {
  customerName: [
    { required: true, message: t('prospects.inputProspectNameRequired'), trigger: 'blur' }
  ],
  customerPhone: [
    { required: true, message: t('prospects.inputPhoneNumberRequired'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: t('prospects.invalidPhoneNumber'), trigger: 'blur' }
  ],
  intentionModel: [
    { required: true, message: t('prospects.selectIntentionModelRequired'), trigger: 'change' }
  ],
  intentionVariant: [
    { required: true, message: t('prospects.selectIntentionVariantRequired'), trigger: 'change' }
  ],
  intentionColor: [
    { required: true, message: t('prospects.selectIntentionColorRequired'), trigger: 'change' }
  ],
  followUpMethod: [
    { required: true, message: t('prospects.selectFollowUpMethodRequired'), trigger: 'change' }
  ],
  followUpTime: [
    { required: true, message: t('prospects.selectFollowUpTimeRequired'), trigger: 'change' }
  ],
  intentionLevel: [
    { required: true, message: t('prospects.selectIntentionLevelRequiredFollowUp'), trigger: 'change' }
  ],
  nextFollowUpTime: [
    { required: true, message: t('prospects.selectNextFollowUpTimeRequired'), trigger: 'change' }
  ],
  followUpDetails: [
    { required: true, message: t('prospects.inputFollowUpDetailsRequired'), trigger: 'blur' }
  ]
}

const regionOptions = computed(() => getOptions(DICTIONARY_TYPES.REGION))
const followUpMethodOptions = computed(() => getOptions(DICTIONARY_TYPES.FOLLOW_UP_METHOD))
const intentLevelOptions = computed(() => getOptions(DICTIONARY_TYPES.INTENT_LEVEL));


const modelOptions = [
  { label: 'Perodua Axia', value: 'AXIA' },
  { label: 'Perodua Bezza', value: 'BEZZA' },
  { label: 'Perodua Myvi', value: 'MYVI' },
  { label: 'Perodua Aruz', value: 'ARUZ' },
  { label: 'Perodua Ativa', value: 'ATIVA' }
]

const variantOptions = [
  { label: 'Standard', value: 'STANDARD' },
  { label: 'Premium', value: 'PREMIUM' },
  { label: 'Advance', value: 'ADVANCE' }
]

const colorOptions = [
  { label: '白色', value: 'WHITE' },
  { label: '黑色', value: 'BLACK' },
  { label: '银色', value: 'SILVER' },
  { label: '红色', value: 'RED' },
  { label: '蓝色', value: 'BLUE' }
]

// 监听意向级别变化，自动计算下次跟进时间
const handleLevelChange = (level: string) => {
  if (formData.followUpTime && level) {
    const nextTime = calculateNextFollowUpTime(formData.followUpTime, level)
    formData.nextFollowUpTime = nextTime
  }
}

// 监听跟进时间变化，自动计算下次跟进时间
watch(() => formData.followUpTime, (newTime) => {
  if (newTime && formData.intentionLevel) {
    const nextTime = calculateNextFollowUpTime(newTime, formData.intentionLevel)
    formData.nextFollowUpTime = nextTime
  }
})

// 监听潜客数据变化，初始化表单
watch(() => props.prospectData, (data) => {
  if (data) {
    let countryCode = '+60'
    let phoneNumber = data.phoneNumber || ''
    
    if (data.phoneNumber) {
      if (data.phoneNumber.startsWith('+60')) {
        countryCode = '+60'
        phoneNumber = data.phoneNumber.substring(3)
      } else if (data.phoneNumber.startsWith('+65')) {
        countryCode = '+65'
        phoneNumber = data.phoneNumber.substring(3)
      } else if (data.phoneNumber.startsWith('+86')) {
        countryCode = '+86'
        phoneNumber = data.phoneNumber.substring(3)
      }
    }

    Object.assign(formData, {
      prospectId: data.id,
      customerName: data.name || '',
      countryCode: countryCode,
      phoneNumber: phoneNumber,
      customerPhone: data.phoneNumber || '',
      idType: data.idType || '01140001',
      idNumber: data.idNumber || '',
      email: data.email || '',
      region: data.region || '',
      intentionModel: data.intentModel || '',
      intentionVariant: data.intentVariant || '',
      intentionColor: data.intentColor || '',
      salesAdvisorId: data.currentSalesAdvisorId || '',
      followUpMethod: '01390001',
      followUpTime: new Date(),
      intentionLevel: data.currentIntentLevel || '01160001',
      nextFollowUpTime: null,
      followUpDetails: ''
    })

    advisorName.value = data.currentSalesAdvisorName || '当前销售顾问'

    // 自动计算下次跟进时间
    if (formData.followUpTime && formData.intentionLevel) {
      const nextTime = calculateNextFollowUpTime(formData.followUpTime, formData.intentionLevel)
      formData.nextFollowUpTime = nextTime
    }
  }
}, { immediate: true })

// 事件处理
const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    submitLoading.value = true

    // 格式化日期时间为 yyyy-MM-dd HH:mm 格式
    const formatDateTime = (date: Date | null | string) => {
      if (!date) return null
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }

    const requestData = {
      ...formData,
      followUpTime: formatDateTime(formData.followUpTime),
      nextFollowUpTime: formatDateTime(formData.nextFollowUpTime)
    }

    await addFollowUp(requestData as unknown as CreateFollowUpRequest)

    ElMessage.success(t('prospects.followUpRecordAddSuccess'))
    emit('success')
    modalVisible.value = false
  } catch (error) {
    ElMessage.error(tc('saveFailed'))
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped>
.follow-up-modal {
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  padding: 0;
}

.form-group-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin: 20px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.form-group-title:first-child {
  margin-top: 0;
}

.text-muted {
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
  height: 32px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-textarea__inner) {
  min-height: 100px;
}
</style>
