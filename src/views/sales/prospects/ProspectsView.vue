<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('prospects.salesProspectManagement') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('prospects.prospectId')">
              <el-input
                v-model="searchParams.prospectId"
                :placeholder="t('prospects.inputProspectId')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('prospects.prospectName')">
              <el-input
                v-model="searchParams.customerName"
                :placeholder="t('prospects.inputProspectName')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('prospects.prospectPhone')">
              <el-input
                v-model="searchParams.phoneNumber"
                :placeholder="t('prospects.inputPhoneNumber')"
                clearable
              >
                <template #prepend>
                  <el-select
                    v-model="searchParams.countryCode"
                    style="width: 80px"
                    placeholder="区号"
                    clearable
                  >
                    <el-option label="+60" value="+60" />
                    <el-option label="+65" value="+65" />
                    <el-option label="+86" value="+86" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('prospects.sourceChannel')">
              <el-select
                v-model="searchParams.sourceChannel"
                :placeholder="tc('all')"
                clearable
              >
                <el-option
                  v-for="option in sourceChannelOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('prospects.prospectLevel')">
              <el-select
                v-model="searchParams.customerLevel"
                :placeholder="tc('all')"
                clearable
              >
                <el-option
                  v-for="option in intentLevelOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('prospects.prospectStatus')">
              <el-select
                v-model="searchParams.customerStatus"
                :placeholder="tc('all')"
                clearable
              >
                <el-option
                  v-for="option in prospectStatusOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesAdvisor')">
              <el-select
                v-model="searchParams.salesAdvisorId"
                :placeholder="tc('all')"
                clearable
                :loading="advisorLoading"
              >
                <el-option
                  v-for="option in salesAdvisorOptions"
                  :key="option.id"
                  :label="option.name"
                  :value="option.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 空列，用于占位 -->
          </el-col>
          <el-col :span="6">
            <div class="buttons-container">
              <el-space>
                <el-button type="primary" :icon="Search" @click="handleSearch">
                  {{ tc('search') }}
                </el-button>
                <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
              </el-space>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20">
      <div class="action-section">
        <el-space>
          <el-button type="primary" @click="handleAdd" :icon="Plus">
            {{ tc('add') }}
          </el-button>
          <el-button @click="handleExport" :icon="Download">
            {{ tc('export') }}
          </el-button>
        </el-space>
        <el-radio-group v-model="dateFilterType" @change="handleDateFilterChange">
          <el-radio-button label="all">{{ tc('all') }}</el-radio-button>
          <el-radio-button label="today">{{ tc('today') }}</el-radio-button>
        </el-radio-group>
      </div>
    </el-card>

    <!-- 数据展示区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column type="index" :label="tc('serialNumber')" width="60" />
        <el-table-column :label="t('prospects.prospectId')" prop="id" min-width="120" />
        <el-table-column :label="t('prospects.prospectName')" prop="name" min-width="120" />
        <el-table-column :label="t('prospects.prospectPhone')" prop="phoneNumber" min-width="130" />
        <el-table-column :label="t('prospects.sourceChannel')" prop="sourceChannel" min-width="120">
          <template #default="{ row }">
            {{ getName(DICTIONARY_TYPES.CUSTOMER_SOURCE,row.sourceChannel) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('prospects.prospectLevel')" prop="currentIntentLevel" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.currentIntentLevel)">
              {{ getName(DICTIONARY_TYPES.INTENT_LEVEL,row.currentIntentLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('prospects.prospectStatus')" prop="prospectStatus" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.prospectStatus)">
              {{ getName(DICTIONARY_TYPES.PROSPECT_STATUS,row.prospectStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('prospects.intentModel')" prop="intentModel" min-width="120" />
        <el-table-column :label="t('prospects.intentVariant')" prop="intentVariant" min-width="150" />
        <el-table-column :label="t('prospects.intentColor')" prop="intentColor" min-width="100" />
        <el-table-column :label="t('prospects.salesAdvisorId')" prop="currentSalesAdvisorId" min-width="120" />
        <el-table-column :label="t('prospects.salesAdvisorName')" prop="currentSalesAdvisorName" min-width="120" />
        <el-table-column :label="t('prospects.prospectAssociatedTime')" prop="leadAssociationTime" min-width="180">
          <template #default="{ row }">
            {{ formatDate(row.leadAssociationTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('prospects.lastFollowUpTime')" prop="lastFollowUpTime" min-width="180">
          <template #default="{ row }">
            {{ formatDate(row.lastFollowUpTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('prospects.nextFollowUpTime')" prop="nextFollowUpTime" min-width="180">
          <template #default="{ row }">
            {{ formatDate(row.nextFollowUpTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="tc('operations')" width="200" fixed="right">
          <template #default="{ row }">
            <el-space>
              <el-button type="primary" link @click="handleFollowUp(row)">
                {{ tc('followUp') }}
              </el-button>
              <el-button type="info" link @click="handleViewDetail(row)">
                {{ tc('detail') }}
              </el-button>
              <el-button type="warning" link @click="handleMarkNoIntention(row)">
                {{ t('prospects.markNoIntention') }}
              </el-button>
              <el-button type="success" link @click="handleAssignAdvisor(row)">
                {{ t('prospects.changeAdvisor') }}
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 模态框组件 -->
    <!-- 新增潜客模态框 -->
    <ProspectAddModal
      v-model:show="showAddModal"
      @success="handleAddSuccess"
    />

    <!-- 潜客详情模态框 -->
    <ProspectDetailModal
      v-model:visible="showDetailModal"
      :prospectId="currentProspectData?.id || ''"
    />

    <!-- 跟进记录模态框 -->
    <ProspectFollowUpModal
      v-model:show="showFollowUpModal"
      :prospect-data="currentProspectData"
      @success="handleFollowUpSuccess"
    />

    <!-- 标记无意向模态框 -->
    <ProspectDefeatModal
      v-model:show="showDefeatModal"
      :prospect-data="currentProspectData"
      @success="handleDefeatSuccess"
    />

    <!-- 分配顾问模态框 -->
    <ProspectAssignModal
      v-model:show="showAssignModal"
      :prospect-data="currentProspectData"
      @success="handleAssignSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { Search, Plus, Download } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getProspects, exportProspects } from '@/api/modules/sales/prospects';
import type { ProspectItem, ProspectSearchParams } from '@/types/sales/prospects';
import { useBatchDictionary } from '@/composables/useDictionary';
import {DICTIONARY_TYPES, type DictionaryType} from '@/constants/dictionary';
import { formatDate } from '@/utils/date-filter';

// 导入模态框组件
import ProspectAddModal from './components/ProspectAddModal.vue';
import ProspectDetailModal from './components/ProspectDetailModal.vue';
import ProspectFollowUpModal from './components/ProspectFollowUpModal.vue';
import ProspectDefeatModal from './components/ProspectDefeatModal.vue';
import ProspectAssignModal from './components/ProspectAssignModal.vue';

// 使用规范的国际化引用
const { t, tc } = useModuleI18n('sales');

// 搜索参数
const searchParams = reactive<ProspectSearchParams>({
  prospectId: undefined,
  customerName: undefined,
  countryCode: '+60', // 设置默认区号
  phoneNumber: undefined,
  customerPhone: undefined,
  sourceChannel: undefined,
  customerLevel: undefined,
  customerStatus: undefined,
  salesAdvisorId: undefined // Add this property
});

// 加载状态
const loading = ref(false);

// 日期筛选
const dateFilterType = ref('all');

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
});

const tableData = ref<ProspectItem[]>([]);

// 模态框状态管理
const showAddModal = ref(false);
const showDetailModal = ref(false);
const showFollowUpModal = ref(false);
const showDefeatModal = ref(false);
const showAssignModal = ref(false);
const currentProspectData = ref<ProspectItem | null>(null);

// 字典数据
const { getOptions,getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.PROSPECT_STATUS
]);


// 获取来源渠道名称
const getName = (dictionaryType: DictionaryType,code: string) => {
  return getNameByCode(dictionaryType, code) || code;
};

const sourceChannelOptions = computed(() => getOptions(DICTIONARY_TYPES.CUSTOMER_SOURCE));
const intentLevelOptions = computed(() => getOptions(DICTIONARY_TYPES.INTENT_LEVEL));
const prospectStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PROSPECT_STATUS));

// 获取标签类型
const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    '01160001': 'danger',  // H级
    '01160002': 'warning', // A级
    '01160003': 'success', // B级
    '01160004': 'info'     // C级
  };
  return typeMap[level] || 'info';
};

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01380001': 'info',    // 新建
    '01380002': 'warning', // 跟进中
    '01380003': 'success', // 已成交
    '01380004': 'danger'   // 无意向
  };
  return typeMap[status] || 'info';
};

// 搜索方法
const handleSearch = async () => {
  loading.value = true;
  try {
    const response = await getProspects({
      ...searchParams,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      isToday: dateFilterType.value === 'today'
    });

    tableData.value = response.records;
    pagination.total = response.total;

    ElMessage.success(tc('operationSuccessful'));
  } catch (error) {
    console.error('搜索失败:', error);
    ElMessage.error(tc('searchFailed'));
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    prospectId: '',
    customerName: '',
    countryCode: '+60', // 重置时也保持默认区号
    phoneNumber: '',
    customerPhone: '',
    sourceChannel: '',
    customerLevel: '',
    customerStatus: ''
  });
  pagination.pageNum = 1;
  dateFilterType.value = 'all';
  handleSearch();
};

// 日期筛选变化
const handleDateFilterChange = () => {
  pagination.pageNum = 1;
  handleSearch();
};

// 操作方法
const handleAdd = () => {
  // 显示新增潜客模态框
  showAddModal.value = true;
};

const handleExport = async () => {
  try {
    loading.value = true;
    const blob = await exportProspects({
      ...searchParams,
      isToday: dateFilterType.value === 'today'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `prospects_${new Date().getTime()}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success(tc('exportSuccessful'));
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(tc('exportFailed'));
  } finally {
    loading.value = false;
  }
};

const handleFollowUp = (row: ProspectItem) => {
  // 显示跟进记录模态框
  currentProspectData.value = row;
  showFollowUpModal.value = true;
};

const handleViewDetail = (row: ProspectItem) => {
  // 显示潜客详情模态框
  currentProspectData.value = row;
  showDetailModal.value = true;
};

const handleMarkNoIntention = (row: ProspectItem) => {
  // 显示标记无意向模态框
  currentProspectData.value = row;
  showDefeatModal.value = true;
};

const handleAssignAdvisor = (row: ProspectItem) => {
  // 显示分配顾问模态框
  currentProspectData.value = row;
  showAssignModal.value = true;
};

// 模态框成功回调
const handleAddSuccess = () => {
  showAddModal.value = false;
  handleSearch(); // 重新加载数据
  ElMessage.success(t('prospects.addProspectSuccess'));
};

const handleFollowUpSuccess = () => {
  showFollowUpModal.value = false;
  handleSearch(); // 重新加载数据
  ElMessage.success(t('prospects.followUpRecordSuccess'));
};

const handleDefeatSuccess = () => {
  showDefeatModal.value = false;
  handleSearch(); // 重新加载数据
  ElMessage.success(t('prospects.markNoIntentionSubmitSuccess'));
};

const handleAssignSuccess = () => {
  showAssignModal.value = false;
  handleSearch(); // 重新加载数据
  ElMessage.success(t('prospects.changeAdvisorSuccess'));
};

// 分页方法
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;
  handleSearch();
};

const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;
  handleSearch();
};

// 监听主页面搜索区号和号码变化，自动组合完整号码
watch([() => searchParams.countryCode, () => searchParams.phoneNumber], () => {
  if (searchParams.countryCode && searchParams.phoneNumber) {
    searchParams.customerPhone = searchParams.countryCode + searchParams.phoneNumber;
  } else {
    searchParams.customerPhone = undefined;
  }
});

// 页面加载时执行一次搜索
onMounted(() => {
  handleSearch();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 15px;
}

.buttons-container {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  height: 100%;
  padding-top: 30px; /* 与表单项标签高度对齐 */
}

.buttons-container .el-button {
  margin-left: 10px;
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
