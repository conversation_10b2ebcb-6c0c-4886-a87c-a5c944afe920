<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 筛选查询区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <!-- 第一行 -->
          <el-col :span="6">
            <el-form-item :label="t('invoiceNumber')">
              <el-input
                v-model="searchParams.invoiceNumber"
                :placeholder="t('invoiceNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerName')">
              <el-input
                v-model="searchParams.customerName"
                :placeholder="t('customerNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerPhone')">
              <el-input
                v-model="searchParams.customerPhone"
                :placeholder="t('customerPhonePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerEmail')">
              <el-input
                v-model="searchParams.customerEmail"
                :placeholder="t('customerEmailPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第二行 -->
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('orderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vin')">
              <el-input
                v-model="searchParams.vin"
                :placeholder="t('vinPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesType')">
              <el-select
                v-model="searchParams.salesType"
                :placeholder="tc('all')"
                clearable
                style="width: 100%"
                :loading="dictionaryLoading"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in salesTypeOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesStore')">
              <el-select
                v-model="searchParams.salesStore"
                :placeholder="tc('all')"
                clearable
                style="width: 100%"
                @change="handleStoreChange"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in storeOptions"
                  :key="option.code"
                  :label="option.dealerName"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第三行 -->
          <el-col :span="6">
            <el-form-item :label="t('salesConsultant')">
              <el-select
                v-model="searchParams.salesConsultant"
                :placeholder="tc('all')"
                clearable
                style="width: 100%"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in consultantOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('invoiceDate')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 预留空间 -->
          </el-col>
          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button @click="resetSearch">
                {{ tc('reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能操作区域 -->
    <el-card class="mb-20 operation-card">
      <div class="operation-buttons">
        <el-button :icon="Download" @click="showExportDialog">
          {{ t('export') }}
        </el-button>
        <el-button
          :icon="Printer"
          @click="handleBatchPrint"
          :disabled="selectedRows.length === 0"
        >
          {{ t('batchPrint') }}
        </el-button>
      </div>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <div class="table-container">
        <el-table
          ref="tableRef"
          :data="tableData"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" fixed="left" />
          <el-table-column :label="tc('index')" type="index" width="70" />
          <el-table-column :label="t('invoiceNumber')" prop="invoiceNumber" min-width="130" show-overflow-tooltip />
          <el-table-column :label="t('invoiceDate')" prop="invoiceDate" min-width="110" />
          <el-table-column :label="t('orderNumber')" prop="orderNumber" min-width="130" show-overflow-tooltip />
          <el-table-column :label="t('customerName')" prop="customerName" min-width="110" show-overflow-tooltip />
          <el-table-column :label="t('customerPhone')" prop="customerPhone" min-width="130" />
          <el-table-column :label="t('customerEmail')" prop="customerEmail" min-width="160" show-overflow-tooltip />
          <el-table-column :label="t('customerAddress')" prop="customerAddress" min-width="180" show-overflow-tooltip />
          <el-table-column :label="t('vin')" prop="vin" min-width="160" show-overflow-tooltip />
          <el-table-column :label="t('model')" prop="model" min-width="90" />
          <el-table-column :label="t('variant')" prop="variant" min-width="140" show-overflow-tooltip />
          <el-table-column :label="t('color')" prop="color" min-width="90" />
          <el-table-column :label="t('salesStore')" prop="salesStore" min-width="110" show-overflow-tooltip>
            <template #default="{ row }">
              {{ formatSalesStore(row.salesStore) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('salesConsultant')" prop="salesConsultant" min-width="110" show-overflow-tooltip>
            <template #default="{ row }">
              {{ formatSalesConsultant(row.salesConsultant) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('paymentMethod')" prop="paymentMethod" min-width="90">
            <template #default="{ row }">
              {{ formatPaymentMethod(row.paymentMethod) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('financeCompany')" prop="financeCompany" min-width="130" show-overflow-tooltip />
          <el-table-column :label="t('loanAmount')" prop="loanAmount" min-width="110" align="right">
            <template #default="{ row }">
              <span v-if="row.loanAmount > 0">RM {{ formatAmount(row.loanAmount) }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column :label="t('invoiceAmount')" prop="invoiceAmount" min-width="110" align="right">
            <template #default="{ row }">
              RM {{ formatAmount(row.invoiceAmount) }}
            </template>
          </el-table-column>
          <el-table-column :label="t('createdTime')" prop="createdTime" min-width="150" />

          <!-- 操作列 -->
          <el-table-column :label="tc('operations')" min-width="280" fixed="right" class-name="operations-column">
            <template #default="{ row }">
              <div class="operation-buttons">
                <el-button type="success" :icon="View" link @click="showDetail(row)" size="small">
                  {{ t('detail') }}
                </el-button>
                <el-button type="primary" :icon="Printer" link @click="handlePrint(row)" size="small">
                  {{ t('print') }}
                </el-button>
                <el-button type="primary" :icon="Message" link @click="showEmailDialog(row)" size="small">
                  {{ t('email') }}
                </el-button>
                <el-button type="info" :icon="Clock" link @click="showLogDialog(row)" size="small">
                  {{ t('log') }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 弹窗组件 -->
    <InvoiceDetailDialog
      v-model:visible="detailDialogVisible"
      :invoice-id="selectedInvoice?.id"
    />

    <EmailConfirmDialog
      v-model:visible="emailDialogVisible"
      :invoice-data="selectedInvoice"
      :loading="emailSending"
      @confirm="handleSendEmail"
    />

    <ExportConfigDialog
      v-model:visible="exportDialogVisible"
      :loading="exporting"
      @confirm="handleExport"
    />

    <OperationLogDialog
      v-model:visible="logDialogVisible"
      :invoice-id="selectedInvoice?.id"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { ElMessage } from 'element-plus';
import { Search, Download, Printer, View, Message, Clock } from '@element-plus/icons-vue';
import type {
  InvoiceSearchParams,
  InvoiceItem,
  ExportConfig,
  StoreOption,
  ConsultantOption
} from '@/types/sales/invoiceManagement';
import {
  getInvoiceList,
  printInvoice,
  batchPrintInvoices,
  sendInvoiceEmail,
  exportInvoiceData,
  getStoreList,
  getConsultantList
} from '@/api/modules/sales/invoiceManagement';
import InvoiceDetailDialog from './components/InvoiceDetailDialog.vue';
import EmailConfirmDialog from './components/EmailConfirmDialog.vue';
import ExportConfigDialog from './components/ExportConfigDialog.vue';
import OperationLogDialog from './components/OperationLogDialog.vue';

// 使用国际化
const { t } = useModuleI18n('sales.invoiceManagement');
const { t: tc } = useModuleI18n('common');

// 使用字典数据
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.SALES_TYPE,
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.SALES_CONSULTANT
]);

// 计算属性获取字典选项
const salesTypeOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.SALES_TYPE)
);
const allStoreOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.STORE)
);
const allConsultantOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.SALES_CONSULTANT)
);

// 数据定义
const loading = ref(false);
const tableData = ref<InvoiceItem[]>([]);
const selectedRows = ref<InvoiceItem[]>([]);
const selectedInvoice = ref<InvoiceItem | null>(null);

// 搜索参数（使用MyBatisPlus标准）
const searchParams = reactive<InvoiceSearchParams>({
  invoiceNumber: '',
  customerName: '',
  customerPhone: '',
  customerEmail: '',
  orderNumber: '',
  vin: '',
  salesType: '',
  salesStore: '',
  salesConsultant: ''
});

// 日期范围
const dateRange = ref<[string, string] | null>(null);

// 分页参数（使用MyBatisPlus标准）
const pagination = reactive({
  pageNum: 1,      // ✅ 使用pageNum
  pageSize: 20,
  total: 0
});

// 弹窗状态
const detailDialogVisible = ref(false);
const emailDialogVisible = ref(false);
const exportDialogVisible = ref(false);
const logDialogVisible = ref(false);

// 其他状态
const emailSending = ref(false);
const exporting = ref(false);

// 下拉选项
const storeOptions = ref<StoreOption[]>([]);
const consultantOptions = ref<ConsultantOption[]>([]);

// 字典转义函数
const formatSalesType = (type: string) =>
  getNameByCode(DICTIONARY_TYPES.SALES_TYPE, type) || type;

const formatPaymentMethod = (method: string) =>
  getNameByCode(DICTIONARY_TYPES.PAYMENT_METHOD, method) || method;

const formatSalesStore = (store: string) =>
  getNameByCode(DICTIONARY_TYPES.STORE, store) || store;

const formatSalesConsultant = (consultant: string) =>
  getNameByCode(DICTIONARY_TYPES.SALES_CONSULTANT, consultant) || consultant;

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 页面初始化
onMounted(() => {
  loadData();
  loadStoreOptions();
});

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchParams.invoiceDateStart = newVal[0];
    searchParams.invoiceDateEnd = newVal[1];
  } else {
    searchParams.invoiceDateStart = '';
    searchParams.invoiceDateEnd = '';
  }
});

// 构建搜索参数
const buildSearchParams = (): InvoiceSearchParams => {
  const params: InvoiceSearchParams = {
    pageNum: pagination.pageNum,      // ✅ 使用标准参数名
    pageSize: pagination.pageSize,
    ...searchParams
  };

  // 处理时间范围
  if (dateRange.value) {
    params.invoiceDateStart = dateRange.value[0];
    params.invoiceDateEnd = dateRange.value[1];
  }

  return filterEmptyParams(params);
};

// 过滤空值参数
const filterEmptyParams = (params: InvoiceSearchParams): InvoiceSearchParams => {
  const filteredParams: InvoiceSearchParams = {
    pageNum: params.pageNum,
    pageSize: params.pageSize
  };

  // 只保留非空值
  Object.keys(params).forEach(key => {
    const value = params[key as keyof InvoiceSearchParams];
    if (value && String(value).trim() !== '' && key !== 'pageNum' && key !== 'pageSize') {
      (filteredParams as any)[key] = value;
    }
  });

  return filteredParams;
};

// 标准API调用和响应处理
const loadData = async () => {
  try {
    loading.value = true;

    const params = buildSearchParams();
    const response = await getInvoiceList(params);

    // 标准响应处理
    if (response.code === '200' || response.code === 200) {
      tableData.value = response.result.records || [];
      pagination.total = response.result.total || 0;
      pagination.pageNum = response.result.pageNum || pagination.pageNum;
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取发票列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 加载门店选项
const loadStoreOptions = async () => {
  try {
    const stores = await getStoreList();
    storeOptions.value = stores.map(store => ({
      code: store.value,
      dealerName: store.label
    }));
  } catch (error) {
    console.error('获取门店列表失败:', error);
  }
};

// 加载销售顾问选项
const loadConsultantOptions = async (storeId?: string) => {
  try {
    const consultants = await getConsultantList(storeId);
    consultantOptions.value = consultants.map(consultant => ({
      code: consultant.value,
      name: consultant.label,
      storeCode: storeId
    }));
  } catch (error) {
    console.error('获取销售顾问列表失败:', error);
  }
};

// 门店变化处理（保持现有逻辑）
const handleStoreChange = (storeCode: string) => {
  searchParams.salesConsultant = '';

  if (storeCode) {
    loadConsultantOptions(storeCode);
  } else {
    consultantOptions.value = [];
  }
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    invoiceNumber: '',
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    orderNumber: '',
    vin: '',
    salesType: '',
    salesStore: '',
    salesConsultant: ''
  });
  dateRange.value = null;
  consultantOptions.value = [];
  handleSearch();
};

// 分页处理（保持MyBatisPlus标准）
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;    // ✅ 使用pageNum
  loadData();
};

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;  // ✅ 使用pageNum
  loadData();
};

// 选择变化
const handleSelectionChange = (selection: InvoiceItem[]) => {
  selectedRows.value = selection;
};

// 显示详情
const showDetail = (row: InvoiceItem) => {
  selectedInvoice.value = row;
  detailDialogVisible.value = true;
};

// 打印发票
const handlePrint = async (row: InvoiceItem) => {
  try {
    const response = await printInvoice(row.id);
    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('printSuccess'));
    } else {
      throw new Error(response.message || '打印失败');
    }
  } catch (error) {
    console.error('打印失败:', error);
    ElMessage.error(t('printFailed'));
  }
};

// 批量打印
const handleBatchPrint = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning(t('pleaseSelectRecords'));
    return;
  }

  try {
    const ids = selectedRows.value.map(row => row.id);
    const response = await batchPrintInvoices(ids);
    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('batchPrintSuccess'));
    } else {
      throw new Error(response.message || '批量打印失败');
    }
  } catch (error) {
    console.error('批量打印失败:', error);
    ElMessage.error(t('batchPrintFailed'));
  }
};

// 显示邮件弹窗
const showEmailDialog = (row: InvoiceItem) => {
  selectedInvoice.value = row;
  emailDialogVisible.value = true;
};

// 发送邮件
const handleSendEmail = async () => {
  if (!selectedInvoice.value) return;

  emailSending.value = true;
  try {
    const response = await sendInvoiceEmail(selectedInvoice.value.id, selectedInvoice.value.customerEmail);
    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('emailSentSuccess'));
      emailDialogVisible.value = false;
    } else {
      throw new Error(response.message || '邮件发送失败');
    }
  } catch (error) {
    console.error('邮件发送失败:', error);
    ElMessage.error(t('emailSentFailed'));
  } finally {
    emailSending.value = false;
  }
};

// 显示导出弹窗
const showExportDialog = () => {
  exportDialogVisible.value = true;
};

// 导出数据
const handleExport = async (config: ExportConfig) => {
  exporting.value = true;
  try {
    const params = {
      ...config,
      searchParams: config.scope === 'filtered' ? buildSearchParams() : undefined
    };

    const response = await exportInvoiceData(params);
    if (response.code === '200' || response.code === 200) {
      ElMessage.success(t('exportSuccess'));
      exportDialogVisible.value = false;
    } else {
      throw new Error(response.message || '导出失败');
    }
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('exportFailed'));
  } finally {
    exporting.value = false;
  }
};

// 显示日志弹窗
const showLogDialog = (row: InvoiceItem) => {
  selectedInvoice.value = row;
  logDialogVisible.value = true;
};
</script>

<style scoped lang="scss">
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.mb-20 {
  margin-bottom: 20px;
}

.search-card {
  .search-form {
    .buttons-col {
      display: flex;
      align-items: flex-end;

      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
}

.operation-card {
  .operation-buttons {
    display: flex;
    gap: 12px;
  }
}

.table-card {
  .table-container {
    overflow-x: auto;

    .el-table {
      .operations-column {
        .operation-buttons {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 0 20px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .page-container {
    padding: 16px;
  }

  .search-form {
    .el-col {
      margin-bottom: 16px;
    }
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 12px;
  }

  .page-title {
    font-size: 20px;
  }

  .search-form {
    .el-row {
      .el-col {
        span: 24 !important;
      }
    }
  }

  .operation-buttons {
    flex-direction: column;
    align-items: stretch;

    .el-button {
      margin-bottom: 8px;
    }
  }
}
</style>
