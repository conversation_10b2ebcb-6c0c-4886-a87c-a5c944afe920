<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('invoiceDetail')"
    width="900px"
    :destroy-on-close="true"
  >
    <div v-if="invoiceDetail" class="invoice-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3>{{ t('basicInfo') }}</h3>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('invoiceNumber') }}:</label>
              <span>{{ invoiceDetail.invoiceNumber }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('invoiceDate') }}:</label>
              <span>{{ invoiceDetail.invoiceDate }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('companyInfo') }}:</label>
              <span>{{ invoiceDetail.invoiceCompany }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('gstNumber') }}:</label>
              <span>{{ invoiceDetail.gstNumber }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('sstNumber') }}:</label>
              <span>{{ invoiceDetail.sstNumber }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('contactPhone') }}:</label>
              <span>{{ invoiceDetail.contactPhone }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('contactEmail') }}:</label>
              <span>{{ invoiceDetail.contactEmail }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('paymentMethod') }}:</label>
              <span>{{ invoiceDetail.paymentMethod }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 客户信息 -->
      <div class="detail-section">
        <h3>{{ t('customerInfo') }}</h3>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('customerName') }}:</label>
              <span>{{ invoiceDetail.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('customerPhone') }}:</label>
              <span>{{ invoiceDetail.customerPhone }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('customerEmail') }}:</label>
              <span>{{ invoiceDetail.customerEmail }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('customerAddress') }}:</label>
              <span>{{ invoiceDetail.customerAddress }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('state') }}:</label>
              <span>{{ invoiceDetail.customerState }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('city') }}:</label>
              <span>{{ invoiceDetail.customerCity}}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('postcode') }}:</label>
              <span>{{ invoiceDetail.customerPostcode}}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('orderNumber') }}:</label>
              <span>{{ invoiceDetail.orderNumber}}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('deliveryNumber') }}:</label>
              <span> {{ invoiceDetail.deliveryNumber}}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('salesConsultantId') }}:</label>
              <span>{{ invoiceDetail.salesConsultant}}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('salesConsultant') }}:</label>
              <span>{{ invoiceDetail.salesConsultant }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 车辆信息 -->
      <div class="detail-section">
        <h3>{{ t('vehicleInfo') }}</h3>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('color') }}:</label>
              <span>{{ invoiceDetail.color }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('tinNumber') }}:</label>
              <span>{{ invoiceDetail.tinNumber }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('sstNumber') }}:</label>
              <span>{{ invoiceDetail.sstNumber }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('modelCode') }}:</label>
              <span>{{ invoiceDetail.modelCode  }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('modelDescription') }}:</label>
              <span>{{ invoiceDetail.modelDescription  }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('engineNumber') }}:</label>
              <span>{{ invoiceDetail.engineNumber  }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('engineDisplacement') }}:</label>
              <span>{{ invoiceDetail.engineDisplacement  }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('chassisNumber') }}:</label>
              <span>{{ invoiceDetail.chassisNumber  }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('vehicleRegistrationDate') }}:</label>
              <span>{{ invoiceDetail.vehicleRegistrationDate  }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ tc('creator') }}:</label>
              <span>{{ invoiceDetail.creator }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ tc('updater') }}:</label>
              <span>{{ invoiceDetail.updater  }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ tc('updateTime') }}:</label>
              <span>{{ invoiceDetail.updateTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 金融信息 -->
      <div class="detail-section">
        <h3>{{ t('financeInfo') }}</h3>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('financeType') }}:</label>
              <span>{{ invoiceDetail.financeType }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('financeCompany') }}:</label>
              <span>{{ invoiceDetail.financeCompany }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('loanAmount') }}:</label>
              <span v-if="invoiceDetail.loanAmount > 0">RM {{ invoiceDetail.loanAmount }}</span>
              <span v-else>-</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('loanPeriod') }}:</label>
              <span>{{ invoiceDetail.loanPeriod }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 保险信息详情 -->
      <div class="detail-section">
        <h3>{{ t('insuranceInfo') }}</h3>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('insuranceCompany') }}:</label>
              <span>{{ invoiceDetail.insuranceCompany }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('agentCode') }}:</label>
              <span>{{ invoiceDetail.agentCode }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('policyNumber') }}:</label>
              <span>{{ invoiceDetail.policyNumber }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="detail-item">
              <label>{{ t('issueDate') }}:</label>
              <span>{{ invoiceDetail.issueDate }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 价格结构明细 -->
      <div class="detail-section">
        <h3>{{ t('priceStructureDetails') }}</h3>
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="detail-item">
              <label>{{ t('vehicleSalesPrice') }}:</label>
              <span>{{ invoiceDetail.vehicleSalesPrice }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>{{ t('licensePlateFee') }}:</label>
              <span>{{ invoiceDetail.licensePlateFee }}</span>
            </div>
          </el-col>
        </el-row>

        <h4>{{ t('optionalAccessories') }}</h4>
        <el-table :data="invoiceDetail.accessories || []" border style="width: 100%; margin-bottom: 20px;">
          <el-table-column :label="t('category')" prop="specification" min-width="100" />
          <el-table-column :label="t('accessoryName')" prop="accessoryName" min-width="150" />
          <el-table-column :label="t('unitPrice')" prop="unitPrice" min-width="100" align="right">
            <template #default="{ row }">RM {{ row.unitPrice }}</template>
          </el-table-column>
          <el-table-column :label="t('quantity')" prop="quantity" min-width="80" align="center" />
          <el-table-column :label="t('totalPrice')" prop="amount" min-width="120" align="right">
            <template #default="{ row }">RM {{ row.amount }}</template>
          </el-table-column>
        </el-table>
        <div class="detail-item text-right">
          <label>{{ t('totalAccessoryAmount') }}:</label>
          <span>RM {{ invoiceDetail.accessoryAmount }}</span>
        </div>

        <div class="detail-item text-right">
          <label>{{ t('subtotal') }}:</label>
          <span>RM {{ invoiceDetail.invoiceAmount }}</span>
        </div>

        <h4>{{ t('otrRegistrationFees') }}</h4>
        <el-table :data="invoiceDetail.otrFees || []" border style="width: 100%; margin-bottom: 20px;">
          <el-table-column :label="t('billNumber')" prop="feeCode" min-width="120" />
          <el-table-column :label="t('feeItem')" prop="feeType" min-width="150" />
          <el-table-column :label="t('feePrice')" prop="taxAmount" min-width="100" align="right">
            <template #default="{ row }">RM {{ row.taxAmount }}</template>
          </el-table-column>
          <el-table-column :label="t('effectiveDate')" prop="effectiveDate" min-width="120" />
          <el-table-column :label="t('expiryDate')" prop="expiryDate" min-width="120" />
        </el-table>
        <div class="detail-item text-right">
          <label>{{ t('totalOtrFeeAmount') }}:</label>
          <span>RM {{ invoiceDetail.otrAmount }}</span>
        </div>

        <div class="detail-item text-right">
          <label>{{ t('insurancePremium') }}:</label>
          <span>RM {{ invoiceDetail.insuranceAmount }}</span>
        </div>

        <div class="detail-item text-right">
          <label>{{ t('totalSalesPrice') }}:</label>
          <span>RM {{ invoiceDetail.vehicleSalesPrice }}</span>
        </div>

        <div class="detail-item text-right">
          <label>{{ t('adjustmentAmount') }}:</label>
          <span>RM {{ invoiceDetail.adjustmentAmount }}</span>
        </div>

        <div class="detail-item text-right">
          <label>{{ t('invoiceNetValue') }}:</label>
          <span>RM {{ invoiceDetail.invoiceNetValue }}</span>
        </div>
      </div>

      <!-- 收据明细信息 -->
      <div class="detail-section">
        <h3>{{ t('receiptDetails') }}</h3>
        <el-table :data="invoiceDetail.receipts || []" border style="width: 100%;">
          <el-table-column :label="t('receiptNumber')" prop="receiptNumber" min-width="120" />
          <el-table-column :label="t('businessType')" prop="receiptType" min-width="100" />
          <el-table-column :label="t('serialNumber')" prop="receiptNo" min-width="120" />
          <el-table-column :label="t('channel')" prop="paymentChannel" min-width="100" />
          <el-table-column :label="t('amount')" prop="paidAmount" min-width="100" align="right">
            <template #default="{ row }">RM {{ row.paidAmount }}</template>
          </el-table-column>
          <el-table-column :label="t('collectionType')" prop="collectionType" min-width="100" />
          <el-table-column :label="t('arrivalTime')" prop="arrivalTime" min-width="160" />
          <el-table-column :label="t('remark')" prop="remarks" min-width="150" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { ElMessage } from 'element-plus';
import type { InvoiceDetail } from '@/types/sales/invoiceManagement';
import { getInvoiceDetail } from '@/api/modules/sales/invoiceManagement';

// Props
interface Props {
  visible: boolean;
  invoiceId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  invoiceId: ''
});

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 使用国际化
const { t } = useModuleI18n('sales.invoiceManagement');
const { t: tc } = useModuleI18n('common');

// 使用字典数据
const { getNameByCode } = useDictionary(DICTIONARY_TYPES.PAYMENT_METHOD);

// 数据定义
const loading = ref(false);
const invoiceDetail = ref<InvoiceDetail | null>(null);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
});

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.invoiceId) {
    loadInvoiceDetail();
  }
});

// 监听发票ID变化
watch(() => props.invoiceId, (newId) => {
  if (newId && props.visible) {
    loadInvoiceDetail();
  }
});

// 加载发票详情
const loadInvoiceDetail = async () => {
  if (!props.invoiceId) return;

  try {
    loading.value = true;
    const response = await getInvoiceDetail(props.invoiceId);

    if (response.code === '200' || response.code === 200) {
      invoiceDetail.value = response.result;
    } else {
      throw new Error(response.message || '获取发票详情失败');
    }
  } catch (error) {
    console.error('获取发票详情失败:', error);
    ElMessage.error(tc('loadFailed'));
    invoiceDetail.value = null;
  } finally {
    loading.value = false;
  }
};

// 格式化付款方式
const formatPaymentMethod = (method: string) => {
  return getNameByCode(method) || method;
};

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
  invoiceDetail.value = null;
};
</script>

<style scoped lang="scss">
.invoice-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }

  h4 {
    font-size: 14px;
    font-weight: 600;
    color: #606266;
    margin: 16px 0 12px 0;
  }
}

.detail-item {
  margin-bottom: 12px;

  label {
    display: inline-block;
    width: 120px;
    font-weight: 500;
    color: #606266;
    margin-right: 8px;
  }

  span {
    color: #303133;
  }

  &.text-right {
    text-align: right;
    margin-bottom: 8px;

    label {
      width: auto;
      margin-right: 12px;
    }
  }
}

:deep(.el-table) {
  margin-bottom: 16px;
}
</style>
