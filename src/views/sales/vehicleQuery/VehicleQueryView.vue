<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索/筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('vin')">
              <el-input v-model="searchParams.vin" :placeholder="t('vinPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('factoryOrderNo')">
              <el-input v-model="searchParams.factoryOrderNo" :placeholder="t('factoryOrderNoPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('warehouseName')">
              <el-select v-model="searchParams.warehouseName" :placeholder="tc('all')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.WAREHOUSE)"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('model')">
              <el-select v-model="searchParams.model" :placeholder="tc('all')" clearable @change="handleModelChange">
                <el-option :label="tc('all')" value="" />
                <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('variant')">
              <el-select v-model="searchParams.variant" :placeholder="tc('all')" clearable @change="handleVariantChange">
                <el-option :label="tc('all')" value="" />
                <el-option v-for="item in variantOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('color')">
              <el-select v-model="searchParams.color" :placeholder="tc('all')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.VEHICLE_COLOR)"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('fmrId')">
              <el-input v-model="searchParams.fmrId" :placeholder="t('fmrIdPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('lockStatus')">
              <el-select v-model="searchParams.lockStatus" :placeholder="tc('all')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.VEHICLE_LOCK_STATUS)"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('invoiceStatus')">
              <el-select v-model="searchParams.invoiceStatus" :placeholder="tc('all')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.VEHICLE_INVOICE_STATUS)"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('invoiceDate')">
              <el-date-picker
                v-model="dateRange.invoice"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('storageDate')">
              <el-date-picker
                v-model="dateRange.storage"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('productionDate')">
              <el-date-picker
                v-model="dateRange.production"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="buttons-col">
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20 operation-card">
      <el-row>
        <el-col :span="24" class="operation-buttons-col">
          <el-button :icon="Download" @click="openExportDialog">{{ tc('export') }}</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card class="table-card">
      <el-table :data="vehicleList" v-loading="loading" style="width: 100%">
        <el-table-column type="index" :label="tc('index')" width="60" />
        <el-table-column prop="factoryOrderNo" :label="t('factoryOrderNo')" min-width="150" />
        <el-table-column prop="vin" :label="t('vin')" min-width="150" />
        <el-table-column prop="model" :label="t('model')" min-width="120" />
        <el-table-column prop="variant" :label="t('variant')" min-width="120" />
        <el-table-column prop="color" :label="t('color')" min-width="100" />
        <el-table-column prop="fmrId" :label="t('fmrId')" min-width="120" />
        <el-table-column prop="warehouseName" :label="t('warehouseName')" min-width="120" />
        <el-table-column prop="stockStatus" :label="t('stockStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.stockStatus, STATUS_TYPE_MAPS.STOCK_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.INVENTORY_STATUS, row.stockStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lockStatus" :label="t('lockStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.lockStatus, STATUS_TYPE_MAPS.LOCK_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_LOCK_STATUS, row.lockStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceStatus" :label="t('invoiceStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.invoiceStatus, STATUS_TYPE_MAPS.INVOICE_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_INVOICE_STATUS, row.invoiceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceDate" :label="t('invoiceDate')" min-width="120" />
        <el-table-column prop="deliveryStatus" :label="t('deliveryStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.deliveryStatus, STATUS_TYPE_MAPS.DELIVERY_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.DELIVERY_STATUS, row.deliveryStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="deliveryDate" :label="t('deliveryDate')" min-width="120" />
        <el-table-column prop="storageDate" :label="t('storageDate')" min-width="120" />
        <el-table-column prop="productionDate" :label="t('productionDate')" min-width="120" />
        <el-table-column :label="tc('operations')" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="View" link @click="openDetailDialog(row)">{{ tc('detail') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态处理 -->
      <div v-if="!vehicleList.length && !loading" class="empty-state">
        <el-icon><el-icon-inbox /></el-icon>
        <h3>{{ tc('noData') }}</h3>
        <p>{{ tc('noDataTip') }}</p>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :layout="paginationLayout"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 车辆详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="t('detailDialogTitle')"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-if="currentVehicleDetail" class="detail-content">
        <el-descriptions :column="3" border>
          <el-descriptions-item :label="t('factoryOrderNo')">
            {{ currentVehicleDetail.factoryOrderNo }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('vin')">
            {{ currentVehicleDetail.vin }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('model')">
            {{ currentVehicleDetail.model }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('variant')">
            {{ currentVehicleDetail.variant }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('color')">
            {{ currentVehicleDetail.color }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('fmrId')">
            {{ currentVehicleDetail.fmrId }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('warehouseName')">
            {{ currentVehicleDetail.warehouseName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('stockStatus')">
            <el-tag :type="getStatusTagType(currentVehicleDetail.stockStatus, STATUS_TYPE_MAPS.STOCK_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.INVENTORY_STATUS, currentVehicleDetail.stockStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('lockStatus')">
            <el-tag :type="getStatusTagType(currentVehicleDetail.lockStatus, STATUS_TYPE_MAPS.LOCK_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_LOCK_STATUS, currentVehicleDetail.lockStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('invoiceStatus')">
            <el-tag :type="getStatusTagType(currentVehicleDetail.invoiceStatus, STATUS_TYPE_MAPS.INVOICE_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_INVOICE_STATUS, currentVehicleDetail.invoiceStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('deliveryStatus')">
            <el-tag :type="getStatusTagType(currentVehicleDetail.deliveryStatus, STATUS_TYPE_MAPS.DELIVERY_STATUS)">
              {{ getNameByCode(DICTIONARY_TYPES.DELIVERY_STATUS, currentVehicleDetail.deliveryStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('invoiceDate')">
            {{ currentVehicleDetail.invoiceDate }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('deliveryDate')">
            {{ currentVehicleDetail.deliveryDate }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('storageDate')">
            {{ currentVehicleDetail.storageDate }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('productionDate')">
            {{ currentVehicleDetail.productionDate }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">{{ tc('close') }}</el-button>
      </template>
    </el-dialog>

    <!-- 导出弹窗 -->
    <el-dialog
      v-model="exportDialogVisible"
      :title="t('exportDialogTitle')"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="exportForm" label-width="100px">
        <el-form-item :label="t('exportFormat')">
          <el-select v-model="exportForm.format" :placeholder="t('exportFormatPlaceholder')">
            <el-option :label="t('excel')" value="excel" />
            <el-option :label="t('csv')" value="csv" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('exportScope')">
          <el-radio-group v-model="exportForm.scope">
            <el-radio label="current">{{ t('exportCurrentPage') }}</el-radio>
            <el-radio label="all">{{ t('exportAllData') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="exportDialogVisible = false">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="confirmExport">{{ tc('confirm') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { Search, Download, View, Box as ElIconInbox } from '@element-plus/icons-vue';
import { ElMessage, ElNotification } from 'element-plus';
import type {
  VehicleQueryItem,
  VehicleQuerySearchParams,
  VehicleQueryDetail,
  VehicleConfiguration,
  VehicleVariant,
  ExportForm
} from '@/types/sales/vehicleQuery';
import { getVehicleQuery, getWarehouseInfo, getVehicleModelList } from '@/api/modules/sales/vehicleQuery';

// 国际化
const { t } = useModuleI18n('sales.vehicleQuery');
const { t: tc } = useModuleI18n('common');

// 数据字典
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.WAREHOUSE,
  DICTIONARY_TYPES.VEHICLE_COLOR,
  DICTIONARY_TYPES.INVENTORY_STATUS,
  DICTIONARY_TYPES.VEHICLE_LOCK_STATUS,
  DICTIONARY_TYPES.VEHICLE_INVOICE_STATUS,
  DICTIONARY_TYPES.DELIVERY_STATUS
]);

// 状态标签样式映射
const STATUS_TYPE_MAPS = {
  STOCK_STATUS: {
    '01200001': 'success',    // 在库
    '01200002': 'warning',    // 配车
    '01200003': 'primary',    // 在途
    '01200004': 'info'        // 调拨
  },
  LOCK_STATUS: {
    '01440001': 'danger',     // 已锁定
    '01440002': 'success'     // 未锁定
  },
  INVOICE_STATUS: {
    '01450001': 'success',    // 已开票
    '01450002': 'info'        // 未开票
  },
  DELIVERY_STATUS: {
    '01220001': 'success',    // 已交车
    '01220002': 'info'        // 未交车
  }
};

// 通用状态标签类型获取函数
const getStatusTagType = (status: string, typeMap: Record<string, string>) => {
  return typeMap[status] || 'info';
};

// --- 搜索条件相关 ---
const searchParams = reactive<VehicleQuerySearchParams>({
  vin: '',
  factoryOrderNo: '',
  warehouseName: '',
  model: '',
  variant: '',
  color: '',
  fmrId: '',
  lockStatus: '',
  invoiceStatus: '',
});

const dateRange = reactive({
  invoice: null as [string, string] | null,
  storage: null as [string, string] | null,
  production: null as [string, string] | null,
});

const modelOptions = ref<{ label: string; value: string }[]>([]);
const allConfigurations = ref<VehicleConfiguration[]>([]);
const variantOptions = ref<{ label: string; value: string }[]>([]);

// 处理车型选择变化
const handleModelChange = (val: string) => {
  searchParams.variant = '';
  searchParams.color = '';
  updateVariantOptions(val);
};

// 处理配置选择变化
const handleVariantChange = (val: string) => {
  searchParams.color = '';
};

// 更新 Variant 选项
const updateVariantOptions = (model: string) => {
  const selectedModel = allConfigurations.value.find(config => config.model === model);
  if (selectedModel) {
    variantOptions.value = selectedModel.variants.map((v: VehicleVariant) => ({
      label: v.variant,
      value: v.variant
    }));
  } else {
    variantOptions.value = [];
  }
};

// 搜索
const handleSearch = () => {
  // 组装日期范围到 searchParams
  searchParams.invoiceDateStart = dateRange.invoice?.[0];
  searchParams.invoiceDateEnd = dateRange.invoice?.[1];
  searchParams.storageDateStart = dateRange.storage?.[0];
  searchParams.storageDateEnd = dateRange.storage?.[1];
  searchParams.productionDateStart = dateRange.production?.[0];
  searchParams.productionDateEnd = dateRange.production?.[1];

  pagination.pageNum = 1; // 搜索时重置页码
  fetchVehicleList();
};

// 重置
const resetSearch = () => {
  Object.assign(searchParams, {
    vin: '',
    factoryOrderNo: '',
    warehouseName: '',
    model: '',
    variant: '',
    color: '',
    fmrId: '',
    lockStatus: '',
    invoiceStatus: '',
    invoiceDateStart: undefined,
    invoiceDateEnd: undefined,
    storageDateStart: undefined,
    storageDateEnd: undefined,
    productionDateStart: undefined,
    productionDateEnd: undefined,
  });
  dateRange.invoice = null;
  dateRange.storage = null;
  dateRange.production = null;
  variantOptions.value = [];
  handleSearch(); // 重置后执行一次搜索
};

// --- 表格数据相关 ---
const vehicleList = ref<VehicleQueryItem[]>([]);
const loading = ref(false);

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
});

const paginationLayout = computed(() => {
  return tc('paginationLayout'); // 使用国际化获取布局字符串
});

// 获取车辆列表
const fetchVehicleList = async () => {
  loading.value = true;
  try {
    // 过滤空值参数，只传递有值的参数
    const filteredSearchParams: Partial<VehicleQuerySearchParams> = {};

    // 遍历搜索参数，只添加非空值
    (Object.keys(searchParams) as (keyof VehicleQuerySearchParams)[]).forEach((key) => {
      const value = searchParams[key];
      if (value !== '' && value !== null && value !== undefined) {
        filteredSearchParams[key] = value;
      }
    });

    const requestParams = {
      ...filteredSearchParams,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    };

    const response = await getVehicleQuery(requestParams);
    vehicleList.value = response.result.records;
    pagination.total = response.result.total;
  } catch (error) {
    console.error('获取车辆列表失败:', error);
    ElMessage.error(tc('fetchFailed'));
  } finally {
    loading.value = false;
  }
};

// 处理每页显示条数变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  fetchVehicleList();
};

// 处理当前页码变化
const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;
  fetchVehicleList();
};

// --- 详情弹窗相关 ---
const detailDialogVisible = ref(false);
const currentVehicleDetail = ref<VehicleQueryDetail | null>(null);

const openDetailDialog = (row: VehicleQueryItem) => {
  currentVehicleDetail.value = row;
  detailDialogVisible.value = true;
};

// --- 导出弹窗相关 ---
const exportDialogVisible = ref(false);
const exportForm = reactive<ExportForm>({
  format: 'excel',
  scope: 'current',
});

const openExportDialog = () => {
  exportDialogVisible.value = true;
};

const confirmExport = () => {
  // 模拟导出操作
  ElNotification({
    title: tc('success'),
    message: t('exportSuccess', { format: exportForm.format }),
    type: 'success',
  });
  exportDialogVisible.value = false;
};

// --- 生命周期钩子 ---
onMounted(async () => {
  // 初始化获取车型配置信息
  try {
    allConfigurations.value = await getVehicleModelList();
    modelOptions.value = allConfigurations.value.map(config => ({
      label: config.modelName,
      value: config.code
    }));
  } catch (error) {
    console.error('获取车型配置失败:', error);
    ElMessage.error(t('fetchConfigFailed'));
  }

  fetchVehicleList(); // 页面加载时获取初始列表
});
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;

  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    color: #333;
  }

  .search-card,
  .operation-card,
  .table-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .search-form {
    .el-form-item {
      margin-right: 20px;
      margin-bottom: 15px;
      &:last-child {
        margin-right: 0;
      }
    }

    .el-date-editor {
      width: 100%;
    }
  }

  .buttons-col {
    text-align: right;
    .el-button {
      margin-left: 10px;
      &:first-child {
        margin-left: 0;
      }
    }
  }

  .operation-buttons-col {
    text-align: right;
    .el-button {
      margin-left: 10px;
      &:first-child {
        margin-left: 0;
      }
    }
  }

  .table-card {
    margin-bottom: 20px;
    :deep(.el-table) {
      .el-table__body td,
      .el-table__header th {
        white-space: nowrap;
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    color: #909399;
    .el-icon {
      font-size: 60px;
      margin-bottom: 20px;
    }
    h3 {
      margin: 0 0 10px 0;
      color: #909399;
    }
    p {
      margin: 0;
      font-size: 14px;
      color: #c0c4cc;
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .detail-content {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }
}
</style>
