<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('vehicleModel.title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('vehicleModel.model')">
              <el-select
                v-model="searchParams.model"
                :placeholder="tc('all')"
                clearable
                @change="handleModelChange"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in modelOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleModel.variantName')">
              <el-select
                v-model="searchParams.variantName"
                :placeholder="tc('all')"
                clearable
                :disabled="!searchParams.model"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in variantOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleModel.colourName')">
              <el-select
                v-model="searchParams.colourName"
                :placeholder="tc('all')"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in colourOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('vehicleModel.fmrid')">
              <el-input
                v-model="searchParams.fmrid"
                :placeholder="t('vehicleModel.fmridPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="buttons-col">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ tc('search') }}
            </el-button>
            <el-button @click="resetSearch">
              {{ tc('reset') }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20">
        <el-col :span="24" class="buttons-col">
          <el-button type="primary" :icon="Refresh" @click="handleSync" :loading="syncLoading">
            {{ t('vehicleModel.syncData') }}
          </el-button>
          <el-button :icon="Document" @click="handleSyncLog">
            {{ t('vehicleModel.syncLog') }}
          </el-button>
          <el-button :icon="Download" @click="handleExport">
            {{ t('vehicleModel.exportData') }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" stripe>
        <el-table-column type="index" :label="tc('serialNumber')" width="80" />
        <el-table-column :label="t('vehicleModel.model')" prop="model" min-width="120">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_MODEL, row.model) || row.model }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleModel.variantName')" prop="variantName" min-width="150">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_VARIANT, row.variantName) || row.variantName }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleModel.variantCode')" prop="variantCode" min-width="120" />
        <el-table-column :label="t('vehicleModel.colourName')" prop="colourName" min-width="150">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_COLOR, row.colourName) || row.colourName }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleModel.colourCode')" prop="colourCode" min-width="120" />
        <el-table-column :label="t('vehicleModel.fmrid')" prop="fmrid" min-width="100" />
        <el-table-column :label="t('vehicleModel.createTime')" prop="createTime" min-width="160">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('vehicleModel.updateTime')" prop="updateTime" min-width="160">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 同步日志弹窗 -->
    <SyncLogDialog ref="syncLogDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { Search, Refresh, Document, Download } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getVehicleModelList, syncVehicleModelData, exportVehicleModelData } from '@/api/modules/sales/vehicleModel';
import type { VehicleModelMasterItem, VehicleModelSearchParams } from '@/types/sales/vehicleModel';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import SyncLogDialog from './components/SyncLogDialog.vue';

// ✅ 使用规范的国际化引用
const { t, tc } = useModuleI18n('sales');

// 搜索参数
const searchParams = reactive<VehicleModelSearchParams>({
  model: '',
  variantName: '',
  colourName: '',
  fmrid: ''
});

// 表格数据
const tableData = ref<VehicleModelMasterItem[]>([]);
const loading = ref(false);
const syncLoading = ref(false);

// ✅ 修正为MyBatisPlus标准分页
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 20,
  total: 0
});

// ✅ 使用标准字典组合式函数
const { getOptions, getNameByCode } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.VEHICLE_VARIANT,
  DICTIONARY_TYPES.VEHICLE_COLOR
]);

// 下拉选项
const modelOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_MODEL));
const variantOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_VARIANT));
const colourOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_COLOR));

// 弹窗引用
const syncLogDialogRef = ref();

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const response = await getVehicleModelList({
      ...searchParams,
      pageNum: pagination.pageNum,    // ✅ 修正为pageNum
      pageSize: pagination.pageSize
    });

    tableData.value = response.records;  // ✅ 标准响应处理
    pagination.total = response.total;   // ✅ 标准响应处理
  } catch (error) {
    console.error('获取车型数据失败:', error);
    ElMessage.error(tc('fetchFailed'));
  } finally {
    loading.value = false;
  }
};

// 车型变化时更新版本选项
const handleModelChange = () => {
  searchParams.variantName = '';
  // 根据选择的车型过滤版本选项
  // 这里可以根据实际业务逻辑实现联动
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  fetchData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    model: '',
    variantName: '',
    colourName: '',
    fmrid: ''
  });
  pagination.pageNum = 1;
  fetchData();
};

// 同步数据
const handleSync = async () => {
  syncLoading.value = true;
  try {
    const result = await syncVehicleModelData();
    if (result.success) {
      ElMessage.success(t('syncSuccess'));
      fetchData();
    }
  } catch (error) {
    console.error('同步失败:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    syncLoading.value = false;
  }
};

// 查看同步日志
const handleSyncLog = () => {
  syncLogDialogRef.value?.open();
};

// 导出数据
const handleExport = async () => {
  try {
    const blob = await exportVehicleModelData(searchParams);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `vehicle-model-${new Date().getTime()}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
    ElMessage.success(t('exportSuccess'));
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// ✅ 修正为MyBatisPlus标准分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  fetchData();
};

const handleCurrentChange = (val: number) => {
  pagination.pageNum = val;  // ✅ 修正为pageNum
  fetchData();
};

// 日期格式化函数
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  return dateString.replace('T', ' ').slice(0, 19);
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.search-card,
.operation-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-right: 20px;
    margin-bottom: 15px;
    &:last-child {
      margin-right: 0;
    }
  }
}

.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.table-card {
  margin-bottom: 20px;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
