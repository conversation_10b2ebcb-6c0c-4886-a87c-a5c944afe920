<template>
  <el-dialog
    v-model="visible"
    :title="t('vehicleModel.syncLogTitle')"
    width="800px"
    @close="handleClose"
  >
    <el-table :data="logData" v-loading="loading" stripe>
      <el-table-column :label="t('No')" type="index" width="80" />
      <el-table-column :label="t('syncTime')" prop="syncTime" min-width="160" />
      <el-table-column :label="tc('status')" prop="status" min-width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
            {{ tc(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('recordCount')" prop="recordCount" min-width="120" />
      <el-table-column :label="t('errorMessage')" prop="errorMessage" min-width="200">
        <template #default="{ row }">
          <span>{{ row.errorMessage || '-' }}</span>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="handleClose">{{ tc('close') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getSyncLogs } from '@/api/modules/sales/vehicleModel';
import type { SyncLogItem } from '@/types/sales/vehicleModel';

const { t, tc } = useModuleI18n('sales');

const visible = ref(false);
const loading = ref(false);
const logData = ref<SyncLogItem[]>([]);

const open = async () => {
  visible.value = true;
  loading.value = true;
  try {
    logData.value = await getSyncLogs();
  } catch (error) {
    console.error('Failed to fetch sync logs:', error);
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  visible.value = false;
  logData.value = [];
};

defineExpose({
  open
});
</script>

<style scoped lang="scss">
.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  .el-button {
    margin-left: 10px;
  }
}
</style>
