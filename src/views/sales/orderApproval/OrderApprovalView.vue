<!--
  订单审批管理页面 - 重构版本
  已完成国际化改造，使用 useModuleI18n('sales.orderApproval') 和 useModuleI18n('common')
  支持中英文切换，符合MyBatisPlus分页规范和数据字典标准化
-->
<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('pageTitle') }}</h1>

    <!-- Tab切换区域 -->
    <el-card class="mb-20">
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane :label="t('pendingTab')" name="pending">
        </el-tab-pane>
        <el-tab-pane :label="t('approvedTab')" name="approved">
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 筛选功能区域 -->
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('searchTitle') }}</span>
        </div>
      </template>

      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('approvalType')">
              <el-select
                v-model="searchParams.approvalType"
                :placeholder="t('approvalTypePlaceholder')"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('approvalTypeOptions.modify_info')" value="modify_info" />
                <el-option :label="t('approvalTypeOptions.cancel_order')" value="cancel_order" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input v-model="searchParams.orderNo" :placeholder="t('orderNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesConsultant')">
              <el-select
                v-model="searchParams.salesConsultantId"
                :placeholder="t('salesConsultantPlaceholder')"
                clearable
                filterable
                :loading="consultantLoading"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="item in salesConsultantOptions"
                  :key="item.userId"
                  :label="item.userName"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('submissionTime')">
              <el-date-picker
                v-model="submissionTimeRange"
                type="daterange"
                :start-placeholder="t('startDate')"
                :end-placeholder="t('endDate')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                @change="handleDateRangeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6" v-if="activeTab === 'approved'">
            <el-form-item :label="t('approvalResult')">
              <el-select
                v-model="searchParams.approvalResult"
                :placeholder="t('approvalResultPlaceholder')"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('approvalResultOptions.approved')" value="approved" />
                <el-option :label="t('approvalResultOptions.rejected')" value="rejected" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="18" class="search-buttons">
            <el-button type="primary" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="handleReset">{{ tc('reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据列表区域 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <div>
            <span>{{ getListTitle() }}</span>
            <span class="total-count">{{ t('totalCount', { count: pagination.total }) }}</span>
          </div>
          <el-button v-if="canExport" type="success" @click="handleExport">{{ tc('export') }}</el-button>
        </div>
      </template>

      <el-table
        :data="tableData"
        :loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column type="index" :label="t('serialNumber')" width="70" />
        <el-table-column prop="approvalNo" :label="t('approvalNumber')" width="160" />
        <el-table-column prop="approvalType" :label="t('approvalType')" width="140">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.approvalType, DICTIONARY_TYPES.APPROVAL_TYPE)">
              {{ getName(DICTIONARY_TYPES.APPROVAL_TYPE, row.approvalType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderNo" :label="t('orderNumber')" width="140">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleViewOrderDetails()">
              {{ row.orderNo }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="salesConsultantName" :label="t('salesConsultant')" width="100" />
        <el-table-column prop="submitTime" :label="t('submissionTime')" width="150" />
        <el-table-column prop="reason" :label="t('applicationReason')" width="180">
          <template #default="{ row }">
            {{ row.reason || 'N/A' }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeTab === 'approved'"
          prop="approvalResult"
          :label="t('approvalResult')"
          width="120"
        >
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.approvalResult, DICTIONARY_TYPES.APPROVAL_STATUS)">
              {{ getName(DICTIONARY_TYPES.APPROVAL_STATUS, row.approvalResult) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeTab === 'approved'"
          prop="approvalTime"
          :label="t('approvalTime')"
          width="150"
        />
        <el-table-column
          v-if="activeTab === 'approved'"
          prop="approvalUserName"
          :label="t('approvedBy')"
          width="100"
        />
        <el-table-column :label="tc('operations')" width="200" fixed="right">
          <template #default="{ row }">
            <el-button v-if="activeTab === 'approved'" link type="primary" @click="handleViewDetails(row)">
              {{ tc('detail') }}
            </el-button>
            <el-button
              v-if="activeTab === 'pending'"
              link
              type="primary"
              @click="handleReview(row)"
            >
              {{ t('review') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 审核详情弹窗 -->
    <ApprovalDetailDialog
      :visible="detailDialogVisible"
      :approval-data="selectedApproval"
      @update:visible="detailDialogVisible = $event"
      @close="detailDialogVisible = false"
    />

    <!-- 审核操作弹窗 -->
    <ApprovalActionDialog
      :visible="actionDialogVisible"
      :approval-id="selectedApproval?.id || null"
      :approval-detail="selectedApproval"
      :action-type="currentActionType"
      @update:visible="actionDialogVisible = $event"
      @success="handleActionSuccess"
    />

    <!-- 审核历史弹窗 -->
    <ApprovalHistoryDialog
      :visible="historyDialogVisible"
      :approval-id="selectedApproval?.id || null"
      @update:visible="historyDialogVisible = $event"
    />

    <!-- 导出设置弹窗 -->
    <ApprovalExportDialog
      :visible="exportDialogVisible"
      :filter-params="searchParams"
      @update:visible="exportDialogVisible = $event"
      @success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import { getStoreList } from '@/api/modules/masterData'
import { getOrderApprovalList } from '@/api/modules/sales/orderApproval'
import type {
  OrderApprovalListItem,
  OrderApprovalSearchParams,
  ApprovalType,
  ApprovalResult,
  UserRole,
  Store
} from '@/types/sales/orderApproval'
import type { DictionaryType } from '@/types/dictionary'
import ApprovalDetailDialog from './components/ApprovalDetailDialog.vue'
import ApprovalActionDialog from './components/ApprovalActionDialog.vue'
import ApprovalHistoryDialog from './components/ApprovalHistoryDialog.vue'
import ApprovalExportDialog from './components/ApprovalExportDialog.vue'

const { t } = useModuleI18n('sales.orderApproval')
const { tc } = useModuleI18n('common')

// 使用标准化字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.APPROVAL_TYPE,
  DICTIONARY_TYPES.APPROVAL_STATUS
])

// 获取字典选项
const approvalTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_TYPE))
const approvalStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.APPROVAL_STATUS))

// 主数据
const storeOptions = ref<Store[]>([])
const masterDataLoading = ref(false)

// 销售顾问数据
const salesConsultantOptions = ref<{ userId: string; userName: string }[]>([])
const consultantLoading = ref(false)

// 当前用户角色和权限
const currentUserRole = ref<UserRole>('sales_manager')

// Tab状态
const activeTab = ref<'pending' | 'approved'>('pending')

// 搜索参数（标准MyBatisPlus分页）
const searchParams = reactive<OrderApprovalSearchParams>({
  approvalType: '',
  orderNo: '',
  salesConsultantId: '',
  submitTimeStart: '',
  submitTimeEnd: '',
  approvalResult: '',
  dealerId: ''
})

const submissionTimeRange = ref<[string, string] | ''>('')

// 表格数据
const tableData = ref<OrderApprovalListItem[]>([])
const loading = ref(false)

// 标准MyBatisPlus分页
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 20,
  total: 0
})

// 弹窗控制
const detailDialogVisible = ref(false)
const actionDialogVisible = ref(false)
const historyDialogVisible = ref(false)
const exportDialogVisible = ref(false)
const selectedApproval = ref<OrderApprovalListItem | null>(null)
const currentActionType = ref<'approved' | 'rejected'>('approved')

// 获取门店数据
const fetchStoreData = async () => {
  try {
    masterDataLoading.value = true
    const stores = await getStoreList()
    storeOptions.value = stores
  } catch (error) {
    console.error('Failed to fetch store data:', error)
    ElMessage.error(tc('loadFailed'))
    storeOptions.value = []
  } finally {
    masterDataLoading.value = false
  }
}

// 获取销售顾问数据
const fetchSalesConsultantData = async () => {
  try {
    consultantLoading.value = true
    // TODO: 实际调用获取销售顾问列表的API
    // 这里模拟一些数据，实际应从后端获取
    salesConsultantOptions.value = [
      { userId: 'sales1', userName: '张三' },
      { userId: 'sales2', userName: '李四' },
      { userId: 'sales3', userName: '王五' }
    ]
  } catch (error) {
    console.error('Failed to fetch sales consultant data:', error)
    ElMessage.error(tc('loadFailed'))
    salesConsultantOptions.value = []
  } finally {
    consultantLoading.value = false
  }
}

// 权限控制
const showStoreFilter = computed(() => {
  return currentUserRole.value === 'sales_manager' || currentUserRole.value === 'regional_manager'
})

const canExport = computed(() => {
  return currentUserRole.value === 'sales_manager' || currentUserRole.value === 'regional_manager'
})

// 标准化的状态标签映射配置
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.APPROVAL_TYPE]: {
    'cancel_order': 'danger',
    'modify_info': 'primary',
    'price_adjustment': 'warning'
  },
  [DICTIONARY_TYPES.APPROVAL_STATUS]: {
    'approved': 'success',
    'rejected': 'danger',
    'pending': 'warning'
  }
} as const

// 统一的字段转义函数（参考ProspectsView.vue）
const getName = (dictionaryType: DictionaryType, code: string) => {
  return getNameByCode(dictionaryType, code) || code
}

// 统一的标签类型获取函数
const getStatusTagType = (status: string, dictionaryType: DictionaryType) => {
  return STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info'
}

const getListTitle = () => {
  return activeTab.value === 'pending'
    ? t('pendingApprovalList')
    : t('approvedList')
}

// 获取数据（标准MyBatisPlus分页）
const fetchData = async () => {
  loading.value = true
  try {
    const params: OrderApprovalSearchParams = {
      ...searchParams,
      status: activeTab.value,
      pageNum: pagination.pageNum,    // ✅ 修正为pageNum
      pageSize: pagination.pageSize
    }

    const response = await getOrderApprovalList(params)
    tableData.value = response.result.records    // ✅ 标准MyBatisPlus响应结构
    pagination.total = response.result.total
  } catch (error) {
    ElMessage.error(t('fetchDataFailed'))
    console.error('Failed to fetch approval data:', error)
  } finally {
    loading.value = false
  }
}

// Tab切换
const handleTabChange = () => {
  pagination.pageNum = 1    // ✅ 修正为pageNum
  fetchData()
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1    // ✅ 修正为pageNum
  fetchData()
}

// 重置
const handleReset = () => {
  searchParams.approvalType = ''
  searchParams.orderNo = ''
  searchParams.salesConsultantId = ''
  searchParams.submitTimeStart = ''
  searchParams.submitTimeEnd = ''
  searchParams.approvalResult = ''
  searchParams.dealerId = ''
  submissionTimeRange.value = ''
  pagination.pageNum = 1    // ✅ 修正为pageNum
  fetchData()
}

// 导出
const handleExport = () => {
  exportDialogVisible.value = true
}

// 分页事件处理修正
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page    // ✅ 修正为pageNum
  fetchData()
}

// 操作方法
const handleViewDetails = (row: OrderApprovalListItem) => {
  selectedApproval.value = row
  detailDialogVisible.value = true
}

const handleViewOrderDetails = () => {
  // TODO: 跳转到订单详情页面
  ElMessage.info(t('orderDetailNotImplemented'))
}

const handleReview = (row: OrderApprovalListItem) => {
  selectedApproval.value = row
  currentActionType.value = 'approved' // 默认操作为批准，弹窗内可能可以更改
  actionDialogVisible.value = true
}

const handleActionSuccess = () => {
  ElMessage.success(t('operationSuccess'))
  fetchData()
}

const handleExportSuccess = () => {
  ElMessage.success(t('exportSuccess'))
  exportDialogVisible.value = false
}

const handleDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    searchParams.submitTimeStart = dates[0]
    searchParams.submitTimeEnd = dates[1]
  } else {
    searchParams.submitTimeStart = ''
    searchParams.submitTimeEnd = ''
  }
}

watch(activeTab, () => {
  handleReset()
})

onMounted(() => {
  fetchData()
  // 获取门店数据（仅管理者角色需要）
  if (showStoreFilter.value) {
    fetchStoreData()
  }
  // 获取销售顾问数据
  fetchSalesConsultantData()
})
</script>

<style lang="scss" scoped>
.page-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card, .table-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;

  .total-count {
    font-size: 14px;
    color: #666;
    font-weight: normal;
    margin-left: 16px;
  }
}

.search-buttons {
  text-align: right;
  padding-top: 30px; /* To align with form items with labels on top */
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

// 剩余时间样式
.text-red-600 {
  color: #dc2626;
}

.text-red-500 {
  color: #ef4444;
}

.text-orange-500 {
  color: #f97316;
}

.font-bold {
  font-weight: 700;
}
</style>
