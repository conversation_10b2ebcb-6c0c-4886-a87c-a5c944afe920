<template>
  <div class="test-drive-report">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 统计概览区 -->
    <div class="stats-section mb-20">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">{{ t('monthlyCount') }}</div>
            <div class="stat-value">{{ stats.monthlyCount }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">{{ t('dailyCount') }}</div>
            <div class="stat-value">{{ stats.dailyCount }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">{{ t('topStore') }}</div>
            <div class="stat-value-long">{{ stats.topStore }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">{{ t('topModel') }}</div>
            <div class="stat-value-long">{{ stats.topModel }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-section mb-20">
      <template #header>
        <span>{{ tc('searchConditions') }}</span>
      </template>
      <el-form ref="filterFormRef" :model="filterForm" label-position="top">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item :label="t('store')">
              <el-select
                v-model="filterForm.storeIds"
                :placeholder="tc('allStores')"
                clearable
                multiple
              >
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.STORE)"
                  :key="option.code"
                  :label="option.name"
                  :value="parseInt(option.code)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('model')">
              <el-select
                v-model="filterForm.model"
                :placeholder="tc('allModels')"
                clearable
                @change="handleModelChange"
              >
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.VEHICLE_MODEL)"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('variant')">
              <el-select
                v-model="filterForm.variant"
                :placeholder="tc('allVariants')"
                clearable
                :disabled="!filterForm.model"
              >
                <el-option
                  v-for="option in getOptions(DICTIONARY_TYPES.VEHICLE_VARIANT)"
                  :key="option.code"
                  :label="option.name"
                  :value="parseInt(option.code)"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('testDriveTime')">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                clearable
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <div style="display: flex; justify-content: flex-end; width: 100%;">
                <el-space>
                  <el-button @click="handleReset" :icon="Refresh">
                    {{ tc('reset') }}
                  </el-button>
                  <el-button type="primary" @click="handleSearch" :icon="Search">
                    {{ tc('search') }}
                  </el-button>
                </el-space>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="action-section mb-20">
      <el-space>
        <el-button @click="handleExport" type="primary" plain :icon="Download">
          {{ tc('exportData') }}
        </el-button>
      </el-space>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-container">
      <template #header>
        <span>{{ t('recordList') }}</span>
      </template>
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        border
        stripe
        height="600"
        style="width: 100%"
        :scroll-x="1800"
      >
        <el-table-column type="index" :label="tc('index')" width="60" fixed="left" />
        <el-table-column prop="testDriveNo" :label="t('testDriveNo')" width="150" />
        <el-table-column prop="storeName" :label="t('store')" width="120">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.STORE, row.storeName) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="t('createTime')" width="140">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="customerName" :label="t('customerName')" width="120" />
        <el-table-column prop="customerPhone" :label="t('customerPhone')" width="140" />
        <el-table-column prop="model" :label="t('model')" width="180">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_MODEL, row.model) }}
          </template>
        </el-table-column>
        <el-table-column prop="variant" :label="t('variant')" width="180" />
        <el-table-column prop="mileage" :label="t('mileage')" width="100" />
        <el-table-column prop="startTime" :label="t('startTime')" width="120">
          <template #default="{ row }">
            {{ formatDateTime(row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="endTime" :label="t('endTime')" width="120">
          <template #default="{ row }">
            {{ formatDateTime(row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="consultantName" :label="t('consultantName')" width="180" />
        <el-table-column :label="tc('operations')" width="80" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">
              {{ tc('detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态处理 -->
      <div v-if="!tableData.length && !loading" class="empty-state">
        <el-icon><el-icon-inbox /></el-icon>
        <h3>{{ tc('noData') }}</h3>
        <p>{{ tc('noDataTip') }}</p>
      </div>

      <!-- 分页 -->
      <div class="pagination-section mt-20">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :total="pagination.total"
          :layout="paginationLayout"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 试驾单详情模态框 -->
    <TestDriveDetailModal
      v-model:show="showDetailModal"
      :record-data="currentRecord"
    />
  </div>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'TestDriveReportView'
})

import { ref, reactive, computed, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { Search, Refresh, Download, Box as ElIconInbox } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { format } from 'date-fns';
import TestDriveDetailModal from './components/TestDriveDetailModal.vue';
import type {
  TestDriveItem,
  TestDriveSearchParams,
  TestDriveStats
} from '@/types/sales/testDriveReport';
import {
  getTestDriveReportList,
  getTestDriveReportStats,
  getTestDriveDetail,
  exportTestDriveReport
} from '@/api/modules/sales/testDriveReport';

// 国际化
const { t } = useModuleI18n('sales.testDriveReport');
const { t: tc } = useModuleI18n('common');

// 数据字典
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.VEHICLE_VARIANT,
  DICTIONARY_TYPES.CUSTOMER_SOURCE,
  DICTIONARY_TYPES.ID_TYPE
]);

// 响应式数据
const tableRef = ref();
const filterFormRef = ref();

const stats = reactive<TestDriveStats>({
  monthlyCount: 0,
  dailyCount: 0,
  topStore: '-',
  topModel: '-'
});

const filterForm = reactive<TestDriveSearchParams>({
  storeIds: [],
  model: '',
  variant: undefined,
  dateRange: null as [string, string] | null,
});

const loading = ref(false);
const tableData = ref<TestDriveItem[]>([]);
const showDetailModal = ref(false);
const currentRecord = ref<TestDriveItem | null>(null);

const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
});

const paginationLayout = computed(() => {
  return tc('paginationLayout'); // 使用国际化获取布局字符串
});

// 处理车型选择变化
const handleModelChange = () => {
  filterForm.variant = undefined;
};

// 处理分页大小变化
const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;
  loadTableData();
};

// 处理页码变化
const handlePageChange = (page: number) => {
  pagination.pageNum = page;
  loadTableData();
};

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1;
  loadTableData();
};

// 重置
const handleReset = () => {
  filterForm.storeIds = [];
  filterForm.model = '';
  filterForm.variant = undefined;
  filterForm.dateRange = null;
  handleSearch();
};

// 导出数据
const handleExport = async () => {
  try {
    const params = buildQueryParams();
    const exportParams = { ...params };
    // 移除分页参数
    delete exportParams.pageNum;
    delete exportParams.pageSize;

    const response = await exportTestDriveReport(exportParams);
    // 处理 blob 响应
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${t('title')}_${format(new Date(), 'yyyyMMddHHmmss')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    ElMessage.success(tc('exportSuccess'));
  } catch (error) {
    ElMessage.error(tc('exportFailed'));
    console.error(error);
  }
};

// 查看详情
const handleViewDetail = async (record: TestDriveItem) => {
  try {
    const res = await getTestDriveDetail(record.testDriveNo);
    currentRecord.value = res.result;
    showDetailModal.value = true;
  } catch(error) {
    ElMessage.error(tc('fetchDetailFailed'));
    console.error(error);
  }
};

// 构建查询参数
const buildQueryParams = (): TestDriveSearchParams => {
  const params: TestDriveSearchParams = {
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize,
    model: filterForm.model || undefined,
    variant: filterForm.variant || undefined,
    storeIds: filterForm.storeIds && filterForm.storeIds.length > 0 ? filterForm.storeIds : undefined,
  };

  if (filterForm.dateRange) {
    params.startTimeBegin = `${filterForm.dateRange[0]}T00:00:00`;
    params.startTimeEnd = `${filterForm.dateRange[1]}T23:59:59`;
  }

  return params;
};

// 加载表格数据
const loadTableData = async () => {
  loading.value = true;
  try {
    const params = buildQueryParams();
    const res = await getTestDriveReportList(params);
    console.log('loadTableData', res);

    if (res.result && res.result.records) {
      tableData.value = res.result.records;
      pagination.total = res.result.total;
    } else {
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    ElMessage.error(tc('fetchDataFailed'));
    console.error(error);
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 加载统计数据
const loadStats = async () => {
  try {
    const res = await getTestDriveReportStats();
    if (res.result) {
      Object.assign(stats, res.result);
    }
  } catch (error) {
    console.error('Failed to load stats:', error);
  }
};

// 格式化日期时间
const formatDateTime = (dateString?: string) => {
  if (!dateString) return '-';
  try {
    return new Date(dateString).toLocaleString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/,/g, '');
  } catch {
    return dateString;
  }
};

// 生命周期钩子
onMounted(() => {
  loadStats();
  loadTableData();
});
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.test-drive-report {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333333;
  }

  .stats-section {
    margin-bottom: 20px;
    .stat-card {
      background-color: white;
      border-radius: 4px;
      padding: 20px;
      box-shadow: 0 1px 4px rgba(0,21,41,.08);
      border-left: 4px solid #1890ff;
      height: 100%;

      .stat-title {
        font-size: 14px;
        color: rgba(0,0,0,.45);
        margin-bottom: 8px;
      }
      .stat-value {
        font-size: 30px;
        font-weight: bold;
        color: #333;
      }
      .stat-value-long {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .filter-section {
    background-color: white;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 16px;

    .button-gi {
      display: flex;
      align-items: flex-end;
      padding-bottom: 2px; /* Small adjustment for alignment */
    }
  }

  .action-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 16px;
  }

  .table-container {
    background-color: white;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    border-radius: 6px;
    overflow: hidden;

    .pagination-section {
      margin-top: 20px;
      text-align: right;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    color: #909399;
    .el-icon {
      font-size: 60px;
      margin-bottom: 20px;
    }
    h3 {
      margin: 0 0 10px 0;
      color: #909399;
    }
    p {
      margin: 0;
      font-size: 14px;
      color: #c0c4cc;
    }
  }
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
