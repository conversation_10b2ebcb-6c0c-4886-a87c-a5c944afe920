<!--
  交付管理页面 - 重构版本
  符合MyBatisPlus分页标准和数据字典规范
  使用模块化目录结构和标准化API
-->
<template>
  <div class="page-container">
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('searchTitle') }}</span>
        </div>
      </template>

      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('deliveryNumber')">
              <el-input v-model="searchParams.deliveryNumber" :placeholder="t('deliveryNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input v-model="searchParams.orderNumber" :placeholder="t('orderNoPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerName')">
              <el-input v-model="searchParams.customerName" :placeholder="t('customerNamePlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerPhone')">
              <el-input v-model="searchParams.customerPhone" :placeholder="t('customerMobilePlaceholder')" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('vin')">
              <el-input v-model="searchParams.vin" :placeholder="t('vinPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('orderStatus')">
              <el-select v-model="searchParams.orderStatus" :placeholder="t('orderStatusPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in orderStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('dealerStore')">
              <el-select v-model="searchParams.dealerStore" :placeholder="t('dealerStorePlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in dealerStoreOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('salesConsultant')">
              <el-input v-model="searchParams.salesConsultant" :placeholder="t('salesmanPlaceholder')" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('deliveryStatus')">
              <el-select v-model="searchParams.deliveryStatus" :placeholder="t('deliveryStatusPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in deliveryStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerConfirmed')">
              <el-select v-model="searchParams.customerConfirmed" :placeholder="t('customerConfirmedPlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in booleanOptions"
                  :key="option.code"
                  :value="option.code === 'true'"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('confirmationType')">
              <el-select v-model="searchParams.confirmationType" :placeholder="t('confirmationTypePlaceholder')" clearable :loading="dictionaryLoading">
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in confirmationMethodOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('deliveryTime')">
              <el-date-picker
                v-model="searchParams.deliveryTimeRange"
                type="daterange"
                range-separator="-"
                :start-placeholder="t('startDate')"
                :end-placeholder="t('endDate')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('customerConfirmTime')">
              <el-date-picker
                v-model="searchParams.customerConfirmTimeRange"
                type="daterange"
                range-separator="-"
                :start-placeholder="t('customerConfirmTimeStartPlaceholder')"
                :end-placeholder="t('customerConfirmTimeEndPlaceholder')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="18" class="search-buttons">
            <el-button type="primary" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="handleReset">{{ tc('reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operation-card mb-20">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-button v-if="canExport" type="success" @click="handleExport">{{ tc('export') }}</el-button>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('listTitle') }}</span>
          <span class="total-count">{{ t('totalCount', { count: pagination.total }) }}</span>
        </div>
      </template>

      <el-table :data="tableData" :loading="loading" border style="width: 100%">
        <el-table-column type="index" :label="tc('index')" width="80" />
        <el-table-column prop="deliveryNumber" :label="t('deliveryNumber')" width="140" />
        <el-table-column prop="orderNumber" :label="t('orderNumber')" width="140" />
        <el-table-column prop="customerName" :label="t('customerName')" width="120" />
        <el-table-column prop="customerPhone" :label="t('customerPhone')" width="130" />
        <el-table-column prop="vin" :label="t('vin')" width="180" />
        <el-table-column prop="model" :label="t('model')" width="100" />
        <el-table-column prop="variant" :label="t('variant')" width="120" />
        <el-table-column prop="color" :label="t('color')" width="80" />
        <el-table-column prop="dealerStore" :label="t('dealerStore')" width="100">
          <template #default="{ row }">
            {{ getName(DICTIONARY_TYPES.DEALER_STORE, row.dealerStore) }}
          </template>
        </el-table-column>
        <el-table-column prop="salesConsultant" :label="t('salesConsultant')" width="100" />
        <el-table-column prop="orderStatus" :label="t('orderStatus')" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.orderStatus, DICTIONARY_TYPES.ORDER_STATUS)">
              {{ getName(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceTime" :label="t('invoiceTime')" width="150" />
        <el-table-column prop="deliveryStatus" :label="t('deliveryStatus')" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.deliveryStatus, DICTIONARY_TYPES.DELIVERY_STATUS)">
              {{ getName(DICTIONARY_TYPES.DELIVERY_STATUS, row.deliveryStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="customerConfirmed" :label="t('customerConfirmed')" width="100">
          <template #default="{ row }">
            {{ row.customerConfirmed ? tc('yes') : tc('no') }}
          </template>
        </el-table-column>
        <el-table-column prop="confirmationType" :label="t('confirmationType')" width="100">
          <template #default="{ row }">
            {{ row.confirmationType ? getName(DICTIONARY_TYPES.CONFIRMATION_METHOD, row.confirmationType) : tc('none') }}
          </template>
        </el-table-column>
        <el-table-column prop="customerConfirmTime" :label="t('customerConfirmTime')" width="150" />
        <el-table-column prop="deliveryTime" :label="t('actualDeliveryDate')" width="150" />
        <el-table-column prop="deliveryNotes" :label="t('deliveryNotes')" width="150" show-overflow-tooltip />
        <el-table-column prop="signaturePhoto" :label="t('signaturePhoto')" width="120">
          <template #default="{ row }">
            <div v-if="row.signaturePhoto">
              <el-image
                :src="row.signaturePhoto"
                :preview-src-list="[row.signaturePhoto]"
                fit="cover"
                style="width: 40px; height: 30px; cursor: pointer;"
                :alt="t('signaturePhoto')"
              />
            </div>
            <span v-else class="text-gray-400">{{ tc('none') }}</span>
          </template>
        </el-table-column>

        <el-table-column :label="tc('actions')" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              link
              type="success"
              @click="handleDetail(row)"
              :loading="detailLoading"
            >
              {{ tc('detail') }}
            </el-button>
            <el-button link type="info" @click="handlePrint()">{{ tc('print') }}</el-button>
            <el-button
              v-if="canSubmitConfirm(row)"
              link
              type="primary"
              @click="handleSubmitConfirm(row)"
            >
              {{ t('submitConfirm') }}
            </el-button>
            <el-button
              v-if="canDeliveryConfirm(row)"
              link
              type="warning"
              @click="handleDeliveryConfirm(row)"
            >
              {{ t('deliveryConfirm') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 标准MyBatisPlus分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 弹窗组件 -->
    <DeliverySubmitDialog
      v-model="submitDialogVisible"
      :order-data="selectedOrder"
      @confirm="handleSubmitConfirmAction"
    />

    <DeliveryConfirmDialog
      v-model="confirmDialogVisible"
      :order-data="selectedOrder"
      @confirm="handleDeliveryConfirmAction"
    />

    <DeliveryDetailDialog
      v-model="detailDialogVisible"
      :order-data="selectedOrder"
      :loading="detailLoading"
    />

    <DeliveryExportDialog
      v-model="exportDialogVisible"
      @confirm="handleExportConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'

// API导入 - 使用新的模块化API
import {
  getDeliveryList,
  getDeliveryDetail,
  submitConfirm,
  deliveryConfirm,
  exportDeliveryData
} from '@/api/modules/sales/delivery'

// 类型导入 - 使用新的类型定义
import type {
  DeliveryListItem,
  DeliverySearchParams,
  SubmitConfirmRequest,
  DeliveryConfirmRequest,
  ExportSettings,
  UserRole,
  DictionaryType
} from '@/types/sales/delivery'

// 组件导入 - 使用相对路径导入组件
import DeliverySubmitDialog from './components/DeliverySubmitDialog.vue'
import DeliveryConfirmDialog from './components/DeliveryConfirmDialog.vue'
import DeliveryDetailDialog from './components/DeliveryDetailDialog.vue'
import DeliveryExportDialog from './components/DeliveryExportDialog.vue'

// 国际化 - 使用新的模块路径
const { t } = useModuleI18n('sales.delivery')
const { tc } = useModuleI18n('common')

// 标准化字典数据获取
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.DELIVERY_STATUS,
  DICTIONARY_TYPES.CONFIRMATION_METHOD,
  DICTIONARY_TYPES.BOOLEAN_TYPE,
  DICTIONARY_TYPES.DEALER_STORE
])

// 获取字典选项
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS))
const deliveryStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.DELIVERY_STATUS))
const confirmationMethodOptions = computed(() => getOptions(DICTIONARY_TYPES.CONFIRMATION_METHOD))
const booleanOptions = computed(() => getOptions(DICTIONARY_TYPES.BOOLEAN_TYPE))
const dealerStoreOptions = computed(() => getOptions(DICTIONARY_TYPES.DEALER_STORE))

// 统一的字段转义函数（参考ProspectsView.vue标准实现）
const getName = (dictionaryType: DictionaryType, code: string) => {
  return getNameByCode(dictionaryType, code) || code;
}

// 标准化的状态标签映射配置
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.ORDER_STATUS]: {
    'normal': 'success',
    'cancelled': 'danger',
    'pending_allocation': 'info',
    'allocating': 'warning',
    'allocated': 'primary',
    'pending_delivery': 'warning',
    'delivered': 'success'
  },
  [DICTIONARY_TYPES.DELIVERY_STATUS]: {
    'pending_delivery': 'warning',
    'pending_confirm': 'primary',
    'delivered': 'success'
  }
} as const

// 统一的标签类型获取函数
const getStatusTagType = (status: string, dictionaryType: DictionaryType) => {
  return STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info'
}

// 当前用户角色 - 实际项目中应从用户信息获取
const currentUserRole = ref<UserRole>('sales_manager')

// 标准MyBatisPlus搜索参数
const searchParams = reactive<DeliverySearchParams>({
  deliveryNumber: '',
  orderNumber: '',
  customerName: '',
  customerPhone: '',
  vin: '',
  orderStatus: undefined,
  dealerStore: undefined,
  salesConsultant: '',
  deliveryStatus: undefined,
  customerConfirmed: undefined,
  confirmationType: undefined,
  deliveryTimeRange: undefined,
  customerConfirmTimeRange: undefined
})

// 表格数据
const tableData = ref<DeliveryListItem[]>([])
const loading = ref(false)

// 标准MyBatisPlus分页参数
const pagination = reactive({
  pageNum: 1,        // 修正为标准MyBatisPlus参数
  pageSize: 20,
  total: 0
})

// 弹窗控制
const submitDialogVisible = ref(false)
const confirmDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const exportDialogVisible = ref(false)
const selectedOrder = ref<DeliveryListItem | null>(null)
const detailLoading = ref(false)

// 权限控制
const canExport = computed(() => currentUserRole.value === 'sales_manager')

const canSubmitConfirm = (row: DeliveryListItem) => {
  return row.deliveryStatus === '01220001'
}

const canDeliveryConfirm = (row: DeliveryListItem) => {
  return row.deliveryStatus === '01220002'
}

// 获取数据 - 使用标准MyBatisPlus分页参数
const fetchData = async () => {
  loading.value = true
  try {
    const params: DeliverySearchParams = {
      ...searchParams,
      pageNum: pagination.pageNum,    // 修正为标准参数
      pageSize: pagination.pageSize
    }

    const response = await getDeliveryList(params)
    tableData.value = response.result.records    // 使用标准MyBatisPlus响应结构
    pagination.total = response.result.total
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error(t('fetchDataFailed'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1    // 修正为标准参数
  fetchData()
}

// 重置
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key as keyof DeliverySearchParams] = undefined
  })
  searchParams.deliveryTimeRange = undefined
  searchParams.customerConfirmTimeRange = undefined
  pagination.pageNum = 1    // 修正为标准参数
  fetchData()
}

// 导出
const handleExport = () => {
  exportDialogVisible.value = true
}

// 标准MyBatisPlus分页事件处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchData()
}

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page    // 修正为标准参数
  fetchData()
}

// 操作方法
const handleDetail = async (row: DeliveryListItem) => {
  try {
    detailLoading.value = true
    const detailData = await getDeliveryDetail(row.deliveryNumber)
    if (detailData) {
      selectedOrder.value = detailData.result
      detailDialogVisible.value = true
    } else {
      ElMessage.error(t('detailNotFound'))
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : t('fetchDetailFailed')
    ElMessage.error(errorMessage)
  } finally {
    detailLoading.value = false
  }
}

const handlePrint = () => {
  ElMessage.info(t('printFeatureNotImplemented'))
}

const handleSubmitConfirm = (row: DeliveryListItem) => {
  selectedOrder.value = row
  submitDialogVisible.value = true
}

const handleDeliveryConfirm = (row: DeliveryListItem) => {
  selectedOrder.value = row
  confirmDialogVisible.value = true
}

const handleSubmitConfirmAction = async (data: SubmitConfirmRequest) => {
  try {
    await submitConfirm(data)
    ElMessage.success(t('submitConfirmSuccess'))
    fetchData()
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : t('submitConfirmFailed')
    ElMessage.error(errorMessage)
  }
}

const handleDeliveryConfirmAction = async (data: DeliveryConfirmRequest) => {
  try {
    await deliveryConfirm(data)
    ElMessage.success(t('deliveryConfirmSuccess'))
    fetchData()
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : t('deliveryConfirmFailed')
    ElMessage.error(errorMessage)
  }
}

const handleExportConfirm = async (exportSettings: ExportSettings) => {
  try {
    const params: DeliverySearchParams = {
      ...searchParams,
      pageNum: 1,
      pageSize: pagination.total
    }
    await exportDeliveryData(exportSettings, params)
    ElMessage.success(t('exportSuccess'))
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : t('exportFailed')
    ElMessage.error(errorMessage)
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.page-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.search-card, .table-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.total-count {
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

.search-buttons {
  text-align: right;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
