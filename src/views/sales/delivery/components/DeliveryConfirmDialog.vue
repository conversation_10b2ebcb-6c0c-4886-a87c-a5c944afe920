<template>
  <el-dialog
    :title="t('deliveryConfirmTitle')"
    :model-value="modelValue"
    @update:model-value="(val: boolean) => $emit('update:modelValue', val)"
    width="900px"
    @close="handleClose"
    class="delivery-confirm-dialog"
  >
    <!-- 订单信息区域 -->
    <div class="info-section">
      <div class="section-header">
        <span class="section-icon">📋</span>
        <span class="section-title">{{ t('orderInfoTitle') }}</span>
      </div>
      <div class="info-content">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">{{ t('deliveryNumberLabel') }}</span>
            <div class="info-value highlighted">{{ orderData?.deliveryNumber }}</div>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('orderNumberLabel') }}</span>
            <div class="info-value highlighted">{{ orderData?.orderNumber }}</div>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">{{ t('customerNameLabel') }}</span>
            <div class="info-value">{{ orderData?.customerName }}</div>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('customerPhoneLabel') }}</span>
            <div class="info-value">{{ orderData?.customerPhone }}</div>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item full-width">
            <span class="info-label">VIN</span>
            <div class="info-value">{{ orderData?.vin }}</div>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">{{ t('modelLabel') }}</span>
            <div class="info-value">{{ orderData?.model }}</div>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('variantLabel') }}</span>
            <div class="info-value">{{ orderData?.variant }}</div>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('colorLabel') }}</span>
            <div class="info-value color-value">{{ orderData?.color }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 交车信息区域 -->
    <div class="info-section">
      <div class="section-header">
        <span class="section-icon">🚚</span>
        <span class="section-title">{{ t('deliveryInfoTitle') }}</span>
      </div>
      <div class="form-content">
        <el-form :model="formData" label-position="top" ref="formRef">
          <div class="form-row">
            <div class="form-item">
              <el-form-item
                :label="`*${t('deliveryTimeLabel')}`"
                prop="deliveryTime"
                :rules="[{ required: true, message: t('deliveryTimeRequired'), trigger: 'change' }]"
              >
                <el-date-picker
                  v-model="formData.deliveryTime"
                  type="datetime"
                  style="width: 100%"
                  :placeholder="t('deliveryTimePlaceholder')"
                  format="YYYY/MM/DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  :clearable="true"
                />
              </el-form-item>
            </div>
            <div class="form-item upload-area">
              <el-form-item
                :label="`*${t('customerSignatureConfirm')}`"
                prop="signaturePhoto"
                :rules="[{ required: true, message: t('signaturePhotoRequired'), trigger: 'change' }]"
              >
                <div class="signature-upload-container">
                  <el-upload
                    class="signature-uploader"
                    :show-file-list="false"
                    :auto-upload="false"
                    :on-change="handleSignatureChange"
                    :on-remove="handleSignatureRemove"
                    :limit="1"
                    :before-upload="() => false"
                    drag
                  >
                    <div v-if="signatureImageUrl" class="signature-preview">
                      <img :src="signatureImageUrl" class="signature-img" />
                      <div class="signature-overlay">
                        <el-button
                          type="danger"
                          size="small"
                          @click.stop="handleSignatureRemove"
                          circle
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>
                    <div v-else class="upload-placeholder">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">{{ t('uploadSignaturePhoto') }}</div>
                    </div>
                  </el-upload>
                  <div class="upload-tip">{{ t('uploadSignaturePhotoTip') }}</div>
                </div>
              </el-form-item>
            </div>
          </div>

          <div class="form-row full-width">
            <el-form-item :label="t('deliveryNotesLabel')" prop="deliveryNotes">
              <el-input
                v-model="formData.deliveryNotes"
                type="textarea"
                :rows="4"
                :placeholder="t('deliveryNotesPlaceholder')"
                style="width: 100%"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)" size="default">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm" size="default">{{ tc('confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import { ElMessage, ElDialog, ElForm, ElFormItem, ElDatePicker, ElButton, ElUpload, ElInput, ElIcon } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { Plus, Delete } from '@element-plus/icons-vue'
import { uploadSignaturePhoto } from '@/api/modules/sales/delivery'
import type { DeliveryListItem } from '@/types/sales/delivery'
import type { UploadFile } from 'element-plus'

interface DeliveryConfirmRequest {
  deliveryNumber: string
  deliveryTime: string
  deliveryNotes: string
  signaturePhoto: File | undefined
}

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  orderData: { type: Object as () => DeliveryListItem | null, default: null }
})

const emit = defineEmits(['update:modelValue', 'confirm'])
const { t } = useModuleI18n('sales.delivery')
const { t: tc } = useModuleI18n('common')

const formRef = ref<InstanceType<typeof ElForm> | null>(null)
const signatureImageUrl = ref<string>('')

const formData = reactive<DeliveryConfirmRequest>({
  deliveryNumber: '',
  deliveryTime: '',
  deliveryNotes: '',
  signaturePhoto: undefined as unknown as File
})

watch(() => props.modelValue, (newVal) => {
  if (newVal && props.orderData) {
    formData.deliveryNumber = props.orderData.deliveryNumber
    formData.deliveryTime = ''
    formData.deliveryNotes = ''
    formData.signaturePhoto = undefined as unknown as File
    signatureImageUrl.value = ''
  }
})

const handleSignatureChange = (file: UploadFile) => {
  if (!file.raw) return false

  const isJPGorPNG = file.raw.type === 'image/jpeg' || file.raw.type === 'image/png'
  const isLt5M = file.raw.size / 1024 / 1024 < 5

  if (!isJPGorPNG) {
    ElMessage.error(t('signaturePhotoFormatError'))
    return false
  }
  if (!isLt5M) {
    ElMessage.error(t('signaturePhotoSizeError'))
    return false
  }

  formData.signaturePhoto = file.raw
  signatureImageUrl.value = URL.createObjectURL(file.raw)

  // 触发表单验证
  formRef.value?.validateField('signaturePhoto')
  return true
}

const handleSignatureRemove = () => {
  formData.signaturePhoto = undefined as unknown as File
  signatureImageUrl.value = ''
  // 清除表单验证
  formRef.value?.clearValidate('signaturePhoto')
}

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleConfirm = () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 签字照片始终是必填项，所以直接检查
      if (!formData.signaturePhoto) {
        ElMessage.error(t('signaturePhotoRequired'))
        return
      }

      let photoPath: string | undefined = undefined
      if (formData.signaturePhoto && props.orderData?.deliveryNumber) {
        try {
          const uploadResult = await uploadSignaturePhoto(
            formData.signaturePhoto,
            props.orderData.deliveryNumber,
            formData.deliveryTime
          )
          photoPath = uploadResult.url
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : String(error)
          ElMessage.error(errorMessage || t('signatureUploadFailed'))
          return
        }
      }

      // 准备发送的数据，不包含 confirmationType
      const requestData = {
        deliveryNumber: formData.deliveryNumber,
        deliveryTime: formData.deliveryTime,
        deliveryNotes: formData.deliveryNotes,
        signaturePhoto: photoPath // 传递上传后的图片路径
      } as DeliveryConfirmRequest

      emit('confirm', requestData)
      emit('update:modelValue', false)
    } else {
      ElMessage.error(tc('formValidationFailed'))
    }
  })
}
</script>

<style scoped>
.delivery-confirm-dialog {
  border-radius: 8px;
}

.info-section {
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fff;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  border-radius: 6px 6px 0 0;
}

.section-icon {
  margin-right: 8px;
  font-size: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.info-content {
  padding: 16px 20px;
}

.info-row {
  display: flex;
  margin-bottom: 16px;
  gap: 24px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-item.full-width {
  flex: 100%;
}

.info-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  font-weight: normal;
}

.info-value {
  font-size: 13px;
  color: #333;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  min-height: 20px;
}

.info-value.highlighted {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
  font-weight: 500;
}

.info-value.color-value {
  background: #fff3e0;
  border-color: #ff9800;
  color: #f57c00;
  font-weight: 500;
}

.form-content {
  padding: 16px 20px;
}

.form-row {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.form-row.full-width {
  flex-direction: column;
}

.form-item {
  flex: 1;
}

.form-item.upload-area {
  flex: 1.2;
}

.signature-upload-container {
  width: 100%;
}

.signature-uploader {
  width: 100%;
}

.signature-uploader :deep(.el-upload) {
  width: 100%;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.signature-uploader :deep(.el-upload:hover) {
  border-color: #409eff;
}

.signature-preview {
  position: relative;
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  overflow: hidden;
}

.signature-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.signature-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.signature-preview:hover .signature-overlay {
  opacity: 1;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
}

.upload-icon {
  font-size: 28px;
  color: #c0c4cc;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
  color: #666;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
  text-align: center;
}

.dialog-footer {
  text-align: right;
  padding: 16px 0 0 0;
}

/* 表单标签样式 */
:deep(.el-form-item__label) {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  padding-bottom: 8px;
}

/* 日期选择器样式 */
:deep(.el-date-editor) {
  width: 100%;
}

/* 文本域样式 */
:deep(.el-textarea__inner) {
  font-size: 13px;
  line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .info-row {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
