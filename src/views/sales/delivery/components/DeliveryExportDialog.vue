<template>
  <el-dialog
    :title="t('exportSettingsTitle')"
    :model-value="modelValue"
    @update:model-value="(val: boolean) => $emit('update:modelValue', val)"
    width="500px"
    @close="handleClose"
  >
    <el-form :model="formData" label-position="top" ref="formRef">
      <el-form-item :label="t('exportFormat')" prop="format" :rules="[{ required: true, message: t('exportFormatRequired'), trigger: 'change' }]">
        <el-radio-group v-model="formData.format">
          <el-radio label="excel">Excel (.xlsx)</el-radio>
          <el-radio label="pdf">PDF (.pdf)</el-radio>
          <el-radio label="csv">CSV (.csv)</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="t('exportRange')" prop="range" :rules="[{ required: true, message: t('exportRangeRequired'), trigger: 'change' }]">
        <el-radio-group v-model="formData.range">
          <el-radio label="current_page">{{ t('exportRangeCurrentPage') }}</el-radio>
          <el-radio label="filtered_result">{{ t('exportRangeFilteredResult') }}</el-radio>
          <el-radio label="all_data">{{ t('exportRangeAllData') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="formData.range === 'all_data'" :label="t('exportTimeRange')" prop="timeRange">
        <el-date-picker
          v-model="formData.timeRange"
          type="daterange"
          :start-placeholder="t('startDate')"
          :end-placeholder="t('endDate')"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="-"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="$emit('update:modelValue', false)">{{ tc('cancel') }}</el-button>
      <el-button type="primary" @click="handleConfirm">{{ tc('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { ExportSettings } from '@/types/sales/delivery'

const props = defineProps({
  modelValue: { type: Boolean, default: false }
})

const emit = defineEmits(['update:modelValue', 'confirm'])
const { t } = useModuleI18n('sales.delivery')
const { t: tc } = useModuleI18n('common')

const formRef = ref<any>(null)

const formData = reactive<ExportSettings>({
  format: 'excel',
  range: 'current_page',
  timeRange: undefined
})

watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // Reset form when dialog opens
    formRef.value?.resetFields()
    formData.format = 'excel'
    formData.range = 'current_page'
    formData.timeRange = undefined
  }
})

const handleConfirm = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      emit('confirm', formData)
      emit('update:modelValue', false)
    } else {
      ElMessage.warning(tc('formValidationFailed'))
    }
  })
}

const handleClose = () => {
  formRef.value?.resetFields()
}
</script>

<style scoped>
/* Add styles if needed */
</style>
