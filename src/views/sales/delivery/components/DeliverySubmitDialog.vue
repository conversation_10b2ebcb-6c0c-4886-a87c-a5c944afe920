<template>
  <el-dialog
    :title="t('submitConfirmTitle')"
    :model-value="modelValue"
    @update:model-value="(val: boolean) => $emit('update:modelValue', val)"
    width="600px"
    @close="handleClose"
    class="submit-confirm-dialog"
  >
    <!-- 确认问题区域 -->
    <div class="confirm-question">
      <el-icon class="question-icon"><QuestionFilled /></el-icon>
      <span class="question-text">{{ t('submitConfirmQuestion') }}</span>
    </div>

    <!-- 订单信息区域 -->
    <div class="order-info-section">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">{{ t('deliveryNumberLabel') }}：</span>
          <span class="info-value">{{ orderData?.deliveryNumber }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">{{ t('orderNumberLabel') }}：</span>
          <span class="info-value">{{ orderData?.orderNumber }}</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">{{ t('customerNameLabel') }}：</span>
          <span class="info-value">{{ orderData?.customerName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">{{ t('customerPhoneLabel') }}：</span>
          <span class="info-value">{{ orderData?.customerPhone }}</span>
        </div>
      </div>
    </div>

    <!-- 状态说明 -->
    <div class="status-note">
      {{ t('submitConfirmNote', { from: t('statusPendingDelivery'), to: t('statusPendingConfirm') }) }}
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)" size="default">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm" size="default">{{ t('confirmSubmit') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { ElMessage, ElIcon } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { DeliveryListItem, SubmitConfirmRequest } from '@/types/sales/delivery'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  orderData: { type: Object as () => DeliveryListItem | null, default: null }
})

const emit = defineEmits(['update:modelValue', 'confirm'])
const { t } = useModuleI18n('sales.delivery')
const { t: tc } = useModuleI18n('common')

const handleConfirm = () => {
  if (props.orderData) {
    const data: SubmitConfirmRequest = {
      deliveryNumber: props.orderData.deliveryNumber
    }
    emit('confirm', data)
    emit('update:modelValue', false)
  } else {
    ElMessage.error(t('noOrderSelected'))
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style scoped>
.submit-confirm-dialog {
  border-radius: 8px;
}

.confirm-question {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  padding: 16px 0;
}

.question-icon {
  font-size: 20px;
  color: #f5a623;
  margin-right: 12px;
  flex-shrink: 0;
}

.question-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.order-info-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

.info-row {
  display: flex;
  gap: 40px;
  margin-bottom: 16px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
  font-weight: normal;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.status-note {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  padding: 16px 20px;
  background: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 6px;
  margin-bottom: 24px;
}

.status-from {
  color: #1976d2;
  font-weight: 500;
  background: #e3f2fd;
  padding: 2px 6px;
  border-radius: 3px;
}

.status-to {
  color: #f57c00;
  font-weight: 500;
  background: #fff3e0;
  padding: 2px 6px;
  border-radius: 3px;
}

.dialog-footer {
  text-align: right;
  padding: 16px 0 0 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .info-row {
    flex-direction: column;
    gap: 12px;
  }

  .info-item {
    justify-content: space-between;
  }

  .info-label {
    min-width: auto;
  }
}
</style>
