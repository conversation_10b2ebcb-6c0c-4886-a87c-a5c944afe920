<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择权益"
    width="60%"
    :close-on-click-modal="false"
  >
    <el-table
      :data="availableRights"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="rightCode" label="权益代码" />
      <el-table-column prop="rightName" label="权益名称" />
      <el-table-column prop="discountPrice" label="折扣金额">
        <template #default="{ row }">
          RM {{ formatCurrency(row.discountPrice) }}
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" />
    </el-table>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { SalesOrderRight } from '@/types/sales/orders'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [rights: SalesOrderRight[]]
}>()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const selectedRights = ref<SalesOrderRight[]>([])

// Mock权益数据
const availableRights = ref<SalesOrderRight[]>([
  {
    id: '1',
    rightCode: 'DISCOUNT_1000',
    rightName: '现金优惠1000',
    discountPrice: 1000,
    description: '购车现金优惠1000元'
  },
  {
    id: '2',
    rightCode: 'MAINTENANCE_FREE',
    rightName: '免费保养',
    discountPrice: 500,
    description: '首次保养免费'
  }
])

const formatCurrency = (amount: number) => {
  return amount?.toLocaleString('en-MY', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || '0.00'
}

const handleSelectionChange = (selection: SalesOrderRight[]) => {
  selectedRights.value = selection
}

const handleConfirm = () => {
  emit('confirm', selectedRights.value)
}

const handleCancel = () => {
  selectedRights.value = []
  dialogVisible.value = false
}
</script>
