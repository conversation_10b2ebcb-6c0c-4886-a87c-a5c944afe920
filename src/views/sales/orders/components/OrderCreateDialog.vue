<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('create.title')"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="create-order-content" v-loading="loading">
      <!-- 客户信息 -->
      <el-card class="mb-20">
        <h3>{{ t('create.customerInformation') }}</h3>
        <el-form :model="createForm" :rules="formRules" ref="formRef" label-position="top">
          <el-row :gutter="20" class="form-row">
            <el-col :span="12">
              <el-form-item :label="t('create.ordererNameLabel')" prop="ordererName">
                <el-input v-model="createForm.ordererName" :placeholder="t('create.placeholders.ordererName')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.ordererPhoneLabel')" prop="ordererPhone">
                <el-input
                  v-model="createForm.ordererPhoneNumber"
                  :placeholder="t('create.placeholders.ordererPhone')"
                >
                  <template #prepend>
                    <el-select
                      v-model="createForm.ordererCountryCode"
                      style="width: 80px"
                      placeholder="区号"
                    >
                      <el-option label="+60" value="+60" />
                      <el-option label="+65" value="+65" />
                      <el-option label="+86" value="+86" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.buyerNameLabel')" prop="buyerName">
                <el-input v-model="createForm.buyerName" :placeholder="t('create.placeholders.buyerName')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.buyerPhoneLabel')" prop="buyerPhone">
                <el-input
                  v-model="createForm.buyerPhoneNumber"
                  :placeholder="t('create.placeholders.buyerPhone')"
                >
                  <template #prepend>
                    <el-select
                      v-model="createForm.buyerCountryCode"
                      style="width: 80px"
                      placeholder="区号"
                    >
                      <el-option label="+60" value="+60" />
                      <el-option label="+65" value="+65" />
                      <el-option label="+86" value="+86" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.buyerIdTypeLabel')" prop="buyerIdType">
                <el-select v-model="createForm.buyerIdType" :placeholder="t('create.placeholders.buyerIdType')" style="width: 100%">
                  <el-option
                    v-for="option in getOptions(DICTIONARY_TYPES.ID_TYPE)"
                    :key="option.code"
                    :value="option.code"
                    :label="option.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.buyerIdNumberLabel')" prop="buyerIdNumber">
                <el-input v-model="createForm.buyerIdNumber" :placeholder="t('create.placeholders.buyerIdNumber')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.buyerEmailLabel')" prop="buyerEmail">
                <el-input v-model="createForm.buyerEmail" :placeholder="t('create.placeholders.buyerEmail')" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.buyerTypeLabel')" prop="buyerType">
                <el-select v-model="createForm.buyerType" :placeholder="t('create.placeholders.buyerType')" style="width: 100%">
                  <el-option
                    v-for="option in getOptions(DICTIONARY_TYPES.BUYER_TYPE)"
                    :key="option.code"
                    :value="option.code"
                    :label="option.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="t('create.buyerAddressLabel')" prop="buyerAddress">
                <el-input v-model="createForm.buyerAddress" :placeholder="t('create.placeholders.buyerAddress')" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('create.buyerStateLabel')" prop="buyerState">
                <el-input v-model="createForm.buyerState" :placeholder="t('create.placeholders.buyerState')" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('create.buyerCityLabel')" prop="buyerCity">
                <el-input v-model="createForm.buyerCity" :placeholder="t('create.placeholders.buyerCity')" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('create.buyerPostcodeLabel')" prop="buyerPostcode">
                <el-input v-model="createForm.buyerPostcode" :placeholder="t('create.placeholders.buyerPostcode')" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 经销商信息 -->
      <el-card class="mb-20">
        <h3>{{ t('create.dealershipInformation') }}</h3>
        <el-form :model="createForm" :rules="formRules" ref="formRef" label-position="top">
          <el-row :gutter="20" class="form-row">
            <el-col :span="12">
              <el-form-item :label="t('create.regionLabel')" prop="region">
                <el-select v-model="createForm.region" :placeholder="t('create.placeholders.region')" style="width: 100%">
                  <el-option
                    v-for="option in regionOptions"
                    :key="option.code"
                    :value="option.code"
                    :label="option.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.dealerCityLabel')" prop="dealerCity">
                <el-select v-model="createForm.dealerCity" :placeholder="t('create.placeholders.dealerCity')" style="width: 100%">
                  <el-option
                    v-for="option in cityOptions"
                    :key="option.code"
                    :value="option.code"
                    :label="option.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.dealershipLabel')" prop="dealership">
                <el-select v-model="createForm.dealership" :placeholder="t('create.placeholders.dealership')" style="width: 100%">
                  <el-option
                    v-for="option in dealershipOptions"
                    :key="option.code"
                    :value="option.code"
                    :label="option.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('create.salesAdvisorLabel')" prop="salesAdvisor">
                <el-select v-model="createForm.salesAdvisor" :placeholder="t('create.placeholders.salesAdvisor')" style="width: 100%">
                  <el-option
                    v-for="option in salesAdvisorOptions"
                    :key="option.code"
                    :value="option.code"
                    :label="option.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- Tab 导航 -->
      <el-tabs v-model="activeTab" class="order-tabs">
        <el-tab-pane :label="t('create.tabs.vehicle')" name="vehicle">
          <!-- 车辆信息 Tab -->
          <div class="tab-content-section">
            <h3>{{ t('create.vehicleInformation') }}</h3>
            <el-form :model="createForm" :rules="formRules" ref="formRef" label-position="top">
              <el-row :gutter="20" class="form-row">
                <el-col :span="12">
                  <el-form-item :label="t('create.modelLabel')" prop="model">
                    <el-select v-model="createForm.model" :placeholder="t('create.placeholders.selectModel')" style="width: 100%">
                      <el-option
                        v-for="option in vehicleModelOptions"
                        :key="option.code"
                        :value="option.code"
                        :label="option.name"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('create.variantLabel')" prop="variant">
                    <el-select v-model="createForm.variant" :placeholder="t('create.placeholders.selectVariant')" style="width: 100%">
                      <el-option
                        v-for="option in variantOptions"
                        :key="option.code"
                        :value="option.code"
                        :label="option.name"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('create.colorLabel')" prop="color">
                    <el-select v-model="createForm.color" :placeholder="t('create.placeholders.selectColor')" style="width: 100%">
                      <el-option
                        v-for="option in vehicleColorOptions"
                        :key="option.code"
                        :value="option.code"
                        :label="option.name"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('create.vinLabel')" prop="vin">
                    <el-input v-model="createForm.vin" :placeholder="t('create.placeholders.vin')" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane :label="t('create.tabs.invoice')" name="invoice">
          <!-- 开票信息 Tab -->
          <div class="tab-content-section">
            <h3>{{ t('create.invoiceInformation') }}</h3>
            <el-form :model="createForm" :rules="formRules" ref="formRef" label-position="top">
              <el-row :gutter="20" class="form-row">
                <el-col :span="12">
                  <el-form-item :label="t('create.invoiceTypeLabel')" prop="invoiceType">
                    <el-select v-model="createForm.invoiceType" :placeholder="t('create.placeholders.selectInvoiceType')" style="width: 100%">
                      <el-option :label="t('create.invoiceTypes.individual')" value="individual" />
                      <el-option :label="t('create.invoiceTypes.company')" value="company" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('create.invoiceNameLabel')" prop="invoiceName">
                    <el-input v-model="createForm.invoiceName" :placeholder="t('create.placeholders.invoiceName')" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('create.invoicePhoneLabel')" prop="invoicePhone">
                    <el-input v-model="createForm.invoicePhone" :placeholder="t('create.placeholders.invoicePhone')" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="t('create.invoiceAddressLabel')" prop="invoiceAddress">
                    <el-input v-model="createForm.invoiceAddress" :placeholder="t('create.placeholders.invoiceAddress')" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane :label="t('create.tabs.payment')" name="payment">
          <!-- 支付信息 Tab -->
          <div class="tab-content-section">
            <h3>{{ t('create.paymentInformation') }}</h3>
            <el-form :model="createForm" :rules="formRules" ref="formRef" label-position="top">
              <el-row :gutter="20" class="form-row">
                <el-col :span="6">
                  <el-form-item :label="t('create.paymentMethodLabel')" prop="paymentMethod">
                    <el-select
                      v-model="createForm.paymentMethod"
                      :placeholder="t('create.placeholders.selectPaymentMethod')"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="option in paymentMethodOptions"
                        :key="option.code"
                        :value="option.code"
                        :label="option.name"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('create.depositAmountLabel')" prop="depositAmount">
                    <el-input-number
                      v-model="createForm.depositAmount"
                      :placeholder="t('create.placeholders.depositAmount')"
                      style="width: 100%"
                      :min="0"
                      :precision="2"
                    />
                  </el-form-item>
                </el-col>
                <template v-if="createForm.paymentMethod === 'LOAN'">
                  <el-col :span="6">
                    <el-form-item :label="t('create.loanAmountLabel')" prop="loanAmount">
                      <el-input-number
                        v-model="createForm.loanAmount"
                        :placeholder="t('create.placeholders.loanAmount')"
                        style="width: 100%"
                        :min="0"
                        :precision="2"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item :label="t('create.loanTermLabel')" prop="loanTerm">
                      <el-select v-model="createForm.loanTerm" :placeholder="t('create.placeholders.selectLoanTerm')" style="width: 100%">
                        <el-option
                          v-for="option in loanTermOptions"
                          :key="option.value"
                          :value="option.value"
                          :label="option.label"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="t('create.loanApprovalStatusLabel')" prop="loanApprovalStatus">
                      <el-select v-model="createForm.loanApprovalStatus" :placeholder="t('create.placeholders.selectApprovalStatus')" style="width: 100%">
                        <el-option
                          v-for="option in approvalStatusOptions"
                          :key="option.code"
                          :value="option.code"
                          :label="option.name"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </template>
                <el-col :span="24">
                  <el-form-item :label="t('create.insuranceNotesLabel')" prop="insuranceNotes">
                    <el-input
                      v-model="createForm.insuranceNotes"
                      type="textarea"
                      :rows="3"
                      :placeholder="t('create.placeholders.insuranceNotes')"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane :label="t('create.tabs.rights')" name="rights">
          <!-- 权益选择 Tab -->
          <div class="tab-content-section">
            <h3>{{ t('create.rightsSelection') }}</h3>
            <el-button type="primary" @click="showRightsDialog">
              {{ t('create.selectRights') }}
            </el-button>
            <div v-if="createForm.selectedRights.length > 0" class="selected-rights mt-20">
              <h4>{{ t('create.selectedRights') }}</h4>
              <el-table :data="createForm.selectedRights" border>
                <el-table-column :label="t('create.rightsName')" prop="name" />
                <el-table-column :label="t('create.rightsValue')" prop="value" />
                <el-table-column :label="t('create.rightsDescription')" prop="description" />
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          {{ tc('save') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import { saveSalesOrder } from '@/api/modules/sales/orders'

// 使用正确的模块作用域
const { t, tc } = useModuleI18n('sales.orders')

// 使用字典数据
const {
  getOptions,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.PAYMENT_METHOD,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.ID_TYPE,
  DICTIONARY_TYPES.BUYER_TYPE
])

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 计算属性 - 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const formRef = ref<FormInstance>()
const activeTab = ref('vehicle')
const rightsDialogVisible = ref(false)

// 创建表单数据
const createForm = reactive({
  ordererName: '',
  ordererCountryCode: '+60',
  ordererPhoneNumber: '',
  buyerName: '',
  buyerCountryCode: '+60',
  buyerPhoneNumber: '',
  buyerIdType: '',
  buyerIdNumber: '',
  buyerEmail: '',
  buyerAddress: '',
  buyerState: '',
  buyerCity: '',
  buyerPostcode: '',
  buyerType: '',
  region: '',
  dealerCity: '',
  dealership: '',
  salesAdvisor: '',
  model: '',
  variant: '',
  color: '',
  vin: '',
  invoiceType: '',
  invoiceName: '',
  invoicePhone: '',
  invoiceAddress: '',
  selectedRights: [] as any[],
  paymentMethod: '',
  depositAmount: 0,
  loanAmount: 0,
  loanTerm: undefined as number | undefined,
  loanApprovalStatus: 'pending_review',
  insuranceNotes: ''
})

// 表单验证规则
const formRules: FormRules = {
  ordererName: [
    { required: true, message: t('create.validation.ordererNameRequired'), trigger: 'blur' }
  ],
  ordererPhoneNumber: [
    { required: true, message: t('create.validation.ordererPhoneRequired'), trigger: 'blur' }
  ],
  buyerName: [
    { required: true, message: t('create.validation.buyerNameRequired'), trigger: 'blur' }
  ],
  buyerPhoneNumber: [
    { required: true, message: t('create.validation.buyerPhoneRequired'), trigger: 'blur' }
  ],
  model: [
    { required: true, message: t('create.validation.modelRequired'), trigger: 'change' }
  ],
  color: [
    { required: true, message: t('create.validation.colorRequired'), trigger: 'change' }
  ],
  paymentMethod: [
    { required: true, message: t('create.validation.paymentMethodRequired'), trigger: 'change' }
  ]
}

// 选项数据
const regionOptions = ref([
  { code: 'north', name: '北部' },
  { code: 'central', name: '中部' },
  { code: 'south', name: '南部' }
])

const cityOptions = ref([
  { code: 'kl', name: '吉隆坡' },
  { code: 'selangor', name: '雪兰莪' },
  { code: 'penang', name: '槟城' }
])

const dealershipOptions = ref([
  { code: 'dealer1', name: '经销商1' },
  { code: 'dealer2', name: '经销商2' }
])

const salesAdvisorOptions = ref([
  { code: 'advisor1', name: '顾问1' },
  { code: 'advisor2', name: '顾问2' }
])

const vehicleModelOptions = ref([
  { code: 'AXIA', name: 'Axia' },
  { code: 'BEZZA', name: 'Bezza' },
  { code: 'MYVI', name: 'Myvi' }
])

const variantOptions = ref([
  { code: 'STANDARD', name: 'Standard' },
  { code: 'PREMIUM', name: 'Premium' },
  { code: 'ADVANCE', name: 'Advance' }
])

const vehicleColorOptions = ref([
  { code: 'red', name: '红色' },
  { code: 'blue', name: '蓝色' },
  { code: 'green', name: '绿色' }
])

const paymentMethodOptions = ref([
  { code: 'CASH', name: '全款' },
  { code: 'LOAN', name: '贷款' }
])

const loanTermOptions = ref([
  { value: 12, label: '12期' },
  { value: 24, label: '24期' },
  { value: 36, label: '36期' },
  { value: 48, label: '48期' },
  { value: 60, label: '60期' },
  { value: 72, label: '72期' }
])

const approvalStatusOptions = ref([
  { code: 'pending_review', name: '待审核' },
  { code: 'approved', name: '已通过' },
  { code: 'rejected', name: '已拒绝' }
])

// 显示权益选择对话框
const showRightsDialog = () => {
  rightsDialogVisible.value = true
}

// 保存订单
const handleSave = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    saveLoading.value = true

    const saveData = {
      ...createForm,
      orderNo: `ORD${Date.now()}` // 生成订单号
    }

    await saveSalesOrder(saveData)
    ElMessage.success(t('create.messages.saveSuccess'))
    
    emit('success')
    handleClose()
  } catch (error: unknown) {
    console.error('创建订单失败:', error)
    const errorMessage = error instanceof Error ? error.message : t('create.messages.saveFailed')
    ElMessage.error(errorMessage)
  } finally {
    saveLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  // 重置表单
  Object.assign(createForm, {
    ordererName: '',
    ordererCountryCode: '+60',
    ordererPhoneNumber: '',
    buyerName: '',
    buyerCountryCode: '+60',
    buyerPhoneNumber: '',
    buyerIdType: '',
    buyerIdNumber: '',
    buyerEmail: '',
    buyerAddress: '',
    buyerState: '',
    buyerCity: '',
    buyerPostcode: '',
    buyerType: '',
    region: '',
    dealerCity: '',
    dealership: '',
    salesAdvisor: '',
    model: '',
    variant: '',
    color: '',
    vin: '',
    invoiceType: '',
    invoiceName: '',
    invoicePhone: '',
    invoiceAddress: '',
    selectedRights: [],
    paymentMethod: '',
    depositAmount: 0,
    loanAmount: 0,
    loanTerm: undefined,
    loanApprovalStatus: 'pending_review',
    insuranceNotes: ''
  })
  
  activeTab.value = 'vehicle'
  dialogVisible.value = false
}
</script>

<style scoped>
.create-order-content {
  max-height: 70vh;
  overflow-y: auto;
}

.mb-20 {
  margin-bottom: 20px;
}

.section {
  margin-bottom: 20px;
}

.tab-content-section {
  padding: 20px 0;
}

.form-row {
  margin-bottom: 20px;
}

.custom-tabs .el-tabs__item {
  font-size: 16px;
}

.form-item-static {
  margin-bottom: 18px;
}

.form-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.form-control-static {
  font-size: 14px;
  color: #303133;
  padding: 8px 0;
}

.rights-section,
.otr-section {
  .total-amount {
    margin-top: 16px;
    text-align: right;
    font-weight: bold;
    font-size: 16px;
    color: #e6a23c;
  }
}

.footer-bar {
  position: sticky;
  bottom: 0;
  background: white;
  border-top: 1px solid #ebeef5;
  padding: 16px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
}

.price-summary {
  display: flex;
  gap: 30px;
  
  .total-price {
    color: #e6a23c;
    font-weight: bold;
    font-size: 18px;
  }
  
  .remaining-amount {
    color: #f56c6c;
    font-weight: bold;
    font-size: 16px;
  }
}

.buttons {
  display: flex;
  gap: 12px;
}

.mt-20 {
  margin-top: 20px;
}

.static-value {
  color: #e6a23c;
  font-weight: bold;
}
</style>
