<template>
  <div class="filter-section">
    <el-form ref="filterFormRef" :model="searchForm" label-position="top">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-form-item :label="t('testDrivePerson')">
            <el-input v-model="searchForm.customerName" :placeholder="t('pleaseEnterDriverName')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('testDrivePersonPhone')">
            <el-input v-model="searchForm.customerPhone" :placeholder="t('pleaseEnterDriverPhone')" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('testDriveModel')">
            <el-select
              v-model="searchForm.model"
              :placeholder="tc('all')"
              clearable
              style="width: 100%;"
              :loading="masterDataLoading"
            >
              <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('testDriveVariant')">
            <el-select
              v-model="searchForm.variant"
              :placeholder="tc('all')"
              clearable
              style="width: 100%;"
              :loading="masterDataLoading"
            >
              <el-option v-for="item in variantOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16" style="align-items: flex-end;">
        <el-col :span="6">
          <el-form-item :label="t('testDriveTime')">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              :range-separator="tc('to')"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <div class="button-group">
            <el-button @click="handleReset">
              {{ tc('reset') }}
            </el-button>
            <el-button type="primary" @click="handleSearch">
              {{ tc('search') }}
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, type PropType } from 'vue'
import { type FormInstance } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { VehicleModel, VehicleVariant } from '@/api/modules/masterData'

const { t, tc } = useModuleI18n('sales')

// 搜索表单数据类型
export interface SearchFormData {
  customerName: string
  customerPhone: string
  model: string | undefined
  variant: string | undefined
  dateRange: Date[]
}

// Props
const props = defineProps({
  modelValue: {
    type: Object as PropType<SearchFormData>,
    required: true
  },
  vehicleModelOptions: {
    type: Array as PropType<VehicleModel[]>,
    default: () => []
  },
  vehicleVariantOptions: {
    type: Array as PropType<VehicleVariant[]>,
    default: () => []
  },
  masterDataLoading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: SearchFormData]
  'search': []
  'reset': []
}>()

// Refs
const filterFormRef = ref<FormInstance>()

// 创建初始搜索表单
const createSearchForm = (): SearchFormData => ({
  customerName: '',
  customerPhone: '',
  model: undefined,
  variant: undefined,
  dateRange: []
})

// 搜索表单数据
const searchForm = reactive<SearchFormData>({ ...props.modelValue })


// 计算属性 - 转换选项格式
const modelOptions = computed(() =>
  props.vehicleModelOptions.map(model => ({
    label: model.name,
    value: model.id
  }))
)

const variantOptions = computed(() =>
  props.vehicleVariantOptions.map(variant => ({
    label: variant.name,
    value: variant.id
  }))
)

// 方法
const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  // 重置表单数据
  Object.assign(searchForm, createSearchForm())
  // 触发重置事件
  emit('reset')
}

// 监听表单数据变化，同步到父组件
watch(searchForm, (newVal) => {
  emit('update:modelValue', { ...newVal })
}, { deep: true })

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  Object.assign(searchForm, newVal)
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  reset: handleReset,
  search: handleSearch
})
</script>

<style scoped lang="scss">
.filter-section {
  background-color: white;
  padding: 24px;
  border-radius: 4px;
  margin-bottom: 16px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);

  .el-form-item {
    margin-bottom: 16px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
    margin-bottom: 8px;
  }

  .button-group {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-bottom: 22px;
  }
}
</style>
