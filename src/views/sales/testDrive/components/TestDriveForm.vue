<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="left" label-width="120px">
    <h3 class="section-title">{{ t('testDriveInfo') }}</h3>
    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item label="*试驾车型 (Model)" prop="model">
          <el-select v-model="formData.model" placeholder="请选择车型" style="width: 100%">
            <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾配置 (Variant)" prop="variant">
          <el-select v-model="formData.variant" placeholder="请选择配置" style="width: 100%">
            <el-option v-for="item in variantOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾人" prop="driverName">
          <el-input v-model="formData.driverName" placeholder="请输入试驾人姓名" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾人手机号" prop="driverPhone">
          <el-input v-model="formData.driverPhone" placeholder="请输入试驾人手机号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾人证件类别" prop="driverIdType">
          <el-select
            v-model="formData.driverIdType"
            placeholder="请选择证件类别"
            style="width: 100%"
            :loading="dictionaryLoading"
          >
            <el-option v-for="item in idTypeOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾人证件号" prop="driverIdNumber">
          <el-input v-model="formData.driverIdNumber" placeholder="请输入试驾人证件号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾人驾照号" prop="driverLicenseNumber">
          <el-input v-model="formData.driverLicenseNumber" placeholder="请输入试驾人驾照号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾开始里程数" prop="startMileage">
          <el-input-number v-model="formData.startMileage" :min="0" placeholder="请输入" style="width: 100%" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾结束里程数" prop="endMileage">
          <el-input-number v-model="formData.endMileage" :min="formData.startMileage || 0" placeholder="请输入" style="width: 100%" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾开始时间" prop="startTime">
          <el-date-picker
            v-model="formData.startTime"
            type="datetime"
            placeholder="年/月/日 --:--"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="*试驾结束时间" prop="endTime">
          <el-date-picker
            v-model="formData.endTime"
            type="datetime"
            placeholder="年/月/日 --:--"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="试驾反馈" prop="feedback">
          <el-input v-model="formData.feedback" type="textarea" placeholder="请输入试驾反馈..." />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ref, watch, type PropType } from 'vue'
import { type FormInstance, type FormRules } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { useDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'

const { t, tc } = useModuleI18n('sales')

// 表单数据接口
export interface TestDriveFormData {
  model: number | string | null
  variant: number | string | null
  driverName: string
  driverPhone: string
  driverIdType: number | string | null
  driverIdNumber: string
  driverLicenseNumber: string
  startMileage: number | null
  endMileage: number | null
  startTime: Date | null
  endTime: Date | null
  feedback: string
}

// Props
const props = defineProps({
  modelValue: {
    type: Object as PropType<TestDriveFormData>,
    required: true
  },
  mode: {
    type: String as PropType<'create' | 'edit'>,
    default: 'create'
  }
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: TestDriveFormData]
  'validate': [valid: boolean]
}>()

// Refs
const formRef = ref<FormInstance>()

// 使用字典数据
const {
  options: idTypeOptions,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.ID_TYPE)

// 响应式表单数据
const formData = ref<TestDriveFormData>({ ...props.modelValue })

// 表单验证规则
const rules: FormRules = {
  model: [
    { required: true, message: '请选择试驾车型', trigger: 'change' }
  ],
  variant: [
    { required: true, message: '请选择试驾配置', trigger: 'change' }
  ],
  driverName: [
    { required: true, message: '请输入试驾人姓名', trigger: 'blur' }
  ],
  driverPhone: [
    { required: true, message: '请输入试驾人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  driverIdType: [
    { required: true, message: '请选择试驾人证件类别', trigger: 'change' }
  ],
  driverIdNumber: [
    { required: true, message: '请输入试驾人证件号', trigger: 'blur' }
  ],
  driverLicenseNumber: [
    { required: true, message: '请输入试驾人驾照号', trigger: 'blur' }
  ],
  startMileage: [
    { required: true, type: 'number', message: '请输入开始里程数', trigger: 'blur' }
  ],
  endMileage: [
    { required: true, type: 'number', message: '请输入结束里程数', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择试驾开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择试驾结束时间', trigger: 'change' }
  ]
}

// 静态选项数据（实际项目中应从后端获取）
const modelOptions = ref([
  { label: 'AXIA', value: 'AXIA' },
  { label: 'BEZZA', value: 'BEZZA' },
  { label: 'ALZA', value: 'ALZA' },
  { label: 'ARUZ', value: 'ARUZ' }
])

const variantOptions = ref([
  { label: 'E', value: 'E' },
  { label: 'G', value: 'G' },
  { label: 'AV', value: 'AV' }
])

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  formData.value = { ...newVal }
}, { deep: true })

// 表单验证方法
const validate = async (): Promise<boolean> => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    
    // 额外的业务验证
    if ((formData.value.endMileage || 0) < (formData.value.startMileage || 0)) {
      return false
    }
    
    if (formData.value.endTime && formData.value.startTime) {
      const startTime = formData.value.startTime instanceof Date ? formData.value.startTime : new Date(formData.value.startTime)
      const endTime = formData.value.endTime instanceof Date ? formData.value.endTime : new Date(formData.value.endTime)
      if (endTime <= startTime) {
        return false
      }
    }
    
    emit('validate', true)
    return true
  } catch {
    emit('validate', false)
    return false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}

// 清除验证
const clearValidate = () => {
  formRef.value?.clearValidate()
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm,
  clearValidate
})
</script>

<style scoped>
.section-title {
  margin-top: 10px;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}
</style>