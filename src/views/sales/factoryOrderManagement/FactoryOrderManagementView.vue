<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 统计概览区域 -->
    <el-card class="mb-20 statistics-card">
      <el-row :gutter="20">
        <!-- 本月订单数量 -->
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.monthlyOrderCount.toLocaleString() }}</div>
            <div class="stat-label">{{ t('monthlyOrderCount') }}</div>
            <div class="stat-growth positive" v-if="statistics.monthlyGrowthRate > 0">
              {{ t('monthlyGrowthRate') }} {{ statistics.monthlyGrowthRate }}%
            </div>
          </div>
        </el-col>

        <!-- 今日订单数量 -->
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ statistics.dailyOrderCount.toLocaleString() }}</div>
            <div class="stat-label">{{ t('dailyOrderCount') }}</div>
            <div class="stat-growth positive" v-if="statistics.dailyGrowthRate > 0">
              {{ t('dailyGrowthRate') }} {{ statistics.dailyGrowthRate }}%
            </div>
          </div>
        </el-col>

        <!-- 订单最多门店 -->
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-store">{{ statistics.topStoreName || '-' }}</div>
            <div class="stat-label">{{ t('topDealer') }}</div>
            <div class="stat-detail">{{ t('monthlyOrderCount') }} {{ statistics.topStoreOrderCount || 0 }}单</div>
          </div>
        </el-col>

        <!-- 最热销车型 -->
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-store">{{ statistics.topModelName || '-' }}</div>
            <div class="stat-label">{{ t('topVehicle') }}</div>
            <div class="stat-detail">{{ t('monthlyOrderCount') }} {{ statistics.topModelCount || 0 }}单</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 搜索筛选区域 -->
    <el-card class="mb-20 search-card">
      <div class="search-title">{{ tc('searchConditions') }}</div>

      <el-form :model="searchParams" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('dealerName')">
              <el-select
                v-model="searchParams.dealerName"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in storeOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('model')">
              <el-select
                v-model="searchParams.model"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
                @change="handleModelChange"
              >
                <el-option
                  v-for="option in modelOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('variant')">
              <el-select
                v-model="searchParams.variant"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
                :disabled="!searchParams.model"
              >
                <el-option
                  v-for="option in variantOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('orderStatus')">
              <el-select
                v-model="searchParams.orderStatus"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in orderStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('paymentStatus')">
              <el-select
                v-model="searchParams.paymentStatus"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in paymentStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('orderDate')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="tc('pleaseInput')"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="6" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button @click="resetSearch">
                {{ tc('reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <div class="operation-bar">
        <div></div>
        <el-button type="success" :icon="Download" @click="handleExport" :loading="exporting">
          {{ t('exportExcel') }}
        </el-button>
      </div>
    </el-card>

    <!-- 订单列表区域 -->
    <el-card class="table-card">
      <template #header>
        <span>{{ t('listTitle') }}</span>
      </template>

      <el-table
        :data="orderList"
        v-loading="loading"
        style="width: 100%"
        height="500"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column type="index" :label="tc('index')" width="60" fixed="left" />
        <el-table-column prop="orderNo" :label="t('orderNumber')" min-width="150" fixed="left" />
        <el-table-column prop="storeName" :label="t('dealerName')" min-width="150">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.STORE, row.storeName) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="t('creationTime')" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="customerName" :label="t('customerName')" min-width="120" />
        <el-table-column prop="customerPhone" :label="t('customerPhone')" min-width="140" />
        <el-table-column prop="customerType" :label="t('customerType')" min-width="120" />
        <el-table-column prop="model" :label="t('model')" min-width="100">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_MODEL, row.model) }}
          </template>
        </el-table-column>
        <el-table-column prop="variant" :label="t('variant')" min-width="120">
          <template #default="{ row }">
            {{ getNameByCode(DICTIONARY_TYPES.VEHICLE_VARIANT, row.variant) }}
          </template>
        </el-table-column>
        <el-table-column prop="color" :label="tc('color')" min-width="100" />
        <el-table-column prop="vin" :label="t('vin')" min-width="150" />
        <el-table-column prop="paymentMethod" :label="t('paymentMethod')" min-width="100" />
        <el-table-column prop="loanStatus" :label="t('loanStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getLoanStatusTagType(row.loanStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.LOAN_STATUS, row.loanStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus" :label="t('orderStatus')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getOrderStatusTagType(row.orderStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.ORDER_STATUS, row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="approvalStatus" :label="t('approvalStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getApprovalStatusTagType(row.approvalStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.APPROVAL_STATUS, row.approvalStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" :label="t('paymentStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getPaymentStatusTagType(row.paymentStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="insuranceStatus" :label="t('insuranceStatus')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getInsuranceStatusTagType(row.insuranceStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.INSURANCE_STATUS, row.insuranceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="jpjRegistrationStatus" :label="t('jpjRegistrationStatus')" min-width="140">
          <template #default="{ row }">
            <el-tag :type="getJpjStatusTagType(row.jpjRegistrationStatus)">
              {{ getNameByCode(DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS, row.jpjRegistrationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="tc('operations')" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="View" link @click="showOrderDetail(row)">
              {{ tc('detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container mt-20">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <FactoryOrderDetailDialog
      v-model:visible="detailDialogVisible"
      :order-number="selectedOrderNumber"
    />
  </div>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({
  name: 'FactoryOrderManagementView'
})

import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Download, View } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type {
  FactoryOrderListItem,
  FactoryOrderSearchParams,
  FactoryOrderStatistics
} from '@/types/sales/factoryOrderManagement';
import {
  getOrderStatistics,
  getFactoryOrderList,
  exportFactoryOrderData,
} from '@/api/modules/sales/factoryOrderManagement';
import FactoryOrderDetailDialog from './components/FactoryOrderDetailDialog.vue';

// 国际化
const { t } = useModuleI18n('sales.factoryOrderManagement');
const { t: tc } = useModuleI18n('common');

// 数据字典
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.VEHICLE_MODEL,
  DICTIONARY_TYPES.VEHICLE_VARIANT,
  DICTIONARY_TYPES.ORDER_STATUS,
  DICTIONARY_TYPES.APPROVAL_STATUS,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.LOAN_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.JPJ_REGISTRATION_STATUS
]);

// 响应式数据
const loading = ref(false);
const exporting = ref(false);

// 统计数据
const statistics = ref<FactoryOrderStatistics>({
  monthlyOrderCount: 0,
  dailyOrderCount: 0,
  monthlyGrowthRate: 0,
  dailyGrowthRate: 0,
  topStoreName: '',
  topStoreOrderCount: 0,
  topModelName: '',
  topModelCount: 0,
  pendingDeliveryCount: 0,
  lastUpdateTime: '',
  topDealers: [],
  topVehicles: []
});

// 搜索参数
const searchParams = reactive<FactoryOrderSearchParams>({
  dealerName: '',
  model: '',
  variant: '',
  orderStatus: '',
  paymentStatus: '',
  orderNumber: '',
  orderDateStart: '',
  orderDateEnd: ''
});

// 日期范围
const dateRange = ref<[string, string] | null>(null);

// 订单列表
const orderList = ref<FactoryOrderListItem[]>([]);

// 分页信息（修正为MyBatisPlus标准）
const pagination = reactive({
  pageNum: 1,        // ✅ 修正为pageNum
  pageSize: 10,
  total: 0
});

// 详情弹窗
const detailDialogVisible = ref(false);
const selectedOrderNumber = ref('');

// 计算属性获取字典选项
const storeOptions = computed(() => getOptions(DICTIONARY_TYPES.STORE));
const modelOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_MODEL));
const variantOptions = computed(() => getOptions(DICTIONARY_TYPES.VEHICLE_VARIANT));
const orderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.ORDER_STATUS));
const paymentStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PAYMENT_STATUS));

// 标签类型映射函数
const getOrderStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01040001': 'warning',  // 待确认
    '01040002': 'info',     // 生产中
    '01040003': 'success',  // 已完成
    '01040004': 'danger'    // 已取消
  };
  return typeMap[status] || 'info';
};

const getApprovalStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01050001': 'warning',  // 待审批
    '01050002': 'success',  // 已通过
    '01050003': 'danger'    // 已拒绝
  };
  return typeMap[status] || 'info';
};

const getPaymentStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01060001': 'warning',  // 待付定金
    '01060002': 'primary',  // 已付定金
    '01060003': 'success',  // 已全款
    '01060004': 'danger'    // 支付失败
  };
  return typeMap[status] || 'info';
};

const getLoanStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01070001': 'warning',  // 申请中
    '01070002': 'success',  // 已通过
    '01070003': 'danger'    // 已拒绝
  };
  return typeMap[status] || 'info';
};

const getInsuranceStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01080001': 'warning',  // 待投保
    '01080002': 'success',  // 已投保
    '01080003': 'danger'    // 投保失败
  };
  return typeMap[status] || 'info';
};

const getJpjStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '01090001': 'warning',  // 待注册
    '01090002': 'success',  // 已注册
    '01090003': 'danger'    // 注册失败
  };
  return typeMap[status] || 'info';
};

// 业务方法
const loadStatistics = async () => {
  try {
    const data = await getOrderStatistics();
    statistics.value = data.result;
  } catch (error) {
    console.error('Failed to load statistics:', error);
  }
};

const loadOrderList = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchParams,
      pageNum: pagination.pageNum,     // ✅ 修正为pageNum
      pageSize: pagination.pageSize
    };
    const response = await getFactoryOrderList(params);
    orderList.value = response.result.records;
    pagination.total = response.result.total;
  } catch (error) {
    console.error('Failed to load order list:', error);
    ElMessage.error(tc('fetchDataFailed'));
  } finally {
    loading.value = false;
  }
};

const handleModelChange = () => {
  searchParams.variant = '';
};

const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchParams.orderDateStart = value[0];
    searchParams.orderDateEnd = value[1];
  } else {
    searchParams.orderDateStart = '';
    searchParams.orderDateEnd = '';
  }
};

const handleSearch = () => {
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  loadOrderList();
};

const resetSearch = () => {
  Object.assign(searchParams, {
    dealerName: '',
    model: '',
    variant: '',
    orderStatus: '',
    paymentStatus: '',
    orderDateStart: '',
    orderDateEnd: '',
    orderNumber: ''
  });
  dateRange.value = null;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  loadOrderList();
};

// 分页处理（修正为MyBatisPlus标准）
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;    // ✅ 修正为pageNum
  loadOrderList();
};

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;  // ✅ 修正为pageNum
  loadOrderList();
};

// 导出功能
const handleExport = async () => {
  exporting.value = true;
  try {
    const params = {
      searchParams: { ...searchParams },
      exportType: 'all_filtered' as const
    };
    const response = await exportFactoryOrderData(params);

    // 创建下载链接
    const link = document.createElement('a');
    link.href = response.downloadUrl;
    link.download = response.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success(tc('exportSuccess'));
  } catch (error) {
    console.error('Export failed:', error);
    ElMessage.error(tc('exportFailed'));
  } finally {
    exporting.value = false;
  }
};

// 显示订单详情
const showOrderDetail = (order: FactoryOrderListItem) => {
  selectedOrderNumber.value = order.orderNo;
  detailDialogVisible.value = true;
};

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-';
  try {
    return new Date(dateString).toLocaleString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return dateString;
  }
};

// 生命周期钩子
onMounted(() => {
  loadStatistics();
  loadOrderList();
});
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333333;
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .mt-20 {
    margin-top: 20px;
  }

  // 统计卡片样式
  .statistics-card {
    .stat-card {
      text-align: center;
      padding: 20px;

      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 8px;
      }

      .stat-store {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 4px;
      }

      .stat-growth {
        font-size: 12px;
        &.positive {
          color: #52c41a;
        }
        &.negative {
          color: #ff4d4f;
        }
      }

      .stat-detail {
        font-size: 12px;
        color: #999;
      }
    }
  }

  // 搜索卡片样式
  .search-card {
    .search-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 16px;
      text-align: left;
    }

    .el-form {
      .el-row {
        margin-bottom: 0;

        &:not(:last-child) {
          margin-bottom: 20px;
        }
      }

      .el-form-item {
        margin-bottom: 20px;
        width: 100%;

        .el-form-item__label {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
          margin-bottom: 8px;
          padding: 0;
          line-height: 1.4;
        }

        .el-select,
        .el-input,
        .el-date-editor {
          width: 100%;
        }
      }
    }

    .buttons-col {
      display: flex;
      align-items: flex-end;
      justify-content: flex-start;

      .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
          visibility: hidden;
          margin-bottom: 8px;
        }

        .el-button {
          margin-right: 10px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }

  // 操作栏样式
  .operation-card {
    .operation-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  // 表格卡片样式
  .table-card {
    .el-table {
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .el-table__header-wrapper {
        .el-table__header {
          th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 500;
          }
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      padding: 10px 0;
    }
  }
}

// 全局表格样式
:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
