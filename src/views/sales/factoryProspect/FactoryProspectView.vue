<template>
  <div class="factory-prospect-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 统计概览 -->
    <div class="stats-container">
      <div class="stats-card">
        <h3>{{ t('statistics.totalLeadCount') }}</h3>
        <div class="number">{{ stats.totalLeadCount || 0 }}</div>
        <div class="trend">
          {{ t('statistics.vsLastMonth') }} {{ stats.totalLeadCountVsLastMonth > 0 ? '+' : '' }}{{ stats.totalLeadCountVsLastMonth || 0 }}%
        </div>
      </div>
      <div class="stats-card">
        <h3>{{ t('statistics.hLevelProspectCount') }}</h3>
        <div class="number">{{ stats.hLevelProspectCount || 0 }}</div>
        <div class="trend">
          {{ t('statistics.vsLastMonth') }} {{ stats.hLevelProspectCountVsLastMonth > 0 ? '+' : '' }}{{ stats.hLevelProspectCountVsLastMonth || 0 }}%
        </div>
      </div>
      <div class="stats-card">
        <h3>{{ t('statistics.monthlyDealProspectCount') }}</h3>
        <div class="number">{{ stats.monthlyDealProspectCount || 0 }}</div>
        <div class="trend">{{ t('statistics.conversionRate') }} {{ stats.monthlyConversionRate || '0' }}%</div>
      </div>
      <div class="stats-card">
        <h3>{{ t('statistics.crossStoreCustomerCount') }}</h3>
        <div class="number">{{ stats.crossStoreCustomerCount || 0 }}</div>
        <div class="trend">{{ t('statistics.ratio') }} {{ stats.crossStoreCustomerRatio || '0' }}%</div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-section">
      <el-form
        ref="formRef"
        :model="filterForm"
        label-position="top"
        :show-message="false"
      >
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item :label="t('search.leadId')">
              <el-input v-model="filterForm.leadId" :placeholder="t('search.leadId')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('search.store')">
              <el-select
                v-model="filterForm.storeId"
                :placeholder="tc('all')"
                clearable
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in storeOptions"
                  :key="option.code"
                  :label="option.name"
                  :value="option.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('search.storeCount')">
              <el-select
                v-model="filterForm.storeCount"
                :placeholder="t('search.storeCount')"
                clearable
              >
                <el-option :label="t('search.allStoreCount')" value="" />
                <el-option :label="t('search.singleStore')" value="single" />
                <el-option :label="t('search.multiStore')" value="multi" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('search.registrationTime')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                clearable
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <el-card class="mb-20">
      <div class="action-section">
        <div class="view-type-tabs">
          <el-button 
            :type="filterForm.viewType === 'all' ? 'primary' : 'default'"
            @click="handleViewTypeChange('all')"
          >
            {{ t('viewType.all') }}
          </el-button>
          <el-button 
            :type="filterForm.viewType === 'crossStore' ? 'primary' : 'default'"
            @click="handleViewTypeChange('crossStore')"
          >
            {{ t('viewType.crossStore') }}
          </el-button>
          <el-button 
            :type="filterForm.viewType === 'noIntention' ? 'primary' : 'default'"
            @click="handleViewTypeChange('noIntention')"
          >
            {{ t('viewType.noIntention') }}
          </el-button>
          <el-button 
            :type="filterForm.viewType === 'converted' ? 'primary' : 'default'"
            @click="handleViewTypeChange('converted')"
          >
            {{ t('viewType.converted') }}
          </el-button>
        </div>
        <div>
          <el-space>
            <el-button type="primary" @click="handleSearch" :icon="Search">
              {{ t('search.search') }}
            </el-button>
            <el-button @click="resetForm" :icon="Refresh">
              {{ t('search.reset') }}
            </el-button>
            <el-button @click="exportData" :icon="Download">
              {{ t('search.export') }}
            </el-button>
          </el-space>
        </div>
      </div>
    </el-card>

    <!-- 潜客列表 -->
    <el-card class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        stripe
        height="600"
        style="width: 100%"
        :row-key="(row: any) => row.globalCustomerId"
      >
        <el-table-column type="index" :label="t('table.index')" width="60" fixed="left" />
        <el-table-column prop="leadId" :label="t('table.leadId')" min-width="120" />
        <el-table-column prop="customerName" :label="t('table.customerName')" min-width="100" />
        <el-table-column prop="phoneNumber" :label="t('table.phoneNumber')" min-width="130">
          <template #default="{ row }">
            {{ row.phoneNumber || tc('noData') }}
          </template>
        </el-table-column>
        <el-table-column prop="associatedStoreCount" :label="t('table.associatedStoreCount')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.associatedStoreCount > 1 ? 'warning' : ''">
              {{ row.associatedStoreCount }}{{ t('storeUnit') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="registrationTime" :label="t('table.registrationTime')" min-width="160">
          <template #default="{ row }">
            {{ row.registrationTime || tc('noData') }}
          </template>
        </el-table-column>
        <el-table-column prop="prospectStatus" :label="t('table.prospectStatus')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.prospectStatus)">
              {{ getStatusText(row.prospectStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column :label="t('table.actions')" min-width="100" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              type="primary"
              link
              @click="showLeadDetail(row.globalCustomerId)"
            >
              {{ t('table.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section mt-20">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 潜客详情模态框 -->
    <customer-detail-modal
      v-model:show="showDetailModal"
      :customer-id="currentCustomerId"
    />

    <!-- 意向级别配置模态框 -->
    <intent-level-config-modal
      v-model:show="showIntentLevelConfigModal"
    />
  </div>
</template>

<script setup lang="ts" name="FactoryProspectManagement">
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Refresh, Download } from '@element-plus/icons-vue';
import { getFactoryProspectList, getFactoryProspectStatistics, exportFactoryProspectData } from '@/api/modules/sales/factoryProspect';
import type {
  FactoryProspectListItem,
  FactoryProspectStatistics,
  FactoryProspectSearchParams
} from '@/types/sales/factoryProspect';
import { useBatchDictionary } from '@/composables/useDictionary';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import CustomerDetailModal from './components/CustomerDetailModal.vue';
import IntentLevelConfigModal from './components/IntentLevelConfigModal.vue';

// 国际化
const { t, tc } = useModuleI18n('sales.factoryProspect');

// 数据字典
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.STORE,
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.PROSPECT_STATUS,
  DICTIONARY_TYPES.STORE_COUNT_TYPE
]);

// 计算属性获取字典选项
const storeOptions = computed(() => getOptions(DICTIONARY_TYPES.STORE));
const storeCountOptions = computed(() => getOptions(DICTIONARY_TYPES.STORE_COUNT_TYPE));

// 标签类型映射函数
const getLevelTagType = (level: string): string => {
  const typeMap: Record<string, string> = {
    '01160001': 'danger',   // H级
    '01160002': 'warning',  // A级
    '01160003': 'success',  // B级
    '01160004': 'info'      // C级
  };
  return typeMap[level] || 'info';
};

const getStatusTagType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'following': 'warning',     // 跟进中
    'converted': 'success',     // 已成交
    'no_intention': 'danger'    // 无意向
  };
  return typeMap[status] || 'info';
};

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'following': '跟进中',
    'converted': '已成交',
    'no_intention': '无意向'
  };
  return statusMap[status] || t('notConverted');
};

// 关联门店数类型转换
const getStoreCountTypeName = (code: string): string => {
  const typeMap: Record<string, string> = {
    'single': t('storeCountTypes.single'),
    'multiple': t('storeCountTypes.multiple'), 
    'all': t('storeCountTypes.all')
  };
  return typeMap[code] || code;
};

// 统计数据
const stats = ref<FactoryProspectStatistics>({
  totalLeadCount: 0,
  hLevelProspectCount: 0,
  monthlyConversionProspectCount: 0,
  crossStoreCustomerCount: 0,
  totalLeadCountVsLastMonth: 0,
  hLevelProspectCountVsLastMonth: 0,
  monthlyConversionRate: '0',
  crossStoreCustomerRatio: '0'
});

// 筛选表单
const filterForm = reactive<FactoryProspectSearchParams>({
  leadId: '',
  storeId: '',
  storeCount: '',
  registrationTimeStart: '',
  registrationTimeEnd: '',
  viewType: 'all',
  pageNum: 1,
  pageSize: 20
});

// 日期范围
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (val) => {
  if (val) {
    filterForm.registrationTimeStart = val[0];
    filterForm.registrationTimeEnd = val[1];
  } else {
    filterForm.registrationTimeStart = '';
    filterForm.registrationTimeEnd = '';
  }
});

// 表格数据
const tableData = ref<FactoryProspectListItem[]>([]);
const loading = ref(false);

// 分页（修正为MyBatisPlus标准）
const pagination = reactive({
  pageNum: 1,     // 修正为pageNum
  pageSize: 20,
  total: 0        // 修正为total
});

// 模态框状态
const showDetailModal = ref(false);
const showIntentLevelConfigModal = ref(false);
const currentCustomerId = ref('');

// 获取统计数据
const fetchStats = async () => {
  try {
    const statsParams = {
      // storeId: filterForm.storeId || undefined,
      // registrationTimeStart: filterForm.registrationTimeStart || undefined,
      // registrationTimeEnd: filterForm.registrationTimeEnd || undefined
    };

    const res = await getFactoryProspectStatistics(statsParams);
    if (res && res.result) {
      stats.value = res.result;
    }
  } catch (error) {
    console.error('获取统计数据失败', error);
    ElMessage.error(tc('fetchStatsFailed'));
  }
};

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true;
  try {
    const params: FactoryProspectSearchParams = {
      ...filterForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    };

    const res = await getFactoryProspectList(params);
    if (res && res.result) {
      tableData.value = res.result.records;
      pagination.total = res.result.total;
      pagination.pageNum = res.result.current;
      pagination.pageSize = res.result.size;
    }
  } catch (error) {
    console.error('获取潜客列表失败', error);
    ElMessage.error(tc('fetchDataFailed'));
  } finally {
    loading.value = false;
  }
};

// 切换视图类型
const handleViewTypeChange = (viewType: string) => {
  filterForm.viewType = viewType as 'all' | 'crossStore' | 'noIntention' | 'converted';
  pagination.pageNum = 1;
  fetchTableData();
};

// 根据视图类型过滤数据的逻辑
const getFilteredData = () => {
  let filteredData = prospectList.value;
  
  switch (filterForm.viewType) {
    case 'crossStore':
      filteredData = filteredData.filter(item => item.associatedStoreCount > 1);
      break;
    case 'noIntention':
      filteredData = filteredData.filter(item => item.prospectStatus === 'no_intention');
      break;
    case 'converted':
      filteredData = filteredData.filter(item => item.prospectStatus === 'converted');
      break;
    default:
      // 'all' - 显示所有数据
      break;
  }
  
  return filteredData;
};

// 查询
const handleSearch = () => {
  pagination.pageNum = 1;
  fetchTableData();
  fetchStats();
};

// 重置表单
const resetForm = () => {
  filterForm.leadId = '';
  filterForm.storeId = '';
  filterForm.storeCount = '';
  dateRange.value = null;
  filterForm.registrationTimeStart = '';
  filterForm.registrationTimeEnd = '';
  filterForm.viewType = 'all';
  ElMessage.success(tc('resetSuccess'));

  fetchTableData();
  fetchStats();
};

// 导出数据
const exportData = async () => {
  try {
    ElMessage.info(tc('exporting'));
    const params: FactoryProspectSearchParams = {
      ...filterForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    };

    const res = await exportFactoryProspectData(params);

    const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${t('title')}_${new Date().toISOString().split('T')[0]}.xlsx`;
    link.click();
    URL.revokeObjectURL(link.href);

    ElMessage.success(tc('exportSuccess'));
  } catch (error) {
    console.error('导出数据失败', error);
    ElMessage.error(tc('exportFailed'));
  }
};

// 分页变化
const handlePageChange = (pageNum: number) => {
  pagination.pageNum = pageNum;
  fetchTableData();
};

// 每页条数变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.pageNum = 1;
  fetchTableData();
};

// 显示潜客详情
const showLeadDetail = (id: string) => {
  currentCustomerId.value = id;
  showDetailModal.value = true;
};

// 初始化
onMounted(() => {
  fetchStats();
  fetchTableData();
});
</script>

<style scoped>
.factory-prospect-container {
  padding: 0;
  width: 100%;
}

.page-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--text-primary, #333333);
}

/* 统计卡片样式 */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  background: #ffffff;
  border-radius: 6px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-left: 4px solid #1890ff;
}

.stats-card h3 {
  margin: 0 0 10px 0;
  font-size: 13px;
  color: #666666;
  font-weight: normal;
}

.stats-card .number {
  font-size: 24px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 5px;
}

.stats-card .trend {
  font-size: 12px;
  color: #52c41a;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 16px;
  width: 100%;
}

/* 功能按钮区域 */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  width: 100%;
}

.ml-2 {
  margin-left: 8px;
}

/* 表格容器 */
.table-container {
  margin-bottom: 16px;
  width: 100%;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 添加一些辅助类 */
.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: flex-end;
}
</style>
