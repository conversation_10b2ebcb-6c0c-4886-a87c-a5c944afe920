<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('pageTitle') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchForm" label-position="top" @submit.prevent="handleSearch">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('search.orderNumber')">
              <el-input v-model="searchForm.orderNumber" :placeholder="t('search.placeholder.orderNumber')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('search.customerName')">
              <el-input v-model="searchForm.customerName" :placeholder="t('search.placeholder.customerName')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('search.customerPhone')">
              <el-input v-model="searchForm.customerPhone" :placeholder="t('search.placeholder.customerPhone')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('search.vin')">
              <el-input v-model="searchForm.vin" :placeholder="t('search.placeholder.vin')" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('search.registrationStatus')">
              <el-select v-model="searchForm.registrationStatus" :placeholder="t('search.placeholder.registrationStatus')" clearable>
                <el-option :label="t('status.all')" value="" />
                <el-option
                  v-for="status in registrationStatusOptions"
                  :key="status.code"
                  :label="status.name"
                  :value="status.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('search.insuranceStatus')">
              <el-select v-model="searchForm.insuranceStatus" :placeholder="t('search.placeholder.insuranceStatus')" clearable>
                <el-option :label="t('insurance.all')" value="" />
                <el-option
                  v-for="status in insuranceStatusOptions"
                  :key="status.code"
                  :label="status.name"
                  :value="status.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('search.salesAdvisor')">
              <el-select v-model="searchForm.salesAdvisor" :placeholder="t('search.placeholder.salesAdvisor')" clearable filterable>
                <el-option
                  v-for="advisor in salesAdvisorOptions"
                  :key="advisor.code"
                  :label="advisor.name"
                  :value="advisor.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('search.pushTimeRange')">
              <el-date-picker
                v-model="pushTimeRange"
                type="datetimerange"
                :start-placeholder="t('search.pushTimeStart')"
                :end-placeholder="t('search.pushTimeEnd')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="18"></el-col>
          <el-col :span="6" class="buttons-col">
            <el-button type="primary" :loading="loading" @click="handleSearch">{{ t('search.searchButton') }}</el-button>
            <el-button @click="handleReset">{{ t('search.resetButton') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="mb-20">
      <div class="action-buttons">
        <el-button
          type="success"
          :loading="exportLoading"
          @click="handleExport"
        >
          {{ t('search.exportButton') }}
        </el-button>
      </div>
    </el-card>

    <!-- 车辆登记列表区域 -->
    <el-card>

      <el-table :data="tableData || []" v-loading="loading" style="width: 100%" stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" :label="tc('index')" width="60" />

        <el-table-column
          :label="t('table.orderNumber')"
          prop="orderNo"
          width="140"
          show-overflow-tooltip
        />

        <el-table-column
          :label="t('table.customerName')"
          prop="customerName"
          width="120"
          show-overflow-tooltip
        />

        <el-table-column
          :label="t('table.customerPhone')"
          prop="customerPhone"
          width="140"
          show-overflow-tooltip
        />

        <el-table-column
          :label="t('table.vin')"
          prop="vin"
          width="180"
          show-overflow-tooltip
        />

        <el-table-column
          :label="t('table.vehicleModel')"
          prop="vehicleModel"
          width="120"
          show-overflow-tooltip
        />

        <el-table-column
          :label="t('table.vehicleColor')"
          prop="vehicleColor"
          width="100"
        />

        <el-table-column
          :label="t('table.insuranceStatus')"
          prop="insuranceStatus"
          width="120"
        >
          <template #default="{ row }">
            <el-tag v-if="row && row.insuranceStatus" :type="getStatusTagType(row.insuranceStatus, DICTIONARY_TYPES.INSURANCE_STATUS)">
              {{ formatInsuranceStatus(row.insuranceStatus) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="t('table.companyName')"
          prop="companyName"
          width="140"
          show-overflow-tooltip
        />

        <el-table-column
          :label="t('table.registrationStatus')"
          prop="status"
          width="120"
        >
          <template #default="{ row }">
            <el-tag v-if="row && row.status" :type="getStatusTagType(row.status, DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS)">
              {{ formatRegistrationStatus(row.status) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          :label="t('table.lastPushTime')"
          prop="lastPushTime"
          width="180"
        >
          <template #default="{ row }">
            {{ row && row.lastPushTime ? formatDateTime(row.lastPushTime) : '-' }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('table.registrationFee')"
          prop="registrationFee"
          width="120"
        >
          <template #default="{ row }">
            {{ row && row.registrationFee ? t('units.currency') + row.registrationFee : '-' }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('table.salesAdvisor')"
          prop="salesAdvisorName"
          width="120"
          show-overflow-tooltip
        />

        <el-table-column
          :label="t('table.createdAt')"
          prop="createdAt"
          width="180"
        >
          <template #default="{ row }">
            {{ row && row.createdAt ? formatDateTime(row.createdAt) : '-' }}
          </template>
        </el-table-column>

        <el-table-column
          :label="t('table.actions')"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <template v-if="row">
              <el-button
                type="primary"
                size="small"
                @click="handleViewDetail(row)"
              >
                {{ t('actions.viewDetail') }}
              </el-button>

              <el-button
                v-if="row.status === 'pending'"
                type="success"
                size="small"
                @click="handlePush(row)"
              >
                {{ t('actions.push') }}
              </el-button>

              <el-button
                v-if="row.status === 'failed'"
                type="warning"
                size="small"
                @click="handleRetryPush(row)"
              >
                {{ t('actions.retryPush') }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <VehicleRegistrationDetailDialog
      v-model:visible="detailDialogVisible"
      :id="currentDetailId"
    />

    <!-- 推送确认对话框 -->
    <PushConfirmDialog
      v-model:visible="pushConfirmDialogVisible"
      :loading="pushLoading"
      :selected-items="selectedItemsForPush"
      @confirm="handleConfirmPush"
    />

    <!-- 重新推送对话框 -->
    <RetryPushDialog
      v-model:visible="retryPushDialogVisible"
      :loading="retryPushLoading"
      :item="currentRetryItem"
      :failure-reason="currentFailureReason"
      @confirm="handleConfirmRetryPush"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { FormInstance, TableInstance } from 'element-plus';

// 组件导入
import VehicleRegistrationDetailDialog from './components/VehicleRegistrationDetailDialog.vue';
import PushConfirmDialog from './components/PushConfirmDialog.vue';
import RetryPushDialog from './components/RetryPushDialog.vue';

// API导入
import {
  getVehicleRegistrationList,
  pushVehicleRegistration,
  retryPushVehicleRegistration,
  batchPushVehicleRegistration,
  exportVehicleRegistrationData,
  getSalesAdvisorsList
} from '@/api/modules/sales/vehicleRegistration';

// 类型导入
import type {
  VehicleRegistrationSearchParams,
  VehicleRegistrationItem,
  VehicleRegistrationPageResponse,
  SalesAdvisorOption
} from '@/types/sales/vehicleRegistration';

// 工具函数导入
import { formatDateTime } from '@/utils/date';

// 国际化
const { t, tc } = useModuleI18n('sales.vehicleRegistration');

// 使用标准数据字典
const { getOptions, getNameByCode, loading: dictionaryLoading } = useBatchDictionary([
  DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS,
  DICTIONARY_TYPES.INSURANCE_STATUS,
  DICTIONARY_TYPES.SALES_ADVISOR
]);

// 表单引用
const searchFormRef = ref<FormInstance>();
const tableRef = ref<TableInstance>();

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const batchPushLoading = ref(false);
const pushLoading = ref(false);
const retryPushLoading = ref(false);

// 搜索表单
const searchForm = reactive<VehicleRegistrationSearchParams>({
  orderNumber: '',
  customerName: '',
  customerPhone: '',
  registrationStatus: '',
  vin: '',
  insuranceStatus: '',
  salesAdvisor: '',
  pushTimeStart: '',
  pushTimeEnd: ''
});

// 时间范围
const pushTimeRange = ref<[string, string] | null>(null);

// 表格数据
const tableData = ref<VehicleRegistrationItem[]>([]);
const selectedRows = ref<VehicleRegistrationItem[]>([]);

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
});

// 对话框状态
const detailDialogVisible = ref(false);
const pushConfirmDialogVisible = ref(false);
const retryPushDialogVisible = ref(false);

// 当前操作的数据
const currentDetailId = ref<string>('');
const currentRetryItem = ref<VehicleRegistrationItem | null>(null);
const currentFailureReason = ref<string>('');
const selectedItemsForPush = ref<VehicleRegistrationItem[]>([]);

// 计算属性获取字典选项
const registrationStatusOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS)
);

const insuranceStatusOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.INSURANCE_STATUS)
);

const salesAdvisorOptions = computed(() =>
  getOptions(DICTIONARY_TYPES.SALES_ADVISOR)
);

// 监听时间范围变化
watch(pushTimeRange, (newVal) => {
  if (newVal) {
    searchForm.pushTimeStart = newVal[0];
    searchForm.pushTimeEnd = newVal[1];
  } else {
    searchForm.pushTimeStart = '';
    searchForm.pushTimeEnd = '';
  }
});

// 状态标签样式映射
const STATUS_TYPE_MAPS = {
  [DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS]: {
    'pending': 'primary',
    'processing': 'warning',
    'success': 'success',
    'failed': 'danger'
  },
  [DICTIONARY_TYPES.INSURANCE_STATUS]: {
    'insured': 'success',
    'not_insured': 'info'
  }
};

// 获取状态标签类型
const getStatusTagType = (status: string, dictionaryType: string) =>
  STATUS_TYPE_MAPS[dictionaryType]?.[status] || 'info';

// 标准字典转义函数
const formatRegistrationStatus = (status: string) =>
  getNameByCode(DICTIONARY_TYPES.VEHICLE_REGISTRATION_STATUS, status) || status;

const formatInsuranceStatus = (status: string) =>
  getNameByCode(DICTIONARY_TYPES.INSURANCE_STATUS, status) || status;

// 获取表格索引
const getTableIndex = (index: number) => {
  return (pagination.pageNum - 1) * pagination.pageSize + index + 1;
};

// 过滤空值参数
const filterEmptyParams = (params: VehicleRegistrationSearchParams): VehicleRegistrationSearchParams => {
  const filteredParams: VehicleRegistrationSearchParams = {
    pageNum: params.pageNum,
    pageSize: params.pageSize
  };

  // 只保留非空值参数
  Object.keys(params).forEach(key => {
    const value = params[key as keyof VehicleRegistrationSearchParams];
    if (value !== null && value !== undefined && String(value).trim() !== '' && key !== 'pageNum' && key !== 'pageSize') {
      (filteredParams as any)[key] = value;
    }
  });

  return filteredParams;
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const rawParams: VehicleRegistrationSearchParams = {
      ...searchForm,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize
    };

    // 过滤空值参数
    const params = filterEmptyParams(rawParams);

    const response = await getVehicleRegistrationList(params);
    if (response.code === '200') {
      const result = response.result as VehicleRegistrationPageResponse;
      tableData.value = result.records;
      pagination.total = result.total;
      pagination.pageNum = result.current;
      pagination.pageSize = result.size;
    } else {
      ElMessage.error(response.message || t('vehicleRegistration.messages.error.loadFailed'));
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error(t('vehicleRegistration.messages.error.loadFailed'));
  } finally {
    loading.value = false;
  }
};



// 搜索处理
const handleSearch = () => {
  pagination.pageNum = 1;
  loadData();
};

// 重置处理
const handleReset = () => {
  searchFormRef.value?.resetFields();
  Object.assign(searchForm, {
    orderNumber: '',
    customerName: '',
    customerPhone: '',
    registrationStatus: '',
    vin: '',
    insuranceStatus: '',
    salesAdvisor: '',
    pushTimeStart: '',
    pushTimeEnd: ''
  });
  pushTimeRange.value = null;
  pagination.pageNum = 1;
  loadData();
};

// 导出处理
const handleExport = async () => {
  exportLoading.value = true;
  try {
    const rawParams: VehicleRegistrationSearchParams = {
      ...searchForm,
      pageNum: 1,
      pageSize: 10000 // 导出所有数据
    };

    // 过滤空值参数
    const params = filterEmptyParams(rawParams);

    const response = await exportVehicleRegistrationData(params);
    if (response.code === '200') {
      ElMessage.success(t('vehicleRegistration.messages.success.exportSuccess'));
      // 这里可以添加下载文件的逻辑
    } else {
      ElMessage.error(response.message || t('vehicleRegistration.messages.error.exportFailed'));
    }
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('vehicleRegistration.messages.error.exportFailed'));
  } finally {
    exportLoading.value = false;
  }
};

// 批量推送处理
const handleBatchPush = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning(t('vehicleRegistration.messages.error.noSelection'));
    return;
  }

  // 过滤出可以推送的记录（状态为pending或failed）
  const pushableItems = selectedRows.value.filter(item =>
    item.status === 'pending' || item.status === 'failed'
  );

  if (pushableItems.length === 0) {
    ElMessage.warning(t('vehicleRegistration.messages.warning.noPushableItems'));
    return;
  }

  selectedItemsForPush.value = pushableItems;
  pushConfirmDialogVisible.value = true;
};

// 表格选择变化处理
const handleSelectionChange = (selection: VehicleRegistrationItem[]) => {
  selectedRows.value = selection;
};

// 分页大小变化处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.pageNum = 1;
  loadData();
};

// 当前页变化处理
const handleCurrentChange = (page: number) => {
  pagination.pageNum = page;
  loadData();
};

// 查看详情处理
const handleViewDetail = (row: VehicleRegistrationItem) => {
  currentDetailId.value = row.id;
  detailDialogVisible.value = true;
};

// 推送处理
const handlePush = (row: VehicleRegistrationItem) => {
  selectedItemsForPush.value = [row];
  pushConfirmDialogVisible.value = true;
};

// 重新推送处理
const handleRetryPush = (row: VehicleRegistrationItem) => {
  currentRetryItem.value = row;
  currentFailureReason.value = '网络连接超时，请重试'; // 这里可以从详情接口获取具体失败原因
  retryPushDialogVisible.value = true;
};

// 确认推送处理
const handleConfirmPush = async () => {
  const items = selectedItemsForPush.value;
  if (items.length === 0) return;

  if (items.length === 1) {
    // 单个推送
    pushLoading.value = true;
    try {
      const response = await pushVehicleRegistration(items[0].id);
      if (response.code === '200') {
        ElMessage.success(t('vehicleRegistration.messages.success.pushSuccess'));
        pushConfirmDialogVisible.value = false;
        loadData(); // 刷新数据
      } else {
        ElMessage.error(response.message || t('vehicleRegistration.messages.error.pushFailed'));
      }
    } catch (error) {
      console.error('推送失败:', error);
      ElMessage.error(t('vehicleRegistration.messages.error.pushFailed'));
    } finally {
      pushLoading.value = false;
    }
  } else {
    // 批量推送
    batchPushLoading.value = true;
    try {
      const ids = items.map(item => item.id);
      const response = await batchPushVehicleRegistration(ids);
      if (response.code === '200') {
        ElMessage.success(t('vehicleRegistration.messages.success.batchPushSuccess'));
        pushConfirmDialogVisible.value = false;
        loadData(); // 刷新数据
        selectedRows.value = []; // 清空选择
      } else {
        ElMessage.error(response.message || t('vehicleRegistration.messages.error.batchPushFailed'));
      }
    } catch (error) {
      console.error('批量推送失败:', error);
      ElMessage.error(t('vehicleRegistration.messages.error.batchPushFailed'));
    } finally {
      batchPushLoading.value = false;
    }
  }
};

// 确认重新推送处理
const handleConfirmRetryPush = async () => {
  if (!currentRetryItem.value) return;

  retryPushLoading.value = true;
  try {
    const response = await retryPushVehicleRegistration(currentRetryItem.value.id);
    if (response.code === '200') {
      ElMessage.success(t('vehicleRegistration.messages.success.retryPushSuccess'));
      retryPushDialogVisible.value = false;
      loadData(); // 刷新数据
    } else {
      ElMessage.error(response.message || t('vehicleRegistration.messages.error.retryPushFailed'));
    }
  } catch (error) {
    console.error('重新推送失败:', error);
    ElMessage.error(t('vehicleRegistration.messages.error.retryPushFailed'));
  } finally {
    retryPushLoading.value = false;
  }
};

// 组件挂载时初始化
onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.page-title {
  font-size: 28px;
  margin-bottom: 25px;
  text-align: center;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.buttons-col {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  align-items: flex-end;

  .el-button {
    margin-left: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
