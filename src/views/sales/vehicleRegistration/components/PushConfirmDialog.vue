<template>
  <el-dialog
    :model-value="visible"
    :title="t('dialog.pushConfirm.title')"
    width="500px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="confirm-content">
      <el-icon class="confirm-icon" size="48" color="#E6A23C">
        <WarningFilled />
      </el-icon>
      <div class="confirm-text">
        <template v-if="isBatch">
          {{ t('dialog.pushConfirm.batchContent', { count: selectedCount }) }}
        </template>
        <template v-else>
          {{ t('dialog.pushConfirm.singleContent') }}
        </template>
      </div>

      <!-- 批量推送时显示选中的记录信息 -->
      <div v-if="isBatch && selectedItems && selectedItems.length > 0" class="selected-items">
        <el-divider />
        <div class="selected-title">{{ t('dialog.pushConfirm.selectedRecords') }}:</div>
        <div class="selected-list">
          <div
            v-for="item in (selectedItems || []).slice(0, 5)"
            :key="item?.id || Math.random()"
            class="selected-item"
          >
            <span class="item-order">{{ item?.orderNo || '-' }}</span>
            <span class="item-customer">{{ item?.customerName || '-' }}</span>
            <span class="item-vin">{{ item?.vin || '-' }}</span>
          </div>
          <div v-if="selectedItems && selectedItems.length > 5" class="more-items">
            {{ t('dialog.pushConfirm.moreRecords', { count: selectedItems.length - 5 }) }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ t('dialog.pushConfirm.cancelButton') }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          {{ t('dialog.pushConfirm.confirmButton') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { WarningFilled } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { VehicleRegistrationItem } from '@/types/sales/vehicleRegistration';

interface Props {
  visible: boolean;
  loading?: boolean;
  selectedItems?: VehicleRegistrationItem[];
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  selectedItems: () => []
});

const emit = defineEmits<Emits>();
// 国际化
const { t, tc } = useModuleI18n('sales.vehicleRegistration');

// 计算属性
const isBatch = computed(() => (props.selectedItems || []).length > 1);
const selectedCount = computed(() => (props.selectedItems || []).length);

// 确认推送
const handleConfirm = () => {
  emit('confirm');
};

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.confirm-content {
  text-align: center;
  padding: 20px 0;
}

.confirm-icon {
  margin-bottom: 16px;
}

.confirm-text {
  font-size: 16px;
  color: #303133;
  line-height: 1.5;
  margin-bottom: 20px;
}

.selected-items {
  text-align: left;
  margin-top: 20px;
}

.selected-title {
  font-weight: 600;
  color: #606266;
  margin-bottom: 12px;
}

.selected-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  padding: 8px;
}

.selected-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #F2F6FC;
  font-size: 14px;
}

.selected-item:last-child {
  border-bottom: none;
}

.item-order {
  flex: 0 0 120px;
  color: #409EFF;
  font-weight: 500;
}

.item-customer {
  flex: 0 0 100px;
  color: #303133;
}

.item-vin {
  flex: 1;
  color: #606266;
  font-family: monospace;
}

.more-items {
  text-align: center;
  color: #909399;
  font-style: italic;
  padding: 8px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
