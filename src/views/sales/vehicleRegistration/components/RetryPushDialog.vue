<template>
  <el-dialog
    :model-value="visible"
    :title="t('dialog.retryPush.title')"
    width="500px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="confirm-content">
      <el-icon class="confirm-icon" size="48" color="#F56C6C">
        <CircleCloseFilled />
      </el-icon>
      <div class="confirm-text">
        {{ t('dialog.retryPush.content') }}
      </div>

      <!-- 显示失败原因 -->
      <div v-if="failureReason" class="failure-reason">
        <el-divider />
        <div class="reason-title">{{ t('dialog.retryPush.failureReason') }}:</div>
        <div class="reason-content">
          <el-alert
            :title="failureReason"
            type="error"
            :closable="false"
            show-icon
          />
        </div>
      </div>

      <!-- 显示记录信息 -->
      <div v-if="item" class="item-info">
        <el-divider />
        <div class="info-title">{{ t('dialog.retryPush.recordInfo') }}:</div>
        <div class="info-content">
          <div class="info-row">
            <span class="info-label">{{ t('fields.orderInfo.orderNumber') }}:</span>
            <span class="info-value">{{ item?.orderNo || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">{{ t('fields.customerInfo.name') }}:</span>
            <span class="info-value">{{ item?.customerName || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">{{ t('fields.vehicleInfo.vin') }}:</span>
            <span class="info-value">{{ item?.vin || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">{{ t('fields.orderInfo.lastPushTime') }}:</span>
            <span class="info-value">{{ item?.lastPushTime ? formatDateTime(item.lastPushTime) : '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ t('dialog.retryPush.cancelButton') }}
        </el-button>
        <el-button
          type="danger"
          :loading="loading"
          @click="handleConfirm"
        >
          {{ t('dialog.retryPush.confirmButton') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { CircleCloseFilled } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { VehicleRegistrationItem } from '@/types/sales/vehicleRegistration';
import { formatDateTime } from '@/utils/date';

interface Props {
  visible: boolean;
  loading?: boolean;
  item?: VehicleRegistrationItem | null;
  failureReason?: string;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  item: null,
  failureReason: ''
});

const emit = defineEmits<Emits>();
// 国际化
const { t, tc } = useModuleI18n('sales.vehicleRegistration');

// 确认重新推送
const handleConfirm = () => {
  emit('confirm');
};

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.confirm-content {
  text-align: center;
  padding: 20px 0;
}

.confirm-icon {
  margin-bottom: 16px;
}

.confirm-text {
  font-size: 16px;
  color: #303133;
  line-height: 1.5;
  margin-bottom: 20px;
}

.failure-reason,
.item-info {
  text-align: left;
  margin-top: 20px;
}

.reason-title,
.info-title {
  font-weight: 600;
  color: #606266;
  margin-bottom: 12px;
}

.reason-content {
  margin-bottom: 16px;
}

.info-content {
  background-color: #F8F9FA;
  border-radius: 4px;
  padding: 16px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  flex: 0 0 100px;
  color: #606266;
  font-weight: 500;
}

.info-value {
  flex: 1;
  color: #303133;
}

.dialog-footer {
  text-align: right;
}
</style>
