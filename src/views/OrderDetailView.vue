<template>
  <div class="page-container" v-loading="loading">
    <h1 class="page-title">{{ t('orderDetail') }}</h1>

    <!-- 订单基本信息 -->
    <el-card class="mb-20">
      <el-row :gutter="20" class="order-summary-header">
        <el-col :span="12">
          {{ t('orderNumber') }}: <strong id="detailOrderNo">{{ orderDetails.orderNo || '-' }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('createTime') }}: <strong id="detailCreateTime">{{ orderDetails.createTime || '-' }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('orderStatus') }}: <strong>{{ formatOrderStatus(orderDetails.orderStatus) }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('approvalStatus') }}: <strong>{{ formatApprovalStatus(orderDetails.approvalStatus) }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('paymentStatus') }}: <strong>{{ formatPaymentStatus(orderDetails.paymentStatus) }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('insuranceStatus') }}: <strong>{{ formatInsuranceStatus(orderDetails.insuranceStatus) }}</strong>
        </el-col>
        <el-col :span="12">
          {{ t('jpjRegistrationStatus') }}: <strong>{{ formatJpjRegistrationStatus(orderDetails.jpjRegistrationStatus) }}</strong>
        </el-col>
      </el-row>
    </el-card>

    <!-- 客户信息 - Personal Details -->
    <el-card class="mb-20 section">
      <h2>{{ t('customerInfo') }}</h2>
      <el-row :gutter="20" class="form-row">
        <el-col :span="12">
          <div class="form-item-static">{{ t('ordererName') }}: <span class="form-control-static">{{ orderDetails.customerName || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('ordererPhone') }}: <span class="form-control-static">{{ orderDetails.customerPhone || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerName') }}: <span class="form-control-static">{{ orderDetails.customerName || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerPhone') }}: <span class="form-control-static">{{ orderDetails.customerPhone || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerIdType') }}: <span class="form-control-static">{{ orderDetails.idType || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerIdNumber') }}: <span class="form-control-static">{{ orderDetails.idNumber || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerEmail') }}: <span class="form-control-static">{{ orderDetails.email || '-' }}</span></div>
        </el-col>
        <el-col :span="24">
          <div class="form-item-static">{{ t('buyerAddress') }}: <span class="form-control-static">{{ orderDetails.address || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerState') }}: <span class="form-control-static">{{ orderDetails.state || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerCity') }}: <span class="form-control-static">{{ orderDetails.city || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerPostcode') }}: <span class="form-control-static">{{ orderDetails.zipCode || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('buyerType') }}: <span class="form-control-static">{{ formatCustomerType(orderDetails.customerType) || '-' }}</span></div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 购车门店信息 - Preferred Outlet & Sales Advisor -->
    <el-card class="mb-20 section">
      <h2>{{ t('storeInfo') }}</h2>
      <el-row :gutter="20" class="form-row">
        <el-col :span="12">
          <div class="form-item-static">{{ t('storeRegion') }}: <span class="form-control-static">{{ orderDetails.storeRegion || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('storeCity') }}: <span class="form-control-static">{{ orderDetails.dealerCity || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('storeName') }}: <span class="form-control-static">{{ orderDetails.dealerName || '-' }}</span></div>
        </el-col>
        <el-col :span="12">
          <div class="form-item-static">{{ t('salesConsultantName') }}: <span class="form-control-static">{{ orderDetails.salesConsultantName || '-' }}</span></div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 购车信息 - Purchase Details -->
    <el-card class="mb-20 section">
      <h2>{{ t('purchaseInfo') }}</h2>
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane :label="t('vehicleInfoTab')" name="vehicle"></el-tab-pane>
        <el-tab-pane :label="t('invoicingInfoTab')" name="invoice"></el-tab-pane>
        <el-tab-pane :label="t('rightsInfoTab')" name="rights"></el-tab-pane>
        <el-tab-pane :label="t('paymentInfoTab')" name="payment"></el-tab-pane>
        <el-tab-pane :label="t('insuranceInfoTab')" name="insurance"></el-tab-pane>
        <el-tab-pane :label="t('otrFeesTab')" name="otr"></el-tab-pane>
      </el-tabs>

      <div v-if="activeTab === 'vehicle'" class="tab-content-section">
        <h3>{{ t('vehicleInfoTab') }}</h3>
        <el-row :gutter="20" class="form-row">
          <el-col :span="12">
            <div class="form-item-static">{{ t('model') }}: <span class="form-control-static">{{ orderDetails.model || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('variant') }}: <span class="form-control-static">{{ orderDetails.variant || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('color') }}: <span class="form-control-static">{{ orderDetails.color || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('salesSubtotal') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.vehiclePrice) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('numberPlatesFee') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.numberPlatesFee) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('vin') }}: <span class="form-control-static">{{ orderDetails.vin || '-' }}</span></div>
          </el-col>
        </el-row>

        <h4>{{ t('accessoriesInformation') }}</h4>
        <el-table :data="orderDetails.accessories" style="width: 100%" border class="mb-10">
          <el-table-column prop="category" :label="t('accessoryCategory')" width="120"></el-table-column>
          <el-table-column prop="name" :label="t('accessoryName')"></el-table-column>
          <el-table-column prop="price" :label="t('unitPrice')" width="100">
            <template #default="scope">
              {{ formatCurrency(scope.row.price) }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" :label="t('quantity')" width="100"></el-table-column>
          <el-table-column prop="total" :label="t('totalPrice')" width="120">
            <template #default="scope">
              {{ formatCurrency(scope.row.total) }}
            </template>
          </el-table-column>
        </el-table>
        <div class="total-amount">{{ t('accessoriesTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalAccessoryAmount) }}</span></div>
      </div>

      <div v-if="activeTab === 'invoice'" class="tab-content-section">
        <h3>{{ t('invoicingInfoTab') }}</h3>
        <el-row :gutter="20" class="form-row">
          <el-col :span="12">
            <div class="form-item-static">{{ t('invoiceTypeLabel') }}: <span class="form-control-static">{{ orderDetails.invoiceType || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('invoiceNameLabel') }}: <span class="form-control-static">{{ orderDetails.invoiceName || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('invoicePhoneLabel') }}: <span class="form-control-static">{{ orderDetails.invoicePhone || '-' }}</span></div>
          </el-col>
          <el-col :span="24">
            <div class="form-item-static">{{ t('invoiceAddressLabel') }}: <span class="form-control-static">{{ orderDetails.invoiceAddress || '-' }}</span></div>
          </el-col>
        </el-row>
      </div>

      <div v-if="activeTab === 'rights'" class="tab-content-section">
        <h3>{{ t('rightsInfo') }}</h3>
        <el-table :data="orderDetails.rights" style="width: 100%" border class="mb-10">
          <el-table-column prop="code" :label="t('rightCode')" width="120"></el-table-column>
          <el-table-column prop="name" :label="t('rightName')"></el-table-column>
          <el-table-column prop="mode" :label="t('rightMode')" width="100"></el-table-column>
          <el-table-column prop="discountPrice" :label="t('discountAmount')" width="120">
            <template #default="scope">
              {{ formatCurrency(scope.row.discountPrice) }}
            </template>
          </el-table-column>
          <el-table-column prop="effectiveDate" :label="t('effectiveDate')" width="120"></el-table-column>
          <el-table-column prop="expiryDate" :label="t('expiryDate')" width="120"></el-table-column>
        </el-table>
        <div class="total-amount">{{ t('rightsDiscountTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalRightsDiscountAmount) }}</span></div>
      </div>

      <div v-if="activeTab === 'payment'" class="tab-content-section">
        <h3>{{ t('paymentInfoTab') }}</h3>
        <el-row :gutter="20" class="form-row">
          <el-col :span="12">
            <div class="form-item-static">{{ t('paymentMethod') }}: <span class="form-control-static">{{ formatPaymentType(orderDetails.paymentMethod) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('loanStatusLabel') }}: <span class="form-control-static">{{ formatLoanStatus(orderDetails.loanApprovalStatus) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('depositAmountLabel') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.depositAmount) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('loanAmountLabel') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.loanAmount) || '-' }}</span></div>
          </el-col>
          <el-col :span="12">
            <div class="form-item-static">{{ t('finalPaymentLabel') }}: <span class="form-control-static">{{ formatCurrency(orderDetails.balanceAmount) || '-' }}</span></div>
          </el-col>
        </el-row>
      </div>

      <div v-if="activeTab === 'insurance'" class="tab-content-section">
        <h3>{{ t('insuranceInfo') }}</h3>
        <el-table :data="orderDetails.policies" style="width: 100%" border class="mb-10">
          <el-table-column prop="policyNumber" :label="t('policyNumber')" width="150"></el-table-column>
          <el-table-column prop="insuranceType" :label="t('insuranceType')" width="100"></el-table-column>
          <el-table-column prop="insuranceCompany" :label="t('insuranceCompany')"></el-table-column>
          <el-table-column prop="effectiveDate" :label="t('effectiveDate')" width="120"></el-table-column>
          <el-table-column prop="expiryDate" :label="t('expiryDate')" width="120"></el-table-column>
          <el-table-column prop="price" :label="t('insurancePrice')" width="100">
            <template #default="scope">
              {{ formatCurrency(scope.row.price) }}
            </template>
          </el-table-column>
        </el-table>
        <el-form-item :label="t('remarksLabel')">
          <el-input type="textarea" v-model="orderDetails.remarks" readonly></el-input>
        </el-form-item>
        <div class="total-amount">{{ t('insuranceTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalInsuranceAmount) }}</span></div>
      </div>

      <div v-if="activeTab === 'otr'" class="tab-content-section">
        <h3>{{ t('otrFeesInfo') }}</h3>
        <el-table :data="orderDetails.otrFees" style="width: 100%" border class="mb-10">
          <el-table-column prop="invoiceNumber" :label="t('ticketNumber')" width="150"></el-table-column>
          <el-table-column prop="item" :label="t('feeItem')"></el-table-column>
          <el-table-column prop="price" :label="t('feePrice')" width="100">
            <template #default="scope">
              {{ formatCurrency(scope.row.price) }}
            </template>
          </el-table-column>
          <el-table-column prop="effectiveDate" :label="t('effectiveDate')" width="120"></el-table-column>
          <el-table-column prop="expiryDate" :label="t('expiryDate')" width="120"></el-table-column>
        </el-table>
        <div class="total-amount">{{ t('otrFeesTotalAmount') }}: <span class="static-value">{{ formatCurrency(orderDetails.totalOtrAmount) }}</span></div>
      </div>
    </el-card>

    <!-- 订单变更记录 - Order Change Records -->
    <el-card class="mb-20 section">
      <h2>{{ t('changeRecords') }}</h2>
      <el-table :data="orderDetails.changeRecords" style="width: 100%" stripe>
        <el-table-column prop="createdAt" :label="t('operationTime')" width="180"></el-table-column>
        <el-table-column prop="createdBy" :label="t('operator')" width="100"></el-table-column>
        <el-table-column prop="originalContent" :label="t('originalContent')"></el-table-column>
        <el-table-column prop="changedContent" :label="t('changedContent')"></el-table-column>
      </el-table>
    </el-card>

    <!-- 底部价格栏 -->
    <div class="footer-bar">
      <div class="price-summary">
        <span>{{ t('totalAmount') }}: <span class="total-price">{{ formatCurrency(orderDetails.totalInvoicePrice) }}</span></span>
        <span>{{ t('remainingReceivable') }}: <span class="remaining-amount">{{ formatCurrency(orderDetails.remainingAmount) }}</span></span>
      </div>
      <div class="buttons">
        <el-button type="primary" @click="goToList">{{ t('returnToList') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  ElButton,
  ElCard,
  ElCol,
  ElFormItem,
  ElInput,
  ElMessage,
  ElRow,
  ElTable,
  ElTableColumn,
  ElTabPane,
  ElTabs,
} from 'element-plus';
import type { SalesOrderDetail, BuyerType, PaymentMethod, LoanApprovalStatus, SalesOrderStatus, SalesOrderApprovalStatus, SalesOrderPaymentStatus, InsuranceStatus, JPJRegistrationStatus } from '@/types/module.d';
import { getSalesOrderDetail } from '@/api/modules/order';

const { t } = useModuleI18n('sales.salesOrderManagement');
const route = useRoute();
const router = useRouter();

const activeTab = ref('vehicle'); // 当前激活的Tab
const loading = ref(false);

// 使用真实的SalesOrderDetail接口
const orderDetails = ref<SalesOrderDetail>({
  // 订单基本信息
  id: '',
  orderNumber: '',
  createTime: '',

  // 客户信息
  ordererName: '',
  ordererPhone: '',
  buyerName: '',
  buyerPhone: '',
  buyerIdType: '',
  buyerIdNumber: '',
  buyerEmail: '',
  buyerAddress: '',
  buyerState: '',
  buyerCity: '',
  buyerPostcode: '',
  buyerType: 'individual' as BuyerType,

  // 门店信息
  storeRegion: '',
  storeCity: '',
  storeName: '',
  salesConsultantName: '',

  // 车辆信息
  model: '',
  variant: '',
  color: '',
  salesSubtotal: 0,
  numberPlatesFee: 0,
  vin: '',

  // 选配件信息
  accessories: [],
  accessoriesTotalAmount: 0,

  // 开票信息
  invoicingType: '',
  invoicingName: '',
  invoicingPhone: '',
  invoicingAddress: '',

  // 权益信息
  rights: [],
  rightsDiscountAmount: 0,

  // 支付信息
  paymentMethod: 'full_payment' as PaymentMethod,
  depositAmount: 0,
  balanceAmount: 0,

  // 保险信息
  insuranceList: [],
  insuranceTotalAmount: 0,

  // OTR费用信息
  otrFees: [],
  otrFeesTotalAmount: 0,

  // 订单变更记录
  changeRecords: [],

  // 状态信息
  orderStatus: 'submitted',
  approvalStatus: 'pending_approval',
  paymentStatus: 'pending_deposit',
  insuranceStatus: 'not_insured',
  jpjRegistrationStatus: 'pending_registration',

  // 计算字段
  totalInvoiceAmount: 0,
  remainingAmount: 0
});

// 格式化方法
const formatCustomerType = (type: BuyerType) => {
  // return t(`buyerTypes.${type}`, type);
  return type;
};

const formatPaymentType = (type: PaymentMethod) => {
  // return t(`paymentMethods.${type}`, type);
  return type;
};

const formatLoanStatus = (status?: LoanApprovalStatus) => {
  if (!status) return '-';
  // return t(`loanApprovalStatuses.${status}`, status);
  return status;
};

const formatOrderStatus = (status: SalesOrderStatus) => {
  // return t(`orderStatuses.${status}`, status);
  return status;
};

const formatApprovalStatus = (status: SalesOrderApprovalStatus) => {
  // return t(`approvalStatuses.${status}`, status);
  return status;
};

const formatPaymentStatus = (status: SalesOrderPaymentStatus) => {
  // return t(`paymentStatuses.${status}`, status);
  return status;
};

const formatInsuranceStatus = (status: InsuranceStatus) => {
  // return t(`insuranceStatuses.${status}`, status);
  return status;
};

const formatJpjRegistrationStatus = (status: JPJRegistrationStatus) => {
  // return t(`jpjRegistrationStatuses.${status}`, status);
  return status;
};

const formatCurrency = (amount?: number) => {
  if (amount === undefined || amount === null) return '-';
  return `RM ${amount.toLocaleString('en-MY', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

const goToList = () => {
  router.push('/sales-order');
};

onMounted(() => {
  const orderNo = route.params.orderNo as string;
  if (orderNo) {
    // 使用API接口获取订单详情
    fetchOrderDetail(orderNo);
  } else {
    ElMessage.error(t('missingOrderNumber'));
    router.push('/sales-order');
  }
});

// 获取订单详情的方法
const fetchOrderDetail = async (orderNo: string) => {
  loading.value = true;
  try {
    // 调用真实的API接口
    const data = await getSalesOrderDetail(orderNo);

    // 直接使用API返回的数据，数据结构已经匹配SalesOrderDetail接口
    orderDetails.value = data;
  } catch (error) {
    console.error('获取订单详情失败:', error);
    ElMessage.error(t('fetchDetailError'));
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.page-title {
  font-size: 28px;
  margin-bottom: 25px;
  text-align: center;
  color: #303133;
}

.mb-20 {
  margin-bottom: 20px;
}

.order-summary-header {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.order-summary-header strong {
  font-weight: bold;
  color: #333;
  margin-right: 20px;
}

.section h2 {
  font-size: 22px;
  color: #303133;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.form-row .form-item-static {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.form-row .form-control-static {
  font-weight: bold;
  color: #333;
}

.custom-tabs .el-tabs__item {
  font-size: 16px;
}

.tab-content-section {
  padding: 20px 0;
}

.tab-content-section h3 {
  font-size: 18px;
  color: #606266;
  margin-top: 20px;
  margin-bottom: 15px;
}

.tab-content-section h4 {
  font-size: 16px;
  color: #606266;
  margin-top: 15px;
  margin-bottom: 10px;
}

.total-amount {
  text-align: right;
  margin-top: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.footer-bar {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 15px 20px;
  border-top: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 1000;
}

.price-summary span {
  font-size: 18px;
  margin-right: 30px;
  color: #303133;
}

.total-price {
  color: #409EFF;
  font-weight: bold;
}

.remaining-amount {
  color: #F56C6C;
  font-weight: bold;
}

.buttons .el-button {
  margin-left: 10px;
}
</style>
