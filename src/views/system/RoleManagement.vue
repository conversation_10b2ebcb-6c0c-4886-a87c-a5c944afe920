<template>
  <div class="role-management-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('role.title') }}</span>
          <el-button
            v-permission="'system:role:create'"
            type="primary"
            @click="handleAddRole"
          >
            {{ t('role.addRole') }}
          </el-button>
        </div>
      </template>

      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
        <el-form-item :label="t('role.roleName')" prop="roleName">
          <el-input v-model="queryParams.roleName" :placeholder="t('role.enterRoleName')" clearable />
        </el-form-item>

        <el-form-item :label="t('role.roleSource')" prop="roleSource">
          <el-select v-model="queryParams.roleSource" :placeholder="t('role.selectRoleSource')" clearable style="width: 200px">
            <el-option :label="t('role.roleSourceFactory')" value="factory" />
            <el-option :label="t('role.roleSourceStore')" value="store" />
          </el-select>
        </el-form-item>

        <el-form-item :label="t('role.roleStatus')" prop="roleStatus">
          <el-select v-model="queryParams.roleStatus" :placeholder="t('role.selectRoleStatus')" clearable style="width: 200px">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="t('user.belongStore')" prop="storeId" v-if="queryParams.roleSource === 'store'">
          <el-select v-model="queryParams.storeId" :placeholder="t('user.selectBelongStore')" clearable style="width: 200px">
            <el-option
              v-for="store in storeOptions"
              :key="store.id"
              :label="store.storeName"
              :value="store.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">{{ tc('search') }}</el-button>
          <el-button @click="resetQuery">{{ tc('reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 角色列表 -->
      <el-table :data="roleList" style="width: 100%" v-loading="loading">
        <el-table-column prop="roleName" :label="t('role.roleName')" width="180" />
        <el-table-column prop="roleCode" :label="t('role.roleCode')" width="180" />
        <el-table-column :label="t('role.roleSource')" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.roleSource === 'factory'" type="primary">{{ t('role.roleSourceFactory') }}</el-tag>
            <el-tag v-else-if="scope.row.roleSource === 'store'" type="info">{{ t('role.roleSourceStore') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="belongStoreName" :label="t('user.belongStore')" width="150" />
        <el-table-column :label="t('role.dataPermissions')" width="150">
          <template #default="scope">
            {{ getDataScopeText(scope.row.roleScope) }}
          </template>
        </el-table-column>
        <el-table-column :label="t('role.roleStatus')" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.roleStatus === 'normal' ? 'success' : 'info'">
              {{ scope.row.roleStatus === 'normal' ? t('role.roleStatusNormal') : t('role.roleStatusDisabled') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="t('role.description')" />
        <el-table-column :label="tc('operations')" width="350" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="handleViewRole(scope.row)"
            >
              {{ tc('view') }}
            </el-button>
            <el-button
              v-permission="'system:role:update'"
              type="primary"
              link
              @click="handleEditRole(scope.row)"
            >
              {{ tc('edit') }}
            </el-button>
            <el-button
              v-permission="'system:role:menu'"
              type="primary"
              link
              @click="handleRolePermission(scope.row)"
            >
              {{ t('role.configMenuPermission') }}
            </el-button>
            <el-button
              v-permission="'system:role:data'"
              type="primary"
              link
              @click="handleDataPermission(scope.row)"
            >
              {{ t('role.configDataPermission') }}
            </el-button>
            <el-button
              v-permission="'system:role:delete'"
              type="danger"
              link
              @click="handleDeleteRole(scope.row)"
              :disabled="scope.row.roleSource === 'factory'"
            >
              {{ tc('delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="queryParams.current"
        v-model:page-size="queryParams.size"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 角色新增/编辑/查看对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      append-to-body
      destroy-on-close
    >
      <el-form :model="roleForm" :rules="rules" ref="roleFormRef" label-width="100px">
        <el-form-item :label="t('role.roleName')" prop="roleName">
          <el-input v-model="roleForm.roleName" :placeholder="t('role.enterRoleName')" :disabled="isView" />
        </el-form-item>
        <el-form-item :label="t('role.roleCode')" prop="roleCode">
          <el-input v-model="roleForm.roleCode" :placeholder="t('role.enterRoleCode')" :disabled="roleForm.id !== undefined || isView" />
        </el-form-item>
        <el-form-item :label="t('role.roleSource')" prop="roleSource">
          <el-select v-model="roleForm.roleSource" :placeholder="t('role.selectRoleSource')" style="width: 100%" :disabled="isView">
            <el-option :label="t('role.roleSourceFactory')" value="factory" :disabled="!isFactoryUser" />
            <el-option :label="t('role.roleSourceStore')" value="store" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('user.belongStore')" prop="storeId" v-if="roleForm.roleSource === 'store'">
          <el-select v-model="roleForm.storeId" :placeholder="t('user.selectBelongStore')" style="width: 100%" :disabled="isView">
            <el-option
              v-for="store in storeOptions"
              :key="store.id"
              :label="store.storeName"
              :value="store.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('role.dataPermissions')" prop="roleScope">
          <el-select v-model="roleForm.roleScope" :placeholder="t('role.selectRoleScope')" style="width: 100%" :disabled="isView">
            <el-option :label="t('role.roleScopeAll')" value="all" :disabled="!isAdmin" />
            <el-option :label="t('role.roleScopeCustom')" value="custom" />
            <el-option :label="t('role.roleScopeDepartment')" value="department" />
            <el-option :label="t('role.roleScopeDepartmentAndBelow')" value="departmentAndBelow" />
            <el-option :label="t('role.roleScopeOnlyPersonal')" value="onlyPersonal" />
          </el-select>
        </el-form-item>
        <!-- 仅在自定义数据权限时显示部门树 -->
        <el-form-item :label="t('department.title')" v-if="roleForm.roleScope === 'custom'">
          <div class="tree-container">
            <el-tree
              ref="roleFormDeptTreeRef"
              :data="deptTree"
              show-checkbox
              node-key="id"
              :props="{ label: 'departmentName', children: 'children' }"
              :default-checked-keys="roleForm.deptIds"
              :disabled="isView"
            />
          </div>
        </el-form-item>
        <el-form-item :label="t('role.roleStatus')" prop="roleStatus">
          <el-select v-model="roleForm.roleStatus" :placeholder="t('role.selectRoleStatus')" style="width: 100%" :disabled="isView">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('role.description')" prop="description">
          <el-input v-model="roleForm.description" type="textarea" :placeholder="t('role.enterDescription')" :disabled="isView" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button v-if="!isView" type="primary" @click="submitForm">{{ tc('confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限设置对话框 -->
    <el-dialog
      :title="t('role.configMenuPermission')"
      v-model="permissionDialogVisible"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form label-width="100px">
        <el-form-item :label="t('role.roleName')">
          <span>{{ currentRole?.roleName }}</span>
        </el-form-item>
        <el-form-item :label="t('role.menuPermissions')">
          <div class="tree-container">
            <el-tree
              ref="menuTreeRef"
              :data="menuTree"
              show-checkbox
              node-key="id"
              :props="{ label: 'menuName', children: 'children' }"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="submitPermission">{{ tc('confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据权限设置对话框 -->
    <el-dialog
      :title="t('role.configDataPermission')"
      v-model="dataPermissionDialogVisible"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <el-form :model="dataPermissionForm" label-width="100px">
        <el-form-item :label="t('role.roleName')">
          <span>{{ currentRole?.roleName }}</span>
        </el-form-item>
        <el-form-item :label="t('role.roleScope')" prop="dataScope">
          <el-select v-model="dataPermissionForm.dataScope" :placeholder="t('role.selectRoleScope')" style="width: 100%">
            <el-option :label="t('role.roleScopeAll')" value="all" :disabled="!isAdmin" />
            <el-option :label="t('role.roleScopeCustom')" value="custom" />
            <el-option :label="t('role.roleScopeDepartment')" value="department" />
            <el-option :label="t('role.roleScopeDepartmentAndBelow')" value="departmentAndBelow" />
            <el-option :label="t('role.roleScopeOnlyPersonal')" value="onlyPersonal" />
          </el-select>
        </el-form-item>
        <el-form-item :label="t('department.title')" prop="deptIds" v-if="dataPermissionForm.dataScope === 'custom'">
          <div class="tree-container">
            <el-tree
              ref="deptTreeRef"
              :data="deptTree"
              show-checkbox
              node-key="id"
              :props="{ label: 'departmentName', children: 'children' }"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dataPermissionDialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="submitDataPermission">{{ tc('confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  addRole,
  configureRoleDataPermission,
  configureRoleMenu,
  deleteRole,
  getDepartmentTree,
  getDictionary,
  getMenuTree,
  getRoleDataPermission,
  getRoleDetail,
  getRoleList,
  getRoleMenuPermission,
  getStoreList,
  updateRole
} from '@/api/modules/permission'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { CreateRoleRequest, Department, Menu, Role, RoleQueryParams, SelectOption, Store, UpdateRoleRequest } from '@/types/permission'
import { ElForm, ElMessage, ElMessageBox, ElTree } from 'element-plus'
import { onMounted, reactive, ref, nextTick, watch } from 'vue'

// --- i18n ---
const { t, tc } = useModuleI18n('base')

// --- State and Refs ---
const loading = ref(false)
const roleList = ref<Role[]>([])
const storeOptions = ref<Store[]>([])
const menuTree = ref<Menu[]>([])
const deptTree = ref<Department[]>([])
const statusOptions = ref<SelectOption[]>([])
const total = ref(0)
const queryForm = ref<InstanceType<typeof ElForm>>()
const roleFormRef = ref<InstanceType<typeof ElForm>>()
const menuTreeRef = ref<InstanceType<typeof ElTree>>()
const deptTreeRef = ref<InstanceType<typeof ElTree>>()
const roleFormDeptTreeRef = ref<InstanceType<typeof ElTree>>()

// 查询参数
const queryParams = reactive<RoleQueryParams>({
  current: 1,
  size: 10,
  roleName: '',
  roleSource: undefined,
  roleStatus: undefined,
  storeId: ''
})

const dialogVisible = ref(false)
const dialogTitle = ref('')
const isView = ref(false)

// 角色表单数据
const roleForm = reactive<Partial<CreateRoleRequest & { id?: string, deptIds?: string[] }>>({
  roleName: '',
  roleCode: '',
  roleSource: 'store',
  storeId: '',
  roleScope: 'onlyPersonal',
  roleStatus: 'normal',
  description: '',
  deptIds: []
})

const permissionDialogVisible = ref(false)
const permissionForm = reactive<{ menuIds: string[] }>({
  menuIds: []
})

const dataPermissionDialogVisible = ref(false)
const dataPermissionForm = reactive<{dataScope: Role['roleScope'], deptIds: string[]}>({
  dataScope: 'onlyPersonal',
  deptIds: []
})

const currentRole = ref<Role | null>(null)
const isAdmin = ref(true)
const isFactoryUser = ref(true)

const rules = {
  roleName: [{ required: true, message: t('role.roleNameRequired'), trigger: 'blur' }],
  roleCode: [{ required: true, message: t('role.roleCodeRequired'), trigger: 'blur' }],
  roleSource: [{ required: true, message: t('role.roleSourceRequired'), trigger: 'change' }],
  roleStatus: [{ required: true, message: t('role.roleStatusRequired'), trigger: 'change' }],
  storeId: [{ required: false, message: t('user.selectBelongStore'), trigger: 'change' }]
}

// 计算属性 - 移除未使用的变量

// --- Text Mappers ---
const getDataScopeText = (scope: Role['roleScope']) => {
  const map = {
    all: t('role.roleScopeAll'),
    custom: t('role.roleScopeCustom'),
    department: t('role.roleScopeDepartment'),
    departmentAndBelow: t('role.roleScopeDepartmentAndBelow'),
    onlyPersonal: t('role.roleScopeOnlyPersonal'),
  };
  return map[scope] || '';
}

// --- Lifecycle ---
onMounted(() => {
  fetchRoleList()
  fetchStoreOptions()
  fetchDeptTree()
  loadStatusOptions()
})

// 监听数据权限范围变化，重置部门树选中状态
watch(() => dataPermissionForm.dataScope, (newScope) => {
  if (newScope !== 'custom' && deptTreeRef.value) {
    deptTreeRef.value.setCheckedKeys([])
  }
})

// --- Methods ---
const fetchRoleList = async () => {
  loading.value = true
  try {
    const response = await getRoleList(queryParams)
    if (response.code == 200) {
      roleList.value = response.result.records
      total.value = response.result.total
      // 使用真实的分页数据
      queryParams.current = response.result.current
      queryParams.size = response.result.size
    }
  } catch (err) {
    console.error(err)
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

const fetchStoreOptions = async () => {
  try {
    const response = await getStoreList({ current: 1, size: 10 })
    if (response.code == 200) {
      storeOptions.value = response.result.records
    }
  } catch (err) {
    console.error(err)
    ElMessage.error(tc('loadFailed'))
  }
}

const fetchDeptTree = async () => {
  try {
    const res = await getDepartmentTree({});
    if (res.code == 200) {
      deptTree.value = res.result;
    }
  } catch (err) {
    console.error(err)
    ElMessage.error(tc('loadFailed'));
  }
}

const loadStatusOptions = async () => {
  try {
    const response = await getDictionary('1004');
    if (response.code == 200) {
      statusOptions.value = response.result;
    }
  } catch (error) {
    console.error('加载状态选项失败:', error);
  }
}

const handleQuery = () => {
  queryParams.current = 1
  fetchRoleList()
}

const resetQuery = () => {
  queryForm.value?.resetFields()
  handleQuery()
}

const handleAddRole = () => {
  Object.assign(roleForm, {
    id: undefined,
    roleName: '',
    roleCode: '',
    roleSource: 'store',
    storeId: '',
    roleScope: 'onlyPersonal',
    roleStatus: 'normal',
    description: '',
    deptIds: []
  })
  isView.value = false
  dialogTitle.value = t('role.addRole')
  dialogVisible.value = true
}

const handleViewRole = async (role: Role) => {
  try {
    loading.value = true
    const response = await getRoleDetail(role.id)
    if (response.code == 200) {
      const roleData = { ...response.result, deptIds: response.result.deptIds || [] };
      Object.assign(roleForm, roleData);
      isView.value = true
      dialogTitle.value = tc('view')
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取角色详情失败:', error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false
  }
}

const handleEditRole = async (role: Role) => {
  try {
    loading.value = true
    const response = await getRoleDetail(role.id)
    if (response.code == 200) {
      const roleData = { ...response.result, deptIds: response.result.deptIds || [] };
      Object.assign(roleForm, roleData);
      isView.value = false
      dialogTitle.value = t('role.editRole')
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取角色详情失败:', error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false
  }
}

const handleDeleteRole = async (role: Role) => {
  try {
    await ElMessageBox.confirm(tc('confirmDelete', { item: role.roleName }), tc('warning'), {
      type: 'warning',
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
    })
    const response = await deleteRole(role.id)
    if (response.code == 200) {
      ElMessage.success(tc('deleteSuccess'))
      fetchRoleList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(tc('deleteFailed'))
    }
  }
}

const submitForm = async () => {
  if (!roleFormRef.value) return;
  await roleFormRef.value.validate()
  try {
    loading.value = true
    const dataToSend = { ...roleForm };
    if (dataToSend.roleScope === 'custom') {
      dataToSend.deptIds = roleFormDeptTreeRef.value?.getCheckedKeys(false) as string[] || [];
    } else {
      dataToSend.deptIds = [];
    }

    let response;
    if (dataToSend.id) {
      response = await updateRole(dataToSend as UpdateRoleRequest)
    } else {
      response = await addRole(dataToSend as CreateRoleRequest)
    }

    if (response.code == 200) {
      ElMessage.success(tc(dataToSend.id ? 'updateSuccess' : 'createSuccess'))
      dialogVisible.value = false
      fetchRoleList()
    }
  } catch (err) {
    console.error(err)
    ElMessage.error(tc('operateFailed'))
  } finally {
    loading.value = false
  }
}

// --- Permission Dialog ---
const handleRolePermission = async (role: Role) => {
  currentRole.value = role
  try {
    loading.value = true
    // 根据角色类型加载不同的菜单
    const menuTreeRes = await getMenuTree({ menuSide: role.roleSource === 'factory' ? 'factory' : 'dealer' })
    if (menuTreeRes.code == 200) {
      menuTree.value = menuTreeRes.result

      // 获取角色已配置的菜单权限
      const permissionRes = await getRoleMenuPermission(role.id)
      if (permissionRes.code == 200) {
        permissionForm.menuIds = permissionRes.result.menuIds
      } else {
        permissionForm.menuIds = []
      }

      permissionDialogVisible.value = true

      // 等待对话框和树组件渲染完成后设置选中状态
      await nextTick()
      if (menuTreeRef.value) {
        // 每次都重置选中状态，避免保留之前的状态
        menuTreeRef.value.setCheckedKeys(permissionForm.menuIds)
      }
    }
  } catch (err) {
    console.error(err)
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

const submitPermission = async () => {
  try {
    loading.value = true
    const checkedKeys = menuTreeRef.value?.getCheckedKeys() as string[]
    const halfCheckedKeys = menuTreeRef.value?.getHalfCheckedKeys() as string[]
    const menuIds = [...(checkedKeys || []), ...(halfCheckedKeys || [])]

    const response = await configureRoleMenu({
      roleId: currentRole.value!.id,
      menuIds
    })
    if (response.code == 200) {
      ElMessage.success(tc('operationSuccessful'))
      permissionDialogVisible.value = false
      fetchRoleList()
    }
  } catch (err) {
    console.error(err)
    ElMessage.error(tc('updateFailed'))
  } finally {
    loading.value = false
  }
}

// --- Data Permission Dialog ---
const handleDataPermission = async (role: Role) => {
  currentRole.value = role
  try {
    loading.value = true
    // 获取角色已配置的数据权限
    const permissionRes = await getRoleDataPermission(role.id)
    if (permissionRes.code == 200) {
      dataPermissionForm.dataScope = permissionRes.result.dataScope as Role['roleScope']
      dataPermissionForm.deptIds = permissionRes.result.deptIds
    } else {
      dataPermissionForm.dataScope = role.roleScope
      dataPermissionForm.deptIds = role.deptIds || []
    }
    dataPermissionDialogVisible.value = true

    // 等待对话框和树组件渲染完成后设置选中状态
    await nextTick()
    if (deptTreeRef.value && dataPermissionForm.deptIds.length > 0 && dataPermissionForm.dataScope === 'custom') {
      deptTreeRef.value.setCheckedKeys(dataPermissionForm.deptIds)
    }
  } catch (err) {
    console.error(err)
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

const submitDataPermission = async () => {
  try {
    loading.value = true
    const deptIds = dataPermissionForm.dataScope == 'custom'
      ? (deptTreeRef.value?.getCheckedKeys(false) as string[] || [])
      : []

    const response = await configureRoleDataPermission({
      roleId: currentRole.value!.id,
      dataScope: dataPermissionForm.dataScope,
      deptIds
    })

    if (response.code == 200) {
      ElMessage.success(tc('operationSuccessful'))
      dataPermissionDialogVisible.value = false
      fetchRoleList()
    }
  } catch (err) {
    console.error(err)
    ElMessage.error(tc('updateFailed'))
  } finally {
    loading.value = false
  }
}

// --- Pagination ---
const handleSizeChange = (size: number) => {
  queryParams.size = size
  fetchRoleList()
}

const handleCurrentChange = (page: number) => {
  queryParams.current = page
  fetchRoleList()
}

// --- Permissions ---
const hasPermission = (permission: string) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _ = permission
  return true
}
</script>

<style scoped>
.role-management-container {
  padding: 20px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search-form .el-form-item {
  margin-bottom: 20px;
}
.dialog-footer {
  text-align: right;
}
.tree-container {
  width: 100%;
  border: 1px solid var(--el-border-color);
  border-radius: var(--el-border-radius-base);
  padding: 5px;
  max-height: 250px;
  overflow-y: auto;
}
</style>
