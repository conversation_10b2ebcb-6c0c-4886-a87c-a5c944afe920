<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('menu.title') }}</h1>

    <!-- 搜索表单 -->
    <el-card class="search-card mb-20" shadow="never">
      <el-form :model="searchParams" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('menu.menuName')">
              <el-input
                v-model="searchParams.menuName"
                :placeholder="t('menu.enterMenuName')"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('menu.menuStatus')">
              <el-select
                v-model="searchParams.menuStatus"
                :placeholder="t('menu.selectMenuStatus')"
                clearable
                style="width: 100%"
                @change="handleSearch"
              >
                <el-option :label="tc('all')" value="" />
                <el-option
                  v-for="option in menuStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('menu.menuSide')">
              <el-select
                v-model="searchParams.menuSide"
                :placeholder="t('menu.selectMenuSide')"
                clearable
                style="width: 100%"
                @change="handleSearch"
              >
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('menu.menuSideFactory')" value="factory" />
                <el-option :label="t('menu.menuSideDealer')" value="dealer" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right;">
             <el-form-item label="&nbsp;">
                <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
                <el-button :icon="RefreshLeft" @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作栏和表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-toolbar mb-20">
        <el-button v-permission="'system:menu:create'" type="primary" :icon="Plus" @click="handleAdd()">{{ t('menu.addMenu') }}</el-button>
        <el-button :icon="Expand" @click="expandAll">{{ t('store.expandAll') }}</el-button>
        <el-button :icon="Fold" @click="collapseAll">{{ t('store.collapseAll') }}</el-button>
      </div>

      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll"
        stripe
        border
      >
        <el-table-column prop="menuName" :label="t('menu.menuName')" min-width="200" show-overflow-tooltip />
        <el-table-column prop="menuIcon" :label="t('menu.menuIcon')" width="80" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.menuIcon">
              <component :is="row.menuIcon" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="menuSide" :label="t('menu.menuSide')" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.menuSide === 'factory' ? 'primary' : 'success'">
              {{ getMenuSideText(row.menuSide) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="menuType" :label="t('menu.menuType')" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getMenuTypeTagType(row.menuType)">
              {{ getMenuTypeText(row.menuType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="permission" :label="t('menu.permission')" min-width="200" show-overflow-tooltip />
        <el-table-column prop="component" :label="t('menu.component')" min-width="200" show-overflow-tooltip/>
        <el-table-column prop="menuStatus" :label="t('menu.menuStatus')" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.menuStatus === 'normal' ? 'success' : 'danger'">
              {{ getStatusText(row.menuStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isVisible" :label="t('menu.isVisible')" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isVisible === 1 ? 'success' : 'info'">
              {{ row.isVisible === 1 ? tc('yes') : tc('no') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="tc('operations')" fixed="right" width="280" align="center">
          <template #default="{ row }">
             <el-button
              v-permission="'system:menu:create'"
              type="primary"
              link
              :icon="Plus"
              @click="handleAdd(row)"
              v-if="row.menuType !== 'button'"
            >{{ tc('add') }}</el-button>
            <el-button type="info" link :icon="View" @click="handleView(row)">{{ tc('view') }}</el-button>
            <el-button v-permission="'system:menu:update'" type="primary" link :icon="Edit" @click="handleEdit(row)">{{ tc('edit') }}</el-button>
            <el-button v-permission="'system:menu:delete'" type="danger" link :icon="Delete" @click="handleDelete(row)">{{ tc('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-if="pagination.total > 0"
        class="mt-20"
        :current-page="pagination.current"
        :page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 新增/编辑/查看弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-position="top" class="dialog-form-modern">
        <el-form-item :label="t('menu.parentMenu')">
           <el-tree-select
            v-model="formData.parentId"
            :data="menuTreeOptions"
            :props="{ label: 'menuName', value: 'id' }"
            :placeholder="t('menu.selectParentMenu')"
            check-strictly
            clearable
            style="width: 100%"
            :disabled="isView"
          />
        </el-form-item>
        <el-form-item :label="t('menu.menuSide')" prop="menuSide">
          <el-radio-group v-model="formData.menuSide" :disabled="isView">
            <el-radio value="factory">{{ t('menu.menuSideFactory') }}</el-radio>
            <el-radio value="dealer">{{ t('menu.menuSideDealer') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('menu.menuType')" prop="menuType">
          <el-radio-group v-model="formData.menuType" :disabled="isView">
            <el-radio value="directory">{{ t('menu.menuTypeDirectory') }}</el-radio>
            <el-radio value="menu">{{ t('menu.menuTypeMenu') }}</el-radio>
            <el-radio value="button">{{ t('menu.menuTypeButton') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('menu.menuName')" prop="menuName">
          <el-input v-model="formData.menuName" :placeholder="t('menu.enterMenuName')" :disabled="isView" />
        </el-form-item>

        <template v-if="formData.menuType !== 'button'">
            <el-form-item :label="t('menu.menuIcon')" prop="menuIcon">
              <el-input v-model="formData.menuIcon" :placeholder="t('menu.selectMenuIcon')" :disabled="isView" />
            </el-form-item>
            <el-form-item :label="t('menu.menuPath')" prop="menuPath">
              <el-input v-model="formData.menuPath" :placeholder="t('menu.enterMenuPath')" :disabled="isView" />
            </el-form-item>
             <el-form-item :label="t('menu.component')" prop="component" v-if="formData.menuType === 'menu'">
              <el-input v-model="formData.component" :placeholder="t('menu.enterComponent')" :disabled="isView" />
            </el-form-item>
        </template>

        <el-form-item :label="t('menu.permission')" prop="permission">
          <el-input v-model="formData.permission" :placeholder="t('menu.enterPermission')" :disabled="isView" />
        </el-form-item>
        <el-form-item :label="t('menu.menuStatus')" prop="menuStatus">
          <el-radio-group v-model="formData.menuStatus" :disabled="isView">
            <el-radio value="normal">{{ t('menu.menuStatusNormal') }}</el-radio>
            <el-radio value="disabled">{{ t('menu.menuStatusDisabled') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('menu.isVisible')" prop="isVisible">
          <el-radio-group v-model="formData.isVisible">
            <el-radio :label="1" :disabled="isView">{{ tc('yes') }}</el-radio>
            <el-radio :label="0" :disabled="isView">{{ tc('no') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="dialogVisible = false">{{ isView ? tc('close') : tc('cancel') }}</el-button>
          <el-button v-if="!isView" type="primary" @click="handleSubmit">{{ tc('confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox, ElForm, ElTable } from 'element-plus';
import { Search, RefreshLeft, Plus, Edit, Delete, Expand, Fold, View } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { Menu, MenuQueryParams, CreateMenuRequest, UpdateMenuRequest, MenuSide, EntityStatus, MenuType, SelectOption } from '@/types/permission';
import { getMenuList, getMenuTree, addMenu, updateMenu, deleteMenu, getMenuDetail, getDictionary } from '@/api/modules/permission';

const { t, tc } = useModuleI18n('base');

const loading = ref(false);
const dialogVisible = ref(false);
const isEdit = ref(false);
const isView = ref(false);
const isExpandAll = ref(false);
const tableRef = ref<InstanceType<typeof ElTable>>();
const formRef = ref<InstanceType<typeof ElForm>>();
const currentId = ref<string | null>(null);

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

const searchParams = reactive({
  menuName: '',
  menuStatus: '',
  menuSide: ''
});

const tableData = ref<Menu[]>([]);

interface MenuTreeOption {
  id: string;
  menuName: string;
  children?: MenuTreeOption[];
}

const menuTreeOptions = ref<MenuTreeOption[]>([]);
const menuStatusOptions = ref<SelectOption[]>([]);

interface MenuFormData {
  parentId: string | null;
  menuSide: MenuSide;
  menuType: MenuType;
  menuName: string;
  menuIcon?: string;
  menuPath?: string;
  component?: string;
  permission?: string;
  menuStatus: EntityStatus;
  isVisible: number;
}

const getInitialFormData = (): MenuFormData => ({
  parentId: null,
  menuSide: 'factory',
  menuType: 'directory',
  menuName: '',
  menuIcon: '',
  menuPath: '',
  component: '',
  permission: '',
  menuStatus: 'normal',
  isVisible: 1,
});

const formData = reactive<MenuFormData>(getInitialFormData());

const dialogTitle = computed(() => {
  if (isView.value) return tc('view') + t('menu.title');
  return isEdit.value ? t('menu.editMenu') : t('menu.addMenu');
});

const formRules = computed(() => ({
  menuName: [{ required: true, message: t('menu.menuNameRequired'), trigger: 'blur' }],
  menuType: [{ required: true, message: t('menu.menuTypeRequired'), trigger: 'change' }],
  menuSide: [{ required: true, message: t('menu.menuSideRequired'), trigger: 'change' }]
}));

const getStatusText = (status: EntityStatus | string) => {
  const map: Record<string, string> = { normal: t('menu.menuStatusNormal'), disabled: t('menu.menuStatusDisabled') };
  return map[status] || status;
};

const getMenuSideText = (side: MenuSide | string) => {
  const map: Record<string, string> = { factory: t('menu.menuSideFactory'), dealer: t('menu.menuSideDealer') };
  return map[side] || side;
}

const getMenuTypeText = (type: string) => {
  const map: Record<string, string> = { directory: t('menu.menuTypeDirectory'), menu: t('menu.menuTypeMenu'), button: t('menu.menuTypeButton') };
  return map[type] || type;
};

const getMenuTypeTagType = (type: string) => {
  const map: Record<string, string> = { directory: 'primary', menu: 'success', button: 'info' };
  return map[type] || 'default';
};

const initData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const queryParams: MenuQueryParams = {
      current: pagination.current,
      size: pagination.size,
      menuName: searchParams.menuName || undefined,
      menuStatus: searchParams.menuStatus ? (searchParams.menuStatus as EntityStatus) : undefined,
      menuSide: searchParams.menuSide ? (searchParams.menuSide as MenuSide) : undefined
    };

    const response = await getMenuList(queryParams);
    if (response.code == 200) {
      const allMenus = response.result.records;

      // 检查后端是否已经返回了树形结构数据
      const hasTreeStructure = allMenus.some(menu => menu.children !== undefined);

      if (hasTreeStructure) {
        // 后端已经返回树形结构，直接使用
        tableData.value = allMenus;
      } else {
        // 后端返回平铺数据，需要前端构建树形结构
        tableData.value = allMenus;
      }
      // 使用真实的分页数据
      queryParams.current = response.result.current
      queryParams.size = response.result.size
      pagination.total = response.result.total;
    } else {
      ElMessage.error(response.message || tc('loadFailed'));
    }
  } catch (error) {
    console.error("Failed to fetch menu list:", error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false;
  }
};

const loadMenuOptions = async () => {
  try {
    const response = await getMenuTree();
    // The root node for selection
    menuTreeOptions.value = [{ id: '0', menuName: t('menu.title'), children: response.result }];
  } catch(e) {
    console.error("Failed to load menu options:", e);
    menuTreeOptions.value = [];
  }
}

const handleSearch = () => {
  pagination.current = 1;
  initData();
};

const resetSearch = () => {
  searchParams.menuName = '';
  searchParams.menuStatus = '';
  searchParams.menuSide = '';
  pagination.current = 1;
  initData();
};

const handleAdd = (row?: Menu) => {
  isEdit.value = false;
  isView.value = false;
  currentId.value = null;
  const initialData = getInitialFormData();
  if (row) {
    initialData.parentId = row.id;
  }
  Object.assign(formData, initialData);
  dialogVisible.value = true;
  loadMenuOptions();
};

const handleEdit = (row: Menu) => {
  isEdit.value = true;
  isView.value = false;
  currentId.value = row.id;

  const rowData: MenuFormData = {
      parentId: row.parentId || null,
      menuSide: row.menuSide,
      menuType: row.menuType,
      menuName: row.menuName,
      menuIcon: row.menuIcon,
      menuPath: row.menuPath,
      component: row.component,
      permission: row.permission,
      menuStatus: row.menuStatus,
      isVisible: typeof row.isVisible === 'boolean' ? (row.isVisible ? 1 : 0) : row.isVisible,
  }
  Object.assign(formData, rowData);
  dialogVisible.value = true;
  loadMenuOptions();
};

const handleView = async (row: Menu) => {
  try {
    loading.value = true;
    const response = await getMenuDetail(row.id);

    if (response.code == 200) {
      isEdit.value = false;
      isView.value = true;
      currentId.value = row.id;

      const rowData: MenuFormData = {
        parentId: response.result.parentId || null,
        menuSide: response.result.menuSide,
        menuType: response.result.menuType,
        menuName: response.result.menuName,
        menuIcon: response.result.menuIcon,
        menuPath: response.result.menuPath,
        component: response.result.component,
        permission: response.result.permission,
        menuStatus: response.result.menuStatus,
        isVisible: typeof response.result.isVisible === 'boolean' ? (response.result.isVisible ? 1 : 0) : response.result.isVisible,
      }
      Object.assign(formData, rowData);
      dialogVisible.value = true;
      loadMenuOptions();
    } else {
      ElMessage.error(response.message || tc('loadFailed'));
    }
  } catch (error) {
    console.error('获取菜单详情失败:', error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false;
  }
};

const handleDelete = async (row: Menu) => {
  try {
      // **已修正**：使用 i18n 的插值功能
      await ElMessageBox.confirm(
      tc('confirmDelete', { item: row.menuName }), // 正确传入要删除的项
      tc('warning'),
      {
        confirmButtonText: tc('confirm'), // 添加中文按钮文字
        cancelButtonText: tc('cancel'),   // 添加中文按钮文字
        type: 'warning'
      }
    );
    await deleteMenu(row.id);
    ElMessage.success(tc('success'));
    initData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error("Failed to delete menu:", error);
      ElMessage.error(tc('deleteFailed'));
    }
  }
};

const handleSizeChange = (size: number) => {
  pagination.size = size;
  initData();
};

const handleCurrentChange = (current: number) => {
  pagination.current = current;
  initData();
};

const expandAll = () => {
  isExpandAll.value = true;
  toggleRowExpansion(tableData.value, true);
};

const collapseAll = () => {
  isExpandAll.value = false;
  toggleRowExpansion(tableData.value, false);
};

const toggleRowExpansion = (data: Menu[], isExpand: boolean) => {
  data.forEach(row => {
    tableRef.value?.toggleRowExpansion(row, isExpand);
    if (row.children) {
      toggleRowExpansion(row.children, isExpand);
    }
  });
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (isEdit.value) {
          const updateData: UpdateMenuRequest = {
            ...formData,
            id: currentId.value!,
            menuCode: formData.menuName,
            sortOrder: 0,
            // isCache: false,
            parentId: formData.parentId || undefined
          };
          await updateMenu(updateData);
          ElMessage.success(tc('success'));
        } else {
          const createData: CreateMenuRequest = {
            ...formData,
            menuCode: formData.menuName,
            sortOrder: 0,
            // isCache: false,
            parentId: formData.parentId || undefined
          };
          await addMenu(createData);
          ElMessage.success(tc('success'));
        }
        dialogVisible.value = false;
        initData();
      } catch (error) {
        console.error("Failed to submit menu:", error);
        ElMessage.error(isEdit.value ? tc('editFailed') : tc('addFailed'));
      }
    }
  });
};

// 加载字典数据
const loadDictionary = async () => {
  try {
    const response = await getDictionary('1002');
    if (response.code === 200) {
      menuStatusOptions.value = response.result;
    }
  } catch (error) {
    console.error('加载字典数据失败:', error);
  }
};

onMounted(() => {
  initData();
  loadDictionary();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}
.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}
.search-card, .table-card {
  margin-bottom: 20px;
}
.table-toolbar {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-bottom: 20px;
}
.dialog-form-modern .el-form-item {
  margin-bottom: 22px;
}
.dialog-footer-buttons {
    display: flex;
    justify-content: flex-end;
}
</style>
