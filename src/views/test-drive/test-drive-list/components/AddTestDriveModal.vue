<template>
  <el-dialog
    v-model="visible"
    title="登记试驾单"
    width="800px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formModel" :rules="rules" label-width="120px">
      <h3 class="section-title">快捷搜索潜客</h3>
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="潜客名称">
            <el-input v-model="prospectSearch.name" placeholder="请输入潜客名称搜索" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="潜客手机号">
            <el-input v-model="prospectSearch.phone" placeholder="输入手机号搜索" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" @click="handleSearchProspect">搜索潜客</el-button>
          </el-form-item>
        </el-col>
      </el-row>

      <h3 class="section-title">潜客信息</h3>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="*潜客来源" prop="sourceChannel">
            <el-input v-model="formModel.sourceChannel" disabled placeholder="搜索后自动填入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*潜客名称" prop="customerName">
            <el-input v-model="formModel.customerName" disabled placeholder="搜索后自动填入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*潜客手机号" prop="customerPhone">
            <el-input v-model="formModel.customerPhone" disabled placeholder="搜索后自动填入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*身份证件类别" prop="idType">
            <el-input v-model="formModel.idType" disabled placeholder="搜索后自动填入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*身份证件号" prop="idNumber">
            <el-input v-model="formModel.idNumber" disabled placeholder="搜索后自动填入" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*邮箱" prop="email">
            <el-input v-model="formModel.email" disabled placeholder="搜索后自动填入" />
          </el-form-item>
        </el-col>
      </el-row>

      <h3 class="section-title">试驾信息</h3>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="试驾单号" prop="testDriveNo">
            <el-input v-model="formModel.testDriveNo" disabled placeholder="保存时自动生成" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*销售顾问" prop="consultantName">
            <el-input v-model="formModel.consultantName" disabled placeholder="潜客所属顾问" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾车型 (Model)" prop="model">
            <el-select v-model="formModel.model" placeholder="请选择车型" style="width: 100%">
              <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾配置 (Variant)" prop="variant">
            <el-select v-model="formModel.variant" placeholder="请选择配置" style="width: 100%">
              <el-option v-for="item in variantOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾人" prop="driverName">
            <el-input v-model="formModel.driverName" placeholder="默认带入潜客信息，可更改" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾人手机号" prop="driverPhone">
            <el-input v-model="formModel.driverPhone" placeholder="默认带入潜客信息，可更改" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾人证件类别" prop="driverIdType">
            <el-select v-model="formModel.driverIdType" placeholder="请选择证件类别" style="width: 100%" :loading="dictionaryLoading">
              <el-option v-for="item in idTypeOptions" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾人证件号" prop="driverIdNumber">
            <el-input v-model="formModel.driverIdNumber" placeholder="请输入试驾人证件号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾人驾照号" prop="driverLicenseNumber">
            <el-input v-model="formModel.driverLicenseNumber" placeholder="请输入试驾人驾照号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾开始里程数" prop="startMileage">
            <el-input-number v-model="formModel.startMileage" :min="0" placeholder="请输入" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾结束里程数" prop="endMileage">
            <el-input-number v-model="formModel.endMileage" :min="formModel.startMileage || 0" placeholder="请输入" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾开始时间" prop="startTime">
            <el-date-picker
              v-model="formModel.startTime"
              type="datetime"
              placeholder="年/月/日 --:--"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*试驾结束时间" prop="endTime">
            <el-date-picker
              v-model="formModel.endTime"
              type="datetime"
              placeholder="年/月/日 --:--"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="试驾反馈" prop="feedback">
            <el-input v-model="formModel.feedback" type="textarea" placeholder="请输入试驾反馈..." />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { testDriveApi } from '@/api/modules/test-drive'
import type { TestDriveRecord } from '@/api/types/test-drive'
import { useDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// Refs
const formRef = ref<FormInstance>()
const loading = ref(false)

// 使用字典数据
const {
  options: idTypeOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.ID_TYPE)

// Computed
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 创建初始表单模型
const createInitialFormModel = () => ({
  testDriveNo: '',
  prospectId: null as number | null,
  sourceChannel: '',
  customerName: '',
  customerPhone: '',
  idType: '',
  idNumber: '',
  email: '',
  consultantName: '',
  model: null as number | null,
  variant: null as number | null,
  driverName: '',
  driverPhone: '',
  driverIdType: null as number | null,
  driverIdNumber: '',
  driverLicenseNumber: '',
  startMileage: null as number | null,
  endMileage: null as number | null,
  startTime: null as Date | null,
  endTime: null as Date | null,
  feedback: ''
})

// 表单数据
const formModel = reactive(createInitialFormModel())

// 潜客搜索数据
const prospectSearch = reactive({
  name: '',
  phone: ''
})

// 表单验证规则
const rules: FormRules = {
  customerName: { required: true, message: '请搜索并选择潜客', trigger: 'change' },
  consultantName: { required: true, message: '潜客需要有所属销售顾问', trigger: 'change' },
  model: { required: true, message: '请选择试驾车型', trigger: 'change' },
  variant: { required: true, message: '请选择试驾配置', trigger: 'change' },
  driverName: { required: true, message: '请输入试驾人姓名', trigger: 'blur' },
  driverPhone: { required: true, message: '请输入试驾人手机号', trigger: 'blur' },
  driverIdType: { required: true, message: '请选择试驾人证件类别', trigger: 'change' },
  driverIdNumber: { required: true, message: '请输入试驾人证件号', trigger: 'blur' },
  driverLicenseNumber: { required: true, message: '请输入试驾人驾照号', trigger: 'blur' },
  startMileage: { required: true, type: 'number', message: '请输入开始里程数', trigger: 'blur' },
  endMileage: { required: true, type: 'number', message: '请输入结束里程数', trigger: 'blur' },
  startTime: { required: true, type: 'date', message: '请选择试驾开始时间', trigger: 'change' },
  endTime: {
    required: true,
    type: 'date',
    message: '请选择试驾结束时间',
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (value && formModel.startTime && value < formModel.startTime) {
        callback(new Error('结束时间不能早于开始时间'))
      } else {
        callback()
      }
    }
  }
}



const modelOptions = [
  { label: 'Model S', value: 1 },
  { label: 'Model 3', value: 2 },
  { label: 'Model X', value: 3 },
  { label: 'Model Y', value: 4 }
]

const variantOptions = [
  { label: '标准版', value: 1 },
  { label: '长续航版', value: 2 },
  { label: '性能版', value: 3 }
]

// 方法
const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
}

const resetForm = () => {
  Object.assign(formModel, createInitialFormModel())
  prospectSearch.name = ''
  prospectSearch.phone = ''
  formRef.value?.clearValidate()
}

const handleSearchProspect = async () => {
  if (!prospectSearch.name && !prospectSearch.phone) {
    ElMessage.warning('请输入潜客名称或手机号进行搜索')
    return
  }

  loading.value = true
  try {
    const params = {
      customerName: prospectSearch.name || undefined,
      customerPhone: prospectSearch.phone || undefined
    }

    const response = await testDriveApi.searchLeads(params)
    console.log('搜索潜客结果:', response)

    // 处理搜索结果
    if (response) {
      const lead = response.result // 取第一个结果

      // 填充潜客信息
      formModel.sourceChannel = lead.sourceChannel || ''
      formModel.customerName = lead.name || ''
      formModel.customerPhone = lead.phoneNumber || ''
      formModel.idType = lead.idType || ''
      formModel.idNumber = lead.idNumber || ''
      formModel.email = lead.email || ''
      formModel.consultantName = lead.currentSalesAdvisorId || ''

      formModel.prospectId = lead.prospectId || null

      // 默认填充试驾人信息
      formModel.driverName = lead.name || ''
      formModel.driverPhone = lead.phoneNumber || ''
      formModel.driverIdType = lead.idType || null
      formModel.driverIdNumber = lead.idNumber || ''

      ElMessage.success('潜客信息加载成功')
    } else {
      ElMessage.warning('未找到匹配的潜客信息')
    }
  } catch (error: any) {
    console.error('搜索潜客出错:', error)
    ElMessage.error(`搜索失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:modelValue', false)
}

const handleConfirm = () => {
  console.log('表单验证前的数据:', JSON.stringify(formModel, null, 2))

  formRef.value?.validate(async (valid) => {
    if (!valid) {
      return
    }

    if ((formModel.endMileage || 0) < (formModel.startMileage || 0)) {
      ElMessage.error('结束里程数不能小于开始里程数')
      return
    }

    if (formModel.endTime && formModel.startTime && formModel.endTime < formModel.startTime) {
      ElMessage.error('试驾结束时间不能早于开始时间')
      return
    }

    loading.value = true
    try {
      const payload: Partial<TestDriveRecord> = {
        ...formModel,
        prospectId: formModel.prospectId,
        startTime: formModel.startTime?.toISOString(),
        endTime: formModel.endTime?.toISOString()
      }
      console.log('提交的数据:', JSON.stringify(payload, null, 2))

      await testDriveApi.createTestDrive(payload)
      ElMessage.success('登记成功')
      emit('success')
      emit('update:modelValue', false)
    } catch (error: any) {
      ElMessage.error(`登记失败: ${error.message}`)
    } finally {
      loading.value = false
    }
  })
}

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm()
  }
})
</script>

<style scoped>
.section-title {
  margin-top: 10px;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
