/**
 * 销售订单数据管理器
 * 统一管理订单数据和相关操作
 */
class SalesOrderManager {
    constructor() {
        // 模拟订单数据
        this.orderData = [
            {
                id: 1,
                orderNo: 'KL001-20240120-001',
                dealer: '吉隆坡中央店',
                createTime: '2024-01-20',
                salesman: '张三',
                salesmanPhone: '+60123456789',
                customer: '李先生',
                customerPhone: '+60123456789',
                customerType: '个人',
                model: 'Model A',
                variant: 'Variant 1',
                color: 'Red',
                vin: 'VIN1234567890',
                paymentMethod: '贷款',
                loanStatus: '审核通过',
                orderStatus: '已确认',
                approvalStatus: '-',
                paymentStatus: '已支付定金',
                insuranceStatus: '投保完成',
                jpjRegistrationStatus: '已登记',
                // 详情页数据
                idType: 'IC',
                idNumber: '123456-78-9012',
                email: '<EMAIL>',
                address: '吉隆坡市中心区',
                state: '吉隆坡',
                city: '吉隆坡',
                zipCode: '50000',
                region: '中央区',
                dealerCity: '吉隆坡',
                salesAdvisor: '张三',
                price: 'RM 85,000.00',
                invoiceType: 'personal',
                invoiceName: '李先生',
                invoicePhone: '+60123456789',
                invoiceAddress: '吉隆坡市中心区',
                deposit: 'RM 8,500.00',
                loanAmount: 'RM 68,000.00',
                finalPayment: 'RM 8,500.00',
                remarks: '客户要求红色车身',
                accessories: [
                    { category: '外观套件', name: '运动包围', price: 2500.00, quantity: 1, total: 2500.00 },
                    { category: '内饰套件', name: '真皮座椅', price: 3500.00, quantity: 1, total: 3500.00 }
                ],
                rights: [
                    { code: 'R001', name: '延保服务', mode: '优惠', discountPrice: 1000.00, effectiveDate: '2024-01-20', expiryDate: '2027-01-20' }
                ],
                policies: [
                    { 
                        policyNumber: 'POL001234', 
                        insuranceType: '全险', 
                        insuranceCompany: '太平洋保险', 
                        effectiveDate: '2024-01-20', 
                        expiryDate: '2025-01-20', 
                        price: 'RM 3,500.00' 
                    }
                ],
                otrFees: [
                    { item: '路税 (Road Tax)', price: 90.00, effectiveDate: '2024-01-20', expiryDate: '2025-01-20' },
                    { item: '注册费 (Registration Fee)', price: 150.00, effectiveDate: '2024-01-20', expiryDate: '2025-01-20' }
                ]
            },
            {
                id: 2,
                orderNo: 'KL001-20240120-002',
                dealer: '吉隆坡中央店',
                createTime: '2024-01-20',
                salesman: '李四',
                salesmanPhone: '+60132345678',
                customer: '王女士',
                customerPhone: '+60132345678',
                customerType: '企业',
                model: 'Model B',
                variant: 'Variant 2',
                color: 'Blue',
                vin: 'VIN2345678901',
                paymentMethod: '全款',
                loanStatus: '-',
                orderStatus: '已确认',
                approvalStatus: '-',
                paymentStatus: '已支付定金',
                insuranceStatus: '投保完成',
                jpjRegistrationStatus: '已登记',
                // 详情页数据
                idType: 'Passport',
                idNumber: 'A12345678',
                email: '<EMAIL>',
                address: '吉隆坡商业区',
                state: '吉隆坡',
                city: '吉隆坡',
                zipCode: '50100',
                region: '商业区',
                dealerCity: '吉隆坡',
                salesAdvisor: '李四',
                price: 'RM 95,000.00',
                invoiceType: 'company',
                invoiceName: '某某公司',
                invoicePhone: '+60132345678',
                invoiceAddress: '吉隆坡商业区',
                deposit: 'RM 9,500.00',
                loanAmount: 'RM 0.00',
                finalPayment: 'RM 85,500.00',
                remarks: '企业采购',
                accessories: [
                    { category: '安全套件', name: '倒车影像', price: 1500.00, quantity: 1, total: 1500.00 }
                ],
                rights: [],
                policies: [
                    { 
                        policyNumber: 'POL002345', 
                        insuranceType: '商业险', 
                        insuranceCompany: '大众保险', 
                        effectiveDate: '2024-01-20', 
                        expiryDate: '2025-01-20', 
                        price: 'RM 4,200.00' 
                    }
                ],
                otrFees: [
                    { item: '路税 (Road Tax)', price: 120.00, effectiveDate: '2024-01-20', expiryDate: '2025-01-20' },
                    { item: '注册费 (Registration Fee)', price: 150.00, effectiveDate: '2024-01-20', expiryDate: '2025-01-20' }
                ]
            },
            {
                id: 3,
                orderNo: 'SLG002-20240119-015',
                dealer: '雪兰莪店',
                createTime: '2024-01-19',
                salesman: '王五',
                salesmanPhone: '+60148765432',
                customer: '陈先生',
                customerPhone: '+60148765432',
                customerType: '企业',
                model: 'Model A',
                variant: 'Variant 2',
                color: 'Green',
                vin: 'VIN3456789012',
                paymentMethod: '贷款',
                loanStatus: '审核通过',
                orderStatus: '待交车',
                approvalStatus: '-',
                paymentStatus: '已支付尾款',
                insuranceStatus: '投保完成',
                jpjRegistrationStatus: '已登记',
                // 详情页数据
                idType: 'IC',
                idNumber: '234567-89-0123',
                email: '<EMAIL>',
                address: '雪兰莪工业区',
                state: '雪兰莪',
                city: '沙阿兰',
                zipCode: '40000',
                region: '雪兰莪',
                dealerCity: '沙阿兰',
                salesAdvisor: '王五',
                price: 'RM 88,000.00',
                invoiceType: 'company',
                invoiceName: '陈氏企业',
                invoicePhone: '+60148765432',
                invoiceAddress: '雪兰莪工业区',
                deposit: 'RM 8,800.00',
                loanAmount: 'RM 70,400.00',
                finalPayment: 'RM 8,800.00',
                remarks: '绿色环保车型',
                accessories: [
                    { category: '科技套件', name: '智能导航', price: 2000.00, quantity: 1, total: 2000.00 },
                    { category: '舒适套件', name: '座椅加热', price: 1200.00, quantity: 1, total: 1200.00 }
                ],
                rights: [
                    { code: 'R002', name: '免费保养', mode: '赠送', discountPrice: 800.00, effectiveDate: '2024-01-19', expiryDate: '2026-01-19' }
                ],
                policies: [
                    { 
                        policyNumber: 'POL003456', 
                        insuranceType: '全险', 
                        insuranceCompany: '安联保险', 
                        effectiveDate: '2024-01-19', 
                        expiryDate: '2025-01-19', 
                        price: 'RM 3,800.00' 
                    }
                ],
                otrFees: [
                    { item: '路税 (Road Tax)', price: 95.00, effectiveDate: '2024-01-19', expiryDate: '2025-01-19' },
                    { item: '注册费 (Registration Fee)', price: 150.00, effectiveDate: '2024-01-19', expiryDate: '2025-01-19' }
                ]
            },
            {
                id: 4,
                orderNo: 'PNG003-20240119-008',
                dealer: '槟城店',
                createTime: '2024-01-19',
                salesman: '赵六',
                salesmanPhone: '+60161234567',
                customer: '孙女士',
                customerPhone: '+60161234567',
                customerType: '个人',
                model: 'Model C',
                variant: 'Variant 1',
                color: 'Yellow',
                vin: 'VIN4567890123',
                paymentMethod: '全款',
                loanStatus: '-',
                orderStatus: '已提交',
                approvalStatus: '-',
                paymentStatus: '待支付定金',
                insuranceStatus: '未投保',
                jpjRegistrationStatus: '待登记',
                // 详情页数据
                idType: 'IC',
                idNumber: '345678-90-1234',
                email: '<EMAIL>',
                address: '槟城乔治市',
                state: '槟城',
                city: '乔治市',
                zipCode: '10000',
                region: '槟城',
                dealerCity: '乔治市',
                salesAdvisor: '赵六',
                price: 'RM 75,000.00',
                invoiceType: 'personal',
                invoiceName: '孙女士',
                invoicePhone: '+60161234567',
                invoiceAddress: '槟城乔治市',
                deposit: 'RM 7,500.00',
                loanAmount: 'RM 0.00',
                finalPayment: 'RM 67,500.00',
                remarks: '喜欢黄色车身',
                accessories: [],
                rights: [],
                policies: [],
                otrFees: [
                    { item: '路税 (Road Tax)', price: 85.00, effectiveDate: '2024-01-19', expiryDate: '2025-01-19' },
                    { item: '注册费 (Registration Fee)', price: 150.00, effectiveDate: '2024-01-19', expiryDate: '2025-01-19' }
                ]
            },
            {
                id: 5,
                orderNo: 'JHR004-20240118-025',
                dealer: '柔佛店',
                createTime: '2024-01-18',
                salesman: '刘七',
                salesmanPhone: '+60179876543',
                customer: '马先生',
                customerPhone: '+60179876543',
                customerType: '个人',
                model: 'Model B',
                variant: 'Variant 1',
                color: 'White',
                vin: 'VIN5678901234',
                paymentMethod: '贷款',
                loanStatus: '审核驳回',
                orderStatus: '已取消',
                approvalStatus: '已审批',
                paymentStatus: '退款完成',
                insuranceStatus: '-',
                jpjRegistrationStatus: '-',
                // 详情页数据
                idType: 'IC',
                idNumber: '456789-01-2345',
                email: '<EMAIL>',
                address: '柔佛新山',
                state: '柔佛',
                city: '新山',
                zipCode: '80000',
                region: '柔佛',
                dealerCity: '新山',
                salesAdvisor: '刘七',
                price: 'RM 82,000.00',
                invoiceType: 'personal',
                invoiceName: '马先生',
                invoicePhone: '+60179876543',
                invoiceAddress: '柔佛新山',
                deposit: 'RM 8,200.00',
                loanAmount: 'RM 0.00',
                finalPayment: 'RM 0.00',
                remarks: '贷款审核未通过，已取消订单',
                accessories: [],
                rights: [],
                policies: [],
                otrFees: []
            }
        ];
    }

    /**
     * 根据订单编号获取订单数据
     */
    getOrderByNo(orderNo) {
        return this.orderData.find(order => order.orderNo === orderNo);
    }

    /**
     * 获取URL参数
     */
    getUrlParam(paramName) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(paramName);
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        // 这里可以添加更复杂的消息显示逻辑，比如toast提示
        if (type === 'error') {
            alert(message);
        }
    }

    /**
     * 返回到列表页面
     */
    goToList() {
        window.location.href = 'Factory Order Management-厂端订单管理.html';
    }

    /**
     * 计算订单金额
     */
    calculateOrderAmount(orderData) {
        // 基础车价
        const basePrice = parseFloat(orderData.price?.replace(/[RM ,]/g, '') || '0') || 0;
        
        // 选配件金额
        let accessoryAmount = 0;
        if (orderData.accessories && orderData.accessories.length > 0) {
            accessoryAmount = orderData.accessories.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);
        }
        
        // 保险金额
        let insuranceAmount = 0;
        if (orderData.policies && orderData.policies.length > 0) {
            insuranceAmount = orderData.policies.reduce((sum, policy) => {
                return sum + (parseFloat(policy.price?.replace(/[RM ,]/g, '') || '0') || 0);
            }, 0);
        }
        
        // OTR费用
        let otrAmount = 0;
        if (orderData.otrFees && orderData.otrFees.length > 0) {
            otrAmount = orderData.otrFees.reduce((sum, fee) => sum + (parseFloat(fee.price) || 0), 0);
        }
        
        // 权益优惠
        let rightsDiscount = 0;
        if (orderData.rights && orderData.rights.length > 0) {
            rightsDiscount = orderData.rights.reduce((sum, right) => sum + (parseFloat(right.discountPrice) || 0), 0);
        }
        
        // 总价计算
        const totalPrice = basePrice + accessoryAmount + insuranceAmount + otrAmount - rightsDiscount;
        
        // 已付金额
        const paidDeposit = parseFloat(orderData.deposit?.replace(/[RM ,]/g, '') || '0') || 0;
        const paidFinal = parseFloat(orderData.finalPayment?.replace(/[RM ,]/g, '') || '0') || 0;
        const paidAmount = paidDeposit + (orderData.paymentStatus === '已支付尾款' ? paidFinal : 0);
        
        // 剩余应收
        const remainingAmount = Math.max(0, totalPrice - paidAmount);
        
        return {
            totalPrice: totalPrice.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}),
            remainingAmount: remainingAmount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}),
            basePrice,
            accessoryAmount,
            insuranceAmount,
            otrAmount,
            rightsDiscount,
            paidAmount
        };
    }
}

// 创建全局实例
const salesOrderManager = new SalesOrderManager(); 