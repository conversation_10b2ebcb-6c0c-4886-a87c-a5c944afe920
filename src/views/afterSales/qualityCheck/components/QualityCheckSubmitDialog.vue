<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('qualityCheck.dialog.submitTitle')"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
    >
      <el-form-item :label="t('qualityCheck.qualityCheckNo')" prop="qualityCheckNo">
        <el-input v-model="formData.qualityCheckNo" disabled />
      </el-form-item>
      
      <el-form-item :label="t('qualityCheck.actualHours')" prop="actualHours">
        <el-input-number
          v-model="formData.actualHours"
          :min="0.1"
          :max="24"
          :step="0.1"
          :precision="1"
          style="width: 200px"
        />
        <span style="margin-left: 8px;">小时</span>
      </el-form-item>
      
      <el-form-item :label="t('qualityCheck.dialog.inputAuditRemark') || '备注'" prop="remarks">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="3"
          :placeholder="t('qualityCheck.dialog.inputAuditRemark') || '请输入备注'"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <!-- 质检项目 -->
      <el-form-item :label="t('qualityCheck.tabs.checkItems')" class="check-items-form">
        <div class="check-items-container">
          <div v-for="category in groupedCheckItems" :key="category.categoryCode" class="check-category">
            <h4 class="category-title">{{ category.categoryName }}</h4>
            <div class="check-items-grid">
              <div 
                v-for="(item, index) in category.items" 
                :key="item.id"
                class="check-item"
              >
                <div class="item-header">
                  <span class="item-name">{{ item.itemName }}</span>
                  <el-tag 
                    v-if="item.isRequired" 
                    type="danger" 
                    size="small"
                  >
                    {{ t('qualityCheck.checkItem.required') }}
                  </el-tag>
                </div>
                
                <div class="item-standard">
                  <span class="label">{{ t('qualityCheck.checkItem.standardValue') }}:</span>
                  <span class="value">{{ item.standardValue }}</span>
                </div>
                
                <div class="item-input">
                  <!-- 布尔类型 -->
                  <el-radio-group 
                    v-if="item.itemType === 'BOOLEAN'"
                    v-model="item.checkResult"
                    size="small"
                  >
                    <el-radio label="PASS">{{ t('qualityCheck.checkResult.PASS') }}</el-radio>
                    <el-radio label="FAIL">{{ t('qualityCheck.checkResult.FAIL') }}</el-radio>
                  </el-radio-group>
                  
                  <!-- 数值类型 -->
                  <div v-else-if="item.itemType === 'NUMERIC'" class="numeric-input">
                    <el-input-number
                      v-model="item.numericValue"
                      :precision="2"
                      size="small"
                      style="width: 120px"
                    />
                    <span v-if="item.unit" class="unit">{{ item.unit }}</span>
                  </div>
                  
                  <!-- 文本类型 -->
                  <el-input
                    v-else
                    v-model="item.textValue"
                    size="small"
                    :placeholder="t('qualityCheck.checkItem.actualValue')"
                    maxlength="100"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button @click="handleSaveDraft" :loading="draftLoading">
          {{ t('qualityCheck.actions.saveDraft') }}
        </el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ t('qualityCheck.dialog.confirmSubmit') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getQualityCheckDetail, getQualityCheckTemplate } from '@/api/modules/afterSales/qualityCheck';
import type { 
  QualityCheckListItem, 
  QualityCheckSubmitForm,
  QualityCheckDetail,
  QualityCheckItemForm
} from '@/types/afterSales/qualityCheck';

// 组件Props
interface Props {
  visible: boolean;
  qualityCheck: QualityCheckListItem | null;
  loading?: boolean;
  draftLoading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm', data: QualityCheckSubmitForm): void;
  (e: 'save-draft', data: QualityCheckSubmitForm): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  draftLoading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 表单引用
const formRef = ref<FormInstance>();

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单数据
const formData = reactive<{
  qualityCheckNo: string;
  qualityCheckId: string;
  actualHours?: number;
  remarks?: string;
}>({
  qualityCheckNo: '',
  qualityCheckId: '',
  actualHours: undefined,
  remarks: ''
});

// 质检项目数据
const checkItems = ref<QualityCheckItemForm[]>([]);

// 表单验证规则
const formRules: FormRules = {
  actualHours: [
    { required: true, message: t('qualityCheck.messages.inputReworkReason') || '请输入实际工时', trigger: 'blur' }
  ]
};

// 分组的质检项目
const groupedCheckItems = computed(() => {
  const groups = new Map();
  checkItems.value.forEach(item => {
    if (!groups.has(item.categoryCode)) {
      groups.set(item.categoryCode, {
        categoryCode: item.categoryCode,
        categoryName: item.categoryName,
        items: []
      });
    }
    groups.get(item.categoryCode).items.push(item);
  });
  
  return Array.from(groups.values()).sort((a, b) => a.categoryCode.localeCompare(b.categoryCode));
});

// 监听质检单变化
watch(
  () => props.qualityCheck,
  async (newQualityCheck) => {
    if (newQualityCheck) {
      formData.qualityCheckNo = newQualityCheck.qualityCheckNo;
      formData.qualityCheckId = newQualityCheck.id;
      formData.actualHours = newQualityCheck.estimatedHours;
      formData.remarks = '';
      
      // 加载质检项目
      await loadCheckItems(newQualityCheck.id, newQualityCheck.workOrderType);
    }
  },
  { immediate: true }
);

// 加载质检项目
const loadCheckItems = async (qualityCheckId: string, workOrderType: string) => {
  try {
    // 先尝试获取已有的质检项目
    const detail = await getQualityCheckDetail(qualityCheckId);
    if (detail.checkItems && detail.checkItems.length > 0) {
      // 转换为表单格式
      checkItems.value = detail.checkItems.map(item => ({
        id: item.id,
        categoryCode: item.categoryCode,
        categoryName: item.categoryName,
        itemCode: item.itemCode,
        itemName: item.itemName,
        itemType: item.itemType,
        checkResult: item.checkResult,
        numericValue: item.numericValue,
        textValue: item.textValue,
        standardValue: item.standardValue,
        unit: item.unit,
        isRequired: item.isRequired,
        sortOrder: item.sortOrder
      }));
    } else {
      // 如果没有已有项目，从模板加载
      const templates = await getQualityCheckTemplate(workOrderType);
      checkItems.value = [];
      templates.forEach(template => {
        template.items.forEach((item, index) => {
          checkItems.value.push({
            categoryCode: template.categoryCode,
            categoryName: template.categoryName,
            itemCode: item.itemCode,
            itemName: item.itemName,
            itemType: item.itemType,
            standardValue: item.standardValue,
            unit: item.unit,
            isRequired: item.isRequired,
            sortOrder: item.sortOrder
          });
        });
      });
    }
  } catch (error) {
    console.error('加载质检项目失败:', error);
    checkItems.value = [];
  }
};

// 验证质检项目
const validateCheckItems = (): boolean => {
  for (const item of checkItems.value) {
    if (item.isRequired) {
      if (item.itemType === 'BOOLEAN' && !item.checkResult) {
        return false;
      }
      if (item.itemType === 'NUMERIC' && (item.numericValue === undefined || item.numericValue === null)) {
        return false;
      }
      if (item.itemType === 'TEXT' && !item.textValue) {
        return false;
      }
    }
  }
  return true;
};

// 确认提交
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    if (!validateCheckItems()) {
      return;
    }
    
    const submitData: QualityCheckSubmitForm = {
      qualityCheckId: formData.qualityCheckId,
      actualHours: formData.actualHours,
      remarks: formData.remarks,
      checkItems: checkItems.value,
      isDraft: false
    };
    
    emit('confirm', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 保存草稿
const handleSaveDraft = async () => {
  const submitData: QualityCheckSubmitForm = {
    qualityCheckId: formData.qualityCheckId,
    actualHours: formData.actualHours,
    remarks: formData.remarks,
    checkItems: checkItems.value,
    isDraft: true
  };
  
  emit('save-draft', submitData);
};

// 关闭处理
const handleClose = () => {
  formRef.value?.resetFields();
  checkItems.value = [];
  emit('close');
};
</script>

<style scoped>
.check-items-form {
  margin-bottom: 0;
}

.check-items-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
}

.check-category {
  margin-bottom: 24px;
}

.check-category:last-child {
  margin-bottom: 0;
}

.category-title {
  margin-bottom: 16px;
  color: #303133;
  font-weight: 500;
  font-size: 14px;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.check-items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.check-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-name {
  font-weight: 500;
  color: #303133;
  font-size: 13px;
}

.item-standard {
  margin-bottom: 8px;
  font-size: 12px;
  color: #606266;
}

.item-standard .label {
  font-weight: 500;
}

.item-standard .value {
  margin-left: 4px;
}

.item-input {
  display: flex;
  align-items: center;
}

.numeric-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unit {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.check-items-form .el-form-item__content) {
  margin-left: 0 !important;
}
</style>
