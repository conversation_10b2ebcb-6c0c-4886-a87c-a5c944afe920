<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('qualityCheck.dialog.auditTitle')"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
    >
      <el-form-item :label="t('qualityCheck.qualityCheckNo')" prop="qualityCheckNo">
        <el-input v-model="formData.qualityCheckNo" disabled />
      </el-form-item>
      
      <el-form-item :label="t('qualityCheck.workOrderNo')" prop="workOrderNo">
        <el-input v-model="formData.workOrderNo" disabled />
      </el-form-item>
      
      <el-form-item :label="t('qualityCheck.technicianName')" prop="technicianName">
        <el-input v-model="formData.technicianName" disabled />
      </el-form-item>
      
      <el-form-item :label="t('qualityCheck.actualHours')" prop="actualHours">
        <el-input v-model="formData.actualHours" disabled>
          <template #suffix>{{ tc('hours') || '小时' }}</template>
        </el-input>
      </el-form-item>
      
      <el-form-item :label="t('qualityCheck.dialog.selectAuditResult')" prop="auditResult">
        <el-radio-group v-model="formData.auditResult">
          <el-radio label="passed">
            <el-tag type="success" size="small">
              {{ t('qualityCheck.auditResult.passed') }}
            </el-tag>
          </el-radio>
          <el-radio label="rework">
            <el-tag type="danger" size="small">
              {{ t('qualityCheck.auditResult.rework') }}
            </el-tag>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      
      <!-- 返工原因 - 仅在选择返工时显示 -->
      <el-form-item 
        v-if="formData.auditResult === 'rework'"
        :label="t('qualityCheck.reworkReason')" 
        prop="reworkReason"
      >
        <el-select
          v-model="formData.reworkReason"
          :placeholder="t('qualityCheck.dialog.inputReworkReason')"
          style="width: 100%"
        >
          <el-option :label="t('qualityCheck.reworkReasons.quality_fail') || '质检项目不合格'" value="quality_fail" />
          <el-option :label="t('qualityCheck.reworkReasons.process_fail') || '工艺标准不达标'" value="process_fail" />
          <el-option :label="t('qualityCheck.reworkReasons.parts_issue') || '零件质量问题'" value="parts_issue" />
          <el-option :label="t('qualityCheck.reworkReasons.operation_issue') || '操作规范不当'" value="operation_issue" />
          <el-option :label="t('qualityCheck.reworkReasons.customer_request') || '客户要求返工'" value="customer_request" />
          <el-option :label="t('qualityCheck.reworkReasons.other') || '其他原因'" value="other" />
        </el-select>
      </el-form-item>
      
      <!-- 返工要求 - 仅在选择返工时显示 -->
      <el-form-item 
        v-if="formData.auditResult === 'rework'"
        :label="t('qualityCheck.reworkRequirement')" 
        prop="reworkRequirement"
      >
        <el-input
          v-model="formData.reworkRequirement"
          type="textarea"
          :rows="3"
          :placeholder="t('qualityCheck.dialog.inputReworkRequirement')"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item :label="t('qualityCheck.auditRemark')" prop="auditRemark">
        <el-input
          v-model="formData.auditRemark"
          type="textarea"
          :rows="3"
          :placeholder="t('qualityCheck.dialog.inputAuditRemark')"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <!-- 质检结果摘要 -->
      <el-form-item :label="t('qualityCheck.tabs.checkItems')">
        <div class="check-summary">
          <div v-if="qualityCheckSummary" class="summary-stats">
            <el-tag type="success" size="small">
              {{ t('qualityCheck.checkResult.PASS') }}: {{ qualityCheckSummary.passCount }}
            </el-tag>
            <el-tag type="danger" size="small" style="margin-left: 8px;">
              {{ t('qualityCheck.checkResult.FAIL') }}: {{ qualityCheckSummary.failCount }}
            </el-tag>
            <el-tag type="info" size="small" style="margin-left: 8px;">
              {{ t('qualityCheck.checkItem.required') }}: {{ qualityCheckSummary.requiredCount }}
            </el-tag>
          </div>
          <el-button 
            type="text" 
            size="small" 
            @click="showDetailSummary = !showDetailSummary"
          >
            {{ showDetailSummary ? (t('qualityCheck.hideDetails') || '收起详情') : (t('qualityCheck.showDetails') || '查看详情') }}
          </el-button>
        </div>
        
        <!-- 详细质检结果 -->
        <div v-if="showDetailSummary" class="check-details">
          <el-table :data="checkItemsSummary" size="small" border>
            <el-table-column prop="categoryName" :label="t('qualityCheck.category')" width="120" />
            <el-table-column prop="itemName" :label="t('qualityCheck.itemName')" width="150" />
            <el-table-column prop="itemType" :label="tc('type')" width="80" align="center">
              <template #default="{ row }">
                <el-tag size="small">
                  {{ t(`qualityCheck.itemType.${row.itemType}`) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="standardValue" :label="t('qualityCheck.standardValue')" width="120" />
            <el-table-column :label="t('qualityCheck.actualValue')" width="120">
              <template #default="{ row }">
                <span v-if="row.itemType === 'BOOLEAN'">
                  <el-tag 
                    :type="row.checkResult === 'PASS' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ row.checkResult ? t(`qualityCheck.checkResult.${row.checkResult}`) : '-' }}
                  </el-tag>
                </span>
                <span v-else-if="row.itemType === 'NUMERIC'">
                  {{ row.numericValue }}{{ row.unit }}
                </span>
                <span v-else>
                  {{ row.textValue || '-' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="isRequired" :label="t('qualityCheck.required')" width="60" align="center">
              <template #default="{ row }">
                <el-tag
                  :type="row.isRequired ? 'danger' : 'info'"
                  size="small"
                >
                  {{ row.isRequired ? tc('yes') : tc('no') }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ t('qualityCheck.dialog.confirmAudit') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getQualityCheckDetail } from '@/api/modules/afterSales/qualityCheck';
import type { 
  QualityCheckListItem, 
  QualityCheckAuditForm,
  QualityCheckDetail,
  QualityCheckItem
} from '@/types/afterSales/qualityCheck';

// 组件Props
interface Props {
  visible: boolean;
  qualityCheck: QualityCheckListItem | null;
  loading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'confirm', data: QualityCheckAuditForm): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 表单引用
const formRef = ref<FormInstance>();

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单数据
const formData = reactive<{
  qualityCheckNo: string;
  workOrderNo: string;
  technicianName: string;
  actualHours: string;
  qualityCheckId: string;
  auditResult: 'passed' | 'rework' | '';
  reworkReason?: string;
  reworkRequirement?: string;
  auditRemark?: string;
}>({
  qualityCheckNo: '',
  workOrderNo: '',
  technicianName: '',
  actualHours: '',
  qualityCheckId: '',
  auditResult: '',
  reworkReason: '',
  reworkRequirement: '',
  auditRemark: ''
});

// 质检项目详情
const checkItemsSummary = ref<QualityCheckItem[]>([]);
const showDetailSummary = ref(false);

// 表单验证规则
const formRules: FormRules = {
  auditResult: [
    { required: true, message: t('qualityCheck.messages.selectAuditResult'), trigger: 'change' }
  ],
  reworkReason: [
    { 
      required: true, 
      message: t('qualityCheck.messages.inputReworkReason'), 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (formData.auditResult === 'rework' && !value) {
          callback(new Error(t('qualityCheck.messages.inputReworkReason')));
        } else {
          callback();
        }
      }
    }
  ],
  reworkRequirement: [
    { 
      required: true, 
      message: t('qualityCheck.dialog.inputReworkRequirement'), 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (formData.auditResult === 'rework' && !value) {
          callback(new Error(t('qualityCheck.dialog.inputReworkRequirement')));
        } else {
          callback();
        }
      }
    }
  ]
};

// 质检结果摘要
const qualityCheckSummary = computed(() => {
  if (!checkItemsSummary.value.length) return null;
  
  const passCount = checkItemsSummary.value.filter(item => 
    item.itemType === 'BOOLEAN' && item.checkResult === 'PASS'
  ).length;
  
  const failCount = checkItemsSummary.value.filter(item => 
    item.itemType === 'BOOLEAN' && item.checkResult === 'FAIL'
  ).length;
  
  const requiredCount = checkItemsSummary.value.filter(item => item.isRequired).length;
  
  return {
    passCount,
    failCount,
    requiredCount,
    totalCount: checkItemsSummary.value.length
  };
});

// 监听质检单变化
watch(
  () => props.qualityCheck,
  async (newQualityCheck) => {
    if (newQualityCheck) {
      formData.qualityCheckNo = newQualityCheck.qualityCheckNo;
      formData.workOrderNo = newQualityCheck.workOrderNo;
      formData.technicianName = newQualityCheck.technicianName;
      formData.actualHours = newQualityCheck.actualHours ? `${newQualityCheck.actualHours}` : '';
      formData.qualityCheckId = newQualityCheck.id;
      formData.auditResult = '';
      formData.reworkReason = '';
      formData.reworkRequirement = '';
      formData.auditRemark = '';
      
      // 加载质检项目详情
      await loadCheckItemsDetail(newQualityCheck.id);
    }
  },
  { immediate: true }
);

// 加载质检项目详情
const loadCheckItemsDetail = async (qualityCheckId: string) => {
  try {
    const detail = await getQualityCheckDetail(qualityCheckId);
    checkItemsSummary.value = detail.checkItems || [];
  } catch (error) {
    console.error('加载质检项目详情失败:', error);
    checkItemsSummary.value = [];
  }
};

// 确认审核
const handleConfirm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    const auditData: QualityCheckAuditForm = {
      qualityCheckId: formData.qualityCheckId,
      auditResult: formData.auditResult as 'passed' | 'rework',
      reworkReason: formData.reworkReason,
      reworkRequirement: formData.reworkRequirement,
      auditRemark: formData.auditRemark
    };
    
    emit('confirm', auditData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 关闭处理
const handleClose = () => {
  formRef.value?.resetFields();
  checkItemsSummary.value = [];
  showDetailSummary.value = false;
  emit('close');
};
</script>

<style scoped>
.check-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 12px;
}

.summary-stats {
  display: flex;
  align-items: center;
}

.check-details {
  margin-top: 12px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-table .cell) {
  padding: 4px 8px;
}
</style>
