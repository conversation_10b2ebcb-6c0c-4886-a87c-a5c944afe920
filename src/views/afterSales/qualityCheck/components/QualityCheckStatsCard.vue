<template>
  <el-row :gutter="20" class="stats-container">
    <el-col :span="4">
      <el-card class="stat-card today-total">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ statistics?.todayTotal || 0 }}</div>
            <div class="stat-label">{{ t('qualityCheck.statistics.todayTotal') }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    
    <el-col :span="4">
      <el-card class="stat-card today-passed">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ statistics?.todayPassed || 0 }}</div>
            <div class="stat-label">{{ t('qualityCheck.statistics.todayPassed') }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    
    <el-col :span="4">
      <el-card class="stat-card today-failed">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ statistics?.todayFailed || 0 }}</div>
            <div class="stat-label">{{ t('qualityCheck.statistics.todayFailed') }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    
    <el-col :span="4">
      <el-card class="stat-card week-total">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ statistics?.weekTotal || 0 }}</div>
            <div class="stat-label">{{ t('qualityCheck.statistics.weekTotal') }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    
    <el-col :span="4">
      <el-card class="stat-card pass-rate">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ formatPercentage(statistics?.passRate) }}</div>
            <div class="stat-label">{{ t('qualityCheck.statistics.passRate') }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    
    <el-col :span="4">
      <el-card class="stat-card avg-time">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ formatHours(statistics?.avgProcessTime) }}</div>
            <div class="stat-label">{{ t('qualityCheck.statistics.avgProcessTime') }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
  
  <!-- 月度趋势图 -->
  <el-card class="trend-card" v-if="statistics?.monthlyTrend?.length">
    <template #header>
      <div class="card-header">
        <span class="card-title">{{ t('qualityCheck.statistics.monthlyTrend') }}</span>
        <el-button size="small" :icon="Refresh" @click="handleRefresh">
          {{ tc('refresh') }}
        </el-button>
      </div>
    </template>
    
    <div class="trend-chart">
      <div class="chart-container" ref="chartContainer">
        <!-- 这里可以集成图表库，如 ECharts -->
        <div class="simple-chart">
          <div class="chart-header">
            <span class="chart-title">质检通过率趋势</span>
          </div>
          <div class="chart-bars">
            <div 
              v-for="(item, index) in statistics.monthlyTrend" 
              :key="index"
              class="chart-bar"
            >
              <div class="bar-container">
                <div 
                  class="bar-fill"
                  :style="{ height: `${item.passRate * 100}%` }"
                ></div>
              </div>
              <div class="bar-label">{{ formatMonth(item.month) }}</div>
              <div class="bar-value">{{ Math.round(item.passRate * 100) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { 
  Document, 
  CircleCheck, 
  CircleClose, 
  Calendar, 
  TrendCharts, 
  Timer,
  Refresh 
} from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { QualityCheckStatistics } from '@/types/afterSales/qualityCheck';

// 组件Props
interface Props {
  statistics: QualityCheckStatistics | null;
  loading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 格式化百分比
const formatPercentage = (value?: number): string => {
  if (value === undefined || value === null) return '0%';
  return `${Math.round(value * 100)}%`;
};

// 格式化小时
const formatHours = (value?: number): string => {
  if (value === undefined || value === null) return '0h';
  return `${value.toFixed(1)}h`;
};

// 格式化月份
const formatMonth = (month: string): string => {
  const date = new Date(month + '-01');
  return date.toLocaleDateString('zh-CN', { month: 'short' });
};

// 刷新处理
const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped>
.stats-container {
  margin-bottom: 20px;
}

.stat-card {
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.today-total {
  border-color: #409eff;
}

.stat-card.today-passed {
  border-color: #67c23a;
}

.stat-card.today-failed {
  border-color: #f56c6c;
}

.stat-card.week-total {
  border-color: #e6a23c;
}

.stat-card.pass-rate {
  border-color: #909399;
}

.stat-card.avg-time {
  border-color: #606266;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
  opacity: 0.8;
}

.today-total .stat-icon {
  color: #409eff;
}

.today-passed .stat-icon {
  color: #67c23a;
}

.today-failed .stat-icon {
  color: #f56c6c;
}

.week-total .stat-icon {
  color: #e6a23c;
}

.pass-rate .stat-icon {
  color: #909399;
}

.avg-time .stat-icon {
  color: #606266;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #606266;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.trend-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 500;
  color: #303133;
}

.trend-chart {
  padding: 20px 0;
}

.chart-container {
  width: 100%;
  height: 200px;
}

.simple-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  text-align: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.chart-bars {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 120px;
  padding: 0 20px;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 60px;
}

.bar-container {
  width: 30px;
  height: 80px;
  background: #f5f7fa;
  border-radius: 4px;
  position: relative;
  margin-bottom: 8px;
}

.bar-fill {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: linear-gradient(to top, #67c23a, #85ce61);
  border-radius: 4px;
  transition: height 0.3s ease;
}

.bar-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.bar-value {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-container .el-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .stat-content {
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .chart-bars {
    padding: 0 10px;
  }
  
  .chart-bar {
    max-width: 40px;
  }
  
  .bar-container {
    width: 20px;
  }
}
</style>
