<template>
  <el-card class="table-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">{{ t('settlement.title') }}</span>
        <div class="header-actions">
          <el-button 
            type="primary" 
            :icon="Download" 
            @click="handleExport"
            :loading="exportLoading"
          >
            {{ t('settlement.actions.export') }}
          </el-button>
          <el-button 
            type="warning" 
            :icon="Promotion" 
            @click="handleBatchPush"
            :disabled="selectedRows.length === 0"
          >
            {{ t('settlement.actions.batchPush') }}
          </el-button>
          <el-button 
            :icon="Refresh" 
            @click="handleRefresh"
            :loading="loading"
          >
            {{ t('settlement.actions.refresh') }}
          </el-button>
        </div>
      </div>
    </template>

    <el-table
      :data="data"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column 
        prop="settlementNo" 
        :label="t('settlement.settlementNo')" 
        width="140"
        fixed="left"
      >
        <template #default="{ row }">
          <el-link type="primary" @click="handleViewDetail(row)">
            {{ row.settlementNo }}
          </el-link>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="workOrderNo" 
        :label="t('settlement.workOrderNo')" 
        width="120"
      />
      
      <el-table-column 
        prop="settlementStatus" 
        :label="t('settlement.settlementStatus')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="getSettlementStatusTagType(row.settlementStatus)"
            size="small"
          >
            {{ t(`settlement.status.${row.settlementStatus}`) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="paymentStatus" 
        :label="t('settlement.paymentStatusLabel')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="getPaymentStatusTagType(row.paymentStatus)"
            size="small"
          >
            {{ t(`settlement.paymentStatus.${row.paymentStatus}`) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="customerName" 
        :label="t('settlement.customerName')" 
        width="100"
      />
      
      <el-table-column 
        prop="vehiclePlate" 
        :label="t('settlement.vehiclePlate')" 
        width="120"
      />
      
      <el-table-column 
        prop="vehicleModel" 
        :label="t('settlement.vehicleModel')" 
        width="100"
      />
      
      <el-table-column 
        prop="technician" 
        :label="t('settlement.technician')" 
        width="100"
      />
      
      <el-table-column 
        prop="serviceAdvisor" 
        :label="t('settlement.serviceAdvisor')" 
        width="100"
      />
      
      <el-table-column 
        prop="totalAmount" 
        :label="t('settlement.totalAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.totalAmount) }}
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="paidAmount" 
        :label="t('settlement.paidAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.paidAmount) }}
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="payableAmount" 
        :label="t('settlement.payableAmount')" 
        width="120"
        align="right"
      >
        <template #default="{ row }">
          ¥{{ formatAmount(row.payableAmount) }}
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="inspectionStatus" 
        :label="t('settlement.inspectionStatus')" 
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag 
            :type="row.inspectionStatus === '质检通过' ? 'success' : 'warning'"
            size="small"
          >
            {{ row.inspectionStatus }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="createdAt" 
        :label="t('settlement.createdAt')" 
        width="160"
        align="center"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>
      
      <el-table-column 
        :label="t('settlement.table.operations')" 
        width="200"
        fixed="right"
        align="center"
      >
        <template #default="{ row }">
          <div class="operation-buttons">
            <el-button 
              v-if="row.settlementStatus === 'pending_settlement'"
              type="primary" 
              size="small" 
              @click="handlePush(row)"
            >
              {{ t('settlement.actions.push') }}
            </el-button>
            
            <el-button 
              v-if="row.paymentStatus !== 'fully_paid'"
              type="success" 
              size="small" 
              @click="handlePayment(row)"
            >
              {{ t('settlement.actions.payment') }}
            </el-button>
            
            <el-button 
              v-if="row.settlementStatus === 'pending_settlement' && row.paymentStatus === 'fully_paid'"
              type="warning" 
              size="small" 
              @click="handleComplete(row)"
            >
              {{ t('settlement.actions.complete') }}
            </el-button>
            
            <el-button 
              type="info" 
              size="small" 
              @click="handleViewDetail(row)"
            >
              {{ t('settlement.actions.detail') }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Download, Promotion, Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { SettlementListItem } from '@/types/afterSales/settlement';

// 组件Props
interface Props {
  data: SettlementListItem[];
  loading?: boolean;
  total: number;
  currentPage: number;
  pageSize: number;
  exportLoading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'push', row: SettlementListItem): void;
  (e: 'payment', row: SettlementListItem): void;
  (e: 'complete', row: SettlementListItem): void;
  (e: 'view-detail', row: SettlementListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'size-change', size: number): void;
  (e: 'selection-change', selection: SettlementListItem[]): void;
  (e: 'export'): void;
  (e: 'batch-push', selection: SettlementListItem[]): void;
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  exportLoading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 选中的行
const selectedRows = ref<SettlementListItem[]>([]);

// 获取结算状态标签类型
const getSettlementStatusTagType = (status: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  const statusMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    pre_settlement: 'info',
    pending_settlement: 'warning',
    completed: 'success',
    cancelled: 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取支付状态标签类型
const getPaymentStatusTagType = (status: string): 'danger' | 'success' | 'warning' | 'info' | 'primary' => {
  const statusMap: Record<string, 'danger' | 'success' | 'warning' | 'info' | 'primary'> = {
    pending: 'danger',
    deposit_paid: 'warning',
    fully_paid: 'success',
    refunding: 'primary',
    refunded: 'info'
  };
  return statusMap[status] || 'info';
};

// 格式化金额
const formatAmount = (amount: number) => {
  return amount.toFixed(2);
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 事件处理
const handlePush = (row: SettlementListItem) => {
  emit('push', row);
};

const handlePayment = (row: SettlementListItem) => {
  emit('payment', row);
};

const handleComplete = (row: SettlementListItem) => {
  emit('complete', row);
};

const handleViewDetail = (row: SettlementListItem) => {
  emit('view-detail', row);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const handleSelectionChange = (selection: SettlementListItem[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

const handleExport = () => {
  emit('export');
};

const handleBatchPush = () => {
  emit('batch-push', selectedRows.value);
};

const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped>
.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 500;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table .cell) {
  padding: 0 8px;
}

:deep(.el-button + .el-button) {
  margin-left: 5px;
}
</style>
