<template>
  <el-card class="mb-20 search-card">
    <template #header>
      <span class="card-title">{{ t('settlement.searchForm.title') }}</span>
    </template>
    
    <el-form :model="localSearchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('settlement.settlementNo')">
            <el-input 
              v-model="localSearchParams.settlementNo" 
              :placeholder="t('settlement.searchForm.settlementNoPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('settlement.workOrderNo')">
            <el-input 
              v-model="localSearchParams.workOrderNo" 
              :placeholder="t('settlement.searchForm.workOrderNoPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('settlement.settlementStatus')">
            <el-select 
              v-model="localSearchParams.settlementStatus" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" value="" />
              <el-option 
                v-for="option in settlementStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('settlement.paymentStatusLabel')">
            <el-select 
              v-model="localSearchParams.paymentStatus" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" value="" />
              <el-option 
                v-for="option in paymentStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('settlement.customerName')">
            <el-input 
              v-model="localSearchParams.customerName" 
              :placeholder="t('settlement.searchForm.customerNamePlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('settlement.vehiclePlate')">
            <el-input 
              v-model="localSearchParams.vehiclePlate" 
              :placeholder="t('settlement.searchForm.vehiclePlatePlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('settlement.technician')">
            <el-input 
              v-model="localSearchParams.technician" 
              :placeholder="t('settlement.searchForm.technicianPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('settlement.serviceAdvisor')">
            <el-input 
              v-model="localSearchParams.serviceAdvisor" 
              :placeholder="t('settlement.searchForm.serviceAdvisorPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item :label="t('settlement.searchForm.amountRange')">
            <div class="amount-range">
              <el-input-number
                v-model="localSearchParams.amountMin"
                :placeholder="t('settlement.searchForm.amountMinPlaceholder')"
                :min="0"
                :precision="2"
                style="width: 45%"
              />
              <span class="range-separator">-</span>
              <el-input-number
                v-model="localSearchParams.amountMax"
                :placeholder="t('settlement.searchForm.amountMaxPlaceholder')"
                :min="0"
                :precision="2"
                style="width: 45%"
              />
            </div>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item :label="t('settlement.createdAt')">
            <el-date-picker
              v-model="createTimeRange"
              type="datetimerange"
              range-separator="-"
              :start-placeholder="tc('startTime')"
              :end-placeholder="tc('endTime')"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24" class="text-right">
          <el-button type="primary" :icon="Search" @click="handleSearch" :loading="loading">
            {{ tc('search') }}
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            {{ tc('reset') }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { Search, Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { SettlementSearchParams } from '@/types/afterSales/settlement';

// 组件Props
interface Props {
  searchParams: SettlementSearchParams;
  loading?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'search', params: SettlementSearchParams): void;
  (e: 'reset'): void;
  (e: 'update:searchParams', params: SettlementSearchParams): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 本地搜索参数
const localSearchParams = reactive<SettlementSearchParams>({ ...props.searchParams });

// 创建时间范围
const createTimeRange = ref<[string, string] | null>(null);

// 结算状态选项
const settlementStatusOptions = computed(() => [
  { label: t('settlement.status.pre_settlement'), value: 'pre_settlement' },
  { label: t('settlement.status.pending_settlement'), value: 'pending_settlement' },
  { label: t('settlement.status.completed'), value: 'completed' },
  { label: t('settlement.status.cancelled'), value: 'cancelled' }
]);

// 支付状态选项
const paymentStatusOptions = computed(() => [
  { label: t('settlement.paymentStatus.pending'), value: 'pending' },
  { label: t('settlement.paymentStatus.deposit_paid'), value: 'deposit_paid' },
  { label: t('settlement.paymentStatus.fully_paid'), value: 'fully_paid' },
  { label: t('settlement.paymentStatus.refunding'), value: 'refunding' },
  { label: t('settlement.paymentStatus.refunded'), value: 'refunded' }
]);

// 监听创建时间范围变化
watch(createTimeRange, (newRange) => {
  if (newRange && newRange.length === 2) {
    localSearchParams.createTimeStart = newRange[0];
    localSearchParams.createTimeEnd = newRange[1];
  } else {
    localSearchParams.createTimeStart = '';
    localSearchParams.createTimeEnd = '';
  }
});

// 监听外部搜索参数变化
watch(
  () => props.searchParams,
  (newParams) => {
    Object.assign(localSearchParams, newParams);
    
    // 更新创建时间范围
    if (newParams.createTimeStart && newParams.createTimeEnd) {
      createTimeRange.value = [newParams.createTimeStart, newParams.createTimeEnd];
    } else {
      createTimeRange.value = null;
    }
  },
  { deep: true }
);

// 搜索处理
const handleSearch = () => {
  emit('search', { ...localSearchParams });
  emit('update:searchParams', { ...localSearchParams });
};

// 重置处理
const handleReset = () => {
  Object.assign(localSearchParams, {
    page: 1,
    pageSize: 20,
    settlementNo: '',
    workOrderNo: '',
    settlementStatus: undefined,
    paymentStatus: undefined,
    customerName: '',
    vehiclePlate: '',
    technician: '',
    serviceAdvisor: '',
    createTimeStart: '',
    createTimeEnd: '',
    amountMin: undefined,
    amountMax: undefined
  });
  createTimeRange.value = null;
  emit('reset');
};
</script>

<style scoped>
.search-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: 500;
  color: #303133;
}

.search-form {
  margin-top: 10px;
}

.amount-range {
  display: flex;
  align-items: center;
  width: 100%;
}

.range-separator {
  margin: 0 10px;
  color: #909399;
}

.text-right {
  text-align: right;
  padding-top: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style>
