<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { 
  getWorkOrderList,
  getWorkOrderDetail,
  createWorkOrder,
  updateWorkOrder,
  deleteWorkOrder,
  changeWorkOrderStatus
} from '@/api/modules/afterSales/workOrder';
import { getServiceAdvisorList, getTechnicianList } from '@/api/modules/masterData';
import type { 
  WorkOrderListItem, 
  WorkOrderSearchParams,
  WorkOrderFormData,
  WorkOrderStatus
} from '@/types/afterSales/workOrder.d.ts';

// 导入子组件
import WorkOrderSearchForm from './components/WorkOrderSearchForm.vue';
import WorkOrderTable from './components/WorkOrderTable.vue';
import WorkOrderFormDialog from './components/WorkOrderFormDialog.vue';
import WorkOrderDetailDialog from './components/WorkOrderDetailDialog.vue';
import WorkOrderStatusDialog from './components/WorkOrderStatusDialog.vue';

const { t, tc } = useModuleI18n('afterSales.workOrder');

// 搜索相关
const searchParams = reactive<WorkOrderSearchParams>({
  workOrderNumber: '',
  status: undefined,
  priority: undefined,
  customerSource: undefined,
  workOrderType: undefined,
  customerName: '',
  customerPhone: '',
  licensePlate: '',
  serviceAdvisor: '',
  technician: '',
  createdAtStart: '',
  createdAtEnd: '',
  page: 1,
  pageSize: 20,
});
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchParams.createdAtStart = newVal[0];
    searchParams.createdAtEnd = newVal[1];
  } else {
    searchParams.createdAtStart = '';
    searchParams.createdAtEnd = '';
  }
});

const workOrderList = ref<WorkOrderListItem[]>([]);
const loading = ref(false);
const serviceAdvisorOptions = ref([]);
const technicianOptions = ref([]);

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
});

// 弹窗相关
const formDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const statusDialogVisible = ref(false);
const currentWorkOrder = ref<WorkOrderListItem | null>(null);
const isEdit = ref(false);

// 字典选项
const statusOptions = computed(() => [
  { label: t('status.draft'), value: 'draft' },
  { label: t('status.pending_confirmation'), value: 'pending_confirmation' },
  { label: t('status.confirmed'), value: 'confirmed' },
  { label: t('status.in_progress'), value: 'in_progress' },
  { label: t('status.completed'), value: 'completed' },
  { label: t('status.cancelled'), value: 'cancelled' },
  { label: t('status.rejected'), value: 'rejected' }
]);

const priorityOptions = computed(() => [
  { label: t('priority.normal'), value: 'normal' },
  { label: t('priority.urgent'), value: 'urgent' }
]);

const customerSourceOptions = computed(() => [
  { label: t('customerSource.appointment'), value: 'appointment' },
  { label: t('customerSource.walk_in'), value: 'walk_in' }
]);

const workOrderTypeOptions = computed(() => [
  { label: t('workOrderType.repair'), value: 'repair' },
  { label: t('workOrderType.maintenance'), value: 'maintenance' },
  { label: t('workOrderType.claim'), value: 'claim' }
]);

// 获取工单列表
const fetchWorkOrderList = async () => {
  loading.value = true;
  try {
    const response = await getWorkOrderList({
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    });
    workOrderList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch work order list:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 获取服务顾问列表
const fetchServiceAdvisorList = async () => {
  try {
    const response = await getServiceAdvisorList();
    serviceAdvisorOptions.value = response.map(item => ({ label: item.name, value: item.name }));
  } catch (error) {
    console.error('Failed to fetch service advisor list:', error);
  }
};

// 获取技师列表
const fetchTechnicianList = async () => {
  try {
    const response = await getTechnicianList();
    technicianOptions.value = response.map(item => ({ label: item.name, value: item.name }));
  } catch (error) {
    console.error('Failed to fetch technician list:', error);
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchWorkOrderList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    workOrderNumber: '',
    status: undefined,
    priority: undefined,
    customerSource: undefined,
    workOrderType: undefined,
    customerName: '',
    customerPhone: '',
    licensePlate: '',
    serviceAdvisor: '',
    technician: '',
    createdAtStart: '',
    createdAtEnd: '',
    page: 1,
    pageSize: 20,
  });
  dateRange.value = null;
  fetchWorkOrderList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchWorkOrderList();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchWorkOrderList();
};

// 新建工单
const handleCreate = () => {
  currentWorkOrder.value = null;
  isEdit.value = false;
  formDialogVisible.value = true;
};

// 编辑工单
const handleEdit = (row: WorkOrderListItem) => {
  currentWorkOrder.value = row;
  isEdit.value = true;
  formDialogVisible.value = true;
};

// 查看详情
const handleViewDetail = (row: WorkOrderListItem) => {
  currentWorkOrder.value = row;
  detailDialogVisible.value = true;
};

// 删除工单
const handleDelete = async (row: WorkOrderListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmDelete'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await deleteWorkOrder(row.workOrderId);
        ElMessage.success(t('messages.deleteSuccess'));
        fetchWorkOrderList();
      } catch (error) {
        console.error('Failed to delete work order:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 取消工单
const handleCancel = async (row: WorkOrderListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmCancel'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await changeWorkOrderStatus(row.workOrderId, 'cancelled', '用户取消');
        ElMessage.success(t('messages.statusChangeSuccess'));
        fetchWorkOrderList();
      } catch (error) {
        console.error('Failed to cancel work order:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 完成工单
const handleComplete = async (row: WorkOrderListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmComplete'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await changeWorkOrderStatus(row.workOrderId, 'completed', '工单完成');
        ElMessage.success(t('messages.statusChangeSuccess'));
        fetchWorkOrderList();
      } catch (error) {
        console.error('Failed to complete work order:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 添加项目
const handleAddItems = (row: WorkOrderListItem) => {
  // 实现添加项目逻辑
  console.log('Add items for work order:', row.workOrderId);
};

// 提交审批
const handleSubmitApproval = (row: WorkOrderListItem) => {
  // 实现提交审批逻辑
  console.log('Submit approval for work order:', row.workOrderId);
};

// 打印工单
const handlePrint = (row: WorkOrderListItem) => {
  // 实现打印逻辑
  console.log('Print work order:', row.workOrderId);
};

// 导出数据
const handleExport = () => {
  // 实现导出逻辑
  console.log('Export work orders');
};

// 表单提交成功
const handleFormSuccess = () => {
  formDialogVisible.value = false;
  fetchWorkOrderList();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchServiceAdvisorList();
  fetchTechnicianList();
  fetchWorkOrderList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索表单 -->
    <WorkOrderSearchForm
      v-model:search-params="searchParams"
      v-model:date-range="dateRange"
      :service-advisor-options="serviceAdvisorOptions"
      :technician-options="technicianOptions"
      :status-options="statusOptions"
      :priority-options="priorityOptions"
      :customer-source-options="customerSourceOptions"
      :work-order-type-options="workOrderTypeOptions"
      @search="handleSearch"
      @reset="resetSearch"
    />

    <!-- 操作按钮 -->
    <el-card class="mb-20 operation-card">
      <el-button type="primary" :icon="Plus" @click="handleCreate">
        {{ t('actions.create') }}
      </el-button>
      <el-button type="success" :icon="Download" @click="handleExport">
        {{ t('actions.export') }}
      </el-button>
    </el-card>

    <!-- 数据表格 -->
    <WorkOrderTable
      :work-order-list="workOrderList"
      :loading="loading"
      :pagination="pagination"
      @view-detail="handleViewDetail"
      @edit="handleEdit"
      @delete="handleDelete"
      @cancel="handleCancel"
      @complete="handleComplete"
      @add-items="handleAddItems"
      @submit-approval="handleSubmitApproval"
      @print="handlePrint"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 工单表单弹窗 -->
    <WorkOrderFormDialog
      v-model:visible="formDialogVisible"
      :work-order="currentWorkOrder"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 工单详情弹窗 -->
    <WorkOrderDetailDialog
      v-model:visible="detailDialogVisible"
      :work-order="currentWorkOrder"
    />

    <!-- 状态变更弹窗 -->
    <WorkOrderStatusDialog
      v-model:visible="statusDialogVisible"
      :work-order="currentWorkOrder"
      @success="handleFormSuccess"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.operation-card {
  margin-bottom: 20px;
  
  .el-button {
    margin-right: 10px;
  }
}
</style>
