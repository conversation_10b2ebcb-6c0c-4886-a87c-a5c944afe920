<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue';
import { ElDialog, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElRow, ElCol, ElInputNumber, ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { createWorkOrder, updateWorkOrder } from '@/api/modules/afterSales/workOrder';
import type { WorkOrderListItem, WorkOrderFormData } from '@/types/afterSales/workOrder.d.ts';

interface Props {
  visible: boolean;
  workOrder: WorkOrderListItem | null;
  isEdit: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.workOrder');

const formRef = ref();
const loading = ref(false);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const dialogTitle = computed(() => {
  return props.isEdit ? t('dialog.editTitle') : t('dialog.createTitle');
});

const formData = reactive<WorkOrderFormData>({
  status: 'draft',
  priority: 'normal',
  customerSource: 'appointment',
  workOrderType: 'maintenance',
  customerName: '',
  customerPhone: '',
  licensePlate: '',
  vehicleModel: '',
  vehicleVin: '',
  serviceAdvisor: '',
  technician: '',
  estimatedAmount: 0,
  actualAmount: 0,
  paymentStatus: 'unpaid',
  description: '',
  notes: ''
});

const formRules = {
  customerName: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  customerPhone: [
    { required: true, message: '请输入客户电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  licensePlate: [
    { required: true, message: '请输入车牌号', trigger: 'blur' }
  ],
  vehicleModel: [
    { required: true, message: '请输入车型', trigger: 'blur' }
  ],
  serviceAdvisor: [
    { required: true, message: '请选择服务顾问', trigger: 'change' }
  ],
  technician: [
    { required: true, message: '请选择技师', trigger: 'change' }
  ],
  estimatedAmount: [
    { required: true, message: '请输入预估金额', trigger: 'blur' }
  ]
};

const statusOptions = [
  { label: t('status.draft'), value: 'draft' },
  { label: t('status.pending_confirmation'), value: 'pending_confirmation' },
  { label: t('status.confirmed'), value: 'confirmed' },
  { label: t('status.in_progress'), value: 'in_progress' },
  { label: t('status.completed'), value: 'completed' },
  { label: t('status.cancelled'), value: 'cancelled' },
  { label: t('status.rejected'), value: 'rejected' }
];

const priorityOptions = [
  { label: t('priority.normal'), value: 'normal' },
  { label: t('priority.urgent'), value: 'urgent' }
];

const customerSourceOptions = [
  { label: t('customerSource.appointment'), value: 'appointment' },
  { label: t('customerSource.walk_in'), value: 'walk_in' }
];

const workOrderTypeOptions = [
  { label: t('workOrderType.repair'), value: 'repair' },
  { label: t('workOrderType.maintenance'), value: 'maintenance' },
  { label: t('workOrderType.claim'), value: 'claim' }
];

const paymentStatusOptions = [
  { label: t('paymentStatus.unpaid'), value: 'unpaid' },
  { label: t('paymentStatus.partial'), value: 'partial' },
  { label: t('paymentStatus.paid'), value: 'paid' }
];

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    status: 'draft',
    priority: 'normal',
    customerSource: 'appointment',
    workOrderType: 'maintenance',
    customerName: '',
    customerPhone: '',
    licensePlate: '',
    vehicleModel: '',
    vehicleVin: '',
    serviceAdvisor: '',
    technician: '',
    estimatedAmount: 0,
    actualAmount: 0,
    paymentStatus: 'unpaid',
    description: '',
    notes: ''
  });
  formRef.value?.clearValidate();
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.isEdit && props.workOrder) {
      // 编辑模式，填充数据
      Object.assign(formData, {
        ...props.workOrder,
        workOrderId: props.workOrder.workOrderId
      });
    } else {
      // 新建模式，重置表单
      resetForm();
    }
  }
});

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    if (props.isEdit && props.workOrder) {
      await updateWorkOrder(props.workOrder.workOrderId, formData);
      ElMessage.success(t('messages.updateSuccess'));
    } else {
      await createWorkOrder(formData);
      ElMessage.success(t('messages.createSuccess'));
    }

    emit('success');
  } catch (error) {
    console.error('Failed to submit work order:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    @close="resetForm"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4>{{ t('form.basicInfo') }}</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('searchForm.status')" prop="status">
              <el-select v-model="formData.status" style="width: 100%">
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('searchForm.priority')" prop="priority">
              <el-select v-model="formData.priority" style="width: 100%">
                <el-option
                  v-for="option in priorityOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('searchForm.customerSource')" prop="customerSource">
              <el-select v-model="formData.customerSource" style="width: 100%">
                <el-option
                  v-for="option in customerSourceOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('searchForm.workOrderType')" prop="workOrderType">
              <el-select v-model="formData.workOrderType" style="width: 100%">
                <el-option
                  v-for="option in workOrderTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 客户信息 -->
      <div class="form-section">
        <h4>{{ t('form.customerInfo') }}</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('searchForm.customerName')" prop="customerName">
              <el-input v-model="formData.customerName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('searchForm.customerPhone')" prop="customerPhone">
              <el-input v-model="formData.customerPhone" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 车辆信息 -->
      <div class="form-section">
        <h4>{{ t('form.vehicleInfo') }}</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('searchForm.licensePlate')" prop="licensePlate">
              <el-input v-model="formData.licensePlate" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('table.vehicleModel')" prop="vehicleModel">
              <el-input v-model="formData.vehicleModel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="VIN码" prop="vehicleVin">
          <el-input v-model="formData.vehicleVin" />
        </el-form-item>
      </div>

      <!-- 服务信息 -->
      <div class="form-section">
        <h4>{{ t('form.serviceInfo') }}</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('searchForm.serviceAdvisor')" prop="serviceAdvisor">
              <el-input v-model="formData.serviceAdvisor" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('searchForm.technician')" prop="technician">
              <el-input v-model="formData.technician" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('table.estimatedAmount')" prop="estimatedAmount">
              <el-input-number v-model="formData.estimatedAmount" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('table.paymentStatus')" prop="paymentStatus">
              <el-select v-model="formData.paymentStatus" style="width: 100%">
                <el-option
                  v-for="option in paymentStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 备注 -->
      <el-form-item :label="t('form.notes')" prop="notes">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          :placeholder="t('form.notesPlaceholder')"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
        >
          {{ tc('confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.form-section {
  margin-bottom: 20px;
  
  h4 {
    margin-bottom: 15px;
    color: #409eff;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 5px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  .el-button {
    margin-left: 10px;
  }
}
</style>
