<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue';
import { ElDialog, ElForm, ElFormItem, ElSelect, ElOption, ElInput, ElButton, ElDescriptions, ElDescriptionsItem, ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { changeWorkOrderStatus } from '@/api/modules/afterSales/workOrder';
import type { WorkOrderListItem, WorkOrderStatus } from '@/types/afterSales/workOrder.d.ts';

interface Props {
  visible: boolean;
  workOrder: WorkOrderListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.workOrder');

const formRef = ref();
const loading = ref(false);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const formData = reactive({
  newStatus: '' as WorkOrderStatus,
  reason: '',
  notes: ''
});

const formRules = {
  newStatus: [
    { required: true, message: '请选择新状态', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入变更原因', trigger: 'blur' }
  ]
};

const statusOptions = [
  { label: t('status.draft'), value: 'draft' },
  { label: t('status.pending_confirmation'), value: 'pending_confirmation' },
  { label: t('status.confirmed'), value: 'confirmed' },
  { label: t('status.in_progress'), value: 'in_progress' },
  { label: t('status.completed'), value: 'completed' },
  { label: t('status.cancelled'), value: 'cancelled' },
  { label: t('status.rejected'), value: 'rejected' }
];

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    newStatus: '',
    reason: '',
    notes: ''
  });
  formRef.value?.clearValidate();
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// 提交状态变更
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    
    if (!props.workOrder) return;
    
    loading.value = true;
    await changeWorkOrderStatus(props.workOrder.workOrderId, formData.newStatus, formData.reason);
    
    ElMessage.success(t('messages.statusChangeSuccess'));
    emit('success');
  } catch (error) {
    console.error('Failed to change work order status:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('dialog.statusChangeTitle')"
    width="600px"
    @close="resetForm"
  >
    <div v-if="workOrder">
      <!-- 工单信息 -->
      <el-descriptions :column="2" border class="mb-20">
        <el-descriptions-item :label="t('table.workOrderNumber')">
          {{ workOrder.workOrderNumber }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.status')">
          {{ t(`status.${workOrder.status}`) }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.customerName')">
          {{ workOrder.customerName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.licensePlate')">
          {{ workOrder.licensePlate }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.vehicleModel')">
          {{ workOrder.vehicleModel }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.serviceAdvisor')">
          {{ workOrder.serviceAdvisor }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 状态变更表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="新状态" prop="newStatus">
          <el-select v-model="formData.newStatus" style="width: 100%">
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.value === workOrder.status"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item :label="t('dialog.statusChangeReason')" prop="reason">
          <el-input
            v-model="formData.reason"
            :placeholder="t('dialog.statusChangeReasonPlaceholder')"
          />
        </el-form-item>
        
        <el-form-item :label="t('form.notes')" prop="notes">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="3"
            :placeholder="t('form.notesPlaceholder')"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
        >
          {{ tc('confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.mb-20 {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  .el-button {
    margin-left: 10px;
  }
}
</style>
