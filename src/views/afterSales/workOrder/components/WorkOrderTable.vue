<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElButton, ElPagination, ElTag } from 'element-plus';
import { View, Edit, Delete, Check, Printer, Plus } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrderListItem } from '@/types/afterSales/workOrder.d.ts';

interface Props {
  workOrderList: WorkOrderListItem[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

interface Emits {
  (e: 'view-detail', row: WorkOrderListItem): void;
  (e: 'edit', row: WorkOrderListItem): void;
  (e: 'delete', row: WorkOrderListItem): void;
  (e: 'cancel', row: WorkOrderListItem): void;
  (e: 'complete', row: WorkOrderListItem): void;
  (e: 'add-items', row: WorkOrderListItem): void;
  (e: 'submit-approval', row: WorkOrderListItem): void;
  (e: 'print', row: WorkOrderListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.workOrder');

const getStatusType = (status: string) => {
  const statusMap = {
    draft: 'info',
    pending_confirmation: 'warning',
    confirmed: 'primary',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger',
    rejected: 'danger'
  };
  return statusMap[status as keyof typeof statusMap] || 'info';
};

const getPriorityType = (priority: string) => {
  return priority === 'urgent' ? 'danger' : 'primary';
};

const canEdit = (row: WorkOrderListItem) => {
  return ['draft', 'pending_confirmation'].includes(row.status);
};

const canDelete = (row: WorkOrderListItem) => {
  return row.status === 'draft';
};

const canCancel = (row: WorkOrderListItem) => {
  return ['pending_confirmation', 'confirmed'].includes(row.status);
};

const canComplete = (row: WorkOrderListItem) => {
  return row.status === 'in_progress';
};

const canAddItems = (row: WorkOrderListItem) => {
  return ['draft', 'confirmed', 'in_progress'].includes(row.status);
};

const canSubmitApproval = (row: WorkOrderListItem) => {
  return row.status === 'draft';
};

const formatAmount = (amount: number) => {
  return `¥${amount.toFixed(2)}`;
};

const handleViewDetail = (row: WorkOrderListItem) => {
  emit('view-detail', row);
};

const handleEdit = (row: WorkOrderListItem) => {
  emit('edit', row);
};

const handleDelete = (row: WorkOrderListItem) => {
  emit('delete', row);
};

const handleCancel = (row: WorkOrderListItem) => {
  emit('cancel', row);
};

const handleComplete = (row: WorkOrderListItem) => {
  emit('complete', row);
};

const handleAddItems = (row: WorkOrderListItem) => {
  emit('add-items', row);
};

const handleSubmitApproval = (row: WorkOrderListItem) => {
  emit('submit-approval', row);
};

const handlePrint = (row: WorkOrderListItem) => {
  emit('print', row);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePageSizeChange = (size: number) => {
  emit('page-size-change', size);
};
</script>

<template>
  <el-card class="table-card">
    <el-table
      :data="workOrderList"
      v-loading="loading"
      style="width: 100%"
      border
      :empty-text="tc('noData')"
    >
      <el-table-column type="index" :label="tc('index')" width="60" />
      <el-table-column prop="workOrderNumber" :label="t('table.workOrderNumber')" min-width="140" />
      <el-table-column prop="status" :label="t('table.status')" min-width="120">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ t(`status.${scope.row.status}`) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="priority" :label="t('table.priority')" min-width="100">
        <template #default="scope">
          <el-tag :type="getPriorityType(scope.row.priority)" size="small">
            {{ t(`priority.${scope.row.priority}`) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="customerSource" :label="t('table.customerSource')" min-width="120">
        <template #default="scope">
          {{ t(`customerSource.${scope.row.customerSource}`) }}
        </template>
      </el-table-column>
      <el-table-column prop="workOrderType" :label="t('table.workOrderType')" min-width="100">
        <template #default="scope">
          {{ t(`workOrderType.${scope.row.workOrderType}`) }}
        </template>
      </el-table-column>
      <el-table-column prop="customerName" :label="t('table.customerName')" min-width="100" />
      <el-table-column prop="customerPhone" :label="t('table.customerPhone')" min-width="120" />
      <el-table-column prop="licensePlate" :label="t('table.licensePlate')" min-width="100" />
      <el-table-column prop="vehicleModel" :label="t('table.vehicleModel')" min-width="120" />
      <el-table-column prop="serviceAdvisor" :label="t('table.serviceAdvisor')" min-width="100" />
      <el-table-column prop="technician" :label="t('table.technician')" min-width="100" />
      <el-table-column prop="estimatedAmount" :label="t('table.estimatedAmount')" min-width="120">
        <template #default="scope">
          {{ formatAmount(scope.row.estimatedAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="actualAmount" :label="t('table.actualAmount')" min-width="120">
        <template #default="scope">
          {{ scope.row.actualAmount ? formatAmount(scope.row.actualAmount) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" :label="t('table.createdAt')" min-width="140" />
      <el-table-column prop="completedAt" :label="t('table.completedAt')" min-width="140">
        <template #default="scope">
          {{ scope.row.completedAt || '-' }}
        </template>
      </el-table-column>
      <el-table-column :label="tc('operations')" width="300" fixed="right">
        <template #default="scope">
          <el-button type="primary" :icon="View" link @click="handleViewDetail(scope.row)">
            {{ t('actions.view') }}
          </el-button>
          
          <el-button 
            v-if="canEdit(scope.row)"
            type="primary" 
            :icon="Edit" 
            link 
            @click="handleEdit(scope.row)"
          >
            {{ t('actions.edit') }}
          </el-button>
          
          <el-button 
            v-if="canAddItems(scope.row)"
            type="warning" 
            :icon="Plus" 
            link 
            @click="handleAddItems(scope.row)"
          >
            {{ t('actions.addItems') }}
          </el-button>
          
          <el-button 
            v-if="canSubmitApproval(scope.row)"
            type="success" 
            :icon="Check" 
            link 
            @click="handleSubmitApproval(scope.row)"
          >
            {{ t('actions.submitApproval') }}
          </el-button>
          
          <el-button 
            v-if="canComplete(scope.row)"
            type="success" 
            :icon="Check" 
            link 
            @click="handleComplete(scope.row)"
          >
            {{ t('actions.complete') }}
          </el-button>
          
          <el-button 
            v-if="canCancel(scope.row)"
            type="danger" 
            :icon="Delete" 
            link 
            @click="handleCancel(scope.row)"
          >
            {{ t('actions.cancel') }}
          </el-button>
          
          <el-button 
            v-if="canDelete(scope.row)"
            type="danger" 
            :icon="Delete" 
            link 
            @click="handleDelete(scope.row)"
          >
            {{ t('actions.delete') }}
          </el-button>
          
          <el-button type="info" :icon="Printer" link @click="handlePrint(scope.row)">
            {{ t('actions.print') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
