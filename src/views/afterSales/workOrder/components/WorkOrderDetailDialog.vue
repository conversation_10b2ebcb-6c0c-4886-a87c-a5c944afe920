<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElDialog, ElDescriptions, ElDescriptionsItem, ElTable, ElTableColumn, ElTabs, ElTabPane, ElTag } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getWorkOrderDetail } from '@/api/modules/afterSales/workOrder';
import type { WorkOrderListItem, WorkOrderDetail } from '@/types/afterSales/workOrder.d.ts';

interface Props {
  visible: boolean;
  workOrder: WorkOrderListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.workOrder');

const loading = ref(false);
const workOrderDetail = ref<WorkOrderDetail | null>(null);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const getStatusType = (status: string) => {
  const statusMap = {
    draft: 'info',
    pending_confirmation: 'warning',
    confirmed: 'primary',
    in_progress: 'primary',
    completed: 'success',
    cancelled: 'danger',
    rejected: 'danger'
  };
  return statusMap[status as keyof typeof statusMap] || 'info';
};

const getPriorityType = (priority: string) => {
  return priority === 'urgent' ? 'danger' : 'primary';
};

const formatAmount = (amount: number) => {
  return `¥${amount.toFixed(2)}`;
};

// 监听弹窗显示状态
watch(() => props.visible, async (newVal) => {
  if (newVal && props.workOrder) {
    loading.value = true;
    try {
      workOrderDetail.value = await getWorkOrderDetail(props.workOrder.workOrderId);
    } catch (error) {
      console.error('Failed to fetch work order detail:', error);
    } finally {
      loading.value = false;
    }
  }
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('dialog.detailTitle')"
    width="1000px"
  >
    <div v-loading="loading">
      <div v-if="workOrderDetail">
        <!-- 基本信息 -->
        <el-descriptions :column="3" border class="mb-20">
          <el-descriptions-item :label="t('table.workOrderNumber')">
            {{ workOrderDetail.workOrderNumber }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.status')">
            <el-tag :type="getStatusType(workOrderDetail.status)">
              {{ t(`status.${workOrderDetail.status}`) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.priority')">
            <el-tag :type="getPriorityType(workOrderDetail.priority)" size="small">
              {{ t(`priority.${workOrderDetail.priority}`) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.customerSource')">
            {{ t(`customerSource.${workOrderDetail.customerSource}`) }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.workOrderType')">
            {{ t(`workOrderType.${workOrderDetail.workOrderType}`) }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.paymentStatus')">
            {{ t(`paymentStatus.${workOrderDetail.paymentStatus}`) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 客户和车辆信息 -->
        <el-descriptions :column="2" border class="mb-20">
          <el-descriptions-item :label="t('table.customerName')">
            {{ workOrderDetail.customerName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.customerPhone')">
            {{ workOrderDetail.customerPhone }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.licensePlate')">
            {{ workOrderDetail.licensePlate }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.vehicleModel')">
            {{ workOrderDetail.vehicleModel }}
          </el-descriptions-item>
          <el-descriptions-item label="VIN码">
            {{ workOrderDetail.vehicleVin }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.serviceAdvisor')">
            {{ workOrderDetail.serviceAdvisor }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.technician')">
            {{ workOrderDetail.technician }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 金额信息 -->
        <el-descriptions :column="2" border class="mb-20">
          <el-descriptions-item :label="t('table.estimatedAmount')">
            {{ formatAmount(workOrderDetail.estimatedAmount) }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.actualAmount')">
            {{ workOrderDetail.actualAmount ? formatAmount(workOrderDetail.actualAmount) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.createdAt')">
            {{ workOrderDetail.createdAt }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.completedAt')">
            {{ workOrderDetail.completedAt || '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 服务项目详情 -->
        <el-tabs type="border-card">
          <el-tab-pane :label="t('form.serviceItems')" name="serviceItems">
            <el-table :data="workOrderDetail.serviceItems" border>
              <el-table-column prop="name" label="服务名称" />
              <el-table-column prop="description" label="描述" />
              <el-table-column prop="price" label="单价">
                <template #default="scope">
                  {{ formatAmount(scope.row.price) }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" />
              <el-table-column prop="total" label="小计">
                <template #default="scope">
                  {{ formatAmount(scope.row.total) }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane :label="t('form.partItems')" name="partItems">
            <el-table :data="workOrderDetail.partItems" border>
              <el-table-column prop="partNumber" label="配件编号" />
              <el-table-column prop="partName" label="配件名称" />
              <el-table-column prop="brand" label="品牌" />
              <el-table-column prop="price" label="单价">
                <template #default="scope">
                  {{ formatAmount(scope.row.price) }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" />
              <el-table-column prop="total" label="小计">
                <template #default="scope">
                  {{ formatAmount(scope.row.total) }}
                </template>
              </el-table-column>
              <el-table-column prop="supplier" label="供应商" />
            </el-table>
          </el-tab-pane>

          <el-tab-pane :label="t('form.laborItems')" name="laborItems">
            <el-table :data="workOrderDetail.laborItems" border>
              <el-table-column prop="operation" label="操作项目" />
              <el-table-column prop="description" label="描述" />
              <el-table-column prop="standardHours" label="标准工时" />
              <el-table-column prop="actualHours" label="实际工时" />
              <el-table-column prop="hourlyRate" label="工时费率">
                <template #default="scope">
                  {{ formatAmount(scope.row.hourlyRate) }}
                </template>
              </el-table-column>
              <el-table-column prop="total" label="小计">
                <template #default="scope">
                  {{ formatAmount(scope.row.total) }}
                </template>
              </el-table-column>
              <el-table-column prop="technician" label="技师" />
            </el-table>
          </el-tab-pane>
        </el-tabs>

        <!-- 备注信息 -->
        <div class="notes-section mt-20">
          <h4>备注信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="工单描述">
              {{ workOrderDetail.description || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="客户备注">
              {{ workOrderDetail.notes || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="技师备注">
              {{ workOrderDetail.technicianNotes || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.notes-section {
  h4 {
    margin-bottom: 15px;
    color: #409eff;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 5px;
  }
}
</style>
