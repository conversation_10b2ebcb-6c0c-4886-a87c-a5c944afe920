<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { 
  ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElCard, ElRow, ElCol, ElText, ElMessage 
} from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { queryVehicleInfo, queryVehicleInfoByOwnerIc } from '@/api/modules/afterSales/checkin';
import type { CheckinListItem, CheckinFormData } from '@/types/afterSales/checkin.d.ts';

interface Props {
  visible: boolean;
  isEdit: boolean;
  recordData: CheckinListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', data: CheckinFormData): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.checkin');

const dialogFormRef = ref();
const licensePlateQuery = ref('');
const ownerIcQuery = ref(''); // 新增车主IC查询
const isManualInput = ref(false); // 是否手动录入模式

const dialogForm = reactive<CheckinFormData>({
  licensePlate: '',
  vin: '',
  vehicleModel: '',
  vehicleConfiguration: '',
  color: '',
  mileage: undefined,
  vehicleAge: undefined,
  repairPersonName: '',
  repairPersonPhone: '',
  serviceAdvisor: '',
  inspectionOrderNo: null, // 更新字段名
  serviceType: '维修',
  notes: '',
  status: 'NORMAL', // 新增状态字段
  isDeleted: false
});

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const isVehicleInfoReadOnly = computed(() =>
  !!(props.isEdit && props.recordData?.checkinId) && !isManualInput.value
);

// 获取当前用户信息（模拟）
const getCurrentUser = () => {
  return {
    name: '当前用户',
  };
};

// 重置表单
const resetDialogForm = () => {
  if (dialogFormRef.value) {
    dialogFormRef.value.resetFields();
  }
  Object.assign(dialogForm, {
    licensePlate: '',
    vin: '',
    vehicleModel: '',
    vehicleConfiguration: '',
    color: '',
    mileage: undefined,
    vehicleAge: undefined,
    repairPersonName: '',
    repairPersonPhone: '',
    serviceAdvisor: '',
    inspectionOrderNo: null,
    serviceType: '维修',
    notes: '',
    status: 'NORMAL',
    isDeleted: false
  });
  licensePlateQuery.value = '';
  ownerIcQuery.value = '';
  isManualInput.value = false;
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.isEdit && props.recordData) {
      // 编辑模式，填充数据
      Object.assign(dialogForm, JSON.parse(JSON.stringify(props.recordData)));
      licensePlateQuery.value = props.recordData.licensePlate;
    } else {
      // 新增模式，重置表单并设置默认值
      resetDialogForm();
      const currentUser = getCurrentUser();
      dialogForm.serviceAdvisor = currentUser.name;
    }
  }
});

// 车牌号查询
const handleQueryVehicleInfo = async () => {
  if (!licensePlateQuery.value) {
    ElMessage.warning(t('licensePlatePlaceholder'));
    return;
  }
  await queryVehicleInfoByType('licensePlate', licensePlateQuery.value);
};

// 车主IC查询
const handleQueryVehicleInfoByOwnerIc = async () => {
  if (!ownerIcQuery.value) {
    ElMessage.warning(t('ownerIcPlaceholder'));
    return;
  }
  await queryVehicleInfoByType('ownerIc', ownerIcQuery.value);
};

// 统一的车辆信息查询方法
const queryVehicleInfoByType = async (type: 'licensePlate' | 'ownerIc', value: string) => {
  try {
    const vehicleInfo = type === 'licensePlate'
      ? await queryVehicleInfo(value)
      : await queryVehicleInfoByOwnerIc(value);

    if (vehicleInfo) {
      dialogForm.licensePlate = vehicleInfo.licensePlate || '';
      dialogForm.vin = vehicleInfo.vin || '';
      dialogForm.vehicleModel = vehicleInfo.vehicleModel || '';
      dialogForm.vehicleConfiguration = vehicleInfo.vehicleConfiguration || '';
      dialogForm.color = vehicleInfo.color || '';
      dialogForm.mileage = vehicleInfo.mileage || undefined;
      dialogForm.vehicleAge = vehicleInfo.vehicleAge;
      isManualInput.value = false;
      ElMessage.success(tc('operationSuccessful'));
    } else {
      // 查询无结果，提示可以手动录入
      ElMessage.warning(t('vehicleInfoNotFoundCanManualInput'));
      isManualInput.value = true;
      // 清空车辆信息，但保留查询的车牌号或IC
      if (type === 'licensePlate') {
        dialogForm.licensePlate = value;
      }
      dialogForm.vin = '';
      dialogForm.vehicleModel = '';
      dialogForm.vehicleConfiguration = '';
      dialogForm.color = '';
      dialogForm.mileage = undefined;
      dialogForm.vehicleAge = undefined;
    }
  } catch (error) {
    console.error('Failed to query vehicle info:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 重置车辆信息
const resetVehicleInfoInDialog = () => {
  dialogForm.licensePlate = '';
  dialogForm.vin = '';
  dialogForm.vehicleModel = '';
  dialogForm.vehicleConfiguration = '';
  dialogForm.color = '';
  dialogForm.mileage = undefined;
  dialogForm.vehicleAge = undefined;
};

// 表单提交
const handleSubmit = async () => {
  const formEl = dialogFormRef.value;
  if (!formEl) return;

  formEl.validate(async (valid: boolean) => {
    if (valid) {
      const submitForm: CheckinFormData = {
        ...dialogForm,
        vehicleAge: undefined, // 客户端计算，不提交
      };
      emit('submit', submitForm);
    }
  });
};

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? t('editCheckinRecord') : t('addCheckinRecord')"
    width="600px"
    class="checkin-dialog"
    @close="resetDialogForm"
  >
    <el-form :model="dialogForm" ref="dialogFormRef" label-position="top" class="dialog-form-modern">
      <!-- 顶部查询框 -->
      <el-row :gutter="20" class="mb-20">
        <el-col :span="12">
          <el-form-item :label="t('licensePlate')">
            <el-input
              v-model="licensePlateQuery"
              :placeholder="t('licensePlatePlaceholder')"
              @keyup.enter="handleQueryVehicleInfo"
              clearable
              :disabled="isEdit && !!recordData?.checkinId"
            >
              <template #append>
                <el-button :icon="Search" @click="handleQueryVehicleInfo">{{ t('query') }}</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="t('ownerIc')">
            <el-input
              v-model="ownerIcQuery"
              :placeholder="t('ownerIcPlaceholder')"
              @keyup.enter="handleQueryVehicleInfoByOwnerIc"
              clearable
              :disabled="isEdit && !!recordData?.checkinId"
            >
              <template #append>
                <el-button :icon="Search" @click="handleQueryVehicleInfoByOwnerIc">{{ t('query') }}</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 手动录入提示 -->
      <el-row v-if="isManualInput" class="mb-20">
        <el-col :span="24">
          <el-text type="warning">{{ t('manualInputTip') }}</el-text>
        </el-col>
      </el-row>

      <!-- 车辆信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('vehicleInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              :label="t('licensePlate')"
              prop="licensePlate"
              :rules="[{ required: true, message: tc('required'), trigger: 'blur' }]"
            >
              <el-input
                v-model="dialogForm.licensePlate"
                :disabled="isVehicleInfoReadOnly"
                :placeholder="isManualInput ? t('licensePlatePlaceholder') : ''"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vin')" prop="vin">
              <el-input
                v-model="dialogForm.vin"
                :disabled="isVehicleInfoReadOnly"
                :placeholder="isManualInput ? t('vinPlaceholder') : ''"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleModel')" prop="vehicleModel">
              <el-input
                v-model="dialogForm.vehicleModel"
                :disabled="isVehicleInfoReadOnly"
                :placeholder="isManualInput ? t('vehicleModelPlaceholder') : ''"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleConfiguration')" prop="vehicleConfiguration">
              <el-input
                v-model="dialogForm.vehicleConfiguration"
                :disabled="isVehicleInfoReadOnly"
                :placeholder="isManualInput ? t('vehicleConfigurationPlaceholder') : ''"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('color')" prop="color">
              <el-input
                v-model="dialogForm.color"
                :disabled="isVehicleInfoReadOnly"
                :placeholder="isManualInput ? t('colorPlaceholder') : ''"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('mileage')" prop="mileage">
              <el-input
                v-model.number="dialogForm.mileage"
                type="number"
                :placeholder="t('mileagePlaceholder')"
                :disabled="isVehicleInfoReadOnly"
              >
                <template #append>{{ t('mileageUnit') }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleAge')" prop="vehicleAge">
              <el-input
                v-model="dialogForm.vehicleAge"
                :disabled="true"
                :placeholder="t('vehicleAgeAutoCalculated')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('customerInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item 
              :label="t('repairPersonName')" 
              prop="repairPersonName" 
              :rules="[{ required: true, message: tc('required'), trigger: 'blur' }]"
            >
              <el-input v-model="dialogForm.repairPersonName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item 
              :label="t('repairPersonPhone')" 
              prop="repairPersonPhone" 
              :rules="[{ required: true, message: tc('required'), trigger: 'blur' }]"
            >
              <el-input v-model="dialogForm.repairPersonPhone" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item 
              :label="t('serviceAdvisor')" 
              prop="serviceAdvisor" 
              :rules="[{ required: true, message: tc('required'), trigger: 'blur' }]"
            >
              <el-input v-model="dialogForm.serviceAdvisor" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 服务类型 -->
      <el-form-item 
        :label="t('serviceType')" 
        prop="serviceType" 
        :rules="[{ required: true, message: tc('required'), trigger: 'change' }]"
      >
        <el-input v-model="dialogForm.serviceType" :disabled="true" />
      </el-form-item>

      <!-- 备注信息 -->
      <el-form-item :label="t('notes')" prop="notes">
        <el-input
          v-model="dialogForm.notes"
          type="textarea"
          :rows="3"
          :placeholder="t('notesPlaceholder')"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="closeDialog">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit">{{ tc('save') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.checkin-dialog {
  .dialog-form-modern {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
  .dialog-footer-buttons {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
    .el-button {
      margin-left: 10px;
    }
  }
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
