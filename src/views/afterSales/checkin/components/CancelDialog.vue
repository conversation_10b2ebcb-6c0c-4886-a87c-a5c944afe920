<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinListItem } from '@/types/afterSales/checkin.d.ts';

interface Props {
  visible: boolean;
  recordData: CheckinListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', cancelReason: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.checkin');

const cancelReason = ref('');
const formRef = ref();

const rules = {
  cancelReason: [
    { required: true, message: t('cancelReasonRequired'), trigger: 'blur' },
    { min: 5, message: t('cancelReasonMinLength'), trigger: 'blur' }
  ]
};

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    cancelReason.value = '';
    formRef.value?.clearValidate();
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirm = async () => {
  try {
    await formRef.value?.validate();
    emit('confirm', cancelReason.value);
  } catch (error) {
    console.error('Form validation failed:', error);
  }
};
</script>

<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="t('cancelRecord')"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="cancel-dialog-content">
      <p class="confirm-text">
        {{ t('confirmCancelRecord', { checkinId: recordData?.checkinId }) }}
      </p>
      
      <el-form
        ref="formRef"
        :model="{ cancelReason }"
        :rules="rules"
        label-position="top"
      >
        <el-form-item :label="t('cancelReason')" prop="cancelReason" required>
          <el-input
            v-model="cancelReason"
            type="textarea"
            :rows="4"
            :placeholder="t('cancelReasonPlaceholder')"
            maxlength="500"
            show-word-limit
          />
          <div class="form-tip">{{ t('cancelReasonTip') }}</div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('close') }}</el-button>
        <el-button type="primary" @click="handleConfirm">{{ tc('confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.cancel-dialog-content {
  .confirm-text {
    margin-bottom: 20px;
    font-size: 14px;
    color: #606266;
  }

  .form-tip {
    margin-top: 5px;
    font-size: 12px;
    color: #909399;
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 10px;
  }
}
</style>
