<script setup lang="ts">
import { computed } from 'vue';
import { ElDialog, ElCard, ElRow, ElCol, ElForm, ElFormItem, ElInput, ElButton } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinListItem } from '@/types/afterSales/checkin.d.ts';

interface Props {
  visible: boolean;
  recordData: CheckinListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.checkin');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const currentDetails = computed(() => props.recordData || {});

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirm = () => {
  emit('confirm');
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${t('createInspectionOrder')} (${t('basedOnCheckin')} ${currentDetails.checkinId})`"
    width="600px"
    class="create-inspection-dialog"
    :close-on-click-modal="false"
  >
    <el-form label-position="top" class="confirmation-form">
      <!-- 车辆信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('vehicleInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('licensePlate')">
              <el-input :model-value="currentDetails.licensePlate" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vin')">
              <el-input :model-value="currentDetails.vin" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleModel')">
              <el-input :model-value="currentDetails.vehicleModel" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleConfiguration')">
              <el-input :model-value="currentDetails.vehicleConfiguration" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('color')">
              <el-input :model-value="currentDetails.color" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('mileage')">
              <el-input 
                :model-value="currentDetails.mileage ? `${currentDetails.mileage} ${t('mileageUnit')}` : '-'" 
                readonly 
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleAge')">
              <el-input :model-value="currentDetails.vehicleAge || '-'" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('customerInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('repairPersonName')">
              <el-input :model-value="currentDetails.repairPersonName" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('repairPersonPhone')">
              <el-input :model-value="currentDetails.repairPersonPhone" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('serviceAdvisor')">
              <el-input :model-value="currentDetails.serviceAdvisor" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 服务信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('serviceInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('serviceType')">
              <el-input :model-value="currentDetails.serviceType" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('notes')">
              <el-input 
                :model-value="currentDetails.notes || '-'" 
                type="textarea" 
                :rows="3" 
                readonly 
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 确认信息 -->
      <div class="confirmation-text">
        <p>{{ t('confirmCreateInspectionOrder') }}</p>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm">{{ tc('confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.create-inspection-dialog {
  .confirmation-form {
    .mb-20 {
      margin-bottom: 20px;
    }
    
    :deep(.el-input__inner) {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
    }
    
    :deep(.el-textarea__inner) {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
    }
  }

  .confirmation-text {
    margin: 20px 0;
    padding: 15px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    
    p {
      margin: 0;
      color: #409eff;
      font-weight: 500;
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 10px;
  }
}
</style>
