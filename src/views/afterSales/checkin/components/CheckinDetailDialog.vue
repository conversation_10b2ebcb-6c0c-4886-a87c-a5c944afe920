<script setup lang="ts">
import { computed } from 'vue';
import { ElDialog, ElCard, ElRow, ElCol, ElForm, ElFormItem, ElInput, ElTag } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinListItem } from '@/types/afterSales/checkin.d.ts';

interface Props {
  visible: boolean;
  recordData: CheckinListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.checkin');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const currentDetails = computed(() => props.recordData || {});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${t('viewDetails')} (${currentDetails.checkinId})`"
    width="600px"
    class="checkin-details-dialog"
  >
    <el-form label-position="top" class="detail-form">
      <!-- 车辆信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('vehicleInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('licensePlate')">
              <el-input :model-value="currentDetails.licensePlate" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vin')">
              <el-input :model-value="currentDetails.vin" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleModel')">
              <el-input :model-value="currentDetails.vehicleModel" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleConfiguration')">
              <el-input :model-value="currentDetails.vehicleConfiguration" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('color')">
              <el-input :model-value="currentDetails.color" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('mileage')">
              <el-input
                :model-value="currentDetails.mileage ? `${currentDetails.mileage} ${t('mileageUnit')}` : '-'"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('vehicleAge')">
              <el-input :model-value="currentDetails.vehicleAge || '-'" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 客户信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('customerInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('repairPersonName')">
              <el-input :model-value="currentDetails.repairPersonName" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('repairPersonPhone')">
              <el-input :model-value="currentDetails.repairPersonPhone" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('serviceAdvisor')">
              <el-input :model-value="currentDetails.serviceAdvisor" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 服务信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('serviceInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('serviceType')">
              <el-input :model-value="currentDetails.serviceType" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('notes')">
              <el-input
                :model-value="currentDetails.notes || '-'"
                type="textarea"
                :rows="3"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 状态信息区域 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('statusInfo') }}</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('status')">
              <el-tag :type="currentDetails.status === 'NORMAL' ? 'success' : 'danger'">
                {{ currentDetails.status === 'NORMAL' ? t('statusNormal') : t('statusCancelled') }}
              </el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('inspectionOrderNo')">
              <el-input :model-value="currentDetails.inspectionOrderNo || '-'" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="currentDetails.status === 'CANCELLED'">
            <el-form-item :label="t('cancelReason')">
              <el-input
                :model-value="currentDetails.cancelReason || '-'"
                type="textarea"
                :rows="2"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('createdAt')">
              <el-input :model-value="currentDetails.createdAt" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('updatedAt')">
              <el-input :model-value="currentDetails.updatedAt" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ tc('close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.checkin-details-dialog {
  .detail-form {
    .mb-20 {
      margin-bottom: 20px;
    }

    :deep(.el-input__inner) {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
    }

    :deep(.el-textarea__inner) {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
