<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElButton, ElPagination, ElTag } from 'element-plus';
import { View, Edit, Delete, Plus, Close } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinListItem } from '@/types/afterSales/checkin.d.ts';

interface Props {
  checkinList: CheckinListItem[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

interface Emits {
  (e: 'view-details', row: CheckinListItem): void;
  (e: 'edit-record', row: CheckinListItem): void;
  (e: 'delete-record', row: CheckinListItem): void;
  (e: 'cancel-record', row: CheckinListItem): void; // 新增取消事件
  (e: 'create-repair-order', row: CheckinListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.checkin');

const handleViewDetails = (row: CheckinListItem) => {
  emit('view-details', row);
};

const handleEditRecord = (row: CheckinListItem) => {
  emit('edit-record', row);
};

const handleDeleteRecord = (row: CheckinListItem) => {
  emit('delete-record', row);
};

const handleCancelRecord = (row: CheckinListItem) => {
  emit('cancel-record', row);
};

const handleCreateRepairOrder = (row: CheckinListItem) => {
  emit('create-repair-order', row);
};

// 判断是否可以取消登记
const canCancelRecord = (row: CheckinListItem) => {
  const result = row.status === 'NORMAL' && !row.inspectionOrderNo;
  return result;
};

// 判断是否可以创建环检单
const canCreateInspectionOrder = (row: CheckinListItem) => {
  const result = row.status === 'NORMAL' && !row.inspectionOrderNo;
  return result;
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePageSizeChange = (size: number) => {
  emit('page-size-change', size);
};
</script>

<template>
  <el-card class="table-card">
    <el-table
      :data="checkinList"
      v-loading="loading"
      style="width: 100%"
      border
      :empty-text="tc('noData')"
    >
      <el-table-column type="index" :label="tc('index')" width="60" />
      <el-table-column prop="checkinId" :label="t('checkinId')" min-width="120" />
      <el-table-column prop="licensePlate" :label="t('licensePlate')" min-width="120" />
      <el-table-column prop="vin" :label="t('vin')" min-width="140" />
      <el-table-column prop="vehicleModel" :label="t('vehicleModel')" min-width="120" />
      <el-table-column prop="vehicleConfiguration" :label="t('vehicleConfiguration')" min-width="150" />
      <el-table-column prop="color" :label="t('color')" min-width="80" />
      <el-table-column prop="mileage" :label="t('mileage')" min-width="100">
        <template #default="scope">
          {{ scope.row.mileage ? `${scope.row.mileage} ${t('mileageUnit')}` : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="vehicleAge" :label="t('vehicleAge')" min-width="80">
        <template #default="scope">
          {{ scope.row.vehicleAge || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="repairPersonName" :label="t('repairPersonName')" min-width="120" />
      <el-table-column prop="repairPersonPhone" :label="t('repairPersonPhone')" min-width="120" />
      <el-table-column prop="serviceAdvisor" :label="t('serviceAdvisor')" min-width="100" />
      <el-table-column prop="inspectionOrderNo" :label="t('inspectionOrderNo')" min-width="140">
        <template #default="scope">
          {{ scope.row.inspectionOrderNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="serviceType" :label="t('serviceType')" min-width="100" />
      <el-table-column prop="status" :label="t('status')" min-width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'NORMAL' ? 'success' : 'danger'">
            {{ scope.row.status === 'NORMAL' ? t('statusNormal') : t('statusCancelled') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" :label="t('createdAt')" min-width="120" />
      <el-table-column prop="updatedAt" :label="t('updatedAt')" min-width="120" />
      <el-table-column :label="tc('operations')" width="320" fixed="right">
        <template #default="scope">
          <!-- 调试信息 -->
          <div style="font-size: 10px; color: #999; margin-bottom: 5px;" v-if="false">
            Status: {{ scope.row.status || 'undefined' }} |
            InspectionNo: {{ scope.row.inspectionOrderNo || 'null' }}
          </div>

          <el-button type="primary" :icon="View" link @click="handleViewDetails(scope.row)">
            {{ t('viewDetails') }}
          </el-button>
          <el-button
            type="primary"
            :icon="Edit"
            link
            @click="handleEditRecord(scope.row)"
            v-if="scope.row.status === 'NORMAL'"
          >
            {{ tc('edit') }}
          </el-button>
          <el-button
            type="warning"
            :icon="Close"
            link
            @click="handleCancelRecord(scope.row)"
            v-if="canCancelRecord(scope.row)"
          >
            {{ t('cancel') }}
          </el-button>
          <el-button
            type="success"
            :icon="Plus"
            link
            @click="handleCreateRepairOrder(scope.row)"
            v-if="canCreateInspectionOrder(scope.row)"
          >
            {{ t('createInspectionOrder') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
