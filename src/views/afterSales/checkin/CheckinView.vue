<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import {
  ElMessage,
  ElMessageBox,
  ElCard,
  ElButton,
  ElRow,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker
} from 'element-plus';
import { Plus, Download, Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getCheckinList,
  addCheckinRecord,
  updateCheckinRecord,
  deleteCheckinRecord,
  createRelatedRepairOrder,
  cancelCheckin
} from '@/api/modules/afterSales/checkin';
import type { 
  CheckinListItem, 
  CheckinListParams, 
  CheckinFormData 
} from '@/types/afterSales/checkin.d.ts';

// 导入子组件
import CheckinTable from './components/CheckinTable.vue';
import CheckinFormDialog from './components/CheckinFormDialog.vue';
import CheckinDetailDialog from './components/CheckinDetailDialog.vue';
import CancelDialog from './components/CancelDialog.vue';
import CreateInspectionDialog from './components/CreateInspectionDialog.vue';

const { t, tc } = useModuleI18n('afterSales.checkin');

// 搜索相关
const searchParams = reactive<CheckinListParams>({
  checkinId: '',
  licensePlate: '',
  repairPersonName: '',
  repairPersonPhone: '',
  ownerIc: '', // 新增车主IC
  status: undefined, // 新增状态筛选
  createdAtStart: '',
  createdAtEnd: '',
});
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchParams.createdAtStart = newVal[0];
    searchParams.createdAtEnd = newVal[1];
  } else {
    searchParams.createdAtStart = '';
    searchParams.createdAtEnd = '';
  }
});

const checkinList = ref<CheckinListItem[]>([]);
const loading = ref(false);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 弹窗相关
const formDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const cancelDialogVisible = ref(false); // 新增取消弹窗
const createInspectionDialogVisible = ref(false); // 新增创建环检单弹窗
const isEdit = ref(false);
const currentRecord = ref<CheckinListItem | null>(null);

// 获取到店登记列表
const fetchCheckinList = async () => {
  loading.value = true;
  try {
    const response = await getCheckinList({
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    });
    checkinList.value = response.list;
    pagination.total = response.total;

    // 调试信息：检查数据结构
    console.log('Checkin List Data:', response.list);
    console.log('Sample record fields:', response.list[0] ? Object.keys(response.list[0]) : 'No data');
    if (response.list.length > 0) {
      console.log('First record status:', response.list[0].status);
      console.log('First record inspectionOrderNo:', response.list[0].inspectionOrderNo);
    }
  } catch (error) {
    console.error('Failed to fetch checkin list:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchCheckinList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    checkinId: '',
    licensePlate: '',
    repairPersonName: '',
    repairPersonPhone: '',
    ownerIc: '',
    status: undefined,
    createdAtStart: '',
    createdAtEnd: '',
  });
  dateRange.value = null;
  pagination.page = 1;
  fetchCheckinList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchCheckinList();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchCheckinList();
};

// 新增登记
const handleAddCheckinRecord = () => {
  isEdit.value = false;
  currentRecord.value = null;
  formDialogVisible.value = true;
};

// 编辑登记
const handleEditRecord = (row: CheckinListItem) => {
  isEdit.value = true;
  currentRecord.value = row;
  formDialogVisible.value = true;
};

// 删除登记
const handleDeleteRecord = async (row: CheckinListItem) => {
  ElMessageBox.confirm(
    tc('confirmDelete', { item: row.checkinId }),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await deleteCheckinRecord(row.checkinId);
        ElMessage.success(tc('operationSuccessful'));
        fetchCheckinList();
      } catch (error) {
        console.error('Failed to delete checkin record:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 创建环检单
const handleCreateRepairOrder = async (row: CheckinListItem) => {
  if (row.inspectionOrderNo) {
    ElMessage.warning(t('inspectionOrderAlreadyExists'));
    return;
  }
  // 显示创建环检单确认对话框
  currentRecord.value = row;
  createInspectionDialogVisible.value = true;
};

// 确认创建环检单
const handleConfirmCreateInspection = async () => {
  if (!currentRecord.value) return;

  try {
    await createRelatedRepairOrder(currentRecord.value.checkinId);
    ElMessage.success(t('inspectionOrderCreatedSuccess'));
    createInspectionDialogVisible.value = false;
    fetchCheckinList();
  } catch (error) {
    console.error('Failed to create inspection order:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 取消登记
const handleCancelRecord = (row: CheckinListItem) => {
  currentRecord.value = row;
  cancelDialogVisible.value = true;
};

// 确认取消登记
const handleConfirmCancel = async (cancelReason: string) => {
  if (!currentRecord.value) return;

  try {
    await cancelCheckin(currentRecord.value.checkinId, { cancelReason });
    ElMessage.success(t('cancelSuccess'));
    cancelDialogVisible.value = false;
    fetchCheckinList();
  } catch (error) {
    console.error('Failed to cancel checkin record:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 查看详情
const handleViewDetails = (row: CheckinListItem) => {
  currentRecord.value = row;
  detailDialogVisible.value = true;
};

// 表单提交
const handleFormSubmit = async (formData: CheckinFormData) => {
  try {
    if (isEdit.value && currentRecord.value) {
      await updateCheckinRecord(currentRecord.value.checkinId, formData);
    } else {
      await addCheckinRecord(formData);
    }
    ElMessage.success(tc('operationSuccessful'));
    formDialogVisible.value = false;
    fetchCheckinList();
  } catch (error) {
    console.error('Failed to save checkin record:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 导出
const handleExport = () => {
  ElMessage.info(tc('exportingReport'));
  console.log('Exporting data with params:', searchParams);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchCheckinList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('checkinList') }}</h1>

    <!-- 搜索表单 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('checkinId')">
              <el-input
                v-model="searchParams.checkinId"
                :placeholder="t('checkinIdPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('licensePlate')">
              <el-input
                v-model="searchParams.licensePlate"
                :placeholder="t('licensePlatePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('repairPersonName')">
              <el-input
                v-model="searchParams.repairPersonName"
                :placeholder="t('repairPersonNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('repairPersonPhone')">
              <el-input
                v-model="searchParams.repairPersonPhone"
                :placeholder="t('repairPersonPhonePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('ownerIc')">
              <el-input
                v-model="searchParams.ownerIc"
                :placeholder="t('ownerIcPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('createdAt')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="-"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('status')">
              <el-select
                v-model="searchParams.status"
                :placeholder="t('statusPlaceholder')"
                clearable
                style="width: 100%"
              >
                <el-option :label="t('statusAll')" value="" />
                <el-option :label="t('statusNormal')" value="NORMAL" />
                <el-option :label="t('statusCancelled')" value="CANCELLED" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6"></el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right;">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ tc('search') }}
            </el-button>
            <el-button @click="resetSearch">
              {{ tc('reset') }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20" justify="end">
        <el-col :span="24" style="text-align: right;">
          <el-button type="primary" :icon="Plus" @click="handleAddCheckinRecord">
            {{ t('createRecord') }}
          </el-button>
          <el-button :icon="Download" @click="handleExport">
            {{ t('export') }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <CheckinTable
      :checkin-list="checkinList"
      :loading="loading"
      :pagination="pagination"
      @view-details="handleViewDetails"
      @edit-record="handleEditRecord"
      @delete-record="handleDeleteRecord"
      @cancel-record="handleCancelRecord"
      @create-repair-order="handleCreateRepairOrder"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 新增/编辑弹窗 -->
    <CheckinFormDialog
      v-model:visible="formDialogVisible"
      :is-edit="isEdit"
      :record-data="currentRecord"
      @submit="handleFormSubmit"
    />

    <!-- 详情弹窗 -->
    <CheckinDetailDialog
      v-model:visible="detailDialogVisible"
      :record-data="currentRecord"
    />

    <!-- 取消登记弹窗 -->
    <CancelDialog
      v-model:visible="cancelDialogVisible"
      :record-data="currentRecord"
      @confirm="handleConfirmCancel"
    />

    <!-- 创建环检单确认弹窗 -->
    <CreateInspectionDialog
      v-model:visible="createInspectionDialogVisible"
      :record-data="currentRecord"
      @confirm="handleConfirmCreateInspection"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.operation-card {
  margin-bottom: 20px;
}

.search-card {
  .search-form {
    .el-form-item {
      margin-bottom: 18px;
    }
  }
}
</style>
