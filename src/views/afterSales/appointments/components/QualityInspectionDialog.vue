<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { AppointmentDetail } from '@/types/afterSales/appointments.d.ts';
import {
  ElDialog,
  ElDescriptions,
  ElDescriptionsItem,
  ElTag,
  ElTable,
  ElTableColumn,
  ElButton,
  ElInput
} from 'element-plus';

interface Props {
  visible: boolean;
  appointmentData: AppointmentDetail | null;
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: { name: string; phone: string }): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.appointments');

const editableServiceContact = ref({ name: '', phone: '' });

// 计算车龄（月）
const calculateVehicleAgeInMonths = (productionDate: string | undefined): string => {
  if (!productionDate) {
    return tc('unknown');
  }
  const production = new Date(productionDate);
  const now = new Date();
  const years = now.getFullYear() - production.getFullYear();
  const months = now.getMonth() - production.getMonth();
  const totalMonths = years * 12 + months;
  return totalMonths >= 0 ? `${totalMonths} ${tc('months')}` : tc('unknown');
};

// 格式化价格显示
const formatPrice = (price: number) => {
  return `¥${price.toLocaleString()}`;
};

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 监听 appointmentData 变化，初始化可编辑的送修人信息
watch(() => props.appointmentData, (newData) => {
  if (newData) {
    editableServiceContact.value = {
      name: newData.serviceContactName,
      phone: newData.serviceContactPhone
    };
  }
}, { immediate: true });

const handleConfirm = () => {
  emit('confirm', editableServiceContact.value);
};

const handleCancel = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('titles.confirmCreateInspection')"
    width="800px"
    class="appointment-detail-dialog"
  >
    <div v-if="appointmentData" v-loading="loading">
      <div
        style="
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f0f9ff;
          border: 1px solid #0ea5e9;
          border-radius: 4px;
        "
      >
        <p style="margin: 0; color: #0369a1; font-weight: 500">
          <i class="el-icon-info"></i> {{ t('messages.confirmCreateInspection') }}
        </p>
      </div>

      <!-- 预约信息 -->
      <el-descriptions :title="t('titles.appointmentInfo')" :column="2" border>
        <el-descriptions-item :label="t('labels.appointmentId')">
          {{ appointmentData.id }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.status')">
          <el-tag
            :type="
              appointmentData.status === 'arrived'
                ? 'success'
                : appointmentData.status === 'not_arrived'
                  ? 'info'
                  : appointmentData.status === 'cancelled'
                    ? 'info'
                    : appointmentData.status === 'pending_payment'
                      ? 'warning'
                      : 'danger'
            "
          >
            {{ t(`statuses.${appointmentData.status}`) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.appointmentTime')">
          {{ appointmentData.appointmentTime }}
          {{ appointmentData.timeSlot }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.serviceType')">
          <el-tag
            :type="appointmentData.serviceType === 'maintenance' ? 'success' : 'warning'"
          >
            {{ t(`serviceTypes.${appointmentData.serviceType}`) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item
          v-if="appointmentData.customerDescription"
          :label="t('labels.customerDescription')"
          :span="2"
        >
          {{ appointmentData.customerDescription }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 客户信息 -->
      <el-descriptions :title="t('titles.customerInfo')" :column="2" border class="mt-20">
        <el-descriptions-item :label="t('labels.reservationContactName')">
          {{ appointmentData.reservationContactName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.reservationContactPhone')">
          {{ appointmentData.reservationContactPhone }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.serviceContactName')">
          <el-input
            v-model="editableServiceContact.name"
            :placeholder="t('placeholders.serviceContactName')"
            style="width: 200px"
          />
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.serviceContactPhone')">
          <el-input
            v-model="editableServiceContact.phone"
            :placeholder="t('placeholders.serviceContactPhone')"
            style="width: 200px"
          />
        </el-descriptions-item>
      </el-descriptions>

      <!-- 车辆信息 -->
      <el-descriptions :title="t('titles.vehicleInfo')" :column="2" border class="mt-20">
        <el-descriptions-item :label="t('labels.licensePlate')">
          {{ appointmentData.licensePlate }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.vin')">
          {{ appointmentData.vin }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.model')">
          {{ appointmentData.model }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.variant')">
          {{ appointmentData.variant }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.color')">
          {{ appointmentData.color }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.mileage')">
          {{
            appointmentData.mileage
              ? appointmentData.mileage + 'km'
              : tc('unknown')
          }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.vehicleAge')">
          {{ calculateVehicleAgeInMonths(appointmentData.productionDate) }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 服务内容确认段 (仅当服务类型为"保养"时显示) -->
      <div v-if="appointmentData.serviceType === 'maintenance'" class="mt-20">
        <h4>{{ t('titles.serviceContent') }} ({{ t('jobTypes.maintenance') }})</h4>
        <div v-if="appointmentData.maintenancePackage" class="service-package">
          <!-- 套餐基本信息 -->
          <el-descriptions :column="2" border class="mb-16">
            <el-descriptions-item :label="t('labels.packageName')">
              {{ appointmentData.maintenancePackage.name }} ({{ appointmentData.maintenancePackage.code }})
            </el-descriptions-item>
            <el-descriptions-item :label="t('labels.estimatedTotal')">
              <strong style="color: #409eff; font-size: 16px">
                {{ formatPrice(appointmentData.maintenancePackage.totalAmount) }}
              </strong>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 工时明细 -->
          <div class="mb-16">
            <h5>{{ t('titles.laborDetails') }}</h5>
            <el-table :data="appointmentData.maintenancePackage.laborItems" border style="width: 100%">
              <el-table-column prop="name" :label="t('headers.itemName')" />
              <el-table-column prop="code" :label="t('headers.itemCode')" width="120" />
              <el-table-column prop="standardHours" :label="t('headers.standardHours')" width="100">
                <template #default="scope">
                  {{ scope.row.standardHours }}h
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" :label="t('headers.unitPrice')" width="100">
                <template #default="scope">
                  {{ formatPrice(scope.row.unitPrice) }}
                </template>
              </el-table-column>
              <el-table-column prop="subtotal" :label="t('headers.subtotal')" width="100">
                <template #default="scope">
                  {{ formatPrice(scope.row.subtotal) }}
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 零件明细 -->
          <div>
            <h5>{{ t('titles.partsDetails') }}</h5>
            <el-table :data="appointmentData.maintenancePackage.partsItems" border style="width: 100%">
              <el-table-column prop="name" :label="t('headers.partsName')" />
              <el-table-column prop="code" :label="t('headers.partsCode')" width="120" />
              <el-table-column prop="quantity" :label="t('headers.quantity')" width="80">
                <template #default="scope">
                  {{ scope.row.quantity }}{{ scope.row.unit }}
                </template>
              </el-table-column>
              <el-table-column prop="unitPrice" :label="t('headers.unitPrice')" width="100">
                <template #default="scope">
                  {{ formatPrice(scope.row.unitPrice) }}
                </template>
              </el-table-column>
              <el-table-column prop="subtotal" :label="t('headers.subtotal')" width="100">
                <template #default="scope">
                  {{ formatPrice(scope.row.subtotal) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 支付信息确认段 (仅当服务类型为"保养"时显示) -->
      <el-descriptions
        v-if="appointmentData.serviceType === 'maintenance'"
        :title="t('titles.paymentInfo')"
        :column="2"
        border
        class="mt-20"
      >
        <el-descriptions-item :label="t('labels.paymentMethod')">
          <el-tag>
            {{ t(`paymentMethods.${appointmentData.paymentMethod || 'online'}`) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item :label="t('labels.paymentStatus')">
          <el-tag
            :type="
              appointmentData.paymentStatus === 'paid'
                ? 'success'
                : appointmentData.paymentStatus === 'unpaid'
                  ? 'warning'
                  : 'info'
            "
          >
            {{ t(`paymentStatuses.${appointmentData.paymentStatus}`) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="loading"
        >
          {{ t('buttons.confirmCreation') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.appointment-detail-dialog {
  .mt-20 {
    margin-top: 20px;
  }

  .mt-10 {
    margin-top: 10px;
  }

  .service-package {
    .total-amount {
      text-align: right;
    }
  }

  .dialog-footer-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
</style>
