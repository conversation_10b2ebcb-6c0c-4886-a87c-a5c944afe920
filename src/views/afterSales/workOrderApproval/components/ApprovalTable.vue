<!-- 审批表格组件 -->
<template>
  <div class="approval-table">
    <el-table
      :data="data"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      :height="600"
    >
      <el-table-column
        prop="approvalNo"
        :label="t('workOrderApproval.approvalNo')"
        width="140"
        fixed="left"
      />

      <el-table-column
        prop="approvalType"
        :label="t('workOrderApproval.approvalType')"
        width="120"
      >
        <template #default="{ row }">
          <el-tag :type="getApprovalTypeTagType(row.approvalType)" size="small">
            {{ getApprovalTypeText(row.approvalType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="submitterName"
        :label="t('workOrderApproval.submitter')"
        width="120"
      />

      <el-table-column
        prop="submitTime"
        :label="t('workOrderApproval.submitTime')"
        width="160"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.submitTime) }}
        </template>
      </el-table-column>

      <el-table-column
        prop="orderNo"
        :label="t('workOrderApproval.orderNo')"
        width="140"
      />

      <el-table-column
        prop="requestReason"
        :label="t('workOrderApproval.requestReason')"
        width="200"
        show-overflow-tooltip
      />

      <el-table-column
        v-if="type === 'pending'"
        prop="timeoutStatus"
        :label="t('workOrderApproval.timeoutStatus')"
        width="120"
      >
        <template #default="{ row }">
          <el-tag :type="getTimeoutStatusTagType(row.timeoutStatus)" size="small">
            {{ getTimeoutStatusText(row.timeoutStatus) }}
          </el-tag>
          <div v-if="row.remainingTime" class="remaining-time">
            {{ row.remainingTime }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="customerName"
        :label="t('workOrderApproval.customerName')"
        width="120"
      />

      <el-table-column
        :label="t('workOrderApproval.vehicleInfo')"
        width="180"
      >
        <template #default="{ row }">
          <div class="vehicle-info">
            <div>{{ row.licensePlate }}</div>
            <div class="vehicle-model">{{ row.vehicleModel }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="storeName"
        :label="t('workOrderApproval.storeName')"
        width="120"
      />

      <el-table-column
        prop="currentLevel"
        :label="t('workOrderApproval.currentLevel')"
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="getLevelTagType(row.currentLevel)" size="small">
            {{ getLevelText(row.currentLevel) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 已审批状态列 -->
      <el-table-column
        v-if="type === 'completed'"
        prop="approvalResult"
        :label="t('workOrderApproval.approvalResult')"
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="getResultTagType(row.approvalResult)" size="small">
            {{ getResultText(row.approvalResult) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        v-if="type === 'completed'"
        prop="approvalTime"
        :label="t('workOrderApproval.approvalTime')"
        width="160"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.approvalTime) }}
        </template>
      </el-table-column>

      <el-table-column
        v-if="type === 'completed'"
        prop="approverName"
        :label="t('workOrderApproval.approverName')"
        width="120"
      />

      <el-table-column
        :label="t('workOrderApproval.actions.title')"
        width="200"
        fixed="right"
      >
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              v-if="type === 'pending' && row.approvalType === 'claim_approval'"
              type="primary"
              size="small"
              @click="handleClaimApproval(row)"
            >
              {{ t('workOrderApproval.actions.approve') }}
            </el-button>

            <el-button
              v-if="type === 'pending' && row.approvalType === 'cancel_approval'"
              type="warning"
              size="small"
              @click="handleCancelApproval(row)"
            >
              {{ t('workOrderApproval.actions.approve') }}
            </el-button>

            <el-button
              type="info"
              size="small"
              plain
              @click="handleViewDetail(row)"
            >
              {{ t('workOrderApproval.actions.detail') }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type {
  PendingApprovalListItem,
  CompletedApprovalListItem,
  WorkOrderApprovalType,
  TimeoutStatus,
  ApprovalLevel,
  ApprovalResult
} from '@/types/afterSales/workOrderApproval';

interface Props {
  data: (PendingApprovalListItem | CompletedApprovalListItem)[];
  loading: boolean;
  total: number;
  currentPage: number;
  pageSize: number;
  type: 'pending' | 'completed';
}

interface Emits {
  (e: 'page-change', page: number): void;
  (e: 'size-change', size: number): void;
  (e: 'claim-approval', item: PendingApprovalListItem): void;
  (e: 'cancel-approval', item: PendingApprovalListItem): void;
  (e: 'view-detail', item: PendingApprovalListItem | CompletedApprovalListItem): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '';
  return dateTime.replace('T', ' ').slice(0, 19);
};

// 获取审批类型标签类型
const getApprovalTypeTagType = (type: WorkOrderApprovalType) => {
  const typeMap: Record<WorkOrderApprovalType, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    'claim_approval': 'primary',
    'cancel_approval': 'warning'
  };
  return typeMap[type] || 'info';
};

// 获取审批类型文本
const getApprovalTypeText = (type: WorkOrderApprovalType) => {
  const typeMap: Record<WorkOrderApprovalType, string> = {
    'claim_approval': t('workOrderApproval.claimApproval'),
    'cancel_approval': t('workOrderApproval.cancelApproval')
  };
  return typeMap[type] || type;
};

// 获取超时状态标签类型
const getTimeoutStatusTagType = (status: TimeoutStatus) => {
  const statusMap: Record<TimeoutStatus, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    'normal': 'success',
    'about_to_timeout': 'warning',
    'timeout': 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取超时状态文本
const getTimeoutStatusText = (status: TimeoutStatus) => {
  const statusMap: Record<TimeoutStatus, string> = {
    'normal': t('workOrderApproval.timeoutStatusSelect.normal'),
    'about_to_timeout': t('workOrderApproval.timeoutStatusSelect.aboutToTimeout'),
    'timeout': t('workOrderApproval.timeoutStatusSelect.timeout')
  };
  return statusMap[status] || status;
};

// 获取级别标签类型
const getLevelTagType = (level: ApprovalLevel) => {
  const levelMap: Record<ApprovalLevel, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    'first_level': 'primary',
    'second_level': 'success'
  };
  return levelMap[level] || 'info';
};

// 获取级别文本
const getLevelText = (level: ApprovalLevel) => {
  const levelMap: Record<ApprovalLevel, string> = {
    'first_level': t('workOrderApproval.level.firstLevel'),
    'second_level': t('workOrderApproval.level.secondLevel')
  };
  return levelMap[level] || level;
};

// 获取结果标签类型
const getResultTagType = (result: ApprovalResult) => {
  const resultMap: Record<ApprovalResult, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    'approved': 'success',
    'rejected': 'danger'
  };
  return resultMap[result] || 'info';
};

// 获取结果文本
const getResultText = (result: ApprovalResult) => {
  const resultMap: Record<ApprovalResult, string> = {
    'approved': t('workOrderApproval.result.approved'),
    'rejected': t('workOrderApproval.result.rejected')
  };
  return resultMap[result] || result;
};

// 事件处理
const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const handleClaimApproval = (item: PendingApprovalListItem) => {
  emit('claim-approval', item);
};

const handleCancelApproval = (item: PendingApprovalListItem) => {
  emit('cancel-approval', item);
};

const handleViewDetail = (item: PendingApprovalListItem | CompletedApprovalListItem) => {
  emit('view-detail', item);
};
</script>

<style scoped lang="scss">
.approval-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;

  .vehicle-info {
    .vehicle-model {
      font-size: 12px;
      color: #909399;
      margin-top: 2px;
    }
  }

  .remaining-time {
    font-size: 12px;
    color: #f56c6c;
    margin-top: 2px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
  }

  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f5f7fa;
          color: #303133;
          font-weight: 600;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}
</style>
