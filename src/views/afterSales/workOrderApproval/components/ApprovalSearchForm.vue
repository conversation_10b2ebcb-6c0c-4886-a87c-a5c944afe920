<!-- 审批搜索表单组件 -->
<template>
  <div class="approval-search-form">
    <el-form
      :model="localSearchParams"
      label-width="120px"
      class="search-form"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('workOrderApproval.approvalNo')">
            <el-input
              v-model="localSearchParams.approvalNo"
              :placeholder="t('workOrderApproval.searchForm.approvalNoPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workOrderApproval.approvalType')">
            <el-select
              v-model="localSearchParams.approvalType"
              :placeholder="tc('pleaseSelect')"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in approvalTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workOrderApproval.submitter')">
            <el-input
              v-model="localSearchParams.submitterName"
              :placeholder="t('workOrderApproval.searchForm.submitterPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workOrderApproval.orderNo')">
            <el-input
              v-model="localSearchParams.orderNo"
              :placeholder="t('workOrderApproval.searchForm.orderNoPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('workOrderApproval.customerName')">
            <el-input
              v-model="localSearchParams.customerName"
              :placeholder="t('workOrderApproval.searchForm.customerNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('workOrderApproval.licensePlate')">
            <el-input
              v-model="localSearchParams.licensePlate"
              :placeholder="t('workOrderApproval.searchForm.licensePlatePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 待审批Tab的筛选条件 -->
        <template v-if="tabType === 'pending'">
          <el-col :span="6">
            <el-form-item :label="t('workOrderApproval.timeoutStatus')">
              <el-select
                v-model="localSearchParams.timeoutStatus"
                :placeholder="tc('pleaseSelect')"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in timeoutStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('workOrderApproval.currentLevel')">
              <el-select
                v-model="localSearchParams.currentLevel"
                :placeholder="tc('pleaseSelect')"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in approvalLevelOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('workOrderApproval.submitTime')">
              <el-date-picker
                v-model="submitTimeRange"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="tc('startTime')"
                :end-placeholder="tc('endTime')"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                clearable
              />
            </el-form-item>
          </el-col>
        </template>

        <!-- 已审批Tab的筛选条件 -->
        <template v-else>
          <el-col :span="6">
            <el-form-item :label="t('workOrderApproval.approvalResult')">
              <el-select
                v-model="localSearchParams.approvalResult"
                :placeholder="tc('pleaseSelect')"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="option in approvalResultOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item :label="t('workOrderApproval.approvalTime')">
              <el-date-picker
                v-model="approvalTimeRange"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="tc('startTime')"
                :end-placeholder="tc('endTime')"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                clearable
              />
            </el-form-item>
          </el-col>
        </template>
      </el-row>

      <el-row>
        <el-col :span="24">
          <div class="search-actions">
            <el-button
              type="primary"
              :icon="Search"
              @click="handleSearch"
            >
              {{ tc('search') }}
            </el-button>
            <el-button
              :icon="RefreshLeft"
              @click="handleReset"
            >
              {{ tc('reset') }}
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Search, RefreshLeft } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type {
  WorkOrderApprovalListParams,
  ApprovalOption
} from '@/types/afterSales/workOrderApproval';

interface Props {
  searchParams: WorkOrderApprovalListParams;
  approvalTypeOptions: ApprovalOption[];
  tabType: 'pending' | 'completed';
}

interface Emits {
  (e: 'update:searchParams', params: WorkOrderApprovalListParams): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

// 本地搜索参数
const localSearchParams = ref<WorkOrderApprovalListParams>({ ...props.searchParams });

// 日期范围
const submitTimeRange = ref<[string, string] | null>(null);
const approvalTimeRange = ref<[string, string] | null>(null);

// 超时状态选项
const timeoutStatusOptions = computed(() => [
  { label: t('workOrderApproval.timeoutStatusSelect.normal'), value: 'normal' },
  { label: t('workOrderApproval.timeoutStatusSelect.aboutToTimeout'), value: 'about_to_timeout' },
  { label: t('workOrderApproval.timeoutStatusSelect.timeout'), value: 'timeout' }
]);

// 审批级别选项
const approvalLevelOptions = computed(() => [
  { label: t('workOrderApproval.level.firstLevel'), value: 'first_level' },
  { label: t('workOrderApproval.level.secondLevel'), value: 'second_level' }
]);

// 审批结果选项
const approvalResultOptions = computed(() => [
  { label: t('workOrderApproval.result.approved'), value: 'approved' },
  { label: t('workOrderApproval.result.rejected'), value: 'rejected' }
]);

// 监听提交时间范围变化
watch(submitTimeRange, (newRange) => {
  if (newRange && newRange.length === 2) {
    localSearchParams.value.submitTimeStart = newRange[0];
    localSearchParams.value.submitTimeEnd = newRange[1];
  } else {
    localSearchParams.value.submitTimeStart = '';
    localSearchParams.value.submitTimeEnd = '';
  }
});

// 监听审批时间范围变化
watch(approvalTimeRange, (newRange) => {
  if (newRange && newRange.length === 2) {
    localSearchParams.value.approvalTimeStart = newRange[0];
    localSearchParams.value.approvalTimeEnd = newRange[1];
  } else {
    localSearchParams.value.approvalTimeStart = '';
    localSearchParams.value.approvalTimeEnd = '';
  }
});

// 监听本地搜索参数变化
watch(
  localSearchParams,
  (newParams) => {
    emit('update:searchParams', { ...newParams });
  },
  { deep: true }
);

// 监听外部搜索参数变化
watch(
  () => props.searchParams,
  (newParams) => {
    localSearchParams.value = { ...newParams };

    // 更新提交时间范围
    if (newParams.submitTimeStart && newParams.submitTimeEnd) {
      submitTimeRange.value = [newParams.submitTimeStart, newParams.submitTimeEnd];
    } else {
      submitTimeRange.value = null;
    }

    // 更新审批时间范围
    if (newParams.approvalTimeStart && newParams.approvalTimeEnd) {
      approvalTimeRange.value = [newParams.approvalTimeStart, newParams.approvalTimeEnd];
    } else {
      approvalTimeRange.value = null;
    }
  },
  { deep: true }
);

// 搜索处理
const handleSearch = () => {
  emit('search');
};

// 重置处理
const handleReset = () => {
  localSearchParams.value = {
    page: 1,
    pageSize: 10,
    approvalNo: '',
    approvalType: undefined,
    submitterName: '',
    orderNo: '',
    customerName: '',
    licensePlate: '',
    timeoutStatus: undefined,
    currentLevel: undefined,
    submitTimeStart: '',
    submitTimeEnd: '',
    approvalResult: undefined,
    approvalTimeStart: '',
    approvalTimeEnd: ''
  };
  submitTimeRange.value = null;
  approvalTimeRange.value = null;
  emit('reset');
};
</script>

<style scoped lang="scss">
.approval-search-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;

  .search-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .search-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 10px;
  }
}
</style>
