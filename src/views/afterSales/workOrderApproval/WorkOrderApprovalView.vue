<!-- 工单审批主页面 -->
<template>
  <div class="work-order-approval">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ t('workOrderApproval.title') }}</h1>
      <div class="page-actions">
        <el-button
          type="primary"
          :icon="Download"
          @click="handleExport"
          :loading="exportLoading"
        >
          {{ tc('export') }}
        </el-button>
      </div>
    </div>

    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="approval-tabs">
      <el-tab-pane
        :label="`${t('workOrderApproval.pendingApproval')} (${pendingTotal})`"
        name="pending"
      >
        <!-- 搜索表单组件 -->
        <ApprovalSearchForm
          v-model:searchParams="searchParams"
          :approval-type-options="approvalTypeOptions"
          :tab-type="'pending'"
          @search="handleSearch"
          @reset="handleReset"
        />

        <!-- 审批表格组件 -->
        <ApprovalTable
          :data="pendingList"
          :loading="loading"
          :total="pendingTotal"
          :current-page="searchParams.page"
          :page-size="searchParams.pageSize"
          type="pending"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @claim-approval="handleClaimApproval"
          @cancel-approval="handleCancelApproval"
          @view-detail="handleViewDetail"
        />
      </el-tab-pane>

      <el-tab-pane
        :label="`${t('workOrderApproval.completedApproval')} (${completedTotal})`"
        name="completed"
      >
        <!-- 已审批内容 -->
        <div class="completed-approval-content">
          <!-- 搜索表单组件 -->
          <ApprovalSearchForm
            v-model:searchParams="searchParams"
            :approval-type-options="approvalTypeOptions"
            :tab-type="'completed'"
            @search="handleSearch"
            @reset="handleReset"
          />

          <ApprovalTable
            :data="completedList"
            :loading="loading"
            :total="completedTotal"
            :current-page="searchParams.page"
            :page-size="searchParams.pageSize"
            type="completed"
            @page-change="handlePageChange"
            @size-change="handleSizeChange"
            @view-detail="handleViewDetail"
          />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 索赔审批对话框 -->
    <ClaimApprovalDialog
      v-model:visible="claimApprovalDialog.visible"
      :approval-no="claimApprovalDialog.approvalNo"
      :loading="claimApprovalDialog.loading"
      @confirm="handleClaimApprovalConfirm"
    />

    <!-- 取消审批对话框 -->
    <CancelApprovalDialog
      v-model:visible="cancelApprovalDialog.visible"
      :approval-no="cancelApprovalDialog.approvalNo"
      :loading="cancelApprovalDialog.loading"
      @confirm="handleCancelApprovalConfirm"
    />

    <!-- 审批详情页面 -->
    <ApprovalDetailView
      v-model:visible="detailDialog.visible"
      :approval-no="detailDialog.approvalNo"
      :approval-type="detailDialog.approvalType"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getPendingApprovalList,
  getCompletedApprovalList,
  exportApprovalData
} from '@/api/modules/afterSales/workOrderApproval';
import type {
  PendingApprovalListItem,
  CompletedApprovalListItem,
  WorkOrderApprovalListParams,
  WorkOrderApprovalType
} from '@/types/afterSales/workOrderApproval';

// 组件导入
import ApprovalSearchForm from './components/ApprovalSearchForm.vue';
import ApprovalTable from './components/ApprovalTable.vue';
import ClaimApprovalDialog from './components/ClaimApprovalDialog.vue';
import CancelApprovalDialog from './components/CancelApprovalDialog.vue';
import ApprovalDetailView from './components/ApprovalDetailView.vue';

const { t, tc } = useModuleI18n('afterSales');

// 响应式数据
const activeTab = ref('pending');
const loading = ref(false);
const exportLoading = ref(false);

// 搜索参数
const searchParams = reactive<WorkOrderApprovalListParams>({
  page: 1,
  pageSize: 10
});

// 数据列表
const pendingList = ref<PendingApprovalListItem[]>([]);
const completedList = ref<CompletedApprovalListItem[]>([]);
const pendingTotal = ref(0);
const completedTotal = ref(0);

// 对话框状态
const claimApprovalDialog = reactive({
  visible: false,
  approvalNo: '',
  loading: false
});

const cancelApprovalDialog = reactive({
  visible: false,
  approvalNo: '',
  loading: false
});

const detailDialog = reactive({
  visible: false,
  approvalNo: '',
  approvalType: '' as WorkOrderApprovalType
});

// 审批类型选项
const approvalTypeOptions = computed(() => [
  { label: t('workOrderApproval.claimApproval'), value: 'claim_approval' },
  { label: t('workOrderApproval.cancelApproval'), value: 'cancel_approval' }
]);

// 加载待审批列表
const loadPendingList = async () => {
  try {
    loading.value = true;
    const response = await getPendingApprovalList(searchParams);
    pendingList.value = response.list;
    pendingTotal.value = response.total;
  } catch (error) {
    console.error('加载待审批列表失败:', error);
    ElMessage.error(t('workOrderApproval.messages.loadListFailed'));
  } finally {
    loading.value = false;
  }
};

// 加载已审批列表
const loadCompletedList = async () => {
  try {
    loading.value = true;
    const response = await getCompletedApprovalList(searchParams);
    completedList.value = response.list;
    completedTotal.value = response.total;
  } catch (error) {
    console.error('加载已审批列表失败:', error);
    ElMessage.error(t('workOrderApproval.messages.loadListFailed'));
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  searchParams.page = 1;
  if (activeTab.value === 'pending') {
    loadPendingList();
  } else {
    loadCompletedList();
  }
};

// 重置处理
const handleReset = () => {
  Object.assign(searchParams, {
    page: 1,
    pageSize: 10,
    approvalNo: '',
    approvalType: undefined,
    submitterName: '',
    orderNo: '',
    customerName: '',
    licensePlate: '',
    timeoutStatus: undefined,
    currentLevel: undefined,
    submitTimeStart: '',
    submitTimeEnd: '',
    approvalResult: undefined,
    approvalTimeStart: '',
    approvalTimeEnd: ''
  });
  handleSearch();
};

// 分页处理
const handlePageChange = (page: number) => {
  searchParams.page = page;
  if (activeTab.value === 'pending') {
    loadPendingList();
  } else {
    loadCompletedList();
  }
};

const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  if (activeTab.value === 'pending') {
    loadPendingList();
  } else {
    loadCompletedList();
  }
};

// Tab切换处理
const handleTabChange = (tabName: string) => {
  searchParams.page = 1;
  if (tabName === 'pending') {
    loadPendingList();
  } else {
    loadCompletedList();
  }
};

// 索赔审批处理
const handleClaimApproval = (item: PendingApprovalListItem) => {
  claimApprovalDialog.approvalNo = item.approvalNo;
  claimApprovalDialog.visible = true;
};

// 取消审批处理
const handleCancelApproval = (item: PendingApprovalListItem) => {
  cancelApprovalDialog.approvalNo = item.approvalNo;
  cancelApprovalDialog.visible = true;
};

// 查看详情处理
const handleViewDetail = (item: PendingApprovalListItem | CompletedApprovalListItem) => {
  detailDialog.approvalNo = item.approvalNo;
  detailDialog.approvalType = item.approvalType;
  detailDialog.visible = true;
};

// 索赔审批确认
const handleClaimApprovalConfirm = async (data: any) => {
  try {
    claimApprovalDialog.loading = true;
    // TODO: 调用审批API
    ElMessage.success(t('workOrderApproval.messages.approvalSuccess'));
    claimApprovalDialog.visible = false;
    loadPendingList();
  } catch (error) {
    console.error('审批失败:', error);
    ElMessage.error(t('workOrderApproval.messages.approvalFailed'));
  } finally {
    claimApprovalDialog.loading = false;
  }
};

// 取消审批确认
const handleCancelApprovalConfirm = async (data: any) => {
  try {
    cancelApprovalDialog.loading = true;
    // TODO: 调用审批API
    ElMessage.success(t('workOrderApproval.messages.approvalSuccess'));
    cancelApprovalDialog.visible = false;
    loadPendingList();
  } catch (error) {
    console.error('审批失败:', error);
    ElMessage.error(t('workOrderApproval.messages.approvalFailed'));
  } finally {
    cancelApprovalDialog.loading = false;
  }
};

// 导出处理
const handleExport = async () => {
  try {
    exportLoading.value = true;
    await exportApprovalData(searchParams);
    ElMessage.success(t('workOrderApproval.messages.exportSuccess'));
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('workOrderApproval.messages.exportFailed'));
  } finally {
    exportLoading.value = false;
  }
};

// 生命周期
onMounted(() => {
  loadPendingList();
});

// 监听Tab变化
watch(activeTab, (newTab) => {
  if (newTab === 'pending') {
    loadPendingList();
  } else {
    loadCompletedList();
  }
});
</script>

<style scoped lang="scss">
.work-order-approval {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  .approval-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }

    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .completed-approval-content {
    min-height: 400px;
  }
}
</style>
