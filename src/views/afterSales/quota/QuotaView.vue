<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { 
  getQuotaList, 
  getQuotaTimeSlots, 
  saveQuotaConfig, 
  getStoreInfo 
} from '@/api/modules/afterSales/quota';
import type { 
  QuotaConfig, 
  TimeSlot, 
  StoreInfo, 
  QuotaConfigRequest 
} from '@/types/afterSales/quota.d.ts';

// 导入子组件
import StoreInfoCard from './components/StoreInfoCard.vue';
import QuotaListTable from './components/QuotaListTable.vue';
import QuotaConfigDialog from './components/QuotaConfigDialog.vue';

const { t, tc } = useModuleI18n('afterSales.quota');

// 响应式数据
const storeInfo = ref<StoreInfo>({ id: '', name: '', code: '' });
const quotaList = ref<QuotaConfig[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const loading = ref(false);

// 弹窗相关
const dialogVisible = ref(false);
const isEditModal = ref(false);
const modalSelectedDate = ref('');
const modalTimeSlots = ref<TimeSlot[]>([]);
const dialogLoading = ref(false);

// 获取门店信息
const fetchStoreInfo = async () => {
  try {
    const info = await getStoreInfo();
    storeInfo.value = info;
  } catch (error) {
    console.error('获取门店信息失败:', error);
  }
};

// 获取配额列表
const fetchQuotaList = async () => {
  loading.value = true;
  try {
    const response = await getQuotaList({
      page: currentPage.value,
      pageSize: pageSize.value
    });
    quotaList.value = response.list;
    total.value = response.total;
  } catch (error) {
    console.error('获取配额列表失败:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchQuotaList();
};

const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchQuotaList();
};

// 打开新增配额弹窗
const handleAddQuota = () => {
  isEditModal.value = false;
  dialogVisible.value = true;
  modalSelectedDate.value = new Date().toISOString().split('T')[0];
  modalTimeSlots.value = [];
};

// 编辑配额
const handleEditQuota = async (configDate: string) => {
  isEditModal.value = true;
  dialogVisible.value = true;
  modalSelectedDate.value = configDate;
  
  try {
    const timeSlots = await getQuotaTimeSlots(configDate);
    modalTimeSlots.value = timeSlots;
  } catch (error) {
    console.error('获取时段配置失败:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 日期变更处理
const handleDateChange = async (date: string) => {
  if (!isEditModal.value) {
    try {
      const timeSlots = await getQuotaTimeSlots(date);
      modalTimeSlots.value = timeSlots;
    } catch (error) {
      modalTimeSlots.value = [];
    }
  }
};

// 保存配额配置
const handleSaveConfig = async (data: QuotaConfigRequest) => {
  dialogLoading.value = true;
  try {
    const result = await saveQuotaConfig(data);
    if (result.success) {
      ElMessage.success(result.message);
      dialogVisible.value = false;
      fetchQuotaList(); // 刷新列表
    } else {
      ElMessage.error(result.message);
    }
  } catch (error) {
    console.error('保存配额配置失败:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    dialogLoading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchStoreInfo();
  fetchQuotaList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('pageTitle') }}</h1>

    <!-- 门店信息展示区 -->
    <StoreInfoCard :store-info="storeInfo" class="mb-20" />

    <!-- 已配置限量列表区 -->
    <QuotaListTable
      :quota-list="quotaList"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      :loading="loading"
      @add-quota="handleAddQuota"
      @edit-quota="handleEditQuota"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 新增/编辑预约限量弹窗 -->
    <QuotaConfigDialog
      v-model:visible="dialogVisible"
      v-model:selected-date="modalSelectedDate"
      v-model:time-slots="modalTimeSlots"
      :is-edit="isEditModal"
      :loading="dialogLoading"
      @save="handleSaveConfig"
      @date-change="handleDateChange"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.page-title {
  color: #303133;
  font-size: 28px;
  margin-bottom: 25px;
  text-align: center;
}

.mb-20 {
  margin-bottom: 20px;
}
</style>
