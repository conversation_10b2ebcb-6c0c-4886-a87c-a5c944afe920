<!-- src/views/afterSales/workAssignment/WorkAssignmentView.vue -->
<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type {
  WorkOrderListItem,
  TechnicianInfo,
  WorkOrderListParams,
  AssignWorkOrderRequest,
  ReassignWorkOrderRequest
} from '@/types/afterSales/workAssignment.d.ts';
import * as workAssignmentApi from '@/api/modules/afterSales/workAssignment';

// 导入子组件
import WorkAssignmentSearchForm from './components/WorkAssignmentSearchForm.vue';
import WorkAssignmentTable from './components/WorkAssignmentTable.vue';
import AssignmentDialog from './components/AssignmentDialog.vue';
import ReassignmentDialog from './components/ReassignmentDialog.vue';

const { t, tc } = useModuleI18n('afterSales');

// 响应式数据
const loading = ref(false);
const workOrderList = ref<WorkOrderListItem[]>([]);
const technicianList = ref<TechnicianInfo[]>([]);

// 筛选条件
const searchParams = reactive<WorkOrderListParams>({
  workOrderId: '',
  workOrderNo: '',
  customerName: '',
  licensePlate: '',
  status: undefined,
  workOrderType: undefined,
  priority: undefined,
  serviceAdvisorId: '',
  assignedTechnicianId: '',
  page: 1,
  pageSize: 20
});

// 日期范围
const dateRange = ref<[string, string] | null>(null);

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
});

// 对话框状态
const assignmentDialog = reactive({
  visible: false,
  workOrder: null as WorkOrderListItem | null,
  loading: false
});

const reassignmentDialog = reactive({
  visible: false,
  workOrder: null as WorkOrderListItem | null,
  loading: false
});

// 状态选项
const statusOptions = computed(() => [
  { label: t('workAssignment.status.pending_assign'), value: 'pending_assign' },
  { label: t('workAssignment.status.pending_start'), value: 'pending_start' },
  { label: t('workAssignment.status.in_progress'), value: 'in_progress' },
  { label: t('workAssignment.status.completed'), value: 'completed' },
  { label: t('workAssignment.status.cancelled'), value: 'cancelled' },
  { label: t('workAssignment.status.on_hold'), value: 'on_hold' }
]);

// 工单类型选项
const typeOptions = computed(() => [
  { label: t('workAssignment.workOrderType.maintenance'), value: 'maintenance' },
  { label: t('workAssignment.workOrderType.repair'), value: 'repair' },
  { label: t('workAssignment.workOrderType.inspection'), value: 'inspection' },
  { label: t('workAssignment.workOrderType.insurance'), value: 'insurance' }
]);

// 优先级选项
const priorityOptions = computed(() => [
  { label: t('workAssignment.priority.low'), value: 'low' },
  { label: t('workAssignment.priority.normal'), value: 'normal' },
  { label: t('workAssignment.priority.high'), value: 'high' },
  { label: t('workAssignment.priority.urgent'), value: 'urgent' }
]);

// 加载工单数据
const loadWorkOrderData = async () => {
  loading.value = true;
  try {
    // 处理日期范围
    const params = { ...searchParams };
    if (dateRange.value) {
      params.creationTimeStart = dateRange.value[0];
      params.creationTimeEnd = dateRange.value[1];
    }
    
    params.page = pagination.current;
    params.pageSize = pagination.pageSize;

    const response = await workAssignmentApi.getWorkOrderList(params);
    workOrderList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    ElMessage.error(t('workAssignment.messages.loadDataFailed'));
    console.error('Load work order data failed:', error);
  } finally {
    loading.value = false;
  }
};

// 加载技师数据
const loadTechnicianData = async () => {
  try {
    technicianList.value = await workAssignmentApi.getTechnicianList();
  } catch (error) {
    console.error('Load technician data failed:', error);
  }
};

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    loadWorkOrderData(),
    loadTechnicianData()
  ]);
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  loadWorkOrderData();
};

// 重置搜索条件
const handleReset = () => {
  Object.assign(searchParams, {
    workOrderId: '',
    workOrderNo: '',
    customerName: '',
    licensePlate: '',
    status: undefined,
    workOrderType: undefined,
    priority: undefined,
    serviceAdvisorId: '',
    assignedTechnicianId: '',
    page: 1,
    pageSize: 20
  });
  dateRange.value = null;
  pagination.current = 1;
  loadWorkOrderData();
};

// 分页相关
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.current = 1;
  searchParams.pageSize = size;
  loadWorkOrderData();
};

const handleCurrentChange = (page: number) => {
  pagination.current = page;
  searchParams.page = page;
  loadWorkOrderData();
};

// 分配工单
const handleAssign = (workOrder: WorkOrderListItem) => {
  assignmentDialog.workOrder = workOrder;
  assignmentDialog.visible = true;
};

// 重新分配工单
const handleReassign = (workOrder: WorkOrderListItem) => {
  reassignmentDialog.workOrder = workOrder;
  reassignmentDialog.visible = true;
};

// 查看详情
const handleViewDetail = (workOrder: WorkOrderListItem) => {
  // TODO: 实现工单详情页面
  ElMessage.info('工单详情功能开发中...');
  console.log('View detail:', workOrder);
};

// 分配确认
const handleAssignmentConfirm = async (data: AssignWorkOrderRequest) => {
  assignmentDialog.loading = true;
  try {
    const result = await workAssignmentApi.assignWorkOrder(data);
    if (result.success) {
      ElMessage.success(t('workAssignment.messages.assignSuccess'));
      assignmentDialog.visible = false;
      loadWorkOrderData();
    } else {
      ElMessage.error(result.message || t('workAssignment.messages.operationFailed'));
    }
  } catch (error) {
    ElMessage.error(t('workAssignment.messages.operationFailed'));
    console.error('Assignment failed:', error);
  } finally {
    assignmentDialog.loading = false;
  }
};

// 重新分配确认
const handleReassignmentConfirm = async (data: ReassignWorkOrderRequest) => {
  reassignmentDialog.loading = true;
  try {
    const result = await workAssignmentApi.reassignWorkOrder(data);
    if (result.success) {
      ElMessage.success(t('workAssignment.messages.reassignSuccess'));
      reassignmentDialog.visible = false;
      loadWorkOrderData();
    } else {
      ElMessage.error(result.message || t('workAssignment.messages.operationFailed'));
    }
  } catch (error) {
    ElMessage.error(t('workAssignment.messages.operationFailed'));
    console.error('Reassignment failed:', error);
  } finally {
    reassignmentDialog.loading = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  refreshData();
});
</script>

<template>
  <div class="work-assignment-management">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <h1 class="page-title">{{ t('workAssignment.title') }}</h1>
      <div class="header-actions">
        <el-button
          type="primary"
          :icon="Refresh"
          @click="refreshData"
          :loading="loading"
        >
          {{ tc('refresh') }}
        </el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <WorkAssignmentSearchForm
      v-model:search-params="searchParams"
      v-model:date-range="dateRange"
      :technician-list="technicianList"
      :status-options="statusOptions"
      :type-options="typeOptions"
      :priority-options="priorityOptions"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 工单表格 -->
    <WorkAssignmentTable
      :data="workOrderList"
      :loading="loading"
      :pagination="pagination"
      @assign="handleAssign"
      @reassign="handleReassign"
      @view-detail="handleViewDetail"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 分配工单对话框 -->
    <AssignmentDialog
      v-model:visible="assignmentDialog.visible"
      :work-order="assignmentDialog.workOrder"
      :technician-list="technicianList"
      :loading="assignmentDialog.loading"
      @confirm="handleAssignmentConfirm"
    />

    <!-- 重新分配对话框 -->
    <ReassignmentDialog
      v-model:visible="reassignmentDialog.visible"
      :work-order="reassignmentDialog.workOrder"
      :technician-list="technicianList"
      :loading="reassignmentDialog.loading"
      @confirm="handleReassignmentConfirm"
    />
  </div>
</template>

<style scoped lang="scss">
.work-assignment-management {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
}
</style>
