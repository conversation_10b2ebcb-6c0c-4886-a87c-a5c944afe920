<!-- src/views/afterSales/workAssignment/components/StatusDistributionChart.vue -->
<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import { ElCard, ElSkeleton } from 'element-plus';
import * as echarts from 'echarts';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { StatusDistribution } from '@/types/afterSales/workAssignmentDashboard.d.ts';

interface Props {
  data: StatusDistribution[];
  loading: boolean;
  height?: number;
}

const props = withDefaults(defineProps<Props>(), {
  height: 400
});

const { t, tc } = useModuleI18n('afterSales');

const chartRef = ref<HTMLDivElement>();
let chartInstance: echarts.ECharts | null = null;

const initChart = () => {
  if (!chartRef.value) return;
  
  chartInstance = echarts.init(chartRef.value);
  updateChart();
};

const updateChart = () => {
  if (!chartInstance || props.loading) return;
  
  const option = {
    title: {
      text: t('workAssignmentDashboard.charts.statusDistributionTitle'),
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: props.data.map(item => getStatusLabel(item.status))
    },
    series: [
      {
        name: '工单状态',
        type: 'pie',
        radius: '60%',
        center: ['50%', '60%'],
        data: props.data.map(item => ({
          value: item.count,
          name: getStatusLabel(item.status),
          itemStyle: {
            color: item.color
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  chartInstance.setOption(option);
};

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending_assign': '待分配',
    'assigned': '已分配',
    'in_progress': '进行中',
    'completed': '已完成'
  };
  return statusMap[status] || status;
};

watch(() => props.data, () => {
  nextTick(() => {
    updateChart();
  });
}, { deep: true });

watch(() => props.loading, (newVal) => {
  if (!newVal) {
    nextTick(() => {
      updateChart();
    });
  }
});

onMounted(() => {
  initChart();
  
  // 响应式处理
  window.addEventListener('resize', () => {
    chartInstance?.resize();
  });
});
</script>

<template>
  <el-card class="chart-card">
    <template #header>
      <span>{{ t('workAssignmentDashboard.charts.statusDistributionTitle') }}</span>
    </template>
    
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div :style="{ height: `${height}px` }" class="skeleton-chart"></div>
      </template>
      
      <div 
        ref="chartRef" 
        :style="{ height: `${height}px` }"
        class="chart-container"
      ></div>
    </el-skeleton>
  </el-card>
</template>

<style scoped lang="scss">
.chart-card {
  margin-bottom: 20px;
  
  .chart-container {
    width: 100%;
  }
  
  .skeleton-chart {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
