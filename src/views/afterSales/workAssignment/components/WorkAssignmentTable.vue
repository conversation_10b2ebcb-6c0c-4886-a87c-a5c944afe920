<!-- src/views/afterSales/workAssignment/components/WorkAssignmentTable.vue -->
<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElButton, ElTag, ElPagination } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrderListItem } from '@/types/afterSales/workAssignment.d.ts';

interface Props {
  data: WorkOrderListItem[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
}

interface Emits {
  (e: 'assign', workOrder: WorkOrderListItem): void;
  (e: 'reassign', workOrder: WorkOrderListItem): void;
  (e: 'view-detail', workOrder: WorkOrderListItem): void;
  (e: 'size-change', size: number): void;
  (e: 'current-change', page: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

// 格式化日期时间
const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-';
  return dateTime;
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending_assign': 'warning',
    'pending_start': 'info',
    'in_progress': 'primary',
    'completed': 'success',
    'cancelled': 'danger',
    'on_hold': 'warning'
  };
  return statusMap[status] || 'info';
};

// 获取优先级标签类型
const getPriorityTagType = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'low': 'info',
    'normal': 'primary',
    'high': 'warning',
    'urgent': 'danger'
  };
  return priorityMap[priority] || 'info';
};

// 获取工单类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'maintenance': 'success',
    'repair': 'warning',
    'inspection': 'info',
    'insurance': 'primary'
  };
  return typeMap[type] || 'info';
};

const handleAssign = (workOrder: WorkOrderListItem) => {
  emit('assign', workOrder);
};

const handleReassign = (workOrder: WorkOrderListItem) => {
  emit('reassign', workOrder);
};

const handleViewDetail = (workOrder: WorkOrderListItem) => {
  emit('view-detail', workOrder);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const handleCurrentChange = (page: number) => {
  emit('current-change', page);
};
</script>

<template>
  <el-card class="table-card">
    <el-table
      :data="data"
      :loading="loading"
      stripe
      border
      style="width: 100%"
      height="600"
    >
      <el-table-column
        prop="workOrderNo"
        :label="t('workAssignment.table.workOrderNo')"
        width="150"
        fixed="left"
      />

      <el-table-column
        prop="customerName"
        :label="t('workAssignment.table.customerName')"
        width="120"
      />

      <el-table-column
        :label="t('workAssignment.table.vehicleInfo')"
        width="200"
      >
        <template #default="{ row }">
          <div>{{ row.licensePlate }}</div>
          <div class="text-muted">{{ row.vehicleModel }}</div>
        </template>
      </el-table-column>

      <el-table-column
        prop="status"
        :label="t('workAssignment.table.status')"
        width="120"
      >
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)" size="small">
            {{ t(`workAssignment.status.${row.status}`) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="workOrderType"
        :label="t('workAssignment.table.workOrderType')"
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="getTypeTagType(row.workOrderType)" size="small">
            {{ t(`workAssignment.workOrderType.${row.workOrderType}`) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="priority"
        :label="t('workAssignment.table.priority')"
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="getPriorityTagType(row.priority)" size="small">
            {{ t(`workAssignment.priority.${row.priority}`) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="serviceAdvisorName"
        :label="t('workAssignment.table.serviceAdvisor')"
        width="120"
      />

      <el-table-column
        :label="t('workAssignment.table.assignedTechnician')"
        width="120"
      >
        <template #default="{ row }">
          <span v-if="row.assignedTechnicianName">{{ row.assignedTechnicianName }}</span>
          <span v-else class="text-muted">{{ t('workAssignment.common.unassigned') }}</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="estimatedDuration"
        :label="t('workAssignment.table.estimatedDuration')"
        width="120"
      >
        <template #default="{ row }">
          {{ row.estimatedDuration }} 分钟
        </template>
      </el-table-column>

      <el-table-column
        prop="creationTime"
        :label="t('workAssignment.table.creationTime')"
        width="180"
      >
        <template #default="{ row }">
          {{ formatDateTime(row.creationTime) }}
        </template>
      </el-table-column>

      <el-table-column
        prop="estimatedStartTime"
        :label="t('workAssignment.table.scheduledStartTime')"
        width="180"
      >
        <template #default="{ row }">
          <span v-if="row.estimatedStartTime">{{ formatDateTime(row.estimatedStartTime) }}</span>
          <span v-else class="text-muted">{{ t('workAssignment.common.notScheduled') }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="t('workAssignment.common.actions')"
        width="200"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            v-if="row.status === 'pending_assign'"
            type="primary"
            size="small"
            @click="handleAssign(row)"
          >
            {{ t('workAssignment.actions.assign') }}
          </el-button>

          <el-button
            v-if="row.status === 'pending_start' || row.status === 'in_progress'"
            type="warning"
            size="small"
            @click="handleReassign(row)"
          >
            {{ t('workAssignment.actions.reassign') }}
          </el-button>

          <el-button
            type="primary"
            size="small"
            plain
            @click="handleViewDetail(row)"
          >
            {{ t('workAssignment.common.detail') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.table-card {
  .pagination-wrapper {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #ebeef5;
  }
  
  .text-muted {
    color: #909399;
    font-style: italic;
    font-size: 12px;
  }
}

:deep(.el-table) {
  .el-table__header-wrapper {
    background-color: #f5f7fa;
  }

  .el-table__row:hover {
    background-color: #f0f9ff;
  }
}
</style>
