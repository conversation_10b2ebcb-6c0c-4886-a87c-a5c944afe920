<!-- src/views/afterSales/workAssignment/components/DashboardHeader.vue -->
<script setup lang="ts">
import { ElRow, ElCol, ElCard, ElStatistic, ElIcon } from 'element-plus';
import { Document, Clock, User, CircleCheck } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { DashboardStatistics } from '@/types/afterSales/workAssignmentDashboard.d.ts';

interface Props {
  statistics: DashboardStatistics;
  loading: boolean;
}

defineProps<Props>();

const { t, tc } = useModuleI18n('afterSales');

const statisticsConfig = [
  {
    key: 'totalOrders',
    title: 'workAssignmentDashboard.statistics.totalOrders',
    icon: Document,
    color: '#409EFF'
  },
  {
    key: 'pendingAssignment',
    title: 'workAssignmentDashboard.statistics.pendingAssignment',
    icon: Clock,
    color: '#E6A23C'
  },
  {
    key: 'inProgressOrders',
    title: 'workAssignmentDashboard.statistics.inProgressOrders',
    icon: User,
    color: '#409EFF'
  },
  {
    key: 'completedOrders',
    title: 'workAssignmentDashboard.statistics.completedOrders',
    icon: CircleCheck,
    color: '#67C23A'
  }
];
</script>

<template>
  <el-row :gutter="20" class="dashboard-header">
    <el-col :span="6" v-for="config in statisticsConfig" :key="config.key">
      <el-card class="statistic-card" :body-style="{ padding: '20px' }">
        <el-statistic
          :title="t(config.title)"
          :value="statistics[config.key as keyof DashboardStatistics]"
          :loading="loading"
        >
          <template #prefix>
            <el-icon :color="config.color" :size="24">
              <component :is="config.icon" />
            </el-icon>
          </template>
        </el-statistic>
      </el-card>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.dashboard-header {
  margin-bottom: 20px;
  
  .statistic-card {
    text-align: center;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
