<!-- src/views/afterSales/workAssignment/components/ReassignmentDialog.vue -->
<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { ElDialog, ElForm, ElFormItem, ElSelect, ElOption, ElDatePicker, ElInput, ElButton, ElMessage, ElTag } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrderListItem, TechnicianInfo, ReassignWorkOrderRequest } from '@/types/afterSales/workAssignment.d.ts';

interface Props {
  visible: boolean;
  workOrder: WorkOrderListItem | null;
  technicianList: TechnicianInfo[];
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: ReassignWorkOrderRequest): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales');

const formRef = ref();
const formData = reactive<ReassignWorkOrderRequest>({
  workOrderId: '',
  fromTechnicianId: '',
  toTechnicianId: '',
  reason: '',
  estimatedStartTime: '',
  notes: ''
});

const rules = {
  toTechnicianId: [
    { required: true, message: t('workAssignment.messages.selectTechnicianFirst'), trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请输入重新分配原因', trigger: 'blur' }
  ]
};

// 监听工单变化，更新表单数据
watch(() => props.workOrder, (newWorkOrder) => {
  if (newWorkOrder) {
    formData.workOrderId = newWorkOrder.workOrderId;
    formData.fromTechnicianId = newWorkOrder.assignedTechnicianId || '';
    formData.toTechnicianId = '';
    formData.reason = '';
    formData.estimatedStartTime = newWorkOrder.estimatedStartTime || '';
    formData.notes = '';
  }
}, { immediate: true });

// 监听对话框显示状态
watch(() => props.visible, (newVisible) => {
  if (!newVisible) {
    // 重置表单
    formRef.value?.resetFields();
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirm = async () => {
  try {
    await formRef.value?.validate();
    emit('confirm', { ...formData });
  } catch (error) {
    ElMessage.error('请完善表单信息');
  }
};

// 获取技师状态显示文本
const getTechnicianStatusText = (status: string) => {
  return t(`workAssignment.technicianStatus.${status}`);
};

// 获取技师状态标签类型
const getTechnicianStatusType = (status: string) => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    'available': 'success',
    'busy': 'warning',
    'offline': 'danger'
  };
  return statusMap[status] || 'info';
};

// 过滤可用技师（排除当前分配的技师）
const availableTechnicians = computed(() => {
  return props.technicianList.filter(tech => 
    tech.isActive && 
    tech.technicianId !== formData.fromTechnicianId
  );
});

// 获取当前分配的技师信息
const currentTechnician = computed(() => {
  return props.technicianList.find(tech => tech.technicianId === formData.fromTechnicianId);
});

// 预设的重新分配原因
const reasonOptions = [
  '技师请假',
  '技师技能不匹配',
  '工作负荷过重',
  '客户要求',
  '紧急情况调配',
  '其他原因'
];
</script>

<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="handleClose"
    :title="t('workAssignment.dialog.reassignTitle')"
    width="600px"
    :close-on-click-modal="false"
  >
    <div v-if="workOrder" class="work-order-info">
      <h4>工单信息</h4>
      <p><strong>{{ t('workAssignment.table.workOrderNo') }}:</strong> {{ workOrder.workOrderNo }}</p>
      <p><strong>{{ t('workAssignment.table.customerName') }}:</strong> {{ workOrder.customerName }}</p>
      <p><strong>{{ t('workAssignment.table.vehicleInfo') }}:</strong> {{ workOrder.licensePlate }} {{ workOrder.vehicleModel }}</p>
      <p><strong>{{ t('workAssignment.technician.currentTechnician') }}:</strong>
        <span v-if="currentTechnician">
          {{ currentTechnician.technicianName }}
          <el-tag 
            :type="getTechnicianStatusType(currentTechnician.status)" 
            size="small"
            class="ml-2"
          >
            {{ getTechnicianStatusText(currentTechnician.status) }}
          </el-tag>
        </span>
        <span v-else class="text-muted">{{ t('workAssignment.common.unassigned') }}</span>
      </p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="reassignment-form"
    >
      <el-form-item
        :label="t('workAssignment.dialog.selectTechnician')"
        prop="toTechnicianId"
      >
        <el-select
          v-model="formData.toTechnicianId"
          :placeholder="t('workAssignment.technician.selectTechnician')"
          style="width: 100%"
        >
          <el-option
            v-for="technician in availableTechnicians"
            :key="technician.technicianId"
            :label="technician.technicianName"
            :value="technician.technicianId"
            :disabled="technician.status === 'offline'"
          >
            <div class="technician-option">
              <div class="technician-info">
                <span class="technician-name">{{ technician.technicianName }}</span>
                <el-tag 
                  :type="getTechnicianStatusType(technician.status)" 
                  size="small"
                  class="status-tag"
                >
                  {{ getTechnicianStatusText(technician.status) }}
                </el-tag>
              </div>
              <div class="technician-details">
                <span class="skill-level">技能等级: {{ technician.skillLevel }}</span>
                <span class="workload">负荷: {{ technician.currentWorkload }}%</span>
              </div>
              <div class="specialties">
                专长: {{ technician.specialties.join(', ') }}
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        :label="t('workAssignment.dialog.reason')"
        prop="reason"
      >
        <el-select
          v-model="formData.reason"
          :placeholder="t('workAssignment.dialog.reasonPlaceholder')"
          style="width: 100%"
          allow-create
          filterable
        >
          <el-option
            v-for="reason in reasonOptions"
            :key="reason"
            :label="reason"
            :value="reason"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="t('workAssignment.dialog.estimatedStartTime')">
        <el-date-picker
          v-model="formData.estimatedStartTime"
          type="datetime"
          placeholder="选择新的预计开始时间"
          value-format="YYYY-MM-DD HH:mm"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item :label="t('workAssignment.dialog.notes')">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          :placeholder="t('workAssignment.dialog.notesPlaceholder')"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">
        {{ t('workAssignment.actions.cancel') }}
      </el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :loading="loading"
      >
        {{ t('workAssignment.actions.confirm') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.work-order-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;

  h4 {
    margin: 0 0 10px 0;
    color: #303133;
  }

  p {
    margin: 5px 0;
    color: #606266;
    font-size: 14px;
  }
}

.technician-option {
  .technician-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    .technician-name {
      font-weight: 500;
    }

    .status-tag {
      margin-left: 10px;
    }
  }

  .technician-details {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #909399;
    margin-bottom: 3px;
  }

  .specialties {
    font-size: 12px;
    color: #909399;
  }
}

.ml-2 {
  margin-left: 8px;
}

.text-muted {
  color: #909399;
  font-style: italic;
}
</style>
