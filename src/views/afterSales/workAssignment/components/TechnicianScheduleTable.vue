<!-- src/views/afterSales/workAssignment/components/TechnicianScheduleTable.vue -->
<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElTag, ElProgress, ElTooltip } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { TechnicianSchedule } from '@/types/afterSales/workAssignmentDashboard.d.ts';

interface Props {
  schedules: TechnicianSchedule[];
  loading: boolean;
}

defineProps<Props>();

const { t, tc } = useModuleI18n('afterSales');

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    'available': 'success',
    'busy': 'warning',
    'break': 'info',
    'offline': 'danger'
  };
  return statusMap[status] || 'info';
};

// 获取工作负荷进度条颜色
const getWorkloadColor = (percentage: number) => {
  if (percentage < 50) return '#67C23A';
  if (percentage < 80) return '#E6A23C';
  return '#F56C6C';
};

// 格式化工作时间
const formatWorkingHours = (workingHours: { start: string; end: string }) => {
  return `${workingHours.start} - ${workingHours.end}`;
};

// 获取工单类型标签类型
const getOrderTypeTagType = (type: string) => {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    'maintenance': 'success',
    'repair': 'warning',
    'inspection': 'info',
    'insurance': 'primary'
  };
  return typeMap[type] || 'info';
};

// 获取优先级标签类型
const getPriorityTagType = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'low': 'info',
    'normal': 'primary',
    'high': 'warning',
    'urgent': 'danger'
  };
  return priorityMap[priority] || 'info';
};
</script>

<template>
  <el-card class="schedule-table-card">
    <template #header>
      <span>{{ t('workAssignmentDashboard.schedule.title') }}</span>
    </template>
    
    <el-table
      :data="schedules"
      :loading="loading"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column
        prop="technicianInfo.technicianName"
        :label="t('workAssignmentDashboard.schedule.technicianName')"
        width="120"
        fixed="left"
      />

      <el-table-column
        prop="technicianInfo.department"
        :label="t('workAssignmentDashboard.schedule.department')"
        width="100"
      />

      <el-table-column
        :label="t('workAssignmentDashboard.schedule.workingHours')"
        width="150"
      >
        <template #default="{ row }">
          {{ formatWorkingHours(row.workingHours) }}
        </template>
      </el-table-column>

      <el-table-column
        prop="currentStatus"
        :label="t('workAssignmentDashboard.schedule.currentStatus')"
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.currentStatus)" size="small">
            {{ t(`workAssignmentDashboard.status.${row.currentStatus}`) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        :label="t('workAssignmentDashboard.schedule.assignedOrders')"
        width="200"
      >
        <template #default="{ row }">
          <div class="assigned-orders">
            <div v-if="row.assignedOrders.length === 0" class="no-orders">
              {{ t('workAssignmentDashboard.common.noData') }}
            </div>
            <div v-else>
              <el-tooltip
                v-for="order in row.assignedOrders.slice(0, 3)"
                :key="order.workOrderId"
                :content="`${order.workOrderNo} - ${order.customerName}`"
                placement="top"
              >
                <el-tag
                  :type="getOrderTypeTagType(order.workOrderType)"
                  size="small"
                  class="order-tag"
                >
                  {{ order.workOrderNo }}
                </el-tag>
              </el-tooltip>
              <span v-if="row.assignedOrders.length > 3" class="more-orders">
                +{{ row.assignedOrders.length - 3 }}
              </span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        :label="t('workAssignmentDashboard.schedule.workloadPercentage')"
        width="200"
      >
        <template #default="{ row }">
          <div class="workload-progress">
            <el-progress
              :percentage="row.workloadPercentage"
              :color="getWorkloadColor(row.workloadPercentage)"
              :stroke-width="8"
            />
            <span class="workload-text">{{ row.workloadPercentage.toFixed(1) }}%</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        :label="t('workAssignmentDashboard.schedule.efficiency')"
        width="100"
      >
        <template #default="{ row }">
          <span :class="{ 'high-efficiency': row.efficiency >= 90, 'low-efficiency': row.efficiency < 70 }">
            {{ row.efficiency }}%
          </span>
        </template>
      </el-table-column>

      <el-table-column
        :label="t('workAssignmentDashboard.schedule.availableCapacity')"
        width="150"
      >
        <template #default="{ row }">
          <span class="capacity-info">
            {{ Math.floor(row.availableCapacity / 60) }}h {{ row.availableCapacity % 60 }}m
          </span>
        </template>
      </el-table-column>

      <el-table-column
        :label="t('workAssignmentDashboard.schedule.specialties')"
        width="200"
      >
        <template #default="{ row }">
          <div class="specialties">
            <el-tag
              v-for="specialty in row.technicianInfo.specialties"
              :key="specialty"
              size="small"
              type="info"
              class="specialty-tag"
            >
              {{ specialty }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<style scoped lang="scss">
.schedule-table-card {
  margin-bottom: 20px;
  
  .assigned-orders {
    .no-orders {
      color: #909399;
      font-style: italic;
      font-size: 12px;
    }
    
    .order-tag {
      margin: 2px;
      font-size: 11px;
    }
    
    .more-orders {
      color: #909399;
      font-size: 12px;
      margin-left: 5px;
    }
  }
  
  .workload-progress {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .workload-text {
      font-size: 12px;
      color: #606266;
      min-width: 40px;
    }
  }
  
  .high-efficiency {
    color: #67C23A;
    font-weight: 600;
  }
  
  .low-efficiency {
    color: #F56C6C;
    font-weight: 600;
  }
  
  .capacity-info {
    color: #606266;
    font-size: 13px;
  }
  
  .specialties {
    .specialty-tag {
      margin: 1px;
      font-size: 11px;
    }
  }
}

:deep(.el-table) {
  .el-table__header-wrapper {
    background-color: #f5f7fa;
  }

  .el-table__row:hover {
    background-color: #f0f9ff;
  }
}
</style>
