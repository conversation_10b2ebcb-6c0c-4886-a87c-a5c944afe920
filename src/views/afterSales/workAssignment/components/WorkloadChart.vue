<!-- src/views/afterSales/workAssignment/components/WorkloadChart.vue -->
<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import { ElCard, ElSkeleton } from 'element-plus';
import * as echarts from 'echarts';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkloadData } from '@/types/afterSales/workAssignmentDashboard.d.ts';

interface Props {
  data: WorkloadData[];
  loading: boolean;
  height?: number;
}

const props = withDefaults(defineProps<Props>(), {
  height: 400
});

const { t, tc } = useModuleI18n('afterSales');

const chartRef = ref<HTMLDivElement>();
let chartInstance: echarts.ECharts | null = null;

const initChart = () => {
  if (!chartRef.value) return;
  
  chartInstance = echarts.init(chartRef.value);
  updateChart();
};

const updateChart = () => {
  if (!chartInstance || props.loading) return;
  
  const option = {
    title: {
      text: t('workAssignmentDashboard.charts.workloadTitle'),
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0];
        return `${data.name}<br/>
                工作负荷: ${data.value}%<br/>
                工单数量: ${props.data[data.dataIndex]?.assignedOrdersCount || 0}`;
      }
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.technicianName),
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '利用率(%)',
      max: 100
    },
    series: [
      {
        name: '工作负荷',
        type: 'bar',
        data: props.data.map(item => ({
          value: item.utilizationRate,
          itemStyle: {
            color: item.utilizationRate > 90 ? '#F56C6C' : 
                   item.utilizationRate > 70 ? '#E6A23C' : '#67C23A'
          }
        })),
        barWidth: '60%'
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  };
  
  chartInstance.setOption(option);
};

watch(() => props.data, () => {
  nextTick(() => {
    updateChart();
  });
}, { deep: true });

watch(() => props.loading, (newVal) => {
  if (!newVal) {
    nextTick(() => {
      updateChart();
    });
  }
});

onMounted(() => {
  initChart();
  
  // 响应式处理
  window.addEventListener('resize', () => {
    chartInstance?.resize();
  });
});
</script>

<template>
  <el-card class="chart-card">
    <template #header>
      <span>{{ t('workAssignmentDashboard.charts.workloadTitle') }}</span>
    </template>
    
    <el-skeleton :loading="loading" animated>
      <template #template>
        <div :style="{ height: `${height}px` }" class="skeleton-chart"></div>
      </template>
      
      <div 
        ref="chartRef" 
        :style="{ height: `${height}px` }"
        class="chart-container"
      ></div>
    </el-skeleton>
  </el-card>
</template>

<style scoped lang="scss">
.chart-card {
  margin-bottom: 20px;
  
  .chart-container {
    width: 100%;
  }
  
  .skeleton-chart {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
