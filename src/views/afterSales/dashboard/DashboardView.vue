<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getDashboardData } from '@/api/modules/afterSales/dashboard';
import type { DashboardAppointmentItem, DashboardStats } from '@/types/afterSales/dashboard.d.ts';

// 导入子组件
import StatsCards from './components/StatsCards.vue';
import FilterButtons from './components/FilterButtons.vue';
import AppointmentTable from './components/AppointmentTable.vue';

const { t } = useModuleI18n('afterSales.dashboard');

// 响应式数据
const loading = ref(false);
const activeFilter = ref('all');
const appointments = ref<DashboardAppointmentItem[]>([]);
const stats = ref<DashboardStats>({
  totalAppointments: 0,
  arrivedCount: 0,
  notArrivedCount: 0,
  notFulfilledCount: 0,
  arrivalRate: 0,
  tomorrowCount: 0
});

// 获取看板数据
const fetchDashboardData = async () => {
  loading.value = true;
  try {
    const response = await getDashboardData({
      filterType: activeFilter.value as 'all' | 'notArrived' | 'tomorrow'
    });
    appointments.value = response.appointments;
    stats.value = response.stats;
  } catch (error) {
    console.error('获取看板数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理筛选变化
const handleFilterChange = (filterType: string) => {
  activeFilter.value = filterType;
  fetchDashboardData();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDashboardData();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('pageTitle') }}</h1>

    <!-- 统计卡片 -->
    <StatsCards :stats="stats" />

    <!-- 筛选按钮 -->
    <FilterButtons 
      :active-filter="activeFilter" 
      @filter-change="handleFilterChange" 
    />

    <!-- 预约表格 -->
    <AppointmentTable :appointments="appointments" />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 600;
}
</style>
