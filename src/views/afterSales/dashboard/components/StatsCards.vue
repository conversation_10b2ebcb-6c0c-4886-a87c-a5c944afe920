<script setup lang="ts">
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ElRow } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { DashboardStats } from '@/types/afterSales/dashboard.d.ts';

interface Props {
  stats: DashboardStats;
}

defineProps<Props>();

const { t } = useModuleI18n('afterSales.dashboard');
</script>

<template>
  <el-row :gutter="20" class="stats-cards">
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-text">
            <div class="stat-label">{{ t('stats.totalAppointments') }}</div>
            <div class="stat-value">{{ stats.totalAppointments }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-text">
            <div class="stat-label">{{ t('stats.arrivedCount') }}</div>
            <div class="stat-value">{{ stats.arrivedCount }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-text">
            <div class="stat-label">{{ t('stats.notArrivedCount') }}</div>
            <div class="stat-value">{{ stats.notArrivedCount }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-text">
            <div class="stat-label">{{ t('stats.arrivalRate') }}</div>
            <div class="stat-value">{{ stats.arrivalRate }}%</div>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: box-shadow 0.3s ease;
  }
}

.stat-content {
  padding: 20px;
}

.stat-label {
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #333;
}
</style>
