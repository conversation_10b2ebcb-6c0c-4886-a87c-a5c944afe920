<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON>, ElRow } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';

interface Props {
  activeFilter: string;
}

interface Emits {
  (e: 'filter-change', filterType: string): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t } = useModuleI18n('afterSales.dashboard');

const handleFilterChange = (filterType: string) => {
  emit('filter-change', filterType);
};
</script>

<template>
  <el-row class="mb-20">
    <el-button
      :type="activeFilter === 'all' ? 'primary' : 'default'"
      @click="handleFilterChange('all')"
    >
      {{ t('filters.all') }}
    </el-button>
    <el-button
      :type="activeFilter === 'notArrived' ? 'primary' : 'default'"
      @click="handleFilterChange('notArrived')"
    >
      {{ t('filters.notArrived') }}
    </el-button>
    <el-button
      :type="activeFilter === 'tomorrow' ? 'primary' : 'default'"
      @click="handleFilterChange('tomorrow')"
    >
      {{ t('filters.tomorrow') }}
    </el-button>
  </el-row>
</template>

<style scoped lang="scss">
.mb-20 {
  margin-bottom: 20px;
}

.el-button {
  margin-right: 12px;
  
  &:last-child {
    margin-right: 0;
  }
}
</style>
