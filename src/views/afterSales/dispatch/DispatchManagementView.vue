<template>
  <div class="dispatch-management-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ t('dispatch.title') }}</h1>
      <p class="page-subtitle">{{ t('dispatch.subtitle') }}</p>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-section" v-if="statistics">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalWorkOrders }}</div>
              <div class="stat-label">{{ t('dispatch.statistics.totalWorkOrders') }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pendingAssignment }}</div>
              <div class="stat-label">{{ t('dispatch.statistics.pendingAssignment') }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card progress">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.inProgress }}</div>
              <div class="stat-label">{{ t('dispatch.statistics.inProgress') }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card completed">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.completed }}</div>
              <div class="stat-label">{{ t('dispatch.statistics.completed') }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card paused">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.paused }}</div>
              <div class="stat-label">{{ t('dispatch.statistics.paused') }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stat-card utilization">
            <div class="stat-content">
              <div class="stat-number">{{ Math.round(statistics.technicianUtilization * 100) }}%</div>
              <div class="stat-label">{{ t('dispatch.statistics.technicianUtilization') }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="20">
      <!-- 左侧：搜索和表格 -->
      <el-col :span="18">
        <!-- 搜索表单 -->
        <DispatchSearchForm
          v-model:searchParams="searchParams"
          :loading="loading"
          @search="handleSearch"
          @reset="handleReset"
        />

        <!-- 派工表格 -->
        <DispatchTable
          :data="workOrderList"
          :loading="loading"
          :total="total"
          :current-page="searchParams.page"
          :page-size="searchParams.pageSize"
          :export-loading="exportLoading"
          @assign="handleAssign"
          @reassign="handleReassign"
          @pause="handlePause"
          @resume="handleResume"
          @complete="handleComplete"
          @view-detail="handleViewDetail"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
          @selection-change="handleSelectionChange"
          @export="handleExport"
          @refresh="handleRefresh"
        />
      </el-col>

      <!-- 右侧：技师状态 -->
      <el-col :span="6">
        <el-card class="technician-panel">
          <template #header>
            <div class="panel-header">
              <span class="panel-title">{{ t('dispatch.technician.status') || '技师状态' }}</span>
              <el-button size="small" :icon="Refresh" @click="loadTechnicianList" :loading="technicianLoading">
                {{ t('dispatch.actions.refresh') }}
              </el-button>
            </div>
          </template>
          
          <div class="technician-list" v-loading="technicianLoading">
            <TechnicianStatusCard
              v-for="technician in technicianList"
              :key="technician.id"
              :technician="technician"
              :show-actions="false"
              @assign-work="handleTechnicianAssign"
              @view-schedule="handleViewSchedule"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分配工单对话框 -->
    <AssignmentDialog
      v-model:visible="assignmentDialogVisible"
      :work-order="selectedWorkOrder"
      :technicians="technicianList"
      :loading="assignmentLoading"
      @confirm="handleAssignmentConfirm"
      @close="handleAssignmentClose"
    />

    <!-- 重新分配对话框 -->
    <ReassignmentDialog
      v-model:visible="reassignmentDialogVisible"
      :work-order="selectedWorkOrder"
      :technicians="technicianList"
      :loading="reassignmentLoading"
      @confirm="handleReassignmentConfirm"
      @close="handleReassignmentClose"
    />

    <!-- 工单详情对话框 -->
    <WorkOrderDetailDialog
      v-model:visible="detailDialogVisible"
      :work-order-no="selectedWorkOrderNo"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';

// 组件导入
import DispatchSearchForm from './components/DispatchSearchForm.vue';
import DispatchTable from './components/DispatchTable.vue';
import AssignmentDialog from './components/AssignmentDialog.vue';
import ReassignmentDialog from './components/ReassignmentDialog.vue';
import WorkOrderDetailDialog from './components/WorkOrderDetailDialog.vue';
import TechnicianStatusCard from './components/TechnicianStatusCard.vue';

// API导入
import {
  getDispatchList,
  getTechnicianList,
  assignWorkOrder,
  reassignWorkOrder,
  pauseWorkOrder,
  resumeWorkOrder,
  completeWorkOrder,
  getDispatchStatistics,
  exportDispatchData
} from '@/api/modules/afterSales/dispatch';

// 类型导入
import type {
  DispatchListItem,
  DispatchSearchParams,
  TechnicianInfo,
  AssignmentFormData,
  ReassignmentFormData,
  DispatchStatistics
} from '@/types/afterSales/dispatch';

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 响应式数据
const loading = ref(false);
const technicianLoading = ref(false);
const assignmentLoading = ref(false);
const reassignmentLoading = ref(false);
const exportLoading = ref(false);

// 搜索参数
const searchParams = reactive<DispatchSearchParams>({
  page: 1,
  pageSize: 10,
  workOrderNo: '',
  priority: undefined,
  workOrderType: undefined,
  assignmentStatus: undefined,
  workOrderStatus: undefined,
  creationTimeStart: '',
  creationTimeEnd: '',
  customerSource: undefined,
  repairmanName: '',
  licensePlateNumber: '',
  serviceAdvisor: '',
  technician: ''
});

// 数据列表
const workOrderList = ref<DispatchListItem[]>([]);
const technicianList = ref<TechnicianInfo[]>([]);
const statistics = ref<DispatchStatistics | null>(null);
const total = ref(0);
const selectedRows = ref<DispatchListItem[]>([]);

// 对话框状态
const assignmentDialogVisible = ref(false);
const reassignmentDialogVisible = ref(false);
const detailDialogVisible = ref(false);

// 选中的数据
const selectedWorkOrder = ref<DispatchListItem | null>(null);
const selectedWorkOrderNo = ref('');

// 页面初始化
onMounted(async () => {
  await Promise.all([
    loadWorkOrderList(),
    loadTechnicianList(),
    loadStatistics()
  ]);
});

// 加载工单列表
const loadWorkOrderList = async () => {
  try {
    loading.value = true;
    const response = await getDispatchList(searchParams);
    workOrderList.value = response.data;
    total.value = response.total;
  } catch (error) {
    console.error('加载工单列表失败:', error);
    ElMessage.error(t('dispatch.messages.loadListFailed'));
  } finally {
    loading.value = false;
  }
};

// 加载技师列表
const loadTechnicianList = async () => {
  try {
    technicianLoading.value = true;
    technicianList.value = await getTechnicianList();
  } catch (error) {
    console.error('加载技师列表失败:', error);
    ElMessage.error('加载技师列表失败');
  } finally {
    technicianLoading.value = false;
  }
};

// 加载统计数据
const loadStatistics = async () => {
  try {
    statistics.value = await getDispatchStatistics();
  } catch (error) {
    console.error('加载统计数据失败:', error);
  }
};

// 搜索处理
const handleSearch = () => {
  searchParams.page = 1;
  loadWorkOrderList();
};

// 重置处理
const handleReset = () => {
  Object.assign(searchParams, {
    page: 1,
    pageSize: 10,
    workOrderNo: '',
    priority: undefined,
    workOrderType: undefined,
    assignmentStatus: undefined,
    workOrderStatus: undefined,
    creationTimeStart: '',
    creationTimeEnd: '',
    customerSource: undefined,
    repairmanName: '',
    licensePlateNumber: '',
    serviceAdvisor: '',
    technician: ''
  });
  loadWorkOrderList();
};

// 分页处理
const handlePageChange = (page: number) => {
  searchParams.page = page;
  loadWorkOrderList();
};

const handleSizeChange = (size: number) => {
  searchParams.pageSize = size;
  searchParams.page = 1;
  loadWorkOrderList();
};

// 选择变化处理
const handleSelectionChange = (selection: DispatchListItem[]) => {
  selectedRows.value = selection;
};

// 刷新处理
const handleRefresh = () => {
  Promise.all([
    loadWorkOrderList(),
    loadTechnicianList(),
    loadStatistics()
  ]);
};

// 分配工单处理
const handleAssign = (row: DispatchListItem) => {
  selectedWorkOrder.value = row;
  assignmentDialogVisible.value = true;
};

// 重新分配处理
const handleReassign = (row: DispatchListItem) => {
  selectedWorkOrder.value = row;
  reassignmentDialogVisible.value = true;
};

// 暂停处理
const handlePause = async (row: DispatchListItem) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      t('dispatch.messages.inputReason'),
      t('dispatch.dialog.confirmPause'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        inputPlaceholder: t('dispatch.dialog.pauseReason')
      }
    );

    await pauseWorkOrder(row.workOrderNo, reason);
    ElMessage.success(t('dispatch.messages.pauseSuccess'));
    loadWorkOrderList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('暂停工单失败:', error);
      ElMessage.error(t('dispatch.messages.pauseFailed'));
    }
  }
};

// 恢复处理
const handleResume = async (row: DispatchListItem) => {
  try {
    await ElMessageBox.confirm(
      t('dispatch.messages.confirmOperation'),
      t('dispatch.dialog.confirmResume'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );

    await resumeWorkOrder(row.workOrderNo);
    ElMessage.success(t('dispatch.messages.resumeSuccess'));
    loadWorkOrderList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('恢复工单失败:', error);
      ElMessage.error(t('dispatch.messages.resumeFailed'));
    }
  }
};

// 完成处理
const handleComplete = async (row: DispatchListItem) => {
  try {
    const { value: notes } = await ElMessageBox.prompt(
      t('dispatch.dialog.notes'),
      t('dispatch.dialog.confirmComplete'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        inputPlaceholder: t('dispatch.dialog.notes')
      }
    );

    await completeWorkOrder(row.workOrderNo, notes);
    ElMessage.success(t('dispatch.messages.completeSuccess'));
    loadWorkOrderList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成工单失败:', error);
      ElMessage.error(t('dispatch.messages.completeFailed'));
    }
  }
};

// 查看详情处理
const handleViewDetail = (row: DispatchListItem) => {
  selectedWorkOrderNo.value = row.workOrderNo;
  detailDialogVisible.value = true;
};

// 导出处理
const handleExport = async () => {
  try {
    exportLoading.value = true;
    const blob = await exportDispatchData({
      ...searchParams,
      exportType: 'all',
      exportFormat: 'excel'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `dispatch_data_${new Date().toISOString().slice(0, 10)}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success(t('dispatch.messages.exportSuccess'));
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error(t('dispatch.messages.exportFailed'));
  } finally {
    exportLoading.value = false;
  }
};

// 分配确认处理
const handleAssignmentConfirm = async (data: AssignmentFormData) => {
  try {
    assignmentLoading.value = true;
    await assignWorkOrder(data);
    ElMessage.success(t('dispatch.messages.assignSuccess'));
    assignmentDialogVisible.value = false;
    loadWorkOrderList();
    loadStatistics();
  } catch (error) {
    console.error('分配工单失败:', error);
    ElMessage.error(t('dispatch.messages.assignFailed'));
  } finally {
    assignmentLoading.value = false;
  }
};

// 分配关闭处理
const handleAssignmentClose = () => {
  assignmentDialogVisible.value = false;
  selectedWorkOrder.value = null;
};

// 重新分配确认处理
const handleReassignmentConfirm = async (data: ReassignmentFormData) => {
  try {
    reassignmentLoading.value = true;
    await reassignWorkOrder(data);
    ElMessage.success(t('dispatch.messages.reassignSuccess'));
    reassignmentDialogVisible.value = false;
    loadWorkOrderList();
    loadStatistics();
  } catch (error) {
    console.error('重新分配工单失败:', error);
    ElMessage.error(t('dispatch.messages.reassignFailed'));
  } finally {
    reassignmentLoading.value = false;
  }
};

// 重新分配关闭处理
const handleReassignmentClose = () => {
  reassignmentDialogVisible.value = false;
  selectedWorkOrder.value = null;
};

// 详情关闭处理
const handleDetailClose = () => {
  detailDialogVisible.value = false;
  selectedWorkOrderNo.value = '';
};

// 技师分配处理
const handleTechnicianAssign = (technician: TechnicianInfo) => {
  // 可以实现快速分配逻辑
  console.log('技师分配:', technician);
};

// 查看技师日程处理
const handleViewSchedule = (technician: TechnicianInfo) => {
  // 可以实现查看技师日程功能
  console.log('查看技师日程:', technician);
};
</script>

<style scoped>
.dispatch-management-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-subtitle {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.statistics-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.pending {
  border-color: #e6a23c;
}

.stat-card.progress {
  border-color: #409eff;
}

.stat-card.completed {
  border-color: #67c23a;
}

.stat-card.paused {
  border-color: #f56c6c;
}

.stat-card.utilization {
  border-color: #909399;
}

.stat-content {
  padding: 10px 0;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 12px;
  color: #606266;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.technician-panel {
  height: fit-content;
  max-height: calc(100vh - 200px);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-weight: 500;
  color: #303133;
}

.technician-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  padding-right: 8px;
}

.technician-list::-webkit-scrollbar {
  width: 6px;
}

.technician-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.technician-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.technician-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-row) {
  margin-bottom: 0;
}

:deep(.el-col) {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dispatch-management-container {
    padding: 16px;
  }

  .statistics-section .el-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .dispatch-management-container {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .stat-number {
    font-size: 24px;
  }

  .technician-panel {
    margin-top: 20px;
  }
}
</style>
