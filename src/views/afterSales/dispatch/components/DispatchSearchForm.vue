<template>
  <el-card class="mb-20 search-card">
    <template #header>
      <span class="card-title">{{ t('dispatch.searchForm.title') || '筛选条件' }}</span>
    </template>
    
    <el-form :model="localSearchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('dispatch.workOrderNo')">
            <el-input 
              v-model="localSearchParams.workOrderNo" 
              :placeholder="t('dispatch.searchForm.workOrderNoPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('dispatch.priorityLabel')">
            <el-select 
              v-model="localSearchParams.priority" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" value="" />
              <el-option 
                v-for="option in priorityOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('dispatch.workOrderTypeLabel')">
            <el-select 
              v-model="localSearchParams.workOrderType" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" value="" />
              <el-option 
                v-for="option in workOrderTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6" v-if="showAssignmentStatus">
          <el-form-item :label="t('dispatch.assignmentStatusLabel')">
            <el-select 
              v-model="localSearchParams.assignmentStatus" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" value="" />
              <el-option 
                v-for="option in assignmentStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('dispatch.workOrderStatusLabel')">
            <el-select 
              v-model="localSearchParams.workOrderStatus" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" value="" />
              <el-option 
                v-for="option in workOrderStatusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('dispatch.customerSourceLabel')">
            <el-select 
              v-model="localSearchParams.customerSource" 
              :placeholder="tc('pleaseSelect')" 
              clearable
              style="width: 100%"
            >
              <el-option :label="tc('all')" value="" />
              <el-option 
                v-for="option in customerSourceOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('dispatch.repairmanName')">
            <el-input 
              v-model="localSearchParams.repairmanName" 
              :placeholder="t('dispatch.searchForm.repairmanNamePlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('dispatch.licensePlateNumber')">
            <el-input 
              v-model="localSearchParams.licensePlateNumber" 
              :placeholder="t('dispatch.searchForm.licensePlateNumberPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('dispatch.serviceAdvisor')">
            <el-input 
              v-model="localSearchParams.serviceAdvisor" 
              :placeholder="t('dispatch.searchForm.serviceAdvisorPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item :label="t('dispatch.technicianLabel')">
            <el-input 
              v-model="localSearchParams.technician" 
              :placeholder="t('dispatch.searchForm.technicianPlaceholder')" 
              clearable 
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item :label="t('dispatch.creationTime')">
            <el-date-picker
              v-model="creationTimeRange"
              type="datetimerange"
              range-separator="-"
              :start-placeholder="tc('startTime')"
              :end-placeholder="tc('endTime')"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24" class="text-right">
          <el-button type="primary" :icon="Search" @click="handleSearch" :loading="loading">
            {{ tc('search') }}
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            {{ tc('reset') }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { Search, Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { DispatchSearchParams } from '@/types/afterSales/dispatch';

// 组件Props
interface Props {
  searchParams: DispatchSearchParams;
  loading?: boolean;
  showAssignmentStatus?: boolean;
}

// 组件Emits
interface Emits {
  (e: 'search', params: DispatchSearchParams): void;
  (e: 'reset'): void;
  (e: 'update:searchParams', params: DispatchSearchParams): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showAssignmentStatus: true
});

const emit = defineEmits<Emits>();

// 国际化
const { t, tc } = useModuleI18n('afterSales');

// 本地搜索参数
const localSearchParams = reactive<DispatchSearchParams>({ ...props.searchParams });

// 创建时间范围
const creationTimeRange = ref<[string, string] | null>(null);

// 选项数据
const priorityOptions = computed(() => [
  { label: t('dispatch.priority.urgent'), value: 'urgent' },
  { label: t('dispatch.priority.normal'), value: 'normal' }
]);

const workOrderTypeOptions = computed(() => [
  { label: t('dispatch.workOrderType.repair'), value: 'repair' },
  { label: t('dispatch.workOrderType.maintenance'), value: 'maintenance' },
  { label: t('dispatch.workOrderType.insurance'), value: 'insurance' }
]);

const workOrderStatusOptions = computed(() => [
  { label: t('dispatch.workOrderStatus.pendingAssignment'), value: 'pendingAssignment' },
  { label: t('dispatch.workOrderStatus.pendingStart'), value: 'pendingStart' },
  { label: t('dispatch.workOrderStatus.inProgress'), value: 'inProgress' },
  { label: t('dispatch.workOrderStatus.paused'), value: 'paused' },
  { label: t('dispatch.workOrderStatus.completed'), value: 'completed' },
  { label: t('dispatch.workOrderStatus.cancelled'), value: 'cancelled' },
  { label: t('dispatch.workOrderStatus.pendingQualityInspection'), value: 'pendingQualityInspection' }
]);

const assignmentStatusOptions = computed(() => [
  { label: t('dispatch.assignmentStatus.pending'), value: 'pending' },
  { label: t('dispatch.assignmentStatus.assigned'), value: 'assigned' }
]);

const customerSourceOptions = computed(() => [
  { label: t('dispatch.customerSource.appointment'), value: 'appointment' },
  { label: t('dispatch.customerSource.walkIn'), value: 'walkIn' }
]);

// 监听创建时间范围变化
watch(creationTimeRange, (newRange) => {
  if (newRange && newRange.length === 2) {
    localSearchParams.creationTimeStart = newRange[0];
    localSearchParams.creationTimeEnd = newRange[1];
  } else {
    localSearchParams.creationTimeStart = '';
    localSearchParams.creationTimeEnd = '';
  }
});

// 监听外部搜索参数变化
watch(
  () => props.searchParams,
  (newParams) => {
    Object.assign(localSearchParams, newParams);
    
    // 更新创建时间范围
    if (newParams.creationTimeStart && newParams.creationTimeEnd) {
      creationTimeRange.value = [newParams.creationTimeStart, newParams.creationTimeEnd];
    } else {
      creationTimeRange.value = null;
    }
  },
  { deep: true }
);

// 搜索处理
const handleSearch = () => {
  emit('search', { ...localSearchParams });
  emit('update:searchParams', { ...localSearchParams });
};

// 重置处理
const handleReset = () => {
  Object.assign(localSearchParams, {
    page: 1,
    pageSize: 10,
    workOrderNo: '',
    priority: undefined,
    workOrderType: undefined,
    assignmentStatus: undefined,
    workOrderStatus: undefined,
    creationTimeStart: '',
    creationTimeEnd: '',
    customerSource: undefined,
    repairmanName: '',
    licensePlateNumber: '',
    serviceAdvisor: '',
    technician: ''
  });
  creationTimeRange.value = null;
  emit('reset');
};
</script>

<style scoped>
.search-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: 500;
  color: #303133;
}

.search-form {
  margin-top: 10px;
}

.text-right {
  text-align: right;
  padding-top: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style>
