<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getInspectionList,
  submitForConfirm,
  recallInspection,
  customerConfirm,
  createWorkOrder,
  getPrintableInspection
} from '@/api/modules/afterSales/inspection';
import type {
  InspectionListItem,
  InspectionSearchParams
} from '@/types/afterSales/inspection.d.ts';
// 导入子组件
import InspectionSearchForm from './components/InspectionSearchForm.vue';
import InspectionTable from './components/InspectionTable.vue';
import DetailEditDialog from './components/DetailEditDialog.vue';
import CustomerConfirmDialog from './components/CustomerConfirmDialog.vue';
import CreateWorkOrderDialog from './components/CreateWorkOrderDialog.vue';

const { t, tc } = useModuleI18n('afterSales.inspection');

// 搜索相关
const searchParams = reactive<InspectionSearchParams>({
  inspectionNo: '',
  inspectionStatus: '',
  licensePlateNo: '',
  repairmanName: '',
  serviceAdvisor: '',
  repairmanPhone: '',
  createTimeRange: null,
});
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  searchParams.createTimeRange = newVal;
});

const inspectionList = ref<InspectionListItem[]>([]);
const loading = ref(false);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 弹窗相关
const detailEditDialogVisible = ref(false);
const customerConfirmDialogVisible = ref(false);
const createWorkOrderDialogVisible = ref(false);
const currentRecord = ref<InspectionListItem | null>(null);
const isEditMode = ref(false);

// 获取环检单列表
const fetchInspectionList = async () => {
  loading.value = true;
  try {
    const response = await getInspectionList({
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    });
    inspectionList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch inspection list:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};



// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchInspectionList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    inspectionNo: '',
    inspectionStatus: '',
    licensePlateNo: '',
    repairmanName: '',
    serviceAdvisor: '',
    repairmanPhone: '',
    createTimeRange: null,
  });
  dateRange.value = null;
  pagination.page = 1;
  fetchInspectionList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchInspectionList();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchInspectionList();
};



// 提交客户确认
const handleSubmitForConfirm = async (row: InspectionListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmSubmit'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await submitForConfirm(row.inspectionNo);
        ElMessage.success(t('messages.submitSuccess'));
        fetchInspectionList();
      } catch (error) {
        console.error('Failed to submit for confirm:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 撤回检查
const handleRecallInspection = async (row: InspectionListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmRecall'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await recallInspection(row.inspectionNo);
        ElMessage.success(t('messages.recallSuccess'));
        fetchInspectionList();
      } catch (error) {
        console.error('Failed to recall inspection:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 客户确认
const handleCustomerConfirm = (row: InspectionListItem) => {
  currentRecord.value = row;
  customerConfirmDialogVisible.value = true;
};

// 查看详情
const handleViewDetail = (row: InspectionListItem) => {
  currentRecord.value = row;
  isEditMode.value = false;
  detailEditDialogVisible.value = true;
};

// 编辑详情
const handleEditDetail = (row: InspectionListItem) => {
  currentRecord.value = row;
  isEditMode.value = true;
  detailEditDialogVisible.value = true;
};

// 保存环检内容
const handleInspectionContentSave = async (data: any) => {
  if (!currentRecord.value) return;

  try {
    // 这里应该调用保存环检内容的API
    // await saveInspectionContent(currentRecord.value.inspectionNo, data);
    ElMessage.success('环检内容保存成功');
    detailEditDialogVisible.value = false;
    fetchInspectionList();
  } catch (error) {
    console.error('Failed to save inspection content:', error);
    ElMessage.error('保存失败');
  }
};



// 客户确认提交
const handleCustomerConfirmSubmit = async (data: { confirmTime: string; signaturePhoto: string; confirmChannel: string }) => {
  if (!currentRecord.value) return;

  try {
    await customerConfirm(currentRecord.value.inspectionNo, data.confirmTime);
    ElMessage.success(t('messages.confirmSuccess'));
    customerConfirmDialogVisible.value = false;
    fetchInspectionList();
  } catch (error) {
    console.error('Failed to customer confirm:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 创建工单
const handleCreateWorkOrder = (row: InspectionListItem) => {
  currentRecord.value = row;
  createWorkOrderDialogVisible.value = true;
};

// 创建工单确认
const handleCreateWorkOrderConfirm = async (workOrderData: any) => {
  if (!currentRecord.value) return;

  try {
    const response = await createWorkOrder(workOrderData);
    ElMessage.success(t('createWorkOrder.messages.createSuccess'));
    if (response.workOrderNo) {
      ElMessage.info(`工单号：${response.workOrderNo}`);
    }
    createWorkOrderDialogVisible.value = false;
    fetchInspectionList();
  } catch (error) {
    console.error('Failed to create work order:', error);
    ElMessage.error(t('createWorkOrder.messages.createFailed'));
  }
};

// 打印环检单
const handlePrintInspection = async (row: InspectionListItem) => {
  try {
    const response = await getPrintableInspection(row.inspectionNo);

    // 创建新窗口并打印
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(response.html);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    } else {
      ElMessage.error('无法打开打印窗口，请检查浏览器设置');
    }
  } catch (error) {
    console.error('Failed to print inspection:', error);
    ElMessage.error('打印失败');
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchInspectionList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索表单 -->
    <InspectionSearchForm
      v-model:search-params="searchParams"
      v-model:date-range="dateRange"
      @search="handleSearch"
      @reset="resetSearch"
    />

    <!-- 数据表格 -->
    <InspectionTable
      :inspection-list="inspectionList"
      :loading="loading"
      :pagination="pagination"
      @submit-for-confirm="handleSubmitForConfirm"
      @recall-inspection="handleRecallInspection"
      @customer-confirm="handleCustomerConfirm"
      @create-work-order="handleCreateWorkOrder"
      @view-detail="handleViewDetail"
      @edit-detail="handleEditDetail"
      @print-inspection="handlePrintInspection"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />



    <!-- 详情编辑弹窗 -->
    <DetailEditDialog
      v-model:visible="detailEditDialogVisible"
      :record-data="currentRecord"
      :is-edit="isEditMode"
      @save="handleInspectionContentSave"
    />

    <!-- 客户确认弹窗 -->
    <CustomerConfirmDialog
      v-model:visible="customerConfirmDialogVisible"
      :record-data="currentRecord"
      @confirm="handleCustomerConfirmSubmit"
    />

    <!-- 创建工单弹窗 -->
    <CreateWorkOrderDialog
      v-model:visible="createWorkOrderDialogVisible"
      :record-data="currentRecord"
      @confirm="handleCreateWorkOrderConfirm"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}
</style>
