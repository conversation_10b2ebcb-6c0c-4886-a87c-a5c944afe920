<template>
  <ElDialog
    :model-value="visible"
    :title="`${t('createWorkOrder.title')} - ${recordData?.inspectionNo || ''}`"
    width="1400px"
    :before-close="handleClose"
    @update:model-value="$emit('update:visible', $event)"
    top="5vh"
    class="create-work-order-dialog"
  >
    <div class="create-work-order-content">
      <!-- 关联环检单信息 -->
      <ElCard class="info-section" shadow="never">
        <template #header>
          <span class="section-title">{{ t('createWorkOrder.relatedInspection') }}</span>
        </template>
        <ElDescriptions :column="3" border>
          <ElDescriptionsItem :label="t('table.inspectionStatus')">
            {{ getStatusText(recordData?.inspectionStatus) }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.registerType')">
            {{ getRegisterTypeText(recordData?.registerType) }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.serviceType')">
            {{ getServiceTypeText(recordData?.serviceType) }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.serviceAdvisor')">
            {{ recordData?.serviceAdvisor }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.createTime')">
            {{ recordData?.createTime }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.confirmChannel')">
            {{ recordData?.confirmChannel || '-' }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.customerConfirmTime')">
            {{ recordData?.customerConfirmTime || '-' }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </ElCard>

      <!-- 客户信息 -->
      <ElCard class="info-section" shadow="never">
        <template #header>
          <span class="section-title">{{ t('createWorkOrder.customerInfo') }}</span>
        </template>
        <ElDescriptions :column="2" border>
          <ElDescriptionsItem :label="t('table.repairmanName')">
            {{ recordData?.repairmanName }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.repairmanPhone')">
            {{ recordData?.repairmanPhone }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </ElCard>

      <!-- 车辆信息 -->
      <ElCard class="info-section" shadow="never">
        <template #header>
          <span class="section-title">{{ t('createWorkOrder.vehicleInfo') }}</span>
        </template>
        <ElDescriptions :column="3" border>
          <ElDescriptionsItem :label="t('table.licensePlateNo')">
            {{ recordData?.licensePlateNo }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="VIN">
            {{ recordData?.vin || '-' }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.vehicleModel')">
            {{ recordData?.vehicleModel }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.vehicleConfig')">
            {{ recordData?.vehicleConfig }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.color')">
            {{ recordData?.color }}
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.mileage')">
            {{ recordData?.mileage }} km
          </ElDescriptionsItem>
          <ElDescriptionsItem :label="t('table.vehicleAge')">
            {{ recordData?.vehicleAge }}{{ t('inspectionContent.months') }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </ElCard>

      <!-- 工单基本信息 -->
      <ElCard class="info-section" shadow="never">
        <template #header>
          <span class="section-title">{{ t('createWorkOrder.basicInfo') }}</span>
        </template>
        <ElForm :model="formData" label-width="100px">
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem :label="t('createWorkOrder.workOrderType')">
                <ElRadioGroup v-model="formData.serviceType">
                  <ElRadio value="repair">{{ t('createWorkOrder.type.repair') }}</ElRadio>
                  <ElRadio value="maintenance">{{ t('createWorkOrder.type.maintenance') }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem :label="t('createWorkOrder.priority')">
                <ElRadioGroup v-model="formData.priority">
                  <ElRadio value="normal">{{ t('createWorkOrder.priorityStatus.normal') }}</ElRadio>
                  <ElRadio value="urgent">{{ t('createWorkOrder.priorityStatus.urgent') }}</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElFormItem :label="t('createWorkOrder.description')">
            <ElInput
              v-model="formData.description"
              type="textarea"
              :rows="3"
              :placeholder="t('createWorkOrder.descriptionPlaceholder')"
            />
          </ElFormItem>
        </ElForm>
      </ElCard>

      <!-- 项目选择 -->
      <ElCard class="info-section" shadow="never">
        <template #header>
          <span class="section-title">{{ t('createWorkOrder.projectSelection') }}</span>
        </template>
        <div class="project-search">
          <ElRow :gutter="12">
            <ElCol :span="18">
              <ElSelect
                v-model="selectedProject"
                :placeholder="t('createWorkOrder.selectProject')"
                filterable
                clearable
                style="width: 100%"
              >
                <ElOption
                  v-for="project in availableProjects"
                  :key="project.id"
                  :label="`${project.code} - ${project.name}`"
                  :value="project.id"
                />
              </ElSelect>
            </ElCol>
            <ElCol :span="6">
              <ElButton type="primary" @click="addProject">{{ t('createWorkOrder.addProject') }}</ElButton>
            </ElCol>
          </ElRow>
        </div>
        <div class="suggested-projects">
          <span class="label">{{ t('createWorkOrder.suggestedProjects') }}</span>
          <ElTag
            v-for="item in suggestedProjects"
            :key="item.id"
            class="project-tag"
            @click="selectSuggestedProject(item)"
          >
            {{ item.name }}
          </ElTag>
        </div>
      </ElCard>

      <!-- 工时项目 -->
      <ElCard class="info-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span class="section-title">{{ t('createWorkOrder.laborItems') }}</span>
            <ElButton type="primary" size="small" @click="addLaborItem">{{ t('createWorkOrder.addLabor') }}</ElButton>
          </div>
        </template>
        <ElTable :data="laborItems" border>
          <ElTableColumn prop="type" :label="t('createWorkOrder.table.type')" width="80" />
          <ElTableColumn prop="code" :label="t('createWorkOrder.table.projectCode')" width="120" />
          <ElTableColumn prop="name" :label="t('createWorkOrder.table.projectName')" />
          <ElTableColumn prop="isClaim" :label="t('createWorkOrder.table.isClaim')" width="60">
            <template #default="scope">
              {{ scope.row.isClaim ? tc('yes') : tc('no') }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="isAdditional" :label="t('createWorkOrder.table.isAdditional')" width="60">
            <template #default="scope">
              {{ scope.row.isAdditional ? tc('yes') : tc('no') }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="hours" :label="t('createWorkOrder.table.hours')" width="80" />
          <ElTableColumn prop="unitPrice" :label="t('createWorkOrder.table.unitPrice')" width="80" />
          <ElTableColumn prop="subtotal" :label="t('createWorkOrder.table.subtotal')" width="80" />
          <ElTableColumn :label="t('createWorkOrder.table.operations')" width="60">
            <template #default="scope">
              <ElButton type="danger" size="small" link @click="removeLaborItem(scope.$index)">{{ t('createWorkOrder.table.delete') }}</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
        <div class="total-row">
          <span>{{ t('createWorkOrder.laborTotal') }}: {{ laborTotal }}{{ tc('currency') }}</span>
        </div>
      </ElCard>

      <!-- 零件项目 -->
      <ElCard class="info-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span class="section-title">{{ t('createWorkOrder.partItems') }}</span>
            <ElButton type="primary" size="small" @click="addPartItem">{{ t('createWorkOrder.addPart') }}</ElButton>
          </div>
        </template>
        <ElTable :data="partItems" border>
          <ElTableColumn prop="type" :label="t('createWorkOrder.table.type')" width="80" />
          <ElTableColumn prop="code" :label="t('createWorkOrder.table.partCode')" width="120" />
          <ElTableColumn prop="name" :label="t('createWorkOrder.table.partName')" />
          <ElTableColumn prop="isClaim" :label="t('createWorkOrder.table.isClaim')" width="60">
            <template #default="scope">
              {{ scope.row.isClaim ? tc('yes') : tc('no') }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="isAdditional" :label="t('createWorkOrder.table.isAdditional')" width="60">
            <template #default="scope">
              {{ scope.row.isAdditional ? tc('yes') : tc('no') }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="stock" :label="t('createWorkOrder.table.stock')" width="60" />
          <ElTableColumn prop="quantity" :label="t('createWorkOrder.table.quantity')" width="60" />
          <ElTableColumn prop="unitPrice" :label="t('createWorkOrder.table.unitPrice')" width="80" />
          <ElTableColumn prop="subtotal" :label="t('createWorkOrder.table.subtotal')" width="80" />
          <ElTableColumn :label="t('createWorkOrder.table.operations')" width="60">
            <template #default="scope">
              <ElButton type="danger" size="small" link @click="removePartItem(scope.$index)">{{ t('createWorkOrder.table.delete') }}</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
        <div class="total-row">
          <span>{{ t('createWorkOrder.partTotal') }}: {{ partTotal }}{{ tc('currency') }}</span>
        </div>
      </ElCard>

      <!-- 费用预算统计 -->
      <ElCard class="info-section" shadow="never">
        <template #header>
          <span class="section-title">{{ t('createWorkOrder.costSummary') }}</span>
        </template>
        <div class="cost-summary">
          <span>{{ t('createWorkOrder.laborCost') }}: {{ laborTotal }}{{ tc('currency') }}</span>
          <span>{{ t('createWorkOrder.partCost') }}: {{ partTotal }}{{ tc('currency') }}</span>
          <span class="total-amount">{{ t('createWorkOrder.totalAmount') }}: {{ totalAmount }}{{ tc('currency') }}</span>
        </div>
      </ElCard>
      <ElCard class="work-order-form-card" :header="t('createWorkOrder.workOrderInfo')">
        <ElForm
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem :label="t('createWorkOrder.fields.serviceType')" prop="serviceType">
                <ElSelect
                  v-model="formData.serviceType"
                  :placeholder="t('createWorkOrder.fields.serviceTypePlaceholder')"
                  style="width: 100%"
                >
                  <ElOption
                    value="maintenance"
                    :label="t('serviceType.maintenance')"
                  />
                  <ElOption
                    value="repair"
                    :label="t('serviceType.repair')"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem :label="t('createWorkOrder.fields.priority')" prop="priority">
                <ElSelect
                  v-model="formData.priority"
                  :placeholder="t('createWorkOrder.fields.priorityPlaceholder')"
                  style="width: 100%"
                >
                  <ElOption value="low" :label="t('createWorkOrder.priorityStatus.low')" />
                  <ElOption value="medium" :label="t('createWorkOrder.priorityStatus.medium')" />
                  <ElOption value="high" :label="t('createWorkOrder.priorityStatus.high')" />
                  <ElOption value="urgent" :label="t('createWorkOrder.priorityStatus.urgent')" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElFormItem :label="t('createWorkOrder.fields.description')" prop="description">
            <ElInput
              v-model="formData.description"
              type="textarea"
              :rows="4"
              :placeholder="t('createWorkOrder.fields.descriptionPlaceholder')"
            />
          </ElFormItem>

          <!-- 推荐维修项目 -->
          <ElFormItem :label="t('createWorkOrder.fields.recommendedItems')">
            <div class="recommended-items">
              <ElCheckbox
                v-for="item in recommendedItems"
                :key="item.id"
                v-model="item.selected"
                :label="item.name"
                class="recommended-item"
              />
            </div>
          </ElFormItem>
        </ElForm>
      </ElCard>
    </div>

    <!-- 工时项目选择弹窗 -->
    <LaborSelectDialog
      v-model:visible="laborSelectDialogVisible"
      :items="availableLaborItems"
      @select="selectLaborItem"
    />

    <!-- 零件项目选择弹窗 -->
    <PartSelectDialog
      v-model:visible="partSelectDialogVisible"
      :items="availablePartItems"
      @select="selectPartItem"
    />

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">{{ tc('cancel') }}</ElButton>
        <ElButton type="info" @click="handleSaveDraft">{{ t('createWorkOrder.saveDraft') }}</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleConfirm">
          {{ t('createWorkOrder.createAndPush') }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import {
  ElDialog,
  ElCard,
  ElDescriptions,
  ElDescriptionsItem,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElRow,
  ElCol,
  ElCheckbox,
  ElMessage,
  ElRadioGroup,
  ElRadio,
  ElTable,
  ElTableColumn,
  ElTag,
  ElIcon
} from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import LaborSelectDialog from './LaborSelectDialog.vue';
import PartSelectDialog from './PartSelectDialog.vue';
import type { InspectionListItem } from '@/types/afterSales/inspection.d.ts';

interface Props {
  visible: boolean;
  recordData: InspectionListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: any): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const formRef = ref();
const loading = ref(false);

// 状态文本转换方法
const getStatusText = (status: string) => {
  return t(`status.${status}`);
};

const getRegisterTypeText = (type: string) => {
  return type === 'appointment' ? t('registerType.appointment') : t('registerType.walk_in');
};

const getServiceTypeText = (type: string) => {
  return type === 'maintenance' ? t('serviceType.maintenance') : t('serviceType.repair');
};

const formData = reactive({
  serviceType: 'repair',
  priority: 'normal',
  description: '',
});

const selectedProject = ref('');

const availableProjects = ref([
  { id: 1, code: 'P001', name: '机油更换' },
  { id: 2, code: 'P002', name: '刹车片更换' },
  { id: 3, code: 'P003', name: '空调清洗' },
  { id: 4, code: 'P004', name: '轮胎更换' },
  { id: 5, code: 'P005', name: '电池检测' },
]);

const suggestedProjects = ref([
  { id: 1, name: '机油更换' },
  { id: 2, name: '刹车片更换' },
  { id: 3, name: '空调清洗' },
]);

const laborItems = ref([
  { type: '保养', code: 'L001', name: '机油更换', isClaim: false, isAdditional: false, hours: 1.0, unitPrice: 80, subtotal: 80 },
  { type: '维修', code: 'L002', name: '刹车维修', isClaim: true, isAdditional: false, hours: 2.5, unitPrice: 120, subtotal: 300 },
]);

const partItems = ref([
  { type: '滤清器', code: 'PT001', name: '机油滤清', isClaim: false, isAdditional: false, stock: 50, quantity: 1, unitPrice: 25, subtotal: 25 },
  { type: '刹车系统', code: 'PT002', name: '刹车片', isClaim: true, isAdditional: false, stock: 8, quantity: 2, unitPrice: 150, subtotal: 300 },
]);

// 工时项目选择弹窗
const laborSelectDialogVisible = ref(false);
const availableLaborItems = ref([
  { id: 1, code: 'L001', name: '机油更换', type: '保养', hours: 1.0, unitPrice: 80 },
  { id: 2, code: 'L002', name: '刹车维修', type: '维修', hours: 2.5, unitPrice: 120 },
  { id: 3, code: 'L003', name: '轮胎更换', type: '维修', hours: 1.5, unitPrice: 100 },
]);

// 零件项目选择弹窗
const partSelectDialogVisible = ref(false);
const availablePartItems = ref([
  { id: 1, code: 'PT001', name: '机油滤清', type: '滤清器', stock: 50, unitPrice: 25 },
  { id: 2, code: 'PT002', name: '刹车片', type: '刹车系统', stock: 8, unitPrice: 150 },
  { id: 3, code: 'PT003', name: '空调滤芯', type: '滤清器', stock: 20, unitPrice: 35 },
]);

const laborTotal = computed(() => {
  return laborItems.value.reduce((total, item) => total + item.subtotal, 0);
});

const partTotal = computed(() => {
  return partItems.value.reduce((total, item) => total + item.subtotal, 0);
});

const totalAmount = computed(() => {
  return laborTotal.value + partTotal.value;
});

const formRules = computed(() => ({
  serviceType: [
    { required: true, message: t('createWorkOrder.validation.serviceTypeRequired'), trigger: 'change' }
  ],
  priority: [
    { required: true, message: t('createWorkOrder.validation.priorityRequired'), trigger: 'change' }
  ],
  description: [
    { required: true, message: t('createWorkOrder.validation.descriptionRequired'), trigger: 'blur' }
  ]
}));

const addProject = () => {
  if (selectedProject.value) {
    const project = availableProjects.value.find(p => p.id === selectedProject.value);
    if (project) {
      console.log('添加项目:', project);
      selectedProject.value = '';
    }
  }
};

const selectSuggestedProject = (project: any) => {
  // 添加建议项目到工时或零件列表
  console.log('选择建议项目:', project);
};

const addLaborItem = () => {
  laborSelectDialogVisible.value = true;
};

const selectLaborItem = (item: any) => {
  laborItems.value.push({
    type: item.type,
    code: item.code,
    name: item.name,
    isClaim: false,
    isAdditional: false,
    hours: item.hours,
    unitPrice: item.unitPrice,
    subtotal: item.hours * item.unitPrice
  });
  laborSelectDialogVisible.value = false;
};

const removeLaborItem = (index: number) => {
  laborItems.value.splice(index, 1);
};

const addPartItem = () => {
  partSelectDialogVisible.value = true;
};

const selectPartItem = (item: any) => {
  partItems.value.push({
    type: item.type,
    code: item.code,
    name: item.name,
    isClaim: false,
    isAdditional: false,
    stock: item.stock,
    quantity: 1,
    unitPrice: item.unitPrice,
    subtotal: item.unitPrice
  });
  partSelectDialogVisible.value = false;
};

const removePartItem = (index: number) => {
  partItems.value.splice(index, 1);
};

const handleSaveDraft = () => {
  // 保存草稿逻辑
  console.log('保存草稿');
};

const handleClose = () => {
  emit('update:visible', false);
  resetForm();
};

const resetForm = () => {
  formRef.value?.resetFields();
  recommendedItems.value.forEach(item => item.selected = false);
};

const handleConfirm = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    const selectedItems = recommendedItems.value.filter(item => item.selected);
    const workOrderData = {
      ...formData,
      recommendedItems: selectedItems,
      inspectionNo: recordData?.inspectionNo
    };

    emit('confirm', workOrderData);
    ElMessage.success(t('createWorkOrder.messages.createSuccess'));
    handleClose();
  } catch (error) {
    console.error('Form validation failed:', error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
:deep(.create-work-order-dialog) {
  .el-dialog__body {
    max-height: 80vh;
    overflow-y: auto;
    padding: 20px;
  }
}

.create-work-order-content {
  .info-section {
    margin-bottom: 20px;

    .section-title {
      font-weight: 600;
      color: #303133;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .section-title {
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .project-search {
    margin-bottom: 16px;
  }

  .suggested-projects {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    .label {
      font-weight: 500;
      color: #606266;
    }

    .project-tag {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #409eff;
        color: white;
      }
    }
  }

  .total-row {
    text-align: right;
    padding: 12px 0;
    font-weight: 600;
    border-top: 1px solid #e4e7ed;
    margin-top: 12px;
  }

  .cost-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 6px;

    .total-amount {
      font-size: 18px;
      font-weight: 600;
      color: #e6a23c;
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}
</style>
