<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import {
  ElDialog,
  ElDescriptions,
  ElDescriptionsItem,
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElRadioGroup,
  ElRadio,
  ElUpload,
  ElButton,
  ElMessage
} from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InspectionListItem, InspectionContent, CheckResult } from '@/types/afterSales/inspection.d.ts';

interface Props {
  visible: boolean;
  recordData: InspectionListItem | null;
  isEdit?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'save', data: InspectionContent): void;
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false
});
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const currentRecord = computed(() => props.recordData);

// 环检内容表单数据
const inspectionForm = reactive<InspectionContent>({
  parkingAreaRecord: {
    waitingArea: '',
    leavingArea: '',
    parkingPosition: ''
  },
  dashboardCheck: {
    mileageRecord: 0,
    batteryLevel: '',
    drivingRange: 0,
    energyConsumption: 0
  },
  functionalCheck: {
    instrumentsAndIndicators: { result: 'good', photos: [], notes: '' },
    airConditioningSystem: { result: 'good', photos: [], notes: '' },
    wipersAndWashers: { result: 'good', photos: [], notes: '' },
    infotainmentSystem: { result: 'good', photos: [], notes: '' }
  },
  warningLightCheck: {
    batterySystem: { result: 'good', photos: [], notes: '' },
    motorSystem: { result: 'good', photos: [], notes: '' },
    chargingSystem: { result: 'good', photos: [], notes: '' }
  },
  appearanceCheck: {
    frontCheck: { result: 'good', photos: [], notes: '' },
    rearCheck: { result: 'good', photos: [], notes: '' },
    leftCheck: { result: 'good', photos: [], notes: '' },
    rightCheck: { result: 'good', photos: [], notes: '' },
    roofCheck: { result: 'good', photos: [], notes: '' },
    chargingPortCover: { result: 'good', photos: [], notes: '' }
  },
  electricSystemCheck: {
    highVoltageBattery: { result: 'good', photos: [], notes: '' },
    motorSystemCheck: { result: 'good', photos: [], notes: '' },
    chargingSystemCheck: { result: 'good', photos: [], notes: '' }
  },
  tireCheck: {
    treadDepth: {
      frontRight: 0,
      frontLeft: 0,
      rearRight: 0,
      rearLeft: 0
    },
    tirePressure: {
      frontRight: 0,
      frontLeft: 0,
      rearRight: 0,
      rearLeft: 0
    },
    tpmsFunction: { result: 'good', photos: [], notes: '' }
  },
  customerIssues: ''
});

const getStatusText = (status: string) => {
  return t(`status.${status}`);
};

const getRegisterTypeText = (type: string) => {
  return type === 'appointment' ? t('registerType.appointment') : t('registerType.walk_in');
};

const getServiceTypeText = (type: string) => {
  return type === 'maintenance' ? t('serviceType.maintenance') : t('serviceType.repair');
};

const getCheckResultText = (result: CheckResult) => {
  const resultMap = {
    good: '良好',
    attention: '需要注意',
    poor: '不良',
    not_applicable: '不适用'
  };
  return resultMap[result];
};

const handleSave = () => {
  emit('save', inspectionForm);
  dialogVisible.value = false;
};

const handleCancel = () => {
  dialogVisible.value = false;
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${isEdit ? t('dialog.editTitle') : t('dialog.viewTitle')} - ${currentRecord?.inspectionNo || ''}`"
    width="1400px"
    :close-on-click-modal="false"
    top="5vh"
    class="inspection-detail-dialog"
  >
    <div v-if="currentRecord" class="inspection-detail">
      <!-- 客户信息区 (只读) -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">{{ t('createWorkOrder.customerInfo') }}</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="t('table.repairmanName')">
            {{ currentRecord.repairmanName }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.repairmanPhone')">
            {{ currentRecord.repairmanPhone }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 车辆信息区 (只读) -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">{{ t('createWorkOrder.vehicleInfo') }}</span>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item :label="t('table.licensePlateNo')">
            {{ currentRecord.licensePlateNo }}
          </el-descriptions-item>
          <el-descriptions-item label="VIN">
            {{ currentRecord.vin || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.vehicleModel')">
            {{ currentRecord.vehicleModel }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.vehicleConfig')">
            {{ currentRecord.vehicleConfig }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.color')">
            {{ currentRecord.color }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.mileage')">
            {{ currentRecord.mileage }} km
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.vehicleAge')">
            {{ currentRecord.vehicleAge }}{{ t('inspectionContent.months') }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 环检单信息区 (只读) -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">{{ t('inspectionContent.inspectionInfo') }}</span>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item :label="t('table.inspectionStatus')">
            {{ getStatusText(currentRecord.inspectionStatus) }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.registerType')">
            {{ getRegisterTypeText(currentRecord.registerType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.serviceType')">
            {{ getServiceTypeText(currentRecord.serviceType) }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.serviceAdvisor')">
            {{ currentRecord.serviceAdvisor }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.createTime')">
            {{ currentRecord.createTime }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.confirmChannel')">
            {{ currentRecord.confirmChannel || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('table.customerConfirmTime')">
            {{ currentRecord.customerConfirmTime || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 环检内容清单区 (查看/编辑) -->
      <el-card class="inspection-content" shadow="never">
        <template #header>
          <span class="card-title">{{ t('inspectionContent.title') }}</span>
        </template>

        <el-form :model="inspectionForm" label-position="top">
          <!-- 停车区域记录 -->
          <div class="section-card">
            <h4 class="section-title">{{ t('inspectionContent.parkingArea.title') }}</h4>
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item :label="t('inspectionContent.parkingArea.waitingArea')">
                  <el-input v-model="inspectionForm.parkingAreaRecord.waitingArea" :placeholder="t('inspectionContent.parkingArea.waitingAreaPlaceholder')" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('inspectionContent.parkingArea.leavingArea')">
                  <el-input v-model="inspectionForm.parkingAreaRecord.leavingArea" :placeholder="t('inspectionContent.parkingArea.leavingAreaPlaceholder')" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('inspectionContent.parkingArea.parkingPosition')">
                  <el-input v-model="inspectionForm.parkingAreaRecord.parkingPosition" :placeholder="t('inspectionContent.parkingArea.parkingPositionPlaceholder')" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 仪表盘检查 -->
          <div class="section-card">
            <h4 class="section-title">{{ t('inspectionContent.dashboard.title') }}</h4>
            <el-row :gutter="16">
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.dashboard.mileageRecord')">
                  <el-input-number v-model="inspectionForm.dashboardCheck.mileageRecord" :min="0" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.dashboard.batteryLevel')">
                  <el-select v-model="inspectionForm.dashboardCheck.batteryLevel" style="width: 100%" :disabled="!isEdit">
                    <el-option label="0-20%" value="0-20%" />
                    <el-option label="20-40%" value="20-40%" />
                    <el-option label="40-60%" value="40-60%" />
                    <el-option label="60-80%" value="60-80%" />
                    <el-option label="80-100%" value="80-100%" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.dashboard.drivingRange')">
                  <el-input-number v-model="inspectionForm.dashboardCheck.drivingRange" :min="0" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.dashboard.energyConsumption')">
                  <el-input-number v-model="inspectionForm.dashboardCheck.energyConsumption" :min="0" :precision="1" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 功能性检查 -->
          <div class="section-card">
            <h4 class="section-title">{{ t('inspectionContent.functional.title') }}</h4>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.functional.instrumentsAndIndicators') }}:</span>
              <el-radio-group v-model="inspectionForm.functionalCheck.instrumentsAndIndicators.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.functionalCheck.instrumentsAndIndicators.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.functional.airConditioningSystem') }}:</span>
              <el-radio-group v-model="inspectionForm.functionalCheck.airConditioningSystem.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.functionalCheck.airConditioningSystem.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.functional.wipersAndWashers') }}:</span>
              <el-radio-group v-model="inspectionForm.functionalCheck.wipersAndWashers.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.functionalCheck.wipersAndWashers.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.functional.infotainmentSystem') }}:</span>
              <el-radio-group v-model="inspectionForm.functionalCheck.infotainmentSystem.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.functionalCheck.infotainmentSystem.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
          </div>

          <!-- 警告灯检查 -->
          <div class="section-card">
            <h4 class="section-title">{{ t('inspectionContent.warningLight.title') }}</h4>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.warningLight.batterySystem') }}:</span>
              <el-radio-group v-model="inspectionForm.warningLightCheck.batterySystem.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.warningLightCheck.batterySystem.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.warningLight.motorSystem') }}:</span>
              <el-radio-group v-model="inspectionForm.warningLightCheck.motorSystem.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.warningLightCheck.motorSystem.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.warningLight.chargingSystem') }}:</span>
              <el-radio-group v-model="inspectionForm.warningLightCheck.chargingSystem.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.warningLightCheck.chargingSystem.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
          </div>

          <!-- 外观检查 -->
          <div class="section-card">
            <h4 class="section-title">{{ t('inspectionContent.appearance.title') }}</h4>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.appearance.frontCheck') }}:</span>
              <el-radio-group v-model="inspectionForm.appearanceCheck.frontCheck.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.appearanceCheck.frontCheck.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.appearance.rearCheck') }}:</span>
              <el-radio-group v-model="inspectionForm.appearanceCheck.rearCheck.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.appearanceCheck.rearCheck.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.appearance.leftCheck') }}:</span>
              <el-radio-group v-model="inspectionForm.appearanceCheck.leftCheck.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.appearanceCheck.leftCheck.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.appearance.rightCheck') }}:</span>
              <el-radio-group v-model="inspectionForm.appearanceCheck.rightCheck.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.appearanceCheck.rightCheck.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.appearance.roofCheck') }}:</span>
              <el-radio-group v-model="inspectionForm.appearanceCheck.roofCheck.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.appearanceCheck.roofCheck.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.appearance.chargingPortCover') }}:</span>
              <el-radio-group v-model="inspectionForm.appearanceCheck.chargingPortCover.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.appearanceCheck.chargingPortCover.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
          </div>

          <!-- 电动系统检查 -->
          <div class="section-card">
            <h4 class="section-title">{{ t('inspectionContent.electricSystem.title') }}</h4>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.electricSystem.highVoltageBattery') }}:</span>
              <el-radio-group v-model="inspectionForm.electricSystemCheck.highVoltageBattery.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.electricSystemCheck.highVoltageBattery.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.electricSystem.motorSystemCheck') }}:</span>
              <el-radio-group v-model="inspectionForm.electricSystemCheck.motorSystemCheck.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.electricSystemCheck.motorSystemCheck.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.electricSystem.chargingSystemCheck') }}:</span>
              <el-radio-group v-model="inspectionForm.electricSystemCheck.chargingSystemCheck.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.electricSystemCheck.chargingSystemCheck.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
          </div>

          <!-- 轮胎检查 -->
          <div class="section-card">
            <h4 class="section-title">{{ t('inspectionContent.tire.title') }}</h4>
            <el-row :gutter="16" class="tire-inputs">
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.tire.treadDepthFR')">
                  <el-input-number v-model="inspectionForm.tireCheck.treadDepth.frontRight" :min="0" :precision="1" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.tire.treadDepthFL')">
                  <el-input-number v-model="inspectionForm.tireCheck.treadDepth.frontLeft" :min="0" :precision="1" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.tire.treadDepthRR')">
                  <el-input-number v-model="inspectionForm.tireCheck.treadDepth.rearRight" :min="0" :precision="1" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.tire.treadDepthRL')">
                  <el-input-number v-model="inspectionForm.tireCheck.treadDepth.rearLeft" :min="0" :precision="1" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="16" class="tire-inputs">
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.tire.tirePressureFR')">
                  <el-input-number v-model="inspectionForm.tireCheck.tirePressure.frontRight" :min="0" :precision="1" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.tire.tirePressureFL')">
                  <el-input-number v-model="inspectionForm.tireCheck.tirePressure.frontLeft" :min="0" :precision="1" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.tire.tirePressureRR')">
                  <el-input-number v-model="inspectionForm.tireCheck.tirePressure.rearRight" :min="0" :precision="1" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('inspectionContent.tire.tirePressureRL')">
                  <el-input-number v-model="inspectionForm.tireCheck.tirePressure.rearLeft" :min="0" :precision="1" style="width: 100%" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="check-item">
              <span class="item-label">{{ t('inspectionContent.tire.tpmsFunction') }}:</span>
              <el-radio-group v-model="inspectionForm.tireCheck.tpmsFunction.result" class="item-result" :disabled="!isEdit">
                <el-radio value="good">{{ t('inspectionContent.checkResult.good') }}</el-radio>
                <el-radio value="attention">{{ t('inspectionContent.checkResult.attention') }}</el-radio>
                <el-radio value="poor">{{ t('inspectionContent.checkResult.poor') }}</el-radio>
                <el-radio value="not_applicable">{{ t('inspectionContent.checkResult.notApplicable') }}</el-radio>
              </el-radio-group>
              <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
                <el-button size="small">{{ t('inspectionContent.upload') }}</el-button>
              </el-upload>
              <span v-else class="item-upload">{{ inspectionForm.tireCheck.tpmsFunction.photos?.length || 0 }}{{ t('inspectionContent.attachments') }}</span>
            </div>
          </div>

          <!-- 客户自述问题 -->
          <div class="section-card">
            <h4 class="section-title">{{ t('inspectionContent.customerIssues.title') }}</h4>
            <el-form-item>
              <el-input
                v-model="inspectionForm.customerIssues"
                type="textarea"
                :rows="4"
                :placeholder="t('inspectionContent.customerIssues.placeholder')"
                :disabled="!isEdit"
              />
            </el-form-item>
          </div>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button v-if="isEdit" type="primary" @click="handleSave">{{ tc('save') }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
:deep(.inspection-detail-dialog) {
  .el-dialog__body {
    max-height: 80vh;
    overflow-y: auto;
    padding: 20px;
  }
}

.inspection-detail {
  .info-card {
    margin-bottom: 16px;

    .card-title {
      font-weight: 600;
      color: #303133;
    }
  }

  .inspection-content {
    .section-card {
      margin-bottom: 20px;
      padding: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      background-color: #fafafa;

      .section-title {
        font-weight: 600;
        color: #303133;
        margin-bottom: 16px;
        font-size: 16px;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 8px;
      }

      .check-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px;
        background-color: white;
        border-radius: 4px;

        .item-label {
          min-width: 140px;
          margin-right: 16px;
          font-weight: 500;
        }

        .item-result {
          margin-right: 16px;
          flex: 1;
        }

        .item-upload {
          margin-left: auto;
        }
      }

      .tire-inputs {
        margin-bottom: 16px;

        .el-col {
          margin-bottom: 12px;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;

  .el-button {
    margin-left: 8px;
  }
}
</style>
