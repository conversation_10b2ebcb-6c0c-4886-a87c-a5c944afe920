<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElInput,
  ElButton,
  ElDescriptions,
  ElDescriptionsItem,
  ElUpload,
  ElMessage
} from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InspectionListItem } from '@/types/afterSales/inspection.d.ts';
import type { UploadProps } from 'element-plus';

interface Props {
  visible: boolean;
  recordData: InspectionListItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', data: { confirmTime: string; signaturePhoto: string; confirmChannel: string }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const confirmTime = ref('');
const signaturePhoto = ref('');
const confirmChannel = ref('线下确认');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const currentRecord = computed(() => props.recordData);

// 重置表单
const resetForm = () => {
  confirmTime.value = new Date().toISOString().slice(0, 16).replace('T', ' ');
  signaturePhoto.value = '';
  confirmChannel.value = t('dialog.offlineConfirm');
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// 文件上传前的校验
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isJPGOrPNG) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
};

// 文件上传成功
const handleUploadSuccess = (response: any, file: any) => {
  signaturePhoto.value = URL.createObjectURL(file.raw);
  ElMessage.success('签字照片上传成功');
};

// 确认
const handleConfirm = () => {
  if (!confirmTime.value) {
    ElMessage.error(t('messages.selectConfirmTime'));
    return;
  }
  if (!signaturePhoto.value) {
    ElMessage.error(t('messages.uploadSignaturePhoto'));
    return;
  }

  emit('confirm', {
    confirmTime: confirmTime.value,
    signaturePhoto: signaturePhoto.value,
    confirmChannel: confirmChannel.value
  });
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};

const getRegisterTypeText = (type: string) => {
  return type === 'appointment' ? t('registerType.appointment') : t('registerType.walk_in');
};

const getServiceTypeText = (type: string) => {
  return type === 'maintenance' ? t('serviceType.maintenance') : t('serviceType.repair');
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('dialog.confirmTitle')"
    width="600px"
    @close="resetForm"
  >
    <div v-if="currentRecord">
      <!-- 检查单信息 -->
      <el-descriptions :column="2" border class="mb-20">
        <el-descriptions-item :label="t('table.inspectionNo')">
          {{ currentRecord.inspectionNo }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.licensePlateNo')">
          {{ currentRecord.licensePlateNo }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.repairmanName')">
          {{ currentRecord.repairmanName }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.repairmanPhone')">
          {{ currentRecord.repairmanPhone }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.vehicleModel')">
          {{ currentRecord.vehicleModel }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.serviceAdvisor')">
          {{ currentRecord.serviceAdvisor }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.registerType')">
          {{ getRegisterTypeText(currentRecord.registerType) }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('table.serviceType')">
          {{ getServiceTypeText(currentRecord.serviceType) }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 线下确认信息 -->
      <el-form label-position="top">
        <el-form-item :label="`*${t('dialog.confirmTimeLabel')}`" required>
          <el-date-picker
            v-model="confirmTime"
            type="datetime"
            :placeholder="t('dialog.confirmTimePlaceholder')"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item :label="`*${t('dialog.signaturePhotoLabel')}`" required>
          <el-upload
            class="signature-upload"
            drag
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :show-file-list="false"
            action="#"
            accept=".jpg,.jpeg,.png"
          >
            <div v-if="!signaturePhoto" class="upload-area">
              <div class="upload-text">
                <p>{{ t('dialog.uploadSignatureText') }}</p>
                <p class="upload-hint">{{ t('dialog.uploadHint') }}</p>
              </div>
            </div>
            <div v-else class="uploaded-image">
              <img :src="signaturePhoto" :alt="t('dialog.signaturePhotoAlt')" />
              <div class="upload-overlay">
                <span>{{ t('dialog.reuploadText') }}</span>
              </div>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!confirmTime || !signaturePhoto"
        >
          {{ tc('confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.mb-20 {
  margin-bottom: 20px;
}

.signature-upload {
  .upload-area {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
      border-color: #409eff;
    }

    .upload-text {
      text-align: center;
      color: #606266;

      .upload-hint {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }
  }

  .uploaded-image {
    position: relative;
    width: 200px;
    height: 120px;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .upload-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
    }

    &:hover .upload-overlay {
      opacity: 1;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  .el-button {
    margin-left: 10px;
  }
}
</style>
