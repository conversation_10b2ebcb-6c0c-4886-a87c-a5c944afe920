<template>
  <div class="check-item">
    <span class="item-label">{{ label }}:</span>
    <el-radio-group v-model="modelValue.result" class="item-result" :disabled="!isEdit">
      <el-radio value="good">良好</el-radio>
      <el-radio value="attention">需要注意</el-radio>
      <el-radio value="poor">不良</el-radio>
      <el-radio value="not_applicable">不适用</el-radio>
    </el-radio-group>
    <el-upload v-if="isEdit" class="item-upload" action="#" :show-file-list="false">
      <el-button size="small">上传</el-button>
    </el-upload>
    <span v-else class="item-upload">{{ modelValue.photos?.length || 0 }}个附件</span>
  </div>
</template>

<script setup lang="ts">
import { ElRadioGroup, ElRadio, ElUpload, ElButton } from 'element-plus';
import type { CheckResult } from '@/types/afterSales/inspection.d.ts';

interface Props {
  label: string;
  modelValue: {
    result: CheckResult;
    photos: string[];
    notes: string;
  };
  isEdit: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void;
}

defineProps<Props>();
defineEmits<Emits>();
</script>

<style scoped lang="scss">
.check-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  
  .item-label {
    min-width: 140px;
    margin-right: 16px;
    font-weight: 500;
  }
  
  .item-result {
    margin-right: 16px;
    flex: 1;
  }
  
  .item-upload {
    margin-left: auto;
  }
}
</style>
