<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('createWorkOrder.selectDialog.partTitle')"
    width="800px"
    :close-on-click-modal="false"
  >
    <!-- 搜索条件 -->
    <div class="search-form">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-input
            v-model="searchForm.code"
            :placeholder="t('createWorkOrder.selectDialog.searchPartCode')"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-input
            v-model="searchForm.name"
            :placeholder="t('createWorkOrder.selectDialog.searchPartName')"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch">{{ t('createWorkOrder.selectDialog.search') }}</el-button>
          <el-button @click="handleReset">{{ t('createWorkOrder.selectDialog.reset') }}</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 零件项目列表 -->
    <el-table :data="filteredItems" border style="margin-top: 16px;">
      <el-table-column prop="code" :label="t('createWorkOrder.table.partCode')" width="120" />
      <el-table-column prop="name" :label="t('createWorkOrder.table.partName')" />
      <el-table-column prop="type" :label="t('createWorkOrder.table.type')" width="100" />
      <el-table-column prop="stock" :label="t('createWorkOrder.selectDialog.stockQuantity')" width="100" />
      <el-table-column prop="unitPrice" :label="t('createWorkOrder.table.unitPrice')" width="100" />
      <el-table-column :label="t('createWorkOrder.table.operations')" width="80">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="handleSelect(scope.row)"
            :disabled="scope.row.stock <= 0"
          >
            {{ t('createWorkOrder.selectDialog.select') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ t('createWorkOrder.selectDialog.cancel') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  ElDialog,
  ElRow,
  ElCol,
  ElInput,
  ElButton,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';

interface PartItem {
  id: number;
  code: string;
  name: string;
  type: string;
  stock: number;
  unitPrice: number;
}

interface Props {
  visible: boolean;
  items: PartItem[];
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'select', item: PartItem): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const searchForm = ref({
  code: '',
  name: ''
});

const filteredItems = computed(() => {
  return props.items.filter(item => {
    const codeMatch = !searchForm.value.code || item.code.toLowerCase().includes(searchForm.value.code.toLowerCase());
    const nameMatch = !searchForm.value.name || item.name.toLowerCase().includes(searchForm.value.name.toLowerCase());
    return codeMatch && nameMatch;
  });
});

const handleSearch = () => {
  // 搜索逻辑已在 computed 中实现
};

const handleReset = () => {
  searchForm.value.code = '';
  searchForm.value.name = '';
};

const handleSelect = (item: PartItem) => {
  emit('select', item);
};

const handleCancel = () => {
  dialogVisible.value = false;
};

// 重置搜索表单当弹窗关闭时
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    handleReset();
  }
});
</script>

<style scoped lang="scss">
.search-form {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.dialog-footer {
  text-align: right;
}
</style>
