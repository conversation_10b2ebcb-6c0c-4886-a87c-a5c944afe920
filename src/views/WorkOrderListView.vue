<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <!-- 第一行筛选条件 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('number')">
              <el-input
                v-model="searchParams.work_order_number"
                :placeholder="t('placeholders.workOrderNumber')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('priority')">
              <el-select
                v-model="searchParams.work_order_priority"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option value="" :label="tc('pleaseSelect')" />
                <el-option
                  v-for="option in workOrderPriorityOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('type')">
              <el-select
                v-model="searchParams.work_order_type"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option value="" :label="tc('pleaseSelect')" />
                <el-option
                  v-for="option in workOrderTypeOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('isClaim')">
              <el-select
                v-model="searchParams.is_claim"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option :value="null" :label="tc('all')" />
                <el-option
                  v-for="option in booleanOptions"
                  :key="option.code"
                  :value="option.code === 'true'"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行筛选条件 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('isOutsourced')">
              <el-select
                v-model="searchParams.is_outsourced"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option :value="null" :label="tc('all')" />
                <el-option
                  v-for="option in booleanOptions"
                  :key="option.code"
                  :value="option.code === 'true'"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('status')">
              <el-select
                v-model="searchParams.work_order_status"
                :placeholder="tc('pleaseSelect')"
                multiple
                clearable
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="option in workOrderStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('paymentStatus')">
              <el-select
                v-model="searchParams.payment_status"
                :placeholder="tc('pleaseSelect')"
                clearable
                :loading="dictionaryLoading"
              >
                <el-option value="" :label="tc('pleaseSelect')" />
                <el-option
                  v-for="option in paymentStatusOptions"
                  :key="option.code"
                  :value="option.code"
                  :label="option.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('senderName')">
              <el-input
                v-model="searchParams.sender_name"
                :placeholder="t('placeholders.customerName')"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行筛选条件 -->
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('senderPhone')">
              <el-input
                v-model="searchParams.sender_phone"
                :placeholder="t('placeholders.customerPhone')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('licensePlate')">
              <el-input
                v-model="searchParams.license_plate"
                :placeholder="t('placeholders.licensePlate')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('serviceAdvisor')">
              <el-select
                v-model="searchParams.service_advisor_id"
                :placeholder="t('placeholders.selectServiceAdvisor')"
                clearable
                :loading="masterDataLoading"
              >
                <el-option
                  v-for="advisor in serviceAdvisorOptions"
                  :key="advisor.id"
                  :value="advisor.id"
                  :label="advisor.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('createdTime')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                :placeholder="t('placeholders.selectDateRange')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                clearable
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 搜索按钮 -->
        <el-row :gutter="20">
          <el-col :span="24" class="buttons-col">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ tc('search') }}
            </el-button>
            <el-button @click="resetSearch">
              {{ tc('reset') }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区 -->
    <el-card class="mb-20 operation-card">
      <el-button type="primary" :icon="Plus" @click="handleCreateWorkOrder">
        {{ t('create') }}
      </el-button>
      <el-button :icon="Download" @click="handleExport">
        {{ t('exportWorkOrder') }}
      </el-button>
    </el-card>

    <!-- 数据列表区 -->
    <el-card class="table-card">
      <el-table
        :data="workOrderList"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column type="index" :label="tc('index')" width="60" align="center" />

        <el-table-column
          prop="work_order_number"
          :label="t('number')"
          width="140"
        >
          <template #default="{ row }">
            <el-link type="primary" @click="handleViewDetail(row)">
              {{ row.work_order_number }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          prop="work_order_priority"
          :label="t('priority')"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="row.work_order_priority === 'urgent' ? 'danger' : 'primary'">
              {{ formatWorkOrderPriority(row.work_order_priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="work_order_type"
          :label="t('type')"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="getWorkOrderTypeColor(row.work_order_type)"
            >
              {{ formatWorkOrderType(row.work_order_type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="is_claim"
          :label="t('isClaim')"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="row.is_claim ? 'success' : ''">
              {{ row.is_claim ? tc('yes') : tc('no') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="is_outsourced"
          :label="t('isOutsourced')"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="row.is_outsourced ? 'success' : ''">
              {{ row.is_outsourced ? tc('yes') : tc('no') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="work_order_status"
          :label="t('status')"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="getWorkOrderStatusColor(row.work_order_status)">
              {{ t(`statuses.${row.work_order_status}`) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="payment_status"
          :label="t('paymentStatus')"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="getPaymentStatusColor(row.payment_status)">
              {{ t(`paymentStatuses.${row.payment_status}`) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="sender_name" :label="t('senderName')" width="100" />
        <el-table-column prop="sender_phone" :label="t('senderPhone')" width="120" />
        <el-table-column prop="license_plate" :label="t('licensePlate')" width="100" align="center" />
        <el-table-column prop="model_config_color" :label="t('modelConfigColor')" width="180" show-overflow-tooltip />

        <el-table-column
          prop="total_amount"
          :label="t('totalAmount')"
          width="120"
          align="right"
        >
          <template #default="{ row }">
            ¥{{ row.total_amount.toFixed(2) }}
          </template>
        </el-table-column>

        <el-table-column prop="service_advisor_name" :label="t('serviceAdvisor')" width="100" />
        <el-table-column prop="technician_name" :label="t('technician')" width="100" />

        <el-table-column
          prop="confirmation_time"
          :label="t('confirmationTime')"
          width="140"
          align="center"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.confirmation_time) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="has_additional_items"
          :label="t('hasAdditionalItems')"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="row.has_additional_items ? 'warning' : ''">
              {{ row.has_additional_items ? tc('yes') : tc('no') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="start_time"
          :label="t('startTime')"
          width="140"
          align="center"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.start_time) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="end_time"
          :label="t('endTime')"
          width="140"
          align="center"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.end_time) }}
          </template>
        </el-table-column>

        <el-table-column
          prop="created_time"
          :label="t('createdTime')"
          width="140"
          align="center"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.created_time) }}
          </template>
        </el-table-column>

        <el-table-column
          :label="tc('operations')"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button type="primary" :icon="View" link @click="handleViewDetail(row)">
              {{ tc('detail') }}
            </el-button>
            <el-button
              v-if="row.work_order_status === 'draft'"
              type="primary"
              :icon="Edit"
              link
              @click="handleEdit(row)"
            >
              {{ tc('edit') }}
            </el-button>
            <el-button
              v-if="canAddItems(row)"
              type="warning"
              :icon="Plus"
              link
              @click="handleAddItems(row)"
            >
              {{ t('addItem') }}
            </el-button>
            <el-button
              v-if="canCancel(row)"
              type="danger"
              :icon="Delete"
              link
              @click="handleCancel(row)"
            >
              {{ tc('cancel') }}
            </el-button>
            <el-button
              v-if="canSubmitApproval(row)"
              type="success"
              :icon="Check"
              link
              @click="handleSubmitApproval(row)"
            >
              {{ t('submitApproval') }}
            </el-button>
            <el-button
              type="info"
              :icon="Printer"
              link
              @click="handlePrint(row)"
            >
              {{ tc('print') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="searchParams.page"
          v-model:page-size="searchParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSearch"
          @current-change="handleSearch"
        />
      </div>
    </el-card>

    <!-- 工单创建/编辑弹窗 -->
    <WorkOrderCreateEditModal
      v-model:visible="createEditVisible"
      :work-order-id="currentWorkOrderId"
      :is-edit="isEdit"
      :is-additional="isAdditional"
      @success="handleCreateEditSuccess"
    />

    <!-- 工单详情弹窗 -->
    <WorkOrderDetailModal
      v-model:visible="detailVisible"
      :work-order-id="currentWorkOrderId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Search,
  Plus,
  Download,
  View,
  Edit,
  Delete,
  Check,
  Printer
} from '@element-plus/icons-vue';
import type { WorkOrderListItem, WorkOrderListParams, User } from '@/types/workOrder';
import { getWorkOrderList, getUsers } from '@/api/modules/workOrder';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import { getServiceAdvisorList } from '@/api/modules/masterData';
import WorkOrderCreateEditModal from '@/components/WorkOrderCreateEditModal.vue';
import WorkOrderDetailModal from '@/components/WorkOrderDetailModal.vue';

const { t,tc } = useModuleI18n('workOrder');

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.WORK_ORDER_PRIORITY,
  DICTIONARY_TYPES.WORK_ORDER_STATUS,
  DICTIONARY_TYPES.WORK_ORDER_TYPE,
  DICTIONARY_TYPES.PAYMENT_STATUS,
  DICTIONARY_TYPES.BOOLEAN_TYPE
]);

// 服务顾问数据 (主数据)
const serviceAdvisorOptions = ref([]);
const masterDataLoading = ref(false);

// 搜索参数
const searchParams = ref<WorkOrderListParams>({
  page: 1,
  pageSize: 20,
  work_order_number: '',
  work_order_priority: undefined,
  work_order_type: undefined,
  is_claim: null,
  is_outsourced: null,
  work_order_status: [],
  payment_status: undefined,
  sender_name: '',
  sender_phone: '',
  license_plate: '',
  service_advisor_id: '',
  confirmation_time_start: '',
  confirmation_time_end: ''
});

// 日期范围
const dateRange = ref<string[]>([]);

// 监听日期范围变化
watch(dateRange, (newRange) => {
  if (newRange && newRange.length === 2) {
    searchParams.value.confirmation_time_start = newRange[0];
    searchParams.value.confirmation_time_end = newRange[1];
  } else {
    searchParams.value.confirmation_time_start = '';
    searchParams.value.confirmation_time_end = '';
  }
});

// 数据相关
const workOrderList = ref<WorkOrderListItem[]>([]);
const total = ref(0);
const loading = ref(false);

// 获取字典选项的计算属性
const workOrderPriorityOptions = computed(() => getOptions(DICTIONARY_TYPES.WORK_ORDER_PRIORITY));
const workOrderStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.WORK_ORDER_STATUS));
const workOrderTypeOptions = computed(() => getOptions(DICTIONARY_TYPES.WORK_ORDER_TYPE));
const paymentStatusOptions = computed(() => getOptions(DICTIONARY_TYPES.PAYMENT_STATUS));
const booleanOptions = computed(() => getOptions(DICTIONARY_TYPES.BOOLEAN_TYPE));

// 加载服务顾问数据
const loadServiceAdvisors = async () => {
  masterDataLoading.value = true;
  try {
    serviceAdvisorOptions.value = await getServiceAdvisorList();
  } catch (error) {
    console.error('获取服务顾问数据失败:', error);
    ElMessage.error('获取服务顾问数据失败');
  } finally {
    masterDataLoading.value = false;
  }
};

// 服务顾问列表 (保留原有的，用于兼容)
const serviceAdvisors = ref<User[]>([]);

// 弹窗相关
const createEditVisible = ref(false);
const detailVisible = ref(false);
const currentWorkOrderId = ref('');
const isEdit = ref(false);
const isAdditional = ref(false);

// 获取工单列表
const fetchWorkOrderList = async () => {
  loading.value = true;
  try {
    const response = await getWorkOrderList(searchParams.value);
    workOrderList.value = response.data;
    total.value = response.total;
  } catch (error) {
    console.error('Failed to fetch work order list:', error);
    ElMessage.error(tc('networkError'));
  } finally {
    loading.value = false;
  }
};

// 获取服务顾问列表
const fetchServiceAdvisors = async () => {
  try {
    const users = await getUsers('service_advisor');
    serviceAdvisors.value = users;
  } catch (error) {
    console.error('Failed to fetch service advisors:', error);
  }
};

// 搜索
const handleSearch = () => {
  searchParams.value.page = 1;
  fetchWorkOrderList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams.value, {
    page: 1,
    pageSize: 20,
    work_order_number: '',
    work_order_priority: undefined,
    work_order_type: undefined,
    is_claim: null,
    is_outsourced: null,
    work_order_status: [],
    payment_status: undefined,
    sender_name: '',
    sender_phone: '',
    license_plate: '',
    service_advisor_id: '',
    confirmation_time_start: '',
    confirmation_time_end: ''
  });
  dateRange.value = [];
  fetchWorkOrderList();
};

// 创建工单
const handleCreateWorkOrder = () => {
  currentWorkOrderId.value = '';
  isEdit.value = false;
  isAdditional.value = false;
  createEditVisible.value = true;
};

// 编辑工单
const handleEdit = (row: WorkOrderListItem) => {
  currentWorkOrderId.value = row.work_order_id!;
  isEdit.value = true;
  isAdditional.value = false;
  createEditVisible.value = true;
};

// 查看详情
const handleViewDetail = (row: WorkOrderListItem) => {
  currentWorkOrderId.value = row.work_order_id!;
  detailVisible.value = true;
};

// 增项
const handleAddItems = (row: WorkOrderListItem) => {
  currentWorkOrderId.value = row.work_order_id!;
  isEdit.value = true;
  isAdditional.value = true;
  createEditVisible.value = true;
};

// 取消工单
const handleCancel = async (row: WorkOrderListItem) => {
  try {
    await ElMessageBox.confirm(
      t('messages.confirmCancel', { workOrderNumber: row.work_order_number }),
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );

    // 这里调用取消接口
    ElMessage.success(t('workOrder.messages.cancelSuccess'));
    fetchWorkOrderList();
  } catch {
    // 用户取消操作
    console.log('User cancelled operation');
  }
};

// 提交审核
const handleSubmitApproval = async (row: WorkOrderListItem) => {
  try {
    await ElMessageBox.confirm(
      `确定提交工单 ${row.work_order_number} 进行审核吗？`,
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );

    // 这里调用提交审核接口
    ElMessage.success(t('workOrder.messages.submitApprovalSuccess'));
    fetchWorkOrderList();
  } catch {
    // 用户取消操作
    console.log('User cancelled operation');
  }
};

// 打印工单
const handlePrint = (row: WorkOrderListItem) => {
  console.log('Print work order:', row.work_order_number);
  ElMessage.info(t('workOrder.messages.printNotReady'));
};

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...');
};

// 创建/编辑成功回调
const handleCreateEditSuccess = () => {
  fetchWorkOrderList();
};

// 判断是否可以增项
const canAddItems = (row: WorkOrderListItem) => {
  const allowedStatuses = ['confirmed', 'pending_assignment', 'pending_start', 'in_progress', 'pending_qc'];
  return allowedStatuses.includes(row.work_order_status!);
};

// 判断是否可以取消
const canCancel = (row: WorkOrderListItem) => {
  const allowedStatuses = ['draft', 'pending_confirmation', 'pending_assignment', 'pending_start'];
  return allowedStatuses.includes(row.work_order_status!);
};

// 判断是否可以提交审核
const canSubmitApproval = (row: WorkOrderListItem) => {
  return row.is_claim && row.work_order_status === 'confirmed';
};

// 格式化显示方法 - 使用字典接口
const formatWorkOrderPriority = (priority: string) => getNameByCode(DICTIONARY_TYPES.WORK_ORDER_PRIORITY, priority) || priority;
const formatWorkOrderType = (type: string) => getNameByCode(DICTIONARY_TYPES.WORK_ORDER_TYPE, type) || type;
const formatWorkOrderStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.WORK_ORDER_STATUS, status) || status;
const formatPaymentStatus = (status: string) => getNameByCode(DICTIONARY_TYPES.PAYMENT_STATUS, status) || status;

// 获取工单类型颜色
const getWorkOrderTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    repair: 'warning',
    maintenance: 'success',
    insurance: ''
  };
  return colorMap[type] || '';
};

// 获取工单状态颜色
const getWorkOrderStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    draft: 'info',
    pending_confirmation: 'warning',
    confirmed: 'primary',
    pending_assignment: '',
    pending_start: '',
    in_progress: 'warning',
    pending_qc: '',
    pending_settlement: '',
    rework_required: 'danger',
    completed: 'success',
    cancelled: 'info',
    additional_pending: 'warning',
    waiting_approval: 'primary',
    waiting_parts: 'warning'
  };
  return colorMap[status] || '';
};

// 获取支付状态颜色
const getPaymentStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'danger',
    deposit_paid: 'warning',
    paid: 'success',
    refunding: 'warning',
    refunded: 'info'
  };
  return colorMap[status] || '';
};

// 格式化日期时间
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  if (targetDate.getTime() === today.getTime()) {
    // 今天只显示时间
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  } else {
    // 其他日期显示完整时间
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

// 页面挂载时获取数据
onMounted(() => {
  fetchWorkOrderList();
  fetchServiceAdvisors(); // 保留原有的获取方法
  loadServiceAdvisors(); // 新增主数据API获取方法
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card, .operation-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
