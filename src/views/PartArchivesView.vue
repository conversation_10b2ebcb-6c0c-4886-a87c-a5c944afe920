<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('partName')">
              <el-input
                v-model="searchParams.partName"
                :placeholder="t('partNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partNumber')">
              <el-input
                v-model="searchParams.partNumber"
                :placeholder="t('partNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('supplierName')">
              <el-input
                v-model="searchParams.supplierName"
                :placeholder="t('supplierNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="buttons-col">
              <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据展示区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column type="index" :label="tc('sequence')" width="80" />
        <el-table-column :label="t('partName')" prop="partName" min-width="150" />
        <el-table-column :label="t('partNumber')" prop="partNumber" min-width="150" />
        <el-table-column :label="t('unit')" prop="unit" min-width="100" />
        <el-table-column :label="t('supplierName')" prop="supplierName" min-width="150" />
        <el-table-column :label="t('purchasePrice')" prop="purchasePrice" min-width="120" />
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const { t, tc } = useModuleI18n('partsArchives');

interface PartArchiveItem {
  partName: string;
  partNumber: string;
  unit: string;
  supplierName: string;
  purchasePrice: number;
}

// 搜索参数
const searchParams = reactive({
  partName: '',
  partNumber: '',
  supplierName: ''
});

// 加载状态
const loading = ref(false);

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 模拟数据
const mockTableData: PartArchiveItem[] = [
  {
    partName: '刹车片',
    partNumber: 'BP001',
    unit: '套',
    supplierName: '供应商A',
    purchasePrice: 150.00
  },
  {
    partName: '机油滤清器',
    partNumber: 'OF002',
    unit: '个',
    supplierName: '供应商B',
    purchasePrice: 50.00
  },
  {
    partName: '火花塞',
    partNumber: 'SP003',
    unit: '支',
    supplierName: '供应商C',
    purchasePrice: 30.00
  },
  {
    partName: '雨刮器',
    partNumber: 'WW004',
    unit: '对',
    supplierName: '供应商D',
    purchasePrice: 80.00
  },
  {
    partName: '空气滤芯',
    partNumber: 'AF005',
    unit: '个',
    supplierName: '供应商E',
    purchasePrice: 65.00
  }
];

const tableData = ref<PartArchiveItem[]>([]);

// 搜索方法
const handleSearch = async () => {
  loading.value = true;
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    const filteredData = mockTableData.filter(item => {
      const partNameMatch = searchParams.partName ? item.partName.includes(searchParams.partName) : true;
      const partNumberMatch = searchParams.partNumber ? item.partNumber.includes(searchParams.partNumber) : true;
      const supplierNameMatch = searchParams.supplierName ? item.supplierName.includes(searchParams.supplierName) : true;
      return partNameMatch && partNumberMatch && supplierNameMatch;
    });

    // 模拟分页
    const start = (pagination.currentPage - 1) * pagination.pageSize;
    const end = start + pagination.pageSize;
    tableData.value = filteredData.slice(start, end);
    pagination.total = filteredData.length;

    ElMessage.success(tc('operationSuccessful'));
  } catch (error) {
    console.error('搜索失败:', error);
    ElMessage.error(tc('searchFailed'));
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const resetSearch = () => {
  searchParams.partName = '';
  searchParams.partNumber = '';
  searchParams.supplierName = '';
  pagination.currentPage = 1; // 重置页码
  handleSearch(); // 重置后重新搜索
};

// 分页方法
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  handleSearch();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  handleSearch();
};

// 页面加载时执行一次搜索
onMounted(() => {
  handleSearch();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

.buttons-col {
  text-align: right;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>
