<template>
  <el-dialog
    v-model="visible"
    :title="`${t('paymentOperation')} [${t('orderNumber')}: ${orderDetail?.orderNumber || '-'}]`"
    width="80vw"
    min-width="800px"
    max-width="1200px"
    :close-on-click-modal="false"
    destroy-on-close
    :loading="loading"
  >
    <div v-loading="loading" class="dialog-content">
      <!-- 订单详细信息 -->
      <div v-if="orderDetail">
        <!-- 订单基本信息 -->
        <el-card class="mb-20 basic-info-card">
          <template #header>
            <span class="card-title">{{ t('orderBasicInfo') }}</span>
          </template>
          <div class="order-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="copyToClipboard(orderDetail.orderNumber)">
                  <label>{{ t('orderNumber') }}:</label>
                  <span class="bold">{{ orderDetail.orderNumber }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderCreateTime') }}:</label>
                  <span>{{ orderDetail.orderCreateTime }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('orderStatus') }}:</label>
                  <el-tag :type="getOrderStatusType(orderDetail.orderStatus)">
                    {{ getOrderStatusText(orderDetail.orderStatus) }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('paymentStatus') }}:</label>
                  <el-tag :type="getPaymentStatusType(orderDetail.paymentStatus)">
                    {{ getPaymentStatusText(orderDetail.paymentStatus) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('paymentMethod') }}:</label>
                  <span>{{ orderDetail.paymentMethod }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('loanAmount') }}:</label>
                  <span :class="{'red-text': orderDetail.loanAmount}">{{ orderDetail.loanAmount ? formatAmount(orderDetail.loanAmount) : '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 客户信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('customerDetailInfo') }} - Personal Details</span>
          </template>
          <div class="customer-info">
            <h3 class="sub-title">{{ t('ordererInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('ordererName') }}:</label>
                  <span>{{ orderDetail.ordererName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.ordererPhone && callPhone(orderDetail.ordererPhone)">
                  <label>{{ t('ordererPhone') }}:</label>
                  <span>{{ orderDetail.ordererPhone ? orderDetail.ordererPhone : '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <h3 class="sub-title">{{ t('buyerInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerName') }}:</label>
                  <span>{{ orderDetail.buyerName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.buyerPhone && callPhone(orderDetail.buyerPhone)">
                  <label>{{ t('buyerPhone') }}:</label>
                  <span>{{ orderDetail.buyerPhone ? orderDetail.buyerPhone : '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerIdType') }}:</label>
                  <span>{{ orderDetail.buyerIdType || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="toggleMaskIdNumber(orderDetail.buyerIdNumber)">
                  <label>{{ t('buyerIdNumber') }}:</label>
                  <span>{{ maskedIdNumber(orderDetail.buyerIdNumber) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item clickable-info" @click="orderDetail.buyerEmail && sendEmail(orderDetail.buyerEmail)">
                  <label>{{ t('buyerEmail') }}:</label>
                  <span>{{ orderDetail.buyerEmail || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerState') }}:</label>
                  <span>{{ orderDetail.buyerState || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerCity') }}:</label>
                  <span>{{ orderDetail.buyerCity || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerPostcode') }}:</label>
                  <span>{{ orderDetail.buyerPostcode || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('buyerAddress') }}:</label>
                  <span>{{ orderDetail.buyerAddress || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 门店与销售信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('dealerAndSalesInfo') }} - Preferred Outlet & Sales Advisor</span>
          </template>
          <div class="dealer-sales-info">
            <h3 class="sub-title">{{ t('dealerInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ t('dealerRegion') }}:</label>
                  <span>{{ orderDetail.dealerRegion || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>{{ t('dealerCity') }}:</label>
                  <span>{{ orderDetail.dealerCity || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('dealerStore') }}:</label>
                  <span>{{ orderDetail.dealerStoreName || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <h3 class="sub-title">{{ t('salesConsultantInfo') }}</h3>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('salesConsultant') }}:</label>
                  <span>{{ orderDetail.salesConsultantName || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 车辆信息 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('vehicleDetailInfo') }}</span>
          </template>
          <div class="vehicle-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('model') }}:</label>
                  <span>{{ orderDetail.model || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('variant') }}:</label>
                  <span>{{ orderDetail.variant || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('color') }}:</label>
                  <span>
                    <span class="color-dot" :style="{ backgroundColor: orderDetail.color }"></span>
                    {{ orderDetail.color || '-' }}
                  </span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item clickable-info" @click="copyToClipboard(orderDetail.vin)">
                  <label>{{ t('vin') }}:</label>
                  <span class="vin-text">{{ orderDetail.vin || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('warehouse') }}:</label>
                  <span>{{ orderDetail.warehouseName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>{{ t('productionDate') }}:</label>
                  <span>{{ orderDetail.productionDate || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20" v-if="orderDetail.options && orderDetail.options.length > 0">
              <el-col :span="24">
                <div class="info-item">
                  <label>{{ t('options') }}:</label>
                  <ul class="option-list">
                    <li v-for="(option, index) in orderDetail.options" :key="index">
                      {{ option.name }} (RM {{ formatAmount(option.price) }})
                    </li>
                    <li class="total-options-price">
                      <strong>{{ t('optionsTotalPrice') }}: RM {{ formatAmount(Array.isArray(orderDetail.options) ? orderDetail.options.reduce((sum, item) => sum + item.price, 0) : 0) }}</strong>
                    </li>
                  </ul>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 价格详细信息区 -->
        <el-card class="mb-20">
          <template #header>
            <span class="card-title">{{ t('priceDetailInfo') }}</span>
          </template>
          <div class="price-info">
            <el-table :data="priceTableData" :show-header="false" border style="width: 100%">
              <el-table-column prop="item" width="200" />
              <el-table-column prop="amount" align="right">
                <template #default="{ row }">
                  <span :class="row.class">{{ formatAmount(row.amount) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="description" />
            </el-table>
          </div>
        </el-card>
      </div>

      <!-- 添加收退款记录 -->
      <el-card class="mb-20">
        <template #header>
          <span class="card-title">{{ t('addPaymentRecord') }}</span>
        </template>
        <el-form :model="paymentForm" :rules="paymentRules" ref="paymentFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="t('businessType')" prop="businessType">
                <el-select v-model="paymentForm.businessType" style="width: 100%">
                  <el-option label="收款" value="收款" />
                  <el-option label="退款" value="退款" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('transactionNumber')" prop="transactionNumber">
                <el-input
                  v-model="paymentForm.transactionNumber"
                  :placeholder="t('enterTransactionNumber')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('channel')" prop="channel">
                <el-select v-model="paymentForm.channel" style="width: 100%">
                  <el-option label="APP" value="APP" />
                  <el-option label="银行卡" value="银行卡" />
                  <el-option label="转账" value="转账" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('amount')" prop="amount">
                <el-input-number
                  v-model="paymentForm.amount"
                  :min="0.01"
                  :precision="2"
                  style="width: 100%"
                  :placeholder="t('enterAmount')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="t('paymentType')" prop="paymentType">
                <el-select v-model="paymentForm.paymentType" style="width: 100%">
                  <el-option label="Book Fee" value="Book Fee" />
                  <el-option label="贷款" value="贷款" />
                  <el-option label="尾款" value="尾款" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('arrivalTime')" prop="arrivalTime">
                <el-date-picker
                  v-model="paymentForm.arrivalTime"
                  type="date"
                  :placeholder="t('selectPaymentDate')"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="tc('remark')" prop="remark">
                <el-input
                  v-model="paymentForm.remark"
                  type="textarea"
                  :rows="3"
                  :placeholder="t('enterRemark')"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" :icon="Plus" @click="handleAddPaymentRecord" :loading="submitLoading">
              {{ t('addPaymentInfo') }}
            </el-button>
            <el-button :icon="Refresh" @click="resetPaymentForm">{{ tc('reset') }}</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 收退款记录列表 -->
      <el-card>
        <template #header>
          <span class="card-title">{{ t('paymentRecordList') }}</span>
        </template>
        <el-table
          :data="paymentRecords"
          style="width: 100%"
          border
          :header-cell-style="{ backgroundColor: '#fafafa', fontWeight: 'bold' }"
        >
          <el-table-column type="index" :label="tc('index')" width="60" align="center" />
          <el-table-column prop="recordNumber" :label="t('paymentRecordNumber')" min-width="150" />
          <el-table-column prop="businessType" :label="t('businessType')" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.businessType === '收款' ? 'success' : 'danger'">
                {{ row.businessType === '收款' ? '+' : '-' }}{{ row.businessType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="transactionNumber" :label="t('transactionNumber')" min-width="150" />
          <el-table-column prop="channel" :label="t('channel')" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getChannelType(row.channel)">
                {{ row.channel }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" :label="t('amount')" width="120" align="right">
            <template #default="{ row }">
              <span :class="{ 'text-success': row.businessType === '收款', 'text-danger': row.businessType === '退款' }">
                {{ formatAmount(row.amount) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="paymentType" :label="t('paymentType')" width="100" align="center" />
          <el-table-column prop="arrivalTime" :label="t('arrivalTime')" width="150" align="center" />
          <el-table-column prop="remark" :label="tc('remark')" min-width="200" />
          <el-table-column :label="tc('operations')" width="80" fixed="right" align="center">
            <template #default="{ row }">
              <el-button
                type="danger"
                size="small"
                link
                :icon="Delete"
                @click="handleDeleteRecord(row)"
                :disabled="row.dataSource === 'APP推送'"
              >
                {{ tc('delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="visible = false">{{ tc('close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { Plus, Refresh, Delete } from '@element-plus/icons-vue' // 引入图标
import { getOrderDetail, addPaymentRecord, deletePaymentRecord, checkTransactionNumber } from '@/api/modules/payment'
import type { OrderDetailInfo, PaymentRecord, AddPaymentRecordForm } from '@/types/module'

const { t,tc } = useModuleI18n('sales.payment')

// Props
const props = defineProps<{
  modelValue: boolean
  orderId: string
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const orderDetail = ref<OrderDetailInfo | null>(null)
const paymentRecords = ref<PaymentRecord[]>([])
const paymentFormRef = ref()
const showFullIdNumber = ref(false) // 用于控制身份证件号显示状态

// 收退款表单
// **重要提示：** 请您手动更新 `src/types/module.d.ts` 文件中的 `AddPaymentRecordForm` 接口定义，使其与以下结构一致：
// interface AddPaymentRecordForm {
//   businessType: '收款' | '退款';
//   transactionNumber: string;
//   channel: 'APP' | '银行卡' | '转账';
//   amount: number;
//   paymentType: 'Book Fee' | '贷款' | '尾款';
//   arrivalTime: string; // YYYY-MM-DD
//   remark?: string;
// }
const paymentForm = reactive<AddPaymentRecordForm>({
  businessType: '收款',
  amount: 0,
  channel: '银行卡',
  transactionNumber: '',
  paymentType: 'Book Fee',
  arrivalTime: '',
  remark: ''
})

// 表单验证规则
const paymentRules = {
  businessType: [
    { required: true, message: t('recordTypeRequired'), trigger: 'change' }
  ],
  amount: [
    { required: true, message: t('amountRequired'), trigger: 'blur' },
    { type: 'number', min: 0.01, message: t('amountMustBePositive'), trigger: 'blur' }
  ],
  channel: [
    { required: true, message: t('paymentMethodRequired'), trigger: 'change' }
  ],
  transactionNumber: [
    { required: true, message: t('transactionNumberRequired'), trigger: 'blur' },
    // {
    //   validator: async (rule: any, value: string, callback: any) => {
    //     if (!value) return callback()
    //     try {
    //       const exists = await checkTransactionNumber(value) // 修复：仅传入流水号
    //       if (exists) {
    //         callback(new Error(t('transactionNumberExists')))
    //       } else {
    //         callback()
    //       }
    //     } catch (error) {
    //       console.error('流水号校验失败', error)
    //       callback() // 校验失败也允许通过，或者根据需求给出更具体的错误提示
    //     }
    //   },
    //   trigger: 'blur'
    // }
  ],
  paymentType: [
    { required: true, message: t('paymentTypeRequired'), trigger: 'change' }
  ],
  arrivalTime: [
    { required: true, message: t('paymentDateRequired'), trigger: 'change' }
    // 可以在这里添加日期不能超过当前日期，不能早于订单创建日期的校验，如果后端没有做强校验
  ],
  remark: [
    { max: 200, message: t('remarkTooLong'), trigger: 'blur' }
  ]
}

// 监听弹窗开启和关闭
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      loadOrderDetail()
    } else {
      resetState()
    }
  }
)

// 加载订单详情
const loadOrderDetail = async () => {
  if (!props.orderId) return

  loading.value = true
  try {
    // 假设 getOrderDetail 返回 OrderDetailInfo 结构，并且其中包含 paymentRecords
    const response = await getOrderDetail(props.orderId)
    console.log('response', response)
    orderDetail.value = response.result
    paymentRecords.value = response.result.paymentRecords || []

    // 初始化表单默认值
    initPaymentForm()
  } catch (error) {
    console.error('Failed to load order detail:', error)
    ElMessage.error(tc('loadFailed'))
  } finally {
    loading.value = false
  }
}

// 初始化表单
const initPaymentForm = () => {
  const now = new Date()
  paymentForm.arrivalTime = now.toISOString().slice(0, 10) // YYYY-MM-DD
}

// 重置表单
const resetPaymentForm = () => {
  paymentFormRef.value?.resetFields()
  paymentForm.businessType = '收款'
  paymentForm.channel = '银行卡'
  paymentForm.paymentType = 'Book Fee'
  initPaymentForm() // 重新设置默认日期
}

// 添加收退款记录
const handleAddPaymentRecord = async () => {
  if (!paymentFormRef.value) return

  try {
    await paymentFormRef.value.validate()
  } catch (error) {
    return
  }

  // 验证金额：退款金额不能超过已支付金额
  if (paymentForm.businessType === '退款' && paymentForm.amount > (orderDetail.value?.paidAmount || 0)) {
    ElMessage.error(t('refundAmountExceedsPaid'))
    return
  }

  submitLoading.value = true

  // 1. 构造一个临时的、用于乐观更新的记录对象
  const tempId = `temp_${Date.now()}` // 创建一个唯一的临时ID
  const optimisticRecord: PaymentRecord = {
    ...paymentForm,
    paymentRecordId: tempId, // 使用临时ID
    paymentRecordNumber: `TEMP-${tempId}`, // 临时的流水号
    orderId: props.orderId,
    dataSource: '手动录入',
    isDeletable: true, // 临时记录应该是可删除的，以便在失败时移除
    createTime: new Date().toISOString(),
    creator: '当前用户', // 这里可能需要从用户状态管理中获取真实用户名
  }

  // 2. 立即将临时记录添加到列表顶部并更新金额
  paymentRecords.value.unshift(optimisticRecord)
  if (orderDetail.value) {
    if (optimisticRecord.businessType === '收款') {
      orderDetail.value.paidAmount += optimisticRecord.amount
      orderDetail.value.unpaidAmount -= optimisticRecord.amount
    } else { // 退款
      orderDetail.value.paidAmount -= optimisticRecord.amount
      orderDetail.value.unpaidAmount += optimisticRecord.amount
    }
  }

  const originalFormState = { ...paymentForm } // 保存原始表单数据以便恢复
  resetPaymentForm() // 立即重置表单，以便用户继续操作

  try {
    // 3. 在后台发送保存请求
    const newRecord = await addPaymentRecord(
      props.orderId,
      originalFormState // 使用保存的原始数据提交
    );

    // 4. 请求成功后，用后端返回的真实数据更新临时记录
    const index = paymentRecords.value.findIndex(r => r.paymentRecordNumber === tempId)
    if (index !== -1) {
      paymentRecords.value[index] = newRecord
    }

    ElMessage.success(t('addRecordSuccess'))
    emit('refresh') // 通知父组件数据已更新
  } catch (error) {
    // 5. 请求失败，回滚UI更新
    ElMessage.error(t('addRecordFailed'))

    // 从列表中移除临时记录
    paymentRecords.value = paymentRecords.value.filter(r => r.paymentRecordNumber !== tempId)

    // 回滚金额
    if (orderDetail.value) {
      if (optimisticRecord.businessType === '收款') {
        orderDetail.value.paidAmount -= optimisticRecord.amount
        orderDetail.value.unpaidAmount += optimisticRecord.amount
      } else { // 退款
        orderDetail.value.paidAmount += optimisticRecord.amount
        orderDetail.value.unpaidAmount -= optimisticRecord.amount
      }
    }

    // （可选）将失败的数据恢复到表单中，以便用户修改后重试
    Object.assign(paymentForm, originalFormState)

    console.error('Failed to add payment record:', error)
  } finally {
    submitLoading.value = false
  }
}

// 删除记录
const handleDeleteRecord = async (record: PaymentRecord) => {
  if (record.dataSource === 'APP推送') {
    ElMessage.warning(t('cannotDeleteAppRecord'))
    return
  }

  try {
    await ElMessageBox.confirm(
      t('confirmDeleteRecord'),
      tc('confirm'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    )

    // 调用API
    await deletePaymentRecord(props.orderId, record.id);

    // 乐观删除：先从UI移除
    const index = paymentRecords.value.findIndex(r => r.paymentRecordId === record.paymentRecordId)
    if (index === -1) return

    const deletedRecord = paymentRecords.value.splice(index, 1)[0]

    // 更新金额
    if (orderDetail.value) {
      if (deletedRecord.businessType === '收款') {
        orderDetail.value.paidAmount -= deletedRecord.amount
        orderDetail.value.unpaidAmount += deletedRecord.amount
      } else { // 退款
        orderDetail.value.paidAmount += deletedRecord.amount
        orderDetail.value.unpaidAmount -= deletedRecord.amount
      }
    }

    ElMessage.success(t('deleteRecordSuccess'))
    emit('refresh') // 通知父组件刷新列表总览数据
  } catch (error) {
    if (error !== 'cancel') {
      // 失败时回滚UI
      const index = paymentRecords.value.findIndex(r => r.paymentRecordId === record.paymentRecordId)
      if (index === -1) { // 避免重复添加
         // 简单地将记录加回列表，也可以用 splice(originalIndex, 0, record) 加回原位
        paymentRecords.value.unshift(record)
      }

      if (orderDetail.value) {
        if (record.businessType === '收款') {
          orderDetail.value.paidAmount += record.amount
          orderDetail.value.unpaidAmount -= record.amount
        } else { // 退款
          orderDetail.value.paidAmount -= record.amount
          orderDetail.value.unpaidAmount += record.amount
        }
      }

      console.error('Failed to delete payment record:', error)
      ElMessage.error(t('deleteRecordFailed'))
    }
  }
}

// 格式化金额
const formatAmount = (amount: number) => {
  if (typeof amount !== 'number') return 'RM 0.00'
  return `RM ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 获取渠道文本和样式
const getChannelType = (channel: string) => {
  const channelMap: Record<string, string> = {
    'APP': 'success',
    '银行卡': 'primary',
    '转账': 'warning'
  }
  return channelMap[channel] || 'info'
}

// --- 从 OrderDetailDialog.vue 迁移的辅助函数 ---

// 脱敏身份证件号（前3后4），并提供切换显示完整号码功能
const maskedIdNumber = (idNumber: string | undefined) => {
  if (!idNumber || idNumber.length < 7) return idNumber || '-'
  if (showFullIdNumber.value) {
    return idNumber
  }
  const prefix = idNumber.substring(0, 3)
  const suffix = idNumber.substring(idNumber.length - 4)
  return `${prefix}****${suffix}`
}

const toggleMaskIdNumber = (idNumber: string | undefined) => {
  if (idNumber) {
    showFullIdNumber.value = !showFullIdNumber.value
    ElMessage.info(showFullIdNumber.value ? t('showFullIdNumber') : t('maskIdNumber'))
  }
}

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  if (text) {
    navigator.clipboard.writeText(text)
      .then(() => {
        ElMessage.success(tc('copied'))
      })
      .catch(err => {
        console.error('Failed to copy text:', err)
        ElMessage.error(tc('copyFailed'))
      })
  }
}

// 拨打电话
const callPhone = (phone: string) => {
  if (phone) {
    window.location.href = `tel:${phone}`
  }
}

// 发送邮件
const sendEmail = (email: string) => {
  if (email) {
    window.location.href = `mailto:${email}`
  }
}

// 获取订单状态样式
const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已确认': 'success',
    '待审核': 'warning',
    '已取消': 'danger',
    '已交车': 'success'
  }
  return statusMap[status] || 'info'
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': t('orderStatusSubmitted'),
    '取消审核中': t('orderStatusCancelPending'),
    '取消审核通过': t('orderStatusCancelApproved'),
    '已取消': t('orderStatusCancelled'),
    '已确认': t('orderStatusConfirmed'),
    '待审核': t('orderStatusPendingReview'),
    '已审核': t('orderStatusReviewed'),
    '待交车': t('orderStatusPendingDelivery'),
    '已交车': t('orderStatusDelivered')
  }
  return statusMap[status] || status
}

// 获取支付状态样式
const getPaymentStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已支付尾款': 'success',
    '已支付定金': 'primary',
    '待支付定金': 'warning',
    '退款中': 'danger',
    '退款完成': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取支付状态文本
const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '待支付定金': t('paymentStatusPendingDeposit'),
    '已支付定金': t('paymentStatusDepositPaid'),
    '退款中': t('paymentStatusRefunding'),
    '退款完成': t('paymentStatusRefunded'),
    '待支付尾款': t('paymentStatusPendingFinal'),
    '已支付尾款': t('paymentStatusFullyPaid')
  }
  return statusMap[status] || status
}

// 价格表格数据
const priceTableData = computed(() => {
  if (!orderDetail.value) return []
  const detail = orderDetail.value
  return [
    { item: t('salesSubtotalIncludeGASA'), amount: detail.vehicleSalesPrice || 0, description: t('salesSubtotalIncludeGASADesc'), class: '' },
    { item: t('consumptionTax'), amount: detail.consumptionTax || 0, description: t('consumptionTaxDesc'), class: '' },
    { item: t('salesTax'), amount: detail.salesTax || 0, description: t('salesTaxDesc'), class: '' },
    { item: t('numberPlatesFee'), amount: detail.numberPlatesFee || 0, description: t('numberPlatesFeeDesc'), class: '' },
    { item: t('optionsPrice'), amount: detail.accessoriesTotalAmount || 0, description: t('optionsPriceDesc'), class: '' },
    { item: t('vehicleSalesPriceSubtotal'), amount: detail.vehicleSalesPrice || 0, description: t('vehicleSalesPriceSubtotalDesc'), class: 'bold' },
    { item: t('insuranceAmount'), amount: detail.insuranceAmount || 0, description: t('insuranceAmountDesc'), class: '' },
    { item: t('otrAmount'), amount: detail.otrAmount || 0, description: t('otrAmountDesc'), class: '' },
    { item: t('orderDiscountAmount'), amount: detail.discountAmount || 0, description: t('orderDiscountAmountDesc'), class: 'red-text' },
    { item: t('orderTotalAmount'), amount: detail.totalAmount || 0, description: t('orderTotalAmountDesc'), class: 'large-font bold' },
    { item: t('orderPaidAmount'), amount: detail.paidAmount || 0, description: t('orderPaidAmountDesc'), class: 'green-text bold' },
    { item: t('orderUnpaidAmount'), amount: detail.unpaidAmount || 0, description: t('orderUnpaidAmountDesc'), class: 'red-text bold' }
  ]
})

// 重置状态
const resetState = () => {
  orderDetail.value = null
  paymentRecords.value = []
  paymentForm.businessType = '收款'
  paymentForm.channel = '银行卡'
  paymentForm.paymentType = 'Book Fee'
  paymentForm.amount = 0
  paymentForm.transactionNumber = ''
  paymentForm.arrivalTime = ''
  paymentForm.remark = ''
}
</script>

<style scoped>
@use '@/assets/styles/_variables.scss' as *;

.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.card-title {
  font-weight: bold;
  color: #303133;
}

.order-info {
  .info-item {
    display: flex;
    margin-bottom: 10px;

    label {
      width: 120px;
      font-weight: bold;
      color: #606266;
    }

    span {
      flex: 1;
      color: #303133;
    }

    .amount {
      font-weight: bold;

      &.paid {
        color: #67c23a;
      }

      &.unpaid {
        color: #f56c6c;
      }
    }
  }
}

.text-success {
  color: #67c23a;
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.mb-20 {
  margin-bottom: 20px;
}

/* --- 从 OrderDetailDialog.vue 迁移的样式 --- */
.sub-title {
  font-size: 14px;
  font-weight: bold;
  color: #409eff; /* Element Plus primary color */
  margin-top: 15px;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.customer-info,
.dealer-sales-info,
.vehicle-info,
.amount-info,
.invoice-info,
.payment-history-info {
  font-size: 13px;
  color: #606266;
}

.info-item label {
  min-width: 80px; /* 确保标签宽度一致 */
}

.info-item span {
  flex: 1;
  min-width: 0;
  word-break: break-word;
}

.red-text {
  color: #f56c6c;
}

.green-text {
  color: #67c23a;
}

.blue-text {
  color: #409eff;
}

.bold {
  font-weight: bold;
}

.large-font {
  font-size: 16px;
}

.clickable-info {
  cursor: pointer;
  color: #409eff; /* Link-like color */
}

.clickable-info:hover {
  text-decoration: underline;
}

.color-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
  vertical-align: middle;
  border: 1px solid #ccc; /* fallback border */
}

.vin-text {
  font-family: 'Courier New', Courier, monospace; /* 等宽字体 */
  background-color: #f0f0f0;
  padding: 2px 5px;
  border-radius: 3px;
}

.option-list {
  list-style-type: disc;
  padding-left: 20px;
  margin: 0;
}

.option-list li {
  margin-bottom: 5px;
}

.total-options-price {
  margin-top: 10px;
  font-size: 14px;
}

.basic-info-card {
  background-color: #f0f8ff; /* 浅蓝色背景 */
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}

.dialog-footer-buttons .el-button {
  margin-left: 10px;
}
</style>
