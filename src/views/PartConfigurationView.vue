<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('partName')">
              <el-input
                v-model="searchParams.partName"
                :placeholder="t('partNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partNumber')">
              <el-input
                v-model="searchParams.partNumber"
                :placeholder="t('partNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('supplierName')">
              <el-input
                v-model="searchParams.supplierName"
                :placeholder="t('supplierNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="buttons-col">
              <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据展示区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column type="index" :label="tc('sequence')" width="80" />
        <el-table-column :label="t('partName')" prop="partName" min-width="150" />
        <el-table-column :label="t('partNumber')" prop="partNumber" min-width="150" />
        <el-table-column :label="t('unit')" prop="unit" min-width="100" />
        <el-table-column :label="t('supplierName')" prop="supplierName" min-width="150" />
        <el-table-column :label="t('safetyStock')" prop="safetyStock" min-width="120" />
        <el-table-column :label="tc('operations')" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleConfigure(row)">
              {{ t('configure') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 零件配置模态框 -->
    <el-dialog
      v-model="configurationDialogVisible"
      :title="t('configurationModalTitle')"
      width="500px"
    >
      <el-form :model="currentPart" label-position="top">
        <el-form-item :label="t('partName')">
          <el-input v-model="currentPart.partName" disabled />
        </el-form-item>
        <el-form-item :label="t('partNumber')">
          <el-input v-model="currentPart.partNumber" disabled />
        </el-form-item>
        <el-form-item :label="t('unit')">
          <el-input v-model="currentPart.unit" disabled />
        </el-form-item>
        <el-form-item :label="t('safetyStock')">
          <el-input-number v-model="currentPart.safetyStock" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="configurationDialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="saveConfiguration">{{ tc('confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const { t, tc } = useModuleI18n('partsConfiguration');

interface PartConfigItem {
  partName: string;
  partNumber: string;
  unit: string;
  supplierName: string;
  safetyStock: number;
}

// 搜索参数
const searchParams = reactive({
  partName: '',
  partNumber: '',
  supplierName: ''
});

// 加载状态
const loading = ref(false);

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 模拟数据
const mockTableData: PartConfigItem[] = [
  {
    partName: '刹车片',
    partNumber: 'BP001',
    unit: '套',
    supplierName: '供应商A',
    safetyStock: 10
  },
  {
    partName: '机油滤清器',
    partNumber: 'OF002',
    unit: '个',
    supplierName: '供应商B',
    safetyStock: 5
  },
  {
    partName: '火花塞',
    partNumber: 'SP003',
    unit: '支',
    supplierName: '供应商C',
    safetyStock: 20
  },
  {
    partName: '雨刮器',
    partNumber: 'WW004',
    unit: '对',
    supplierName: '供应商D',
    safetyStock: 8
  },
  {
    partName: '空气滤芯',
    partNumber: 'AF005',
    unit: '个',
    supplierName: '供应商E',
    safetyStock: 15
  }
];

const tableData = ref<PartConfigItem[]>([]);
const currentPart = ref<Partial<PartConfigItem>>({}); // 用于模态框的当前零件数据
const configurationDialogVisible = ref(false); // 模态框显示状态

// 搜索方法
const handleSearch = async () => {
  loading.value = true;
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    const filteredData = mockTableData.filter(item => {
      const partNameMatch = searchParams.partName ? item.partName.includes(searchParams.partName) : true;
      const partNumberMatch = searchParams.partNumber ? item.partNumber.includes(searchParams.partNumber) : true;
      const supplierNameMatch = searchParams.supplierName ? item.supplierName.includes(searchParams.supplierName) : true;
      return partNameMatch && partNumberMatch && supplierNameMatch;
    });

    // 模拟分页
    const start = (pagination.currentPage - 1) * pagination.pageSize;
    const end = start + pagination.pageSize;
    tableData.value = filteredData.slice(start, end);
    pagination.total = filteredData.length;

    ElMessage.success(tc('operationSuccessful'));
  } catch (error) {
    console.error('搜索失败:', error);
    ElMessage.error(tc('searchFailed'));
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const resetSearch = () => {
  searchParams.partName = '';
  searchParams.partNumber = '';
  searchParams.supplierName = '';
  pagination.currentPage = 1; // 重置页码
  handleSearch(); // 重置后重新搜索
};

// 处理配置按钮点击
const handleConfigure = (row: PartConfigItem) => {
  currentPart.value = { ...row }; // 将当前行数据复制到模态框表单
  configurationDialogVisible.value = true; // 显示模态框
};

// 保存配置
const saveConfiguration = () => {
  // TODO: 实现保存配置的逻辑（例如调用后端API）
  console.log('保存配置:', currentPart.value);
  ElMessage.success(tc('operationSuccessful'));
  configurationDialogVisible.value = false; // 关闭模态框
  handleSearch(); // 刷新数据
};

// 分页方法
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  handleSearch();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  handleSearch();
};

// 页面加载时执行一次搜索
onMounted(() => {
  handleSearch();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

.buttons-col {
  text-align: right;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
}

.dialog-footer-buttons .el-button {
  margin-left: 10px;
}
</style>
