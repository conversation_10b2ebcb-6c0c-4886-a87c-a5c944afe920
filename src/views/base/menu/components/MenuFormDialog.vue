<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="700px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-position="top" class="dialog-form-modern">
      <el-form-item :label="t('menu.parentMenu')">
        <el-tree-select
          v-model="formData.parentId"
          :data="menuTreeOptions"
          :props="{ label: 'menuName', value: 'id' }"
          :placeholder="t('menu.selectParentMenu')"
          check-strictly
          clearable
          style="width: 100%"
          :disabled="isView || lockParent"
        />
      </el-form-item>
      
      <el-form-item :label="tc('type')" prop="menuType">
        <DictionaryRadio
          v-model="formData.menuType"
          :dictionary-type="DICTIONARY_TYPES.MENU_TYPE"
          :disabled="isView"
        />
      </el-form-item>
      
      <el-form-item :label="t('menu.menuName')" prop="menuName">
        <el-input 
          v-model="formData.menuName" 
          :placeholder="t('menu.enterMenuName')" 
          :disabled="isView" 
        />
      </el-form-item>
      
      <el-form-item :label="t('menu.menuCode')" prop="menuCode">
        <el-input 
          v-model="formData.menuCode" 
          :placeholder="t('menu.enterMenuCode')" 
          :disabled="isView" 
        />
      </el-form-item>

      <template v-if="formData.menuType !== '02030003'">
        <el-form-item :label="t('menu.menuIcon')" prop="menuIcon">
          <el-input 
            v-model="formData.menuIcon" 
            :placeholder="t('menu.selectMenuIcon')" 
            :disabled="isView" 
          />
        </el-form-item>
        
        <el-form-item :label="t('menu.menuPath')" prop="menuPath">
          <el-input 
            v-model="formData.menuPath" 
            :placeholder="t('menu.enterMenuPath')" 
            :disabled="isView" 
          />
        </el-form-item>
        
        <el-form-item 
          v-if="formData.menuType === '02030002'"
          :label="t('menu.component')" 
          prop="component"
        >
          <el-input 
            v-model="formData.component" 
            :placeholder="t('menu.enterComponent')" 
            :disabled="isView" 
          />
        </el-form-item>
      </template>

      <el-form-item :label="t('menu.permission')" prop="permission">
        <el-input 
          v-model="formData.permission" 
          :placeholder="t('menu.enterPermission')" 
          :disabled="isView" 
        />
      </el-form-item>
      
      <el-form-item :label="tc('status')" prop="menuStatus">
        <DictionaryRadio
          v-model="formData.menuStatus"
          :dictionary-type="DICTIONARY_TYPES.COMMON_STATUS"
          :disabled="isView"
        />
      </el-form-item>
      
      <el-form-item :label="tc('visible')" prop="isVisible">
        <el-radio-group v-model="formData.isVisible" :disabled="isView">
          <el-radio :label="1">{{ tc('yes') }}</el-radio>
          <el-radio :label="0">{{ tc('no') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item :label="tc('sort')" prop="sortOrder">
        <el-input-number
          v-model="formData.sortOrder"
          :min="1"
          :max="9999"
          :placeholder="tc('enterSort')"
          :disabled="isView"
        />
      </el-form-item>
      

    </el-form>
    
    <template #footer>
      <span class="dialog-footer-buttons">
        <el-button @click="dialogVisible = false">
          {{ isView ? tc('close') : tc('cancel') }}
        </el-button>
        <el-button v-if="!isView" type="primary" @click="handleSubmit">
          {{ tc('confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { ElForm } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import DictionaryRadio from '@/components/common/DictionaryRadio.vue';
import type { Menu, MenuFormData, CreateMenuRequest, UpdateMenuRequest, MenuTreeOption } from '@/types/base/menu';

const { t, tc } = useModuleI18n('base');

// 定义Props
interface Props {
  visible: boolean;
  isEdit?: boolean;
  isView?: boolean;
  menuData?: Menu | null;
  parentMenu?: Menu;
  lockParent?: boolean;
  menuTreeOptions?: MenuTreeOption[];
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false,
  isView: false,
  menuData: null,
  parentMenu: undefined,
  lockParent: false,
  menuTreeOptions: () => []
});

// 定义事件
const emit = defineEmits<{
  'update:visible': [visible: boolean];
  submit: [data: CreateMenuRequest | UpdateMenuRequest];
}>();

// 表单引用
const formRef = ref<InstanceType<typeof ElForm>>();

// 弹窗可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 弹窗标题
const dialogTitle = computed(() => {
  if (props.isView) return tc('view') + t('menu.menu');
  return props.isEdit ? t('menu.editMenu') : t('menu.addMenu');
});

// 表单数据
const formData = reactive<MenuFormData>({
  parentId: null,
  menuType: '02030001', // 默认目录（字典编码）
  menuName: '',
  menuCode: '',
  menuIcon: '',
  menuPath: '',
  component: '',
  permission: '',
  menuStatus: '00020001', // 默认正常（字典编码）
  isVisible: 1,
  sortOrder: 1,
  menuSide: 'factory',
  isCache: 0
});

// 表单验证规则
const formRules = computed(() => ({
  menuName: [{ required: true, message: t('menu.menuNameRequired'), trigger: 'blur' }],
  menuCode: [{ required: true, message: t('menu.menuCodeRequired'), trigger: 'blur' }],
  menuType: [{ required: true, message: t('menu.menuTypeRequired'), trigger: 'change' }],
  menuStatus: [{ required: true, message: t('menu.menuStatusRequired'), trigger: 'change' }]
}));

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    parentId: null,
    menuType: '02030001', // 目录（字典编码）
    menuName: '',
    menuCode: '',
    menuIcon: '',
    menuPath: '',
    component: '',
    permission: '',
    menuStatus: '00020001', // 正常（字典编码）
    isVisible: 1,
    sortOrder: 1,
    menuSide: 'factory',
    isCache: 0
  });
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal && props.menuData) {
    nextTick(() => {
      Object.assign(formData, {
        parentId: props.menuData!.parentId || null,
        menuType: props.menuData!.menuType,
        menuName: props.menuData!.menuName,
        menuCode: props.menuData!.menuCode,
        menuIcon: props.menuData!.menuIcon || '',
        menuPath: props.menuData!.menuPath || '',
        component: props.menuData!.component || '',
        permission: props.menuData!.permission || '',
        menuStatus: props.menuData!.menuStatus,
        isVisible: props.menuData!.isVisible,
        sortOrder: props.menuData!.sortOrder || 1,
        menuSide: props.menuData!.menuSide || 'factory',
        isCache: props.menuData!.isCache || 0
      });
    });
  } else if (newVal && !props.isEdit && !props.isView) {
    // 新增时重置表单
    resetForm();
    // 如果指定了父菜单，设置父菜单ID
    if (props.parentMenu) {
      formData.parentId = props.parentMenu.id;
    }
  }
});

// 表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const submitData = props.isEdit 
        ? { ...formData, id: props.menuData!.id } as UpdateMenuRequest
        : { ...formData } as CreateMenuRequest;
      
      emit('submit', submitData);
    }
  });
};
</script>

<style lang="scss" scoped>
.dialog-form-modern {
  .el-form-item {
    margin-bottom: 22px;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
