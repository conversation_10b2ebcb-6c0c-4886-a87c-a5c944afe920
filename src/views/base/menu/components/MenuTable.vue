<template>
  <el-table
    ref="tableRef"
    v-loading="loading"
    :data="tableData"
    row-key="id"
    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    :default-expand-all="defaultExpandAll"
    stripe
    border
  >
    <el-table-column type="index" :label="tc('index')" width="80" />
    <el-table-column prop="menuName" :label="t('menu.menuName')" min-width="200" show-overflow-tooltip />
    <el-table-column prop="menuIcon" :label="t('menu.menuIcon')" width="80" align="center">
      <template #default="{ row }">
        <el-icon v-if="row.menuIcon">
          <component :is="row.menuIcon" />
        </el-icon>
      </template>
    </el-table-column>
    <el-table-column prop="menuType" :label="t('menu.menuType')" width="100" align="center">
       <template #default="{ row }">
          {{ getMenuTypeName(row.menuType) }}
       </template>
    </el-table-column>
    <el-table-column prop="permission" :label="t('menu.permission')" min-width="200" show-overflow-tooltip />
    <el-table-column prop="component" :label="t('menu.component')" min-width="200" show-overflow-tooltip />
    <el-table-column prop="menuStatus" :label="t('menu.menuStatus')" width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="getMenuStatusTagType(row.menuStatus)">
          {{ getMenuStatusName(row.menuStatus) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="isVisible" :label="tc('visible')" width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="row.isVisible === 1 ? 'success' : 'info'">
          {{ row.isVisible === 1 ? tc('yes') : tc('no') }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="sortOrder" :label="tc('sort')" width="80" />
    <el-table-column :label="tc('operations')" fixed="right" width="320" align="center">
      <template #default="{ row }">
        <el-button
          v-permission="'system:menu:create'"
          type="primary"
          link
          :icon="Plus"
          @click="$emit('addChild', row)"
          v-if="row.menuType !== '02030003'"
        >
          {{ tc('add') }}
        </el-button>
        <el-button type="info" link :icon="View" @click="$emit('view', row)">
          {{ tc('view') }}
        </el-button>
        <el-button 
          v-permission="'system:menu:update'" 
          type="primary" 
          link 
          :icon="Edit" 
          @click="$emit('edit', row)"
        >
          {{ tc('edit') }}
        </el-button>
        <el-button 
          v-permission="'system:menu:delete'" 
          type="danger" 
          link 
          :icon="Delete" 
          @click="$emit('delete', row)"
        >
          {{ tc('delete') }}
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElTable } from 'element-plus';
import { Plus, Edit, Delete, View } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { Menu } from '@/types/base/menu';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { t, tc } = useModuleI18n('base');

// 使用字典系统获取部门状态和部门类型
const {
  getNameByCode
} = useBatchDictionary([
  DICTIONARY_TYPES.COMMON_STATUS,      // 菜单状态
  DICTIONARY_TYPES.MENU_TYPE     // 菜单类型
]);

// 定义Props
interface Props {
  tableData: Menu[];
  loading?: boolean;
  defaultExpandAll?: boolean;
}

defineProps<Props>();

// 定义事件
defineEmits<{
  addChild: [row: Menu];
  view: [row: Menu];
  edit: [row: Menu];
  delete: [row: Menu];
}>();

// 表格引用
const tableRef = ref<InstanceType<typeof ElTable>>();

// ✅ 标准字典转义函数
const getMenuTypeName = (type: string) => {
  return getNameByCode(DICTIONARY_TYPES.MENU_TYPE, type) || type;
};

const getMenuStatusName = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.COMMON_STATUS, status) || status;
};

// ✅ 标签类型映射函数（保留用于el-tag的type属性）
const getMenuStatusTagType = (status: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' => {
  const map: Record<string, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    '00020001': 'success',  // 正常
    '00020002': 'danger'   // 禁用
  };
  return map[status] || 'info';
};

// 暴露表格引用给父组件
defineExpose({
  tableRef
});
</script>

<style lang="scss" scoped>
.el-table {
  .el-button + .el-button {
    margin-left: 8px;
  }
}
</style>
