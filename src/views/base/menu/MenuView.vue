<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('menu.title') }}</h1>

    <!-- 搜索表单 -->
    <MenuSearchForm @search="handleSearch" @reset="resetSearch" />

    <!-- 操作栏和表格 -->
    <el-card class="table-card" shadow="never">
      <div class="table-toolbar mb-20">
        <el-button 
          v-permission="'system:menu:create'" 
          type="primary" 
          :icon="Plus" 
          @click="handleAdd()"
        >
          {{ t('menu.addMenu') }}
        </el-button>
        <el-button :icon="Expand" @click="expandAll">
          {{ tc('expandAll') }}
        </el-button>
        <el-button :icon="Fold" @click="collapseAll">
          {{ tc('collapseAll') }}
        </el-button>
      </div>

      <MenuTable
        ref="tableRef"
        :table-data="tableData"
        :loading="loading"
        :default-expand-all="isExpandAll"
        @add-child="handleAddChild"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
      />

      <el-pagination
        v-if="pagination.total > 0"
        class="mt-20"
        :current-page="pagination.current"
        :page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 表单弹窗 -->
    <MenuFormDialog
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :is-view="isView"
      :menu-data="currentMenuData"
      :parent-menu="parentMenu"
      :lock-parent="lockParent"
      :menu-tree-options="menuTreeOptions"
      @submit="handleFormSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Expand, Fold } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import MenuSearchForm from './components/MenuSearchForm.vue';
import MenuTable from './components/MenuTable.vue';
import MenuFormDialog from './components/MenuFormDialog.vue';
import { getMenuList, getMenuTree, addMenu, updateMenu, deleteMenu, getMenuDetail } from '@/api/modules/base/menu';
import type { Menu, MenuSearchParams, CreateMenuRequest, UpdateMenuRequest, MenuTreeOption } from '@/types/base/menu';

const { t, tc } = useModuleI18n('base');

// 页面状态
const loading = ref(false);
const dialogVisible = ref(false);
const isEdit = ref(false);
const isView = ref(false);
const isExpandAll = ref(true); // 默认展开所有节点
const tableRef = ref();

// 数据状态
const tableData = ref<Menu[]>([]);
const currentMenuData = ref<Menu | null>(null);
const parentMenu = ref<Menu | undefined>(undefined);
const lockParent = ref(false);
const menuTreeOptions = ref<MenuTreeOption[]>([]);

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 搜索参数
const searchParams = reactive<MenuSearchParams>({
  menuName: '',
  menuStatus: '',
  menuType: ''
});



// 从树形结构中提取所有菜单（只包含目录和菜单，不包含按钮）
const extractAllMenus = (menus: Menu[]): MenuTreeOption[] => {
  const result: MenuTreeOption[] = [];
  const addedIds = new Set<string | number>();

  const traverse = (menuList: Menu[]) => {
    menuList.forEach(menu => {
      // 只处理目录和菜单类型，过滤掉按钮类型
      if (menu.menuType !== '02030003' && !addedIds.has(menu.id)) { // 02030003 是按钮类型
        const menuOption: MenuTreeOption = {
          id: menu.id,
          menuName: menu.menuName,
          children: []
        };

        // 递归处理子菜单
        if (menu.children && menu.children.length > 0) {
          const childOptions = extractAllMenus(menu.children);
          if (childOptions.length > 0) {
            menuOption.children = childOptions;
          }
        }

        result.push(menuOption);
        addedIds.add(menu.id);
      }
    });
  };

  traverse(menus);
  return result;
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    
    const queryParams = {
      current: pagination.current,
      size: pagination.size,
      menuName: searchParams.menuName || undefined,
      menuStatus: searchParams.menuStatus || undefined,
      menuType: searchParams.menuType || undefined
    };

    const response = await getMenuList(queryParams);
    
    if (response.code === '200' && response.success) {
      // API返回的数据已经是树形结构，直接使用
      tableData.value = response.result.records || [];

      // 调试信息：查看数据结构
      console.log('🔍 菜单数据结构:', tableData.value);
      console.log('🔍 第一条记录:', tableData.value[0]);
      if (tableData.value[0]?.children) {
        console.log('🔍 第一条记录的子菜单:', tableData.value[0].children);
      }

      // 从树形结构中提取所有菜单用于父菜单选择器
      menuTreeOptions.value = [
        { id: '', menuName: t('menu.title'), children: extractAllMenus(tableData.value) }
      ];

      // 更新分页信息
      pagination.total = response.result.total || 0;
      pagination.current = response.result.current || pagination.current;
    } else {
      ElMessage.error(response.message || tc('loadFailed'));
    }
  } catch (error) {
    console.error('获取菜单列表失败:', error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = (params: MenuSearchParams) => {
  Object.assign(searchParams, params);
  pagination.current = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    menuName: '',
    menuStatus: '',
    menuType: ''
  });
  pagination.current = 1;
  loadData();
};

// 新增菜单
const handleAdd = () => {
  isEdit.value = false;
  isView.value = false;
  currentMenuData.value = null;
  parentMenu.value = undefined;
  lockParent.value = false;
  dialogVisible.value = true;
};

// 新增子菜单
const handleAddChild = (row: Menu) => {
  isEdit.value = false;
  isView.value = false;
  currentMenuData.value = null;
  parentMenu.value = row;
  lockParent.value = true;
  dialogVisible.value = true;
};

// 编辑菜单
const handleEdit = (row: Menu) => {
  isEdit.value = true;
  isView.value = false;
  currentMenuData.value = { ...row };
  parentMenu.value = undefined;
  lockParent.value = false;
  dialogVisible.value = true;
};

// 查看菜单
const handleView = async (row: Menu) => {
  try {
    loading.value = true;
    const response = await getMenuDetail(row.id);

    if (response.code === '200' && response.success) {
      isEdit.value = false;
      isView.value = true;
      currentMenuData.value = response.result;
      parentMenu.value = undefined;
      lockParent.value = false;
      dialogVisible.value = true;
    } else {
      ElMessage.error(response.message || tc('loadFailed'));
    }
  } catch (error) {
    console.error('获取菜单详情失败:', error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false;
  }
};

// 删除菜单
const handleDelete = async (row: Menu) => {
  try {
    // 检查是否有子菜单
    if (row.children && row.children.length > 0) {
      ElMessage.warning(t('menu.cannotDeleteMenuWithChildren'));
      return;
    }

    await ElMessageBox.confirm(
      tc('confirmDelete', { item: row.menuName }),
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );

    const response = await deleteMenu(row.id);
    
    if (response.code === '200' && response.success) {
      ElMessage.success(tc('success'));
      loadData();
    } else {
      ElMessage.error(response.message || tc('deleteFailed'));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除菜单失败:', error);
      ElMessage.error(tc('deleteFailed'));
    }
  }
};

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (current: number) => {
  pagination.current = current;
  loadData();
};

// 展开/折叠
const expandAll = () => {
  isExpandAll.value = true;
  toggleRowExpansion(tableData.value, true);
};

const collapseAll = () => {
  isExpandAll.value = false;
  toggleRowExpansion(tableData.value, false);
};

const toggleRowExpansion = (data: Menu[], isExpand: boolean) => {
  data.forEach(row => {
    tableRef.value?.tableRef?.toggleRowExpansion(row, isExpand);
    if (row.children) {
      toggleRowExpansion(row.children, isExpand);
    }
  });
};

// 表单提交处理
const handleFormSubmit = async (data: CreateMenuRequest | UpdateMenuRequest) => {
  try {
    let response: any;
    if (isEdit.value) {
      response = await updateMenu(data as UpdateMenuRequest);
    } else {
      response = await addMenu(data as CreateMenuRequest);
    }

    if (response.code === '200' && response.success) {
      ElMessage.success(tc('success'));
      dialogVisible.value = false;
      loadData();
    } else {
      ElMessage.error(response.message || tc('operationFailed'));
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 页面初始化
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-toolbar {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-bottom: 20px;
}
</style>
