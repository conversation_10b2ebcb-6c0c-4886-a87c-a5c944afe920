<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('department.title') }}</h1>

    <!-- ✅ 搜索表单组件 -->
    <DepartmentSearchForm
      @search="handleSearchWithParams"
      @reset="handleReset"
    />

    <!-- ✅ 表格组件 -->
    <DepartmentTable
      :loading="loading"
      :table-data="tableData"
      :pagination="pagination"
      @add="handleAdd"
      @edit="handleEdit"
      @view="handleView"
      @delete="handleDelete"
      @add-child="handleAddChild"
      @expand-all="handleExpandAll"
      @collapse-all="handleCollapseAll"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- ✅ 表单弹窗组件 -->
    <DepartmentFormDialog
      v-model:visible="dialogVisible"
      :is-edit="isEdit"
      :is-view="isView"
      :department-data="currentDepartmentData"
      :department-options="departmentOptions"
      :loading="formLoading"
      :parent-department="parentDepartment"
      :lock-parent="lockParent"
      @submit="handleFormSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type {
  Department,
  DepartmentSearchParams,
  CreateDepartmentRequest,
  UpdateDepartmentRequest
} from '@/types/base/department';
import {
  getDepartmentPage,
  getDepartmentDetail,
  addDepartment,
  updateDepartment,
  deleteDepartment
} from '@/api/modules/base/department';
import DepartmentSearchForm from './components/DepartmentSearchForm.vue';
import DepartmentTable from './components/DepartmentTable.vue';
import DepartmentFormDialog from './components/DepartmentFormDialog.vue';

const { t, tc } = useModuleI18n('base');

// ✅ 状态管理（参考门店管理）
const loading = ref(false);
const formLoading = ref(false);
const tableData = ref<Department[]>([]);
const departmentOptions = ref<Department[]>([]);
const dialogVisible = ref(false);

const isEdit = ref(false);
const isView = ref(false);
const currentDepartmentData = ref<Department | undefined>(undefined);
const parentDepartment = ref<Department | undefined>(undefined);
const lockParent = ref(false);

// ✅ 使用MyBatisPlus标准分页参数
const pagination = reactive({
  current: 1,        // ✅ 符合MyBatisPlus标准
  size: 10,          // ✅ 符合MyBatisPlus标准
  total: 0,
});

const searchParams = reactive<DepartmentSearchParams>({
  departmentName: '',
  departmentCode: '',
  departmentStatus: '',
  departmentType: ''
});

// 构建树形结构（如果后端返回的不是树形结构）
const buildTree = (departments: Department[]): Department[] => {
  // 如果后端已经返回树形结构，直接使用
  if (departments.some(dept => dept.children && dept.children.length > 0)) {
    return departments;
  }

  // 如果是扁平结构，则构建树形结构
  const departmentMap = new Map(departments.map(dept => [dept.id, { ...dept, children: [] }]));
  const tree: Department[] = [];

  departments.forEach(dept => {
    const departmentNode = departmentMap.get(dept.id)!;
    if (dept.parentId) {
      const parent = departmentMap.get(dept.parentId);
      if (parent) {
        parent.children!.push(departmentNode);
      } else {
        tree.push(departmentNode);
      }
    } else {
      tree.push(departmentNode);
    }
  });

  return tree;
};

// ✅ 标准数据加载函数
const loadData = async () => {
  try {
    loading.value = true;

    const queryParams = {
      ...searchParams,
      current: pagination.current,    // ✅ 使用MyBatisPlus标准参数
      size: pagination.size          // ✅ 使用MyBatisPlus标准参数
    };

    const response = await getDepartmentPage(queryParams);

    // ✅ 标准响应处理（参考门店管理）
    if (response.code === '200' && response.success) {

      // ✅ 使用response.result.records
      tableData.value = response.result.records || [];
      // ✅ 使用response.result.total
      pagination.total = response.result.total || 0;
      pagination.current = response.result.current || pagination.current;

      // 构建树形结构用于表格显示
      tableData.value = buildTree(response.result.records || []);

      // 从树形结构中提取所有部门（包括子部门）用于父部门选择器
      const extractAllDepartments = (depts: Department[]): Department[] => {
        const result: Department[] = [];
        const addedIds = new Set<string>(); // 防止重复

        const traverse = (departments: Department[]) => {
          departments.forEach(dept => {
            if (!addedIds.has(dept.id)) {
              // 创建扁平化的部门对象（移除children避免循环引用）
              result.push({
                id: dept.id,
                departmentName: dept.departmentName,
                departmentCode: dept.departmentCode,
                parentId: dept.parentId,
                departmentType: dept.departmentType,
                departmentStatus: dept.departmentStatus,
                departmentHead: dept.departmentHead,
                sortOrder: dept.sortOrder,
                description: dept.description,
                createdAt: dept.createdAt,
                children: null
              });
              addedIds.add(dept.id);
            }
            // 递归处理子部门
            if (dept.children && dept.children.length > 0) {
              traverse(dept.children);
            }
          });
        };

        traverse(depts);
        return result;
      };

      // 使用提取的所有部门作为选项（确保包含所有层级的部门）
      departmentOptions.value = extractAllDepartments(tableData.value);
    } else {
      throw new Error(response.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取部门列表失败:', error);
    ElMessage.error(tc('loadFailed'));
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索事件处理
const handleSearchWithParams = (params: DepartmentSearchParams) => {
  // 直接使用传入的参数更新 searchParams
  searchParams.departmentName = params.departmentName;
  searchParams.departmentCode = params.departmentCode;
  searchParams.departmentStatus = params.departmentStatus;
  searchParams.departmentType = params.departmentType;
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  searchParams.departmentName = '';
  searchParams.departmentCode = '';
  searchParams.departmentStatus = '';
  searchParams.departmentType = '';
  pagination.current = 1;
  loadData();
};

// 表格事件处理
const handleAdd = () => {
  isEdit.value = false;
  isView.value = false;
  currentDepartmentData.value = undefined;
  parentDepartment.value = undefined;
  lockParent.value = false;
  dialogVisible.value = true;
};

// 新增子部门
const handleAddChild = (row: Department) => {

  isEdit.value = false;
  isView.value = false;
  currentDepartmentData.value = undefined;
  parentDepartment.value = row;
  lockParent.value = true;
  dialogVisible.value = true;
};

const handleEdit = (row: Department) => {

  isEdit.value = true;
  isView.value = false;

  // 确保数据完整性，特别是sortOrder字段
  const editData = {
    ...row,
    sortOrder: row.sortOrder ?? 1 // 如果sortOrder为null或undefined，设置默认值1
  };

  currentDepartmentData.value = editData;
  parentDepartment.value = undefined;
  lockParent.value = false;
  dialogVisible.value = true;
};

const handleView = async (row: Department) => {
  try {
    loading.value = true;
    const response = await getDepartmentDetail(row.id);

    if (response.code === '200' && response.success) {
      isEdit.value = false;
      isView.value = true;
      currentDepartmentData.value = response.result;
      dialogVisible.value = true;
    } else {
      ElMessage.error(response.message || tc('loadFailed'));
    }
  } catch (error) {
    console.error('获取部门详情失败:', error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false;
  }
};

const handleDelete = async (row: Department) => {
  try {
    // 检查是否有子部门
    if (row.children && row.children.length > 0) {
      ElMessage.warning(t('department.cannotDeleteDepartmentWithChildren'));
      return;
    }

    await ElMessageBox.confirm(
      tc('confirmDelete', { item: row.departmentName }),
      tc('warning'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning'
      }
    );

    const response = await deleteDepartment(row.id);
    if (response.code === '200' && response.success) {
      ElMessage.success(tc('success'));
      loadData();
    } else {
      ElMessage.error(response.message || tc('operationFailed'));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
    }
  }
};

// 表单提交处理
const handleFormSubmit = async (data: CreateDepartmentRequest | UpdateDepartmentRequest) => {
  try {

    formLoading.value = true;

    let response: any;
    if (isEdit.value) {
      response = await updateDepartment(data as UpdateDepartmentRequest);
    } else {
      response = await addDepartment(data as CreateDepartmentRequest);
    }

    if (response.code === '200' && response.success) {
      ElMessage.success(tc('success'));
      dialogVisible.value = false;
      loadData();
    } else {
      ElMessage.error(response.message || tc('operationFailed'));
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    formLoading.value = false;
  }
};

// ✅ 修正分页处理函数
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;    // ✅ 使用current
  loadData();
};

const handleCurrentChange = (current: number) => {
  pagination.current = current;  // ✅ 使用current
  loadData();
};

// 展开/折叠处理（由表格组件处理）
const handleExpandAll = () => {
  // 由表格组件内部处理
};

const handleCollapseAll = () => {
  // 由表格组件内部处理
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 20px;
}
</style>
