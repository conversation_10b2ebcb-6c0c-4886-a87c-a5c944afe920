<template>
  <el-card class="table-card" shadow="never">
    <div class="table-header">
      <div class="table-actions">
        <el-button
          v-permission="'system:department:create'"
          type="primary"
          @click="$emit('add')"
        >
          {{ t('department.addDepartment') }}
        </el-button>
        <el-button @click="handleExpandAll">{{ tc('expandAll') }}</el-button>
        <el-button @click="handleCollapseAll">{{ tc('collapseAll') }}</el-button>
      </div>
    </div>

    <el-table
      ref="tableRef"
      :data="tableData"
      v-loading="loading"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :default-expand-all="defaultExpandAll"
    >
      <el-table-column type="index" :label="tc('index')" width="80" />
      <el-table-column prop="departmentName" :label="t('department.departmentName')" min-width="150" />
      <el-table-column prop="departmentCode" :label="t('department.departmentCode')" width="120" />
      <el-table-column prop="departmentType" :label="t('department.departmentType')" width="120">
        <template #default="{ row }">
          {{ getDepartmentTypeName(row.departmentType) }}
        </template>
      </el-table-column>
      <el-table-column prop="departmentStatus" :label="t('department.departmentStatus')" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.departmentStatus)">
            {{ getDepartmentStatusName(row.departmentStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="departmentHead" :label="t('department.departmentHead')" width="100" />
      <el-table-column prop="sortOrder" :label="tc('sort')" width="80" />
      <el-table-column :label="tc('operations')" fixed="right" width="320">
        <template #default="{ row }">
          <el-button type="info" link @click="$emit('view', row)">{{ tc('view') }}</el-button>
          <el-button
            v-permission="'system:department:update'"
            type="primary"
            link
            @click="$emit('edit', row)"
          >
            {{ tc('edit') }}
          </el-button>
          <el-button
            v-permission="'system:department:create'"
            type="success"
            link
            @click="$emit('add-child', row)"
          >
            {{ tc('add') }}
          </el-button>
          <el-button
            v-permission="'system:department:delete'"
            type="danger"
            link
            @click="$emit('delete', row)"
          >
            {{ tc('delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-if="pagination.total > 0"
      class="mt-20"
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useBatchDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import type { Department } from '@/types/base/department';

interface Props {
  loading: boolean;
  tableData: Department[];
  pagination: {
    current: number;
    size: number;
    total: number;
  };
}

interface Emits {
  (e: 'add'): void;
  (e: 'edit', row: Department): void;
  (e: 'view', row: Department): void;
  (e: 'delete', row: Department): void;
  (e: 'add-child', row: Department): void;
  (e: 'expand-all'): void;
  (e: 'collapse-all'): void;
  (e: 'size-change', size: number): void;
  (e: 'current-change', current: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('base');

// 使用字典系统获取部门状态和部门类型
const {
  getNameByCode
} = useBatchDictionary([
  DICTIONARY_TYPES.COMMON_STATUS,      // 部门状态
  DICTIONARY_TYPES.DEPARTMENT_TYPE     // 部门类型
]);

const tableRef = ref();
const defaultExpandAll = ref(false);

// 展开/折叠状态管理
const expandedKeys = ref<string[]>([]);

// ✅ 标准字典转义函数
const getDepartmentTypeName = (type: string) => {
  return getNameByCode(DICTIONARY_TYPES.DEPARTMENT_TYPE, type) || type;
};

const getDepartmentStatusName = (status: string) => {
  return getNameByCode(DICTIONARY_TYPES.COMMON_STATUS, status) || status;
};

// ✅ 状态标签样式映射（根据数据字典编码）
const getStatusTagType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '00020001': 'success',  // 正常
    '00020002': 'danger',   // 禁用
    // 兼容字符串值
    'normal': 'success',
    'disabled': 'danger'
  };
  return typeMap[status] || 'info';
};

// 分页事件处理
const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const handleCurrentChange = (current: number) => {
  emit('current-change', current);
};

// 展开全部
const handleExpandAll = () => {
  if (tableRef.value) {
    // 获取所有有子节点的部门ID
    const getAllParentIds = (departments: Department[]): string[] => {
      const ids: string[] = [];
      departments.forEach(dept => {
        if (dept.children && dept.children.length > 0) {
          ids.push(dept.id);
          ids.push(...getAllParentIds(dept.children));
        }
      });
      return ids;
    };

    const allParentIds = getAllParentIds(props.tableData);
    allParentIds.forEach(id => {
      tableRef.value.toggleRowExpansion({ id }, true);
    });
    expandedKeys.value = allParentIds;
  }
  emit('expand-all');
};

// 折叠全部
const handleCollapseAll = () => {
  if (tableRef.value) {
    expandedKeys.value.forEach(id => {
      tableRef.value.toggleRowExpansion({ id }, false);
    });
    expandedKeys.value = [];
  }
  emit('collapse-all');
};
</script>

<style lang="scss" scoped>
.table-card {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.mt-20 {
  margin-top: 20px;
}
</style>
