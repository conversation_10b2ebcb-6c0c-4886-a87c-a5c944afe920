<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isView ? tc('view') + t('department.title') : (isEdit ? t('department.editDepartment') : t('department.addDepartment'))"
    width="600px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" class="dialog-form-modern">
      <el-form-item :label="t('department.departmentCode')" prop="departmentCode">
        <el-input
          v-model="formData.departmentCode"
          :placeholder="t('department.enterDepartmentCode')"
          :disabled="isEdit || isView"
        />
      </el-form-item>

      <el-form-item :label="t('department.departmentName')" prop="departmentName">
        <el-input
          v-model="formData.departmentName"
          :placeholder="t('department.enterDepartmentName')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('department.parentDepartment')" prop="parentId">
        <el-select
          v-model="formData.parentId"
          :placeholder="t('department.selectParentDepartment')"
          clearable
          filterable
          :disabled="isView || lockParent"
        >
          <el-option
            v-for="dept in flatDepartmentOptions"
            :key="dept.id"
            :label="dept.departmentName"
            :value="dept.id"
          />
        </el-select>
        <!-- <div v-if="lockParent && parentDepartment" class="parent-dept-info">
          <el-text type="info" size="small">
            {{ tc('parentDepartmentLocked') }}: {{ parentDepartment.departmentName }}
          </el-text>
        </div> -->
      </el-form-item>

      <el-form-item :label="t('department.departmentType')" prop="departmentType">
        <DictionarySelect
          v-model="formData.departmentType"
          :dictionary-type="DICTIONARY_TYPES.DEPARTMENT_TYPE"
          :placeholder="t('department.selectDepartmentType')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('department.departmentStatus')" prop="departmentStatus">
        <DictionaryRadio
          v-model="formData.departmentStatus"
          :dictionary-type="DICTIONARY_TYPES.COMMON_STATUS"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="t('department.departmentHead')" prop="departmentHead">
        <el-input
          v-model="formData.departmentHead"
          :placeholder="t('department.selectDepartmentHead')"
          :disabled="isView"
        />
      </el-form-item>

      <el-form-item :label="tc('sort')" prop="sortOrder">
        <el-input-number
          v-model="formData.sortOrder"
          :min="1"
          :max="9999"
          :placeholder="tc('enterSort')"
          :disabled="isView"
          @change="handleSortOrderChange"
        />
      </el-form-item>

      <el-form-item :label="t('department.description')" prop="description">
        <el-input
          v-model="formData.description"
          :placeholder="t('department.enterDescription')"
          type="textarea"
          :rows="3"
          :disabled="isView"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer-buttons">
        <el-button @click="handleClose">{{ isView ? tc('close') : tc('cancel') }}</el-button>
        <el-button v-if="!isView" type="primary" :loading="loading" @click="handleSubmit">
          {{ tc('confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { DICTIONARY_TYPES } from '@/constants/dictionary';
import DictionarySelect from '@/components/common/DictionarySelect.vue';
import DictionaryRadio from '@/components/common/DictionaryRadio.vue';
import type { Department, CreateDepartmentRequest, UpdateDepartmentRequest } from '@/types/base/department';

interface Props {
  visible: boolean;
  isEdit: boolean;
  isView: boolean;
  departmentData?: Department;
  departmentOptions: Department[];
  loading: boolean;
  parentDepartment?: Department; // 新增：指定的父部门（用于新增子部门）
  lockParent?: boolean; // 新增：是否锁定父部门选择
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', data: CreateDepartmentRequest | UpdateDepartmentRequest): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('base');

const formRef = ref();
const dialogVisible = ref(false);

// 扁平化部门选项，用于下拉选择
const flatDepartmentOptions = computed(() => {
  const departments = props.departmentOptions || [];
  const result: Department[] = [];
  const addedIds = new Set<string>(); // 用于去重

  // 如果传入的已经是扁平数据，直接过滤
  departments.forEach(dept => {
    // 排除当前编辑的部门（避免选择自己作为父部门）并去重
    if (dept.id !== formData.id && !addedIds.has(dept.id)) {
      result.push({
        id: dept.id,
        departmentName: dept.departmentName,
        departmentCode: dept.departmentCode,
        parentId: dept.parentId,
        departmentType: dept.departmentType,
        departmentStatus: dept.departmentStatus,
        departmentHead: dept.departmentHead,
        sortOrder: dept.sortOrder,
        description: dept.description,
        createdAt: dept.createdAt,
        children: null // 下拉选择不需要children
      });
      addedIds.add(dept.id);
    }
  });

  return result;
});

// 表单数据
const formData = reactive({
  id: '',
  departmentName: '',
  departmentCode: '',
  parentId: '',
  departmentType: '',
  departmentStatus: '00020001', // 默认正常状态（使用数据字典编码）
  departmentHead: '',
  sortOrder: 1, // 默认排序为1
  description: ''
});

// 表单验证规则
const formRules = {
  departmentName: [{ required: true, message: t('department.departmentNameRequired'), trigger: 'blur' }],
  departmentCode: [{ required: true, message: t('department.departmentCodeRequired'), trigger: 'blur' }],
  departmentType: [{ required: true, message: t('department.departmentTypeRequired'), trigger: 'change' }],
  departmentStatus: [{ required: true, message: t('department.departmentStatusRequired'), trigger: 'change' }]
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal && props.departmentData) {
    console.log('🔍 编辑模式 - 传入的部门数据:', props.departmentData);
    console.log('🔍 编辑模式 - sortOrder值:', props.departmentData.sortOrder);
    nextTick(() => {
      Object.assign(formData, props.departmentData);
      // 确保sortOrder有值，如果为null或undefined则设置默认值
      if (formData.sortOrder === null || formData.sortOrder === undefined) {
        formData.sortOrder = 1;
      }
      console.log('🔍 编辑模式 - 赋值后的表单数据:', formData);
    });
  } else if (newVal && !props.isEdit && !props.isView) {
    // 新增时重置表单
    resetForm();
    // 如果指定了父部门，设置父部门ID
    if (props.parentDepartment) {
      console.log('🔍 表单弹窗 - 设置父部门:', props.parentDepartment);
      console.log('🔍 表单弹窗 - 锁定状态:', props.lockParent);
      console.log('🔍 表单弹窗 - 部门选项数据:', props.departmentOptions);
      formData.parentId = props.parentDepartment.id;
    }
  }
});

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
});

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    departmentName: '',
    departmentCode: '',
    parentId: '',
    departmentType: '',
    departmentStatus: '00020001', // 使用数据字典编码
    departmentHead: '',
    sortOrder: 1, // 默认排序为1
    description: ''
  });
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

const handleClose = () => {
  dialogVisible.value = false;
};

// 排序字段变化处理
const handleSortOrderChange = (value: number | undefined) => {
  formData.sortOrder = value || 1;
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const submitData = props.isEdit
        ? { ...formData } as UpdateDepartmentRequest
        : {
            departmentName: formData.departmentName,
            departmentCode: formData.departmentCode,
            parentId: formData.parentId || undefined,
            departmentType: formData.departmentType,
            departmentStatus: formData.departmentStatus,
            departmentHead: formData.departmentHead,
            sortOrder: formData.sortOrder,
            description: formData.description
          } as CreateDepartmentRequest;

      emit('submit', submitData);
    }
  });
};
</script>

<style lang="scss" scoped>
.dialog-form-modern {
  .el-form-item {
    margin-bottom: 22px;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  .el-button {
    margin-left: 10px;
  }
}

.parent-dept-info {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;

  .el-text {
    display: flex;
    align-items: center;

    &::before {
      content: "🔒";
      margin-right: 6px;
    }
  }
}
</style>
