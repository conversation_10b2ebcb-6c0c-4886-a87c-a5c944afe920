<template>
  <div class="page-container">
    <h1 class="page-title">{{ t_checkin('checkinList') }}</h1>

    <!-- 搜索/筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t_checkin('checkinId')">
              <el-input
                v-model="searchParams.checkinId"
                :placeholder="t_checkin('checkinIdPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t_checkin('licensePlate')">
              <el-input
                v-model="searchParams.licensePlate"
                :placeholder="t_checkin('licensePlatePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t_checkin('repairPersonName')">
              <el-input
                v-model="searchParams.repairPersonName"
                :placeholder="t_checkin('repairPersonNamePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t_checkin('repairPersonPhone')">
              <el-input
                v-model="searchParams.repairPersonPhone"
                :placeholder="t_checkin('repairPersonPhonePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t_checkin('createdAt')">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="-"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="18"></el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24" class="buttons-col">
            <el-form-item class="search-buttons">
              <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20" justify="end">
        <el-col :span="24" style="text-align: right;">
          <el-button type="primary" :icon="Plus" @click="handleAddCheckinRecord">{{ t_checkin('createRecord') }}</el-button>
          <el-button :icon="Download" @click="handleExport">{{ t_checkin('export') }}</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据展示（表格）区域 -->
    <el-card class="table-card">
      <el-table
        :data="checkinList"
        v-loading="loading"
        style="width: 100%"
        border
        :empty-text="tc('noData')"
      >
        <el-table-column type="index" :label="t_checkin('id')" width="60" />
        <el-table-column prop="checkinId" :label="t_checkin('checkinId')" min-width="120" />
        <el-table-column prop="licensePlate" :label="t_checkin('licensePlate')" min-width="120" />
        <el-table-column prop="vin" :label="t_checkin('vin')" min-width="140" />
        <el-table-column prop="vehicleModel" :label="t_checkin('vehicleModel')" min-width="120" />
        <el-table-column prop="vehicleConfiguration" :label="t_checkin('vehicleConfiguration')" min-width="150" />
        <el-table-column prop="color" :label="t_checkin('color')" min-width="80" />
        <el-table-column prop="mileage" :label="t_checkin('mileage')" min-width="100">
          <template #default="scope">
            {{ scope.row.mileage ? `${scope.row.mileage} ${t_checkin('mileageUnit')}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="repairPersonName" :label="t_checkin('repairPersonName')" min-width="120" />
        <el-table-column prop="repairPersonPhone" :label="t_checkin('repairPersonPhone')" min-width="120" />
        <el-table-column prop="serviceAdvisor" :label="t_checkin('serviceAdvisor')" min-width="100" />
        <el-table-column prop="relatedRepairOrderId" :label="t_checkin('relatedRepairOrderId')" min-width="140">
          <template #default="scope">
            {{ scope.row.relatedRepairOrderId || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="serviceType" :label="t_checkin('serviceType')" min-width="100" />
        <el-table-column prop="createdAt" :label="t_checkin('createdAt')" min-width="120" />
        <el-table-column prop="updatedAt" :label="t_checkin('updatedAt')" min-width="120" />
        <el-table-column :label="tc('operations')" width="280" fixed="right">
          <template #default="scope">
            <el-button type="primary" :icon="View" link @click="handleViewDetails(scope.row)">{{ t_checkin('viewDetails') }}</el-button>
            <el-button type="primary" :icon="Edit" link @click="handleEditCheckinRecord(scope.row)">{{ tc('edit') }}</el-button>
            <el-button
              type="danger"
              :icon="Delete"
              link
              @click="handleDeleteCheckinRecord(scope.row)"
              v-if="!scope.row.relatedRepairOrderId"
            >
              {{ tc('delete') }}
            </el-button>
            <el-button
              type="success"
              :icon="Plus"
              link
              @click="handleCreateRepairOrder(scope.row)"
              v-if="!scope.row.relatedRepairOrderId"
            >
              {{ t_checkin('createRepairOrder') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑登记单弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? t_checkin('editCheckinRecord') : t_checkin('addCheckinRecord')"
      width="600px"
      class="checkin-dialog"
      @close="resetDialogForm"
    >
      <el-form :model="dialogForm" ref="dialogFormRef" label-position="top" class="dialog-form-modern">
        <!-- 顶部查询框 -->
        <el-row :gutter="20" class="mb-20">
          <el-col :span="18">
            <el-form-item :label="t_checkin('licensePlate')">
              <el-input
                v-model="licensePlateQuery"
                :placeholder="t_checkin('licensePlatePlaceholder')"
                @keyup.enter="handleQueryVehicleInfo"
                clearable
                :disabled="isEdit && !!dialogForm.checkinId"
              >
                <template #append>
                  <el-button :icon="Search" @click="handleQueryVehicleInfo"></el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right; line-height: 52px;">
            <el-text type="info">{{ t_checkin('vehicleInfoAutoFill') }}</el-text>
          </el-col>
        </el-row>

        <!-- 车辆信息区域 -->
        <el-card class="mb-20">
          <template #header>
            <span>{{ t_checkin('vehicleInfo') }}</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t_checkin('licensePlate')" prop="licensePlate">
                <el-input v-model="dialogForm.licensePlate" :disabled="isVehicleInfoReadOnly" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t_checkin('vin')" prop="vin">
                <el-input v-model="dialogForm.vin" :disabled="isVehicleInfoReadOnly" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t_checkin('vehicleModel')" prop="vehicleModel">
                <el-input v-model="dialogForm.vehicleModel" :disabled="isVehicleInfoReadOnly" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t_checkin('vehicleConfiguration')" prop="vehicleConfiguration">
                <el-input v-model="dialogForm.vehicleConfiguration" :disabled="isVehicleInfoReadOnly" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t_checkin('color')" prop="color">
                <el-input v-model="dialogForm.color" :disabled="isVehicleInfoReadOnly" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t_checkin('mileage')" prop="mileage">
                <el-input
                  v-model.number="dialogForm.mileage"
                  type="number"
                  :placeholder="t_checkin('mileagePlaceholder')"
                >
                  <template #append>{{ t_checkin('mileageUnit') }}</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t_checkin('vehicleAge')" prop="vehicleAge">
                <el-input v-model="dialogForm.vehicleAge" :disabled="true" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 客户信息区域 -->
        <el-card class="mb-20">
          <template #header>
            <span>{{ t_checkin('customerInfo') }}</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t_checkin('repairPersonName')" prop="repairPersonName" :rules="[{ required: true, message: tc('required'), trigger: 'blur' }]" >
                <el-input v-model="dialogForm.repairPersonName" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t_checkin('repairPersonPhone')" prop="repairPersonPhone" :rules="[{ required: true, message: tc('required'), trigger: 'blur' }]" >
                <el-input v-model="dialogForm.repairPersonPhone" />
              </el-form-item>
            </el-col>
             <el-col :span="12">
              <el-form-item :label="t_checkin('serviceAdvisor')" prop="serviceAdvisor" :rules="[{ required: true, message: tc('required'), trigger: 'blur' }]" >
                <el-input v-model="dialogForm.serviceAdvisor" :disabled="true" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 服务类型 -->
        <el-form-item :label="t_checkin('serviceType')" prop="serviceType" :rules="[{ required: true, message: tc('required'), trigger: 'change' }]" >
          <el-input v-model="dialogForm.serviceType" :disabled="true" />
        </el-form-item>

        <!-- 备注信息 -->
        <el-form-item :label="t_checkin('notes')" prop="notes">
          <el-input
            v-model="dialogForm.notes"
            type="textarea"
            :rows="3"
            :placeholder="t_checkin('notesPlaceholder')"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="dialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmit">{{ tc('save') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 详情弹窗 (简洁版，主要展示数据) -->
    <el-dialog
      v-model="detailsDialogVisible"
      :title="t_checkin('viewDetails')"
      width="500px"
      class="checkin-details-dialog"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item :label="t_checkin('checkinId')">{{ currentDetails.checkinId }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('licensePlate')">{{ currentDetails.licensePlate }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('vin')">{{ currentDetails.vin }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('vehicleModel')">{{ currentDetails.vehicleModel }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('vehicleConfiguration')">{{ currentDetails.vehicleConfiguration }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('color')">{{ currentDetails.color }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('mileage')">
          {{ currentDetails.mileage ? `${currentDetails.mileage} ${t_checkin('mileageUnit')}` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item :label="t_checkin('vehicleAge')">
          {{ currentDetails.vehicleAge ? `${currentDetails.vehicleAge} ${tc('months')}` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item :label="t_checkin('repairPersonName')">{{ currentDetails.repairPersonName }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('repairPersonPhone')">{{ currentDetails.repairPersonPhone }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('serviceAdvisor')">{{ currentDetails.serviceAdvisor }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('relatedRepairOrderId')">{{ currentDetails.relatedRepairOrderId || '-' }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('serviceType')">{{ currentDetails.serviceType }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('createdAt')">{{ currentDetails.createdAt }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('updatedAt')">{{ currentDetails.updatedAt }}</el-descriptions-item>
        <el-descriptions-item :label="t_checkin('notes')">{{ currentDetails.notes || '-' }}</el-descriptions-item>
        <el-descriptions-item :label="t('vehicleModel')">{{ currentDetails.vehicleModel }}</el-descriptions-item>
        <el-descriptions-item :label="t('vehicleConfiguration')">{{ currentDetails.vehicleConfiguration }}</el-descriptions-item>
        <el-descriptions-item :label="t('color')">{{ currentDetails.color }}</el-descriptions-item>
        <el-descriptions-item :label="t('mileage')">
          {{ currentDetails.mileage ? `${currentDetails.mileage} ${t('mileageUnit')}` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('vehicleAge')">
          {{ currentDetails.vehicleAge ? `${currentDetails.vehicleAge} ${tc('months')}` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('repairPersonName')">{{ currentDetails.repairPersonName }}</el-descriptions-item>
        <el-descriptions-item :label="t('repairPersonPhone')">{{ currentDetails.repairPersonPhone }}</el-descriptions-item>
        <el-descriptions-item :label="t('serviceAdvisor')">{{ currentDetails.serviceAdvisor }}</el-descriptions-item>
        <el-descriptions-item :label="t('relatedRepairOrderId')">{{ currentDetails.relatedRepairOrderId || '-' }}</el-descriptions-item>
        <el-descriptions-item :label="t('serviceType')">{{ currentDetails.serviceType }}</el-descriptions-item>
        <el-descriptions-item :label="t('createdAt')">{{ currentDetails.createdAt }}</el-descriptions-item>
        <el-descriptions-item :label="t('updatedAt')">{{ currentDetails.updatedAt }}</el-descriptions-item>
        <el-descriptions-item :label="t('notes')">{{ currentDetails.notes || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
    addCheckinRecord,
    createRelatedRepairOrder,
    deleteCheckinRecord,
    getCheckinList,
    queryVehicleInfo,
    updateCheckinRecord
} from '@/api/modules/checkin';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinListItem, CheckinListParams } from '@/types/module';
import { Delete, Download, Edit, Plus, Search, View } from '@element-plus/icons-vue';
import {
    ElButton,
    ElCard,
    ElCol,
    ElDatePicker,
    ElDescriptions,
    ElDescriptionsItem,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElMessage,
    ElMessageBox,
    ElPagination,
    ElRow,
    ElTable,
    ElTableColumn,
    ElText
} from 'element-plus';
import { computed, onMounted, reactive, ref, watch } from 'vue';

const { t, tc } = useModuleI18n('afterSales');

const t_checkin = (key: string, params?: Record<string, unknown>) => t(`checkin.${key}`, params);

// 搜索相关
const searchParams = reactive<CheckinListParams>({
  checkinId: '',
  licensePlate: '',
  repairPersonName: '',
  repairPersonPhone: '',
  createdAtStart: '',
  createdAtEnd: '',
});
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchParams.createdAtStart = newVal[0];
    searchParams.createdAtEnd = newVal[1];
  } else {
    searchParams.createdAtStart = '';
    searchParams.createdAtEnd = '';
  }
});

const checkinList = ref<CheckinListItem[]>([]);
const loading = ref(false);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 模态框相关
const dialogVisible = ref(false);
const isEdit = ref(false);
const dialogFormRef = ref(); // 用于表单校验
const dialogForm = reactive<CheckinListItem>({
  checkinId: '',
  licensePlate: '',
  vin: '',
  vehicleModel: '',
  vehicleConfiguration: '',
  color: '',
  mileage: undefined,
  repairPersonName: '',
  repairPersonPhone: '',
  serviceAdvisor: '',
  relatedRepairOrderId: null,
  serviceType: '维修', // 默认服务类型
  createdAt: '',
  updatedAt: '',
  notes: '',
  isDeleted: false,
  // 车辆查询带出字段
  vehicleAge: undefined,
});

const licensePlateQuery = ref(''); // 弹窗内部用于查询车牌号的输入
const isVehicleInfoReadOnly = computed(() => !!dialogForm.checkinId && isEdit.value); // 编辑状态且已存在checkinId时，车辆信息只读

// 详情弹窗相关
const detailsDialogVisible = ref(false);
const currentDetails = ref<Partial<CheckinListItem>>({});

// 获取到店登记列表
const fetchCheckinList = async () => {
  loading.value = true;
  try {
    const response = await getCheckinList({
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    });
    checkinList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch checkin list:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1; // 搜索时重置回第一页
  fetchCheckinList();
};

// 重置搜索
const resetSearch = () => {
  searchParams.checkinId = '';
  searchParams.licensePlate = '';
  searchParams.repairPersonName = '';
  searchParams.repairPersonPhone = '';
  dateRange.value = null;
  searchParams.createdAtStart = '';
  searchParams.createdAtEnd = '';
  pagination.page = 1;
  fetchCheckinList();
};

// 处理每页大小变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  fetchCheckinList();
};

// 处理当前页变化
const handleCurrentChange = (val: number) => {
  pagination.page = val;
  fetchCheckinList();
};

// 获取当前用户信息（模拟）
const getCurrentUser = () => {
  // 实际项目中应该从用户状态管理或API获取
  return {
    name: '当前用户', // 这里应该是实际的用户名
    // 可以从localStorage、sessionStorage或者用户状态管理中获取
  };
};

// 处理新增登记单按钮点击
const handleAddCheckinRecord = () => {
  isEdit.value = false;
  resetDialogForm(); // 清空表单
  // 自动填入当前用户为服务顾问
  const currentUser = getCurrentUser();
  dialogForm.serviceAdvisor = currentUser.name;
  dialogVisible.value = true;
};

// 处理编辑登记单
const handleEditCheckinRecord = (row: CheckinListItem) => {
  isEdit.value = true;
  // 深度复制行数据到表单，避免直接修改表格数据
  Object.assign(dialogForm, JSON.parse(JSON.stringify(row)));
  licensePlateQuery.value = row.licensePlate; // 设置查询框的值为当前车牌号
  dialogVisible.value = true;
};

// 处理删除登记单
const handleDeleteCheckinRecord = async (row: CheckinListItem) => {
  ElMessageBox.confirm(
    tc('confirmDelete', { item: row.checkinId }),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await deleteCheckinRecord(row.checkinId);
        ElMessage.success(tc('operationSuccessful'));
        fetchCheckinList();
      } catch (error) {
        console.error('Failed to delete checkin record:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 处理创建环检单
const handleCreateRepairOrder = async (row: CheckinListItem) => {
  if (row.relatedRepairOrderId) {
    ElMessage.warning(t_checkin('repairOrderAlreadyExists'));
    return;
  }
  ElMessageBox.confirm(
    t_checkin('confirmCreateRepairOrder', { checkinId: row.checkinId }),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'info',
    }
  )
    .then(async () => {
      try {
        await createRelatedRepairOrder(row.checkinId);
        ElMessage.success(t_checkin('repairOrderCreatedSuccess'));
        fetchCheckinList();
      } catch (error) {
        console.error('Failed to create repair order:', error);
        // 错误信息已经在API层处理，这里不再重复ElMessage.error
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 处理查看详情
const handleViewDetails = (row: CheckinListItem) => {
  currentDetails.value = row;
  detailsDialogVisible.value = true;
};

// 处理导出
const handleExport = () => {
  ElMessage.info(tc('exporting'));
  // 实际导出逻辑待实现，可能需要后端接口配合
  console.log('Exporting data with params:', searchParams);
};

// 模态框表单提交
const handleSubmit = async () => {
  const formEl = dialogFormRef.value;
  if (!formEl) return;

  formEl.validate(async (valid: boolean) => {
    if (valid) {
      // 移除不需要提交的字段
      const submitForm: Partial<CheckinListItem> = {
        ...dialogForm,
        vehicleAge: undefined, // 客户端计算，不提交
      };

      ElMessageBox.confirm(
        isEdit.value ? tc('confirmSave') : tc('confirmAdd'),
        tc('confirm'),
        {
          confirmButtonText: tc('confirm'),
          cancelButtonText: tc('cancel'),
          type: 'info',
        }
      )
        .then(async () => {
          try {
            if (isEdit.value) {
              await updateCheckinRecord(dialogForm.checkinId, submitForm);
            } else {
              await addCheckinRecord(submitForm as CheckinListItem);
            }
            ElMessage.success(tc('operationSuccessful'));
            dialogVisible.value = false;
            fetchCheckinList();
          } catch (error) {
            console.error('Failed to save checkin record:', error);
            ElMessage.error(tc('operationFailed'));
          }
        })
        .catch(() => {
          // 用户取消保存/新增
        });
    }
  });
};

// 重置模态框表单
const resetDialogForm = () => {
  if (dialogFormRef.value) {
    dialogFormRef.value.resetFields();
  }
  Object.assign(dialogForm, {
    checkinId: '',
    licensePlate: '',
    vin: '',
    vehicleModel: '',
    vehicleConfiguration: '',
    color: '',
    mileage: undefined,
    repairPersonName: '',
    repairPersonPhone: '',
    serviceAdvisor: '',
    relatedRepairOrderId: null,
    serviceType: '维修',
    createdAt: '',
    updatedAt: '',
    notes: '',
    isDeleted: false,
    vehicleAge: undefined,
  });
  licensePlateQuery.value = '';
};

// 弹窗内部的车牌号查询
const handleQueryVehicleInfo = async () => {
  if (!licensePlateQuery.value) {
    ElMessage.warning(t_checkin('licensePlatePlaceholder'));
    return;
  }
  try {
    const vehicleInfo = await queryVehicleInfo(licensePlateQuery.value);
    if (vehicleInfo) {
      dialogForm.licensePlate = vehicleInfo.licensePlate || '';
      dialogForm.vehicleModel = vehicleInfo.vehicleModel || '';
      dialogForm.vehicleConfiguration = vehicleInfo.vehicleConfiguration || '';
      dialogForm.color = vehicleInfo.color || '';
      dialogForm.mileage = vehicleInfo.mileage || undefined;
      dialogForm.vehicleAge = vehicleInfo.vehicleAge;
      ElMessage.success(tc('operationSuccessful'));
    } else {
      resetVehicleInfoInDialog();
      ElMessage.warning(t_checkin('vehicleInfoNotFound'));
    }
  } catch (error) {
    console.error('Failed to query vehicle info:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 重置弹窗中的车辆信息字段
const resetVehicleInfoInDialog = () => {
  dialogForm.licensePlate = '';
  dialogForm.vehicleModel = '';
  dialogForm.vehicleConfiguration = '';
  dialogForm.color = '';
  dialogForm.mileage = undefined;
  dialogForm.vehicleAge = undefined;
};

onMounted(() => {
  fetchCheckinList();
});
</script>

<style scoped lang="scss">
.page-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-title {
    margin: 0 0 20px 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .search-card {
    .search-form {
      .buttons-col {
        margin-top: 20px;
      }

      .text-right {
        text-align: right;
      }
    }
  }

  .operation-card {
    .filter-summary {
      font-size: 14px;
      color: #606266;
      line-height: 32px;
    }

    .text-right {
      text-align: right;
    }
  }

  .table-card {
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

// 全局样式覆盖
:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  .el-button--link {
    padding: 0;
    height: auto;
    line-height: 1;
  }

  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}

:deep(.el-pagination) {
  justify-content: flex-end;
}

.buttons-col {
  text-align: right;
  :deep(.el-form-item__content) {
    justify-content: flex-end;
  }
}

.checkin-dialog {
  .dialog-form-modern {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
  .dialog-footer-buttons {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
    .el-button {
      margin-left: 10px;
    }
  }
}

.checkin-details-dialog {
  // 详情弹窗样式，如果需要可以定制
}
</style>
