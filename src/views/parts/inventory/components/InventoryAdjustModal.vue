
<template>
  <el-dialog
    v-model="visible"
    :title="`${t('dialog.adjustTitle')}${currentInventory ? ' - ' + currentInventory.partCode + ' ' + currentInventory.partName : ''}`"
    width="700px"
    top="5vh"
    @close="onClose"
  >
    <div v-loading="loading">
      <!-- 零件信息区块 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>📦 {{ t('dialog.partInfo') }}</span>
          </div>
        </template>
        <div v-if="currentInventory" class="part-info-grid">
          <div class="info-item">
            <span class="info-label">{{ t('table.partCode') }}：</span>
            <span class="info-value">{{ currentInventory.partCode }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('table.partName') }}：</span>
            <span class="info-value">{{ currentInventory.partName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('table.specification') }}：</span>
            <span class="info-value">{{ currentInventory.specification }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('table.currentStock') }}：</span>
            <span class="info-value">{{ currentInventory.currentStock }} {{ unit }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('table.availableStock') }}：</span>
            <span class="info-value">{{ currentInventory.availableStock }} {{ unit }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('table.occupiedStock') }}：</span>
            <span class="info-value">{{ currentInventory.occupiedStock }} {{ unit }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">{{ t('table.damagedStock') }}：</span>
            <span class="info-value">{{ currentInventory.damagedStock }} {{ unit }}</span>
          </div>
        </div>
      </el-card>

      <!-- 调整信息区块 -->
      <el-card class="info-card mt-20" shadow="never">
        <template #header>
          <div class="card-header">
            <span>⚙️ {{ t('dialog.adjustInfo') }}</span>
          </div>
        </template>
        <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
          <el-form-item :label="t('dialog.adjustType')" prop="adjustType">
            <el-radio-group v-model="form.adjustType">
              <el-radio value="INCREASE">{{ t('dialog.increase') }}</el-radio>
              <el-radio value="DECREASE">{{ t('dialog.decrease') }}</el-radio>
              <el-radio value="SET_TO">{{ t('dialog.setTo') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item :label="t('dialog.adjustQuantity')" prop="quantity">
            <div class="quantity-input-wrapper">
              <el-input-number 
                v-model="form.quantity" 
                :min="0"
                :controls-position="'right'"
                class="quantity-input"
              />
              <span class="unit-text">{{ unit }}</span>
              <span class="current-stock-hint">
                ({{ t('dialog.currentStockHint') }}：{{ currentInventory?.currentStock || 0 }} {{ unit }})
              </span>
            </div>
          </el-form-item>

          <el-form-item v-if="adjustedStock !== null" :label="t('dialog.adjustedStock')">
            <div class="adjusted-stock-display">
              {{ adjustedStock }} {{ unit }}
            </div>
          </el-form-item>

          <el-form-item :label="t('dialog.adjustReason')" prop="reason">
            <el-radio-group v-model="form.reason">
              <el-radio value="INVENTORY_CHECK">{{ t('dialog.reasonInventoryCheck') }}</el-radio>
              <el-radio value="SYSTEM_CORRECTION">{{ t('dialog.reasonSystemCorrection') }}</el-radio>
              <el-radio value="OTHER">{{ t('dialog.reasonOther') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item :label="t('dialog.remark')">
            <el-input 
              v-model="form.remark" 
              type="textarea" 
              :rows="3" 
              :placeholder="t('dialog.remarkPlaceholder')" 
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 警告提示 -->
      <el-alert
        class="mt-20"
        :title="t('dialog.adjustWarning')"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <template #footer>
      <el-button @click="onClose">{{ tc('cancel') }}</el-button>
      <el-button type="primary" @click="onConfirm">{{ t('dialog.confirmAdjust') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { useInventoryStore } from '@/stores/modules/parts/inventory';
import { adjustInventory } from '@/api/modules/parts/inventory';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { FormInstance, FormRules } from 'element-plus';
import type { InventoryAdjustParams, InventoryItem } from '@/types/parts/inventory';

const props = defineProps({ modelValue: Boolean, inventoryId: Number });
const emit = defineEmits(['update:modelValue', 'success']);

const { t, tc } = useModuleI18n('parts.inventoryManagement');
const inventoryStore = useInventoryStore();

const formRef = ref<FormInstance>();
const loading = ref(false);
const currentInventory = ref<InventoryItem | null>(null);
const unit = ref('个'); // 默认单位，实际应该从零件详情获取

const form = reactive<Omit<InventoryAdjustParams, 'inventoryId' | 'operator'> & { inventoryId?: number, operator?: string }>({
  adjustType: 'INCREASE',
  quantity: 0,
  reason: 'INVENTORY_CHECK',
  remark: ''
});

const rules = reactive<FormRules>({
  adjustType: [{ required: true, message: t('validation.adjustTypeRequired'), trigger: 'change' }],
  quantity: [
    { required: true, message: t('validation.quantityRequired'), trigger: 'blur' },
    { type: 'number', min: 1, message: t('validation.quantityMin'), trigger: 'blur' }
  ],
  reason: [{ required: true, message: t('validation.reasonRequired'), trigger: 'change' }],
});

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// 计算调整后的库存
const adjustedStock = computed(() => {
  if (!currentInventory.value || !form.quantity) return null;
  
  const current = currentInventory.value.currentStock;
  const qty = form.quantity;
  
  switch (form.adjustType) {
    case 'INCREASE':
      return current + qty;
    case 'DECREASE':
      return Math.max(0, current - qty);
    case 'SET_TO':
      return qty;
    default:
      return current;
  }
});

// 监听inventoryId变化，获取库存详情
watch(() => props.inventoryId, async (newId) => {
  if (newId && visible.value) {
    loading.value = true;
    try {
      // 从列表中查找当前零件信息
      const item = inventoryStore.inventoryList.find(item => item.inventoryId === newId);
      if (item) {
        currentInventory.value = item;
        // 实际项目中应该从零件详情获取单位
        unit.value = '个';
      }
    } finally {
      loading.value = false;
    }
  }
}, { immediate: true });

// 监听弹窗显示状态，重置表单
watch(visible, (newVal) => {
  if (newVal) {
    // 重置表单
    form.adjustType = 'INCREASE';
    form.quantity = 0;
    form.reason = 'INVENTORY_CHECK';
    form.remark = '';
    formRef.value?.clearValidate();
  } else {
    currentInventory.value = null;
  }
});

const onConfirm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        await adjustInventory({ ...form, inventoryId: props.inventoryId, operator: 'admin' } as InventoryAdjustParams);
        emit('success');
        onClose();
      } finally {
        loading.value = false;
      }
    }
  });
};

const onClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.info-card {
  border: 1px solid #e4e7ed;
  
  .card-header {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }
}

.part-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  
  .info-item {
    display: flex;
    align-items: center;
    
    .info-label {
      color: #909399;
      min-width: 80px;
    }
    
    .info-value {
      color: #303133;
      font-weight: 500;
    }
  }
}

.quantity-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .quantity-input {
    width: 200px;
  }
  
  .unit-text {
    color: #606266;
  }
  
  .current-stock-hint {
    color: #909399;
    font-size: 14px;
  }
}

.adjusted-stock-display {
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
}

.mt-20 {
  margin-top: 20px;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: row;
  gap: 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style>
