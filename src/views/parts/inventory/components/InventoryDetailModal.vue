
<template>
  <el-dialog
    v-model="visible"
    :title="t('dialog.detailTitle')"
    width="900px"
    top="5vh"
    @close="onClose"
  >
    <div v-if="loading.detail" class="loading-container">...</div>
    <div v-else-if="currentDetail" class="detail-content">
      <!-- 零件基础信息区域 -->
      <div class="info-section">
        <div class="section-header">
          <span class="section-icon">📋</span>
          <span class="section-title">{{ t('dialog.partInfo') }}</span>
        </div>
        <div class="part-info-grid">
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">{{ t('table.partCode') }}：</span>
              <span class="info-value">{{ currentDetail.partInfo.partCode }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('table.partName') }}：</span>
              <span class="info-value">{{ currentDetail.partInfo.partName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('table.specification') }}：</span>
              <span class="info-value">{{ currentDetail.partInfo.specification }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">{{ t('dialog.unit') }}：</span>
              <span class="info-value">{{ currentDetail.partInfo.unit }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('dialog.retailPrice') }}：</span>
              <span class="info-value">{{ currentDetail.partInfo.retailPrice }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">{{ t('dialog.purchasePrice') }}：</span>
              <span class="info-value">{{ currentDetail.partInfo.purchasePrice }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 库存详细信息区域 -->
      <div class="info-section">
        <div class="section-header">
          <span class="section-icon">📦</span>
          <span class="section-title">{{ t('dialog.inventoryInfo') }}</span>
        </div>
        <div class="inventory-cards">
          <div class="inventory-card-row">
            <div class="inventory-card stock-numbers">
              <div class="card-item">
                <span class="card-label">{{ t('table.currentStock') }}：</span>
                <span class="card-value highlight">{{ currentDetail.inventoryInfo.currentStock }} {{ currentDetail.partInfo.unit }}</span>
              </div>
              <div class="card-item">
                <span class="card-label">{{ t('table.availableStock') }}：</span>
                <span class="card-value">{{ currentDetail.inventoryInfo.availableStock }} {{ currentDetail.partInfo.unit }}</span>
              </div>
              <div class="card-item">
                <span class="card-label">{{ t('table.occupiedStock') }}：</span>
                <span class="card-value">{{ currentDetail.inventoryInfo.occupiedStock }} {{ currentDetail.partInfo.unit }}</span>
              </div>
              <div class="card-item">
                <span class="card-label">{{ t('table.safetyStock') }}：</span>
                <span class="card-value">{{ currentDetail.inventoryInfo.safetyStock }} {{ currentDetail.partInfo.unit }}</span>
              </div>
              <div class="card-item">
                <span class="card-label">{{ t('table.stockStatus') }}：</span>
                <span class="card-value status-value">{{ getStatusEmoji(currentDetail.inventoryInfo.stockStatus) }} {{ t(`status.${currentDetail.inventoryInfo.stockStatus}`) }}</span>
              </div>
            </div>
            <div class="inventory-card location-info">
              <div class="card-item">
                <span class="card-label">{{ t('dialog.lastCheckTime') }}：</span>
                <span class="card-value">{{ currentDetail.inventoryInfo.lastCheckTime }}</span>
              </div>
              <div class="card-item">
                <span class="card-label">{{ t('dialog.checkPerson') }}：</span>
                <span class="card-value">{{ currentDetail.inventoryInfo.checkPerson }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 库存变化趋势区域 -->
      <div class="info-section">
        <div class="section-header">
          <span class="section-icon">📈</span>
          <span class="section-title">{{ t('dialog.inventoryTrend') }} ({{ t('dialog.last30Days') }})</span>
        </div>
        <div v-if="loading.trend" class="loading-container">...</div>
        <div v-else-if="inventoryTrendData" class="trend-content">
          <div class="chart-info">
            <span>{{ t('dialog.safetyStockLine') }}: {{ inventoryTrendData.safetyStockLine }}</span>
          </div>
          <div class="chart-wrapper">
            <InventoryTrendChart 
              :data="inventoryTrendData.trendData" 
              :safety-stock-line="inventoryTrendData.safetyStockLine"
              height="220px"
            />
          </div>
        </div>
        <div v-else class="no-trend-data">
          {{ t('dialog.noTrendData') }}
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="onClose">{{ globalT('common.cancel') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useInventoryStore } from '@/stores/modules/parts/inventory';
import { storeToRefs } from 'pinia';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useI18n } from 'vue-i18n';
import type { InventoryTrend, StockStatus } from '@/types/parts/inventory';
import { getInventoryTrend } from '@/api/modules/parts/inventory';
import InventoryTrendChart from '@/components/InventoryTrendChart.vue';

const props = defineProps({ modelValue: Boolean, inventoryId: { type: Number, default: null } });
const emit = defineEmits(['update:modelValue']);

const { t } = useModuleI18n('parts.inventoryManagement');
const { t: globalT } = useI18n();
const inventoryStore = useInventoryStore();
const { currentDetail, loading } = storeToRefs(inventoryStore);

const inventoryTrendData = ref<InventoryTrend | null>(null);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const getStatusEmoji = (status: StockStatus) => {
  switch (status) {
    case 'SHORTAGE': return '🔴';
    case 'WARNING': return '⚠️';
    case 'NORMAL': return '✅';
    case 'OVERSTOCKED': return '📦';
    default: return '❓';
  }
};

watch(() => props.inventoryId, async (newId) => {
  if (newId) {
    await inventoryStore.fetchInventoryDetail(newId);
    // Fetch trend data when modal opens and inventoryId is set
    loading.value.trend = true;
    try {
      inventoryTrendData.value = await getInventoryTrend({ inventoryId: newId });
    } catch (error) {
      console.error('Failed to fetch inventory trend:', error);
      inventoryTrendData.value = null;
    } finally {
      loading.value.trend = false;
    }
  }
});

const onClose = () => {
  visible.value = false;
  inventoryTrendData.value = null; // Clear trend data on close
};
</script>

<style scoped lang="scss">
.detail-content {
  .info-section {
    margin-bottom: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-header {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 10px 16px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;
      border-radius: 4px 4px 0 0;
      
      .section-icon {
        font-size: 16px;
      }
      
      .section-title {
        font-size: 13px;
        font-weight: 500;
        color: #303133;
      }
    }
    
    .part-info-grid {
      padding: 16px;
      
      .info-row {
        display: flex;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .info-item {
          flex: 1;
          display: flex;
          padding-right: 12px;
          min-width: 0; // 防止flex item溢出
          
          .info-label {
            display: inline-block;
            width: 75px;
            color: #606266;
            font-size: 12px;
            flex-shrink: 0;
          }
          
          .info-value {
            color: #303133;
            font-size: 12px;
            font-weight: 500;
            word-break: break-word; // 长文本换行
          }
        }
      }
    }
    
    .inventory-cards {
      padding: 16px;
      
      .inventory-card-row {
        display: flex;
        gap: 16px;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .inventory-card {
          flex: 1;
          padding: 12px;
          background-color: #fafafa;
          border: 1px solid #ebeef5;
          border-radius: 3px;
          
          .card-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .card-label {
              display: inline-block;
              width: 90px;
              color: #606266;
              font-size: 12px;
              flex-shrink: 0;
            }
            
            .card-value {
              color: #303133;
              font-size: 12px;
              font-weight: 500;
              flex: 1;
              word-break: break-word;
              line-height: 1.4;
              
              &.highlight {
                color: #409eff;
                font-weight: 600;
                font-size: 13px;
              }
              
              &.status-value {
                display: flex;
                align-items: center;
                gap: 3px;
              }
            }
          }
        }
      }
    }
    
    .trend-content {
      padding: 16px;
      
      .chart-info {
        margin-bottom: 12px;
        padding: 8px 12px;
        background-color: #f0f9ff;
        border: 1px solid #cce7ff;
        border-radius: 3px;
        color: #0369a1;
        font-size: 12px;
        text-align: center;
      }
      
      .chart-wrapper {
        border: 1px solid #e4e7ed;
        border-radius: 3px;
        padding: 12px;
        background-color: #fefefe;
      }
    }
    
    .no-trend-data {
      padding: 30px 16px;
      text-align: center;
      color: #909399;
      font-size: 13px;
    }
  }
}

.loading-container {
  padding: 30px;
  text-align: center;
  color: #909399;
  font-size: 13px;
}
</style>
