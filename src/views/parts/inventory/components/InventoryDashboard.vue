
<template>
  <el-row :gutter="20" class="dashboard-container">
    <el-col :span="6">
      <el-card class="statistic-card total-sku">
        <div class="total-sku-content">
          <div class="total-sku-left">
            <div class="statistic-title">{{ t('dashboard.totalSkuCount') }}</div>
            <div class="statistic-value">{{ dashboardData?.totalSkuCount || 0 }}</div>
          </div>
          <div class="total-sku-right">
            <div class="statistic-trend">
              <span :class="trendClass">{{ dashboardData?.trends.skuGrowthRate || 0 }}%</span>
              <el-icon><component :is="trendIcon" /></el-icon>
            </div>
          </div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="statistic-card shortage-alert">
        <div class="statistic-header">
          <span class="statistic-icon">🔴</span>
          <span class="statistic-title">{{ t('dashboard.shortageCount') }}</span>
        </div>
        <div class="statistic-value">{{ dashboardData?.shortageCount || 0 }}</div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="statistic-card inventory-warning">
        <div class="statistic-header">
          <span class="statistic-icon">⚠️</span>
          <span class="statistic-title">{{ t('dashboard.warningCount') }}</span>
        </div>
        <div class="statistic-value">{{ dashboardData?.warningCount || 0 }}</div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="statistic-card occupied-value">
        <div class="statistic-header">
          <span class="statistic-icon">💰</span>
          <span class="statistic-title">{{ t('dashboard.occupiedValue') }}</span>
        </div>
        <div class="statistic-value">¥{{ dashboardData?.occupiedValue?.toLocaleString() || '0.00' }}</div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useInventoryStore } from '@/stores/modules/parts/inventory';
import { storeToRefs } from 'pinia';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue';

const { t } = useModuleI18n('parts.inventoryManagement');
const inventoryStore = useInventoryStore();
const { dashboardData } = storeToRefs(inventoryStore);

const trendIcon = computed(() => {
  if (!dashboardData.value) return Minus;
  switch (dashboardData.value.trends.trendDirection) {
    case 'UP': return ArrowUp;
    case 'DOWN': return ArrowDown;
    default: return Minus;
  }
});

const trendClass = computed(() => {
  if (!dashboardData.value) return '';
  return dashboardData.value.trends.trendDirection === 'UP' ? 'is-up' : 'is-down';
});

</script>

<style scoped lang="scss">
.dashboard-container {
  margin-bottom: 20px;
}

.statistic-card {
  border-radius: 8px;
  color: #fff;
  height: 120px;
  
  :deep(.el-card__body) {
    height: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
  }
  
  .statistic-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    
    .statistic-icon {
      font-size: 18px;
    }
    
    .statistic-title {
      font-size: 14px;
      opacity: 0.9;
    }
  }
  
  .statistic-title {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 8px;
  }
  
  .statistic-value {
    font-size: 32px;
    font-weight: bold;
    line-height: 1;
    flex: 1;
    display: flex;
    align-items: center;
  }
  
  .statistic-divider {
    font-size: 12px;
    opacity: 0.6;
    margin: 8px 0;
    letter-spacing: 2px;
  }
  
  .statistic-trend {
    font-size: 12px;
    .is-up {
      color: #67c23a;
    }
    .is-down {
      color: #f56c6c;
    }
  }
  
  // .statistic-description {
  //   font-size: 12px;
  //   opacity: 0.8;
  //   margin-top: 4px;
  // }
}

.total-sku { 
  background-color: #409eff;
  
  .total-sku-content {
    display: flex;
    flex-direction: row;
    height: 100%;
    justify-content: space-between;
    align-items: center;
    
    .total-sku-left {
      flex: 1;
      
      .statistic-title {
        font-size: 14px;
        opacity: 0.8;
        margin-bottom: 8px;
      }
      
      .statistic-value {
        font-size: 32px;
        font-weight: bold;
        line-height: 1;
      }
    }
    
    .total-sku-right {
      margin-left: 16px;
      
      .statistic-trend {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 14px;
        opacity: 0.9;
        
        span {
          margin-bottom: 4px;
        }
        
        .el-icon {
          font-size: 16px;
        }
      }
    }
  }
}

.shortage-alert { 
  background-color: #f56c6c;
}

.inventory-warning { 
  background-color: #e6a23c;
}

.occupied-value { 
  background-color: #67c23a;
}
</style>
