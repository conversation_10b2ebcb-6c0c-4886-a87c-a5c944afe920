
<template>
  <el-dialog
    v-model="visible"
    :title="t('actions.batchReplenishment')"
    width="700px"
    @close="onClose"
  >
    <el-table :data="items" border height="300px">
      <el-table-column :label="t('table.partCode')" prop="partCode" />
      <el-table-column :label="t('table.partName')" prop="partName" />
      <el-table-column :label="t('table.currentStock')" prop="currentStock" />
      <el-table-column :label="t('dialog.adjustQuantity')">
        <template #default="{ row }">
          <el-input-number v-model="row.requestQuantity" :min="1" />
        </template>
      </el-table-column>
    </el-table>
    <el-form class="mt-20" :model="form" label-position="top">
        <el-form-item :label="t('dialog.remark')">
            <el-input v-model="form.remark" type="textarea" :rows="3" />
        </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose">{{ tc('cancel') }}</el-button>
      <el-button type="primary" @click="onConfirm">{{ tc('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { batchCreateReplenishment } from '@/api/modules/parts/inventory';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InventoryItem, ReplenishmentItem } from '@/types/parts/inventory';

const props = defineProps({ modelValue: Boolean, selectedItems: { type: Array as () => InventoryItem[], default: () => [] } });
const emit = defineEmits(['update:modelValue', 'success']);

const { t, tc } = useModuleI18n('parts.inventoryManagement');

const items = ref<(InventoryItem & { requestQuantity: number })[]>([]);
const form = reactive({ remark: '' });

watch(() => props.selectedItems, (newVal) => {
  items.value = newVal.map(item => ({ ...item, requestQuantity: item.safetyStock - item.currentStock > 0 ? item.safetyStock - item.currentStock : 1 }));
});

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const onConfirm = async () => {
  const replenishmentItems: ReplenishmentItem[] = items.value.map(item => ({ inventoryId: item.inventoryId, requestQuantity: item.requestQuantity }));
  await batchCreateReplenishment({ storeId: 1001, inventoryItems: replenishmentItems, applicant: 'admin', remark: form.remark });
  // ElMessage.success(t('inventoryManagement.messages.replenishmentSuccess'));
  emit('success');
  onClose();
};

const onClose = () => {
  visible.value = false;
};
</script>

<style scoped>
.mt-20 {
    margin-top: 20px;
}
</style>
