<template>
  <div class="oem-purchase-page">
    <!-- 统计看板 -->
    <OemDashboard />
    
    <!-- 搜索筛选区 -->
    <el-card class="search-card" shadow="never">
      <div class="search-section">
        <div class="search-input">
          <el-input
            v-model="searchKeyword"
            :placeholder="t('search.placeholder')"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch">
                {{ t('search.searchButton') }}
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="filter-buttons">
          <el-button type="primary" @click="handleBatchApproval">
            {{ t('dashboard.batchApproval') }}
          </el-button>
          <el-button type="success" @click="handleBatchShipment">
            {{ t('dashboard.batchShipment') }}
          </el-button>
        </div>
      </div>
      
      <!-- 快速筛选 -->
      <div class="filter-section">
        <el-radio-group v-model="statusFilter" @change="handleFilterChange">
          <el-radio-button value="">{{ t('filters.all') }}</el-radio-button>
          <el-radio-button value="PENDING_APPROVAL">{{ t('filters.pending') }}</el-radio-button>
          <el-radio-button value="APPROVED">{{ t('filters.approved') }}</el-radio-button>
          <el-radio-button value="REJECTED">{{ t('filters.rejected') }}</el-radio-button>
          <el-radio-button value="PENDING_SHIPMENT">{{ t('filters.pendingShipment') }}</el-radio-button>
          <el-radio-button value="PARTIALLY_SHIPPED">{{ t('filters.partialShipped') }}</el-radio-button>
          <el-radio-button value="SHIPPED_ALL">{{ t('filters.allShipped') }}</el-radio-button>
        </el-radio-group>
      </div>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card" shadow="never">
      <OemOrderTable
        :data="orderList"
        :loading="loading"
        :selected-orders="selectedOrders"
        @approve="handleApprove"
        @ship="handleShip"
        @detail="handleDetail"
        @selection-change="handleSelectionChange"
      />
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 审批弹窗 -->
    <OemApprovalModal
      v-model="approvalDialogVisible"
      :order-id="selectedOrderId"
      @success="handleApprovalSuccess"
    />

    <!-- 发货弹窗 -->
    <OemShipmentModal
      v-model="shipmentDialogVisible"
      :order-id="selectedOrderId"
      @success="handleShipmentSuccess"
    />

    <!-- 批量操作弹窗 -->
    <OemBatchModal
      v-model="batchDialogVisible"
      :batch-type="batchType"
      :selected-orders="selectedOrders"
      @success="handleBatchSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useModuleI18n } from '@/composables/useModuleI18n'
import OemDashboard from './components/OemDashboard.vue'
import OemOrderTable from './components/OemOrderTable.vue'
import OemApprovalModal from './components/OemApprovalModal.vue'
import OemShipmentModal from './components/OemShipmentModal.vue'
import OemBatchModal from './components/OemBatchModal.vue'
import { purchaseOemApi } from '@/api/modules/parts/purchase-oem'
import type { OemPurchaseOrder, OemOrderListParams, OemOrderStatus } from '@/types/parts/purchase-oem'

// 国际化
const { t } = useModuleI18n('parts.purchase.oem')

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref<OemOrderStatus | ''>('')
const orderList = ref<OemPurchaseOrder[]>([])
const selectedOrders = ref<OemPurchaseOrder[]>([])
const approvalDialogVisible = ref(false)
const shipmentDialogVisible = ref(false)
const batchDialogVisible = ref(false)
const selectedOrderId = ref<number>(0)
const batchType = ref<'approval' | 'shipment'>('approval')

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  totalCount: 0,
  totalPages: 0
})

// 获取订单列表
const fetchOrderList = async () => {
  try {
    loading.value = true
    
    const params: OemOrderListParams = {
      page: pagination.currentPage,
      size: pagination.pageSize
    }
    
    if (searchKeyword.value) {
      params.query = searchKeyword.value
    }
    
    if (statusFilter.value) {
      params.status = [statusFilter.value]
    }
    
    const result = await purchaseOemApi.getOrderList(params)
    orderList.value = result.data
    pagination.totalCount = result.pagination.totalCount
    pagination.totalPages = result.pagination.totalPages
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchOrderList()
}

// 筛选变化
const handleFilterChange = () => {
  pagination.currentPage = 1
  fetchOrderList()
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.currentPage = page
  fetchOrderList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchOrderList()
}

// 审核订单
const handleApprove = (order: OemPurchaseOrder) => {
  selectedOrderId.value = order.orderId
  approvalDialogVisible.value = true
}

// 发货订单
const handleShip = (order: OemPurchaseOrder) => {
  selectedOrderId.value = order.orderId
  shipmentDialogVisible.value = true
}

// 查看详情
const handleDetail = (order: OemPurchaseOrder) => {
  router.push(`/parts/purchase/oem/${order.orderId}/detail`)
}

// 选择变化
const handleSelectionChange = (selection: OemPurchaseOrder[]) => {
  selectedOrders.value = selection
}

// 批量审批
const handleBatchApproval = () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要批量审批的订单')
    return
  }
  batchType.value = 'approval'
  batchDialogVisible.value = true
}

// 批量发货
const handleBatchShipment = () => {
  if (selectedOrders.value.length === 0) {
    ElMessage.warning('请选择要批量发货的订单')
    return
  }
  batchType.value = 'shipment'
  batchDialogVisible.value = true
}

// 审批成功回调
const handleApprovalSuccess = () => {
  fetchOrderList()
}

// 发货成功回调
const handleShipmentSuccess = () => {
  fetchOrderList()
}

// 批量操作成功回调
const handleBatchSuccess = () => {
  selectedOrders.value = []
  fetchOrderList()
}

// 初始化
onMounted(() => {
  fetchOrderList()
})
</script>

<style scoped lang="scss">
.oem-purchase-page {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .search-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .filter-buttons {
    display: flex;
    gap: 8px;
  }
  
  .filter-section {
    border-top: 1px solid var(--el-border-color-lighter);
    padding-top: 16px;
  }
  
  .table-card {
    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>