<template>
  <el-table
    :data="data"
    :loading="loading"
    stripe
    border
    style="width: 100%"
    empty-text="暂无数据"
    @selection-change="handleSelectionChange"
  >
    <!-- 选择框 -->
    <el-table-column type="selection" width="55" align="center" />
    
    <!-- 状态 -->
    <el-table-column :label="t('table.orderStatus')" min-width="90" align="center">
      <template #default="{ row }">
        <el-tag :type="getStatusType(row.status)" size="small">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
    </el-table-column>
    
    <!-- 优先级 -->
    <el-table-column label="优先级" min-width="80" align="center">
      <template #default="{ row }">
        <el-tag 
          :type="getPriorityType(row.priority)" 
          size="small"
          effect="plain"
        >
          {{ getPriorityText(row.priority) }}
        </el-tag>
      </template>
    </el-table-column>
    
    <!-- 采购单号 -->
    <el-table-column :label="t('table.orderNo')" prop="orderNo" min-width="140" align="center">
      <template #default="{ row }">
        <el-button link type="primary" @click="$emit('detail', row)">
          {{ row.orderNo }}
        </el-button>
      </template>
    </el-table-column>
    
    <!-- 经销商名称 -->
    <el-table-column :label="t('table.dealerName')" min-width="160" align="center">
      <template #default="{ row }">
        <div class="dealer-info">
          <div class="dealer-name">{{ row.dealerName }}</div>
          <div class="dealer-contact">{{ row.dealerContact }} {{ row.dealerPhone }}</div>
        </div>
      </template>
    </el-table-column>
    
    <!-- 订单金额 -->
    <el-table-column :label="t('table.totalAmount')" min-width="120" align="center">
      <template #default="{ row }">
        ¥{{ formatAmount(row.totalAmount) }}
      </template>
    </el-table-column>
    
    <!-- 申请日期 -->
    <el-table-column :label="t('table.createTime')" min-width="120" align="center">
      <template #default="{ row }">
        {{ formatDate(row.createdAt) }}
      </template>
    </el-table-column>
    
    <!-- 审批人 -->
    <el-table-column label="审批人" min-width="100" align="center">
      <template #default="{ row }">
        {{ row.auditorName || '-' }}
      </template>
    </el-table-column>
    
    <!-- 审批时间 -->
    <el-table-column label="审批时间" min-width="120" align="center">
      <template #default="{ row }">
        {{ row.auditedAt ? formatDate(row.auditedAt) : '-' }}
      </template>
    </el-table-column>
    
    <!-- 操作 -->
    <el-table-column :label="t('table.actions')" min-width="150" align="center" fixed="right">
      <template #default="{ row }">
        <div class="action-buttons">
          <!-- 待审核状态 -->
          <template v-if="row.status === 'PENDING_APPROVAL'">
            <el-button link type="primary" size="small" @click="$emit('approve', row)">
              {{ t('actions.approve') }}
            </el-button>
          </template>
          
          <!-- 待发货状态 -->
          <template v-else-if="row.status === 'PENDING_SHIPMENT' || row.status === 'PARTIALLY_SHIPPED'">
            <el-button link type="success" size="small" @click="$emit('ship', row)">
              {{ t('actions.ship') }}
            </el-button>
          </template>
          
          <!-- 通用查看按钮 -->
          <el-button link type="info" size="small" @click="$emit('detail', row)">
            {{ t('actions.detail') }}
          </el-button>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { OemPurchaseOrder } from '@/types/parts/purchase-oem'

// Props
interface Props {
  data: OemPurchaseOrder[]
  loading?: boolean
  selectedOrders?: OemPurchaseOrder[]
}

defineProps<Props>()

// Emits
const emit = defineEmits<{
  approve: [order: OemPurchaseOrder]
  ship: [order: OemPurchaseOrder]
  detail: [order: OemPurchaseOrder]
  'selection-change': [selection: OemPurchaseOrder[]]
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.oem')

// 获取状态标签类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PENDING_APPROVAL': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'PENDING_SHIPMENT': 'primary',
    'PARTIALLY_SHIPPED': 'warning',
    'SHIPPED_ALL': 'success',
    'CANCELLED': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'PENDING_APPROVAL': '待审核',
    'APPROVED': '已审核',
    'REJECTED': '已驳回',
    'PENDING_SHIPMENT': '待发货',
    'PARTIALLY_SHIPPED': '部分发货',
    'SHIPPED_ALL': '全部发货',
    'CANCELLED': '已取消'
  }
  return textMap[status] || status
}

// 获取优先级标签类型
const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    'LOW': 'info',
    'NORMAL': 'primary',
    'HIGH': 'warning',
    'URGENT': 'danger'
  }
  return typeMap[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    'LOW': '低',
    'NORMAL': '普通',
    'HIGH': '高',
    'URGENT': '紧急'
  }
  return textMap[priority] || priority
}

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 处理选择变化
const handleSelectionChange = (selection: OemPurchaseOrder[]) => {
  // 向父组件传递选择变化事件
  emit('selection-change', selection)
}
</script>

<style scoped lang="scss">
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.dealer-info {
  text-align: left;
  
  .dealer-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 2px;
  }
  
  .dealer-contact {
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

:deep(.el-table) {
  font-size: 14px;
  
  .el-table__cell {
    padding: 8px 0;
  }
  
  .el-button--small {
    padding: 4px 8px;
    font-size: 12px;
  }
}
</style>