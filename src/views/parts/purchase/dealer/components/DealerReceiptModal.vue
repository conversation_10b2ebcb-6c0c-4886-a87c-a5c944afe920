<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('receipt.title') + ': ' + orderId"
    width="900px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="receipt-content">
      <!-- 选择发货单 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <span class="section-title">{{ t('receipt.selectShipment') }}</span>
        </template>
        
        <div v-if="shipmentOptions.length === 0" class="no-shipments">
          <el-empty :description="t('receipt.noShipments')" />
        </div>
        <div v-else class="shipment-list">
          <el-radio-group v-model="selectedShipmentId" class="shipment-radio-group">
            <div 
              v-for="shipment in shipmentOptions" 
              :key="shipment.shipmentId"
              class="shipment-option"
            >
              <el-radio :value="shipment.shipmentId" class="shipment-radio">
                <div class="shipment-info">
                  <div class="shipment-main">
                    <span class="shipment-no">{{ shipment.shipmentNo }}</span>
                    <el-tag 
                      :type="getShipmentStatusType(shipment.status)" 
                      size="small"
                      class="shipment-status"
                    >
                      {{ getShipmentStatusText(shipment.status) }}
                    </el-tag>
                  </div>
                  <div class="shipment-details">
                    <span>{{ t('receipt.trackingNo') }}: {{ shipment.trackingNumber }}</span>
                    <span>{{ t('receipt.carrier') }}: {{ shipment.carrier }}</span>
                    <span>{{ t('receipt.shipDate') }}: {{ formatDate(shipment.shippingDate) }}</span>
                  </div>
                </div>
              </el-radio>
            </div>
          </el-radio-group>
        </div>
      </el-card>

      <!-- 核对入库明细 -->
      <el-card v-if="selectedShipment" shadow="never" class="section-card">
        <template #header>
          <span class="section-title">
            {{ t('receipt.receiptDetails') }} 
            ({{ t('receipt.shipmentNo') }}: {{ selectedShipment.shipmentNo }})
          </span>
        </template>
        
        <el-table :data="receiptItems" border style="width: 100%">
          <el-table-column :label="t('receipt.receiptTable.index')" type="index" width="60" align="center" />
          
          <el-table-column :label="t('receipt.receiptTable.partCode')" prop="partCode" width="120" />
          
          <el-table-column :label="t('receipt.receiptTable.partName')" prop="partName" min-width="150" />
          
          <el-table-column :label="t('receipt.receiptTable.orderedQty')" width="100" align="center">
            <template #default="{ row }">
              {{ row.orderedQuantity }}
            </template>
          </el-table-column>
          
          <el-table-column :label="t('receipt.receiptTable.receivedQty')" width="100" align="center">
            <template #default="{ row }">
              {{ row.receivedQuantity }}
            </template>
          </el-table-column>
          
          <el-table-column :label="t('receipt.receiptTable.shippedQty')" width="100" align="center">
            <template #default="{ row }">
              {{ row.shippedQuantity }}
            </template>
          </el-table-column>
          
          <el-table-column :label="t('receipt.receiptTable.receiptQty')" width="120" align="center">
            <template #default="{ row }">
              <el-input-number
                v-model="row.currentReceiptQuantity"
                :min="0"
                :max="row.shippedQuantity"
                size="small"
                style="width: 100%"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 入库信息 -->
      <el-card v-if="selectedShipment" shadow="never" class="section-card">
        <template #header>
          <span class="section-title">{{ t('receipt.receiptInfo') }}</span>
        </template>
        
        <el-form :model="receiptForm" label-position="top" class="receipt-form">
          <div class="form-grid">
            <el-form-item :label="t('receipt.receiptDate')">
              <el-date-picker
                v-model="receiptForm.receiptDate"
                type="date"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
            
            <el-form-item :label="t('receipt.handler')">
              <el-input v-model="receiptForm.handler" />
            </el-form-item>
          </div>
        </el-form>
      </el-card>

      <!-- 没有选择发货单时的提示 -->
      <el-card v-if="!selectedShipment && shipmentOptions.length > 0" shadow="never" class="section-card">
        <el-empty :description="t('receipt.selectShipmentFirst')" />
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('common.cancel') }}</el-button>
        <el-button 
          type="primary" 
          :loading="submitting" 
          :disabled="!canConfirmReceipt"
          @click="handleConfirm"
        >
          {{ t('receipt.confirmReceipt') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { purchaseDealerApi } from '@/api/modules/parts/purchase-dealer'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { ReceiptRequest, ShipmentOrder, ShipmentOrderItem } from '@/types/parts/purchase-dealer'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Props
interface Props {
  modelValue: boolean
  orderId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  success: []
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer')
const { t: tc } = useI18n()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 发货单相关数据
const shipmentOptions = ref<ShipmentOrder[]>([])
const selectedShipmentId = ref<number | null>(null)

// 选中的发货单
const selectedShipment = computed(() => {
  if (!selectedShipmentId.value) return null
  return shipmentOptions.value.find(s => s.shipmentId === selectedShipmentId.value) || null
})

// 收货表单
const receiptForm = ref({
  receiptDate: new Date().toISOString().split('T')[0],
  handler: '张三'
})

// 收货明细数据
interface ReceiptItem extends ShipmentOrderItem {
  currentReceiptQuantity: number
}

const receiptItems = ref<ReceiptItem[]>([])

// 是否可以确认收货
const canConfirmReceipt = computed(() => {
  if (!selectedShipment.value || receiptItems.value.length === 0) return false
  
  return receiptItems.value.every((item: ReceiptItem) => 
    item.currentReceiptQuantity >= 0 && 
    item.currentReceiptQuantity <= item.shippedQuantity
  )
})

// 监听订单ID变化，加载发货单数据
watch(() => props.orderId, (newOrderId) => {
  if (newOrderId && props.modelValue) {
    loadShipmentData()
  }
})

// 监听选中的发货单变化，更新收货明细
watch(selectedShipment, (newShipment) => {
  if (newShipment) {
    receiptItems.value = newShipment.items.map(item => ({
      ...item,
      currentReceiptQuantity: item.shippedQuantity // 默认全部收货
    }))
  } else {
    receiptItems.value = []
  }
})

// 加载发货单数据
const loadShipmentData = async () => {
  if (!props.orderId) return
  
  try {
    loading.value = true
    
    // 模拟发货单数据
    const mockShipments: ShipmentOrder[] = [
      {
        shipmentId: 1,
        shipmentNo: 'SH20250728005',
        purchaseOrderId: parseInt(props.orderId),
        shippingDate: '2025-07-28',
        carrier: '顺丰速运',
        trackingNumber: 'SF1234567890',
        status: 'DELIVERED',
        items: [
          {
            itemId: 1,
            shipmentOrderId: 1,
            purchaseOrderItemId: 1,
            partId: 'P10003',
            partCode: 'P10003',
            partName: '刹车片前',
            unit: '个',
            orderedQuantity: 20,
            shippedQuantity: 10,
            receivedQuantity: 5
          },
          {
            itemId: 2,
            shipmentOrderId: 1,
            purchaseOrderItemId: 2,
            partId: 'P10008',
            partCode: 'P10008',
            partName: '雨刮片',
            unit: '个',
            orderedQuantity: 30,
            shippedQuantity: 30,
            receivedQuantity: 0
          }
        ]
      },
      {
        shipmentId: 2,
        shipmentNo: 'SH20250729001',
        purchaseOrderId: parseInt(props.orderId),
        shippingDate: '2025-07-29',
        carrier: '圆通快递',
        trackingNumber: 'YT987654321',
        status: 'DELIVERED',
        items: [
          {
            itemId: 3,
            shipmentOrderId: 2,
            purchaseOrderItemId: 3,
            partId: 'P10001',
            partCode: 'P10001',
            partName: '火花塞',
            unit: '个',
            orderedQuantity: 15,
            shippedQuantity: 10,
            receivedQuantity: 0
          }
        ]
      }
    ]
    
    shipmentOptions.value = mockShipments
    
    // TODO: 实际API调用
    // const response = await purchaseDealerApi.getShipmentOrders(props.orderId)
    // shipmentOptions.value = response.shipments
    
    console.log('加载发货单数据:', props.orderId, mockShipments)
  } catch (error) {
    console.error('加载发货单数据失败:', error)
    ElMessage.error('加载发货单数据失败')
  } finally {
    loading.value = false
  }
}

// 确认收货
const handleConfirm = async () => {
  if (!selectedShipment.value || !canConfirmReceipt.value) {
    ElMessage.warning('请检查收货信息')
    return
  }
  
  try {
    submitting.value = true
    
    const receiptData: ReceiptRequest = {
      shipmentId: selectedShipment.value.shipmentId.toString(),
      warehouseId: 1, // TODO: 从实际数据获取
      receiptDate: receiptForm.value.receiptDate,
      handler: receiptForm.value.handler,
      items: receiptItems.value
        .filter((item: ReceiptItem) => item.currentReceiptQuantity > 0)
        .map((item: ReceiptItem) => ({
          partId: item.partId,
          receivedQuantity: item.currentReceiptQuantity,
          locationId: 'DEFAULT' // 使用默认库位
        }))
    }
    
    await purchaseDealerApi.confirmReceipt(props.orderId, receiptData)
    ElMessage.success('收货成功')
    
    emit('success')
    handleClose()
  } catch (error) {
    console.error('收货失败:', error)
    ElMessage.error('收货失败')
  } finally {
    submitting.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  // 重置数据
  selectedShipmentId.value = null
  receiptItems.value = []
  shipmentOptions.value = []
  
  emit('update:modelValue', false)
}

// 获取发货单状态类型
const getShipmentStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'SHIPPED': 'warning',
    'IN_TRANSIT': 'primary',
    'DELIVERED': 'success',
    'RECEIVED': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取发货单状态文本
const getShipmentStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'SHIPPED': '已发货',
    'IN_TRANSIT': '运输中',
    'DELIVERED': '待收货',
    'RECEIVED': '已收货'
  }
  return textMap[status] || status
}

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}
</script>

<style scoped lang="scss">
.receipt-content {
  .section-card {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  
  .no-shipments {
    padding: 40px 0;
  }
  
  .shipment-list {
    .shipment-radio-group {
      width: 100%;
      
      .shipment-option {
        width: 100%;
        margin-bottom: 16px;
        padding: 16px;
        border: 1px solid var(--el-border-color);
        border-radius: 6px;
        transition: all 0.2s;
        
        &:hover {
          border-color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
        }
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .shipment-radio {
          width: 100%;
          
          :deep(.el-radio__label) {
            width: 100%;
            padding-left: 8px;
          }
        }
        
        .shipment-info {
          width: 100%;
          
          .shipment-main {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            
            .shipment-no {
              font-weight: 600;
              font-size: 16px;
              color: var(--el-text-color-primary);
            }
            
            .shipment-status {
              margin-left: 12px;
            }
          }
          
          .shipment-details {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            font-size: 14px;
            color: var(--el-text-color-regular);
            
            span {
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
  
  .receipt-form {
    .form-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 让选中的发货单选项高亮
:deep(.el-radio.is-checked) {
  .shipment-option {
    border-color: var(--el-color-primary) !important;
    background-color: var(--el-color-primary-light-9) !important;
  }
}

@media (max-width: 768px) {
  .shipment-details {
    flex-direction: column !important;
    gap: 8px !important;
  }
  
  .form-grid {
    grid-template-columns: 1fr !important;
  }
}
</style>