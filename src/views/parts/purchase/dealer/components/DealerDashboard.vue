<template>
  <div class="dashboard-container">
    <div class="stats-grid">
      <div class="stat-card pending-approval">
        <div class="stat-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ dashboardData.pendingApprovalCount }}</div>
          <div class="stat-label">{{ t('dashboard.pendingApproval') }}</div>
        </div>
        <div class="stat-trend">
          <span class="trend-indicator up">↗</span>
        </div>
      </div>
      
      <div class="stat-card in-transit">
        <div class="stat-icon">
          <el-icon><Van /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ dashboardData.inTransitCount }}</div>
          <div class="stat-label">{{ t('dashboard.inTransit') }}</div>
        </div>
        <div class="stat-trend">
          <span class="trend-indicator stable">→</span>
        </div>
      </div>
      
      <div class="stat-card pending-receipt">
        <div class="stat-icon">
          <el-icon><Box /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ dashboardData.pendingReceiptCount }}</div>
          <div class="stat-label">{{ t('dashboard.pendingReceipt') }}</div>
        </div>
        <div class="stat-trend">
          <span class="trend-indicator up">↗</span>
        </div>
      </div>
      
      <div class="stat-card monthly-total">
        <div class="stat-icon">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">¥{{ formatAmount(dashboardData.monthlyTotalAmount) }}</div>
          <div class="stat-label">{{ t('dashboard.monthlyTotal') }}</div>
        </div>
        <div class="stat-trend">
          <span class="trend-indicator up">↗</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { purchaseDealerApi } from '@/api/modules/parts/purchase-dealer'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { DealerDashboardStats } from '@/types/parts/purchase-dealer'
import { Box, Clock, Money, Van } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer')

// 响应式数据
const dashboardData = ref<DealerDashboardStats>({
  pendingApprovalCount: 0,
  inTransitCount: 0,
  pendingReceiptCount: 0,
  monthlyTotalAmount: 0
})

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

// 获取统计数据
const fetchDashboardData = async () => {
  try {
    const result = await purchaseDealerApi.getStatistics()
    dashboardData.value = result
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 初始化
onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped lang="scss">
.dashboard-container {
  margin-bottom: 24px;
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    
    .stat-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px;
      display: flex;
      align-items: center;
      gap: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
      }
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      }
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--icon-bg);
        color: var(--icon-color);
        
        .el-icon {
          font-size: 24px;
        }
      }
      
      .stat-content {
        flex: 1;
        
        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: var(--number-color);
          margin-bottom: 4px;
          line-height: 1;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--label-color);
          font-weight: 500;
        }
      }
      
      .stat-trend {
        .trend-indicator {
          font-size: 16px;
          font-weight: bold;
          padding: 4px 8px;
          border-radius: 8px;
          
          &.up {
            color: #10b981;
            background: rgba(16, 185, 129, 0.1);
          }
          
          &.down {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
          }
          
          &.stable {
            color: #6b7280;
            background: rgba(107, 114, 128, 0.1);
          }
        }
      }
      
      // 不同卡片的主题色
      &.pending-approval {
        --card-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --icon-bg: rgba(102, 126, 234, 0.1);
        --icon-color: #667eea;
        --number-color: #667eea;
        --label-color: #6b7280;
      }
      
      &.in-transit {
        --card-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --icon-bg: rgba(240, 147, 251, 0.1);
        --icon-color: #f093fb;
        --number-color: #f093fb;
        --label-color: #6b7280;
      }
      
      &.pending-receipt {
        --card-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --icon-bg: rgba(79, 172, 254, 0.1);
        --icon-color: #4facfe;
        --number-color: #4facfe;
        --label-color: #6b7280;
      }
      
      &.monthly-total {
        --card-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --icon-bg: rgba(67, 233, 123, 0.1);
        --icon-color: #43e97b;
        --number-color: #43e97b;
        --label-color: #6b7280;
      }
    }
  }
}

@media (max-width: 1024px) {
  .dashboard-container {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      
      .stat-card {
        padding: 20px;
        
        .stat-icon {
          width: 40px;
          height: 40px;
          
          .el-icon {
            font-size: 20px;
          }
        }
        
        .stat-content {
          .stat-number {
            font-size: 24px;
          }
          
          .stat-label {
            font-size: 13px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    .stats-grid {
      gap: 12px;
      
      .stat-card {
        padding: 16px;
        gap: 12px;
        
        .stat-content {
          .stat-number {
            font-size: 20px;
          }
          
          .stat-label {
            font-size: 12px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    .stats-grid {
      grid-template-columns: 1fr;
      
      .stat-card {
        text-align: center;
        flex-direction: column;
        gap: 8px;
        
        .stat-trend {
          position: absolute;
          top: 16px;
          right: 16px;
        }
      }
    }
  }
}
</style>