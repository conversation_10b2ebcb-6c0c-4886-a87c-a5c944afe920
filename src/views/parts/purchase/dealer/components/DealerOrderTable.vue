<template>
  <el-table
    :data="data"
    :loading="loading"
    stripe
    border
    style="width: 100%"
    empty-text="暂无数据"
  >
    <!-- 状态 -->
    <el-table-column :label="t('table.status')" min-width="80" align="center">
      <template #default="{ row }">
        <el-tag :type="getStatusType(row.status)" size="small">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
    </el-table-column>
    
    <!-- 采购单号 -->
    <el-table-column :label="t('table.orderNo')" prop="orderNo" min-width="140" align="center" />
    
    <!-- 订单金额 -->
    <el-table-column :label="t('table.totalAmount')" min-width="120" align="center">
      <template #default="{ row }">
        ¥{{ formatAmount(row.totalAmount) }}
      </template>
    </el-table-column>
    
    <!-- 创建日期 -->
    <el-table-column :label="t('table.createTime')" min-width="120" align="center">
      <template #default="{ row }">
        {{ formatDate(row.createTime) }}
      </template>
    </el-table-column>
    
    <!-- 发货日期 -->
    <el-table-column :label="t('table.shipTime')" min-width="120" align="center">
      <template #default="{ row }">
        {{ row.shipTime ? formatDate(row.shipTime) : '-' }}
      </template>
    </el-table-column>
    
    <!-- 操作 -->
    <el-table-column :label="t('table.actions')" min-width="200" align="center" fixed="right">
      <template #default="{ row }">
        <div class="action-buttons">
          <!-- 草稿状态 -->
          <template v-if="row.status === 'draft'">
            <el-button link type="primary" size="small" @click="$emit('edit', row)">
              {{ t('actions.edit') }}
            </el-button>
            <el-button link type="success" size="small" @click="$emit('submit', row)">
              {{ t('actions.submit') }}
            </el-button>
            <el-button link type="danger" size="small" @click="$emit('delete', row)">
              {{ t('actions.delete') }}
            </el-button>
          </template>
          
          <!-- 已提交状态 -->
          <template v-else-if="row.status === 'submitted'">
            <el-button link type="primary" size="small" @click="$emit('detail', row)">
              {{ t('actions.detail') }}
            </el-button>
            <el-button link type="warning" size="small" @click="$emit('cancel', row)">
              {{ t('actions.cancel') }}
            </el-button>
          </template>
          
          <!-- 已发货/部分发货状态 -->
          <template v-else-if="row.status === 'shipped' || row.status === 'partiallyShipped'">
            <el-button link type="primary" size="small" @click="$emit('detail', row)">
              {{ t('actions.detail') }}
            </el-button>
            <el-button link type="success" size="small" @click="$emit('receipt', row)">
              {{ t('actions.receipt') }}
            </el-button>
          </template>
          
          <!-- 其他状态只能查看 -->
          <template v-else>
            <el-button link type="primary" size="small" @click="$emit('detail', row)">
              {{ t('actions.detail') }}
            </el-button>
          </template>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { PurchaseOrder } from '@/types/parts/purchase-dealer';

// Props
interface Props {
  data: PurchaseOrder[]
  loading?: boolean
}

defineProps<Props>()

// Emits
defineEmits<{
  edit: [order: PurchaseOrder]
  detail: [order: PurchaseOrder]
  submit: [order: PurchaseOrder]
  cancel: [order: PurchaseOrder]
  delete: [order: PurchaseOrder]
  receipt: [order: PurchaseOrder]
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer')

// 获取状态标签类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'draft': 'info',
    'submitted': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'shipped': 'primary',
    'partiallyShipped': 'warning',
    'partiallyReceived': 'warning',
    'received': 'success',
    'cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  // 使用国际化翻译
  return t(`status.${status}`) || status
}

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}
</script>

<style scoped lang="scss">
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

:deep(.el-table) {
  font-size: 14px;
  
  .el-table__cell {
    padding: 8px 0;
  }
  
  .el-button--small {
    padding: 4px 8px;
    font-size: 12px;
  }
}
</style>