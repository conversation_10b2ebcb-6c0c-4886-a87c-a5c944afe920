<template>
  <div v-loading="loading" class="order-form">
    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules" 
      label-position="top"
    >
      <!-- 驳回原因 -->
      <el-card v-if="props.orderData?.status === 'rejected' && props.orderData?.rejectionReason" shadow="never" class="reject-reason-card">
        <div class="reject-reason-content">
          <div class="reject-reason-title">{{ t('form.rejectReason') }}</div>
          <div class="reject-reason-text">{{ props.orderData.rejectionReason }}</div>
        </div>
      </el-card>

      <!-- 订单信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <span class="section-title">{{ t('form.orderInfo') }}</span>
        </template>
        
        <div class="order-info-grid">
          <!-- 第一行：门店名称和期望到货日期 -->
          <div class="form-row">
            <el-form-item :label="t('form.storeName')" class="form-item-half">
              <el-input 
                :value="getCurrentStoreName()" 
                readonly 
                style="width: 100%"
              />
            </el-form-item>
            
            <el-form-item :label="t('form.expectedDeliveryDate')" class="form-item-half">
              <el-date-picker
                v-model="formData.expectedDeliveryDate"
                type="date"
                :placeholder="t('form.expectedDeliveryDatePlaceholder')"
                style="width: 100%"
                :disabled-date="disabledDate"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :readonly="props.readonly"
              />
            </el-form-item>
          </div>
          
          <!-- 第二行：申请备注 -->
          <div class="form-row form-row-full">
            <el-form-item :label="t('form.remark')" class="form-item-full">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                :rows="2"
                :placeholder="t('form.remarkPlaceholder')"
                maxlength="500"
                show-word-limit
                :readonly="props.readonly"
              />
            </el-form-item>
          </div>
        </div>
      </el-card>

      <!-- 添加零件按钮 -->
      <div v-if="!props.readonly" class="add-part-section">
        <el-button type="primary" size="large" @click="handleAddOriginalPart" class="add-part-btn">
          <el-icon><Plus /></el-icon>
          {{ t('form.addPart') }}
        </el-button>
      </div>

      <!-- 分开显示：草稿状态 -->
      <template v-if="shouldShowSeparateTables">
        <!-- 原厂零件申请明细 -->
        <el-card shadow="never" class="section-card">
          <template #header>
            <span class="section-title">▼ {{ t('form.originalPartDetails') }}</span>
          </template>
          
          <el-table :data="originalPartItems" border style="width: 100%" :empty-text="t('form.addOriginalPartsPlaceholder')">
            <el-table-column :label="t('form.partTable.index')" type="index" width="60" align="center" />
            
            <el-table-column :label="t('form.partTable.partCode')" prop="partCode" width="120" />
            
            <el-table-column :label="t('form.partTable.partName')" prop="partName" min-width="150" />
            
            <el-table-column :label="t('form.partTable.currentStock')" width="100" align="center">
              <template #default="{ row }">
                {{ row.currentStock }}
              </template>
            </el-table-column>
            
            <el-table-column :label="t('form.partTable.safetyStock')" width="100" align="center">
              <template #default="{ row }">
                {{ row.safetyStock }}
              </template>
            </el-table-column>
            
            <el-table-column :label="t('form.partTable.quantity')" width="120" align="center">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.quantity"
                  :min="1"
                  size="small"
                  style="width: 100%"
                  :readonly="props.readonly"
                  @change="updateOriginalPartAmount($index)"
                />
              </template>
            </el-table-column>
            
            <el-table-column v-if="!props.readonly" :label="t('form.partTable.actions')" width="80" align="center">
              <template #default="{ $index }">
                <el-button link type="danger" size="small" @click="handleRemoveOriginalPart($index)">
                  {{ tc('common.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 非原厂零件申请明细 -->
        <el-card shadow="never" class="section-card">
          <template #header>
            <div class="section-header">
              <span class="section-title">▼ {{ t('form.nonOriginalPartDetails') }}</span>
            </div>
          </template>
          
          <el-table :data="nonOriginalPartItems" border style="width: 100%" :empty-text="t('form.addNonOriginalPartsPlaceholder')">
            <el-table-column :label="t('form.partTable.index')" type="index" width="60" align="center" />
            
            <el-table-column :label="t('form.partTable.partCode')" prop="partCode" width="120" />
            
            <el-table-column :label="t('form.partTable.partName')" prop="partName" min-width="150" />
            
            <el-table-column :label="t('form.partTable.currentStock')" width="100" align="center">
              <template #default="{ row }">
                {{ row.currentStock }}
              </template>
            </el-table-column>
            
            <el-table-column :label="t('form.partTable.safetyStock')" width="100" align="center">
              <template #default="{ row }">
                {{ row.safetyStock }}
              </template>
            </el-table-column>
            
            <el-table-column :label="t('form.partTable.quantity')" width="120" align="center">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.quantity"
                  :min="1"
                  size="small"
                  style="width: 100%"
                  :readonly="props.readonly"
                  @change="updateNonOriginalPartAmount($index)"
                />
              </template>
            </el-table-column>
            
            <el-table-column v-if="!props.readonly" :label="t('form.partTable.actions')" width="80" align="center">
              <template #default="{ $index }">
                <el-button link type="danger" size="small" @click="handleRemoveNonOriginalPart($index)">
                  {{ tc('common.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </template>

      <!-- 统一显示：非草稿状态 -->
      <template v-else>
        <!-- 统一零件申请明细 -->
        <el-card shadow="never" class="section-card">
          <template #header>
            <span class="section-title">▼ {{ t('form.unifiedPartDetails') }}</span>
          </template>
          
          <el-table :data="unifiedPartItems" border style="width: 100%" :empty-text="t('common.noData')">
            <el-table-column :label="t('form.partTable.index')" type="index" width="60" align="center" />
            
            <el-table-column :label="t('form.partTable.partCode')" prop="partCode" width="120" />
            
            <el-table-column :label="t('form.partTable.partName')" prop="partName" min-width="150" />
            
            <el-table-column :label="t('form.partTable.partType')" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.partType === 'ORIGINAL' ? 'primary' : 'success'" size="small">
                  {{ row.partType === 'ORIGINAL' ? '原厂' : '非原厂' }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column :label="t('form.partTable.currentStock')" width="100" align="center">
              <template #default="{ row }">
                {{ row.currentStock }}
              </template>
            </el-table-column>
            
            <el-table-column :label="t('form.partTable.safetyStock')" width="100" align="center">
              <template #default="{ row }">
                {{ row.safetyStock }}
              </template>
            </el-table-column>
            
            <el-table-column :label="t('form.partTable.quantity')" width="120" align="center">
              <template #default="{ row }">
                {{ row.quantity }}
              </template>
            </el-table-column>
            
            <el-table-column :label="t('form.partTable.unitPrice')" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatAmount(row.unitPrice) }}
              </template>
            </el-table-column>
            
            <el-table-column :label="t('form.partTable.amount')" width="120" align="center">
              <template #default="{ row }">
                ¥{{ formatAmount(row.amount) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </template>
        
        <!-- 总计信息 -->
      <el-card shadow="never" class="section-card" v-if="totalQuantity > 0">
        <div class="total-info">
          <!-- 分开显示的总计 -->
          <template v-if="shouldShowSeparateTables">
            <div class="total-row">
              <span>{{ t('form.originalTotal') }}：{{ originalTotalQuantity }} {{ tc('common.unit.piece') }}，¥{{ formatAmount(originalTotalAmount) }}</span>
            </div>
            <div class="total-row">
              <span>{{ t('form.nonOriginalTotal') }}：{{ nonOriginalTotalQuantity }} {{ tc('common.unit.piece') }}，¥{{ formatAmount(nonOriginalTotalAmount) }}</span>
            </div>
            <div class="total-row total-grand">
              <span>{{ t('form.grandTotal') }}：{{ totalQuantity }} {{ tc('common.unit.piece') }}，¥{{ formatAmount(totalAmount) }}</span>
            </div>
          </template>
          
          <!-- 统一显示的总计 -->
          <template v-else>
            <div class="total-row total-grand">
              <span>{{ t('form.grandTotal') }}：{{ unifiedTotalQuantity }} {{ tc('common.unit.piece') }}，¥{{ formatAmount(unifiedTotalAmount) }}</span>
            </div>
          </template>
        </div>
      </el-card>

      <!-- 提示信息 -->
      <el-alert
        :title="getSplitOrderTip()"
        type="info"
        show-icon
        :closable="false"
        class="tip-alert"
      />

      <!-- 操作按钮 -->
      <div v-if="!props.readonly" class="form-actions">
        <el-button @click="$emit('cancel')">
          {{ t('form.cancel') }}
        </el-button>
        <el-button @click="handleSaveDraft">
          {{ t('form.saveDraft') }}
        </el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ t('form.submitApproval') }}
        </el-button>
      </div>
    </el-form>

    <!-- 配件选择弹窗 -->
    <DealerPartSelector
      v-model="partSelectorVisible"
      :selected-parts="formData.items"
      @confirm="handlePartSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { PurchaseOrder, PurchaseOrderForm, PurchaseOrderFormItem } from '@/types/parts/purchase-dealer'
import { Plus } from '@element-plus/icons-vue'
import { ElForm, ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import DealerPartSelector from './DealerPartSelector.vue'

// Props
interface Props {
  loading?: boolean
  orderData?: PurchaseOrder | null
  editMode?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  orderData: null,
  editMode: false,
  readonly: false
})

// Emits
const emit = defineEmits<{
  submit: [formData: PurchaseOrderForm]
  'save-draft': [formData: PurchaseOrderForm]
  cancel: []
}>()

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer')
const { t: tc } = useI18n()

// 响应式数据
const formRef = ref<InstanceType<typeof ElForm>>()
const partSelectorVisible = ref(false)
const warehouseOptions = ref<Array<{ id: number; name: string; type: string }>>([])

// 表单数据
const formData = reactive<PurchaseOrderForm>({
  dealerStoreInfo: undefined,
  warehouseId: 0,
  expectedDeliveryDate: '',
  remarks: '',
  items: []
})

// 表单验证规则
const formRules = {
  warehouseId: [
    { required: true, message: t('validation.warehouseRequired'), trigger: 'change' }
  ]
}

// 计算属性 - 分离原厂和非原厂零件
const originalPartItems = computed(() => {
  return formData.items.filter(item => item.partType === 'ORIGINAL')
})

const nonOriginalPartItems = computed(() => {
  return formData.items.filter(item => item.partType === 'NON_ORIGINAL')
})

// 计算原厂零件总计
const originalTotalQuantity = computed(() => {
  return originalPartItems.value.reduce((sum, item) => sum + item.quantity, 0)
})

const originalTotalAmount = computed(() => {
  return originalPartItems.value.reduce((sum, item) => sum + item.amount, 0)
})

// 计算非原厂零件总计
const nonOriginalTotalQuantity = computed(() => {
  return nonOriginalPartItems.value.reduce((sum, item) => sum + item.quantity, 0)
})

const nonOriginalTotalAmount = computed(() => {
  return nonOriginalPartItems.value.reduce((sum, item) => sum + item.amount, 0)
})

// 总计
const totalQuantity = computed(() => {
  return originalTotalQuantity.value + nonOriginalTotalQuantity.value
})

const totalAmount = computed(() => {
  return originalTotalAmount.value + nonOriginalTotalAmount.value
})

// 判断是否需要分开显示两个表格
const shouldShowSeparateTables = computed(() => {
  // 草稿状态总是分开显示，便于编辑
  if (props.orderData?.status === 'draft') {
    return true
  }
  
  // 非草稿状态，已提交的订单应该只包含一种类型的零件
  return false
})

// 统一的零件列表（用于非草稿状态显示）
const unifiedPartItems = computed(() => {
  return formData.items
})

// 计算统一列表的总计
const unifiedTotalQuantity = computed(() => {
  return unifiedPartItems.value.reduce((sum, item) => sum + item.quantity, 0)
})

const unifiedTotalAmount = computed(() => {
  return unifiedPartItems.value.reduce((sum, item) => sum + item.amount, 0)
})

// 监听订单数据变化
watch(() => props.orderData, (newData) => {
  if (newData) {
    // 将订单数据转换为表单数据
    formData.dealerStoreInfo = newData.dealerStoreInfo
    formData.warehouseId = newData.warehouseId
    formData.expectedDeliveryDate = newData.expectedDeliveryDate || ''
    formData.remarks = newData.remark || ''
    
    // 转换订单明细
    formData.items = newData.items.map(item => ({
      id: item.id,
      partId: item.partId,
      partCode: item.partCode,
      partName: item.partName,
      partType: item.partType,
      brand: item.brand || '',
      specification: item.specification || '',
      unit: item.unit,
      purchasePrice: item.unitPrice,
      unitPrice: item.unitPrice,
      orderQuantity: item.quantity,
      quantity: item.quantity,
      amount: item.amount,
      currentStock: item.currentStock || 0,
      safetyStock: item.safetyStock || 0
    })) as PurchaseOrderFormItem[]
  }
}, { immediate: true })

// 格式化金额
const formatAmount = (amount: number): string => {
  return amount.toLocaleString()
}

// 更新原厂零件金额
const updateOriginalPartAmount = (index: number) => {
  const item = originalPartItems.value[index]
  if (item) {
    item.amount = item.unitPrice * item.quantity
  }
}

// 更新非原厂零件金额
const updateNonOriginalPartAmount = (index: number) => {
  const item = nonOriginalPartItems.value[index]
  if (item) {
    item.amount = item.unitPrice * item.quantity
  }
}

// 添加原厂配件
const handleAddOriginalPart = () => {
  partSelectorVisible.value = true
  // 这里可以设置一个标志来区分是添加原厂还是非原厂
}

// 添加非原厂配件
const handleAddNonOriginalPart = () => {
  partSelectorVisible.value = true
  // 这里可以设置一个标志来区分是添加原厂还是非原厂
}

// 配件选择确认
const handlePartSelected = (selectedParts: PurchaseOrderFormItem[]) => {
  // 过滤掉已存在的配件
  const existingPartIds = formData.items.map(item => item.partId)
  const newParts = selectedParts.filter(part => !existingPartIds.includes(part.partId))
  
  formData.items.push(...newParts)
}

// 删除原厂配件
const handleRemoveOriginalPart = (index: number) => {
  const item = originalPartItems.value[index]
  if (item) {
    const globalIndex = formData.items.findIndex(globalItem => globalItem.id === item.id)
    if (globalIndex > -1) {
      formData.items.splice(globalIndex, 1)
    }
  }
}

// 删除非原厂配件
const handleRemoveNonOriginalPart = (index: number) => {
  const item = nonOriginalPartItems.value[index]
  if (item) {
    const globalIndex = formData.items.findIndex(globalItem => globalItem.id === item.id)
    if (globalIndex > -1) {
      formData.items.splice(globalIndex, 1)
    }
  }
}

// 保存草稿
const handleSaveDraft = async () => {
  try {
    await formRef.value?.validate()
    
    if (formData.items.length === 0) {
      ElMessage.error(t('validation.itemsRequired'))
      return
    }
    
    emit('save-draft', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 获取订单拆分提示
const getSplitOrderTip = (): string => {
  const hasOriginal = originalPartItems.value.length > 0
  const hasNonOriginal = nonOriginalPartItems.value.length > 0
  
  if (hasOriginal && hasNonOriginal) {
    return t('form.tips.splitOrderTip')
  } else if (hasOriginal) {
    return t('form.tips.originalOrderTip')
  } else if (hasNonOriginal) {
    return t('form.tips.nonOriginalOrderTip')
  } else {
    return t('form.tips.addPartsTip')
  }
}

// 提交审核
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    if (formData.items.length === 0) {
      ElMessage.error(t('validation.itemsRequired'))
      return
    }
    
    // 如果同时包含两种类型的零件，给出确认提示
    const hasOriginal = originalPartItems.value.length > 0
    const hasNonOriginal = nonOriginalPartItems.value.length > 0
    
    if (hasOriginal && hasNonOriginal) {
      const confirmed = await ElMessageBox.confirm(
        t('form.tips.splitOrderConfirm'),
        t('form.confirmSubmitTitle'),
        {
          confirmButtonText: t('form.continueSubmit'),
          cancelButtonText: tc('common.cancel'),
          type: 'warning'
        }
      ).catch(() => false)
      
      if (!confirmed) return
    }
    
    emit('submit', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 获取当前门店名称
const getCurrentStoreName = (): string => {
  if (formData.dealerStoreInfo) {
    return `${formData.dealerStoreInfo.dealerName}-${formData.dealerStoreInfo.storeName}`
  }
  return 'A经销商-北京总店' // 默认值，实际应该从用户信息或API获取
}

// 禁用日期规则（不能选择过去的日期）
const disabledDate = (time: Date): boolean => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用昨天及之前的日期
}

// 加载仓库选项（设置默认仓库）
const loadWarehouseOptions = async () => {
  try {
    // 根据线框图，仓库信息不需要用户选择，使用默认主仓库
    formData.warehouseId = 3001 // 默认主仓库ID
  } catch (error) {
    console.error('设置默认仓库失败:', error)
  }
}

// 加载当前经销商门店信息
const loadDealerStoreInfo = async () => {
  try {
    // TODO: 从API获取当前用户的门店信息
    // 临时使用模拟数据
    formData.dealerStoreInfo = {
      dealerId: 'DEALER001',
      dealerName: 'A经销商',
      storeName: '北京总店',
      storeCode: 'BJ001',
      region: '华北区',
      contactPerson: '张经理',
      contactPhone: '138-xxxx-xxxx'
    }
  } catch (error) {
    console.error('加载门店信息失败:', error)
  }
}

// 初始化
onMounted(() => {
  loadWarehouseOptions()
  loadDealerStoreInfo()
})
</script>

<style scoped lang="scss">
.order-form {
  // 全局紧凑模式样式
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
  
  :deep(.el-input) {
    font-size: 13px;
  }
  
  :deep(.el-button) {
    padding: 6px 15px;
    font-size: 13px;
  }
  .section-card {
    margin-bottom: 16px;
    border-radius: 6px;
    border: 1px solid var(--el-border-color-lighter);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    :deep(.el-card__header) {
      padding: 12px 20px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
      border-radius: 6px 6px 0 0;
    }
    
    :deep(.el-card__body) {
      padding: 16px 20px;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .section-title {
      font-weight: 600;
      color: var(--el-text-color-primary);
      font-size: 15px;
      position: relative;
      padding-left: 10px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 14px;
        background: var(--el-color-primary);
        border-radius: 1px;
      }
    }
  }
  
  .add-part-section {
    text-align: right;
    margin-bottom: 12px;
    padding: 8px 0;
    
    .add-part-btn {
      padding: 8px 20px;
      font-size: 14px;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }
    }
  }
  
  .order-info-grid {
    .form-row {
      display: flex;
      gap: 24px;
      margin-bottom: 16px;
      align-items: flex-start;
      
      &.form-row-full {
        margin-bottom: 0;
      }
      
      .form-item-half {
        flex: 1;
        margin-bottom: 0;
        min-width: 0; // 防止内容溢出
        
        :deep(.el-form-item__label) {
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
          font-size: 13px;
          line-height: 1.2;
        }
        
        :deep(.el-input__wrapper) {
          border-radius: 4px;
          transition: all 0.2s;
          min-height: 32px;
          
          &:hover {
            box-shadow: 0 0 0 1px var(--el-color-primary) inset;
          }
        }
        
        :deep(.el-date-editor.el-input) {
          width: 100%;
          
          .el-input__wrapper {
            border-radius: 4px;
            min-height: 32px;
          }
        }
      }
      
      .form-item-full {
        flex: 1;
        margin-bottom: 0;
        
        :deep(.el-form-item__label) {
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
          font-size: 13px;
          line-height: 1.2;
        }
        
        :deep(.el-textarea__inner) {
          border-radius: 4px;
          transition: all 0.2s;
          min-height: 60px;
          padding: 8px 12px;
          line-height: 1.4;
          
          &:hover {
            border-color: var(--el-color-primary);
          }
          
          &:focus {
            border-color: var(--el-color-primary);
            box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
          }
        }
      }
      
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 12px;
        margin-bottom: 12px;
        
        .form-item-half {
          flex: none;
          width: 100%;
        }
      }
    }
  }
  
  .total-info {
    padding: 16px;
    
    .total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
      padding: 8px 0;
      font-size: 14px;
      color: var(--el-text-color-regular);
      
      &.total-grand {
        border-top: 1px solid var(--el-border-color-lighter);
        margin-top: 8px;
        padding-top: 12px;
        font-weight: 600;
        font-size: 16px;
      color: var(--el-text-color-primary);
      }
    }
  }
  
  .tip-alert {
    margin: 20px 0;
  }

  .reject-reason-card {
    margin-bottom: 16px;
    background-color: #fff0f6; /* 粉红色背景 */
    border: 1px solid #ffadd2; /* 粉红色边框 */
    border-radius: 4px;
    
    .reject-reason-content {
      padding: 12px 16px;
      
      .reject-reason-title {
        font-size: 14px;
        font-weight: 600;
        color: #eb2f96; /* 粉红色标题 */
        margin-bottom: 6px;
      }
      
      .reject-reason-text {
        font-size: 13px;
        color: #c41d7f; /* 深粉红色文本 */
        line-height: 1.5;
      }
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

:deep(.el-table) {
  .el-input-number {
    .el-input__inner {
      text-align: center;
    }
  }
}
</style>