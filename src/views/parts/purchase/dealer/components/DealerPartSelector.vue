<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('selector.title')"
    width="1200px"
    :before-close="handleClose"
    class="part-selector-dialog"
  >
    <div v-loading="loading" class="part-selector">
      <!-- 顶部筛选区域 -->
      <div class="filter-section">
        <div class="section-title">{{ t('selector.filterTitle') || '筛选条件' }}</div>
        <div class="filter-content">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">{{ t('selector.partType') }}</span>
              <el-radio-group v-model="selectedPartType" @change="handlePartTypeChange" size="small">
                <el-radio value="ORIGINAL">{{ t('selector.originalPart') }}</el-radio>
                <el-radio value="NON_ORIGINAL">{{ t('selector.nonOriginalPart') }}</el-radio>
              </el-radio-group>
            </div>
          </div>
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">{{ t('selector.partCode') }}</span>
              <el-input
                v-model="partCodeKeyword"
                :placeholder="t('selector.searchPlaceholder')"
                style="width: 200px"
                size="small"
                clearable
                @keyup.enter="handleSearch"
              />
            </div>
            <div class="filter-item">
              <span class="filter-label">{{ t('selector.stockStatus') }}</span>
              <el-select
                v-model="selectedStockStatus"
                :placeholder="tc('common.pleaseSelect')"
                style="width: 180px"
                size="small"
                clearable
                @change="handleSearch"
              >
                <el-option value="normal" :label="t('selector.stockStatusNormal')" />
                <el-option value="warning" :label="t('selector.stockStatusWarning')" />
              </el-select>
            </div>
            <div class="filter-item">
              <el-button type="primary" size="small" @click="handleSearch">{{ tc('common.search') }}</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 主体区域 -->
      <div class="main-content">
        <!-- 左侧零件种类 -->
        <div class="category-panel section-card">
          <div class="section-title">{{ t('selector.partCategory') }}</div>
          <div class="category-content">
            <div 
              v-for="category in partCategories"
              :key="category.value"
              :class="['category-item', { 'active': selectedCategory === category.value }]"
              @click="handleCategoryChange(category.value)"
            >
              {{ category.label }}
              <span v-if="category.count" class="count">({{ category.count }})</span>
            </div>
          </div>
        </div>

        <!-- 右侧配件列表 -->
        <div class="parts-panel section-card">
          <div class="section-title">{{ t('selector.selectedParts') }}</div>
          <div class="parts-content">
            <el-table
              ref="tableRef"
              :data="filteredPartList"
              height="400"
              @selection-change="handleSelectionChange"
            >
        <el-table-column type="selection" width="55" :selectable="isSelectable" />
        
            <el-table-column :label="t('form.partTable.partCode')" prop="partCode" width="120" />
        
            <el-table-column :label="t('form.partTable.partName')" prop="partName" width="200" />
        
            <el-table-column :label="t('selector.partTable.stockStatus')" width="120" align="center">
              <template #default="{ row }">
                <el-tag 
                  :type="getStockStatusType(row)" 
                  size="small"
                >
                  {{ getStockStatusText(row) }}
                </el-tag>
              </template>
            </el-table-column>
        
            <el-table-column :label="t('selector.partTable.currentStock')" prop="currentStock" width="100" align="center" />
        
            <el-table-column :label="t('selector.partTable.availableStock')" prop="availableStock" width="100" align="center" />
        
            <el-table-column :label="t('selector.partTable.occupiedStock')" width="100" align="center">
          <template #default="{ row }">
                {{ row.currentStock - row.availableStock }}
          </template>
        </el-table-column>
        
            <el-table-column :label="t('selector.partTable.safetyStock')" prop="safetyStock" width="100" align="center" />
            
            <el-table-column :label="tc('common.operations')" width="85" align="center">
          <template #default="{ row }">
                <el-button
                  link
                  type="primary"
                  size="small"
                  :disabled="!isSelectable(row)"
                  @click="handleAddSinglePart(row)"
                >
                  {{ tc('common.add') }}
                </el-button>
          </template>
        </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 已选配件区域 -->
      <div class="selected-parts-section">
        <!-- 原厂零件 -->
        <div class="selected-part-group section-card">
          <div class="section-title">{{ t('selector.selectedOriginalParts') }}</div>
          <div class="selected-content">
            <el-table
              :data="selectedOriginalParts"
              max-height="200"
            >
            <el-table-column :label="tc('common.index')" type="index" width="100" align="center" />
            <el-table-column :label="t('form.partTable.partCode')" prop="partCode" width="180" />
            <el-table-column :label="t('form.partTable.partName')" prop="partName" width="300" />
            <el-table-column :label="t('selector.partTable.suggestedQuantity')" width="180" align="center">
              <template #default="{ row }">
                {{ getSuggestedQuantity(row) }}
              </template>
            </el-table-column>
            <el-table-column :label="t('selector.partTable.orderQuantity')" width="220" align="center">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.quantity"
                  :min="1"
                  size="small"
                  style="width: 100%"
                  @change="updatePartAmount(row)"
                />
              </template>
            </el-table-column>
            <el-table-column :label="tc('common.operations')" width="180" align="center">
          <template #default="{ row }">
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="handleRemovePart(row, 'original')"
                >
                  {{ tc('common.delete') }}
                </el-button>
          </template>
        </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 非原厂零件 -->
        <div class="selected-part-group section-card">
          <div class="section-title">{{ t('selector.selectedNonOriginalParts') }}</div>
          <div class="selected-content">
            <el-table
              :data="selectedNonOriginalParts"
              max-height="200"
            >
            <el-table-column :label="tc('common.index')" type="index" width="100" align="center" />
            <el-table-column :label="t('form.partTable.partCode')" prop="partCode" width="180" />
            <el-table-column :label="t('form.partTable.partName')" prop="partName" width="300" />
            <el-table-column :label="t('selector.partTable.suggestedQuantity')" width="180" align="center">
              <template #default="{ row }">
                {{ getSuggestedQuantity(row) }}
              </template>
            </el-table-column>
            <el-table-column :label="t('selector.partTable.orderQuantity')" width="220" align="center">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.quantity"
                  :min="1"
                  size="small"
                  style="width: 100%"
                  @change="updatePartAmount(row)"
                />
              </template>
            </el-table-column>
            <el-table-column :label="tc('common.operations')" width="180" align="center">
          <template #default="{ row }">
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="handleRemovePart(row, 'non-original')"
                >
                  {{ tc('common.delete') }}
                </el-button>
          </template>
        </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 提示信息 -->
        <div class="tip-section">
          <el-alert
            :title="t('selector.splitOrderWarning')"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
          <el-button @click="handleClose">{{ tc('common.cancel') }}</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :disabled="totalSelectedParts === 0"
        >
          {{ t('selector.confirmAdd') }}
          </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { purchaseDealerApi } from '@/api/modules/parts/purchase-dealer'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { PartForSelection, PartType, PurchaseOrderFormItem } from '@/types/parts/purchase-dealer'
import { ElMessage, ElTable } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer')
const { t: tc } = useI18n()

// Props
interface Props {
  modelValue: boolean
  selectedParts?: PurchaseOrderFormItem[]
}

const props = withDefaults(defineProps<Props>(), {
  selectedParts: () => []
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [parts: PurchaseOrderFormItem[]]
}>()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const partCodeKeyword = ref('')
const selectedStockStatus = ref('')
const selectedPartType = ref<PartType>('ORIGINAL')
const selectedCategory = ref('all')
const partList = ref<PartForSelection[]>([])
const selectedOriginalParts = ref<PurchaseOrderFormItem[]>([])
const selectedNonOriginalParts = ref<PurchaseOrderFormItem[]>([])
const tableRef = ref<InstanceType<typeof ElTable>>()

// 零件种类选项
const partCategories = ref([
  { label: tc('common.all'), value: 'all', count: 0 },
  { label: t('selector.category.engine'), value: 'engine', count: 0 },
  { label: t('selector.category.body'), value: 'body', count: 0 },
  { label: t('selector.category.chassis'), value: 'chassis', count: 0 },
  { label: t('selector.category.electrical'), value: 'electrical', count: 0 }
])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 根据当前筛选条件过滤配件列表
const filteredPartList = computed(() => {
  let filtered = partList.value.filter(part => part.partType === selectedPartType.value)
  
  // 按零件号筛选
  if (partCodeKeyword.value) {
    filtered = filtered.filter(part => 
      part.partCode.toLowerCase().includes(partCodeKeyword.value.toLowerCase()) ||
      part.partName.toLowerCase().includes(partCodeKeyword.value.toLowerCase())
    )
  }
  
  // 按库存状态筛选
  if (selectedStockStatus.value) {
    filtered = filtered.filter(part => {
      if (selectedStockStatus.value === 'normal') {
        return part.currentStock > part.safetyStock
      } else if (selectedStockStatus.value === 'warning') {
        return part.currentStock <= part.safetyStock
      }
      return true
    })
  }
  
  // 按种类筛选（这里简化处理，实际可根据配件名称或其他字段判断种类）
  if (selectedCategory.value !== 'all') {
    // TODO: 根据实际业务逻辑实现种类筛选
  }
  
  return filtered
})

// 已选配件总数
const totalSelectedParts = computed(() => {
  return selectedOriginalParts.value.length + selectedNonOriginalParts.value.length
})

// 已选择的配件ID（避免重复选择）
const selectedPartIds = computed(() => {
  const originalIds = selectedOriginalParts.value.map(part => part.partId)
  const nonOriginalIds = selectedNonOriginalParts.value.map(part => part.partId)
  const propsIds = props.selectedParts?.map(part => part.partId) || []
  return [...originalIds, ...nonOriginalIds, ...propsIds]
})

// 监听弹窗打开
watch(dialogVisible, (visible) => {
  if (visible) {
    initializeComponent()
  }
})

// 监听零件类型变化
watch(selectedPartType, () => {
  updateCategoryCounts()
})

// 组件挂载
onMounted(() => {
  loadPartList()
})

// 初始化组件
const initializeComponent = () => {
  selectedCategory.value = 'all'
  partCodeKeyword.value = ''
  selectedStockStatus.value = ''
  loadPartList()
}

// 获取库存状态类型
const getStockStatusType = (part: PartForSelection) => {
  if (part.currentStock === 0) return 'danger'
  if (part.currentStock <= part.safetyStock) return 'warning'
  return 'success'
}

// 获取库存状态文本
const getStockStatusText = (part: PartForSelection) => {
  if (part.currentStock === 0) return t('selector.status.SHORTAGE')
  if (part.currentStock <= part.safetyStock) return t('selector.status.WARNING')
  return t('selector.status.NORMAL')
}

// 判断行是否可选择
const isSelectable = (row: PartForSelection) => {
  return !selectedPartIds.value.includes(row.partId)
}

// 计算建议数量（安全库存 - 当前库存）
const getSuggestedQuantity = (part: PartForSelection | PurchaseOrderFormItem) => {
  const suggested = Math.max(0, part.safetyStock - part.currentStock)
  return suggested || 1
}

// 加载配件列表
const loadPartList = async () => {
  try {
    loading.value = true
    partList.value = await purchaseDealerApi.getPartsForSelection({
      keyword: partCodeKeyword.value,
      partType: selectedPartType.value
    })
    updateCategoryCounts()
  } catch (error) {
    console.error('加载配件列表失败:', error)
    ElMessage.error(t('messages.loadPartsFailed'))
  } finally {
    loading.value = false
  }
}

// 更新种类统计
const updateCategoryCounts = () => {
  // 简化处理，实际应根据业务逻辑统计
  partCategories.value[0].count = filteredPartList.value.length
}

// 处理零件类型切换
const handlePartTypeChange = () => {
  loadPartList()
}

// 处理种类切换
const handleCategoryChange = (category: string) => {
  selectedCategory.value = category
}

// 搜索
const handleSearch = () => {
  loadPartList()
}

// 选择变化（暂时保留，但实际使用单个添加）
const handleSelectionChange = (selection: PartForSelection[]) => {
  // 这个方法现在主要用于兼容，实际使用handleAddSinglePart
}

// 添加单个配件
const handleAddSinglePart = (part: PartForSelection) => {
  if (!isSelectable(part)) return
  
  const formItem: PurchaseOrderFormItem = {
    id: `${Date.now()}_${part.partId}`,
    partId: part.partId,
    partCode: part.partCode,
    partName: part.partName,
    partType: part.partType,
    brand: part.brand || '',
    specification: part.specification || '',
    unit: part.unit,
    unitPrice: part.purchasePrice,
    quantity: getSuggestedQuantity(part),
    amount: part.purchasePrice * getSuggestedQuantity(part),
    currentStock: part.currentStock,
    safetyStock: part.safetyStock
  }
  
  // 添加到对应的列表
  if (part.partType === 'ORIGINAL') {
    selectedOriginalParts.value.push(formItem)
  } else {
    selectedNonOriginalParts.value.push(formItem)
  }
}

// 删除配件
const handleRemovePart = (part: PurchaseOrderFormItem, type: string) => {
  if (type === 'original') {
    const index = selectedOriginalParts.value.findIndex(p => p.partId === part.partId)
    if (index > -1) {
      selectedOriginalParts.value.splice(index, 1)
    }
  } else {
    const index = selectedNonOriginalParts.value.findIndex(p => p.partId === part.partId)
    if (index > -1) {
      selectedNonOriginalParts.value.splice(index, 1)
    }
  }
}

// 更新配件金额
const updatePartAmount = (part: PurchaseOrderFormItem) => {
  part.amount = part.unitPrice * part.quantity
}

// 确认选择
const handleConfirm = () => {
  const allSelected = [...selectedOriginalParts.value, ...selectedNonOriginalParts.value]
  emit('confirm', allSelected)
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  // 清空已选配件
  selectedOriginalParts.value = []
  selectedNonOriginalParts.value = []
  emit('update:modelValue', false)
}
</script>

<style scoped lang="scss">
// 模态框全局样式
:deep(.part-selector-dialog) {
  margin-top: 5vh !important;
  .el-dialog__header {
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .el-dialog__body {
    padding: 16px 20px;
  }
  
  .el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    position: relative;
    padding-left: 12px;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 16px;
      background: var(--el-color-primary);
      border-radius: 2px;
    }
  }
}

.part-selector {
  // 统一的卡片样式
  .section-card {
    margin-bottom: 16px;
    border-radius: 6px;
    border: 1px solid var(--el-border-color-lighter);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      padding: 12px 16px;
      background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
      border-bottom: 1px solid var(--el-border-color-lighter);
      border-radius: 6px 6px 0 0;
      font-weight: 600;
      color: var(--el-text-color-primary);
      font-size: 15px;
      position: relative;
      padding-left: 26px;
      margin: 0;
      
      &::before {
        content: '';
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 14px;
        background: var(--el-color-primary);
        border-radius: 1px;
      }
    }
  }
  .filter-section {
    margin-bottom: 16px;
    border-radius: 6px;
    border: 1px solid var(--el-border-color-lighter);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    
    .section-title {
      padding: 12px 16px;
      background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
      border-bottom: 1px solid var(--el-border-color-lighter);
      border-radius: 6px 6px 0 0;
      font-weight: 600;
      color: var(--el-text-color-primary);
      font-size: 15px;
      position: relative;
      padding-left: 26px;
      
      &::before {
        content: '';
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 14px;
        background: var(--el-color-primary);
        border-radius: 1px;
      }
    }

    .filter-content {
      padding: 12px 16px;
    }

    .filter-row {
      display: flex;
      gap: 20px;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .filter-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .filter-label {
          font-weight: 500;
          color: var(--el-text-color-primary);
          white-space: nowrap;
          font-size: 13px;
          min-width: 60px;
        }
      }
    }
  }

  .main-content {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .category-panel {
      width: 200px;

      .category-content {
        padding: 0;
        
        .category-item {
          padding: 8px 16px;
          cursor: pointer;
          border-bottom: 1px solid var(--el-border-color-lighter);
          display: flex;
          justify-content: space-between;
          align-items: center;
          transition: all 0.2s;
          font-size: 13px;

          &:hover {
            background: var(--el-bg-color-page);
          }

          &.active {
            background: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
            font-weight: 500;
          }
          
          &:last-child {
            border-bottom: none;
          }

          .count {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
      }
    }

    .parts-panel {
      flex: 1;
      
      .parts-content {
        padding: 0;
        
        :deep(.el-table) {
          border: none;
          border-radius: 0;
          
          .el-table__header {
            th {
              background: var(--el-bg-color-page);
              border-bottom: 1px solid var(--el-border-color-lighter);
              font-size: 13px;
              padding: 8px 0;
            }
          }
          
          .el-table__body {
            tr {
              td {
                font-size: 13px;
                padding: 6px 0;
              }
            }
          }
        }
      }
    }
  }

  .selected-parts-section {
    margin-top: 16px;

    .selected-part-group {
      margin-bottom: 16px;
      
      .selected-content {
        padding: 0;
        
        :deep(.el-table) {
          border: none;
          border-radius: 0;
          
          .el-table__header {
            th {
              background: var(--el-bg-color-page);
              border-bottom: 1px solid var(--el-border-color-lighter);
              font-size: 13px;
              padding: 8px 0;
            }
          }
          
          .el-table__body {
            tr {
              td {
                font-size: 13px;
                padding: 6px 0;
              }
            }
          }
        }
      }
    }

    .tip-section {
      margin-top: 12px;
      
      :deep(.el-alert) {
        border-radius: 4px;
        font-size: 13px;
        padding: 8px 12px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
  
  .el-button {
    padding: 6px 15px;
    font-size: 13px;
    border-radius: 4px;
  }
}
  
:deep(.el-table) {
  .el-table__row {
    &.current-row {
      background-color: var(--el-table-row-hover-bg-color);
    }
  }
}
</style>