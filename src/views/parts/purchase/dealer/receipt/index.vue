<template>
  <div class="receipt-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button 
          type="text" 
          @click="handleBack"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          {{ t('backToList') }}
        </el-button>
        <div class="page-title">
          <h2>{{ t('title') }}</h2>
          <span class="order-no">订单号：{{ orderInfo?.orderNo }}</span>
        </div>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          @click="handleExportReport"
          :loading="exportLoading"
        >
          <el-icon><Download /></el-icon>
          {{ t('exportReport') }}
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" v-loading="loading">
      <!-- 叫料单基本信息 -->
      <ReceiptHeader 
        v-if="orderInfo"
        :order-info="orderInfo"
        class="content-section"
      />

      <!-- 收货进度 -->
      <ReceiptProgress 
        v-if="statistics"
        :statistics="statistics"
        :progress="orderInfo?.receiptProgress"
        class="content-section"
      />

      <!-- 收货单列表 -->
      <ReceiptList 
        v-if="receiptOrders.length > 0"
        :receipt-orders="receiptOrders"
        :loading="listLoading"
        @view-detail="handleViewDetail"
        @confirm-receipt="handleConfirmReceipt"
        @refresh="fetchReceiptOrderList"
        class="content-section"
      />

      <!-- 空状态 -->
      <div v-else-if="!loading && !listLoading" class="empty-state">
        <el-empty :description="t('list.noPendingOrders')" />
      </div>
    </div>

    <!-- 收货详情弹窗 -->
    <ReceiptDetailModal
      v-model="detailModalVisible"
      :receipt-order-id="selectedReceiptOrderId"
      @refresh="fetchReceiptOrderList"
    />

    <!-- 收货确认弹窗 -->
    <ReceiptConfirmModal
      v-model="confirmModalVisible"
      :receipt-order-id="selectedReceiptOrderId"
      @confirm="handleReceiptConfirmed"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowLeft, Download } from '@element-plus/icons-vue';
import { receiptApi } from '@/api/modules/parts/receipt';
import ReceiptHeader from './components/ReceiptHeader.vue';
import ReceiptProgress from './components/ReceiptProgress.vue';
import ReceiptList from './components/ReceiptList.vue';
import ReceiptDetailModal from './components/ReceiptDetailModal.vue';
import ReceiptConfirmModal from './components/ReceiptConfirmModal.vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { 
  PurchaseOrderExtended, 
  ReceiptStatistics, 
  ReceiptOrder 
} from '@/types/parts/purchase-dealer';

// 路由相关
const route = useRoute();
const router = useRouter();
const purchaseOrderId = route.params.id as string;

// 国际化
const { t } = useModuleI18n('parts.purchase.receipt');
const { t: tc } = useModuleI18n('common');

// 响应式数据
const loading = ref(false);
const listLoading = ref(false);
const exportLoading = ref(false);

// 主要数据
const orderInfo = ref<PurchaseOrderExtended | null>(null);
const statistics = ref<ReceiptStatistics | null>(null);
const receiptOrders = ref<ReceiptOrder[]>([]);

// 弹窗控制
const detailModalVisible = ref(false);
const confirmModalVisible = ref(false);
const selectedReceiptOrderId = ref('');

// 计算属性
const pageTitle = computed(() => {
  return orderInfo.value ? `${t('title')} - ${orderInfo.value.orderNo}` : t('title');
});

/**
 * 返回上一页
 */
const handleBack = () => {
  router.push('/parts/purchase/dealer');
};

/**
 * 获取收货概览数据
 */
const fetchReceiptOverview = async () => {
  try {
    loading.value = true;
    console.log('🔍 正在获取收货概览，订单ID:', purchaseOrderId);
    const response = await receiptApi.getReceiptOverview(purchaseOrderId);
    console.log('📊 收货概览数据:', response);
    orderInfo.value = response.orderInfo;
    statistics.value = response.statistics;
  } catch (error) {
    console.error('获取收货概览失败:', error);
    ElMessage.error(t('messages.loadReceiptOverviewFailed'));
  } finally {
    loading.value = false;
  }
};

/**
 * 获取收货单列表
 */
const fetchReceiptOrderList = async () => {
  try {
    listLoading.value = true;
    console.log('📦 正在获取收货单列表，订单ID:', purchaseOrderId);
    const response = await receiptApi.getReceiptOrderList(purchaseOrderId);
    console.log('📋 收货单列表数据:', response);
    receiptOrders.value = response.list;
  } catch (error) {
    console.error('获取收货单列表失败:', error);
    ElMessage.error(t('messages.loadReceiptOrderListFailed'));
  } finally {
    listLoading.value = false;
  }
};

/**
 * 查看收货详情
 */
const handleViewDetail = (receiptOrderId: string) => {
  selectedReceiptOrderId.value = receiptOrderId;
  detailModalVisible.value = true;
};

/**
 * 确认收货
 */
const handleConfirmReceipt = (receiptOrderId: string) => {
  selectedReceiptOrderId.value = receiptOrderId;
  confirmModalVisible.value = true;
};

/**
 * 收货确认完成
 */
const handleReceiptConfirmed = () => {
  confirmModalVisible.value = false;
  ElMessage.success(t('messages.receiptConfirmSuccess'));
  // 刷新数据
  fetchReceiptOverview();
  fetchReceiptOrderList();
};

/**
 * 导出收货报表
 */
const handleExportReport = async () => {
  try {
    const result = await ElMessageBox.confirm(
      t('messages.confirmExport'),
      t('exportReport'),
      {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'info'
      }
    );

    exportLoading.value = true;
    const response = await receiptApi.exportReceiptReport(purchaseOrderId, {
      format: 'excel'
    });
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = response.downloadUrl;
    link.download = response.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    ElMessage.success(t('messages.exportSuccess'));
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出报表失败:', error);
      ElMessage.error(t('messages.exportFailed'));
    }
  } finally {
    exportLoading.value = false;
  }
};

// 生命周期
onMounted(() => {
  fetchReceiptOverview();
  fetchReceiptOrderList();
});

// 设置页面标题
document.title = pageTitle.value;
</script>

<style lang="scss" scoped>
.receipt-management {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 16px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-btn {
        color: #606266;
        font-size: 14px;
        padding: 8px 0;

        &:hover {
          color: #409eff;
        }
      }

      .page-title {
        h2 {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: #303133;
        }

        .order-no {
          font-size: 14px;
          color: #909399;
          margin-left: 8px;
        }
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .main-content {
    flex: 1;
    padding: 0 24px;
    overflow-y: auto;

    .content-section {
      margin-bottom: 16px;
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .receipt-management {
    .page-header {
      flex-direction: column;
      gap: 12px;
      padding: 12px 16px;

      .header-left {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .page-title {
          h2 {
            font-size: 18px;
          }
        }
      }

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .main-content {
      padding: 0 16px;

      .content-section {
        padding: 16px;
      }
    }
  }
}
</style>