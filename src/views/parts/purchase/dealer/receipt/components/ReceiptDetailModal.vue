<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('receiptDetail')"
    width="80%"
    :before-close="handleClose"
    append-to-body
    destroy-on-close
  >
    <div class="receipt-detail" v-loading="loading">
      <!-- 收货单基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">{{ t('detail.receiptOrderInfo') }}</h4>
        <div class="info-grid">
          <div class="info-row">
            <div class="info-item">
              <label>{{ t('list.receiptNo') }}：</label>
              <span class="value">{{ receiptOrder?.receiptNo }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('detail.relatedPurchaseOrder') }}：</label>
              <span class="value">{{ receiptOrder?.purchaseOrderNo }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('list.shipmentNo') }}：</label>
              <span class="value">{{ receiptOrder?.shipmentNo }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <label>{{ t('detail.supplier') }}：</label>
              <span class="value">{{ receiptOrder?.supplierName }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('list.expectedReceiptDate') }}：</label>
              <span class="value">{{ formatDate(receiptOrder?.expectedDate) }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('list.actualReceiptDate') }}：</label>
              <span class="value">{{ formatDateTime(receiptOrder?.actualReceiptDate) }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <label>{{ t('detail.receiptStatus') }}：</label>
              <el-tag 
                :type="getStatusTagType(receiptOrder?.status)" 
                size="small"
              >
                {{ getStatusText(receiptOrder?.status) }}
              </el-tag>
            </div>
            <div class="info-item" v-if="receiptOrder?.handler">
              <label>{{ t('list.signer') }}：</label>
              <span class="value">{{ receiptOrder?.handler }}</span>
            </div>
            <div class="info-item">
              <label>{{ t('detail.createTime') }}：</label>
              <span class="value">{{ formatDateTime(receiptOrder?.createTime) }}</span>
            </div>
          </div>

          <div class="info-row" v-if="receiptOrder?.remark">
            <div class="info-item full-width">
              <label>{{ t('list.remark') }}：</label>
              <span class="value remark">{{ receiptOrder?.remark }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 零件明细表格 -->
      <div class="detail-section">
        <h4 class="section-title">{{ t('detail.partsDetail') }}</h4>
        <el-table 
          :data="receiptOrder?.items || []" 
          border
          stripe
          class="detail-table"
        >
          <el-table-column 
            type="index" 
            :label="t('detail.index')" 
            width="60" 
            align="center"
          />
          
          <el-table-column 
            prop="partCode" 
            :label="t('detail.partCode')" 
            min-width="120"
          />
          
          <el-table-column 
            prop="partName" 
            :label="t('detail.partName')" 
            min-width="150"
            show-overflow-tooltip
          />
          
          <el-table-column 
            prop="unit" 
            :label="t('detail.unit')" 
            width="80" 
            align="center"
          />
          
          <el-table-column 
            prop="orderedQuantity" 
            :label="t('detail.orderedQuantity')" 
            width="100" 
            align="center"
          />
          
          <el-table-column 
            prop="shippedQuantity" 
            :label="t('detail.shippedQuantity')" 
            width="100" 
            align="center"
          />
          
          <el-table-column 
            prop="actualReceiptQuantity" 
            :label="t('detail.actualReceiptQuantity')" 
            width="100" 
            align="center"
          >
            <template #default="{ row }">
              <span 
                :class="{
                  'quantity-normal': row.actualReceiptQuantity === row.shippedQuantity,
                  'quantity-shortage': row.actualReceiptQuantity < row.shippedQuantity,
                  'quantity-excess': row.actualReceiptQuantity > row.shippedQuantity
                }"
              >
                {{ row.actualReceiptQuantity }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="status" 
            :label="t('detail.receiptStatus')" 
            width="100" 
            align="center"
          >
            <template #default="{ row }">
              <el-tag 
                :type="getStatusTagType(row.status)" 
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="locationName" 
            :label="t('detail.storageLocation')" 
            width="120"
            show-overflow-tooltip
          />
          
          <el-table-column 
            prop="batchNo" 
            :label="t('detail.batchNo')" 
            width="120"
            show-overflow-tooltip
          />
          
          <el-table-column 
            :label="t('detail.abnormalInfo')" 
            width="150"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div v-if="row.abnormalType">
                <div class="abnormal-type">{{ getAbnormalTypeText(row.abnormalType) }}</div>
                <div class="abnormal-reason" v-if="row.abnormalReason">
                  {{ row.abnormalReason }}
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="unitPrice" 
            :label="t('detail.unitPrice')" 
            width="100" 
            align="right"
          >
            <template #default="{ row }">
              {{ formatCurrency(row.unitPrice) }}
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="amount" 
            :label="t('detail.amount')" 
            width="120" 
            align="right"
          >
            <template #default="{ row }">
              {{ formatCurrency(row.amount) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 统计汇总 -->
      <div class="detail-section">
        <div class="summary-info">
          <div class="summary-item">
            <label>{{ t('detail.totalItems') }}：</label>
            <span>{{ receiptOrder?.totalItems || 0 }}</span>
          </div>
          <div class="summary-item">
            <label>{{ t('detail.receivedItems') }}：</label>
            <span class="received">{{ receiptOrder?.receivedItems || 0 }}</span>
          </div>
          <div class="summary-item">
            <label>{{ t('detail.abnormalItems') }}：</label>
            <span class="abnormal">{{ receiptOrder?.abnormalItems || 0 }}</span>
          </div>
          <div class="summary-item">
            <label>{{ t('detail.totalAmount') }}：</label>
            <span class="amount">{{ formatCurrency(calculateTotalAmount()) }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ tc('close') }}</el-button>
        <el-button 
          v-if="!receiptOrder?.actualReceiptDate"
          type="primary" 
          @click="handleConfirmReceipt"
        >
          {{ t('list.confirmReceipt') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { receiptApi } from '@/api/modules/parts/receipt';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { 
  ReceiptOrder, 
  ReceiptStatus, 
  ReceiptAbnormalType 
} from '@/types/parts/purchase-dealer';

// Props
interface Props {
  modelValue: boolean;
  receiptOrderId: string;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  refresh: [];
  confirmReceipt: [receiptOrderId: string];
}>();

// 国际化
const { t } = useModuleI18n('parts.purchase.receipt');
const { t: tc } = useModuleI18n('common');

// 响应式数据
const loading = ref(false);
const receiptOrder = ref<ReceiptOrder | null>(null);

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

/**
 * 获取收货单详情
 */
const fetchReceiptOrderDetail = async () => {
  if (!props.receiptOrderId) return;
  
  try {
    loading.value = true;
    receiptOrder.value = await receiptApi.getReceiptOrderDetail(props.receiptOrderId);
  } catch (error) {
    console.error('获取收货单详情失败:', error);
    ElMessage.error(t('messages.loadReceiptOrderDetailFailed'));
  } finally {
    loading.value = false;
  }
};

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status?: ReceiptStatus): string => {
  if (!status) return 'info';
  
  const typeMap = {
    'NORMAL': 'success',
    'SHORTAGE': 'warning',
    'DAMAGE': 'danger',
    'REJECTED': 'danger'
  };
  return typeMap[status] || 'info';
};

/**
 * 获取状态文本
 */
const getStatusText = (status?: ReceiptStatus): string => {
  if (!status) return '-';
  return t(`status.${status}`) || status;
};

/**
 * 获取异常类型文本
 */
const getAbnormalTypeText = (abnormalType?: ReceiptAbnormalType): string => {
  if (!abnormalType) return '-';
  return t(`abnormalType.${abnormalType}`) || abnormalType;
};

/**
 * 格式化日期
 */
const formatDate = (dateStr?: string): string => {
  if (!dateStr) return '-';
  
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
  } catch (error) {
    return dateStr;
  }
};

/**
 * 格式化日期时间
 */
const formatDateTime = (dateStr?: string): string => {
  if (!dateStr) return '-';
  
  try {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
  } catch (error) {
    return dateStr;
  }
};

/**
 * 格式化金额
 */
const formatCurrency = (amount?: number): string => {
  if (typeof amount !== 'number') return '-';
  
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

/**
 * 计算总金额
 */
const calculateTotalAmount = (): number => {
  if (!receiptOrder.value?.items) return 0;
  
  return receiptOrder.value.items.reduce((total, item) => {
    return total + (item.amount || 0);
  }, 0);
};

/**
 * 关闭弹窗
 */
const handleClose = () => {
  dialogVisible.value = false;
  receiptOrder.value = null;
};

/**
 * 确认收货
 */
const handleConfirmReceipt = () => {
  if (!receiptOrder.value) return;
  
  emit('confirmReceipt', receiptOrder.value.id);
  handleClose();
};

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible && props.receiptOrderId) {
      fetchReceiptOrderDetail();
    }
  },
  { immediate: true }
);

// 监听收货单ID变化
watch(
  () => props.receiptOrderId,
  (newId) => {
    if (newId && props.modelValue) {
      fetchReceiptOrderDetail();
    }
  }
);
</script>

<style lang="scss" scoped>
.receipt-detail {
  .detail-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }

    .info-grid {
      .info-row {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          display: flex;
          align-items: center;

          &.full-width {
            grid-column: 1 / -1;
          }

          label {
            font-size: 14px;
            color: #606266;
            margin-right: 8px;
            white-space: nowrap;
            min-width: 100px;
          }

          .value {
            font-size: 14px;
            color: #303133;
            flex: 1;

            &.remark {
              background: #f5f7fa;
              padding: 8px 12px;
              border-radius: 4px;
              border: 1px solid #dcdfe6;
              word-break: break-all;
            }
          }
        }
      }
    }

    .detail-table {
      width: 100%;

      :deep(.quantity-normal) {
        color: #67c23a;
        font-weight: 600;
      }

      :deep(.quantity-shortage) {
        color: #f56c6c;
        font-weight: 600;
      }

      :deep(.quantity-excess) {
        color: #e6a23c;
        font-weight: 600;
      }

      .abnormal-type {
        font-size: 12px;
        color: #f56c6c;
        font-weight: 600;
      }

      .abnormal-reason {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }

    .summary-info {
      display: flex;
      justify-content: space-around;
      align-items: center;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 16px;

      .summary-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        label {
          font-size: 14px;
          color: #606266;
        }

        span {
          font-size: 18px;
          font-weight: 600;
          color: #303133;

          &.received {
            color: #67c23a;
          }

          &.abnormal {
            color: #f56c6c;
          }

          &.amount {
            color: #409eff;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .receipt-detail {
    .detail-section {
      .info-grid {
        .info-row {
          grid-template-columns: 1fr;
          gap: 12px;

          .info-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;

            label {
              min-width: auto;
              margin-right: 0;
            }

            .value {
              width: 100%;
            }
          }
        }
      }

      .summary-info {
        flex-direction: column;
        gap: 12px;

        .summary-item {
          flex-direction: row;
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }
}
</style>