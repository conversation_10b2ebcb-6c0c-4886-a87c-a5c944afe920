<template>
  <div class="receipt-list">
    <div class="header-title">
      <h3>{{ t('receiptOrderList') }}</h3>
      <div class="header-actions">
        <el-button 
          type="text" 
          @click="handleRefresh"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          {{ t('list.refresh') }}
        </el-button>
      </div>
    </div>

    <!-- 分类标签 -->
    <div class="category-tabs">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane 
          :label="t('pendingReceipt')" 
          name="pending"
          :badge="pendingOrders.length.toString()"
        >
          <template #label>
            <span class="tab-label">
              {{ t('pendingReceipt') }}
              <el-badge 
                v-if="pendingOrders.length > 0" 
                :value="pendingOrders.length" 
                class="tab-badge"
              />
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane 
          :label="t('completedReceipt')" 
          name="completed"
        >
          <template #label>
            <span class="tab-label">
              {{ t('completedReceipt') }}
              <el-badge 
                v-if="completedOrders.length > 0" 
                :value="completedOrders.length" 
                class="tab-badge" 
                type="success"
              />
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 收货单卡片列表 -->
    <div class="orders-container" v-loading="loading">
      <div 
        v-if="currentOrders.length === 0 && !loading" 
        class="empty-state"
      >
        <el-empty :description="getEmptyDescription()" />
      </div>

      <div 
        v-else
        class="orders-list"
      >
        <div 
          v-for="order in currentOrders" 
          :key="order.id"
          class="order-card"
          :class="{ 'abnormal': hasAbnormalItems(order) }"
        >
          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="header-left">
              <h4 class="order-title">{{ order.receiptNo }}</h4>
              <el-tag 
                :type="getStatusTagType(order.status)" 
                size="small"
              >
                {{ getStatusText(order.status) }}
              </el-tag>
            </div>
            <div class="header-right">
              <span class="supplier-name">{{ order.supplierName }}</span>
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <div class="content-row">
              <div class="content-item">
                <label>{{ t('list.shipmentNo') }}：</label>
                <span>{{ order.shipmentNo }}</span>
              </div>
              <div class="content-item">
                <label>{{ t('list.expectedReceiptDate') }}：</label>
                <span>{{ formatDate(order.expectedDate) }}</span>
              </div>
              <div class="content-item">
                <label>{{ t('list.goodsDetail') }}：</label>
                <span>{{ order.totalItems }} 项</span>
              </div>
            </div>

            <div class="content-row">
              <div class="content-item">
                <label>{{ t('list.received') }}：</label>
                <span class="received-count">{{ order.receivedItems }} 项</span>
              </div>
              <div class="content-item" v-if="order.abnormalItems > 0">
                <label>{{ t('list.abnormal') }}：</label>
                <span class="abnormal-count">{{ order.abnormalItems }} 项</span>
              </div>
              <div class="content-item" v-if="order.actualReceiptDate">
                <label>{{ t('list.actualReceiptDate') }}：</label>
                <span>{{ formatDateTime(order.actualReceiptDate) }}</span>
              </div>
            </div>

            <div v-if="order.handler" class="content-row">
              <div class="content-item">
                <label>{{ t('list.signer') }}：</label>
                <span>{{ order.handler }}</span>
              </div>
            </div>

            <div v-if="order.remark" class="content-row">
              <div class="content-item full-width">
                <label>{{ t('list.remark') }}：</label>
                <span class="remark-text">{{ order.remark }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="card-actions">
            <el-button 
              type="text" 
              @click="handleViewDetail(order.id)"
            >
              <el-icon><View /></el-icon>
              {{ t('list.viewDetail') }}
            </el-button>
            
            <el-button 
              v-if="!order.actualReceiptDate"
              type="primary" 
              size="small"
              @click="handleConfirmReceipt(order.id)"
            >
              <el-icon><Check /></el-icon>
              {{ t('list.confirmReceipt') }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Refresh, View, Check } from '@element-plus/icons-vue';
import type { ReceiptOrder, ReceiptStatus } from '@/types/parts/purchase-dealer';
import { useModuleI18n } from '@/composables/useModuleI18n';

// Props
interface Props {
  receiptOrders: ReceiptOrder[];
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// Emits
const emit = defineEmits<{
  viewDetail: [receiptOrderId: string];
  confirmReceipt: [receiptOrderId: string];
  refresh: [];
}>();

// 国际化
const { t } = useModuleI18n('parts.purchase.receipt');

// 响应式数据
const activeTab = ref<'pending' | 'completed'>('pending');

// 计算属性
const pendingOrders = computed(() => {
  return props.receiptOrders.filter(order => !order.actualReceiptDate);
});

const completedOrders = computed(() => {
  return props.receiptOrders.filter(order => !!order.actualReceiptDate);
});

const currentOrders = computed(() => {
  return activeTab.value === 'pending' ? pendingOrders.value : completedOrders.value;
});

/**
 * 判断是否有异常明细
 */
const hasAbnormalItems = (order: ReceiptOrder): boolean => {
  return order.abnormalItems > 0;
};

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: ReceiptStatus): string => {
  const typeMap = {
    'NORMAL': 'success',
    'SHORTAGE': 'warning',
    'DAMAGE': 'danger',
    'REJECTED': 'danger',
    'PENDING': 'info',
    'URGENT': 'warning'
  };
  return typeMap[status] || 'info';
};

/**
 * 获取状态文本
 */
const getStatusText = (status: ReceiptStatus): string => {
  return t(`status.${status}`) || status;
};

/**
 * 格式化日期
 */
const formatDate = (dateStr?: string): string => {
  if (!dateStr) return '-';
  
  try {
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
  } catch (error) {
    return dateStr;
  }
};

/**
 * 格式化日期时间
 */
const formatDateTime = (dateStr?: string): string => {
  if (!dateStr) return '-';
  
  try {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
  } catch (error) {
    return dateStr;
  }
};

/**
 * 获取空状态描述
 */
const getEmptyDescription = (): string => {
  return activeTab.value === 'pending' 
    ? t('list.noPendingOrders') 
    : t('list.noCompletedOrders');
};

/**
 * 标签页切换处理
 */
const handleTabClick = () => {
  // 可以在这里添加统计或其他逻辑
};

/**
 * 刷新数据
 */
const handleRefresh = () => {
  emit('refresh');
};

/**
 * 查看详情
 */
const handleViewDetail = (receiptOrderId: string) => {
  emit('viewDetail', receiptOrderId);
};

/**
 * 确认收货
 */
const handleConfirmReceipt = (receiptOrderId: string) => {
  emit('confirmReceipt', receiptOrderId);
};
</script>

<style lang="scss" scoped>
.receipt-list {
  .header-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .category-tabs {
    margin-bottom: 20px;

    .tab-label {
      display: flex;
      align-items: center;
      gap: 8px;

      .tab-badge {
        :deep(.el-badge__content) {
          position: static;
          transform: none;
          padding: 0 6px;
          height: 16px;
          line-height: 16px;
          font-size: 11px;
          min-width: 16px;
        }
      }
    }
  }

  .orders-container {
    min-height: 300px;

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
    }

    .orders-list {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .order-card {
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        padding: 20px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        &.abnormal {
          border-left: 4px solid #f56c6c;
          background: #fef0f0;
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .header-left {
            display: flex;
            align-items: center;
            gap: 12px;

            .order-title {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: #303133;
            }
          }

          .header-right {
            .supplier-name {
              font-size: 14px;
              color: #606266;
              font-weight: 500;
            }
          }
        }

        .card-content {
          margin-bottom: 16px;

          .content-row {
            display: flex;
            flex-wrap: wrap;
            gap: 24px;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .content-item {
              display: flex;
              align-items: center;
              min-width: 200px;

              &.full-width {
                width: 100%;
                min-width: auto;
              }

              label {
                font-size: 14px;
                color: #606266;
                margin-right: 8px;
                white-space: nowrap;
              }

              span {
                font-size: 14px;
                color: #303133;

                &.received-count {
                  color: #67c23a;
                  font-weight: 600;
                }

                &.abnormal-count {
                  color: #f56c6c;
                  font-weight: 600;
                }

                &.remark-text {
                  background: #f5f7fa;
                  padding: 4px 8px;
                  border-radius: 4px;
                  border: 1px solid #dcdfe6;
                  word-break: break-all;
                }
              }
            }
          }
        }

        .card-actions {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          padding-top: 16px;
          border-top: 1px solid #f0f2f5;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .receipt-list {
    .header-title {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .orders-container {
      .orders-list {
        .order-card {
          padding: 16px;

          .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .header-left {
              width: 100%;
              justify-content: space-between;
            }

            .header-right {
              width: 100%;
            }
          }

          .card-content {
            .content-row {
              flex-direction: column;
              gap: 8px;

              .content-item {
                min-width: auto;
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;

                label {
                  margin-right: 0;
                }
              }
            }
          }

          .card-actions {
            flex-direction: column;
            gap: 8px;

            .el-button {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
</style>