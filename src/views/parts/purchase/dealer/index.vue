<template>
  <div class="dealer-purchase-page">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="page-title">
        <h2>{{ t('title') }}</h2>
        <p class="page-subtitle">{{ t('description') || '管理和跟踪您的零件采购订单' }}</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="large" @click="handleCreate" class="create-btn">
          <el-icon><Plus /></el-icon>
          {{ t('actions.create') }}
        </el-button>
      </div>
    </div>

    <!-- 统计看板 -->
    <DealerDashboard />
    
    <!-- 搜索筛选区 -->
    <div class="search-filter-container">
      <div class="search-section">
        <div class="search-input-wrapper">
          <el-input
            v-model="searchKeyword"
            :placeholder="t('search.placeholder')"
            size="large"
            clearable
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon class="search-icon"><Search /></el-icon>
            </template>
            <template #suffix>
              <el-button 
                type="primary" 
                @click="handleSearch"
                size="small"
                class="search-btn"
              >
                {{ t('search.searchButton') }}
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
      
      <!-- 快速筛选 -->
      <div class="filter-section">
        <div class="filter-label">
          <el-icon><Filter /></el-icon>
          <span>{{ t('filters.title') }}</span>
        </div>
        <div class="filter-buttons">
          <el-radio-group v-model="statusFilter" @change="handleFilterChange" size="small">
            <el-radio-button value="">{{ t('filters.all') }}</el-radio-button>
            <el-radio-button value="draft">{{ t('filters.draft') }}</el-radio-button>
            <el-radio-button value="submitted">{{ t('filters.pendingApproval') }}</el-radio-button>
            <el-radio-button value="approved">{{ t('filters.approved') }}</el-radio-button>
            <el-radio-button value="rejected">{{ t('filters.rejected') }}</el-radio-button>
            <el-radio-button value="cancelled">{{ t('filters.cancelled') }}</el-radio-button>
            <el-radio-button value="shipped">{{ t('filters.inTransit') }}</el-radio-button>
            <el-radio-button value="partiallyReceived">{{ t('filters.partialReceived') }}</el-radio-button>
            <el-radio-button value="received">{{ t('filters.completed') }}</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="table-container">
      <div class="table-header">
        <h3 class="table-title">{{ t('table.title') }}</h3>
        <div class="table-meta">
          {{ t('table.totalRecords', { total: pagination.total }) }}
        </div>
      </div>
      
      <div class="table-content">
        <DealerOrderTable
          :data="orderList"
          :loading="loading"
          @edit="handleEdit"
          @detail="handleDetail"
          @submit="handleSubmit"
          @cancel="handleCancel"
          @delete="handleDelete"
          @receipt="handleReceipt"
        />
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
          background
        />
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { purchaseDealerApi } from '@/api/modules/parts/purchase-dealer'
import { useModuleI18n } from '@/composables/useModuleI18n'
import type { PurchaseOrder, PurchaseOrderListQuery } from '@/types/parts/purchase-dealer'
import { Filter, Plus, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import DealerDashboard from './components/DealerDashboard.vue'
import DealerOrderTable from './components/DealerOrderTable.vue'

// 国际化
const { t, tc } = useModuleI18n('parts.purchase.dealer')

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const orderList = ref<PurchaseOrder[]>([])

// 分页参数
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 获取订单列表
const fetchOrderList = async () => {
  try {
    loading.value = true
    
    const params: PurchaseOrderListQuery = {
      page: pagination.page,
      size: pagination.size
    }
    
    if (searchKeyword.value) {
      params.orderNo = searchKeyword.value
    }
    
    if (statusFilter.value) {
      params.status = statusFilter.value as any
    }
    
    const result = await purchaseDealerApi.getOrderList(params)
    orderList.value = result.list
    pagination.total = result.total
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchOrderList()
}

// 筛选变化
const handleFilterChange = () => {
  pagination.page = 1
  fetchOrderList()
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.page = page
  fetchOrderList()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchOrderList()
}

// 新建订单
const handleCreate = () => {
  router.push('/parts/purchase/dealer/create')
}

// 编辑订单
const handleEdit = (order: PurchaseOrder) => {
  router.push(`/parts/purchase/dealer/${order.id}/edit`)
}

// 查看详情
const handleDetail = (order: PurchaseOrder) => {
  router.push(`/parts/purchase/dealer/${order.id}/detail`)
}

// 提交审核
const handleSubmit = async (order: PurchaseOrder) => {
  try {
    await ElMessageBox.confirm(
      t('messages.confirmSubmit'),
      '确认',
      {
        type: 'warning'
      }
    )
    
    const result = await purchaseDealerApi.submitForApproval(order.id)
    ElMessage.success(result.message)
    
    // 如果拆分了订单，显示额外信息
    if (result.data.length > 1) {
      ElMessage.info(`已生成${result.data.length}个独立订单：${result.data.map(d => d.orderNo).join(', ')}`)
    }
    
    fetchOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  }
}

// 取消订单
const handleCancel = async (order: PurchaseOrder) => {
  try {
    await ElMessageBox.confirm(
      t('messages.confirmCancel'),
      '确认',
      {
        type: 'warning'
      }
    )
    
    await purchaseDealerApi.cancelOrder(order.id)
    ElMessage.success('取消成功')
    fetchOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消失败:', error)
      ElMessage.error('取消失败')
    }
  }
}

// 删除订单
const handleDelete = async (order: PurchaseOrder) => {
  try {
    await ElMessageBox.confirm(
      t('messages.confirmDelete'),
      '确认',
      {
        type: 'warning'
      }
    )
    
    await purchaseDealerApi.deleteOrder(order.id)
    ElMessage.success('删除成功')
    fetchOrderList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 收货 - 跳转到收货管理页面
const handleReceipt = (order: PurchaseOrder) => {
  router.push(`/parts/purchase/dealer/${order.id}/receipt`)
}

// 初始化
onMounted(() => {
  fetchOrderList()
})
</script>

<style scoped lang="scss">
.dealer-purchase-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
  
  // 页面标题区域
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 32px;
    padding: 0 4px;
    
    .page-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        color: var(--el-text-color-primary);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .page-subtitle {
        margin: 0;
        font-size: 16px;
        color: var(--el-text-color-regular);
        opacity: 0.8;
      }
    }
    
    .header-actions {
      .create-btn {
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
        }
      }
    }
  }
  
  // 搜索筛选容器
  .search-filter-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    .search-section {
      margin-bottom: 20px;
      
      .search-input-wrapper {
        max-width: 500px;
        
        .search-input {
          :deep(.el-input__wrapper) {
            border-radius: 25px;
            padding: 8px 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--el-border-color-light);
            
            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }
            
            .search-icon {
              color: var(--el-color-primary);
            }
            
            .search-btn {
              border-radius: 15px;
              margin-right: -8px;
            }
          }
        }
      }
    }
    
    .filter-section {
      display: flex;
      align-items: center;
      gap: 16px;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color-lighter);
      
      .filter-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        min-width: 80px;
        
        .el-icon {
          color: var(--el-color-primary);
        }
      }
      
      .filter-buttons {
        flex: 1;
        
        :deep(.el-radio-group) {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .el-radio-button {
            margin: 0;
            
            .el-radio-button__inner {
              border-radius: 20px;
              padding: 6px 16px;
              font-size: 13px;
              transition: all 0.3s ease;
              
              &:hover {
                transform: translateY(-1px);
              }
            }
          }
        }
      }
    }
  }
  
  // 表格容器
  .table-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    .table-header {
      padding: 20px 24px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid var(--el-border-color-lighter);
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .table-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      .table-meta {
        font-size: 14px;
        color: var(--el-text-color-regular);
        background: var(--el-color-primary-light-9);
        padding: 4px 12px;
        border-radius: 12px;
        border: 1px solid var(--el-color-primary-light-7);
      }
    }
    
    .table-content {
      :deep(.el-table) {
        background: transparent;
        
        .el-table__header {
          th {
            background: var(--el-bg-color-page);
            border-bottom: 2px solid var(--el-border-color-lighter);
            font-weight: 600;
          }
        }
        
        .el-table__body {
          tr {
            transition: all 0.2s ease;
            
            &:hover {
              background-color: var(--el-color-primary-light-9);
              transform: scale(1.001);
            }
          }
        }
      }
    }
    
    .pagination-container {
      padding: 20px 24px;
      background: var(--el-bg-color-page);
      display: flex;
      justify-content: center;
      border-top: 1px solid var(--el-border-color-lighter);
      
      :deep(.el-pagination) {
        .el-pager li {
          border-radius: 6px;
          margin: 0 2px;
        }
        
        .el-pagination__jump {
          margin-left: 16px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dealer-purchase-page {
    padding: 16px;
    
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      
      .page-title {
        text-align: center;
        
        h2 {
          font-size: 24px;
        }
      }
      
      .header-actions {
        align-self: center;
      }
    }
    
    .search-filter-container {
      padding: 16px;
      
      .filter-section {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        
        .filter-label {
          min-width: auto;
        }
      }
    }
    
    .table-header {
      padding: 16px !important;
      flex-direction: column;
      gap: 8px;
      text-align: center;
    }
  }
}

@media (max-width: 480px) {
  .dealer-purchase-page {
    .search-filter-container {
      .filter-buttons {
        :deep(.el-radio-group) {
          flex-direction: column;
        }
      }
    }
  }
}
</style>