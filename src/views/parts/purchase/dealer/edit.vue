<template>
  <div class="edit-purchase-page">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">{{ t('actions.edit') }}</span>
          <el-button @click="handleBack">
            <el-icon><ArrowLeft /></el-icon>
            {{ tc('common.back') }}
          </el-button>
        </div>
      </template>

      <DealerOrderForm
        :loading="loading"
        :order-data="orderData"
        edit-mode
        @submit="handleSubmit"
        @save-draft="handleSaveDraft"
        @cancel="handleBack"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { useI18n } from 'vue-i18n'
import DealerOrderForm from './components/DealerOrderForm.vue'
import { purchaseDealerApi } from '@/api/modules/parts/purchase-dealer'
import type { PurchaseOrder, PurchaseOrderForm } from '@/types/parts/purchase-dealer'

// 国际化
const { t } = useModuleI18n('parts.purchase.dealer')
const { t: tc } = useI18n()

// 路由
const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const orderData = ref<PurchaseOrder | null>(null)

// 获取订单ID
const orderId = route.params.id as string

// 加载订单数据
const loadOrderData = async () => {
  try {
    loading.value = true
    orderData.value = await purchaseDealerApi.getOrderDetail(orderId)
  } catch (error) {
    console.error('加载订单数据失败:', error)
    ElMessage.error('加载订单数据失败')
    router.back()
  } finally {
    loading.value = false
  }
}

// 返回列表
const handleBack = () => {
  router.back()
}

// 保存草稿
const handleSaveDraft = async (formData: PurchaseOrderForm) => {
  try {
    loading.value = true
    
    const updateData = {
      warehouseId: formData.warehouseId,
      expectedDeliveryDate: formData.expectedDeliveryDate,
      remark: formData.remarks,
      items: formData.items.map(item => ({
        id: item.partId, // TODO: 使用正确的明细ID
        partId: item.partId,
        partCode: item.partCode,
        partName: item.partName,
        partType: item.partType,
        unit: item.unit,
        unitPrice: item.unitPrice,
        quantity: item.quantity
      }))
    }
    
    await purchaseDealerApi.updateOrder(orderId, updateData)
    
    ElMessage.success(t('messages.saveSuccess'))
    router.push('/parts/purchase/dealer')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 提交审核
const handleSubmit = async (formData: PurchaseOrderForm) => {
  try {
    loading.value = true
    
    // 先保存修改
    await handleSaveDraft(formData)
    
    // 再提交审核
    await purchaseDealerApi.submitForApproval(orderId)
    
    ElMessage.success(t('messages.submitSuccess'))
    router.push('/parts/purchase/dealer')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadOrderData()
})
</script>

<style scoped lang="scss">
.edit-purchase-page {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}
</style>