<template>
  <div class="part-management-view">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 查询表单区域 -->
    <el-card class="box-card mb-4" v-loading="loading">
      <el-form :model="queryForm" class="query-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('documentType')">
              <el-select v-model="queryForm.documentType" :placeholder="t('selectDocumentType')" style="width: 220px;" clearable>
                <el-option :label="t('documentTypeRequisition')" value="requisition"></el-option>
                <el-option :label="t('documentTypeScrap')" value="scrap"></el-option>
                <el-option :label="t('documentTypePicking')" value="picking"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('requisitionNumber')">
              <el-input v-model="queryForm.requisitionNumber" :placeholder="t('requisitionNumberPlaceholder')" style="width: 220px;" :disabled="isFiltersDisabled"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partName')">
              <el-select
                v-model="queryForm.partName"
                :placeholder="t('partNamePlaceholder')"
                style="width: 220px;"
                filterable
                clearable
                :disabled="isFiltersDisabled"
              >
                <el-option
                  v-for="item in partOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('partNumber')">
              <el-select
                v-model="queryForm.partNumber"
                :placeholder="t('partNumberPlaceholder')"
                style="width: 220px;"
                filterable
                clearable
                :disabled="isFiltersDisabled"
              >
                <el-option
                  v-for="item in partNumberOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('supplierName')">
              <el-select
                v-model="queryForm.supplierName"
                :placeholder="t('supplierNamePlaceholder')"
                style="width: 220px;"
                filterable
                clearable
                :disabled="isFiltersDisabled"
              >
                <el-option
                  v-for="item in supplierOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('documentDateRange')">
              <el-date-picker
                v-model="queryForm.requisitionDateRange"
                type="daterange"
                :range-separator="tc('to')"
                :start-placeholder="tc('startDate')"
                :end-placeholder="tc('endDate')"
                style="width: 220px;"
                :disabled="isFiltersDisabled"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="getStatusFilterLabel()">
              <el-select v-model="queryForm.requisitionStatus" :placeholder="getStatusFilterPlaceholder()" style="width: 220px;" :disabled="isFiltersDisabled">
                <template v-if="queryForm.documentType === 'scrap'">
                  <el-option :label="t('statusSubmitted')" value="submitted"></el-option>
                  <el-option :label="t('statusApproved')" value="approved"></el-option>
                  <el-option :label="t('statusRejected')" value="rejected"></el-option>
                </template>
                <template v-else-if="queryForm.documentType === 'picking'">
                  <el-option :label="t('workOrderStatus.pending')" value="pending"></el-option>
                  <el-option :label="t('workOrderStatus.picked')" value="picked"></el-option>
                  <el-option :label="t('workOrderStatus.outOfStock')" value="outOfStock"></el-option>
                  <el-option :label="t('workOrderStatus.closed')" value="closed"></el-option>
                </template>
                <template v-else>
                  <el-option :label="t('statusSubmitted')" value="submitted"></el-option>
                  <el-option :label="t('statusApproved')" value="approved"></el-option>
                  <el-option :label="t('statusRejected')" value="rejected"></el-option>
                  <el-option :label="t('statusShipped')" value="shipped"></el-option>
                  <el-option :label="t('statusPartialShipped')" value="partialShipped"></el-option>
                  <el-option :label="t('statusPartialReceived')" value="partialReceived"></el-option>
                  <el-option :label="t('statusReceived')" value="received"></el-option>
                  <el-option :label="t('statusVoided')" value="voided"></el-option>
                </template>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="query-buttons-row">
          <el-form-item class="form-buttons">
            <el-button type="primary" @click="onSubmit" :disabled="isFiltersDisabled" :loading="loading">{{ tc('query') }}</el-button>
            <el-button @click="onReset" :disabled="isFiltersDisabled">{{ tc('reset') }}</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <div class="mb-4">
      <el-button type="primary" @click="handleCreateRequisition">{{ t('newRequisition') }}</el-button>
      <el-button :disabled="multipleSelection.length === 0" @click="handleExportReport">{{ t('exportReport') }}</el-button>
      <el-button @click="handlePartScrap">{{ t('partScrap') }}</el-button>
      <el-button @click="handleScrapRecord">{{ t('scrapRecord') }}</el-button>
      <el-button @click="handleViewMaterialOrder">{{ t('viewMaterialOrder') }}</el-button>
      <el-button @click="handleRefresh" :loading="loading">
        <el-icon><Refresh /></el-icon>
        {{ tc('refresh') }}
      </el-button>
    </div>

    <!-- List Section -->
    <el-table 
      :data="tableData" 
      style="width: 100%;" 
      border 
      v-loading="loading"
      :empty-text="getEmptyText()"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" fixed="left"></el-table-column>
      
      <!-- 单据号列 -->
      <el-table-column prop="requisitionNumber" :label="getDocumentNumberLabel()" min-width="120" align="left"></el-table-column>
      
      <!-- 零件信息列 -->
      <el-table-column 
        v-if="shouldShowColumn('partInfo')" 
        prop="partName" 
        :label="t('partName')" 
        min-width="150" 
        align="left"
      >
        <template #default="{ row }">
          <div class="part-info">
            <div class="part-name">{{ row.partName }}</div>
            <div class="part-code text-gray-500 text-sm">{{ row.partCode || row.partNumber }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 零件编号列 -->
      <el-table-column 
        v-if="shouldShowColumn('partNumber')" 
        prop="partNumber" 
        :label="t('partNumber')" 
        min-width="120" 
        align="left"
      />

      <!-- 供应商信息列 -->
      <el-table-column 
        v-if="shouldShowColumn('supplierInfo')" 
        prop="supplierName" 
        :label="t('supplierName')" 
        min-width="120" 
        align="left"
      >
        <template #default="{ row }">
          {{ formatSupplierInfo(row.supplierName, row.supplierCode) }}
        </template>
      </el-table-column>

      <!-- 数量列 -->
      <el-table-column 
        v-if="shouldShowColumn('quantity')" 
        prop="quantity" 
        :label="t('quantity')" 
        min-width="80" 
        align="center"
      >
        <template #default="{ row }">
          {{ formatQuantity(row.quantity) }}
        </template>
      </el-table-column>

      <!-- 报损数量列 -->
      <el-table-column 
        v-if="shouldShowColumn('scrapQuantity')" 
        :label="t('scrapQuantityLabel')" 
        min-width="100" 
        align="center"
      >
        <template #default="{ row }">
          {{ formatQuantity(row.scrapQuantity) }}
        </template>
      </el-table-column>

      <!-- 报损来源列 -->
      <el-table-column 
        v-if="shouldShowColumn('scrapSource')" 
        prop="scrapSource" 
        :label="t('scrapSourceLabel')" 
        min-width="100" 
        align="center"
      >
        <template #default="{ row }">
          {{ getScrapSourceLabel(row.scrapSource) }}
        </template>
      </el-table-column>

      <!-- 日期列 -->
      <el-table-column prop="requisitionDate" :label="getGenerateDateLabel()" min-width="120" align="left">
        <template #default="{ row }">
          {{ formatDate(row.requisitionDate) }}
        </template>
      </el-table-column>

      <!-- 状态列 -->
      <el-table-column prop="requisitionStatus" :label="getDocumentStatusLabel()" min-width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getUnifiedStatusTagType(row.requisitionStatus, row.documentType)">
            {{ getUnifiedStatusLabel(row.requisitionStatus, row.documentType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column :label="tc('operations')" min-width="280" align="left" header-align="left">
        <template #default="scope">
          <div class="operation-buttons">
            <!-- 叫料单操作 -->
            <template v-if="!queryForm.documentType || queryForm.documentType === 'requisition'">
              <el-button link type="primary" size="small" @click="handleDetail(scope.row)">{{ tc('detail') }}</el-button>
              <el-button
                link
                type="warning"
                size="small"
                :disabled="scope.row.requisitionStatus !== 'submitted' && scope.row.requisitionStatus !== 'rejected'"
                @click="handleEdit(scope.row)"
              >
                {{ tc('edit') }}
              </el-button>
              <el-button
                link
                type="danger"
                size="small"
                :disabled="scope.row.requisitionStatus !== 'submitted' || !multipleSelection.some(item => item === scope.row)"
                @click="handleCancel(scope.row)"
              >
                {{ tc('void') }}
              </el-button>
            </template>
            <!-- 拣货单（工单）操作 -->
            <template v-else-if="queryForm.documentType === 'picking'">
              <el-button
                link
                type="primary"
                size="small"
                @click="handleWorkOrderDetail(scope.row)"
              >
                {{ t('workOrderDetail') }}
              </el-button>
              <el-button
                link
                type="success"
                size="small"
                :disabled="scope.row.requisitionStatus === 'outOfStock'"
                @click="handlePrintMaterialOrder(scope.row)"
              >
                {{ t('printMaterialOrder') }}
              </el-button>
              <el-button
                v-if="scope.row.requisitionStatus === 'pending_pick'"
                link
                type="warning"
                size="small"
                @click="handleCompletePicking(scope.row)"
              >
                {{ t('completePicking') }}
              </el-button>
              <el-button
                v-if="scope.row.requisitionStatus === 'picked'"
                link
                type="danger"
                size="small"
                @click="handleReturnPicking(scope.row)"
              >
                {{ t('returnPicking') }}
              </el-button>
            </template>
            <!-- 报损单操作 -->
            <template v-else-if="queryForm.documentType === 'scrap'">
              <el-button link type="primary" size="small" @click="handleDetail(scope.row)">{{ tc('detail') }}</el-button>
              <el-button
                link
                type="warning"
                size="small"
                :disabled="scope.row.requisitionStatus !== 'submitted' && scope.row.requisitionStatus !== 'rejected'"
                @click="handleEditScrapFromMain(scope.row)"
              >
                {{ tc('edit') }}
              </el-button>
              <el-button
                v-if="scope.row.requisitionStatus !== 'voided'"
                link
                type="danger"
                size="small"
                :disabled="scope.row.requisitionStatus !== 'submitted' || !multipleSelection.some(item => item === scope.row)"
                @click="handleVoidScrapOrder(scope.row)"
              >
                {{ tc('void') }}
              </el-button>
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- Pagination -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      :small="false"
      :background="true"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; justify-content: center;"
      :hide-on-single-page="false"
    />

    <!-- 对话框组件 -->
    <!-- 新建叫料单对话框 -->
    <el-dialog
      v-model="newRequisitionDialogVisible"
      :title="t('newRequisition')"
      width="90%"
      :before-close="handleNewRequisitionDialogClose"
    >
      <NewRequisitionForm
        @submit-success="handleNewRequisitionSuccess"
        @cancel="handleNewRequisitionDialogClose"
      />
    </el-dialog>

    <!-- 零件报损对话框 -->
    <el-dialog
      v-model="partScrapDialogVisible"
      :title="t('partScrap')"
      width="90%"
      :before-close="handlePartScrapDialogClose"
    >
      <PartScrapForm
        @submit-success="handlePartScrapSuccess"
        @cancel="handlePartScrapDialogClose"
      />
    </el-dialog>

    <!-- 报损记录列表对话框 -->
    <el-dialog
      v-model="scrapRecordDialogVisible"
      :title="t('scrapRecordList')"
      width="95%"
      :before-close="handleScrapRecordDialogClose"
    >
      <ScrapRecordList
        :is-visible="scrapRecordDialogVisible"
        @close="handleScrapRecordDialogClose"
        @edit-scrap-order="handleEditScrapOrder"
      />
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog
      v-model="approvalDialogVisible"
      :title="t('approval')"
      width="80%"
      :before-close="handleApprovalDialogClose"
    >
      <ApprovalModal
        :requisition-data="currentApprovalData"
        @submit-success="handleApprovalSuccess"
        @cancel="handleApprovalDialogClose"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';

// 导入对话框组件
import NewRequisitionForm from './components/NewRequisitionForm.vue';
import PartScrapForm from './components/PartScrapForm.vue';
import ScrapRecordList from './components/ScrapRecordList.vue';
import ApprovalModal from './components/ApprovalModal.vue';

// 导入API模块函数
import {
  getPartManagementDataAPI,
  exportData,
  // 叫料单相关API
  createRequisition,
  updateRequisition,
  voidRequisition,
  getRequisitionDetail,
  batchVoidRequisitions,
  
  // 报损单相关API
  createScrapRecord,
  updateScrapRecord,
  voidScrapRecord,
  getScrapRecordsListAPI,
  
  // 工单相关API
  getMaterialOrders,
  getWorkOrderDetail,
  completePicking,
  updateReturnPicking,
  completeWork,
  printMaterialOrder,
  
  // 重试机制
  apiCallWithRetry,
  getPartManagementDataWithRetry
} from '@/api/modules/parts/management';

// 导入类型定义
import type {
  UnifiedSearchParams,
  UnifiedTableItem,
  QueryForm
} from '@/types/parts/management';

// 国际化
const { t, tc } = useModuleI18n('parts.management');

// 数据缓存机制
const dataCache = new Map<string, {
  data: any;
  timestamp: number;
  expiresIn: number;
}>();

const CACHE_EXPIRY = 5 * 60 * 1000; // 5分钟缓存

// 生成缓存键
const getCacheKey = (params: UnifiedSearchParams) => {
  return JSON.stringify(params);
};

// 检查缓存是否有效
const isCacheValid = (cacheItem: any) => {
  return Date.now() - cacheItem.timestamp < cacheItem.expiresIn;
};

// Mock数据状态显示
const mockDataStatus = ref({
  enabled: false,
  dataSource: 'unknown'
});

// 错误类型定义
interface ApiError {
  code?: string;
  message: string;
  details?: any;
}

// 统一错误处理函数
const handleApiError = (error: any, operation: string) => {
  console.error(`${operation} failed:`, error);

  // 根据错误类型显示不同消息
  if (error.code === 'NETWORK_ERROR') {
    ElMessage.error(t('networkError'));
  } else if (error.code === 'TIMEOUT') {
    ElMessage.error(t('requestTimeout'));
  } else if (error.code === 'UNAUTHORIZED') {
    ElMessage.error(t('unauthorized'));
    // 可能需要跳转到登录页
  } else {
    ElMessage.error(t('operationFailed', { operation }));
  }
};

// 安全的API调用封装
const safeApiCall = async <T>(apiCall: () => Promise<T>, operation: string): Promise<T | null> => {
  try {
    return await apiCall();
  } catch (error) {
    handleApiError(error, operation);
    return null;
  }
};

// 基础响应式数据
const loading = ref(false);
const tableData = ref<UnifiedTableItem[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const multipleSelection = ref<any[]>([]);

// 数据同步状态
const syncStatus = reactive({
  lastSyncTime: null as Date | null,
  isSyncing: false,
  syncInterval: null as number | null
});

// 自动同步数据（可选）
const startAutoSync = () => {
  if (syncStatus.syncInterval) return;

  syncStatus.syncInterval = window.setInterval(() => {
    if (!loading.value && queryForm.documentType) {
      fetchData(false); // 不使用缓存
      syncStatus.lastSyncTime = new Date();
    }
  }, 30000); // 30秒同步一次
};

const stopAutoSync = () => {
  if (syncStatus.syncInterval) {
    window.clearInterval(syncStatus.syncInterval);
    syncStatus.syncInterval = null;
  }
};

// 手动刷新数据
const handleRefresh = () => {
  fetchData(false);
  ElMessage.success(t('dataRefreshed'));
};

// 查询表单
const queryForm = reactive<QueryForm>({
  partName: '',
  partNumber: '',
  requisitionNumber: '',
  documentType: 'requisition', // 默认选择叫料单
  supplierName: '',
  requisitionDateRange: [],
  requisitionStatus: '',
  inventoryStatus: '',
});

// 简化的选项数据
const partOptions = ref([
  { label: '刹车片', value: '刹车片' },
  { label: '机油滤清器', value: '机油滤清器' },
  { label: '空气滤清器', value: '空气滤清器' }
]);

const partNumberOptions = ref([
  { label: 'PN-001', value: 'PN-001' },
  { label: 'PN-002', value: 'PN-002' },
  { label: 'PN-003', value: 'PN-003' }
]);

const supplierOptions = ref([
  { label: '博世', value: '博世' },
  { label: '德尔福', value: '德尔福' },
  { label: '大陆集团', value: '大陆集团' }
]);

// 模态框状态
const newRequisitionDrawerVisible = ref(false);
const newRequisitionDialogVisible = ref(false);
const partScrapDialogVisible = ref(false);
const scrapRecordDialogVisible = ref(false);
const materialOrderDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const workOrderDetailDialogVisible = ref(false);
const returnPickingDialogVisible = ref(false);
const approvalDialogVisible = ref(false);

// 编辑状态
const editingRequisition = ref(null);
const scrapEditMode = ref(false);
const scrapEditData = ref(null);
const currentDetail = ref(null);
const currentWorkOrderDetail = ref(null);
const currentReturnPickingDetail = ref(null);
const currentApprovalData = ref(null);

// 计算属性：判断是否禁用其他筛选框
const isFiltersDisabled = computed(() => {
  return !queryForm.documentType;
});

// 动态表格列控制逻辑
const shouldShowColumn = (columnName: string) => {
  const columnConfig = {
    requisition: ['partInfo', 'partNumber', 'supplierInfo', 'quantity'],
    scrap: ['partInfo', 'scrapQuantity', 'scrapSource'],
    picking: ['partInfo', 'partNumber', 'quantity']
  };

  const currentColumns = columnConfig[queryForm.documentType as keyof typeof columnConfig] || [];
  return currentColumns.includes(columnName);
};

// 数据格式化函数
const formatDate = (dateString: string | Date) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

const formatQuantity = (quantity: number) => {
  return quantity ? quantity.toLocaleString() : '0';
};

const formatSupplierInfo = (supplierName: string, supplierCode?: string) => {
  return supplierCode ? `${supplierName}(${supplierCode})` : supplierName;
};

// 空数据处理
const getEmptyText = () => {
  if (loading.value) return '';
  if (!queryForm.documentType) return t('pleaseSelectDocumentType');
  return t('noDataFound');
};

// 表格选择状态管理
const selectionState = reactive({
  selectedRows: [] as UnifiedTableItem[],
  selectAll: false,
  indeterminate: false
});

// 清空选择
const clearSelection = () => {
  multipleSelection.value = [];
  selectionState.selectedRows = [];
  selectionState.selectAll = false;
  selectionState.indeterminate = false;
};

// 标签和占位符获取函数
const getStatusFilterLabel = () => {
  switch (queryForm.documentType) {
    case 'scrap': return t('scrapDocumentStatus');
    case 'picking': return t('pickingDocumentStatus');
    default: return t('requisitionStatus');
  }
};

const getStatusFilterPlaceholder = () => {
  switch (queryForm.documentType) {
    case 'scrap': return t('pleaseSelectScrapStatus');
    case 'picking': return t('pleaseSelectPickingStatus');
    default: return t('selectRequisitionStatus');
  }
};

const getDocumentNumberLabel = () => {
  switch (queryForm.documentType) {
    case 'scrap': return t('scrapOrderNumber');
    case 'picking': return t('workOrderNumber');
    default: return t('requisitionNumber');
  }
};

const getGenerateDateLabel = () => {
  switch (queryForm.documentType) {
    case 'scrap': return t('scrapDate');
    case 'picking': return t('createDate');
    default: return t('requisitionDate');
  }
};

const getDocumentStatusLabel = () => {
  switch (queryForm.documentType) {
    case 'scrap': return t('scrapStatus');
    case 'picking': return t('workOrderStatus.label');
    default: return t('requisitionStatus');
  }
};

// 状态和标签处理函数
const getScrapSourceLabel = (scrapSource: string) => {
  const sourceMap: Record<string, string> = {
    'receipt': t('sourceReceipt'),
    'repair': t('sourceRepair'),
    'other': t('sourceOther')
  };
  return sourceMap[scrapSource] || scrapSource;
};

const getWorkOrderStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': t('workOrderStatus.pending'),
    'picked': t('workOrderStatus.picked'),
    'outOfStock': t('workOrderStatus.outOfStock'),
    'closed': t('workOrderStatus.closed')
  };
  return statusMap[status] || status;
};

const getWorkOrderStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'picked': 'success',
    'outOfStock': 'danger',
    'closed': 'info'
  };
  return typeMap[status] || 'info';
};

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'submitted': 'primary',
    'approved': 'success',
    'rejected': 'danger',
    'shipped': 'warning',
    'received': 'success',
    'voided': 'info'
  };
  return typeMap[status] || 'primary';
};

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'submitted': t('statusSubmitted'),
    'approved': t('statusApproved'),
    'rejected': t('statusRejected'),
    'shipped': t('statusShipped'),
    'partialShipped': t('statusPartialShipped'),
    'partialReceived': t('statusPartialReceived'),
    'received': t('statusReceived'),
    'voided': t('statusVoided')
  };
  return statusMap[status] || status;
};

const getUnifiedStatusLabel = (status: string, documentType: string) => {
  return documentType === 'picking' ? getWorkOrderStatusLabel(status) : getStatusLabel(status);
};

const getUnifiedStatusTagType = (status: string, documentType: string) => {
  return documentType === 'picking' ? getWorkOrderStatusTagType(status) : getStatusTagType(status);
};

// 增强的数据获取函数
const fetchData = async (useCache = true) => {
  // 防止重复请求
  if (loading.value) return;

  const params: UnifiedSearchParams = {
    documentType: queryForm.documentType,
    page: currentPage.value,
    pageSize: pageSize.value,
    partName: queryForm.partName,
    partNumber: queryForm.partNumber,
    requisitionNumber: queryForm.requisitionNumber,
    supplierName: queryForm.supplierName,
    requisitionDateRange: queryForm.requisitionDateRange,
    requisitionStatus: queryForm.requisitionStatus,
    inventoryStatus: queryForm.inventoryStatus,
  };

  // 检查缓存
  const cacheKey = getCacheKey(params);
  if (useCache && dataCache.has(cacheKey)) {
    const cached = dataCache.get(cacheKey)!;
    if (isCacheValid(cached)) {
      tableData.value = cached.data.data;
      total.value = cached.data.total;
      clearSelection();
      return;
    }
  }

  loading.value = true;
  try {
    // 使用重试机制的API调用
    const response = await apiCallWithRetry(() => getPartManagementDataAPI(params));

    tableData.value = response.data;
    total.value = response.total;

    // 缓存数据
    dataCache.set(cacheKey, {
      data: response,
      timestamp: Date.now(),
      expiresIn: CACHE_EXPIRY
    });

    clearSelection();
  } catch (error) {
    console.error('Failed to load data:', error);
    ElMessage.error(t('dataLoadFailed'));
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 表单操作
const onSubmit = () => {
  currentPage.value = 1;
  fetchData();
};

const onReset = () => {
  const currentDocumentType = queryForm.documentType;
  Object.assign(queryForm, {
    partName: '',
    partNumber: '',
    requisitionNumber: '',
    documentType: currentDocumentType,
    supplierName: '',
    requisitionDateRange: [],
    requisitionStatus: '',
    inventoryStatus: '',
  });
  currentPage.value = 1;
  fetchData();
};

// 操作处理函数
const handleCreateRequisition = () => {
  editingRequisition.value = null;
  newRequisitionDialogVisible.value = true;
};

const handlePartScrap = () => {
  scrapEditMode.value = false;
  scrapEditData.value = null;
  partScrapDialogVisible.value = true;
};

const handleScrapRecord = () => {
  scrapRecordDialogVisible.value = true;
};

const handleViewMaterialOrder = () => {
  materialOrderDialogVisible.value = true;
};

const handleExportReport = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning(tc('pleaseSelectData'));
    return;
  }

  try {
    const blob = await exportData(multipleSelection.value);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.download = `part-management-export-${Date.now()}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    ElMessage.success(tc('exportingReport'));
  } catch (error) {
    console.error('Export failed:', error);
    ElMessage.error(t('exportFailed'));
  }
};

// 业务操作函数 
const handleDetail = (row: any) => {
  currentDetail.value = {
    requisitionNumber: row.requisitionNumber,
    purchaseOrderNumber: row.purchaseOrderNumber,
    requisitionDate: row.requisitionDate,
    requisitionStatus: row.requisitionStatus,
    rejectionReason: row.rejectionReason || '',
    items: row.items || []
  };
  detailDialogVisible.value = true;
};

const handleEdit = (row: any) => {
  if (row.requisitionStatus === 'submitted' || row.requisitionStatus === 'rejected') {
    editingRequisition.value = { ...row };
    newRequisitionDrawerVisible.value = true;
  }
};

// 叫料单作废操作
const handleCancel = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t('confirmVoidRequisition', { number: row.requisitionNumber }),
      t('common.tip'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      }
    );

    await voidRequisition(row.requisitionNumber);
    ElMessage.success(t('voidRequisitionSuccess'));
    fetchData(false); // 强制刷新，不使用缓存
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Void operation failed:', error);
      ElMessage.error(t('voidOperationFailed'));
    }
  }
};

const handleWorkOrderDetail = (row: any) => {
  currentWorkOrderDetail.value = row;
  workOrderDetailDialogVisible.value = true;
};

// 工单完成拣货操作
const handleCompletePicking = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t('confirmCompletePicking', { workOrderNumber: row.requisitionNumber }),
      t('common.tip'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      }
    );

    await completePicking(row.requisitionNumber);
    ElMessage.success(t('completePickingSuccess'));

    // 更新本地数据状态
    const index = tableData.value.findIndex(item => item.requisitionNumber === row.requisitionNumber);
    if (index !== -1) {
      tableData.value[index].requisitionStatus = 'picked';
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Complete picking failed:', error);
      ElMessage.error(t('completePickingFailed'));
    }
  }
};

const handleReturnPicking = (row: any) => {
  console.log('Return picking operation:', row);
  ElMessage.info(t('returnPickingNotImplemented'));
};

const handlePrintMaterialOrder = (row: any) => {
  console.log('Print material order:', row);
  ElMessage.info(t('printFunctionNotImplemented'));
};

const handleEditScrapFromMain = (row: any) => {
  scrapEditMode.value = true;
  scrapEditData.value = row;
  partScrapDialogVisible.value = true;
};

// 报损单作废操作
const handleVoidScrapOrder = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      t('confirmVoidScrapOrder', { number: row.requisitionNumber }),
      t('common.tip'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      }
    );

    await voidScrapRecord(row.id);
    ElMessage.success(t('voidScrapOrderSuccess'));
    fetchData(false);
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Void scrap order failed:', error);
      ElMessage.error(t('voidScrapOrderFailed'));
    }
  }
};

// 业务逻辑处理函数
const handleNewRequisitionSubmitSuccess = () => {
  newRequisitionDrawerVisible.value = false;
  fetchData();
};

const handlePartScrapSubmitSuccess = () => {
  partScrapDialogVisible.value = false;
  scrapEditMode.value = false;
  scrapEditData.value = null;
  fetchData();
};

const handleEditScrapOrder = (scrapOrderData: any) => {
  scrapRecordDialogVisible.value = false;
  scrapEditMode.value = true;
  scrapEditData.value = scrapOrderData;
  partScrapDialogVisible.value = true;
};

// 新建叫料单对话框处理函数
const handleNewRequisitionDialogClose = () => {
  newRequisitionDialogVisible.value = false;
};

const handleNewRequisitionSuccess = () => {
  newRequisitionDialogVisible.value = false;
  fetchData();
  ElMessage.success(t('operationSuccessful'));
};

// 零件报损对话框处理函数
const handlePartScrapDialogClose = () => {
  partScrapDialogVisible.value = false;
  scrapEditMode.value = false;
  scrapEditData.value = null;
};

const handlePartScrapSuccess = () => {
  partScrapDialogVisible.value = false;
  scrapEditMode.value = false;
  scrapEditData.value = null;
  fetchData();
  ElMessage.success(t('operationSuccessful'));
};

// 报损记录对话框处理函数
const handleScrapRecordDialogClose = () => {
  scrapRecordDialogVisible.value = false;
};

// 审批对话框处理函数
const handleApprovalDialogClose = () => {
  approvalDialogVisible.value = false;
  currentApprovalData.value = null;
};

const handleApprovalSuccess = () => {
  approvalDialogVisible.value = false;
  currentApprovalData.value = null;
  fetchData();
  ElMessage.success(t('operationSuccessful'));
};

// 分页和选择处理
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置到第一页
  fetchData();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  fetchData();
};

const handleSelectionChange = (selection: UnifiedTableItem[]) => {
  multipleSelection.value = selection;
  selectionState.selectedRows = selection;

  // 更新全选状态
  const total = tableData.value.length;
  const selected = selection.length;
  selectionState.selectAll = selected === total && total > 0;
  selectionState.indeterminate = selected > 0 && selected < total;
};

// 防抖定时器
let debounceTimer: number | null = null;

// 防抖函数
const debounceRefresh = () => {
  if (debounceTimer) {
    window.clearTimeout(debounceTimer);
  }
  debounceTimer = window.setTimeout(() => {
    currentPage.value = 1;
    fetchData();
  }, 300);
};

// 单据类型切换监听
watch(() => queryForm.documentType, (newType, oldType) => {
  if (newType !== oldType) {
    // 清空其他筛选条件，但保持单据类型
    queryForm.requisitionStatus = '';
    queryForm.partName = '';
    queryForm.partNumber = '';
    queryForm.supplierName = '';
    queryForm.requisitionDateRange = [];

    // 清空表格选择
    clearSelection();

    // 自动刷新数据
    currentPage.value = 1;
    fetchData();
  }
});

// 输入框防抖监听（可选，为高级用户体验）
watch(() => queryForm.requisitionNumber, () => {
  if (queryForm.documentType && queryForm.requisitionNumber.trim()) {
    debounceRefresh();
  }
});

// 检查Mock数据状态
const checkMockDataStatus = () => {
  // 检查环境变量
  const useMock = import.meta.env.VITE_APP_USE_MOCK_API || import.meta.env.VITE_USE_MOCK;
  mockDataStatus.value.enabled = useMock === 'true' || useMock === true;
  mockDataStatus.value.dataSource = mockDataStatus.value.enabled ? 'Mock Data' : 'Real API';

  console.log(`Data Source: ${mockDataStatus.value.dataSource}`);
};

// 初始化数据加载
onMounted(() => {
  checkMockDataStatus();
  fetchData();
  // 可选：启动自动同步
  // startAutoSync();
});

// 清理资源
onUnmounted(() => {
  stopAutoSync();
});
</script>

<style scoped>
.part-management-view {
  padding: 20px;
}

.mb-4 {
  margin-bottom: 20px;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 20px;
}

.query-form {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-row {
    margin-bottom: 20px;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}

.operation-buttons {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: flex-start !important;
  min-height: 32px;
  padding: 4px 0;
  width: 100%;
  text-align: left !important;
}

.operation-buttons .el-button {
  margin: 0 !important;
  min-width: 60px;
  white-space: nowrap;
}

.query-buttons-row {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 表格相关样式 */
.part-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.part-name {
  font-weight: 500;
  color: #303133;
}

.part-code {
  font-size: 12px;
  color: #909399;
}

.text-gray-500 {
  color: #909399;
}

.text-sm {
  font-size: 12px;
}

/* 分页样式 */
:deep(.el-pagination) {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

/* 表格加载状态优化 */
:deep(.el-table__empty-block) {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 选择列固定宽度优化 */
:deep(.el-table .el-table-column--selection .el-checkbox) {
  display: flex;
  justify-content: center;
}
</style>