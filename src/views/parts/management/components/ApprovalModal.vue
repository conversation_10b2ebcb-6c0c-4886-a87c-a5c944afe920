<template>
  <el-form :model="form" label-width="auto">
    <el-form-item :label="t('requisitionNumber')">
      <el-input v-model="requisitionData.requisitionNumber" disabled></el-input>
    </el-form-item>
    <el-form-item :label="t('requisitionDate')">
      <el-input v-model="requisitionData.requisitionDate" disabled></el-input>
    </el-form-item>
    <el-form-item :label="t('approvalResult')">
      <el-select v-model="form.approvalResult" :placeholder="t('selectApprovalResult')">
        <el-option :label="t('statusApproved')" value="approved"></el-option>
        <el-option :label="t('statusRejected')" value="rejected"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-if="form.approvalResult === 'rejected'" :label="t('rejectionReason')" prop="rejectionReason" :rules="[{ required: true, message: t('rejectionReasonRequired'), trigger: 'blur' }]" >
      <el-input
        v-model="form.rejectionReason"
        type="textarea"
        :rows="3"
        :placeholder="t('enterRejectionReason')"
        maxlength="1000"
        show-word-limit
      ></el-input>
    </el-form-item>
  </el-form>

  <h4 class="mt-4">{{ t('requisitionDetails') }}</h4>
  <el-table :data="requisitionData.detailList" style="width: 100%;" border class="mt-2">
    <el-table-column type="index" :label="tc('sequence')" width="60"></el-table-column>
    <el-table-column prop="partName" :label="t('partName')" min-width="120"></el-table-column>
    <el-table-column prop="partNumber" :label="t('partNumber')" min-width="120"></el-table-column>
    <el-table-column prop="quantity" :label="t('quantity')" width="80"></el-table-column>
    <el-table-column prop="unit" :label="t('unit')" width="80"></el-table-column>
    <el-table-column prop="requisitionStatus" :label="t('requisitionStatus')" min-width="100">
      <template #default="{ row }">
        <el-tag :type="getStatusTagType(row.requisitionStatus)">{{ getStatusLabel(row.requisitionStatus) }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="requisitionDate" :label="t('requisitionDate')" min-width="120"></el-table-column>
    <el-table-column prop="expectedArrivalTime" :label="t('expectedArrivalTime')" min-width="140"></el-table-column>
    <el-table-column prop="supplierName" :label="t('supplierName')" min-width="120"></el-table-column>
  </el-table>

  <div class="dialog-footer mt-4" style="text-align: right;">
    <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
    <el-button type="primary" @click="handleSubmit">{{ tc('confirm') }}</el-button>
  </div>
</template>

<script setup lang="ts">
import { reactive, defineProps, defineEmits, watch } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { ElMessage } from 'element-plus';

// 国际化设置
const { t, tc } = useModuleI18n('parts.management');

const props = defineProps({
  requisitionData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['submitSuccess', 'cancel']);

const form = reactive({
  approvalResult: '',
  rejectionReason: ''
});

// Watch for changes in requisitionData to reset form when modal opens with new data
watch(() => props.requisitionData, (newVal) => {
  if (newVal) {
    form.approvalResult = '';
    form.rejectionReason = '';
  }
}, { immediate: true });

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'submitted':
      return ''; // 普通标签
    case 'approved':
      return 'success'; // 成功标签
    case 'rejected':
      return 'danger'; // 危险标签
    case 'shipped':
      return 'warning'; // 警示标签
    case 'received':
      return 'success'; // 成功标签
    case 'cancelled':
      return ''; // 普通标签
    default:
      return '';
  }
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'submitted':
      return t('statusSubmitted');
    case 'approved':
      return t('statusApproved');
    case 'rejected':
      return t('statusRejected');
    case 'shipped':
      return t('statusShipped');
    case 'received':
      return t('statusReceived');
    case 'cancelled':
      return t('statusCancelled');
    default:
      return status;
  }
};

const handleSubmit = () => {
  if (form.approvalResult === 'rejected' && !form.rejectionReason) {
    ElMessage.error(t('rejectionReasonRequired'));
    return;
  }
  if (!form.approvalResult) {
    ElMessage.error(t('selectApprovalResult'));
    return;
  }

  console.log('审批提交', { ...props.requisitionData, ...form });
  ElMessage.success(t('approvalSuccess'));
  emit('submitSuccess');
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<style scoped>
.mt-2 {
  margin-top: 10px;
}
.mt-4 {
  margin-top: 20px;
}
</style> 