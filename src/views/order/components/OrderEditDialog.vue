<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? t('order.editOrder') : t('order.editOrder')"
    width="90%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div v-loading="loading">
      <!-- 订单基本信息 -->
      <div class="order-header">
        <div class="order-info">
          <span class="order-number">{{ orderData.orderNumber }}</span>
          <span class="create-time">{{ orderData.createTime }}</span>
        </div>
        <!-- 隐藏状态字段 -->
        <input type="hidden" :value="orderData.orderStatus" />
        <input type="hidden" :value="orderData.jpjRegistrationStatus" />
        <input type="hidden" :value="orderData.paymentStatus" />
        <input type="hidden" :value="orderData.insuranceStatus" />
        <input type="hidden" :value="orderData.approvalStatus" />
      </div>

      <!-- 客户信息模块 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('order.personalDetails') }}</span>
        </template>
        <el-form :model="orderData" label-position="top" readonly>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="t('order.customerName')">
                <el-input v-model="orderData.customerName" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.customerPhone')">
                <el-input v-model="orderData.customerPhone" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.buyerName')">
                <el-input v-model="orderData.buyerName" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.buyerPhone')">
                <el-input v-model="orderData.buyerPhone" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="t('order.buyerIdType')">
                <el-input v-model="orderData.buyerIdType" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.buyerIdNumber')">
                <el-input v-model="orderData.buyerIdNumber" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.buyerEmail')">
                <el-input v-model="orderData.buyerEmail" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.buyerType')">
                <el-input v-model="orderData.buyerType" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('order.buyerAddress')">
                <el-input v-model="orderData.buyerAddress" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item :label="t('order.buyerState')">
                <el-input v-model="orderData.buyerState" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item :label="t('order.buyerCity')">
                <el-input v-model="orderData.buyerCity" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.buyerPostalCode')">
                <el-input v-model="orderData.buyerPostalCode" readonly />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 购车门店信息模块 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('order.preferredOutletSalesAdvisor') }}</span>
        </template>
        <el-form :model="orderData" label-position="top" readonly>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="t('order.storeState')">
                <el-input v-model="orderData.storeState" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.storeCity')">
                <el-input v-model="orderData.storeCity" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.storeName')">
                <el-input v-model="orderData.storeName" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('order.salesConsultant')">
                <el-input v-model="orderData.salesConsultantName" readonly />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 购车信息模块 - Tab页导航 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('order.purchaseDetails') }}</span>
        </template>
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 车辆信息 Tab -->
          <el-tab-pane :label="t('order.vehicleInfo')" name="vehicle">
            <el-form :model="orderData" label-position="top">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item :label="t('order.model')">
                    <el-input v-model="orderData.model" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.variant')">
                    <el-input v-model="orderData.variant" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.color')">
                    <el-select v-model="orderData.color" @change="handleColorChange">
                      <el-option label="白色" value="白色" />
                      <el-option label="黑色" value="黑色" />
                      <el-option label="红色" value="红色" />
                      <el-option label="蓝色" value="蓝色" />
                      <el-option label="银色" value="银色" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.vin')">
                    <el-input v-model="orderData.vin" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item :label="t('order.salesSubtotal')">
                    <el-input v-model="orderData.salesSubtotal" readonly>
                      <template #append>RM</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.numberPlatesFee')">
                    <el-input v-model="orderData.numberPlatesFee" readonly>
                      <template #append>RM</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            
            <!-- 选配件信息表格 -->
            <div class="accessories-section">
              <h4>{{ t('order.accessories') }}</h4>
              <el-table :data="orderData.accessories" border>
                <el-table-column prop="category" :label="t('order.accessoryCategory')" />
                <el-table-column prop="accessoryName" :label="t('order.accessoryName')" />
                <el-table-column prop="price" :label="t('order.accessoryPrice')">
                  <template #default="{ row }">RM {{ row.price }}</template>
                </el-table-column>
                <el-table-column prop="quantity" :label="t('order.accessoryQuantity')" />
                <el-table-column prop="totalPrice" :label="t('order.accessoryTotalPrice')">
                  <template #default="{ row }">RM {{ row.totalPrice }}</template>
                </el-table-column>
              </el-table>
              <div class="total-amount">
                {{ t('order.accessoryTotalAmount') }}: RM {{ accessoryTotalAmount }}
              </div>
            </div>
          </el-tab-pane>

          <!-- 开票信息 Tab -->
          <el-tab-pane :label="t('order.invoiceInfo')" name="invoice">
            <el-form :model="orderData" label-position="top" readonly>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item :label="t('order.invoicingType')">
                    <el-input v-model="orderData.invoicingType" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.invoicingName')">
                    <el-input v-model="orderData.invoicingName" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.invoicingPhone')">
                    <el-input v-model="orderData.invoicingPhone" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.invoicingAddress')">
                    <el-input v-model="orderData.invoicingAddress" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>

          <!-- 服务&权益信息 Tab -->
          <el-tab-pane :label="t('order.serviceRightsInfo')" name="rights">
            <div class="rights-section">
              <el-button type="primary" @click="showRightsDialog">
                {{ t('order.addRights') }}
              </el-button>
              <el-table :data="orderData.rights" border class="mt-20">
                <el-table-column prop="rightCode" :label="t('order.rightCode')" />
                <el-table-column prop="rightName" :label="t('order.rightName')" />
                <el-table-column prop="mode" :label="t('order.rightMode')" />
                <el-table-column prop="discountAmount" :label="t('order.rightDiscountAmount')">
                  <template #default="{ row }">RM {{ row.discountAmount }}</template>
                </el-table-column>
                <el-table-column prop="effectiveDate" :label="t('order.rightEffectiveDate')" />
                <el-table-column prop="expiryDate" :label="t('order.rightExpiryDate')" />
                <el-table-column :label="t('common.operations')" width="100">
                  <template #default="{ row, $index }">
                    <el-button type="danger" link @click="deleteRight($index)">
                      {{ t('order.deleteRight') }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="total-amount">
                {{ t('order.rightsTotalDiscount') }}: RM {{ rightsTotalDiscount }}
              </div>
            </div>
          </el-tab-pane>

          <!-- 支付信息 Tab -->
          <el-tab-pane :label="t('order.paymentInfo')" name="payment">
            <el-form :model="orderData" label-position="top">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item :label="t('order.paymentMethod')">
                    <el-select v-model="orderData.paymentMethod" @change="handlePaymentMethodChange">
                      <el-option 
                        :label="t('order.paymentMethods.full_payment')" 
                        value="full_payment" 
                      />
                      <el-option 
                        :label="t('order.paymentMethods.installment')" 
                        value="installment" 
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="orderData.paymentMethod === 'installment'">
                  <el-form-item :label="t('order.loanApprovalStatus')">
                    <el-select v-model="orderData.loanApprovalStatus">
                      <el-option label="审核中" value="审核中" />
                      <el-option label="已通过" value="已通过" />
                      <el-option label="已驳回" value="已驳回" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.deposit')">
                    <el-input v-model="orderData.deposit" readonly>
                      <template #append>RM</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20" v-if="orderData.paymentMethod === 'installment'">
                <el-col :span="6">
                  <el-form-item :label="t('order.loanAmount')">
                    <el-input 
                      v-model="orderData.loanAmount" 
                      type="number"
                      @input="calculateFinalPayment"
                    >
                      <template #append>RM</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.loanTerm')">
                    <el-input v-model="orderData.loanTerm" type="number">
                      <template #append>{{ t('common.month') }}</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="t('order.finalPayment')">
                    <el-input v-model="finalPayment" readonly>
                      <template #append>RM</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>

          <!-- 保险信息 Tab -->
          <el-tab-pane :label="t('order.insuranceInfo')" name="insurance">
            <div class="insurance-section">
              <el-button 
                type="primary" 
                @click="pushInsurance" 
                :loading="insurancePushing"
                :disabled="insurancePushed"
              >
                {{ insurancePushed ? '已推送' : t('order.pushToInsurance') }}
              </el-button>
              
              <el-table :data="insuranceData" border class="mt-20" v-if="insuranceData.length > 0">
                <el-table-column prop="policyNumber" :label="t('order.policyNumber')" />
                <el-table-column prop="insuranceType" :label="t('order.insuranceType')" />
                <el-table-column prop="company" :label="t('order.insuranceCompany')" />
                <el-table-column prop="effectiveDate" :label="t('order.insuranceEffectiveDate')" />
                <el-table-column prop="expiryDate" :label="t('order.insuranceExpiryDate')" />
                <el-table-column prop="amount" :label="t('order.insuranceAmount')">
                  <template #default="{ row }">RM {{ row.amount }}</template>
                </el-table-column>
              </el-table>
              
              <div class="total-amount" v-if="insuranceData.length > 0">
                {{ t('order.insuranceTotalAmount') }}: RM {{ insuranceTotalAmount }}
              </div>
              
              <el-form :model="orderData" label-position="top" class="mt-20">
                <el-form-item :label="t('order.insuranceNotes')">
                  <el-input 
                    v-model="insuranceNotes" 
                    type="textarea" 
                    :rows="3"
                    :placeholder="t('order.insuranceNotes')"
                  />
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- OTR费用信息 Tab -->
          <el-tab-pane :label="t('order.otrFeesInfo')" name="otr">
            <div class="otr-section">
              <el-table :data="orderData.otrFees" border>
                <el-table-column prop="ticketNumber" :label="t('order.otrTicketNumber')" />
                <el-table-column prop="feeItem" :label="t('order.otrFeeItem')" />
                <el-table-column prop="feePrice" :label="t('order.otrFeePrice')">
                  <template #default="{ row }">RM {{ row.feePrice }}</template>
                </el-table-column>
                <el-table-column prop="effectiveDate" :label="t('order.otrEffectiveDate')" />
                <el-table-column prop="expiryDate" :label="t('order.otrExpiryDate')" />
              </el-table>
              <div class="total-amount">
                {{ t('order.otrFeesTotalAmount') }}: RM {{ otrFeesTotalAmount }}
              </div>
              
              <el-button 
                type="success" 
                @click="submitDelivery" 
                :loading="deliverySubmitting"
                class="mt-20"
              >
                {{ t('order.submitDelivery') }}
              </el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 底部操作栏 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="price-info">
          <div class="price-item">
            <span class="label">{{ t('order.totalAmount') }}:</span>
            <span class="value">RM {{ totalInvoicePrice }}</span>
          </div>
          <div class="price-item">
            <span class="label">{{ t('order.remainingReceivable') }}:</span>
            <span class="value">RM {{ remainingReceivable }}</span>
          </div>
        </div>
        <div class="action-buttons">
          <el-button @click="handleCancel">{{ t('order.backToList') }}</el-button>
          <el-button type="primary" @click="handleSave" :loading="saving">
            {{ t('order.save') }}
          </el-button>
        </div>
      </div>
    </template>

    <!-- 权益选择模态框 -->
    <RightsSelectionDialog 
      v-model="rightsDialogVisible" 
      @confirm="handleRightsSelected"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderDetail, saveOrder, pushInsurance as pushInsuranceAPI, submitDelivery as submitDeliveryAPI } from '@/api/modules/order'
import type { Order, OrderRight, OrderInsurance } from '@/types/order.d'
import RightsSelectionDialog from './RightsSelectionDialog.vue'

const { t } = useI18n()

// Props
interface Props {
  modelValue: boolean
  orderId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'saved': []
}>()

// 数据
const loading = ref(false)
const saving = ref(false)
const insurancePushing = ref(false)
const deliverySubmitting = ref(false)
const activeTab = ref('vehicle')
const insurancePushed = ref(false)
const rightsDialogVisible = ref(false)

const originalColor = ref('')
const colorChanged = ref(false)

const orderData = reactive<Order>({
  id: '',
  orderNumber: '',
  customerId: '',
  customerName: '',
  customerPhone: '',
  buyerName: '',
  buyerPhone: '',
  buyerIdType: '',
  buyerIdNumber: '',
  buyerEmail: '',
  buyerAddress: '',
  buyerState: '',
  buyerCity: '',
  buyerPostalCode: '',
  buyerType: '',
  storeId: '',
  storeName: '',
  storeState: '',
  storeCity: '',
  salesConsultantId: '',
  salesConsultantName: '',
  model: '',
  variant: '',
  color: '',
  vin: '',
  salesSubtotal: 0,
  numberPlatesFee: 98,
  totalAmount: 0,
  deposit: 1000,
  finalPayment: 0,
  loanAmount: 0,
  loanTerm: 60,
  paymentMethod: 'full_payment',
  loanApprovalStatus: '',
  orderStatus: 'confirmed',
  paymentStatus: 'deposit_paid',
  approvalStatus: 'approved',
  insuranceStatus: 'pending',
  jpjRegistrationStatus: 'pending',
  accessories: [],
  rights: [],
  insurance: {
    id: '',
    policyNumber: '',
    insuranceType: '',
    company: '',
    amount: 0,
    effectiveDate: '',
    expiryDate: '',
    status: 'pending',
    notes: ''
  },
  otrFees: [],
  createTime: '',
  updateTime: '',
  invoicingType: '',
  invoicingName: '',
  invoicingPhone: '',
  invoicingAddress: ''
})

const insuranceData = ref<any[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.orderId)

const accessoryTotalAmount = computed(() => {
  return orderData.accessories.reduce((sum, item) => sum + item.totalPrice, 0)
})

const rightsTotalDiscount = computed(() => {
  return orderData.rights.reduce((sum, item) => sum + item.discountAmount, 0)
})

const insuranceTotalAmount = computed(() => {
  return insuranceData.value.reduce((sum, item) => sum + item.amount, 0)
})

const otrFeesTotalAmount = computed(() => {
  return orderData.otrFees.reduce((sum, item) => sum + item.feePrice, 0)
})

const totalInvoicePrice = computed(() => {
  return orderData.salesSubtotal + 
         orderData.numberPlatesFee + 
         accessoryTotalAmount.value + 
         insuranceTotalAmount.value + 
         otrFeesTotalAmount.value - 
         rightsTotalDiscount.value
})

const finalPayment = computed(() => {
  if (orderData.paymentMethod === 'installment' && orderData.loanAmount) {
    return totalInvoicePrice.value - orderData.deposit - orderData.loanAmount
  }
  return totalInvoicePrice.value - orderData.deposit
})

const remainingReceivable = computed(() => {
  return finalPayment.value
})

// 为保险备注创建计算属性，安全处理 orderData.insurance 可能为 undefined 的情况
const insuranceNotes = computed({
  get: () => orderData.insurance?.notes || '',
  set: (value: string) => {
    if (orderData.insurance) {
      orderData.insurance.notes = value
    }
  }
})

// 方法
const getDetail = async () => {
  if (!props.orderId) {
    loading.value = false
    return
  }
  loading.value = true
  try {
    const result = await getOrderDetail(props.orderId)
    if (result) {
      Object.assign(orderData, result)
      // 如果 insurance 为空，则初始化为一个默认的 OrderInsurance 对象
      if (!orderData.insurance) {
        orderData.insurance = {
          id: '',
          policyNumber: '',
          insuranceType: '',
          company: '',
          amount: 0,
          effectiveDate: '',
          expiryDate: '',
          status: 'pending',
          notes: ''
        } as OrderInsurance // 明确类型断言
      }
      originalColor.value = result.color || ''
    }
  } catch (error) {
    ElMessage.error(t('order.getDetailFailed'))
  } finally {
    loading.value = false
  }
}

const handleColorChange = () => {
  colorChanged.value = orderData.color !== originalColor.value
}

const handlePaymentMethodChange = () => {
  if (orderData.paymentMethod === 'full_payment') {
    orderData.loanAmount = 0
    orderData.loanTerm = 0
    orderData.loanApprovalStatus = ''
  }
}

const calculateFinalPayment = () => {
  // 自动计算尾款
}

const showRightsDialog = () => {
  rightsDialogVisible.value = true
}

const handleRightsSelected = (selectedRights: OrderRight[]) => {
  orderData.rights.push(...selectedRights)
}

const deleteRight = (index: number) => {
  orderData.rights.splice(index, 1)
}

const pushInsurance = async () => {
  insurancePushing.value = true
  try {
    await pushInsuranceAPI(orderData.id)
    // 模拟生成保单信息
    insuranceData.value = [{
      id: 'ins001',
      policyNumber: `POL${Date.now()}`,
      insuranceType: '全险',
      company: '大众保险',
      amount: 3500,
      effectiveDate: new Date().toISOString().split('T')[0],
      expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'completed'
    }]
    insurancePushed.value = true
    ElMessage.success(t('common.operationSuccessful'))
  } catch (error: any) {
    ElMessage.error(error.message || t('common.operationFailed'))
  } finally {
    insurancePushing.value = false
  }
}

const submitDelivery = async () => {
  try {
    await ElMessageBox.confirm(
      t('order.confirmSubmitDelivery'),
      t('common.warning'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )
    
    deliverySubmitting.value = true
    await submitDeliveryAPI(orderData.id)
    ElMessage.success(t('common.operationSuccessful'))
    emit('saved')
    dialogVisible.value = false
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || t('common.operationFailed'))
    }
  } finally {
    deliverySubmitting.value = false
  }
}

const handleSave = async () => {
  saving.value = true
  try {
    if (colorChanged.value) {
      await ElMessageBox.confirm(
        t('order.confirmColorChange'),
        t('common.warning'),
        {
          confirmButtonText: t('common.confirm'),
          cancelButtonText: t('common.cancel'),
          type: 'warning'
        }
      )
    }
    
    await saveOrder(orderData)
    ElMessage.success(t('common.operationSuccessful'))
    emit('saved')
    dialogVisible.value = false
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || t('common.operationFailed'))
    }
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
}

// 监听
watch(() => props.orderId, (newId) => {
  if (newId && dialogVisible.value) {
    getDetail()
  }
}, { immediate: true })

watch(dialogVisible, (visible) => {
  if (visible && props.orderId) {
    getDetail()
  }
})
</script>

<style scoped lang="scss">
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
  
  .order-info {
    .order-number {
      font-size: 18px;
      font-weight: bold;
      margin-right: 20px;
    }
    
    .create-time {
      color: #666;
    }
  }
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.accessories-section,
.rights-section,
.insurance-section,
.otr-section {
  h4 {
    margin-bottom: 16px;
    color: #333;
  }
  
  .total-amount {
    margin-top: 16px;
    text-align: right;
    font-weight: bold;
    font-size: 16px;
    color: #e6a23c;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .price-info {
    .price-item {
      margin-bottom: 8px;
      
      .label {
        margin-right: 8px;
        color: #666;
      }
      
      .value {
        font-weight: bold;
        font-size: 16px;
        color: #e6a23c;
      }
    }
  }
  
  .action-buttons {
    .el-button {
      margin-left: 10px;
    }
  }
}
</style> 