<template>
  <el-dialog
    v-model="dialogVisible"
    :title="t('order.selectRights')"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="rights-container" v-loading="loading">
      <el-table
        :data="availableRights"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="rightType" :label="t('order.rightType')" width="120" />
        <el-table-column prop="description" :label="t('order.description')" min-width="200" />
        <el-table-column prop="discountAmount" :label="t('order.discountAmount')" width="120">
          <template #default="scope">
            <span class="discount-amount">
              -RM {{ formatCurrency(scope.row.discountAmount) }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ t('common.cancel') }}</el-button>
        <el-button type="primary" @click="handleConfirm">{{ t('common.confirm') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { getAvailableRights } from '@/api/modules/order'
import type { OrderRight } from '@/types/order.d'

const { t } = useI18n()

interface Props {
  modelValue: boolean
  existingRights?: OrderRight[]
}

const props = withDefaults(defineProps<Props>(), {
  existingRights: () => []
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [rights: OrderRight[]]
}>()

const loading = ref(false)
const availableRights = ref<OrderRight[]>([])
const selectedRights = ref<OrderRight[]>([])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const fetchAvailableRights = async () => {
  loading.value = true
  try {
    const response = await getAvailableRights({
      page: 1,
      pageSize: 50
    })
    // 将 AvailableRight 类型映射为 OrderRight 类型
    availableRights.value = response.list.map(item => ({
      id: item.id,
      rightCode: item.rightCode,
      rightName: item.rightName,
      mode: item.rightMode, // rightMode 映射为 mode
      discountAmount: item.discountAmount,
      effectiveDate: item.effectiveDate,
      expiryDate: item.expiryDate
    }))
  } catch (error) {
    console.error('获取可选权益失败:', error)
    ElMessage.error(t('common.operationFailed'))
  } finally {
    loading.value = false
  }
}

const handleSelectionChange = (selection: OrderRight[]) => {
  selectedRights.value = selection
}

const handleConfirm = () => {
  emit('confirm', selectedRights.value)
  dialogVisible.value = false
  selectedRights.value = []
}

const formatCurrency = (amount: number) => {
  return amount.toLocaleString('en-MY', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

watch(() => dialogVisible.value, (visible) => {
  if (visible) {
    fetchAvailableRights()
    selectedRights.value = []
  }
})

onMounted(() => {
  fetchAvailableRights()
})
</script>

<style lang="scss" scoped>
.rights-container {
  .discount-amount {
    color: #f56c6c;
    font-weight: 600;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
