<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="t('order.orderDetail')" 
    width="80%"
    top="5vh"
    :close-on-click-modal="false"
    class="order-detail-dialog"
  >
    <el-scrollbar height="70vh" v-loading="loading">
      <div class="order-detail-container">
        <!-- 订单基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h3>{{ t('order.basicInfo') }}</h3>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.orderNumber') }}:</label>
                <span>{{ orderData.orderNumber }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.orderStatus') }}:</label>
                <el-tag :type="getStatusType(orderData.orderStatus)">
                  {{ t(`order.status.${orderData.orderStatus}`) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.createTime') }}:</label>
                <span>{{ formatDateTime(orderData.createTime) }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 车辆信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h3>{{ t('order.vehicleInfo') }}</h3>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.model') }}:</label>
                <span>{{ orderData.model }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.variant') }}:</label>
                <span>{{ orderData.variant }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.color') }}:</label>
                <span>{{ orderData.color }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 客户信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h3>{{ t('order.customerInfo') }}</h3>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.customerName') }}:</label>
                <span>{{ orderData.customerName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.customerPhone') }}:</label>
                <span>{{ orderData.customerPhone }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.buyerName') }}:</label>
                <span>{{ orderData.buyerName }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 支付信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h3>{{ t('order.paymentInfo') }}</h3>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.totalAmount') }}:</label>
                <span class="amount highlight">RM {{ formatCurrency(orderData.totalAmount) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.deposit') }}:</label>
                <span class="amount">RM {{ formatCurrency(orderData.deposit) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>{{ t('order.finalPayment') }}:</label>
                <span class="amount">RM {{ formatCurrency(orderData.finalPayment) }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ t('common.close') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { getOrderDetail } from '@/api/modules/order'
import type { Order } from '@/types/order.d'

const { t } = useI18n()

interface Props {
  modelValue: boolean
  orderId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const loading = ref(false)

const orderData = reactive<Order>({
  id: '',
  orderNumber: '',
  customerId: '',
  customerName: '',
  customerPhone: '',
  buyerName: '',
  buyerPhone: '',
  buyerIdType: '',
  buyerIdNumber: '',
  buyerEmail: '',
  buyerAddress: '',
  buyerState: '',
  buyerCity: '',
  buyerPostalCode: '',
  buyerType: '',
  storeId: '',
  storeName: '',
  storeState: '',
  storeCity: '',
  salesConsultantId: '',
  salesConsultantName: '',
  model: '',
  variant: '',
  color: '',
  vin: '',
  salesSubtotal: 0,
  numberPlatesFee: 0,
  totalAmount: 0,
  deposit: 0,
  finalPayment: 0,
  loanAmount: 0,
  loanTerm: 0,
  paymentMethod: 'full_payment',
  loanApprovalStatus: '',
  orderStatus: 'confirmed',
  paymentStatus: 'pending_deposit',
  approvalStatus: 'approved',
  insuranceStatus: 'pending',
  jpjRegistrationStatus: 'pending',
  accessories: [],
  rights: [],
  insurance: undefined,
  otrFees: [],
  createTime: '',
  updateTime: '',
  invoicingType: '',
  invoicingName: '',
  invoicingPhone: '',
  invoicingAddress: ''
})

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const fetchOrderDetail = async () => {
  if (!props.orderId) return
  
  loading.value = true
  try {
    const data = await getOrderDetail(props.orderId)
    Object.assign(orderData, data)
  } catch (error) {
    ElMessage.error(t('common.operationFailed'))
  } finally {
    loading.value = false
  }
}

const formatCurrency = (amount: number) => {
  return amount.toLocaleString('en-MY', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString()
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'confirmed': 'success',
    'pending_delivery': 'warning',
    'completed': 'success',
    'cancelled': 'danger',
    'pending_deposit': 'warning'
  }
  return typeMap[status] || 'info'
}

watch(() => props.orderId, (newId) => {
  if (newId && dialogVisible.value) {
    fetchOrderDetail()
  }
})

watch(() => dialogVisible.value, (visible) => {
  if (visible && props.orderId) {
    fetchOrderDetail()
  }
})
</script>

<style lang="scss" scoped>
.order-detail-dialog {
  .order-detail-container {
    .detail-card {
      margin-bottom: 20px;
      border: 1px solid #e4e7ed;

      .card-header {
        h3 {
          margin: 0;
          color: #303133;
          font-size: 18px;
          font-weight: 600;
        }
      }

      .info-item {
        margin-bottom: 16px;
        
        label {
          display: inline-block;
          width: 140px;
          color: #606266;
          font-weight: 500;
          vertical-align: top;
        }

        span {
          color: #303133;
          
          &.amount {
            font-weight: 600;
            color: #409eff;

            &.highlight {
              color: #f56c6c;
              font-size: 18px;
            }
          }
        }
      }
    }
  }
}
</style>