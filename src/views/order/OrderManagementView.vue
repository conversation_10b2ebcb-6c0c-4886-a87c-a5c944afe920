<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('order.orderManagement') }}</h1>

    <!-- 统计概览 -->
    <el-card class="mb-20 stats-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('order.orderOverview') }}</span>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ orderStats.monthlyOrderCount }}</div>
            <div class="stat-label">{{ t('order.monthlyOrderCount') }}</div>
            <div class="stat-growth positive">
              <el-icon><TrendCharts /></el-icon>
              {{ t('order.growth') }} {{ orderStats.monthlyOrderGrowth }}%
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ orderStats.todayOrderCount }}</div>
            <div class="stat-label">{{ t('order.todayOrderCount') }}</div>
            <div class="stat-growth positive">
              <el-icon><TrendCharts /></el-icon>
              {{ t('order.growth') }} {{ orderStats.todayOrderGrowth }}%
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ orderStats.topStore }}</div>
            <div class="stat-label">{{ t('order.topStore') }}</div>
            <div class="stat-growth">
              {{ t('order.thisMonthOrders') }}: {{ orderStats.topStoreOrderCount }}
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ orderStats.hotModel }}</div>
            <div class="stat-label">{{ t('order.hotModel') }}</div>
            <div class="stat-growth">
              {{ t('order.thisMonthOrders') }}: {{ orderStats.hotModelOrderCount }}
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 筛选条件 -->
    <el-card class="mb-20 search-card">
      <template #header>
        <div class="card-header">
          <span>{{ t('order.filterConditions') }}</span>
        </div>
      </template>
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('order.storeName')">
              <el-select
                v-model="searchParams.storeFilter"
                :placeholder="t('order.storeNamePlaceholder')"
                clearable
              >
                <el-option label="吉隆坡中央门店" value="吉隆坡中央门店" />
                <el-option label="雪兰莪门店" value="雪兰莪门店" />
                <el-option label="槟城门店" value="槟城门店" />
                <el-option label="柔佛门店" value="柔佛门店" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('order.model')">
              <el-select
                v-model="searchParams.modelFilter"
                :placeholder="t('order.modelPlaceholder')"
                clearable
                @change="handleModelChange"
              >
                <el-option label="Myvi" value="Myvi" />
                <el-option label="Alza" value="Alza" />
                <el-option label="Axia" value="Axia" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('order.variant')">
              <el-select
                v-model="searchParams.variantFilter"
                :placeholder="variantPlaceholder"
                clearable
                :disabled="!searchParams.modelFilter"
              >
                <el-option
                  v-for="variant in availableVariants"
                  :key="variant"
                  :label="variant"
                  :value="variant"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('order.orderStatus')">
              <el-select
                v-model="searchParams.orderStatus"
                :placeholder="t('order.orderStatusPlaceholder')"
                clearable
              >
                <el-option
                  v-for="(label, key) in $t('order.orderStatuses')"
                  :key="key"
                  :label="label"
                  :value="key"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('order.paymentStatus')">
              <el-select
                v-model="searchParams.paymentStatus"
                :placeholder="t('order.paymentStatusPlaceholder')"
                clearable
              >
                <el-option
                  v-for="(label, key) in $t('order.paymentStatuses')"
                  :key="key"
                  :label="label"
                  :value="key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('order.orderNumber')">
              <el-input
                v-model="searchParams.orderNumber"
                :placeholder="t('order.orderNumberPlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('order.createDateRange')">
              <el-date-picker
                v-model="searchParams.dateRange"
                type="daterange"
                :placeholder="t('order.createDateRangePlaceholder')"
                :start-placeholder="t('common.startDate')"
                :end-placeholder="t('common.endDate')"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" class="buttons-right-aligned">
            <el-button type="primary" :icon="Search" @click="handleSearch">
              {{ t('common.search') }}
            </el-button>
            <el-button @click="resetSearch">{{ t('common.reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <el-button :icon="Download" @click="handleExport" :loading="exporting">
        {{ exporting ? t('common.exporting') : t('order.exportExcel') }}
      </el-button>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        stripe
        :default-sort="{ prop: 'createTime', order: 'descending' }"
      >
        <el-table-column type="index" :label="t('common.index')" width="60" />
        <el-table-column prop="orderNumber" :label="t('order.orderNumber')" min-width="140" />
        <el-table-column prop="storeName" :label="t('order.storeName')" min-width="120" />
        <el-table-column prop="createTime" :label="t('order.createTime')" min-width="160" sortable />
        <el-table-column prop="customerName" :label="t('order.customerName')" min-width="100" />
        <el-table-column prop="customerPhone" :label="t('order.customerPhone')" min-width="120" />
        <el-table-column prop="buyerName" :label="t('order.buyerName')" min-width="100" />
        <el-table-column prop="buyerPhone" :label="t('order.buyerPhone')" min-width="120" />
        <el-table-column prop="buyerType" :label="t('order.buyerType')" min-width="100" />
        <el-table-column prop="model" :label="t('order.model')" min-width="80" />
        <el-table-column prop="variant" :label="t('order.variant')" min-width="120" />
        <el-table-column prop="color" :label="t('order.color')" min-width="80" />
        <el-table-column prop="vin" :label="t('order.vin')" min-width="140" />
        <el-table-column prop="paymentMethod" :label="t('order.paymentMethod')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.paymentMethod === 'full_payment' ? 'success' : 'info'">
              {{ t(`order.paymentMethods.${row.paymentMethod}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="loanApprovalStatus" :label="t('order.loanApprovalStatus')" min-width="120">
          <template #default="{ row }">
            <span v-if="row.paymentMethod === 'installment'">{{ row.loanApprovalStatus || '-' }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus" :label="t('order.orderStatus')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.orderStatus)">
              {{ t(`order.orderStatuses.${row.orderStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="approvalStatus" :label="t('order.approvalStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getApprovalStatusType(row.approvalStatus)">
              {{ t(`order.approvalStatuses.${row.approvalStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" :label="t('order.paymentStatus')" min-width="120">
          <template #default="{ row }">
            <el-tag :type="getPaymentStatusType(row.paymentStatus)">
              {{ t(`order.paymentStatuses.${row.paymentStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="insuranceStatus" :label="t('order.insuranceStatus')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getInsuranceStatusType(row.insuranceStatus)">
              {{ t(`order.insuranceStatuses.${row.insuranceStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="jpjRegistrationStatus" :label="t('order.jpjRegistrationStatus')" min-width="140">
          <template #default="{ row }">
            <el-tag :type="getJpjStatusType(row.jpjRegistrationStatus)">
              {{ t(`order.jpjRegistrationStatuses.${row.jpjRegistrationStatus}`) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="t('common.operations')" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="Edit" link @click="handleEdit(row)">
              {{ t('order.editOrder') }}
            </el-button>
            <el-button type="info" :icon="View" link @click="handleViewDetail(row)">
              {{ t('common.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单编辑模态框 -->
    <OrderEditDialog
      v-model="editDialogVisible"
      :order-id="currentOrderId"
      @saved="handleOrderSaved"
    />

    <!-- 订单详情模态框 -->
    <OrderDetailDialog
      v-model="detailDialogVisible"
      :order-id="currentOrderId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Search, Download, Edit, View, TrendCharts } from '@element-plus/icons-vue'
import { getOrderList, getOrderStats, exportOrderData } from '@/api/modules/order'
import type { OrderListItem, OrderListParams, OrderStats } from '@/types/order.d'
import OrderEditDialog from './components/OrderEditDialog.vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'

const { t } = useI18n()

// 数据
const loading = ref(false)
const exporting = ref(false)
const tableData = ref<OrderListItem[]>([])
const orderStats = ref<OrderStats>({
  monthlyOrderCount: 0,
  monthlyOrderGrowth: 0,
  todayOrderCount: 0,
  todayOrderGrowth: 0,
  topStore: '',
  topStoreOrderCount: 0,
  hotModel: '',
  hotModelOrderCount: 0,
  pendingDeliveryCount: 0
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索参数
const searchParams = reactive<OrderListParams>({
  page: 1,
  pageSize: 10,
  storeFilter: '',
  modelFilter: '',
  variantFilter: '',
  orderStatus: undefined,
  paymentStatus: undefined,
  orderNumber: '',
  dateRange: undefined
})

// 模态框控制
const editDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentOrderId = ref('')

// 车型配置映射
const variantMap = {
  'Myvi': ['1.3 Standard G', '1.5 Premium X'],
  'Alza': ['1.5 Standard', '1.5 Premium'],
  'Axia': ['1.0 E', '1.0 G']
}

// 计算属性
const availableVariants = computed(() => {
  if (!searchParams.modelFilter) return []
  return variantMap[searchParams.modelFilter as keyof typeof variantMap] || []
})

const variantPlaceholder = computed(() => {
  return searchParams.modelFilter ? t('order.variantPlaceholder') : t('order.variantPlaceholder')
})

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchParams,
      page: currentPage.value,
      pageSize: pageSize.value
    }
    const response = await getOrderList(params)
    tableData.value = response.data
    total.value = response.total
  } catch (error) {
    ElMessage.error(t('common.operationFailed'))
  } finally {
    loading.value = false
  }
}

const fetchStats = async () => {
  try {
    orderStats.value = await getOrderStats()
  } catch (error) {
    ElMessage.error(t('common.operationFailed'))
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

const resetSearch = () => {
  Object.assign(searchParams, {
    storeFilter: '',
    modelFilter: '',
    variantFilter: '',
    orderStatus: undefined,
    paymentStatus: undefined,
    orderNumber: '',
    dateRange: undefined
  })
  currentPage.value = 1
  fetchData()
}

const handleModelChange = () => {
  searchParams.variantFilter = ''
}

const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  searchParams.pageSize = newSize
  fetchData()
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  searchParams.page = newPage
  fetchData()
}

const handleEdit = (row: OrderListItem) => {
  currentOrderId.value = row.id
  editDialogVisible.value = true
}

const handleViewDetail = (row: OrderListItem) => {
  currentOrderId.value = row.id
  detailDialogVisible.value = true
}

const handleOrderSaved = () => {
  fetchData()
}

const handleExport = async () => {
  exporting.value = true
  try {
    const blob = await exportOrderData(searchParams)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `orders_${new Date().getTime()}.csv`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success(t('common.operationSuccessful'))
  } catch (error) {
    ElMessage.error(t('common.operationFailed'))
  } finally {
    exporting.value = false
  }
}

// 状态样式方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'confirmed': 'success',
    'pending_delivery': 'warning',
    'completed': 'success',
    'cancelled': 'danger',
    'pending_deposit': 'warning'
  }
  return typeMap[status] || 'info'
}

const getApprovalStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'approved': 'success',
    'pending': 'warning',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

const getPaymentStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'fully_paid': 'success',
    'deposit_paid': 'success',
    'pending_final': 'warning',
    'pending_deposit': 'danger'
  }
  return typeMap[status] || 'info'
}

const getInsuranceStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'completed': 'success',
    'pending': 'warning',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

const getJpjStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'completed': 'success',
    'pending': 'warning',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

// 生命周期
onMounted(() => {
  fetchStats()
  fetchData()
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.buttons-right-aligned {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.stats-card {
  .stat-item {
    text-align: center;

    .stat-value {
      font-size: 28px;
      font-weight: bold;
      color: $primary-color;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 4px;
    }

    .stat-growth {
      font-size: 12px;
      color: #999;

      &.positive {
        color: #67c23a;
      }

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap;
  }
}
</style>