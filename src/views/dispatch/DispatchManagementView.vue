<template>
  <div class="page-container dispatch-management-page">
    <h1 class="page-title">{{ t('pageTitle') }}</h1>

    <!-- 筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('workOrderNo')">
              <el-input v-model="searchParams.workOrderNo" :placeholder="t('workOrderNoPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('priority')">
              <el-select v-model="searchParams.priority" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('priorityUrgent')" value="urgent" />
                <el-option :label="t('priorityNormal')" value="normal" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('workOrderType')">
              <el-select v-model="searchParams.workOrderType" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('typeRepair')" value="repair" />
                <el-option :label="t('typeMaintenance')" value="maintenance" />
                <el-option :label="t('typeInsurance')" value="insurance" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="isTechnicianManager">
            <el-form-item :label="t('assignmentStatus')">
              <el-select v-model="searchParams.assignmentStatus" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('status.pendingAssignment')" value="pending" />
                <el-option :label="t('status.assigned')" value="assigned" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('workOrderStatus')">
              <el-select v-model="searchParams.workOrderStatus" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('status.pendingAssignment')" value="pendingAssignment" v-if="isTechnicianManager" />
                <el-option :label="t('status.pendingStart')" value="pendingStart" />
                <el-option :label="t('status.inProgress')" value="inProgress" />
                <el-option :label="t('status.pendingQualityInspection')" value="pendingQualityInspection" v-if="isTechnicianManager" />
                <el-option :label="t('status.pendingRework')" value="pendingRework" />
                <el-option :label="t('status.completed')" value="completed" />
                <el-option :label="t('status.additionalItemsPendingConfirmation')" value="additionalPendingConfirmation" />
                <el-option :label="t('status.waitingForParts')" value="waitingForParts" v-if="isTechnicianManager" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('workOrderCreationTime')">
              <el-date-picker
                v-model="searchParams.creationTimeRange"
                type="datetimerange"
                :start-placeholder="t('startTime')"
                :end-placeholder="t('endTime')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerSource')">
              <el-select v-model="searchParams.customerSource" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('source.appointment')" value="appointment" />
                <el-option :label="t('source.walkIn')" value="walkIn" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('repairmanName')">
              <el-input v-model="searchParams.repairmanName" :placeholder="t('repairmanNamePlaceholder')" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('licensePlateNumber')">
              <el-input v-model="searchParams.licensePlateNumber" :placeholder="t('licensePlateNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('serviceAdvisor')">
              <el-select v-model="searchParams.serviceAdvisor" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option label="张三" value="zhangsan" />
                <el-option label="李四" value="lisi" />
                <el-option label="王五" value="wangwu" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="isTechnicianManager">
            <el-form-item :label="t('technician')">
              <el-select v-model="searchParams.technician" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option label="技师A-王强" value="wangqiang" />
                <el-option label="技师B-张伟" value="zhangwei" />
                <el-option label="技师C-李明" value="liming" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button :icon="Refresh" @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 列表控制区域 -->
    <el-card class="mb-20 operation-card">
      <div class="list-control-area">
        <el-button type="primary" :icon="Download" @click="handleExport">{{ tc('export') }}</el-button>
      </div>
    </el-card>

    <!-- 数据列表区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" style="width: 100%" :row-class-name="tableRowClassName">
        <el-table-column type="index" :label="tc('index')" width="50" />
        <el-table-column prop="workOrderNo" :label="t('workOrderNo')" min-width="120" />
        <el-table-column prop="priority" :label="t('priority')" min-width="60">
          <template #default="{ row }">
            <el-tag v-if="row.priority === 'urgent'" type="danger">{{ t('priorityUrgent') }}</el-tag>
            <el-tag v-else type="info">{{ t('priorityNormal') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="workOrderType" :label="t('workOrderType')" min-width="60">
          <template #default="{ row }">
            <el-tag v-if="row.workOrderType === 'repair'" type="primary">{{ t('typeRepair') }}</el-tag>
            <el-tag v-else-if="row.workOrderType === 'maintenance'" type="success">{{ t('typeMaintenance') }}</el-tag>
            <el-tag v-else-if="row.workOrderType === 'insurance'" type="warning">{{ t('typeInsurance') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="workOrderStatus" :label="t('workOrderStatus')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.workOrderStatus)">{{ t(`status.${row.workOrderStatus}`) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creationTime" :label="t('workOrderCreationTime')" min-width="150" />
        <el-table-column prop="customerSource" :label="t('customerSource')" min-width="80">
          <template #default="{ row }">
            {{ t(`source.${row.customerSource}`) }}
          </template>
        </el-table-column>
        <el-table-column prop="repairmanName" :label="t('repairmanName')" min-width="80" />
        <el-table-column prop="repairmanPhone" :label="t('repairmanPhone')" min-width="100" />
        <el-table-column prop="licensePlateNumber" :label="t('licensePlateNumber')" min-width="90" />
        <el-table-column prop="vehicleModel" :label="t('vehicleModel')" min-width="100" />
        <el-table-column prop="configuration" :label="t('configuration')" min-width="80" />
        <el-table-column prop="color" :label="t('color')" min-width="60" />
        <el-table-column prop="vehicleAge" :label="t('vehicleAge')" min-width="60">
          <template #default="{ row }">
            {{ row.vehicleAge }}{{ t('months') }}
          </template>
        </el-table-column>
        <el-table-column prop="mileage" :label="t('mileage')" min-width="80">
          <template #default="{ row }">
            {{ row.mileage }}{{ t('km') }}
          </template>
        </el-table-column>
        <el-table-column prop="serviceAdvisor" :label="t('serviceAdvisor')" min-width="80" />
        <el-table-column prop="assignmentStatus" :label="t('assignmentStatus')" min-width="80" v-if="isTechnicianManager">
          <template #default="{ row }">
            <el-tag :type="getAssignmentStatusTagType(row.assignmentStatus)">{{ t(`status.${row.assignmentStatus}`) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="technician" :label="t('technician')" min-width="100" v-if="isTechnicianManager" />
        <el-table-column prop="estimatedWorkHours" :label="t('estimatedWorkHours')" min-width="80" />
        <el-table-column prop="estimatedStartTime" :label="t('estimatedStartTime')" min-width="120" />
        <el-table-column prop="estimatedFinishTime" :label="t('estimatedFinishTime')" min-width="120" />
        <el-table-column prop="actualStartTime" :label="t('actualStartTime')" min-width="120" />
        <el-table-column prop="actualFinishTime" :label="t('actualFinishTime')" min-width="120" />
        <el-table-column :label="tc('operations')" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="Document" link @click="handleDetail(row)">{{ tc('detail') }}</el-button>
            <!-- 技师经理操作 -->
            <template v-if="isTechnicianManager">
              <el-button
                v-if="row.assignmentStatus === 'pendingAssignment' && row.workOrderStatus === 'pendingAssignment'"
                type="primary"
                :icon="UserFilled"
                link
                @click="handleAssign(row)"
              >
                {{ t('assign') }}
              </el-button>
              <el-button
                v-else-if="row.assignmentStatus === 'assigned' && row.workOrderStatus === 'pendingStart'"
                type="primary"
                :icon="Share"
                link
                @click="handleReassign(row)"
              >
                {{ t('reassign') }}
              </el-button>
            </template>
            <!-- 技师操作 -->
            <template v-else-if="isTechnician">
              <el-button
                v-if="row.workOrderStatus === 'pendingStart'"
                type="success"
                :icon="VideoPlay"
                link
                @click="handleStartWork(row)"
              >
                {{ t('startWork') }}
              </el-button>
              <el-button
                v-else-if="row.workOrderStatus === 'inProgress' && !row.isPaused"
                type="warning"
                :icon="VideoPause"
                link
                @click="handlePauseResume(row)"
              >
                {{ t('pause') }}
              </el-button>
              <el-button
                v-else-if="row.workOrderStatus === 'inProgress' && row.isPaused"
                type="success"
                :icon="VideoPlay"
                link
                @click="handlePauseResume(row)"
              >
                {{ t('resume') }}
              </el-button>
              <el-button
                v-else-if="row.workOrderStatus === 'inProgress'"
                type="danger"
                :icon="CircleCheck"
                link
                @click="handleCompleteWork(row)"
              >
                {{ t('completeWork') }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="t('detailDialogTitle')"
      width="800px"
      class="dispatch-detail-dialog"
    >
      <span>{{ t('detailDialogContent') }}</span>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="detailDialogVisible = false">{{ tc('close') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分配弹窗 -->
    <el-dialog
      v-model="assignDialogVisible"
      :title="t('assignDialogTitle')"
      width="800px"
      class="dispatch-assign-dialog"
    >
      <el-form :model="assignForm" label-position="top" class="dialog-form-modern">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('workOrderNo')">
              <el-input v-model="assignForm.workOrderNo" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('assignedTechnician')">
              <el-select v-model="assignForm.technicianId" :placeholder="tc('pleaseSelect')" clearable>
                <el-option label="技师A-王强" value="techA" />
                <el-option label="技师B-张伟" value="techB" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('estimatedStartTime')">
              <el-date-picker
                v-model="assignForm.estimatedStartTime"
                type="datetime"
                :placeholder="tc('pleaseSelect')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('estimatedFinishTime')">
              <el-input v-model="assignForm.estimatedFinishTime" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="t('assignmentNotes')">
              <el-input v-model="assignForm.notes" type="textarea" :rows="3" :placeholder="t('assignmentNotesPlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="assignDialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="confirmAssign">{{ tc('confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重新分配弹窗 -->
    <el-dialog
      v-model="reassignDialogVisible"
      :title="t('reassignDialogTitle')"
      width="800px"
      class="dispatch-reassign-dialog"
    >
      <el-form :model="reassignForm" label-position="top" class="dialog-form-modern">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('workOrderNo')">
              <el-input v-model="reassignForm.workOrderNo" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('originalTechnician')">
              <el-input v-model="reassignForm.originalTechnician" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('newTechnician')">
              <el-select v-model="reassignForm.newTechnicianId" :placeholder="tc('pleaseSelect')" clearable>
                <el-option label="技师A-王强" value="techA" />
                <el-option label="技师B-张伟" value="techB" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('estimatedStartTime')">
              <el-date-picker
                v-model="reassignForm.estimatedStartTime"
                type="datetime"
                :placeholder="tc('pleaseSelect')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('estimatedFinishTime')">
              <el-input v-model="reassignForm.estimatedFinishTime" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="t('reassignmentReason')">
              <el-input v-model="reassignForm.reason" type="textarea" :rows="3" :placeholder="t('reassignmentReasonPlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="reassignDialogVisible = false">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="confirmReassign">{{ tc('confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Search, Refresh, Download, Document, UserFilled, Share, VideoPause, VideoPlay, CircleCheck } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const { t } = useI18n()

// 模拟角色判断 (实际项目中应从用户认证信息中获取)
const isTechnicianManager = ref(true) // 假设当前是技师经理
const isTechnician = computed(() => !isTechnicianManager.value) // 技师经理和技师互斥

// 定义工单数据类型
interface WorkOrderItem {
  id: number
  workOrderNo: string
  priority: 'urgent' | 'normal'
  workOrderType: 'repair' | 'maintenance' | 'insurance'
  workOrderStatus: string
  creationTime: string
  customerSource: 'appointment' | 'walkIn'
  repairmanName: string
  repairmanPhone: string
  licensePlateNumber: string
  vehicleModel: string
  configuration: string
  color: string
  vehicleAge: number
  mileage: number
  serviceAdvisor: string
  assignmentStatus: 'pendingAssignment' | 'assigned'
  technician: string
  estimatedWorkHours: number
  estimatedStartTime: string
  estimatedFinishTime: string
  actualStartTime: string
  actualFinishTime: string
  isPaused: boolean
}

// 定义搜索参数类型
interface SearchParams {
  workOrderNo: string
  priority: string
  workOrderType: string
  assignmentStatus: string
  workOrderStatus: string
  creationTimeRange: string[]
  customerSource: string
  repairmanName: string
  licensePlateNumber: string
  serviceAdvisor: string
  technician: string
}

// 定义分配表单类型
interface AssignForm {
  workOrderNo: string
  technicianId: string
  estimatedStartTime: string
  estimatedFinishTime: string
  notes: string
  currentWorkOrder: WorkOrderItem | null
}

// 定义重新分配表单类型
interface ReassignForm {
  workOrderNo: string
  originalTechnician: string
  newTechnicianId: string
  estimatedStartTime: string
  estimatedFinishTime: string
  reason: string
  currentWorkOrder: WorkOrderItem | null
}

// 筛选条件
const searchParams = reactive<SearchParams>({
  workOrderNo: '',
  priority: '',
  workOrderType: '',
  assignmentStatus: '',
  workOrderStatus: '',
  creationTimeRange: [],
  customerSource: '',
  repairmanName: '',
  licensePlateNumber: '',
  serviceAdvisor: '',
  technician: '',
})

// 表格数据
const loading = ref<boolean>(false)
const tableData = ref<WorkOrderItem[]>([
  {
    id: 1,
    workOrderNo: 'WO20241210001',
    priority: 'urgent',
    workOrderType: 'repair',
    workOrderStatus: 'pendingAssignment',
    creationTime: '2024-12-10 10:00:00',
    customerSource: 'appointment',
    repairmanName: '张三',
    repairmanPhone: '13800138000',
    licensePlateNumber: '粤B12345',
    vehicleModel: 'Model Y',
    configuration: '长续航',
    color: '白色',
    vehicleAge: 24,
    mileage: 30000,
    serviceAdvisor: '李明',
    assignmentStatus: 'pendingAssignment',
    technician: '',
    estimatedWorkHours: 3.5,
    estimatedStartTime: '',
    estimatedFinishTime: '',
    actualStartTime: '',
    actualFinishTime: '',
    isPaused: false,
  },
  {
    id: 2,
    workOrderNo: 'WO20241210002',
    priority: 'normal',
    workOrderType: 'maintenance',
    workOrderStatus: 'pendingStart',
    creationTime: '2024-12-10 11:30:00',
    customerSource: 'walkIn',
    repairmanName: '李四',
    repairmanPhone: '13912345678',
    licensePlateNumber: '沪A67890',
    vehicleModel: 'Model 3',
    configuration: '标准续航',
    color: '黑色',
    vehicleAge: 12,
    mileage: 15000,
    serviceAdvisor: '王强',
    assignmentStatus: 'assigned',
    technician: '技师A-王强',
    estimatedWorkHours: 2.0,
    estimatedStartTime: '2024-12-10 14:00:00',
    estimatedFinishTime: '2024-12-10 16:00:00',
    actualStartTime: '',
    actualFinishTime: '',
    isPaused: false,
  },
  {
    id: 3,
    workOrderNo: 'WO20241210003',
    priority: 'normal',
    workOrderType: 'repair',
    workOrderStatus: 'inProgress',
    creationTime: '2024-12-10 09:00:00',
    customerSource: 'appointment',
    repairmanName: '王五',
    repairmanPhone: '13787654321',
    licensePlateNumber: '苏E54321',
    vehicleModel: 'Model X',
    configuration: '高性能',
    color: '银色',
    vehicleAge: 36,
    mileage: 50000,
    serviceAdvisor: '赵六',
    assignmentStatus: 'assigned',
    technician: '技师B-张伟',
    estimatedWorkHours: 5.0,
    estimatedStartTime: '2024-12-10 10:00:00',
    estimatedFinishTime: '2024-12-10 15:00:00',
    actualStartTime: '2024-12-10 10:05:00',
    actualFinishTime: '',
    isPaused: true,
  },
])

// 分页信息类型
interface Pagination {
  currentPage: number
  pageSize: number
  total: number
}

// 分页信息
const pagination = reactive<Pagination>({
  currentPage: 1,
  pageSize: 10,
  total: tableData.value.length,
})

// 模态框可见性
const detailDialogVisible = ref<boolean>(false)
const assignDialogVisible = ref<boolean>(false)
const reassignDialogVisible = ref<boolean>(false)

// 分配表单数据
const assignForm = reactive<AssignForm>({
  workOrderNo: '',
  technicianId: '',
  notes: '',
  estimatedStartTime: '',
  estimatedFinishTime: '',
  currentWorkOrder: null,
})

// 重新分配表单数据
const reassignForm = reactive<ReassignForm>({
  workOrderNo: '',
  originalTechnician: '',
  newTechnicianId: '',
  reason: '',
  estimatedStartTime: '',
  estimatedFinishTime: '',
  currentWorkOrder: null,
})

// 模拟获取数据
const fetchData = () => {
  loading.value = true
  // 实际项目中这里会调用API获取数据
  setTimeout(() => {
    let filteredData = tableData.value // 假设这里是后端返回的全部数据
    // 模拟筛选逻辑 (实际由后端完成)
    if (searchParams.workOrderNo) {
      filteredData = filteredData.filter(item => item.workOrderNo.includes(searchParams.workOrderNo))
    }
    if (searchParams.priority) {
      filteredData = filteredData.filter(item => item.priority === searchParams.priority)
    }
    // ... 其他筛选条件
    if (isTechnician.value) { // 技师角色只显示分配给自己的工单
      filteredData = filteredData.filter(item => item.technician === '技师A-王强' || item.technician === '技师B-张伟') // 假设当前技师是王强或张伟
      const technicianStatuses = ['pendingStart', 'inProgress', 'pendingRework', 'completed', 'additionalPendingConfirmation']
      filteredData = filteredData.filter(item => technicianStatuses.includes(item.workOrderStatus))
    }

    pagination.total = filteredData.length
    // 模拟分页
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    tableData.value = filteredData.slice(start, end)

    loading.value = false
  }, 500)
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchData()
  ElMessage.success(t('common.searchSuccess'))
}

// 重置
const resetSearch = () => {
  Object.assign(searchParams, {
    workOrderNo: '',
    priority: '',
    workOrderType: '',
    assignmentStatus: '',
    workOrderStatus: '',
    creationTimeRange: [],
    customerSource: '',
    repairmanName: '',
    licensePlateNumber: '',
    serviceAdvisor: '',
    technician: '',
  })
  pagination.currentPage = 1
  fetchData()
  ElMessage.info(t('common.resetSuccess'))
}

// 导出
const handleExport = () => {
  ElMessage.success(t('common.exportSuccess'))
  // 实际导出逻辑
}

// 改变每页显示数量
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  fetchData()
}

// 改变当前页码
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  fetchData()
}

// 获取工单状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'pendingAssignment': return 'info'
    case 'pendingStart': return 'warning'
    case 'inProgress': return '' // 默认蓝色
    case 'pendingQualityInspection': return 'warning'
    case 'pendingRework': return 'danger'
    case 'completed': return 'success'
    case 'additionalPendingConfirmation': return 'info'
    case 'waitingForParts': return 'info'
    default: return 'info'
  }
}

// 获取分配状态标签类型
const getAssignmentStatusTagType = (status: string) => {
  switch (status) {
    case 'pendingAssignment': return 'info'
    case 'assigned': return 'success'
    default: return 'info'
  }
}

// 表格行样式
const tableRowClassName = ({ row }: { row: WorkOrderItem }) => {
  if (row.priority === 'urgent') {
    return 'urgent-row'
  }
  return ''
}

// 详情弹窗
const handleDetail = (row: WorkOrderItem) => {
  console.log('查看详情:', row)
  detailDialogVisible.value = true
}

// 分配弹窗
const handleAssign = (row: WorkOrderItem) => {
  assignForm.workOrderNo = row.workOrderNo
  assignForm.currentWorkOrder = row
  assignDialogVisible.value = true
}

// 确认分配
const confirmAssign = () => {
  // 实际分配逻辑，调用API
  console.log('确认分配:', assignForm)
  ElMessage.success(t('dispatch.assignSuccess'))
  assignDialogVisible.value = false
  fetchData() // 刷新列表
}

// 重新分配弹窗
const handleReassign = (row: WorkOrderItem) => {
  reassignForm.workOrderNo = row.workOrderNo
  reassignForm.originalTechnician = row.technician
  reassignForm.currentWorkOrder = row
  reassignDialogVisible.value = true
}

// 确认重新分配
const confirmReassign = () => {
  // 实际重新分配逻辑，调用API
  console.log('确认重新分配:', reassignForm)
  ElMessage.success(t('dispatch.reassignSuccess'))
  reassignDialogVisible.value = false
  fetchData() // 刷新列表
}

// 技师开工
const handleStartWork = (row: WorkOrderItem) => {
  ElMessage.success(t('dispatch.startWorkSuccess') + row.workOrderNo)
  // 实际开工逻辑，更新工单状态和实际开工时间
  row.workOrderStatus = 'inProgress'
  row.actualStartTime = new Date().toLocaleString()
}

// 技师暂停/恢复
const handlePauseResume = (row: WorkOrderItem) => {
  if (row.isPaused) {
    ElMessage.success(t('dispatch.resumeWorkSuccess') + row.workOrderNo)
  } else {
    ElMessage.warning(t('dispatch.pauseWorkSuccess') + row.workOrderNo)
  }
  row.isPaused = !row.isPaused // 切换暂停状态
}

// 技师完工
const handleCompleteWork = (row: WorkOrderItem) => {
  ElMessage.success(t('dispatch.completeWorkSuccess') + row.workOrderNo)
  // 实际完工逻辑，更新工单状态和实际完工时间
  row.workOrderStatus = 'pendingQualityInspection' // 完工后进入待质检
  row.actualFinishTime = new Date().toLocaleString()
}

// 初始加载数据
fetchData()
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card, .operation-card, .table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-right: 20px; // 表单项右侧间距
    margin-bottom: 15px; // 表单项底部间距
    &:last-child {
      margin-right: 0;
    }
  }
}

.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.list-control-area {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-bottom: 10px;
}

// 表格样式
:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap; // 禁止文本换行
  }
  .urgent-row {
    background-color: #fff2f0; // 紧急工单浅红色背景
  }
  .el-button + .el-button {
    margin-left: 8px; // 调整操作按钮间距
  }
}


.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-form-modern {
  .el-form-item {
    margin-bottom: 20px; // 增加表单项垂直间距
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  .el-button {
    margin-left: 10px;
  }
}
</style>
