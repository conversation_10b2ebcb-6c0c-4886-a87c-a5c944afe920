<template>
  <div class="page-container dispatch-management-page">
    <h1 class="page-title">{{ t('pageTitle') }}</h1>

    <!-- 筛选区域 -->
    <el-card class="mb-20 search-card">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('workOrderNo')">
              <el-input v-model="searchParams.workOrderNo" :placeholder="t('workOrderNoPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('priority')">
              <el-select v-model="searchParams.priority" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('priorityUrgent')" value="urgent" />
                <el-option :label="t('priorityNormal')" value="normal" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('workOrderType')">
              <el-select v-model="searchParams.workOrderType" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('typeRepair')" value="repair" />
                <el-option :label="t('typeMaintenance')" value="maintenance" />
                <el-option :label="t('typeInsurance')" value="insurance" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="isTechnicianManager">
            <el-form-item :label="t('assignmentStatus')">
              <el-select v-model="searchParams.assignmentStatus" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('status.pendingAssignment')" value="pending_assignment" />
                <el-option :label="t('status.assigned')" value="assigned" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('workOrderStatus')">
              <el-select
                v-model="searchParams.workOrderStatus"
                :placeholder="tc('pleaseSelect')"
                clearable
                multiple
              >
                <el-option
                  v-for="status in workOrderStatuses"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('workOrderCreationTime')">
              <el-date-picker
                v-model="searchParams.creationTimeRange"
                type="datetimerange"
                :start-placeholder="t('startTime')"
                :end-placeholder="t('endTime')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('customerSource')">
              <el-select v-model="searchParams.customerSource" :placeholder="tc('pleaseSelect')" clearable>
                <el-option :label="tc('all')" value="" />
                <el-option :label="t('source.appointment')" value="appointment" />
                <el-option :label="t('source.walkIn')" value="walk_in" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('repairmanName')">
              <el-input v-model="searchParams.repairmanName" :placeholder="t('repairmanNamePlaceholder')" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="t('licensePlateNumber')">
              <el-input v-model="searchParams.licensePlateNumber" :placeholder="t('licensePlateNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('serviceAdvisor')">
              <el-select v-model="searchParams.serviceAdvisor" :placeholder="t('common.pleaseSelect')" clearable>
                <el-option :label="t('common.all')" value="" />
                <el-option label="张三" value="zhangsan" />
                <el-option label="李四" value="lisi" />
                <el-option label="王五" value="wangwu" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="isTechnicianManager">
            <el-form-item :label="t('technician')">
              <el-select v-model="searchParams.technician" :placeholder="t('common.pleaseSelect')" clearable>
                <el-option :label="t('common.all')" value="" />
                <el-option label="技师A-王强" value="wangqiang" />
                <el-option label="技师B-张伟" value="zhangwei" />
                <el-option label="技师C-李明" value="liming" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="buttons-col">
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button :icon="Refresh" @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 列表控制区域 -->
    <el-card class="mb-20 operation-card">
      <div class="list-control-area">
        <el-button type="primary" :icon="Download" @click="handleExport">{{ t('common.export') }}</el-button>
      </div>
    </el-card>

    <!-- 数据列表区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" style="width: 100%" :row-class-name="tableRowClassName">
        <el-table-column type="index" :label="t('common.serialNo')" width="50" />
        <el-table-column prop="workOrderNo" :label="t('dispatch.workOrderNo')" min-width="120" />
        <el-table-column prop="priority" :label="t('dispatch.priority')" min-width="60">
          <template #default="{ row }">
            <el-tag v-if="row.priority === 'urgent'" type="danger">{{ t('dispatch.priorityUrgent') }}</el-tag>
            <el-tag v-else type="info">{{ t('dispatch.priorityNormal') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="workOrderType" :label="t('dispatch.workOrderType')" min-width="60">
          <template #default="{ row }">
            <el-tag v-if="row.workOrderType === 'repair'" type="primary">{{ t('dispatch.typeRepair') }}</el-tag>
            <el-tag v-else-if="row.workOrderType === 'maintenance'" type="success">{{ t('dispatch.typeMaintenance') }}</el-tag>
            <el-tag v-else-if="row.workOrderType === 'insurance'" type="warning">{{ t('dispatch.typeInsurance') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="workOrderStatus" :label="t('dispatch.workOrderStatus')" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.workOrderStatus)">{{ t(`dispatch.status.${row.workOrderStatus}`) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creationTime" :label="t('dispatch.workOrderCreationTime')" min-width="150" />
        <el-table-column prop="customerSource" :label="t('dispatch.customerSource')" min-width="80">
          <template #default="{ row }">
            {{ t(`dispatch.source.${row.customerSource}`) }}
          </template>
        </el-table-column>
        <el-table-column prop="repairmanName" :label="t('dispatch.repairmanName')" min-width="80" />
        <el-table-column prop="repairmanPhone" :label="t('dispatch.repairmanPhone')" min-width="100" />
        <el-table-column prop="licensePlateNumber" :label="t('dispatch.licensePlateNumber')" min-width="90" />
        <el-table-column prop="vehicleModel" :label="t('dispatch.vehicleModel')" min-width="100" />
        <el-table-column prop="configuration" :label="t('dispatch.configuration')" min-width="80" />
        <el-table-column prop="color" :label="t('dispatch.color')" min-width="60" />
        <el-table-column prop="vehicleAge" :label="t('dispatch.vehicleAge')" min-width="60">
          <template #default="{ row }">
            {{ row.vehicleAge }}{{ t('dispatch.months') }}
          </template>
        </el-table-column>
        <el-table-column prop="mileage" :label="t('dispatch.mileage')" min-width="80">
          <template #default="{ row }">
            {{ row.mileage }}{{ t('dispatch.km') }}
          </template>
        </el-table-column>
        <el-table-column prop="serviceAdvisor" :label="t('dispatch.serviceAdvisor')" min-width="80" />
        <el-table-column prop="assignmentStatus" :label="t('dispatch.assignmentStatus')" min-width="80" v-if="isTechnicianManager">
          <template #default="{ row }">
            <el-tag :type="getAssignmentStatusTagType(row.assignmentStatus)">{{ t(`dispatch.status.${row.assignmentStatus}`) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="technician" :label="t('dispatch.technician')" min-width="100" v-if="isTechnicianManager" />
        <el-table-column prop="estimatedWorkHours" :label="t('dispatch.estimatedWorkHours')" min-width="80" />
        <el-table-column prop="estimatedStartTime" :label="t('dispatch.estimatedStartTime')" min-width="120" />
        <el-table-column prop="estimatedFinishTime" :label="t('dispatch.estimatedFinishTime')" min-width="120" />
        <el-table-column prop="actualStartTime" :label="t('dispatch.actualStartTime')" min-width="120" />
        <el-table-column prop="actualFinishTime" :label="t('dispatch.actualFinishTime')" min-width="120" />
        <el-table-column :label="t('common.operations')" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" :icon="Document" link @click="handleDetail(row)">{{ t('common.detail') }}</el-button>
            <!-- 技师经理操作 -->
            <template v-if="isTechnicianManager">
              <el-button
                v-if="row.assignmentStatus === 'pending_assignment' && row.workOrderStatus === 'pending_assignment'"
                type="primary"
                :icon="UserFilled"
                link
                @click="handleAssign(row)"
              >
                {{ t('dispatch.assign') }}
              </el-button>
              <el-button
                v-else-if="row.assignmentStatus === 'assigned' && row.workOrderStatus === 'pending_start'"
                type="primary"
                :icon="Share"
                link
                @click="handleReassign(row)"
              >
                {{ t('dispatch.reassign') }}
              </el-button>
            </template>
            <!-- 技师操作 -->
            <template v-else-if="isTechnician">
              <el-button
                v-if="row.workOrderStatus === 'pending_start'"
                type="success"
                :icon="VideoPlay"
                link
                @click="handleStartWork(row)"
              >
                {{ t('dispatch.startWork') }}
              </el-button>
              <el-button
                v-else-if="row.workOrderStatus === 'in_progress' && !row.isPaused"
                type="warning"
                :icon="VideoPause"
                link
                @click="handlePauseResume(row)"
              >
                {{ t('dispatch.pause') }}
              </el-button>
              <el-button
                v-else-if="row.workOrderStatus === 'in_progress' && row.isPaused"
                type="success"
                :icon="VideoPlay"
                link
                @click="handlePauseResume(row)"
              >
                {{ t('dispatch.resume') }}
              </el-button>
              <el-button
                v-else-if="row.workOrderStatus === 'in_progress'"
                type="danger"
                :icon="CircleCheck"
                link
                @click="handleCompleteWork(row)"
              >
                {{ t('dispatch.completeWork') }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="t('dispatch.detailDialogTitle')"
      width="800px"
      class="dispatch-detail-dialog"
    >
      <span>{{ t('dispatch.detailDialogContent') }}</span>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="detailDialogVisible = false">{{ t('common.close') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分配弹窗 -->
    <el-dialog
      v-model="assignDialogVisible"
      :title="t('dispatch.assignDialogTitle')"
      width="800px"
      class="dispatch-assign-dialog"
    >
      <el-form :model="assignForm" label-position="top" class="dialog-form-modern">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('dispatch.workOrderNo')">
              <el-input v-model="assignForm.workOrderNo" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('dispatch.assignedTechnician')">
              <el-select v-model="assignForm.technicianId" :placeholder="t('common.pleaseSelect')" clearable>
                <el-option label="技师A-王强" value="techA" />
                <el-option label="技师B-张伟" value="techB" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('dispatch.estimatedStartTime')">
              <el-date-picker
                v-model="assignForm.estimatedStartTime"
                type="datetime"
                :placeholder="t('common.pleaseSelect')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('dispatch.estimatedFinishTime')">
              <el-input v-model="assignForm.estimatedFinishTime" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="t('dispatch.assignmentNotes')">
              <el-input v-model="assignForm.notes" type="textarea" :rows="3" :placeholder="t('dispatch.assignmentNotesPlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="assignDialogVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="confirmAssign">{{ t('common.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重新分配弹窗 -->
    <el-dialog
      v-model="reassignDialogVisible"
      :title="t('dispatch.reassignDialogTitle')"
      width="800px"
      class="dispatch-reassign-dialog"
    >
      <el-form :model="reassignForm" label-position="top" class="dialog-form-modern">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="t('dispatch.workOrderNo')">
              <el-input v-model="reassignForm.workOrderNo" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('dispatch.originalTechnician')">
              <el-input v-model="reassignForm.originalTechnician" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('dispatch.newTechnician')">
              <el-select v-model="reassignForm.newTechnicianId" :placeholder="t('common.pleaseSelect')" clearable>
                <el-option label="技师A-王强" value="techA" />
                <el-option label="技师B-张伟" value="techB" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('dispatch.estimatedStartTime')">
              <el-date-picker
                v-model="reassignForm.estimatedStartTime"
                type="datetime"
                :placeholder="t('common.pleaseSelect')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('dispatch.estimatedFinishTime')">
              <el-input v-model="reassignForm.estimatedFinishTime" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="t('dispatch.reassignmentReason')">
              <el-input v-model="reassignForm.reason" type="textarea" :rows="3" :placeholder="t('dispatch.reassignmentReasonPlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="reassignDialogVisible = false">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" @click="confirmReassign">{{ t('common.confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download, Document, UserFilled, Share, VideoPause, VideoPlay, CircleCheck } from '@element-plus/icons-vue'
import DispatchTable from './components/DispatchTable.vue'
import WorkOrderDetailDialog from './components/WorkOrderDetailDialog.vue'
import type { DispatchSearchParams, DispatchListItem, Technician } from '@/types/dispatch'
import { getDispatchList, getTechnicians } from '@/api/modules/dispatch'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('dispatch')
const router = useRouter()

// 模拟角色判断 (实际项目中应从用户认证信息中获取)
const isTechnicianManager = ref(true); // 假设当前是技师经理
const isTechnician = computed(() => !isTechnicianManager.value); // 技师经理和技师互斥

const loading = ref(false)
const searchParams = ref<DispatchSearchParams>({
  page: 1,
  pageSize: 10
})
const searchDateRange = ref<[string, string] | null>(null)
const total = ref(0)
const dispatchList = ref<DispatchListItem[]>([])
const technicians = ref<Technician[]>([])
const currentWorkOrderId = ref<string | null>(null)
const detailDialogVisible = ref(false)

const assignForm = ref({ technicianId: '', notes: '' })
const reassignForm = ref({ newTechnicianId: '', reason: '' })

// For assign/reassign dialogs
const currentWorkOrder = ref<DispatchListItem | null>(null)
const assignDialogVisible = ref(false)
const reassignDialogVisible = ref(false)

const workOrderStatuses = computed(() => [
  { label: t('status.pendingStart'), value: 'pending_start' },
  { label: t('status.inProgress'), value: 'in_progress' },
  { label: t('status.pendingQualityInspection'), value: 'pending_quality_inspection' },
  { label: t('status.pendingRework'), value: 'pending_rework' },
  { label: t('status.completed'), value: 'completed' },
  {
    label: t('status.additionalPendingConfirmation'),
    value: 'additional_items_pending_confirmation'
  },
  { label: t('status.waitingForParts'), value: 'waiting_for_parts' }
])

const handleSearch = async () => {
  loading.value = true
  try {
    const params: DispatchSearchParams = { ...searchParams.value }
    if (searchDateRange.value) {
      params.startTime = searchDateRange.value[0]
      params.endTime = searchDateRange.value[1]
    }
    const res = await getDispatchList(params)
    dispatchList.value = res.data
    total.value = res.total
  } catch (error) {
    console.error(error)
    ElMessage.error(tc('networkError'))
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchParams.value = {
    page: 1,
    pageSize: 10
  }
  searchDateRange.value = null
  handleSearch()
}

const handleViewDetail = (row: DispatchListItem) => {
  currentWorkOrderId.value = row.workOrderId
  detailDialogVisible.value = true
}

const handleAssign = (row: DispatchListItem) => {
  currentWorkOrder.value = row
  assignForm.value = { technicianId: '', notes: '' }
  assignDialogVisible.value = true
}

const handleReassign = (row: DispatchListItem) => {
  currentWorkOrder.value = row
  reassignForm.value = { newTechnicianId: '', reason: '' }
  reassignDialogVisible.value = true
}

const fetchTechs = async () => {
  try {
    const res = await getTechnicians()
    technicians.value = res.data
  } catch (error) {
    console.error(error)
  }
}

const handleConfirmAssign = () => {
  // Mock logic
  console.log('Assigning...', assignForm.value)
  assignDialogVisible.value = false
  ElMessage.success(t('assignSuccess'))
  handleSearch()
}

const handleConfirmReassign = () => {
  // Mock logic
  console.log('Reassigning...', reassignForm.value)
  reassignDialogVisible.value = false
  ElMessage.success(t('reassignSuccess'))
  handleSearch()
}

onMounted(() => {
  handleSearch()
  fetchTechs()
})

// 获取工单状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'pending_start': return 'warning';
    case 'in_progress': return ''; // 默认蓝色
    case 'pending_quality_inspection': return 'warning';
    case 'pending_rework': return 'danger';
    case 'completed': return 'success';
    case 'additional_items_pending_confirmation': return 'info';
    case 'waiting_for_parts': return 'info';
    default: return 'info';
  }
};

// 获取分配状态标签类型
const getAssignmentStatusTagType = (status: string) => {
  switch (status) {
    case 'pending_assignment': return 'info';
    case 'assigned': return 'success';
    default: return 'info';
  }
};

// 表格行样式
const tableRowClassName = ({ row }: { row: any }) => {
  if (row.priority === 'urgent') {
    return 'urgent-row';
  }
  return '';
};

// 详情弹窗
const handleDetail = (row: any) => {
  console.log('查看详情:', row);
  detailDialogVisible.value = true;
};

// 技师开工
const handleStartWork = (row: any) => {
  ElMessage.success(t('dispatch.startWorkSuccess') + row.workOrderNo);
  // 实际开工逻辑，更新工单状态和实际开工时间
  row.workOrderStatus = 'in_progress';
  row.actualStartTime = new Date().toLocaleString();
};

// 技师暂停/恢复
const handlePauseResume = (row: any) => {
  if (row.isPaused) {
    ElMessage.success(t('dispatch.resumeWorkSuccess') + row.workOrderNo);
  } else {
    ElMessage.warning(t('dispatch.pauseWorkSuccess') + row.workOrderNo);
  }
  row.isPaused = !row.isPaused; // 切换暂停状态
};

// 技师完工
const handleCompleteWork = (row: any) => {
  ElMessage.success(t('dispatch.completeWorkSuccess') + row.workOrderNo);
  // 实际完工逻辑，更新工单状态和实际完工时间
  row.workOrderStatus = 'pending_quality_inspection'; // 完工后进入待质检
  row.actualFinishTime = new Date().toLocaleString();
};

// 改变每页显示数量
const handleSizeChange = (val: number) => {
  searchParams.value.pageSize = val;
  searchParams.value.page = 1;
  handleSearch();
};

// 改变当前页码
const handleCurrentChange = (val: number) => {
  searchParams.value.page = val;
  handleSearch();
};

// 导出
const handleExport = () => {
  ElMessage.success(t('common.exportSuccess'));
  // 实际导出逻辑
};
</script>

<style scoped lang="scss">
@use '@/assets/styles/_variables.scss' as *;

.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.search-card, .operation-card, .table-card {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-right: 20px; // 表单项右侧间距
    margin-bottom: 15px; // 表单项底部间距
    &:last-child {
      margin-right: 0;
    }
  }
}

.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}

.list-control-area {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-bottom: 10px;
}

// 表格样式
:deep(.el-table) {
  .el-table__body td,
  .el-table__header th {
    white-space: nowrap; // 禁止文本换行
  }
  .urgent-row {
    background-color: #fff2f0; // 紧急工单浅红色背景
  }
  .el-button + .el-button {
    margin-left: 8px; // 调整操作按钮间距
  }
}


.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-form-modern {
  .el-form-item {
    margin-bottom: 20px; // 增加表单项垂直间距
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  .el-button {
    margin-left: 10px;
  }
}
</style>
