<template>
  <el-dialog
    v-model="modalVisible"
    title="{{ t('addProspect') }}"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="add-prospect-modal">
      <!-- 线索查询区域 -->
      <div class="search-section">
        <el-form label-position="top">
          <el-row :gutter="16">
            <el-col :span="10">
              <el-form-item :label="t('prospectName')">
                <el-input
                  v-model="searchForm.customerName"
                  :placeholder="t('inputProspectName')"
                  :disabled="leadSearched"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item :label="t('prospectPhone')">
                <el-input
                  v-model="searchForm.customerPhone"
                  :placeholder="t('inputPhoneNumber')"
                  :disabled="leadSearched"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="&nbsp;"> 
                <el-button
                  v-if="!leadSearched"
                  type="primary"
                  @click="handleSearchLead"
                  :loading="searchLoading"
                  style="width: 100%;"
                >
                  {{ tc('query') }}
                </el-button>
                <el-button
                  v-else
                  @click="handleResetSearch"
                  style="width: 100%;"
                >
                  {{ tc('reset') }}
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 线索查询结果显示 -->
        <div v-if="leadSearched" class="search-result" style="margin-top: 16px;">
          <el-alert v-if="leadInfo" type="success" style="margin-bottom: 16px;" show-icon>
            <template #title>
              <span>{{ t('foundMatchingLead') }}</span>
            </template>
            <el-descriptions :column="2" size="small">
              <el-descriptions-item :label="t('customerName')">{{ leadInfo.customerName }}</el-descriptions-item>
              <el-descriptions-item :label="t('phoneNumber')">{{ leadInfo.customerPhone }}</el-descriptions-item>
              <el-descriptions-item :label="t('email')">{{ leadInfo.email || '-' }}</el-descriptions-item>
              <el-descriptions-item :label="t('sourceChannel')">{{ getSourceChannelName(leadInfo.sourceChannel || '') || '-' }}</el-descriptions-item>
            </el-descriptions>
          </el-alert>
          <el-alert v-else type="warning" show-icon>
            <template #title>
              <span>{{ t('noMatchingLead') }}</span>
            </template>
            <p>{{ t('noMatchingLeadMessage') }}</p>
          </el-alert>
        </div>
      </div>

      <!-- 新增表单 -->
      <div class="form-section">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-position="top"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospectName')" prop="customerName">
                <el-input
                  v-model="formData.customerName"
                  :placeholder="t('inputProspectName')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospectPhone')" prop="customerPhone">
                <el-input
                  v-model="formData.customerPhone"
                  :placeholder="t('inputPhoneNumber')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('idType')" prop="idType">
                <el-select
                  v-model="formData.idType"
                  :placeholder="tc('pleaseSelect')"
                  style="width: 100%"
                >
                  <el-option :label="t('idCard')" value="01140001"></el-option>
                  <el-option :label="t('passport')" value="01140002"></el-option>
                  <el-option :label="t('residencePermit')" value="01140003"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('idNumber')" prop="idNumber">
                <el-input
                  v-model="formData.idNumber"
                  :placeholder="t('inputIdNumber')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('email')" prop="email">
                <el-input
                  v-model="formData.email"
                  :placeholder="t('inputEmail')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('intentionLevel')" prop="intentionLevel">
                <el-select
                  v-model="formData.intentionLevel"
                  :placeholder="tc('pleaseSelect')"
                  style="width: 100%"
                  :loading="dictionaryLoading"
                >
                  <el-option
                    v-for="option in prospectLevelOptions"
                    :key="option.code"
                    :label="option.name"
                    :value="option.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          >
          {{ tc('save') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import { storeProspectApi } from '@/api/modules/prospective-customer'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('sales')
import {
  type SearchExistingLeadRequest,
  type ExistingLeadInfo,
  type AddStoreProspectRequest,
  SourceChannel,
  ProspectLevel,
  IdType
} from '@/types/prospective-customer.d'

// 组件属性
interface Props {
  show: boolean
  presetData?: {
    customerName?: string
    customerPhone?: string
    sourceChannel?: SourceChannel
  }
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  presetData: () => ({})
})

const emit = defineEmits<Emits>()

// 使用字典数据
const {
  getOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.INTENT_LEVEL,
  DICTIONARY_TYPES.CUSTOMER_SOURCE
])

// 获取来源渠道名称
const getSourceChannelName = (code: string) => {
  return getNameByCode(DICTIONARY_TYPES.CUSTOMER_SOURCE, code) || code
}

// 响应式状态
const searchFormRef = ref<FormInstance>()
const formRef = ref<FormInstance>()

const searchLoading = ref(false)
const submitLoading = ref(false)
const leadSearched = ref(false)
const leadInfo = ref<ExistingLeadInfo | null>(null)

// 模态框显示状态
const modalVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 查询表单
const searchForm = reactive<SearchExistingLeadRequest>({
  customerName: '',
  customerPhone: ''
})

// 新增表单
const formData = reactive<AddStoreProspectRequest>({
  customerName: '',
  customerPhone: '',
  idType: '01140001', // 默认身份证
  idNumber: '',
  email: '',
  intentionLevel: '01160002' // 默认高意向 (A级)
})



// 表单验证规则
const formRules: FormRules = {
  customerName: [
    { required: true, message: t('inputProspectNameRequired'), trigger: 'blur' }
  ],
  customerPhone: [
    { required: true, message: t('inputPhoneNumberRequired'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: t('invalidPhoneNumber'), trigger: 'blur' }
  ],
  idType: [
    { required: true, message: t('selectIdTypeRequired'), trigger: 'change' }
  ],
  idNumber: [
    { required: true, message: t('inputIdNumberRequired'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('inputEmailRequired'), trigger: 'blur' },
    { type: 'email', message: t('invalidEmail'), trigger: 'blur' }
  ],
  intentionLevel: [
    { required: true, message: t('selectIntentionLevelRequired'), trigger: 'change' }
  ]
}

const prospectLevelOptions = computed(() => getOptions(DICTIONARY_TYPES.INTENT_LEVEL))

// 监听预设数据变化
watch(() => props.presetData, (data) => {
  if (data) {
    searchForm.customerName = data.customerName || ''
    searchForm.customerPhone = data.customerPhone || ''
  }
}, { immediate: true })

// 监听模态框显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    resetAll()
  }
})



// 事件处理函数
const handleSearchLead = async () => {
  if (!searchForm.customerName && !searchForm.customerPhone) {
    ElMessage.warning(t('inputNameOrPhoneRequired'))
    return
  }

  try {
    searchLoading.value = true
    const response = await storeProspectApi.searchExistingLead(searchForm)
    leadInfo.value = response.data
    leadSearched.value = true

    if (leadInfo.value) {
      ElMessage.success(t('foundMatchingLead'))
      // 自动填充表单
      Object.assign(formData, {
        customerName: leadInfo.value.customerName,
        customerPhone: leadInfo.value.customerPhone,
        idType: leadInfo.value.idType || '01140001', // 默认身份证
        idNumber: leadInfo.value.idNumber || '',
        email: leadInfo.value.email || '',
        intentionLevel: '01160001' // 默认高意向 (A级)
      })
    } else {
      ElMessage.warning(t('noMatchingLead'))
    }
  } catch (error) {
    ElMessage.error(tc('queryFailed'))
  } finally {
    searchLoading.value = false
  }
}

const handleResetSearch = () => {
  searchForm.customerName = ''
  searchForm.customerPhone = ''
  leadSearched.value = false
  leadInfo.value = null
}



const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    submitLoading.value = true
    await storeProspectApi.addStoreProspect(formData)

    ElMessage.success(t('addProspectSuccess'))
    emit('success')
    modalVisible.value = false
  } catch (error) {
    ElMessage.error(tc('saveFailed'))
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetAll()
}

const resetAll = () => {
  searchForm.customerName = ''
  searchForm.customerPhone = ''
  leadSearched.value = false
  leadInfo.value = null

  // 重置表单数据
  Object.assign(formData, {
    customerName: '',
    customerPhone: '',
    idType: IdType.ID_CARD,
    idNumber: '',
    email: '',
    intentionLevel: ProspectLevel.C
  })
}
</script>

<style scoped>
.add-prospect-modal {
  max-height: 70vh;
  overflow-y: auto;
}

.search-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.search-header {
  margin-bottom: 16px;
}

.search-form {
  margin-bottom: 8px;
}

.form-section {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #ffffff;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}
</style>
