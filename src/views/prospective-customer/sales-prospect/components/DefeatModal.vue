<template>
  <el-dialog
    v-model="modalVisible"
    :title="t('markNoIntentionConfirm')"
    width="600px"
    :before-close="handleCancel"
  >
    <div class="defeat-modal">
      <!-- 潜客信息区域 -->
      <div class="prospect-info-section">
        <el-form label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('prospectId')">
                <el-input :value="prospectInfo.prospectId || '-'" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospectName')">
                <el-input :value="prospectInfo.customerName || '-'" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('prospectPhone')">
                <el-input :value="prospectInfo.customerPhone || '-'" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('salesAdvisorName')">
                <el-input :value="prospectInfo.advisorName || '-'" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item :label="t('markTime')">
                <el-input :value="currentTime" readonly />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表单区域 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-position="top"
      >
        <el-form-item :label="t('noIntentionReason')" prop="defeatReason">
          <el-select
            v-model="formData.defeatReason"
            :placeholder="t('selectNoIntentionReason')"
            style="width: 100%"
            :loading="dictionaryLoading"
          >
            <el-option
              v-for="item in defeatReasonOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="t('detailedDescription')" prop="defeatDetails">
          <el-input
            v-model="formData.defeatDetails"
            type="textarea"
            :placeholder="t('detailedDescriptionPlaceholder')"
            :rows="4"
            show-word-limit
            :maxlength="1000"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          {{ tc('confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import type { FormInstance, FormRules } from 'element-plus'
import { ProspectStatus, type ProspectBaseInfo } from '@/types/prospective-customer.d'
import { storeProspectApi } from '@/api/modules/prospective-customer'
import { useDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('sales')




// 日期格式化函数
const formatDate = (date: string) => {
  return new Date(date).toLocaleString()
}

// 组件属性
interface Props {
  show: boolean
  prospectData: ProspectBaseInfo | null
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 使用字典数据
const {
  options: defeatReasonOptions,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.DEFEAT_REASON)

// 响应式状态
const formRef = ref<FormInstance>()

// 状态变量
const submitLoading = ref(false)
const currentTime = ref('')

// 潜客信息
const prospectInfo = reactive({
  prospectId: '',
  customerName: '',
  customerPhone: '',
  advisorName: ''
})

// 表单数据
const formData = reactive({
  storeProspectId: '',
  defeatReason: '',
  defeatDetails: ''
})

// 模态框显示状态
const modalVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})



// 表单验证规则
const formRules: FormRules = {
  defeatReason: [
    { required: true, message: t('selectNoIntentionReason'), trigger: 'change' }
  ],
  defeatDetails: [
    { required: true, message: t('inputDetailedDescriptionRequired'), trigger: 'blur' }
  ]
}

// 事件处理函数
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    submitLoading.value = true

    // 构建请求数据
    const requestData = {
      prospectId: Number(formData.storeProspectId),
      defeatReason: formData.defeatReason,
      defeatDescription: formData.defeatDetails
    }

    // 调用API提交战败申请
    await storeProspectApi.applyDefeat(requestData)

    ElMessage.success(t('markNoIntentionSubmitSuccess'))
    emit('success')
    handleCancel()
  } catch (error) {
    console.error('标记无意向申请提交失败:', error)
    ElMessage.error(t('formValidationFailed'))
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
}

const resetForm = () => {
  Object.assign(formData, {
    storeProspectId: '',
    defeatReason: '',
    defeatDetails: ''
  })

  Object.assign(prospectInfo, {
    prospectId: '',
    customerName: '',
    customerPhone: '',
    advisorName: ''
  })
}

const initializeForm = () => {
  if (props.prospectData) {
    // 检查状态是否允许申请标记无意向
    // if (props.prospectData.customerStatus !== ProspectStatus.FOLLOWING) {
    //   ElMessage.warning('只有"跟进中"状态的潜客才能申请标记无意向')
    //   return
    // }

    // 将ID转换为字符串
    formData.storeProspectId = String(props.prospectData.id)
    prospectInfo.prospectId = String(props.prospectData.id)

    // 更新为正确的字段名
    prospectInfo.customerName = props.prospectData.customerName || '-'
    prospectInfo.customerPhone = props.prospectData.customerPhone || '-'
    prospectInfo.advisorName = props.prospectData.currentSalesAdvisorName || '-'

    currentTime.value = formatDate(new Date().toISOString())
  }
}

// 监听模态框显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    initializeForm()
  } else {
    resetForm()
  }
})
</script>

<style scoped lang="scss">
.defeat-modal {
  .prospect-info-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .info-header {
      margin-bottom: 12px;

      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }
    }

    .info-content {
      .info-item {
        display: flex;
        margin-bottom: 8px;
        font-size: 13px;

        label {
          font-weight: 500;
          color: #666;
          margin-right: 8px;
          min-width: 80px;
        }

        span {
          color: #333;
        }
      }
    }
  }

  .form-section {
    margin-bottom: 20px;

    :deep(.el-form-item__label) {
      font-size: 13px;
    }
  }

  .warning-section {
    margin-bottom: 16px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
