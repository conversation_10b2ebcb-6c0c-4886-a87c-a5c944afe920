<template>
  <el-dialog
    v-model="modalVisible"
    title="{{ t('prospectFollowUp') }}"
    width="900px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <div class="follow-up-modal">
      <!-- 跟进记录表单 -->
      <div class="form-section">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-position="top"
        >
          <!-- 潜客信息part -->
          <div class="form-group-title">{{ t('prospectInfo') }}</div>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospectId')" prop="prospectId">
                <el-input
                  v-model="formData.prospectId"
                  :placeholder="t('prospectId')"
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('prospectName')" prop="customerName">
                <el-input
                  v-model="formData.customerName"
                  :placeholder="t('inputProspectName')"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('prospectPhone')" prop="customerPhone">
                <el-input
                  v-model="formData.customerPhone"
                  :placeholder="t('inputPhoneNumber')"
                  readonly
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('idType')" prop="idType">
                <el-select
                  v-model="formData.idType"
                  :placeholder="t('idTypePlaceholder')"
                  style="width: 100%"
                >
                  <el-option :label="t('idCard')" value="01140001"></el-option>
                  <el-option :label="t('passport')" value="01140002"></el-option>
                  <el-option :label="t('residencePermit')" value="01140003"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('email')" prop="email">
                <el-input
                  v-model="formData.email"
                  :placeholder="t('inputEmail')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('region')" prop="region">
                <el-select
                  v-model="formData.region"
                  :placeholder="t('selectRegion')"
                  style="width: 100%"
                  :loading="dictionaryLoading"
                >
                  <el-option
                    v-for="option in regionOptions"
                    :key="option.code"
                    :label="option.name"
                    :value="option.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 潜客意向part -->
          <div class="form-group-title">{{ t('prospectIntention') }}</div>
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item :label="t('intentModel')" prop="intentionModel">
                <el-select
                  v-model="formData.intentionModel"
                  :placeholder="t('selectIntentModel')"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in modelOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
            <el-form-item :label="t('intentVariant')" prop="intentionVariant">
              <el-select
                v-model="formData.intentionVariant"
                :placeholder="t('selectIntentVariant')"
                clearable
                filterable
              >
                <el-option
                  v-for="item in variantOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
            <el-col :span="8">
              <el-form-item :label="t('intentColor')" prop="intentionColor">
                <el-select
                  v-model="formData.intentionColor"
                  :placeholder="t('selectIntentColor')"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in colorOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 跟进记录part -->
          <div class="form-group-title">{{ t('followUpRecord') }}</div>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('salesAdvisorName')">
                <el-input
                  v-model="advisorName"
                  readonly
                  :placeholder="t('currentAdvisor')"
                  style="background-color: #f5f5f5; color: #999;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('followUpMethod')" prop="followUpMethod">
                <el-select
                  v-model="formData.followUpMethod"
                  :placeholder="t('selectFollowUpMethod')"
                  style="width: 100%"
                  :loading="dictionaryLoading"
                >
                  <el-option
                    v-for="option in followUpMethodOptions"
                    :key="option.code"
                    :label="option.name"
                    :value="option.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('followUpTime')" prop="followUpTime">
                <el-date-picker
                  v-model="formData.followUpTime"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm"
                  :placeholder="t('selectFollowUpTime')"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="t('intentionLevel')" prop="intentionLevel">
                <el-select
                  v-model="formData.intentionLevel"
                  :placeholder="t('intentionLevelPlaceholder')"
                  style="width: 100%"
                  @change="handleLevelChange"
                >
                  <el-option :label="t('levelH')" value="01160001"></el-option>
                  <el-option :label="t('levelA')" value="01160002"></el-option>
                  <el-option :label="t('levelB')" value="01160003"></el-option>
                  <el-option :label="t('levelC')" value="01160004"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="t('nextFollowUpTime')" prop="nextFollowUpTime">
                <el-date-picker
                  v-model="formData.nextFollowUpTime"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm"
                  :placeholder="t('nextFollowUpTimePlaceholder')"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="!formData.intentionLevel">
              <div class="text-muted">
                <small>请先选择意向级别和跟进时间</small>
              </div>
            </el-col>
          </el-row>

          <el-form-item :label="t('followUpDetails')" prop="followUpDetails">
            <el-input
              v-model="formData.followUpDetails"
              type="textarea"
              :placeholder="t('followUpDetailsPlaceholder')"
              :rows="4"
              show-word-limit
              :maxlength="1000"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
        >
          {{ tc('save') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { storeProspectApi } from '@/api/modules/prospective-customer'
import {
  type ProspectBaseInfo,
  type RecordFollowUpRequest,
  FollowUpMethod,
  ProspectLevel,
  IdType
} from '@/types/prospective-customer.d'
import { calculateNextFollowUpTime, dateToString } from '@/utils/date-filter'
import { useBatchDictionary } from '@/composables/useDictionary'
import { DICTIONARY_TYPES } from '@/constants/dictionary'
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('sales')

// 组件属性
interface Props {
  show: boolean
  prospectData: ProspectBaseInfo | null
}

// 组件事件
interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  prospectData: null
})

const emit = defineEmits<Emits>()

const {
  getOptions,
  loading: batchDictionaryLoading
} = useBatchDictionary([
  DICTIONARY_TYPES.REGION,
  DICTIONARY_TYPES.FOLLOW_UP_METHOD
])

const dictionaryLoading = computed(() => batchDictionaryLoading.value)

// 响应式数据
const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const advisorName = ref('当前销售顾问')

// 模态框显示状态
const modalVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 表单数据
const formData = reactive<RecordFollowUpRequest>({
  prospectId: 0,
  customerName: '',
  customerPhone: '',
  idType: '01140001', // 默认身份证
  idNumber: '',
  email: '',
  region: '',
  intentionModel: '',
  intentionVariant: '',
  intentionColor: '',
  followUpMethod: '01390001', // 默认电话
  followUpTime: null,
  intentionLevel: '01160001', // 默认 H级 - 最高意向
  nextFollowUpTime: null,
  followUpDetails: ''
})

// 表单验证规则
const formRules: FormRules = {
  customerName: [
    { required: true, message: t('inputProspectNameRequired'), trigger: 'blur' }
  ],
  customerPhone: [
    { required: true, message: t('inputPhoneNumberRequired'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: t('invalidPhoneNumber'), trigger: 'blur' }
  ],
  intentionModel: [
    { required: true, message: t('selectIntentionModelRequired'), trigger: 'change' }
  ],
  intentionVariant: [
    { required: true, message: t('selectIntentionVariantRequired'), trigger: 'change' }
  ],
  intentionColor: [
    { required: true, message: t('selectIntentionColorRequired'), trigger: 'change' }
  ],
  followUpMethod: [
    { required: true, message: t('selectFollowUpMethodRequired'), trigger: 'change' }
  ],
  followUpTime: [
    { required: true, message: t('selectFollowUpTimeRequired'), trigger: 'change' }
  ],
  intentionLevel: [
    { required: true, message: t('selectIntentionLevelRequiredFollowUp'), trigger: 'change' }
  ],
  nextFollowUpTime: [
    { required: true, message: t('selectNextFollowUpTimeRequired'), trigger: 'change' }
  ],
  followUpDetails: [
    { required: true, message: t('inputFollowUpDetailsRequired'), trigger: 'blur' }
  ]
}

const regionOptions = computed(() => getOptions(DICTIONARY_TYPES.REGION))
const followUpMethodOptions = computed(() => getOptions(DICTIONARY_TYPES.FOLLOW_UP_METHOD))

const modelOptions = [
  { label: 'Perodua Axia', value: 'AXIA' },
  { label: 'Perodua Bezza', value: 'BEZZA' },
  { label: 'Perodua Myvi', value: 'MYVI' },
  { label: 'Perodua Aruz', value: 'ARUZ' },
  { label: 'Perodua Ativa', value: 'ATIVA' }
]

const variantOptions = [
  { label: 'Standard', value: 'STANDARD' },
  { label: 'Premium', value: 'PREMIUM' },
  { label: 'Advance', value: 'ADVANCE' }
]

const colorOptions = [
  { label: '白色', value: 'WHITE' },
  { label: '黑色', value: 'BLACK' },
  { label: '银色', value: 'SILVER' },
  { label: '红色', value: 'RED' },
  { label: '蓝色', value: 'BLUE' }
]





// 监听意向级别变化，自动计算下次跟进时间
const handleLevelChange = (level: string) => {
  if (formData.followUpTime && level) {
    const nextTime = calculateNextFollowUpTime(formData.followUpTime, level)
    formData.nextFollowUpTime = nextTime
  }
}

// 监听跟进时间变化，自动计算下次跟进时间
watch(() => formData.followUpTime, (newTime) => {
  if (newTime && formData.intentionLevel) {
    const nextTime = calculateNextFollowUpTime(newTime, formData.intentionLevel)
    formData.nextFollowUpTime = nextTime
  }
})

// 监听潜客数据变化，初始化表单
watch(() => props.prospectData, (data) => {
  if (data) {
    Object.assign(formData, {
      prospectId: data.id,
      customerName: data.customerName || '',
      customerPhone: data.customerPhone || '',
      idType: data.idType || '01140001', // 默认身份证
      idNumber: data.idNumber || '',
      email: data.email || '',
      region: data.region || '',
      intentionModel: data.intentModel || '',
      intentionVariant: data.intentVariant || '',
      intentionColor: data.intentColor || '',
      followUpMethod: '01390001', // 默认电话
      followUpTime: new Date(),
      intentionLevel: data.customerLevel || '01160001', // 默认 H级 - 最高意向
      nextFollowUpTime: null,
      followUpDetails: ''
    })

    advisorName.value = data.salesConsultant || '当前销售顾问'

    // 自动计算下次跟进时间
    if (formData.followUpTime && formData.intentionLevel) {
      const nextTime = calculateNextFollowUpTime(formData.followUpTime, formData.intentionLevel)
      formData.nextFollowUpTime = nextTime
    }
  }
}, { immediate: true })

// 事件处理
const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    submitLoading.value = true

    // 格式化日期时间为 yyyy-MM-dd HH:mm 格式
    const formatDateTime = (date: Date | null | string) => {
      if (!date) return null
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }

    const requestData = {
      ...formData,
      followUpTime: formatDateTime(formData.followUpTime),
      nextFollowUpTime: formatDateTime(formData.nextFollowUpTime)
    }

    await storeProspectApi.recordFollowUp(requestData)

    ElMessage.success(t('followUpRecordAddSuccess'))
    emit('success')
    modalVisible.value = false
  } catch (error) {
    ElMessage.error(tc('saveFailed'))
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped>
.follow-up-modal {
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  padding: 0;
}

.form-group-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin: 20px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.form-group-title:first-child {
  margin-top: 0;
}

.text-muted {
  color: #909399;
  font-size: 12px;
  display: flex;
  align-items: center;
  height: 32px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-textarea__inner) {
  min-height: 100px;
}
</style>
