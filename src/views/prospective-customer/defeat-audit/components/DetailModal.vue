<template>
  <el-dialog
    v-model="showModal"
    title="战败申请详情"
    width="600px"
    :close-on-click-modal="false"
    :modal="false"
  >
    <div class="detail-modal-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请单号">
          {{ applicationData?.applicationNo || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="申请人">
          {{ applicationData?.applicantName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">
          {{ applicationData?.applicationTime ? formatDate(applicationData.applicationTime) : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="潜客姓名">
          {{ applicationData?.prospectName ? maskName(applicationData.prospectName) : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="潜客手机号">
          {{ applicationData?.prospectPhone ? maskPhone(applicationData.prospectPhone) : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="战败原因">
          {{ applicationData?.defeatReason || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="审核结果" :span="2">
          <el-tag
            v-if="applicationData?.auditResult"
            :type="getAuditResultTagType(applicationData.auditResult)"
            size="small"
          >
            {{ getAuditResultText(applicationData.auditResult) }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="审核时间" :span="2">
          {{ applicationData?.auditTime ? formatDate(applicationData.auditTime) : '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="1" v-if="applicationData?.defeatDescription">
        <el-descriptions-item label="详细说明">
          <div class="content-text">
            {{ applicationData.defeatDescription }}
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="1" v-if="applicationData?.auditComment">
        <el-descriptions-item label="审核意见">
          <div class="content-text">
            {{ applicationData.auditComment }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  type DefeatApplication,
  AuditResult
} from '@/api/types/defeat-audit'
import { formatDate } from '@/utils/date-filter'
import { maskPhone, maskName } from '@/utils/data-mask'

// Props定义
interface Props {
  show: boolean
  applicationData: DefeatApplication | null
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  applicationData: null
})

// Emits定义
interface Emits {
  (e: 'update:show', value: boolean): void
}

const emit = defineEmits<Emits>()

// 计算属性
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 获取审核结果标签类型
const getAuditResultTagType = (result: AuditResult) => {
  switch (result) {
    case AuditResult.APPROVED: return 'success'
    case AuditResult.REJECTED: return 'danger'
    default: return 'primary'
  }
}

// 获取审核结果文本
const getAuditResultText = (result: AuditResult) => {
  switch (result) {
    case AuditResult.APPROVED: return '审核通过'
    case AuditResult.REJECTED: return '审核驳回'
    default: return '待审核'
  }
}

// 处理关闭
const handleClose = () => {
  showModal.value = false
}
</script>

<style scoped>
.detail-modal-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-text {
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
