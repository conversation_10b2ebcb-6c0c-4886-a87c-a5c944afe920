<template>
  <div class="defeat-audit-management">
    <h1 class="page-title">战败审核列表</h1>

    <!-- 筛选区域 -->
    <el-card class="filter-section mb-20">
      <el-form ref="filterFormRef" :model="filterForm" label-position="top">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="申请人">
              <el-input
                v-model="filterForm.applicantName"
                placeholder="请输入申请人姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="潜客姓名">
              <el-input
                v-model="filterForm.prospectName"
                placeholder="请输入潜客姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="潜客手机号">
              <el-input
                v-model="filterForm.prospectPhone"
                placeholder="请输入手机号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="申请时间">
              <el-date-picker
                v-model="filterForm.applicationTime"
                type="daterange"
                clearable
                placeholder="选择申请日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="form-buttons">
            <el-space>
              <el-button type="primary" @click="handleSearch" :icon="Search">
                查询
              </el-button>
              <el-button @click="handleReset" :icon="Refresh">
                重置
              </el-button>
            </el-space>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 功能按钮区域 -->
    <div class="action-bar mb-20">
      <div class="left-actions">
        <el-button type="primary" @click="handleExport" :icon="Download">
          导出
        </el-button>
      </div>
      <div class="right-actions">
        <el-radio-group v-model="activeTab" @change="handleTabChange">
          <el-radio-button label="pending">待审核</el-radio-button>
          <el-radio-button label="audited">已审核</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      ref="tableRef"
      :data="tableData"
      v-loading="loading"
      border
      stripe
      height="600"
      style="width: 100%; margin-bottom: 20px;"
    >
      <!-- 表格列在计算属性中配置 -->
      <el-table-column type="index" label="序号" width="60" fixed="left" />
      <el-table-column prop="applicationNo" label="申请单号" width="150" />
      <el-table-column prop="applicantName" label="申请人" width="120" />
      <el-table-column prop="prospectName" label="潜客姓名" width="120" />
      <el-table-column prop="prospectPhone" label="潜客手机号" width="140" />
      <el-table-column prop="applicationTime" label="申请时间" width="180" />
      <el-table-column prop="defeatReason" label="战败原因" width="120" />
      <el-table-column v-if="activeTab === 'audited'" prop="auditResult" label="审核结果" width="100">
        <template #default="{ row }">
          <el-tag :type="row.auditResult === 'APPROVED' ? 'success' : 'danger'">
            {{ row.auditResult === 'APPROVED' ? '通过' : '拒绝' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" v-if="activeTab !== 'pending'" link @click="handleViewDetail(row)">
            详情
          </el-button>
          <el-button
            v-if="activeTab === 'pending'"
            type="primary"
            link
            @click="handleAudit(row)"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="pagination.pageSizes"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 审核弹窗 -->
    <AuditModal
      v-model:show="showAuditModal"
      :application-data="currentApplicationData"
      @success="handleAuditSuccess"
    />

    <!-- 详情查看弹窗 -->
    <DetailModal
      v-model:show="showDetailModal"
      :application-data="currentApplicationData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElTable } from 'element-plus'
import { Search, Refresh, Download } from '@element-plus/icons-vue'
import { defeatAuditApi } from '@/api/modules/defeat-audit'
import {
  type DefeatApplication,
  type GetDefeatApplicationListRequest,
  AuditResult
} from '@/api/types/defeat-audit'
import AuditModal from './components/AuditModal.vue'
import DetailModal from './components/DetailModal.vue'

// 定义组件名称
defineOptions({
  name: 'DefeatAuditManagement'
})

// 表格引用
const tableRef = ref<InstanceType<typeof ElTable>>()
const filterFormRef = ref()

// 筛选表单数据
const filterForm = reactive<{
  applicantName?: string
  prospectName?: string
  prospectPhone?: string
  applicationTime?: [number, number] | null
  auditResult?: AuditResult
}>({
  applicantName: '',
  prospectName: '',
  prospectPhone: '',
  applicationTime: null,
  auditResult: undefined
})

// 状态变量
const loading = ref(false)
const tableData = ref<DefeatApplication[]>([])
const activeTab = ref<'pending' | 'audited'>('pending')

// 模态框状态
const showAuditModal = ref(false)
const showDetailModal = ref(false)
const currentApplicationData = ref<DefeatApplication | null>(null)

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  onUpdatePage: (page: number) => {
    pagination.page = page
    loadTableData()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    loadTableData()
  }
})

// 处理分页大小变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadTableData()
}

// 处理页码变化
const handlePageChange = (page: number) => {
  pagination.page = page
  loadTableData()
}

// 事件处理函数
const handleSearch = async () => {
  pagination.page = 1
  await loadTableData()
}

const handleReset = () => {
  // 重置所有筛选条件为初始状态
  filterForm.applicantName = ''
  filterForm.prospectName = ''
  filterForm.prospectPhone = ''
  filterForm.applicationTime = null
  filterForm.auditResult = undefined

  // 重置分页到第一页
  pagination.page = 1

  // 重新加载数据
  loadTableData()
}

const handleTabChange = (tab: 'pending' | 'audited') => {
  activeTab.value = tab
  pagination.page = 1
  loadTableData()
}

const handleAudit = (application: DefeatApplication) => {
  console.log('审核按钮点击，申请数据:', application)

  // 确保有潜客ID
  if (!application.id) {
    console.error('缺少潜客ID，无法打开审核弹窗', application)
    ElMessage.error('缺少潜客信息，无法打开审核弹窗')
    return
  }
  currentApplicationData.value = application
  showAuditModal.value = true
}

const handleViewDetail = (application: DefeatApplication) => {
  currentApplicationData.value = application
  showDetailModal.value = true
}

// 参数清理工具函数
const cleanParams = (params: Record<string, unknown>): Record<string, unknown> => {
  const cleaned: Record<string, unknown> = {}

  Object.keys(params).forEach(key => {
    const value = params[key]
    // 保留非空值，0和false是有效值
    if (value !== null && value !== undefined && value !== '') {
      cleaned[key] = value
    }
  })

  return cleaned
}

// 加载表格数据
const loadTableData = async () => {
  try {
    loading.value = true

    // 处理日期范围，将时间戳转换为日期字符串
    let applicationTimeStart: string | undefined
    let applicationTimeEnd: string | undefined

    if (filterForm.applicationTime && filterForm.applicationTime.length === 2) {
      applicationTimeStart = new Date(filterForm.applicationTime[0]).toISOString().split('T')[0]
      applicationTimeEnd = new Date(filterForm.applicationTime[1]).toISOString().split('T')[0]
    }

    // 构建查询参数，过滤空值
    const queryParams = cleanParams({
      applicantName: filterForm.applicantName,
      prospectName: filterForm.prospectName,
      prospectPhone: filterForm.prospectPhone,
      applicationTimeStart,
      applicationTimeEnd,
      auditResult: activeTab.value === 'pending' ? [AuditResult.PENDING] :
                   activeTab.value === 'audited' ? (filterForm.auditResult === undefined ?
                   [AuditResult.APPROVED, AuditResult.REJECTED] : [filterForm.auditResult]) :
                   filterForm.auditResult === undefined ? undefined : [filterForm.auditResult],
      pageNum: pagination.page,
      pageSize: pagination.pageSize
    })

    console.log('API请求参数:', queryParams)

    const response = await defeatAuditApi.getDefeatApplicationList(queryParams as unknown as GetDefeatApplicationListRequest)

    console.log('API响应:', response)

    if (response && response.code === '200' && response.result) {
      tableData.value = response.result.records || []
      pagination.total = response.result.total || 0

      // 如果后端返回了分页信息，同步更新
      if (response.result.pageNum) {
        pagination.page = response.result.pageNum
      }

      console.log(`数据加载成功: ${tableData.value.length} 条记录`)
    } else {
      console.error('API返回错误:', response?.message || '未知错误')
      ElMessage.error(response?.message || '查询失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('API请求失败:', error)
    ElMessage.error(`查询失败: ${error instanceof Error ? error.message : '网络异常'}`)
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 导出功能
const handleExport = async () => {
  try {
    loading.value = true

    // 处理日期范围，将时间戳转换为日期字符串
    let applicationTimeStart: string | undefined
    let applicationTimeEnd: string | undefined

    if (filterForm.applicationTime && filterForm.applicationTime.length === 2) {
      applicationTimeStart = new Date(filterForm.applicationTime[0]).toISOString().split('T')[0]
      applicationTimeEnd = new Date(filterForm.applicationTime[1]).toISOString().split('T')[0]
    }

    // 构建导出参数，过滤空值
    const exportParams = cleanParams({
      applicantName: filterForm.applicantName,
      prospectName: filterForm.prospectName,
      prospectPhone: filterForm.prospectPhone,
      applicationTimeStart,
      applicationTimeEnd,
      auditResult: activeTab.value === 'pending' ? [AuditResult.PENDING] :
                   activeTab.value === 'audited' ? (filterForm.auditResult === undefined ?
                   [AuditResult.APPROVED, AuditResult.REJECTED] : [filterForm.auditResult]) :
                   filterForm.auditResult === undefined ? undefined : [filterForm.auditResult]
    })

    console.log('导出参数:', exportParams)

    const blob = await defeatAuditApi.exportDefeatApplications(exportParams)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `战败审核列表_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}

const handleAuditSuccess = () => {
  ElMessage.success('战败申请审核提交成功')
  handleSearch()
}

// 组件挂载时加载数据
onMounted(() => {
  loadTableData()
})
</script>

<style scoped>
.defeat-audit-management {
  padding: 20px;
  background-color: #fafafa;
  min-height: 100vh;
}

.page-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333333;
}

.filter-section {
  background-color: white;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.pagination-section {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
  background-color: white;
  border-radius: 0 0 6px 6px;
  padding: 10px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
</style>
