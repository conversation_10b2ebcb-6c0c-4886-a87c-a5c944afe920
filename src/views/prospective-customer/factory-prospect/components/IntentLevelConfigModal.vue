<template>
  <el-dialog
    v-model="showModal"
    title="意向级别配置"
    width="600px"
    :before-close="handleCancel"
  >
    <div class="alert-info">
      <el-alert
        type="info"
        show-icon
      >
        规则变更只对变更后新设置的意向级别生效，不影响已存在的潜客
      </el-alert>
    </div>

    <el-form
      ref="formRef"
      :model="configForm"
      label-position="left"
      label-width="auto"
    >
      <!-- H级 -->
      <div class="intent-level-section">
        <el-form-item label="H级 - 高意向潜客">
          <div class="input-group">
            <span class="input-group-text">跟进时限</span>
            <el-input-number
              v-model="configForm.hLevel.followUpDurationHours"
              :min="1"
              :max="240"
              style="width: 120px"
            />
            <span class="input-group-text">小时</span>
          </div>
        </el-form-item>
        <el-form-item label="定义描述">
          <el-input
            v-model="configForm.hLevel.definitionDescription"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
      </div>

      <!-- A级 -->
      <div class="intent-level-section">
        <el-form-item label="A级 - 较高意向潜客">
          <div class="input-group">
            <span class="input-group-text">跟进时限</span>
            <el-input-number
              v-model="configForm.aLevel.followUpDurationHours"
              :min="1"
              :max="240"
              style="width: 120px"
            />
            <span class="input-group-text">小时</span>
          </div>
        </el-form-item>
        <el-form-item label="定义描述">
          <el-input
            v-model="configForm.aLevel.definitionDescription"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
      </div>

      <!-- B级 -->
      <div class="intent-level-section">
        <el-form-item label="B级 - 中等意向潜客">
          <div class="input-group">
            <span class="input-group-text">跟进时限</span>
            <el-input-number
              v-model="configForm.bLevel.followUpDurationHours"
              :min="1"
              :max="720"
              style="width: 120px"
            />
            <span class="input-group-text">小时</span>
          </div>
        </el-form-item>
        <el-form-item label="定义描述">
          <el-input
            v-model="configForm.bLevel.definitionDescription"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
      </div>

      <!-- C级 -->
      <div class="intent-level-section">
        <el-form-item label="C级 - 低意向潜客">
          <div class="input-group">
            <span class="input-group-text">跟进时限</span>
            <el-input-number
              v-model="configForm.cLevel.followUpDurationHours"
              :min="1"
              :max="1440"
              style="width: 120px"
            />
            <span class="input-group-text">小时</span>
          </div>
        </el-form-item>
        <el-form-item label="定义描述">
          <el-input
            v-model="configForm.cLevel.definitionDescription"
            type="textarea"
            :rows="2"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存配置</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getIntentLevelConfig, saveIntentLevelConfig } from '@/api/modules/factory-prospect';
import type { IntentLevelConfig } from '@/api/types/factory-prospect';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:show']);

const showModal = ref(props.show);
const formRef = ref();

// 监听 show 属性变化
watch(() => props.show, (newVal) => {
  showModal.value = newVal;
  if (newVal) {
    fetchIntentLevelConfig();
  }
});

// 监听 showModal 变化，通知父组件
watch(showModal, (newVal) => {
  emit('update:show', newVal);
});

// 表单数据
const configForm = reactive({
  hLevel: {
    id: '',
    intentLevel: 'H级',
    followUpDurationHours: 24,
    definitionDescription: '高意向潜客，明确表示近期有购车计划，并已有明确预算。'
  },
  aLevel: {
    id: '',
    intentLevel: 'A级',
    followUpDurationHours: 72,
    definitionDescription: '较高意向潜客，有明确购买意向但暂未决定，需要继续跟进。'
  },
  bLevel: {
    id: '',
    intentLevel: 'B级',
    followUpDurationHours: 168,
    definitionDescription: '中等意向潜客，有一定购买意向但时间不确定，需要持续维护关系。'
  },
  cLevel: {
    id: '',
    intentLevel: 'C级',
    followUpDurationHours: 720,
    definitionDescription: '低意向潜客，近期无购买打算，但有长期购买可能性。'
  }
});

// 获取意向级别配置
const fetchIntentLevelConfig = async () => {
  try {
    const res = await getIntentLevelConfig();
    if (res && res.configs) {
      res.configs.forEach((config: IntentLevelConfig) => {
        switch (config.intentLevel) {
          case 'H级':
            configForm.hLevel = { ...config };
            break;
          case 'A级':
            configForm.aLevel = { ...config };
            break;
          case 'B级':
            configForm.bLevel = { ...config };
            break;
          case 'C级':
            configForm.cLevel = { ...config };
            break;
          default:
            break;
        }
      });
    }
  } catch (error) {
    console.error('获取意向级别配置失败', error);
    ElMessage.error('获取意向级别配置失败');
  }
};

// 保存配置
const handleSave = async () => {
  try {
    const configs = [
      configForm.hLevel,
      configForm.aLevel,
      configForm.bLevel,
      configForm.cLevel
    ];

    await saveIntentLevelConfig({ configs });
    ElMessage.success('配置保存成功');
    showModal.value = false;
  } catch (error) {
    console.error('保存配置失败', error);
    ElMessage.error('保存配置失败');
  }
};

// 取消
const handleCancel = () => {
  showModal.value = false;
};
</script>

<style scoped>
.alert-info {
  margin-bottom: 20px;
}

.intent-level-section {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.intent-level-section:last-child {
  margin-bottom: 0;
}

.input-group {
  display: flex;
  align-items: center;
}

.input-group-text {
  margin: 0 8px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-button + .el-button) {
  margin-left: 10px;
}
</style>
