<template>
    <div class="page-container">
      <h1 class="page-title">{{ t('title') }}</h1>
  
      <!-- Filter Area -->
      <el-card class="mb-20 search-card">
        <el-form :model="searchParams" class="search-form" label-position="top">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item :label="t('form.inspectionNo')">
                <el-input
                  v-model="searchParams.inspectionNo"
                  :placeholder="t('form.inspectionNo')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('form.inspectionStatus')">
                <el-select
                  v-model="searchParams.inspectionStatus"
                  :placeholder="tc('pleaseSelect')"
                  clearable
                >
                  <el-option
                    :label="t('status.pending')"
                    value="pending"
                  ></el-option>
                  <el-option
                    :label="t('status.in_progress')"
                    value="in_progress"
                  ></el-option>
                  <el-option
                    :label="t('status.pending_confirm')"
                    value="pending_confirm"
                  ></el-option>
                  <el-option
                    :label="t('status.confirmed')"
                    value="confirmed"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('form.licensePlateNo')">
                <el-input
                  v-model="searchParams.licensePlateNo"
                  :placeholder="t('form.licensePlateNo')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('form.repairmanName')">
                <el-input
                  v-model="searchParams.repairmanName"
                  :placeholder="t('form.repairmanName')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('form.technician')">
                <el-input
                  v-model="searchParams.technician"
                  :placeholder="t('form.technician')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('form.repairmanPhone')">
                <el-input
                  v-model="searchParams.repairmanPhone"
                  :placeholder="t('form.repairmanPhone')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="t('form.createTime')">
                <el-date-picker
                  v-model="searchParams.createTimeRange"
                  type="daterange"
                  :range-separator="tc('to')"
                  :start-placeholder="tc('startDate')"
                  :end-placeholder="tc('endDate')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" class="buttons-col">
              <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button @click="resetSearch">{{ tc('reset') }}</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
  
      <!-- Operation Area -->
      <el-card class="mb-20 operation-card">
        <el-button type="primary" :icon="Download" @click="handleExport">{{ tc('export') }}</el-button>
      </el-card>
  
      <!-- Table Area -->
      <el-card class="table-card">
        <el-table :data="tableData" v-loading="loading" style="width: 100%">
          <el-table-column :label="t('table.index')" type="index" min-width="60" />
          <el-table-column
            :label="t('table.inspectionNo')"
            prop="inspectionNo"
            min-width="150"
          />
          <el-table-column
            :label="t('table.inspectionStatus')"
            prop="inspectionStatus"
            min-width="120"
          >
            <template #default="scope">
              {{ t(`status.${scope.row.inspectionStatus}`) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="t('table.repairmanName')"
            prop="repairmanName"
            min-width="120"
          />
          <el-table-column
            :label="t('table.repairmanPhone')"
            prop="repairmanPhone"
            min-width="150"
          />
          <el-table-column
            :label="t('table.licensePlateNo')"
            prop="licensePlateNo"
            min-width="120"
          />
          <el-table-column
            :label="t('createWorkOrderModal.form.vehicleModel')"
            prop="vehicleModel"
            min-width="120"
          />
          <el-table-column
            :label="t('createWorkOrderModal.form.vehicleConfig')"
            prop="vehicleConfig"
            min-width="120"
          />
          <el-table-column :label="t('table.color')" prop="color" min-width="100" />
          <el-table-column
            :label="t('createWorkOrderModal.form.mileage')"
            prop="mileage"
            min-width="100"
          >
            <template #default="scope">
              {{ scope.row.mileage }} {{ t('units.km') }}
            </template>
          </el-table-column>
          <el-table-column
            :label="t('createWorkOrderModal.form.vehicleAge')"
            prop="vehicleAge"
            min-width="100"
          >
            <template #default="scope">
              {{ scope.row.vehicleAge }} {{ t('units.months') }}
            </template>
          </el-table-column>
          <el-table-column
            :label="t('table.serviceAdvisor')"
            prop="serviceAdvisor"
            min-width="120"
          />
          <el-table-column
            :label="t('table.technician')"
            prop="technician"
            min-width="120"
          />
          <el-table-column
            :label="t('table.registerType')"
            prop="registerType"
            min-width="120"
          />
          <el-table-column
            :label="t('table.serviceType')"
            prop="serviceType"
            min-width="120"
          />
          <el-table-column
            :label="t('table.customerConfirmTime')"
            prop="customerConfirmTime"
            min-width="180"
          />
          <el-table-column
            :label="t('table.createTime')"
            prop="createTime"
            min-width="180"
          />
          <el-table-column
            :label="t('table.updateTime')"
            prop="updateTime"
            min-width="180"
          />
          <el-table-column :label="tc('operations')" width="280" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="handleDetail(scope.row)">{{ tc('detail') }}</el-button>
              <el-button
                type="primary"
                link
                :icon="Edit"
                @click="handleEdit(scope.row)"
                v-if="
                  scope.row.inspectionStatus !== 'pending_confirm' &&
                  scope.row.inspectionStatus !== 'confirmed'
                "
              >
                {{ tc('edit') }}
              </el-button>
              <el-button
                type="primary"
                link
                :icon="User"
                @click="handleAssign(scope.row)"
                v-if="
                  scope.row.inspectionStatus === 'pending' ||
                  scope.row.inspectionStatus === 'in_progress'
                "
              >
                {{ tc('assign') }}
              </el-button>
              <el-button
                type="primary"
                link
                @click="handleSubmitConfirm(scope.row)"
                v-if="scope.row.inspectionStatus === 'in_progress'"
              >
                {{ tc('submitConfirm') }}
              </el-button>
              <el-button
                type="danger"
                link
                @click="handleRecall(scope.row)"
                v-if="scope.row.inspectionStatus === 'pending_confirm'"
              >
                {{ tc('recall') }}
              </el-button>
              <el-button
                type="primary"
                link
                @click="handlePrint(scope.row)"
                v-if="scope.row.inspectionStatus === 'pending_confirm'"
              >
                {{ tc('print') }}
              </el-button>
              <el-button
                type="primary"
                link
                @click="handleCustomerConfirm(scope.row)"
                v-if="scope.row.inspectionStatus === 'pending_confirm'"
              >
                {{ tc('customerConfirm') }}
              </el-button>
              <el-button
                type="primary"
                link
                @click="handleCreateWorkOrder(scope.row)"
                v-if="scope.row.inspectionStatus === 'confirmed'"
              >
                {{ tc('createWorkOrder') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          >
          </el-pagination>
        </div>
      </el-card>
      <AssignInspectionFormDialog ref="assignDialogRef" />
      <InspectionFormDetailEditDialog ref="detailEditDialogRef" />
      <CustomerConfirmDialog ref="customerConfirmDialogRef" />
      <WorkOrderCreateModal
        ref="workOrderCreateModalRef"
        v-model="workOrderModalVisible"
        :inspectionFormData="selectedInspectionForm"
        @created="handleWorkOrderCreated"
        @saved="handleWorkOrderSaved"
      />
    </div>
  </template>

  <script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
import { useModuleI18n } from '@/composables/useModuleI18n'
import { Search, Download, Edit, User } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import AssignInspectionFormDialog from './AssignInspectionFormDialog.vue'
import InspectionFormDetailEditDialog from './InspectionFormDetailEditDialog.vue'
import CustomerConfirmDialog from './CustomerConfirmDialog.vue'
import WorkOrderCreateModal from '@/components/WorkOrderCreateModal.vue'
import type { InspectionFormListItem, InspectionFormSearchParams, WorkOrderCreateForm } from '@/types/module.d'

// Internationalization
const { t, tc } = useModuleI18n('inspectionForm')

const assignDialogRef = ref<InstanceType<typeof AssignInspectionFormDialog> | null>(null)
const detailEditDialogRef = ref<InstanceType<typeof InspectionFormDetailEditDialog> | null>(null)
const customerConfirmDialogRef = ref<InstanceType<typeof CustomerConfirmDialog> | null>(null)
const workOrderCreateModalRef = ref<InstanceType<typeof WorkOrderCreateModal> | null>(null)

// Search parameters
const searchParams = reactive<InspectionFormSearchParams>({
  inspectionNo: '',
  inspectionStatus: '',
  licensePlateNo: '',
  repairmanName: '',
  technician: '',
  repairmanPhone: '',
  createTimeRange: null
})

// Table data
const tableData = ref<InspectionFormListItem[]>([])
const loading = ref(false)

// Modal state
const workOrderModalVisible = ref(false)
const selectedInspectionForm = ref<InspectionFormListItem | undefined>(undefined)

// Pagination
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
  
  // Mock API data and delay
  const mockData: InspectionFormListItem[] = [
    {
      inspectionNo: '**********',
      inspectionStatus: 'pending' as const,
      repairmanName: t('mockData.names.zhangSan'),
      repairmanPhone: '13800138000',
      licensePlateNo: '粤A12345',
      vehicleModel: 'Model Y 2023',
      vehicleConfig: t('vehicleConfigs.longRange'),
      color: t('mockData.colors.white'),
      mileage: 10000,
      vehicleAge: 12,
      serviceAdvisor: t('mockData.names.liSi'),
      technician: t('mockData.names.wangWu'),
      registerType: t('mockData.registerTypes.appointment'),
      serviceType: t('mockData.serviceTypes.maintenance'),
      customerConfirmTime: '',
      createTime: '2023-01-01 10:00:00',
      updateTime: '2023-01-01 10:00:00'
    },
    {
      inspectionNo: '**********',
      inspectionStatus: 'in_progress' as const,
      repairmanName: t('mockData.names.liHua'),
      repairmanPhone: '13912345678',
      licensePlateNo: '京B67890',
      vehicleModel: 'Model 3 2022',
      vehicleConfig: t('vehicleConfigs.standardRange'),
      color: t('mockData.colors.black'),
      mileage: 20000,
      vehicleAge: 24,
      serviceAdvisor: t('mockData.names.zhaoLiu'),
      technician: t('mockData.names.wangWu'),
      registerType: t('mockData.registerTypes.walkIn'),
      serviceType: t('mockData.serviceTypes.repair'),
      customerConfirmTime: '',
      createTime: '2023-01-02 11:30:00',
      updateTime: '2023-01-02 14:00:00'
    },
    {
      inspectionNo: '**********',
      inspectionStatus: 'pending_confirm' as const,
      repairmanName: t('mockData.names.wangDaChui'),
      repairmanPhone: '13011112222',
      licensePlateNo: '沪C54321',
      vehicleModel: 'Model X 2024',
      vehicleConfig: t('vehicleConfigs.performance'),
      color: t('mockData.colors.blue'),
      mileage: 30000,
      vehicleAge: 36,
      serviceAdvisor: t('mockData.names.qianQi'),
      technician: t('mockData.names.sunBa'),
      registerType: t('mockData.registerTypes.appointment'),
      serviceType: t('mockData.serviceTypes.repair'),
      customerConfirmTime: '',
      createTime: '2023-01-03 09:00:00',
      updateTime: '2023-01-03 16:30:00'
    },
    {
      inspectionNo: '**********',
      inspectionStatus: 'confirmed' as const,
      repairmanName: t('mockData.names.linYiYi'),
      repairmanPhone: '13133334444',
      licensePlateNo: '苏D98765',
      vehicleModel: 'Model Y 2023',
      vehicleConfig: t('vehicleConfigs.performance'),
      color: t('mockData.colors.gray'),
      mileage: 40000,
      vehicleAge: 48,
      serviceAdvisor: t('mockData.names.zhouJiu'),
      technician: t('mockData.names.wuShi'),
      registerType: t('mockData.registerTypes.walkIn'),
      serviceType: t('mockData.serviceTypes.maintenance'),
      customerConfirmTime: '2023-01-04 11:00:00',
      createTime: '2023-01-04 08:00:00',
      updateTime: '2023-01-04 11:00:00'
    }
  ]

  const fetchData = () => {
    loading.value = true
    setTimeout(() => {
      // Mock filtering and pagination logic
      const filteredData = mockData.filter(item => {
        const matchesInspectionNo = searchParams.inspectionNo ? item.inspectionNo.includes(searchParams.inspectionNo) : true
        const matchesStatus = searchParams.inspectionStatus ? item.inspectionStatus === searchParams.inspectionStatus : true
        const matchesLicensePlateNo = searchParams.licensePlateNo ? item.licensePlateNo.includes(searchParams.licensePlateNo) : true
        const matchesRepairmanName = searchParams.repairmanName ? item.repairmanName.includes(searchParams.repairmanName) : true
        const matchesTechnician = searchParams.technician ? item.technician.includes(searchParams.technician) : true
        const matchesRepairmanPhone = searchParams.repairmanPhone ? item.repairmanPhone.includes(searchParams.repairmanPhone) : true

        let matchesCreateTime = true
        if (searchParams.createTimeRange && searchParams.createTimeRange.length === 2) {
          const [startDate, endDate] = searchParams.createTimeRange
          if (startDate && endDate) {
            const itemCreateTime = new Date(item.createTime)
            // Convert start and end date to YYYY-MM-DD for comparison
            const searchStartDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())
            const searchEndDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate(), 23, 59, 59)

            matchesCreateTime = itemCreateTime >= searchStartDate && itemCreateTime <= searchEndDate
          } else {
            matchesCreateTime = false
          }
        }
        return matchesInspectionNo && matchesStatus && matchesLicensePlateNo && matchesRepairmanName && matchesTechnician && matchesRepairmanPhone && matchesCreateTime
      })

      pagination.total = filteredData.length
      const start = (pagination.currentPage - 1) * pagination.pageSize
      const end = start + pagination.pageSize
      tableData.value = filteredData.slice(start, end)
      loading.value = false
    }, 500)
  }

  onMounted(() => {
    fetchData()
  })

  const handleSearch = () => {
    pagination.currentPage = 1
    fetchData()
  }

  const resetSearch = () => {
    searchParams.inspectionNo = ''
    searchParams.inspectionStatus = ''
    searchParams.licensePlateNo = ''
    searchParams.repairmanName = ''
    searchParams.technician = ''
    searchParams.repairmanPhone = ''
    searchParams.createTimeRange = null
    pagination.currentPage = 1
    fetchData()
  }

  const handleExport = () => {
    ElMessage.success(tc('exporting'))
    console.log(t('messages.exportingData'), searchParams)
  }

  const handleSizeChange = (val: number) => {
    pagination.pageSize = val
    fetchData()
  }

  const handleCurrentChange = (val: number) => {
    pagination.currentPage = val
    fetchData()
  }

  const handleDetail = (row: InspectionFormListItem) => {
    console.log(t('messages.viewDetail'), row)
    detailEditDialogRef.value?.open(row, false)
  }

  const handleAssign = (row: InspectionFormListItem) => {
    console.log(t('messages.assignInspectionForm'), row)
    assignDialogRef.value?.open({
      inspectionNo: row.inspectionNo,
      licensePlateNo: row.licensePlateNo,
      repairmanName: row.repairmanName,
      registerType: row.registerType,
      serviceType: row.serviceType,
    })
  }

  const handleEdit = (row: InspectionFormListItem) => {
    console.log(t('messages.editInspectionForm'), row)
    detailEditDialogRef.value?.open(row, true)
  }

  const handleSubmitConfirm = (row: InspectionFormListItem) => {
    ElMessageBox.confirm(
      `${t('common.confirmSubmitConfirm')}`, // Use internationalized text
      `${t('common.tip')}`, // Use internationalized text
      {
        confirmButtonText: t('common.confirm'), // Use internationalized text
        cancelButtonText: t('common.cancel'), // Use internationalized text
        type: 'warning'
      }
    )
      .then(() => {
        console.log(t('messages.submitConfirm'), row)
        ElMessage.success(t('common.operationSuccessful'))
        // Mock status update
        row.inspectionStatus = 'pendingConfirm'
        fetchData()
      })
      .catch(() => {
        ElMessage.info(tc('operationCanceled'))
      })
  }

  const handleRecall = (row: InspectionFormListItem) => {
    ElMessageBox.confirm(
      `${t('common.confirmRecall')}`, // Use internationalized text
      `${t('common.tip')}`, // Use internationalized text
      {
        confirmButtonText: t('common.confirm'), // Use internationalized text
        cancelButtonText: t('common.cancel'), // Use internationalized text
        type: 'warning'
      }
    )
      .then(() => {
        console.log(t('messages.recall'), row)
        ElMessage.success(t('common.operationSuccessful'))
        // Mock status update
        row.inspectionStatus = 'inProgress'
        fetchData()
      })
      .catch(() => {
        ElMessage.info(tc('operationCanceled'))
      })
  }

  const handlePrint = (row: InspectionFormListItem) => {
    ElMessageBox.confirm(
      `${t('common.confirmPrint')}`, // Use internationalized text
      `${t('common.tip')}`, // Use internationalized text
      {
        confirmButtonText: t('common.confirm'), // Use internationalized text
        cancelButtonText: t('common.cancel'), // Use internationalized text
        type: 'info'
      }
    )
      .then(() => {
        console.log(t('messages.print'), row)
        ElMessage.success(tc('operationSuccessful'))
      })
      .catch(() => {
        ElMessage.info(tc('operationCanceled'))
      })
  }

  const handleCustomerConfirm = (row: InspectionFormListItem) => {
    console.log(t('messages.customerConfirm'), row)
    customerConfirmDialogRef.value?.open({
      inspectionNo: row.inspectionNo,
      licensePlateNo: row.licensePlateNo,
      repairmanName: row.repairmanName,
    })
  }

  const handleCreateWorkOrder = (row: InspectionFormListItem) => {
    console.log(t('messages.createWorkOrder'), row)
    selectedInspectionForm.value = row
    workOrderModalVisible.value = true
  }

  // Work order creation and save handling
  const handleWorkOrderCreated = (workOrder: WorkOrderCreateForm) => {
    console.log('Work order created successfully:', workOrder)
    ElMessage.success(tc('createWorkOrderModal.messages.createSuccess'))
    // Here you can call API to save work order data
    // May need to refresh list or perform other operations
  }

  const handleWorkOrderSaved = (workOrder: WorkOrderCreateForm) => {
    console.log('Work order draft saved:', workOrder)
    ElMessage.success(tc('createWorkOrderModal.messages.saveDraftSuccess'))
    // Here you can call API to save draft data
  }
  </script>

  <style scoped lang="scss">
  @use '@/assets/styles/_variables.scss' as *;

  .page-container {
    padding: 20px;
  }

  .page-title {
    margin-bottom: 20px;
  }

  .search-card,
  .operation-card {
    margin-bottom: 20px;
  }

  .search-form {
    .el-form-item {
      margin-right: 20px;
      margin-bottom: 15px;
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .buttons-col {
    text-align: right;
    .el-button {
      margin-left: 10px;
    }
  }

  .table-card {
    margin-bottom: 20px; /* Consistent with pagination */
  }

  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap; // Prevent text wrapping
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  </style>
