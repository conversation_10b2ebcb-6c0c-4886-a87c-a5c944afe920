<template>
    <el-dialog
      :title="isEdit ? t('detailEdit.titleEdit') : t('detailEdit.titleDetail')"
      v-model="dialogVisible"
      width="80%"
      :before-close="handleClose"
      class="inspection-form-detail-edit-dialog"
    >
      <el-scrollbar max-height="70vh">
        <el-form :model="formData" label-position="top" class="dialog-form-modern">
          <!-- 客户信息区 -->
          <el-card class="mb-20" shadow="never">
            <template #header>
              <div class="card-header">{{ t('detailEdit.clientInfo') }}</div>
            </template>
            <el-row :gutter="20">
              <el-col :span="8" v-if="formData.registerType === t('detailEdit.registerTypeAppointment')">
                <el-form-item :label="t('detailEdit.reserveName')">
                  <el-input v-model="formData.reserveName" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="formData.registerType === t('detailEdit.registerTypeAppointment')">
                <el-form-item :label="t('detailEdit.reservePhone')">
                  <el-input v-model="formData.reservePhone" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.repairmanName')">
                  <el-input v-model="formData.repairmanName" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.repairmanPhone')">
                  <el-input v-model="formData.repairmanPhone" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.remark')">
                  <el-input type="textarea" v-model="formData.remark" disabled />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>

          <!-- 车辆信息区 -->
          <el-card class="mb-20" shadow="never">
            <template #header>
              <div class="card-header">{{ t('detailEdit.vehicleInfo') }}</div>
            </template>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.licensePlateNo')">
                  <el-input v-model="formData.licensePlateNo" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.vin')">
                  <el-input v-model="formData.vin" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.modelConfig')">
                  <el-input v-model="formData.modelConfig" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.color')">
                  <el-input v-model="formData.color" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.mileage')">
                  <el-input v-model="formData.mileage" :disabled="!isEdit" placeholder="km" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.vehicleAge')">
                  <el-input v-model="formData.vehicleAge" disabled />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>

          <!-- 环检单信息区 -->
          <el-card class="mb-20" shadow="never">
            <template #header>
              <div class="card-header">{{ t('detailEdit.inspectionFormInfo') }}</div>
            </template>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.inspectionStatus')">
                  <el-input v-model="formData.inspectionStatus" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.registerType')">
                  <el-input v-model="formData.registerType" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="formData.serviceType === t('detailEdit.serviceTypeMaintenance')">
                <el-form-item :label="t('detailEdit.servicePackageName')">
                  <el-input v-model="formData.servicePackageName" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.createTime')">
                  <el-input v-model="formData.createTime" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.serviceAdvisor')">
                  <el-input v-model="formData.serviceAdvisor" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.technician')">
                  <el-input v-model="formData.technician" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.customerConfirmTime')">
                  <el-input v-model="formData.customerConfirmTime" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="!isEdit && formData.customerConfirmImage">
                <el-form-item :label="t('detailEdit.customerConfirmImage')">
                  <el-image
                    style="width: 100px; height: 100px"
                    :src="formData.customerConfirmImage"
                    :preview-src-list="[formData.customerConfirmImage]"
                    :initial-index="0"
                    fit="cover"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>

          <!-- 环检内容清单区 -->
          <el-card class="mb-20" shadow="never">
            <template #header>
              <div class="card-header">{{ t('detailEdit.inspectionContentList') }}</div>
            </template>

            <!-- 停车区域记录 -->
            <h4 class="section-title">{{ t('detailEdit.parkingAreaRecord') }}</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.waitingArea')">
                  <el-input v-model="formData.inspectionContent.parkingAreaRecord.waitingArea" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.leavingArea')">
                  <el-input v-model="formData.inspectionContent.parkingAreaRecord.leavingArea" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.parkingZone')">
                  <el-input v-model="formData.inspectionContent.parkingAreaRecord.parkingZone" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 仪表盘检查 -->
            <h4 class="section-title">{{ t('detailEdit.dashboardInspection') }}</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.mileageRecord')">
                  <el-input v-model="formData.inspectionContent.dashboardInspection.mileageRecord" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.batteryLevel')">
                  <el-select v-model="formData.inspectionContent.dashboardInspection.batteryLevel" :disabled="!isEdit">
                    <el-option label="0-20%" value="0-20%" />
                    <el-option label="20-40%" value="20-40%" />
                    <el-option label="40-60%" value="40-60%" />
                    <el-option label="60-80%" value="60-80%" />
                    <el-option label="80-100%" value="80-100%" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.remainingRange')">
                  <el-input v-model="formData.inspectionContent.dashboardInspection.remainingRange" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.energyConsumption')">
                  <el-input v-model="formData.inspectionContent.dashboardInspection.energyConsumption" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 功能性检查 -->
            <h4 class="section-title">{{ t('detailEdit.functionalityCheck') }}</h4>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.gaugesIndicators')">
                  <el-radio-group v-model="formData.inspectionContent.functionalityCheck.gaugesIndicators" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.airConditioningSystem')">
                  <el-radio-group v-model="formData.inspectionContent.functionalityCheck.airConditioningSystem" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.wiperWasher')">
                  <el-radio-group v-model="formData.inspectionContent.functionalityCheck.wiperWasher" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.infotainmentSystem')">
                  <el-radio-group v-model="formData.inspectionContent.functionalityCheck.infotainmentSystem" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 警告灯检查 -->
            <h4 class="section-title">{{ t('detailEdit.warningLightsCheck') }}</h4>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="">
                  <el-radio-group v-model="formData.inspectionContent.warningLightsCheck" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 外观检查 -->
            <h4 class="section-title">{{ t('detailEdit.exteriorInspection') }}</h4>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.bodyExteriorInspection')">
                  <el-radio-group v-model="formData.inspectionContent.exteriorInspection.bodyExteriorInspection" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.chargingPortCover')">
                  <el-radio-group v-model="formData.inspectionContent.exteriorInspection.chargingPortCover" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 电动系统检查 -->
            <h4 class="section-title">{{ t('detailEdit.electricSystemInspection') }}</h4>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.batteryPackVisualInspection')">
                  <el-radio-group v-model="formData.inspectionContent.electricSystemInspection.highVoltageBatteryCheck.batteryPackVisualInspection" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.batteryCoolingSystem')">
                  <el-radio-group v-model="formData.inspectionContent.electricSystemInspection.highVoltageBatteryCheck.batteryCoolingSystem" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.highVoltageCableInspection')">
                  <el-radio-group v-model="formData.inspectionContent.electricSystemInspection.highVoltageBatteryCheck.highVoltageCableInspection" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item :label="t('detailEdit.driveMotor')">
                  <el-radio-group v-model="formData.inspectionContent.electricSystemInspection.driveMotor" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.motorController')">
                  <el-radio-group v-model="formData.inspectionContent.electricSystemInspection.motorController" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <p>{{ t('detailEdit.chargingSystemCheck') }}</p>
                <el-form-item :label="t('detailEdit.onboardCharger')">
                  <el-radio-group v-model="formData.inspectionContent.electricSystemInspection.onboardCharger" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.chargingInterface')">
                  <el-radio-group v-model="formData.inspectionContent.electricSystemInspection.chargingInterface" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 轮胎磨损检查 -->
            <h4 class="section-title">{{ t('detailEdit.tireWearInspection') }}</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.tyreThreadCheck')">
                  <el-input v-model="formData.inspectionContent.tireWearInspection.tyreThreadCheck" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.checkDeviation')">
                  <el-input v-model="formData.inspectionContent.tireWearInspection.checkDeviation" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="t('detailEdit.lowRollingResistanceTyreStatus')">
                  <el-input v-model="formData.inspectionContent.tireWearInspection.lowRollingResistanceTyreStatus" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 轮胎位置 -->
            <h4 class="section-title">{{ t('detailEdit.tirePosition') }}</h4>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.frontRight')">
                  <el-input v-model="formData.inspectionContent.tirePosition.frontRight" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.frontLeft')">
                  <el-input v-model="formData.inspectionContent.tirePosition.frontLeft" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.rearRight')">
                  <el-input v-model="formData.inspectionContent.tirePosition.rearRight" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="t('detailEdit.rearLeft')">
                  <el-input v-model="formData.inspectionContent.tirePosition.rearLeft" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 轮胎压力监测 -->
            <h4 class="section-title">{{ t('detailEdit.tirePressureMonitoring') }}</h4>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="">
                  <el-radio-group v-model="formData.inspectionContent.tirePressureMonitoring" :disabled="!isEdit">
                    <el-radio :label="tc('good')" value="good" />
                    <el-radio :label="tc('needsAttention')" value="needsAttention" />
                    <el-radio :label="tc('bad')" value="bad" />
                    <el-radio :label="tc('notApplicable')" value="notApplicable" />
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 客户自述问题 -->
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="t('detailEdit.customerSelfDescribedProblem')">
                  <el-input type="textarea" v-model="formData.inspectionContent.customerSelfDescribedProblem" :disabled="!isEdit" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </el-form>
      </el-scrollbar>

      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="handleClose">{{ tc('cancel') }}</el-button>
          <el-button type="primary" @click="handleSubmit" v-if="isEdit">{{ tc('save') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </template>

  <script setup lang="ts">
  import { ref, reactive } from 'vue'
  import { useModuleI18n } from '@/composables/useModuleI18n'
  import { ElMessage } from 'element-plus'
  import type { InspectionFormListItem } from '@/types/module'

  const { t,tc } = useModuleI18n('inspectionForm')

  interface InspectionFormDetail extends InspectionFormListItem {
    inspectionContent: any
  }

  const dialogVisible = ref(false)
  const isEdit = ref(false)
  const formData = reactive<InspectionFormDetail>({
    inspectionNo: '',
    inspectionStatus: 'pending',
    repairmanName: '',
    repairmanPhone: '',
    licensePlateNo: '',
    vehicleModel: '',
    vehicleConfig: '',
    color: '',
    mileage: 0,
    vehicleAge: 0,
    serviceAdvisor: '',
    technician: '',
    registerType: '',
    serviceType: '',
    customerConfirmTime: '',
    createTime: '',
    updateTime: '',
    // 客户信息
    reserveName: '',
    reservePhone: '',
    remark: '',
    // 车辆信息
    vin: '',
    modelConfig: '',
    // 环检单信息
    servicePackageName: '',
    customerConfirmImage: '',
    // 环检内容清单
    inspectionContent: {
      parkingAreaRecord: {
        waitingArea: '',
        leavingArea: '',
        parkingZone: '',
      },
      dashboardInspection: {
        mileageRecord: '',
        batteryLevel: '',
        remainingRange: '',
        energyConsumption: '',
      },
      functionalityCheck: {
        gaugesIndicators: '',
        airConditioningSystem: '',
        wiperWasher: '',
        infotainmentSystem: '',
      },
      warningLightsCheck: '',
      exteriorInspection: {
        bodyExteriorInspection: '',
        chargingPortCover: '',
      },
      electricSystemInspection: {
        highVoltageBatteryCheck: {
          batteryPackVisualInspection: '',
          batteryCoolingSystem: '',
          highVoltageCableInspection: '',
        },
        chargingSystemCheck: {
          onboardCharger: '',
          chargingInterface: '',
        },
        driveMotor: '',
        motorController: '',
        onboardCharger: '',
        chargingInterface: ''
      },
      tireWearInspection: {
        tyreThreadCheck: '',
        checkDeviation: '',
        lowRollingResistanceTyreStatus: '',
      },
      tirePosition: {
        frontRight: '',
        frontLeft: '',
        rearRight: '',
        rearLeft: '',
      },
      tirePressureMonitoring: '',
      customerSelfDescribedProblem: '',
    }
  })

  const open = (data: InspectionFormListItem, editMode: boolean) => {
    dialogVisible.value = true
    isEdit.value = editMode
    Object.assign(formData, data)

    // For demonstration, deeply assign inspectionContent if it exists in data
    if (data.inspectionContent) {
      Object.assign(formData.inspectionContent, data.inspectionContent)
    }

    // Simulate image URL if in detail mode
    if (!editMode) {
      formData.customerConfirmImage = 'https://picsum.photos/200/300?random=1'; // Simulated image URL
    } else {
      formData.customerConfirmImage = ''; // Clear image if in edit mode
    }
  }

  const handleClose = () => {
    dialogVisible.value = false
    // Reset form data
    Object.keys(formData).forEach(key => {
      if (typeof formData[key] === 'object' && formData[key] !== null) {
        if (Array.isArray(formData[key])) {
          formData[key] = ''
        } else {
          // Deep reset for nested objects, simple for demonstration
          for (const subKey in formData[key]) {
            if (typeof formData[key][subKey] === 'object' && formData[key][subKey] !== null) {
              if (Array.isArray(formData[key][subKey])) {
                formData[key][subKey] = ''
              } else {
                for (const deepKey in formData[key][subKey]) {
                  if (typeof formData[key][subKey][deepKey] === 'object' && formData[key][subKey][deepKey] !== null) {
                    if (Array.isArray(formData[key][subKey][deepKey])) {
                      formData[key][subKey][deepKey] = ''
                    } else {
                      formData[key][subKey][deepKey] = ''
                    }
                  } else {
                    formData[key][subKey][deepKey] = ''
                  }
                }
              }
            } else {
              formData[key][subKey] = ''
            }
          }
        }
      } else {
        formData[key] = ''
      }
    })
    // Ensure customerConfirmImage is reset on close
    formData.customerConfirmImage = '';
  }

  const handleSubmit = () => {
    // Implement save logic here
    ElMessage.success(tc('operationSuccessful'))
    console.log(t('messages.saveInspectionFormDetail'), formData)
    handleClose()
  }

  defineExpose({
    open
  })
  </script>

  <style scoped lang="scss">
  @use '@/assets/styles/_variables.scss' as *;

  .dialog-form-modern {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .card-header {
    font-weight: bold;
    font-size: 16px;
  }

  .section-title {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 15px;
    color: #333;
  }

  .dialog-footer-buttons {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
    .el-button {
      margin-left: 10px;
    }
  }
  </style>
