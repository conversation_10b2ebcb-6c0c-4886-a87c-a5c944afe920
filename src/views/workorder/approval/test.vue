<template>
  <div class="test-page">
    <h1>工单审批测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正确。</p>
    <el-button type="primary" @click="goToMainPage">
      前往主页面
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToMainPage = () => {
  router.push('/work-order-approval')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  text-align: center;
}
</style>
