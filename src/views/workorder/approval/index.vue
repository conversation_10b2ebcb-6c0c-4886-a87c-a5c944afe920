<template>
  <div class="work-order-approval">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ $t('workOrderApproval.title') }}</h1>
      <div class="page-actions">
        <el-button
          type="primary"
          :icon="Download"
          @click="handleExport"
          :loading="exportLoading"
        >
          {{ $t('common.export') }}
        </el-button>
      </div>
    </div>

    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="approval-tabs">
      <el-tab-pane
        :label="`${$t('workOrderApproval.pendingApproval')} (${pendingTotal})`"
        name="pending"
      >
        <!-- 筛选区域 -->
        <div class="filter-section">
          <el-form :model="filterForm" inline class="filter-form">
            <el-form-item :label="$t('workOrderApproval.approvalNo')">
              <el-input
                v-model="filterForm.approvalNo"
                :placeholder="$t('workOrderApproval.approvalNoPlaceholder')"
                clearable
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.approvalType')">
              <el-select
                v-model="filterForm.approvalType"
                :placeholder="$t('common.pleaseSelect')"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="option in approvalTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.submitter')">
              <el-input
                v-model="filterForm.submitterName"
                :placeholder="$t('workOrderApproval.submitterPlaceholder')"
                clearable
                style="width: 150px"
              />
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.orderNo')">
              <el-input
                v-model="filterForm.orderNo"
                :placeholder="$t('workOrderApproval.orderNoPlaceholder')"
                clearable
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.submitTime')">
              <el-date-picker
                v-model="submitTimeRange"
                type="datetimerange"
                :range-separator="$t('common.to')"
                :start-placeholder="$t('common.startTime')"
                :end-placeholder="$t('common.endTime')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 350px"
              />
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.customer')">
              <el-input
                v-model="filterForm.customerName"
                :placeholder="$t('workOrderApproval.customerPlaceholder')"
                clearable
                style="width: 150px"
              />
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.licensePlate')">
              <el-input
                v-model="filterForm.licensePlate"
                :placeholder="$t('workOrderApproval.licensePlatePlaceholder')"
                clearable
                style="width: 150px"
              />
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.timeoutStatus')">
              <el-select
                v-model="filterForm.timeoutStatus"
                :placeholder="$t('common.pleaseSelect')"
                clearable
                style="width: 120px"
              >
                <el-option
                  v-for="option in timeoutStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.approvalLevel')">
              <el-select
                v-model="filterForm.currentLevel"
                :placeholder="$t('common.pleaseSelect')"
                clearable
                style="width: 120px"
              >
                <el-option
                  v-for="option in approvalLevelOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                {{ $t('common.search') }}
              </el-button>
              <el-button @click="handleReset">
                {{ $t('common.reset') }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 待审批列表 -->
        <div class="table-section">
          <el-table
            :data="pendingList"
            v-loading="loading"
            stripe
            border
            height="600"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column
              prop="approvalNo"
              :label="$t('workOrderApproval.approvalNo')"
              width="180"
              fixed="left"
            />
            <el-table-column
              prop="approvalType"
              :label="$t('workOrderApproval.approvalType')"
              width="120"
            >
              <template #default="{ row }">
                <el-tag :type="getApprovalTypeTagType(row.approvalType)">
                  {{ getApprovalTypeText(row.approvalType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="submitterName"
              :label="$t('workOrderApproval.submitter')"
              width="100"
            />
            <el-table-column
              prop="submitTime"
              :label="$t('workOrderApproval.submitTime')"
              width="160"
            />
            <el-table-column
              prop="orderNo"
              :label="$t('workOrderApproval.orderNo')"
              width="180"
            />
            <el-table-column
              prop="requestReason"
              :label="$t('workOrderApproval.requestReason')"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="timeoutStatus"
              :label="$t('workOrderApproval.timeoutStatus')"
              width="120"
            >
              <template #default="{ row }">
                <el-tag :type="getTimeoutStatusTagType(row.timeoutStatus)">
                  {{ getTimeoutStatusText(row.timeoutStatus) }}
                </el-tag>
                <div v-if="row.remainingTime" class="remaining-time">
                  {{ row.remainingTime }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="customerName"
              :label="$t('workOrderApproval.customer')"
              width="100"
            />
            <el-table-column
              prop="licensePlate"
              :label="$t('workOrderApproval.licensePlate')"
              width="120"
            />
            <el-table-column
              prop="vehicleModel"
              :label="$t('workOrderApproval.vehicleModel')"
              width="150"
            />
            <el-table-column
              prop="storeName"
              :label="$t('workOrderApproval.store')"
              width="120"
            />
            <el-table-column
              prop="currentLevel"
              :label="$t('workOrderApproval.currentLevel')"
              width="100"
            >
              <template #default="{ row }">
                {{ getApprovalLevelText(row.currentLevel) }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('common.actions')"
              width="200"
              fixed="right"
            >
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleApproval(row)"
                >
                  {{ $t('workOrderApproval.approve') }}
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="handleViewDetail(row)"
                >
                  {{ $t('common.detail') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.pageSize"
              :total="pendingTotal"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane
        :label="`${$t('workOrderApproval.completedApproval')} (${completedTotal})`"
        name="completed"
      >
        <!-- 已审批筛选区域 -->
        <div class="filter-section">
          <el-form :model="filterForm" inline class="filter-form">
            <el-form-item :label="$t('workOrderApproval.approvalNo')">
              <el-input
                v-model="filterForm.approvalNo"
                :placeholder="$t('workOrderApproval.approvalNoPlaceholder')"
                clearable
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.approvalType')">
              <el-select
                v-model="filterForm.approvalType"
                :placeholder="$t('common.pleaseSelect')"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="option in approvalTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.submitter')">
              <el-input
                v-model="filterForm.submitterName"
                :placeholder="$t('workOrderApproval.submitterPlaceholder')"
                clearable
                style="width: 150px"
              />
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.orderNo')">
              <el-input
                v-model="filterForm.orderNo"
                :placeholder="$t('workOrderApproval.orderNoPlaceholder')"
                clearable
                style="width: 200px"
              />
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.approvalResult')">
              <el-select
                v-model="filterForm.approvalResult"
                :placeholder="$t('common.pleaseSelect')"
                clearable
                style="width: 120px"
              >
                <el-option
                  v-for="option in approvalResultOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('workOrderApproval.approvalTime')">
              <el-date-picker
                v-model="approvalTimeRange"
                type="datetimerange"
                :range-separator="$t('common.to')"
                :start-placeholder="$t('common.startTime')"
                :end-placeholder="$t('common.endTime')"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 350px"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                {{ $t('common.search') }}
              </el-button>
              <el-button @click="handleReset">
                {{ $t('common.reset') }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 已审批列表 -->
        <div class="table-section">
          <el-table
            :data="completedList"
            v-loading="loading"
            stripe
            border
            height="600"
          >
            <el-table-column
              prop="approvalNo"
              :label="$t('workOrderApproval.approvalNo')"
              width="180"
              fixed="left"
            />
            <el-table-column
              prop="approvalType"
              :label="$t('workOrderApproval.approvalType')"
              width="120"
            >
              <template #default="{ row }">
                <el-tag :type="getApprovalTypeTagType(row.approvalType)">
                  {{ getApprovalTypeText(row.approvalType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="submitterName"
              :label="$t('workOrderApproval.submitter')"
              width="100"
            />
            <el-table-column
              prop="submitTime"
              :label="$t('workOrderApproval.submitTime')"
              width="160"
            />
            <el-table-column
              prop="orderNo"
              :label="$t('workOrderApproval.orderNo')"
              width="180"
            />
            <el-table-column
              prop="requestReason"
              :label="$t('workOrderApproval.requestReason')"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="approvalResult"
              :label="$t('workOrderApproval.approvalResult')"
              width="120"
            >
              <template #default="{ row }">
                <el-tag :type="getApprovalResultTagType(row.approvalResult)">
                  {{ getApprovalResultText(row.approvalResult) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="approvalRemark"
              :label="$t('workOrderApproval.approvalRemark')"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              prop="approvalTime"
              :label="$t('workOrderApproval.approvalTime')"
              width="160"
            />
            <el-table-column
              prop="approverName"
              :label="$t('workOrderApproval.approver')"
              width="100"
            />
            <el-table-column
              prop="customerName"
              :label="$t('workOrderApproval.customer')"
              width="100"
            />
            <el-table-column
              prop="licensePlate"
              :label="$t('workOrderApproval.licensePlate')"
              width="120"
            />
            <el-table-column
              prop="vehicleModel"
              :label="$t('workOrderApproval.vehicleModel')"
              width="150"
            />
            <el-table-column
              prop="storeName"
              :label="$t('workOrderApproval.store')"
              width="120"
            />
            <el-table-column
              :label="$t('common.actions')"
              width="120"
              fixed="right"
            >
              <template #default="{ row }">
                <el-button
                  type="info"
                  size="small"
                  @click="handleViewDetail(row)"
                >
                  {{ $t('common.detail') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.pageSize"
              :total="completedTotal"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 审批弹窗 -->
    <ClaimApprovalModal
      v-if="showClaimModal"
      v-model:visible="showClaimModal"
      :approval-no="currentApprovalNo"
      @success="handleApprovalSuccess"
    />

    <CancelApprovalModal
      v-if="showCancelModal"
      v-model:visible="showCancelModal"
      :approval-no="currentApprovalNo"
      @success="handleApprovalSuccess"
    />

    <!-- 详情查看弹窗 -->
    <ApprovalDetailModal
      v-if="showDetailModal"
      v-model:visible="showDetailModal"
      :approval-no="currentApprovalNo"
      :approval-type="currentApprovalType"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'
import type {
  PendingApprovalListItem,
  CompletedApprovalListItem,
  WorkOrderApprovalListParams,
  WorkOrderApprovalType
} from '@/types/module'
import {
  getPendingApprovalList,
  getCompletedApprovalList,
  exportApprovalData
} from '@/api/modules/workOrderApproval'

// 组件导入（稍后创建）
import ClaimApprovalModal from './components/ClaimApprovalModal.vue'
import CancelApprovalModal from './components/CancelApprovalModal.vue'
import ApprovalDetailModal from './components/ApprovalDetailModal.vue'

const { t } = useI18n()

// 响应式数据
const activeTab = ref('pending')
const loading = ref(false)
const exportLoading = ref(false)

// 筛选表单
const filterForm = reactive<WorkOrderApprovalListParams>({
  approvalNo: '',
  approvalType: undefined,
  submitterName: '',
  orderNo: '',
  customerName: '',
  licensePlate: '',
  timeoutStatus: undefined,
  currentLevel: undefined
})

const submitTimeRange = ref<[string, string] | null>(null)
const approvalTimeRange = ref<[string, string] | null>(null)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20
})

// 列表数据
const pendingList = ref<PendingApprovalListItem[]>([])
const completedList = ref<CompletedApprovalListItem[]>([])
const pendingTotal = ref(0)
const completedTotal = ref(0)
const selectedRows = ref<PendingApprovalListItem[]>([])

// 弹窗控制
const showClaimModal = ref(false)
const showCancelModal = ref(false)
const showDetailModal = ref(false)
const currentApprovalNo = ref('')
const currentApprovalType = ref<WorkOrderApprovalType>('claim_approval')

// 下拉选项
const approvalTypeOptions = computed(() => [
  { label: t('workOrderApproval.claimApproval'), value: 'claim_approval' },
  { label: t('workOrderApproval.cancelApproval'), value: 'cancel_approval' }
])

const timeoutStatusOptions = computed(() => [
  { label: t('workOrderApproval.normal'), value: 'normal' },
  { label: t('workOrderApproval.aboutToTimeout'), value: 'about_to_timeout' },
  { label: t('workOrderApproval.timeout'), value: 'timeout' }
])

const approvalLevelOptions = computed(() => [
  { label: t('workOrderApproval.firstLevel'), value: 'first_level' },
  { label: t('workOrderApproval.secondLevel'), value: 'second_level' }
])

const approvalResultOptions = computed(() => [
  { label: t('workOrderApproval.approved'), value: 'approved' },
  { label: t('workOrderApproval.rejected'), value: 'rejected' }
])

// 方法
const loadPendingList = async () => {
  try {
    loading.value = true
    const params: WorkOrderApprovalListParams = {
      ...filterForm,
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    if (submitTimeRange.value) {
      params.submitTimeStart = submitTimeRange.value[0]
      params.submitTimeEnd = submitTimeRange.value[1]
    }

    const result = await getPendingApprovalList(params)
    pendingList.value = result.list
    pendingTotal.value = result.total
  } catch (error) {
    ElMessage.error(t('common.loadFailed'))
  } finally {
    loading.value = false
  }
}

const loadCompletedList = async () => {
  try {
    loading.value = true
    const params: WorkOrderApprovalListParams = {
      ...filterForm,
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    if (submitTimeRange.value) {
      params.submitTimeStart = submitTimeRange.value[0]
      params.submitTimeEnd = submitTimeRange.value[1]
    }

    if (approvalTimeRange.value) {
      params.approvalTimeStart = approvalTimeRange.value[0]
      params.approvalTimeEnd = approvalTimeRange.value[1]
    }

    const result = await getCompletedApprovalList(params)
    completedList.value = result.list
    completedTotal.value = result.total
  } catch (error) {
    ElMessage.error(t('common.loadFailed'))
  } finally {
    loading.value = false
  }
}

const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
  pagination.page = 1
  if (tabName === 'pending') {
    loadPendingList()
  } else {
    loadCompletedList()
  }
}

const handleSearch = () => {
  pagination.page = 1
  if (activeTab.value === 'pending') {
    loadPendingList()
  } else {
    loadCompletedList()
  }
}

const handleReset = () => {
  Object.assign(filterForm, {
    approvalNo: '',
    approvalType: undefined,
    submitterName: '',
    orderNo: '',
    customerName: '',
    licensePlate: '',
    timeoutStatus: undefined,
    currentLevel: undefined,
    approvalResult: undefined
  })
  submitTimeRange.value = null
  approvalTimeRange.value = null
  handleSearch()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  if (activeTab.value === 'pending') {
    loadPendingList()
  } else {
    loadCompletedList()
  }
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  if (activeTab.value === 'pending') {
    loadPendingList()
  } else {
    loadCompletedList()
  }
}

const handleSelectionChange = (selection: PendingApprovalListItem[]) => {
  selectedRows.value = selection
}

const handleApproval = (row: PendingApprovalListItem) => {
  currentApprovalNo.value = row.approvalNo
  currentApprovalType.value = row.approvalType

  if (row.approvalType === 'claim_approval') {
    showClaimModal.value = true
  } else {
    showCancelModal.value = true
  }
}

const handleViewDetail = (row: PendingApprovalListItem | CompletedApprovalListItem) => {
  currentApprovalNo.value = row.approvalNo
  currentApprovalType.value = row.approvalType
  showDetailModal.value = true
}

const handleApprovalSuccess = () => {
  ElMessage.success(t('workOrderApproval.approvalSuccess'))
  loadPendingList()
}

const handleExport = async () => {
  try {
    exportLoading.value = true
    const params: WorkOrderApprovalListParams = {
      ...filterForm
    }

    if (submitTimeRange.value) {
      params.submitTimeStart = submitTimeRange.value[0]
      params.submitTimeEnd = submitTimeRange.value[1]
    }

    const blob = await exportApprovalData(params)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `工单审批数据_${new Date().toISOString().slice(0, 10)}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success(t('common.exportSuccess'))
  } catch (error) {
    ElMessage.error(t('common.exportFailed'))
  } finally {
    exportLoading.value = false
  }
}

// 辅助方法
const getApprovalTypeText = (type: WorkOrderApprovalType) => {
  const map = {
    claim_approval: t('workOrderApproval.claimApproval'),
    cancel_approval: t('workOrderApproval.cancelApproval')
  }
  return map[type] || type
}

const getApprovalTypeTagType = (type: WorkOrderApprovalType) => {
  const map = {
    claim_approval: 'primary',
    cancel_approval: 'warning'
  }
  return map[type] || 'info'
}

const getTimeoutStatusText = (status: string) => {
  const map = {
    normal: t('workOrderApproval.normal'),
    about_to_timeout: t('workOrderApproval.aboutToTimeout'),
    timeout: t('workOrderApproval.timeout')
  }
  return map[status] || status
}

const getTimeoutStatusTagType = (status: string) => {
  const map = {
    normal: 'success',
    about_to_timeout: 'warning',
    timeout: 'danger'
  }
  return map[status] || 'info'
}

const getApprovalLevelText = (level: string) => {
  const map = {
    first_level: t('workOrderApproval.firstLevel'),
    second_level: t('workOrderApproval.secondLevel')
  }
  return map[level] || level
}

const getApprovalResultText = (result: string) => {
  const map = {
    approved: t('workOrderApproval.approved'),
    rejected: t('workOrderApproval.rejected')
  }
  return map[result] || result
}

const getApprovalResultTagType = (result: string) => {
  const map = {
    approved: 'success',
    rejected: 'danger'
  }
  return map[result] || 'info'
}

// 生命周期
onMounted(() => {
  loadPendingList()
})

// 监听Tab变化
watch(activeTab, (newTab) => {
  if (newTab === 'pending') {
    loadPendingList()
  } else {
    loadCompletedList()
  }
})
</script>

<style scoped lang="scss">
.work-order-approval {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-actions {
      display: flex;
      gap: 12px;
    }
  }

  .approval-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }

    :deep(.el-tabs__item) {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .filter-section {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;

    .filter-form {
      :deep(.el-form-item) {
        margin-bottom: 16px;
        margin-right: 20px;
      }

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #606266;
      }
    }
  }

  .table-section {
    .remaining-time {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 20px 0;
  }

  .completed-approval-content {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    font-size: 14px;
  }
}
</style>
