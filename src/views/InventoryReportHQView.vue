<template>
  <div class="inventory-report-hq-container">
    <h1>{{ t('title') }}</h1>
    <p>{{ t('description') }}</p>

    <el-card class="filter-card">
      <el-form :inline="true" :model="form" class="filter-form">
        <el-form-item :label="t('partName')">
          <el-input v-model="form.partName" :placeholder="t('partNamePlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="t('partNumber')">
          <el-input v-model="form.partNumber" :placeholder="t('partNumberPlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="t('supplierName')">
          <el-input v-model="form.supplierName" :placeholder="t('supplierNamePlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="t('storeName')">
          <el-input v-model="form.storeName" :placeholder="t('storeNamePlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="t('inventoryStatus')">
          <el-select v-model="form.inventoryStatus" :placeholder="t('inventoryStatusPlaceholder')" :loading="dictionaryLoading">
            <el-option
              v-for="option in inventoryStatusOptions"
              :key="option.code"
              :value="option.code"
              :label="option.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{ tc('query') }}</el-button>
          <el-button @click="onReset">{{ tc('reset') }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" :scroll-x="true">
        <el-table-column :label="t('serialNumber')" type="index" min-width="50" align="left"></el-table-column>
        <el-table-column :label="t('storeName')" prop="storeName" min-width="120" align="left"></el-table-column>
        <el-table-column :label="t('partName')" prop="partName" min-width="120" align="left"></el-table-column>
        <el-table-column :label="t('partNumber')" prop="partNumber" min-width="120" align="left"></el-table-column>
        <el-table-column :label="t('supplierName')" prop="supplierName" min-width="120" align="left"></el-table-column>
        <el-table-column :label="t('totalInventory')" prop="totalInventory" min-width="100" align="right"></el-table-column>
        <el-table-column :label="t('availableInventory')" prop="availableInventory" min-width="100" align="right"></el-table-column>
        <el-table-column :label="t('lockedInventory')" prop="lockedInventory" min-width="100" align="right"></el-table-column>
        <el-table-column :label="t('defectiveProducts')" prop="defectiveProducts" min-width="100" align="right"></el-table-column>
        <el-table-column :label="t('pendingReceipt')" prop="pendingReceipt" min-width="100" align="right"></el-table-column>
        <el-table-column :label="t('safetyStock')" prop="safetyStock" min-width="100" align="right"></el-table-column>
        <el-table-column :label="t('inventoryStatus')" prop="inventoryStatus" min-width="100" align="left">
           <template #default="{ row }">
            <el-tag :type="row.inventoryStatus === 'normal' ? 'success' : 'danger'">
              {{ getNameByCode(DICTIONARY_TYPES.INVENTORY_STATUS, row.inventoryStatus) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { useDictionary } from '@/composables/useDictionary';
import { DICTIONARY_TYPES } from '@/constants/dictionary';

const { t, tc } = useModuleI18n('inventoryReportHQ');

// 使用字典数据
const {
  options: inventoryStatusOptions,
  getNameByCode,
  loading: dictionaryLoading
} = useDictionary(DICTIONARY_TYPES.INVENTORY_STATUS);

const form = reactive({
  partName: '',
  partNumber: '',
  supplierName: '',
  storeName: '',
  inventoryStatus: '',
});

const tableData = reactive([
  // Mock data for demonstration
  {
    serialNumber: 1,
    storeName: '门店A',
    partName: '刹车片',
    partNumber: 'BP001',
    supplierName: '供应商A',
    totalInventory: 100,
    availableInventory: 80,
    lockedInventory: 10,
    defectiveProducts: 5,
    pendingReceipt: 5,
    safetyStock: 20,
    inventoryStatus: 'normal',
  },
  {
    serialNumber: 2,
    storeName: '门店B',
    partName: '机油滤清器',
    partNumber: 'OF002',
    supplierName: '供应商B',
    totalInventory: 50,
    availableInventory: 30,
    lockedInventory: 5,
    defectiveProducts: 2,
    pendingReceipt: 13,
    safetyStock: 10,
    inventoryStatus: 'belowSafety',
  },
  {
    serialNumber: 3,
    storeName: '门店A',
    partName: '火花塞',
    partNumber: 'SP003',
    supplierName: '供应商C',
    totalInventory: 200,
    availableInventory: 150,
    lockedInventory: 20,
    defectiveProducts: 10,
    pendingReceipt: 20,
    safetyStock: 50,
    inventoryStatus: 'normal',
  },
]);

const onSubmit = () => {
  console.log('Query form submitted:', form);
  // In a real application, you would make an API call here to fetch data based on form.
  // For now, we'll just log.
};

const onReset = () => {
  form.partName = '';
  form.partNumber = '';
  form.supplierName = '';
  form.storeName = '';
  form.inventoryStatus = '';
  // In a real application, you might re-fetch initial data or clear current table data.
  console.log('Form reset.');
};

onMounted(() => {
  // Potentially fetch initial data here
});

</script>

<style scoped>
.inventory-report-hq-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 0;
}

.table-card {
  margin-top: 20px;
}
</style>
