#!/usr/bin/env node

/**
 * 用于将现有的大型国际化文件拆分为模块化文件的脚本
 */
const fs = require('fs')
const path = require('path')

// 读取现有的国际化文件
const zhPath = path.join(__dirname, '../src/locales/zh.json')
const enPath = path.join(__dirname, '../src/locales/en.json')

// 检查文件是否存在
if (!fs.existsSync(zhPath)) {
  console.error('zh.json file not found:', zhPath)
  process.exit(1)
}

if (!fs.existsSync(enPath)) {
  console.error('en.json file not found:', enPath)
  process.exit(1)
}

const zhData = JSON.parse(fs.readFileSync(zhPath, 'utf8'))
const enData = JSON.parse(fs.readFileSync(enPath, 'utf8'))

// 创建模块目录
const modulesDir = path.join(__dirname, '../src/locales/modules')

// 确保模块目录存在
if (!fs.existsSync(modulesDir)) {
  fs.mkdirSync(modulesDir, { recursive: true })
  console.log('Created modules directory:', modulesDir)
}

// 统计信息
let totalModules = 0
let totalKeysZh = 0
let totalKeysEn = 0

// 为每个模块创建文件
Object.keys(zhData).forEach(moduleName => {
  const moduleDir = path.join(modulesDir, moduleName)

  // 创建模块目录
  if (!fs.existsSync(moduleDir)) {
    fs.mkdirSync(moduleDir, { recursive: true })
  }

  const zhModuleData = zhData[moduleName]
  const enModuleData = enData[moduleName] || {}

  // 统计键值对数量
  const zhKeys = Object.keys(zhModuleData || {}).length
  const enKeys = Object.keys(enModuleData).length

  totalKeysZh += zhKeys
  totalKeysEn += enKeys
  totalModules++

  // 写入中文文件
  const zhModulePath = path.join(moduleDir, 'zh.json')
  fs.writeFileSync(
    zhModulePath,
    JSON.stringify(zhModuleData, null, 2),
    'utf8'
  )

  // 写入英文文件
  const enModulePath = path.join(moduleDir, 'en.json')
  fs.writeFileSync(
    enModulePath,
    JSON.stringify(enModuleData, null, 2),
    'utf8'
  )

  console.log(`✓ Created module: ${moduleName} (zh: ${zhKeys} keys, en: ${enKeys} keys)`)
})

// 备份原始文件
const backupDir = path.join(__dirname, '../src/locales/backup')
if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true })
}

const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
fs.copyFileSync(zhPath, path.join(backupDir, `zh-${timestamp}.json`))
fs.copyFileSync(enPath, path.join(backupDir, `en-${timestamp}.json`))

console.log('\n📊 统计信息:')
console.log(`- 总模块数: ${totalModules}`)
console.log(`- 中文键值对: ${totalKeysZh}`)
console.log(`- 英文键值对: ${totalKeysEn}`)
console.log(`\n💾 原始文件已备份到: ${backupDir}`)
console.log('\n🎉 国际化文件拆分完成!')
console.log('\n📝 接下来你需要:')
console.log('1. 运行 npm run build 检查是否有模块缺失')
console.log('2. 检查各模块文件是否正确生成')
console.log('3. 更新项目中的国际化使用方式')

// 创建模块索引文件
const indexContent = `// 自动生成的模块索引文件
// 请勿手动修改此文件

export const moduleNames = [
  ${Object.keys(zhData).map(name => `'${name}'`).join(',\n  ')}
] as const

export type ModuleName = typeof moduleNames[number]
`

fs.writeFileSync(path.join(modulesDir, 'index.ts'), indexContent, 'utf8')
console.log('✓ Created module index file')

// 生成模块使用说明
const readmeContent = `# 国际化模块说明

本目录包含了项目的模块化国际化文件。

## 目录结构

\`\`\`
modules/
├── common/           # 通用模块 (按钮、操作等)
├── sales/           # 销售模块
├── order/           # 订单模块
├── parts/           # 零件模块
└── ...              # 其他业务模块
\`\`\`

## 使用方式

### 在组件中使用

\`\`\`vue
<script setup>
import { useModuleI18n } from '@/composables/useModuleI18n'

// 使用特定模块的国际化
const { t, tc } = useModuleI18n('sales')

// t() 用于访问当前模块的翻译
const title = t('vehicleList') // 相当于 $t('sales.vehicleList')

// tc() 用于访问通用模块的翻译
const confirmText = tc('confirm') // 相当于 $t('common.confirm')
</script>
\`\`\`

### 添加新模块

1. 在 \`modules/\` 目录下创建新的模块文件夹
2. 添加 \`zh.json\` 和 \`en.json\` 文件
3. 在 \`loader.ts\` 中注册新模块

## 注意事项

- 每个模块的文件结构应保持一致
- 新增模块后需要在 loader.ts 中注册
- 避免在不同模块中使用相同的键名
`

fs.writeFileSync(path.join(modulesDir, 'README.md'), readmeContent, 'utf8')
console.log('✓ Created README file')
