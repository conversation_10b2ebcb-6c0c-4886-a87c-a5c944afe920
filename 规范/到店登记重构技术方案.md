# 到店登记页面重构技术方案

本方案旨在根据《页面目录结构规范》和《页面重构技术规范》对 `CheckinListView.vue` 进行重构，提升代码的模块化、可维护性和可读性。

## 1. 重构目标

- **遵循目录规范**：将页面和相关文件迁移到 `src/views/afterSales/checkin/` 目录下。
- **模块化拆分**：将类型定义、API 请求、Mock 数据和子组件进行分离。
- **提升代码质量**：移除内联类型和数据，使用统一的 API 和类型定义。
- **增强可维护性**：通过组件化和模块化，降低单个文件的复杂性。

## 2. 重构分析

`CheckinListView.vue` 当前存在以下问题：

- **文件位置不规范**：直接位于 `src/views/`，未按 `afterSales` 模块进行组织。
- **API 模块分散**：使用 `@/api/modules/checkin` 而非模块化的 `afterSales/checkin`。
- **缺少独立类型定义**：类型定义在 `@/types/module` 中，应提取到独立的 `.d.ts` 文件。
- **组件复杂度高**：单文件 775 行，包含多个弹窗和复杂逻辑，应拆分为子组件。
- **国际化使用不规范**：使用 `afterSales` 模块但应该更具体到 `checkin` 功能。

## 3. 重构步骤

### 步骤 1：创建目录和文件结构

根据规范，创建以下目录和文件：

```
src/
├── views/afterSales/checkin/
│   ├── CheckinView.vue                 # 主页面（包含集成的搜索表单）
│   └── components/
│       ├── CheckinTable.vue            # 到店登记表格组件
│       ├── CheckinFormDialog.vue       # 新增/编辑弹窗组件
│       ├── CheckinDetailDialog.vue     # 详情查看弹窗组件
│       ├── CancelDialog.vue            # 取消登记弹窗组件
│       └── CreateInspectionDialog.vue  # 创建环检单弹窗组件
├── api/modules/afterSales/
│   └── checkin.ts                      # 到店登记 API 模块
├── types/afterSales/
│   └── checkin.d.ts                    # 到店登记类型定义
├── mock/data/afterSales/
│   └── checkin.ts                      # 到店登记 Mock 数据
└── locales/modules/afterSales/
    ├── zh.json                         # 中文语言包（已存在，需更新）
    └── en.json                         # 英文语言包（已存在，需更新）
```

### 步骤 2：迁移和重构类型定义

创建 `src/types/afterSales/checkin.d.ts`，定义到店登记相关的类型：

```typescript
// src/types/afterSales/checkin.d.ts

export interface CheckinListItem {
  checkinId: string;
  licensePlate: string;
  vin: string;
  vehicleModel: string;
  vehicleConfiguration: string;
  color: string;
  mileage?: number;
  vehicleAge?: number;
  repairPersonName: string;
  repairPersonPhone: string;
  serviceAdvisor: string;
  relatedRepairOrderId?: string | null;
  serviceType: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
  isDeleted: boolean;
}

export interface CheckinListParams {
  checkinId?: string;
  licensePlate?: string;
  repairPersonName?: string;
  repairPersonPhone?: string;
  createdAtStart?: string;
  createdAtEnd?: string;
  page?: number;
  pageSize?: number;
}

export interface CheckinPageResponse {
  list: CheckinListItem[];
  total: number;
}

export interface VehicleInfo {
  licensePlate: string;
  vin?: string;
  vehicleModel?: string;
  vehicleConfiguration?: string;
  color?: string;
  mileage?: number;
  vehicleAge?: number;
}

export interface CheckinFormData extends Omit<CheckinListItem, 'checkinId' | 'createdAt' | 'updatedAt'> {
  checkinId?: string;
}
```

### 步骤 3：重构 Mock 数据

将 Mock 数据逻辑迁移到 `src/mock/data/afterSales/checkin.ts`：

```typescript
// src/mock/data/afterSales/checkin.ts

import type { 
  CheckinListParams, 
  CheckinPageResponse, 
  CheckinListItem,
  VehicleInfo,
  CheckinFormData 
} from '@/types/afterSales/checkin.d.ts';

// 生成动态 Mock 数据
function generateMockCheckinData(): CheckinListItem[] {
  const data: CheckinListItem[] = [];
  const licensePlates = ['京A12345', '沪B67890', '粤C11111', '浙D22222', '苏E33333', '川F44444', '鲁G55555'];
  const vehicleModels = ['奔驰C级', '宝马3系', '奥迪A4L', '凯迪拉克CT5', '沃尔沃S60'];
  const configurations = ['豪华版', '运动版', '舒适版', '尊贵版', '标准版'];
  const colors = ['珍珠白', '曜岩黑', '极地银', '天空蓝', '玛瑙红'];
  const serviceAdvisors = ['张三', '李四', '王五', '赵六', '钱七'];
  const repairPersonNames = ['陈先生', '刘女士', '杨先生', '周女士', '吴先生'];

  for (let i = 1; i <= 28; i++) {
    const createdDate = new Date();
    createdDate.setDate(createdDate.getDate() - Math.floor(Math.random() * 30));
    const updatedDate = new Date(createdDate.getTime() + Math.random() * 86400000);

    data.push({
      checkinId: `CK${String(i).padStart(6, '0')}`,
      licensePlate: licensePlates[Math.floor(Math.random() * licensePlates.length)],
      vin: `WBA${Math.random().toString(36).substr(2, 14).toUpperCase()}`,
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleConfiguration: configurations[Math.floor(Math.random() * configurations.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      mileage: Math.floor(Math.random() * 100000) + 5000,
      vehicleAge: Math.floor(Math.random() * 60) + 6,
      repairPersonName: repairPersonNames[Math.floor(Math.random() * repairPersonNames.length)],
      repairPersonPhone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      serviceAdvisor: serviceAdvisors[Math.floor(Math.random() * serviceAdvisors.length)],
      relatedRepairOrderId: Math.random() > 0.6 ? `RO${String(i).padStart(6, '0')}` : null,
      serviceType: Math.random() > 0.5 ? '维修' : '保养',
      createdAt: createdDate.toISOString().slice(0, 16).replace('T', ' '),
      updatedAt: updatedDate.toISOString().slice(0, 16).replace('T', ' '),
      notes: Math.random() > 0.7 ? '客户反映发动机异响' : '',
      isDeleted: false
    });
  }
  
  return data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
}

const mockCheckinData = generateMockCheckinData();

export const getCheckinList = (params: CheckinListParams): Promise<CheckinPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockCheckinData];

      // 应用筛选条件
      if (params.checkinId) {
        filteredData = filteredData.filter(item => 
          item.checkinId.toLowerCase().includes(params.checkinId!.toLowerCase())
        );
      }

      if (params.licensePlate) {
        filteredData = filteredData.filter(item => 
          item.licensePlate.toLowerCase().includes(params.licensePlate!.toLowerCase())
        );
      }

      if (params.repairPersonName) {
        filteredData = filteredData.filter(item => 
          item.repairPersonName.includes(params.repairPersonName!)
        );
      }

      if (params.repairPersonPhone) {
        filteredData = filteredData.filter(item => 
          item.repairPersonPhone.includes(params.repairPersonPhone!)
        );
      }

      if (params.createdAtStart && params.createdAtEnd) {
        filteredData = filteredData.filter(item => {
          const itemDate = item.createdAt.split(' ')[0];
          return itemDate >= params.createdAtStart! && itemDate <= params.createdAtEnd!;
        });
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};

export const queryVehicleInfo = (licensePlate: string): Promise<VehicleInfo | null> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟根据车牌号查询车辆信息
      const existingVehicle = mockCheckinData.find(item => item.licensePlate === licensePlate);
      
      if (existingVehicle) {
        resolve({
          licensePlate: existingVehicle.licensePlate,
          vin: existingVehicle.vin,
          vehicleModel: existingVehicle.vehicleModel,
          vehicleConfiguration: existingVehicle.vehicleConfiguration,
          color: existingVehicle.color,
          mileage: existingVehicle.mileage,
          vehicleAge: existingVehicle.vehicleAge
        });
      } else {
        resolve(null);
      }
    }, 500);
  });
};

export const addCheckinRecord = (data: CheckinFormData): Promise<{ success: boolean; checkinId: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newId = `CK${String(mockCheckinData.length + 1).padStart(6, '0')}`;
      const now = new Date().toISOString().slice(0, 16).replace('T', ' ');
      
      const newRecord: CheckinListItem = {
        ...data,
        checkinId: newId,
        createdAt: now,
        updatedAt: now,
        isDeleted: false
      } as CheckinListItem;
      
      mockCheckinData.unshift(newRecord);
      
      resolve({
        success: true,
        checkinId: newId
      });
    }, 800);
  });
};

export const updateCheckinRecord = (checkinId: string, data: Partial<CheckinFormData>): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockCheckinData.findIndex(item => item.checkinId === checkinId);
      if (index !== -1) {
        mockCheckinData[index] = {
          ...mockCheckinData[index],
          ...data,
          updatedAt: new Date().toISOString().slice(0, 16).replace('T', ' ')
        };
      }
      
      resolve({ success: true });
    }, 800);
  });
};

export const deleteCheckinRecord = (checkinId: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockCheckinData.findIndex(item => item.checkinId === checkinId);
      if (index !== -1) {
        mockCheckinData.splice(index, 1);
      }
      
      resolve({ success: true });
    }, 500);
  });
};

export const createRelatedRepairOrder = (checkinId: string): Promise<{ success: boolean; repairOrderId: string }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockCheckinData.findIndex(item => item.checkinId === checkinId);
      if (index !== -1) {
        const repairOrderId = `RO${checkinId.slice(2)}`;
        mockCheckinData[index].relatedRepairOrderId = repairOrderId;
        mockCheckinData[index].updatedAt = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      
      resolve({
        success: true,
        repairOrderId: `RO${checkinId.slice(2)}`
      });
    }, 1000);
  });
};
```

### 步骤 4：创建 API 模块

创建 `src/api/modules/afterSales/checkin.ts`，统一管理 API 请求：

```typescript
// src/api/modules/afterSales/checkin.ts

import request from '@/api';
import type {
  CheckinListParams,
  CheckinPageResponse,
  VehicleInfo,
  CheckinFormData
} from '@/types/afterSales/checkin.d.ts';
import {
  getCheckinList as getMockCheckinList,
  queryVehicleInfo as queryMockVehicleInfo,
  addCheckinRecord as addMockCheckinRecord,
  updateCheckinRecord as updateMockCheckinRecord,
  deleteCheckinRecord as deleteMockCheckinRecord,
  createRelatedRepairOrder as createMockRelatedRepairOrder
} from '@/mock/data/afterSales/checkin';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getCheckinList = (params: CheckinListParams): Promise<CheckinPageResponse> => {
  if (USE_MOCK_API) {
    return getMockCheckinList(params);
  }
  return request.get<any, CheckinPageResponse>('/after-sales/checkin/list', { params });
};

export const queryVehicleInfo = (licensePlate: string): Promise<VehicleInfo | null> => {
  if (USE_MOCK_API) {
    return queryMockVehicleInfo(licensePlate);
  }
  return request.get<any, VehicleInfo | null>(`/after-sales/checkin/vehicle-info/${licensePlate}`);
};

export const addCheckinRecord = (data: CheckinFormData): Promise<{ success: boolean; checkinId: string }> => {
  if (USE_MOCK_API) {
    return addMockCheckinRecord(data);
  }
  return request.post<any, { success: boolean; checkinId: string }>('/after-sales/checkin', data);
};

export const updateCheckinRecord = (checkinId: string, data: Partial<CheckinFormData>): Promise<{ success: boolean }> => {
  if (USE_MOCK_API) {
    return updateMockCheckinRecord(checkinId, data);
  }
  return request.put<any, { success: boolean }>(`/after-sales/checkin/${checkinId}`, data);
};

export const deleteCheckinRecord = (checkinId: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API) {
    return deleteMockCheckinRecord(checkinId);
  }
  return request.delete<any, { success: boolean }>(`/after-sales/checkin/${checkinId}`);
};

export const createRelatedRepairOrder = (checkinId: string): Promise<{ success: boolean; repairOrderId: string }> => {
  if (USE_MOCK_API) {
    return createMockRelatedRepairOrder(checkinId);
  }
  return request.post<any, { success: boolean; repairOrderId: string }>(`/after-sales/checkin/${checkinId}/repair-order`);
};
```

### 步骤 5：更新国际化文件

在现有的 `src/locales/modules/afterSales/zh.json` 和 `en.json` 中，将到店登记相关的翻译组织在 `checkin` 键下：

```json
// src/locales/modules/afterSales/zh.json (新增部分)
{
  "checkin": {
    "checkinList": "到店登记",
    "checkinId": "登记单号",
    "checkinIdPlaceholder": "请输入登记单号",
    "licensePlate": "车牌号",
    "licensePlatePlaceholder": "请输入车牌号",
    "vin": "车架号",
    "vehicleModel": "车型",
    "vehicleConfiguration": "配置",
    "color": "颜色",
    "mileage": "里程",
    "mileageUnit": "公里",
    "mileagePlaceholder": "请输入里程数",
    "vehicleAge": "车龄",
    "repairPersonName": "送修人姓名",
    "repairPersonNamePlaceholder": "请输入送修人姓名",
    "repairPersonPhone": "送修人电话",
    "repairPersonPhonePlaceholder": "请输入送修人电话",
    "serviceAdvisor": "服务顾问",
    "relatedRepairOrderId": "关联维修单号",
    "serviceType": "服务类型",
    "createdAt": "创建时间",
    "updatedAt": "更新时间",
    "notes": "备注",
    "notesPlaceholder": "请输入备注信息",
    "createRecord": "新增登记",
    "addCheckinRecord": "新增到店登记",
    "editCheckinRecord": "编辑到店登记",
    "viewDetails": "查看详情",
    "export": "导出",
    "vehicleInfo": "车辆信息",
    "customerInfo": "客户信息",
    "vehicleInfoAutoFill": "输入车牌号自动填充",
    "vehicleInfoNotFound": "未找到车辆信息",
    "createRepairOrder": "创建维修单",
    "repairOrderAlreadyExists": "该登记单已关联维修单",
    "confirmCreateRepairOrder": "确定为登记单 {checkinId} 创建维修单吗？",
    "repairOrderCreatedSuccess": "维修单创建成功"
  }
}
```

### 步骤 6：创建子组件

#### `CheckinSearchForm.vue` - 搜索表单组件
```vue
<!-- src/views/afterSales/checkin/components/CheckinSearchForm.vue -->
<script setup lang="ts">
import { ElCard, ElForm, ElFormItem, ElInput, ElDatePicker, ElButton, ElRow, ElCol } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinListParams } from '@/types/afterSales/checkin.d.ts';

interface Props {
  searchParams: CheckinListParams;
  dateRange: [string, string] | null;
}

interface Emits {
  (e: 'update:searchParams', value: CheckinListParams): void;
  (e: 'update:dateRange', value: [string, string] | null): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.checkin');

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  emit('reset');
};

const updateSearchParams = (field: keyof CheckinListParams, value: any) => {
  emit('update:searchParams', { ...props.searchParams, [field]: value });
};

const updateDateRange = (value: [string, string] | null) => {
  emit('update:dateRange', value);
};
</script>

<template>
  <el-card class="mb-20 search-card">
    <el-form :model="searchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('checkinId')">
            <el-input
              :model-value="searchParams.checkinId"
              @update:model-value="(val) => updateSearchParams('checkinId', val)"
              :placeholder="t('checkinIdPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('licensePlate')">
            <el-input
              :model-value="searchParams.licensePlate"
              @update:model-value="(val) => updateSearchParams('licensePlate', val)"
              :placeholder="t('licensePlatePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('repairPersonName')">
            <el-input
              :model-value="searchParams.repairPersonName"
              @update:model-value="(val) => updateSearchParams('repairPersonName', val)"
              :placeholder="t('repairPersonNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('repairPersonPhone')">
            <el-input
              :model-value="searchParams.repairPersonPhone"
              @update:model-value="(val) => updateSearchParams('repairPersonPhone', val)"
              :placeholder="t('repairPersonPhonePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('createdAt')">
            <el-date-picker
              :model-value="dateRange"
              @update:model-value="updateDateRange"
              type="daterange"
              range-separator="-"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="18"></el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" class="buttons-col">
          <el-form-item class="search-buttons">
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="handleReset">{{ tc('reset') }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<style scoped lang="scss">
.search-form {
  .el-form-item {
    margin-right: 20px;
    margin-bottom: 15px;
    &:last-child {
      margin-right: 0;
    }
  }
  .search-buttons {
    margin-bottom: 15px;
    .el-button {
      margin-left: 10px;
    }
  }
}

.buttons-col {
  text-align: right;
  :deep(.el-form-item__content) {
    justify-content: flex-end;
  }
}
</style>
```

#### `CheckinTable.vue` - 到店登记表格组件
```vue
<!-- src/views/afterSales/checkin/components/CheckinTable.vue -->
<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElButton, ElPagination } from 'element-plus';
import { View, Edit, Delete, Plus } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { CheckinListItem } from '@/types/afterSales/checkin.d.ts';

interface Props {
  checkinList: CheckinListItem[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

interface Emits {
  (e: 'view-details', row: CheckinListItem): void;
  (e: 'edit-record', row: CheckinListItem): void;
  (e: 'delete-record', row: CheckinListItem): void;
  (e: 'create-repair-order', row: CheckinListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.checkin');

const handleViewDetails = (row: CheckinListItem) => {
  emit('view-details', row);
};

const handleEditRecord = (row: CheckinListItem) => {
  emit('edit-record', row);
};

const handleDeleteRecord = (row: CheckinListItem) => {
  emit('delete-record', row);
};

const handleCreateRepairOrder = (row: CheckinListItem) => {
  emit('create-repair-order', row);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePageSizeChange = (size: number) => {
  emit('page-size-change', size);
};
</script>

<template>
  <el-card class="table-card">
    <el-table
      :data="checkinList"
      v-loading="loading"
      style="width: 100%"
      border
      :empty-text="tc('noData')"
    >
      <el-table-column type="index" :label="t('id')" width="60" />
      <el-table-column prop="checkinId" :label="t('checkinId')" min-width="120" />
      <el-table-column prop="licensePlate" :label="t('licensePlate')" min-width="120" />
      <el-table-column prop="vin" :label="t('vin')" min-width="140" />
      <el-table-column prop="vehicleModel" :label="t('vehicleModel')" min-width="120" />
      <el-table-column prop="vehicleConfiguration" :label="t('vehicleConfiguration')" min-width="150" />
      <el-table-column prop="color" :label="t('color')" min-width="80" />
      <el-table-column prop="mileage" :label="t('mileage')" min-width="100">
        <template #default="scope">
          {{ scope.row.mileage ? `${scope.row.mileage} ${t('mileageUnit')}` : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="repairPersonName" :label="t('repairPersonName')" min-width="120" />
      <el-table-column prop="repairPersonPhone" :label="t('repairPersonPhone')" min-width="120" />
      <el-table-column prop="serviceAdvisor" :label="t('serviceAdvisor')" min-width="100" />
      <el-table-column prop="relatedRepairOrderId" :label="t('relatedRepairOrderId')" min-width="140">
        <template #default="scope">
          {{ scope.row.relatedRepairOrderId || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="serviceType" :label="t('serviceType')" min-width="100" />
      <el-table-column prop="createdAt" :label="t('createdAt')" min-width="120" />
      <el-table-column prop="updatedAt" :label="t('updatedAt')" min-width="120" />
      <el-table-column :label="tc('operations')" width="280" fixed="right">
        <template #default="scope">
          <el-button type="primary" :icon="View" link @click="handleViewDetails(scope.row)">
            {{ t('viewDetails') }}
          </el-button>
          <el-button type="primary" :icon="Edit" link @click="handleEditRecord(scope.row)">
            {{ tc('edit') }}
          </el-button>
          <el-button
            type="danger"
            :icon="Delete"
            link
            @click="handleDeleteRecord(scope.row)"
            v-if="!scope.row.relatedRepairOrderId"
          >
            {{ tc('delete') }}
          </el-button>
          <el-button
            type="success"
            :icon="Plus"
            link
            @click="handleCreateRepairOrder(scope.row)"
            v-if="!scope.row.relatedRepairOrderId"
          >
            {{ t('createRepairOrder') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
```

### 步骤 7：重构主页面 `CheckinView.vue`

- **移动和重命名**：将 `CheckinListView.vue` 移动到 `src/views/afterSales/checkin/` 并重命名为 `CheckinView.vue`。
- **移除内联逻辑**：将搜索表单和表格逻辑移动到子组件。
- **更新数据获取**：使用新的 API 模块获取数据。
- **更新类型引用**：从 `@/types/afterSales/checkin.d.ts` 导入类型。
- **更新国际化引用**：使用 `useModuleI18n('afterSales.checkin')`。
- **引入子组件**：在模板中引入并使用子组件。

```vue
<!-- src/views/afterSales/checkin/CheckinView.vue -->
<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox, ElCard, ElButton, ElRow, ElCol } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getCheckinList,
  addCheckinRecord,
  updateCheckinRecord,
  deleteCheckinRecord,
  createRelatedRepairOrder
} from '@/api/modules/afterSales/checkin';
import type {
  CheckinListItem,
  CheckinListParams,
  CheckinFormData
} from '@/types/afterSales/checkin.d.ts';

// 导入子组件
import CheckinSearchForm from './components/CheckinSearchForm.vue';
import CheckinTable from './components/CheckinTable.vue';
import CheckinFormDialog from './components/CheckinFormDialog.vue';
import CheckinDetailDialog from './components/CheckinDetailDialog.vue';

const { t, tc } = useModuleI18n('afterSales.checkin');

// 搜索相关
const searchParams = reactive<CheckinListParams>({
  checkinId: '',
  licensePlate: '',
  repairPersonName: '',
  repairPersonPhone: '',
  createdAtStart: '',
  createdAtEnd: '',
});
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchParams.createdAtStart = newVal[0];
    searchParams.createdAtEnd = newVal[1];
  } else {
    searchParams.createdAtStart = '';
    searchParams.createdAtEnd = '';
  }
});

const checkinList = ref<CheckinListItem[]>([]);
const loading = ref(false);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 弹窗相关
const formDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const isEdit = ref(false);
const currentRecord = ref<CheckinListItem | null>(null);

// 获取到店登记列表
const fetchCheckinList = async () => {
  loading.value = true;
  try {
    const response = await getCheckinList({
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    });
    checkinList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch checkin list:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchCheckinList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    checkinId: '',
    licensePlate: '',
    repairPersonName: '',
    repairPersonPhone: '',
    createdAtStart: '',
    createdAtEnd: '',
  });
  dateRange.value = null;
  pagination.page = 1;
  fetchCheckinList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchCheckinList();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchCheckinList();
};

// 新增登记
const handleAddCheckinRecord = () => {
  isEdit.value = false;
  currentRecord.value = null;
  formDialogVisible.value = true;
};

// 编辑登记
const handleEditRecord = (row: CheckinListItem) => {
  isEdit.value = true;
  currentRecord.value = row;
  formDialogVisible.value = true;
};

// 删除登记
const handleDeleteRecord = async (row: CheckinListItem) => {
  ElMessageBox.confirm(
    tc('confirmDelete', { item: row.checkinId }),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await deleteCheckinRecord(row.checkinId);
        ElMessage.success(tc('operationSuccessful'));
        fetchCheckinList();
      } catch (error) {
        console.error('Failed to delete checkin record:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 创建维修单
const handleCreateRepairOrder = async (row: CheckinListItem) => {
  if (row.relatedRepairOrderId) {
    ElMessage.warning(t('repairOrderAlreadyExists'));
    return;
  }
  ElMessageBox.confirm(
    t('confirmCreateRepairOrder', { checkinId: row.checkinId }),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'info',
    }
  )
    .then(async () => {
      try {
        await createRelatedRepairOrder(row.checkinId);
        ElMessage.success(t('repairOrderCreatedSuccess'));
        fetchCheckinList();
      } catch (error) {
        console.error('Failed to create repair order:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 查看详情
const handleViewDetails = (row: CheckinListItem) => {
  currentRecord.value = row;
  detailDialogVisible.value = true;
};

// 表单提交
const handleFormSubmit = async (formData: CheckinFormData) => {
  try {
    if (isEdit.value && currentRecord.value) {
      await updateCheckinRecord(currentRecord.value.checkinId, formData);
    } else {
      await addCheckinRecord(formData);
    }
    ElMessage.success(tc('operationSuccessful'));
    formDialogVisible.value = false;
    fetchCheckinList();
  } catch (error) {
    console.error('Failed to save checkin record:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 导出
const handleExport = () => {
  ElMessage.info(tc('exporting'));
  console.log('Exporting data with params:', searchParams);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchCheckinList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('checkinList') }}</h1>

    <!-- 搜索表单 -->
    <CheckinSearchForm
      v-model:search-params="searchParams"
      v-model:date-range="dateRange"
      @search="handleSearch"
      @reset="resetSearch"
    />

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20" justify="end">
        <el-col :span="24" style="text-align: right;">
          <el-button type="primary" :icon="Plus" @click="handleAddCheckinRecord">
            {{ t('createRecord') }}
          </el-button>
          <el-button :icon="Download" @click="handleExport">
            {{ t('export') }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <CheckinTable
      :checkin-list="checkinList"
      :loading="loading"
      :pagination="pagination"
      @view-details="handleViewDetails"
      @edit-record="handleEditRecord"
      @delete-record="handleDeleteRecord"
      @create-repair-order="handleCreateRepairOrder"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 新增/编辑弹窗 -->
    <CheckinFormDialog
      v-model:visible="formDialogVisible"
      :is-edit="isEdit"
      :record-data="currentRecord"
      @submit="handleFormSubmit"
    />

    <!-- 详情弹窗 -->
    <CheckinDetailDialog
      v-model:visible="detailDialogVisible"
      :record-data="currentRecord"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.operation-card {
  margin-bottom: 20px;
}
</style>
```

### 步骤 8：更新路由配置

修改 `src/router/index.ts` 或相关路由模块，更新到店登记页面的路由配置：

```typescript
// src/router/index.ts
{
  path: '/after-sales/checkin',
  name: 'CheckinManagement',
  component: () => import('@/views/afterSales/checkin/CheckinView.vue'),
  meta: {
    title: 'menu.checkinManagement',
    requiresAuth: true,
    icon: 'Document'
  }
}
```

## 4. 预期收益

- **结构清晰**：项目结构符合团队规范，新成员更容易上手。
- **职责明确**：每个文件和组件的职责更加单一，便于理解和修改。
- **易于维护**：修改或扩展功能时，只需关注相关模块，降低了代码耦合带来的风险。
- **提升开发效率**：模块化和组件化使得代码复用更加方便。
- **数据动态化**：Mock 数据支持动态生成，便于测试不同场景。
- **类型安全**：完整的 TypeScript 类型定义，提高代码质量和开发体验。

## 5. 重构检查清单

### 5.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/checkin/CheckinView.vue`
- [ ] API模块创建在 `src/api/modules/afterSales/checkin.ts`
- [ ] Mock数据创建在 `src/mock/data/afterSales/checkin.ts`
- [ ] 类型定义创建在 `src/types/afterSales/checkin.d.ts`
- [ ] 子组件创建在 `src/views/afterSales/checkin/components/`

### 5.2 代码质量验证
- [ ] 移除页面中的内联数据和复杂逻辑
- [ ] 使用统一的API调用替换分散的API模块
- [ ] TypeScript类型安全，无编译错误
- [ ] 国际化完整覆盖，无硬编码文本
- [ ] Mock数据功能完整，支持完整的CRUD操作

### 5.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 新增/编辑功能正常工作
- [ ] 删除功能正常工作
- [ ] 创建维修单功能正常工作
- [ ] 车辆信息查询功能正常工作
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

### 5.4 路由验证
- [ ] 路由路径更新为新的模块化路径
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 6. 实施建议

### 6.1 实施顺序
1. **创建基础结构**：先创建目录和类型定义
2. **重构数据层**：创建Mock数据和API模块
3. **拆分组件**：创建子组件并测试
4. **重构主页面**：更新主页面逻辑
5. **更新配置**：修改路由和国际化
6. **测试验证**：全面测试功能正常性

### 6.2 风险控制
- **渐进式重构**：一次只重构一个模块，避免大范围影响
- **保持功能不变**：重构过程中不改变业务逻辑和用户体验
- **及时测试**：每个步骤完成后及时验证功能正常
- **代码备份**：重构前备份原始代码

---

**本技术方案基于页面目录结构规范和页面重构技术规范制定，为到店登记页面的重构提供详细的实施指导。**
