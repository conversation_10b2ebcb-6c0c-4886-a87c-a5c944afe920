

## 技术开发规范 (供 AI 使用)

这份规范将作为 AI 编写 DMS 前端项目代码的“行为准则”，旨在统一代码风格、强制最佳实践、减少错误、提高代码质量和可维护性。AI 在生成任何代码时，必须严格遵守以下约定。

### 1. Vue.js 编程范式规范

* **1.1 Composition API 优先**
    * **规范：** 所有 Vue 组件的 `<script setup>` 块必须使用 Composition API。避免使用 Options API (`data`, `methods`, `computed` 等选项)，除非有特殊兼容性或遗留代码需求（本项目无此需求）。
    * **最佳实践：**
        * `ref` 用于基本数据类型和对象（当希望整个对象被替换时响应式）。
        * `reactive` 用于对象（当希望对象的内部属性被修改时响应式）。
        * `computed` 用于派生状态，并自动缓存。
        * `watch` 和 `watchEffect` 用于响应式数据变化时的副作用。
        * 将相关逻辑封装为独立的函数，提高代码可读性和可复用性。
        * 复杂逻辑提取到 `composables` 目录（例如 `src/composables/usePagination.ts`, `src/composables/useForm.ts`），并在组件中导入使用。

* **1.2 Props 和 Emits 定义**
    * **规范：** 必须使用 TypeScript 语法 (`defineProps<{...}>`, `defineEmits<{...}>`) 明确定义组件的所有 `props` 和 `emits` 的类型和默认值（如果适用）。
    * **最佳实践：**
        * `props` 必须定义 `type`。
        * `props` 应该定义 `required` 属性（如果该属性是组件正常工作所必需的）。
        * 为 `props` 提供 `default` 值，使组件在没有父组件传递时也能正常工作。
        * `emits` 应该定义其触发的事件名称和参数类型，以便父组件能够获得正确的类型提示。
        * 避免在子组件中直接修改 `props`，应通过 `emit` 通知父组件进行数据更新。

* **1.3 模板语法**
    * **规范：** 使用 `v-for` 时，必须提供 `:key` 属性，且 `key` 必须是唯一的、稳定的值（通常是后端返回的 ID）。
    * **最佳实践：**
        * 尽可能使用短语法 (`:` for `v-bind`, `@` for `v-on`, `#` for `v-slot` )。
        * 条件渲染 (`v-if`, `v-else-if`, `v-else`, `v-show`) 遵循语义化原则，`v-if` 用于频繁切换的场景，`v-show` 用于不频繁切换的场景。
        * 组件属性顺序：`is` -> `v-for` -> `v-if/v-show` -> `v-once` -> `id` -> `ref` -> `key` -> `slot` -> `v-model` -> `props` -> `events`。

### 2. TypeScript 类型安全规范

* **2.1 强制类型定义**
    * **规范：** 所有变量、函数参数、函数返回值、组件 `props`、`emits` 参数、Pinia `state`、`getters`、`actions` 参数和返回值，以及 API 请求和响应数据，都必须明确定义 TypeScript 类型。
    * **最佳实践：**
        * 使用 `interface` 定义复杂对象结构。
        * 使用 `type` 定义联合类型、交叉类型或字面量类型。
        * 函数参数和返回值类型应尽可能具体，避免使用 `any`。
        * 对于来自后端的复杂数据结构，应在 `src/types/api.d.ts` 或 `src/types/module.d.ts` 中定义相应的 `interface`。
        * `ref` 和 `reactive` 声明时应明确泛型参数，例如 `const count = ref<number>(0);` 或 `const user = reactive<User>({...});`。

* **2.2 枚举和常量**
    * **规范：** 对于具有固定、有限选项的业务状态或类型，使用 TypeScript `enum` 或字面量联合类型定义。
    * **最佳实践：** 将枚举或常量定义在 `src/types/` 或对应业务模块的 `api/modules` 下，方便集中管理和引用。

### 3. 数据请求与 API 规范

* **3.1 Axios 统一封装**
    * **规范：** 所有 HTTP 请求必须通过 `src/api/index.ts` 中封装的 Axios 实例发出。不允许直接在组件中裸调 Axios。
    * **最佳实践：**
        * 请求拦截器统一添加 Token、处理请求头。
        * 响应拦截器统一处理业务错误码、HTTP 状态码、全局错误提示 (`ElMessage.error`)、认证过期处理。
        * API 请求函数定义在 `src/api/modules/[business_module].ts` 中，并明确输入和输出类型。

* **3.2 错误处理**
    * **规范：** 在 `api/index.ts` 中统一捕获和处理网络错误、HTTP 错误和后端返回的业务错误。
    * **最佳实践：**
        * 对于可预期的业务错误，通过 `ElMessage.error` 给予用户友好提示。
        * 对于不可恢复的错误（如认证过期），强制跳转登录页。
        * 组件中调用 API 时，只需关注 `try...catch` 中对具体业务逻辑的额外处理（如表单清空、Loading 状态解除），无需重复处理通用错误提示。

* **3.3 数据源规范 (重要补充)**
    * **规范：** 在页面开发和组件测试过程中，**不允许直接在组件内部硬编码模拟数据**（例如 `const tableData = ref([...])`）。所有需要数据的场景，都必须通过**调用 Mock 方法**或**实际的 API 请求**来获取数据。
    * **最佳实践：**
        * **开发环境使用 Mock Server (推荐):** 配置一个独立的 Mock Server (如 Vite 的 `vite.config.ts` 中的 `server.proxy` 配合 `src/mock` 目录下的 mock 数据文件，或使用 `msw` 等库) 来拦截 API 请求并返回模拟数据。前端代码直接调用 `src/api/modules` 中定义的 API 方法。
        * **API 接口的 Mock 实现 (备选):** 如果不使用独立的 Mock Server，可以在 `src/api/modules/[business_module].ts` 中，为每个 API 方法提供一个临时的 Mock 实现。**当后端接口就绪时，直接替换为真实的 Axios 请求即可，无需修改组件代码。**
            ```typescript
            // 示例：src/api/modules/sales.ts
            import request from '@/api';
            import type { VehicleListItem, VehicleListParams } from '@/types/module.d.ts';
            import { mockVehicleList } from '@/mock/data/sales'; // 导入模拟数据

            // 真实的 API 请求
            export const getVehicleList = (params: VehicleListParams): Promise<{ list: VehicleListItem[], total: number }> => {
              // return request.get('/sales/vehicles', { params }); // 真实后端接口
              // 开发阶段使用 Mock 数据
              return new Promise((resolve) => {
                setTimeout(() => {
                  const { page = 1, pageSize = 10, ...filter } = params;
                  let filteredList = mockVehicleList.filter(item => {
                    return (!filter.vin || item.vin.includes(filter.vin)) &&
                           (!filter.model || item.model.includes(filter.model)) &&
                           (!filter.status || item.status === filter.status);
                  });
                  const total = filteredList.length;
                  const list = filteredList.slice((page - 1) * pageSize, page * pageSize);
                  resolve({ list, total });
                }, 500); // 模拟网络延迟
              });
            };

            // ... 其他销售模块 API
            ```
            **AI 在生成组件时，如果需要数据，应自动生成类似 `useSalesStore().getVehicleList()` 或直接调用 `getVehicleList()` 的代码，而不是在组件内创建 `ref` 假数据。**
    * **约束 AI：**
        * **禁止硬编码数据：** 永远不要在组件的 `<script setup>` 或任何 Vue 文件内部创建 `const data = [...]` 这样的模拟数据。
        * **强制 API 调用：** 所有数据展示必须通过调用 `src/api/modules` 中定义的 API 方法或 Pinia `actions` 来获取数据。
        * **引导 Mock 数据生成：** 如果 AI 识别到某个 API 方法还没有 Mock 实现或真实的后端接口，应提示人类开发者创建相应的 Mock 数据文件 (`src/mock/data/[module].ts`) 或在 API 方法中添加临时的 Promise 模拟数据。

### 4. 状态管理 (Pinia) 规范

* **4.1 模块化 Store**
    * **规范：** 严格遵循 `src/stores/modules/` 目录结构，每个业务模块拥有自己的 Store。
    * **最佳实践：**
        * Store 的 `state` 仅包含该模块需要共享的数据。
        * `getters` 仅用于从 `state` 派生数据，且应利用其缓存特性。
        * `actions` 用于封装所有异步操作（如 API 请求）和复杂的状态修改逻辑。直接修改 `state` 仅限在 `actions` 内部或非常简单的场景。

* **4.2 持久化**
    * **规范：** 需要跨页面或刷新后保留的状态（如用户 Token、用户信息、系统主题）应使用 `pinia-plugin-persistedstate` 进行持久化配置。
    * **最佳实践：** 在 `store` 定义的 `persist` 选项中，明确指定 `key` 和 `paths`（只持久化必要的属性），避免存储敏感或不必要的数据。

### 5. 权限管理规范

* **5.1 权限判断统一接口**
    * **规范：** 所有前端权限判断（路由守卫、菜单渲染、按钮显示）必须通过 `useUserStore()` 提供的 `checkPermission()`, `checkAnyPermission()`, `checkRole()`, `checkAnyRole()` 等方法。
    * **最佳实践：** 避免在业务组件中直接访问 `userStore.userInfo.permissions` 数组进行判断，强制通过封装好的方法，以便未来权限逻辑调整。

* **5.2 `v-permission` 指令应用**
    * **规范：** 在 HTML 模板中控制功能（按钮、输入框、表格列等）的显示/隐藏时，优先使用自定义指令 `v-permission`。
    * **最佳实践：**
        * `v-permission="'permission:key'"` 用于单个权限点。
        * `v-permission="['permission:key1', 'permission:key2']"` 用于多个权限点（默认满足其一即可显示）。
        * 当 `v-permission` 无法满足复杂逻辑（如权限点与组件状态结合判断）时，才允许在模板中使用 `v-if` 结合 `userStore` 的方法。

### 6. 国际化 (i18n) 规范

* **6.1 模块化国际化架构**
    * **规范：** 项目采用模块化国际化架构，统一使用 `useModuleI18n` 组合式API进行国际化管理。国际化文件按照以下五个模块进行组织：
        * **common**: 通用文本（按钮、操作、状态等）
        * **sales**: 销售管理相关文本
        * **aftersales**: 售后服务相关文本  
        * **parts**: 零件管理相关文本
        * **base**: 基础系统功能文本（登录、系统设置等）
    * **最佳实践：**
        * 所有组件必须使用 `useModuleI18n(moduleName)` 获取对应模块的翻译函数
        * 禁止直接使用 `$t()` 或 `useI18n()` 的传统方式
        * 新增页面时必须明确属于哪个模块，并使用相应的模块化国际化

* **6.2 组件中的国际化使用**
    * **规范：** 在 Vue 组件中使用模块化国际化的标准方式：
        ```vue
        <template>
          <div>
            <h1>{{ t('vehicleList') }}</h1>
            <el-button>{{ tc('save') }}</el-button>
            <el-button>{{ tc('cancel') }}</el-button>
          </div>
        </template>

        <script setup>
        import { useModuleI18n } from '@/composables/useModuleI18n'

        // 使用对应业务模块的国际化
        const { t, tc } = useModuleI18n('sales')
        // t() 用于访问当前模块的翻译
        // tc() 用于访问通用模块(common)的翻译
        </script>
        ```
    * **最佳实践：**
        * 优先使用 `t()` 访问当前模块的翻译
        * 使用 `tc()` 访问通用模块的翻译（如确认、取消、保存等）
        * 对于需要多个模块翻译的复杂组件，使用 `useMultiModuleI18n(['sales', 'parts'])`

* **6.3 多模块国际化使用**
    * **规范：** 当组件需要使用多个模块的国际化时，使用 `useMultiModuleI18n`：
        ```vue
        <script setup>
        import { useMultiModuleI18n } from '@/composables/useModuleI18n'

        // 同时使用多个模块
        const { sales, parts, tc } = useMultiModuleI18n(['sales', 'parts'])

        // 使用方式
        const title = sales('vehicleList')      // 销售模块
        const partName = parts('partName')      // 零件模块
        const saveText = tc('save')             // 通用模块
        </script>
        ```
    * **最佳实践：**
        * 仅在确实需要多个模块翻译时使用 `useMultiModuleI18n`
        * 避免在单个组件中使用过多模块，保持组件职责单一

* **6.4 模块文件结构与命名**
    * **规范：** 国际化文件严格按照以下结构组织：
        ```
        src/locales/
        ├── modules/
        │   ├── common/           # 通用模块
        │   │   ├── zh.json
        │   │   └── en.json
        │   ├── sales/            # 销售模块
        │   │   ├── zh.json
        │   │   └── en.json
        │   ├── aftersales/       # 售后服务模块
        │   │   ├── zh.json
        │   │   └── en.json
        │   ├── parts/            # 零件管理模块
        │   │   ├── zh.json
        │   │   └── en.json
        │   └── base/             # 基础系统模块
        │       ├── zh.json
        │       └── en.json
        └── loader.ts             # 模块加载器
        ```
    * **最佳实践：**
        * 翻译键名使用 camelCase 命名法
        * 键名应具有明确的语义，如 `vehicleList`、`orderDetail`
        * 避免使用缩写或数字后缀

* **6.5 错误提示与消息国际化**
    * **规范：** 所有用户可见的提示信息必须进行国际化处理：
        ```javascript
        // ElMessage 国际化
        ElMessage.success(tc('saveSuccess'))
        ElMessage.error(tc('saveError'))

        // ElMessageBox 国际化
        ElMessageBox.confirm(tc('confirmDelete'), tc('warning'), {
          confirmButtonText: tc('confirm'),
          cancelButtonText: tc('cancel'),
          type: 'warning'
        })
        ```
    * **最佳实践：**
        * 系统级提示信息统一使用 `tc()` 从 common 模块获取
        * 业务相关的提示信息使用对应模块的 `t()` 函数
        * 动态枚举值显示也需要国际化处理

* **6.6 日期、数字、货币格式化**
    * **规范：** 日期、数字、货币等敏感格式必须使用 JavaScript `Intl` 对象（`Intl.DateTimeFormat`, `Intl.NumberFormat`）结合当前语言环境 (`i18n.global.locale.value`) 进行格式化。
    * **最佳实践：** 封装通用工具函数在 `src/utils/datetime.ts` 或 `src/utils/format.ts` 中，供全项目使用，避免重复代码。

### 7. 代码风格与规范 (ESLint + Prettier)

* **7.1 自动格式化与 Lint 检查**
    * **规范：** AI 提交的所有代码必须通过 ESLint 和 Prettier 的检查，无任何警告或错误。在 VS Code 中，必须配置保存时自动格式化和修复 Lint 问题。
    * **最佳实践：** 熟悉并遵循项目根目录下的 `.eslintrc.cjs` 和 `.prettierrc.cjs` 配置。

* **7.2 命名约定**
    * **规范：**
        * 文件/目录名：`PascalCase` 用于组件 (`VehicleList.vue`), `camelCase` 用于函数库 (`datetime.ts`), `kebab-case` 用于 CSS 类名（如果不是 BEM）。
        * 变量/函数名：`camelCase` (`userName`, `fetchData`).
        * 常量：`UPPER_SNAKE_CASE` (`MAX_RETRIES`).
        * 组件名：`PascalCase` (`<MyComponent>`).
    * **最佳实践：** 命名应清晰、语义化，反映其功能或内容。

* **7.3 注释**
    * **规范：** 复杂逻辑、非常规用法、重要算法或未来可能需要修改的部分必须添加清晰的注释。公共函数和组件的 `props`/`emits` 应使用 JSDoc 风格的注释提供类型和说明。
    * **最佳实践：** 注释应简洁明了，说明“为什么”这么做，而不是简单地重复代码“做了什么”。

### 8. 性能优化规范

* **8.1 路由懒加载**
    * **规范：** 所有路由组件必须使用路由懒加载 (`component: () => import('...')`)，以优化首次加载性能。
    * **最佳实践：** 避免将非路由组件作为懒加载目标，除非它们是大型独立模块。

* **8.2 避免不必要的渲染**
    * **规范：** 避免在 `computed` 属性或 `watch` 监听器中执行昂贵的副作用或修改响应式数据，这可能导致无限循环或不必要的重新渲染。
    * **最佳实践：** 谨慎使用 `deep` 监听，仅在必要时开启。

* **8.3 列表虚拟化 (按需)**
    * **规范：** 对于超过 100 条数据的长列表，必须考虑使用列表虚拟化技术（如 `vue-virtual-scroller` 或 `element-plus-virtual-table`）以提升滚动性能。
    * **最佳实践：** 在设计列表页时，AI 应自动评估数据量级，并在适当时候提示或自动集成虚拟化方案。

