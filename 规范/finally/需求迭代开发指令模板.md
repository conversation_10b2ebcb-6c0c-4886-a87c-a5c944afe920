### **需求迭代开发指令模板 (V1.0)**

**使用说明**：
请复制以下 `[需求迭代任务卡]` 的全部内容，并填写所有标记为 `(由需求方填写)` 的部分。
填写完成后，将整个 `[需求迭代任务卡]` 内容作为你的聊天消息，发送给 AI 开发工程师。
AI 将根据你提供的信息，自动分析、执行任务并生成相应的交付物。我们共同遵循此模板，以确保每次迭代都能达到最高效、最优质的效果。

---

`[需求迭代任务卡]`

## **1. 任务元信息**

*   **模块名称**: (由需求方填写，例如：库存管理)
*   **迭代版本**: (由需求方填写，例如：V1.1)
*   **需求提出人**: (由需求方填写)
*   **执行人**: AI 开发工程师
*   **日期**: (由需求方填写)

## **2. 输入信息 (由需求方填写)**

#### **2.1 迭代目标**
*(请在此处用一两句话，清晰地描述本次迭代的商业或业务目标)*

#### **2.2 相关文档与代码路径**
*(请提供所有相关文件的准确路径，这将极大提升AI的分析效率)*

*   **需求文档目录**: `docs/[模块名称]/[子模块]/`
*   **API 接口文件**: `src/api/modules/[模块]/[相关].ts`
*   **前端视图文件**: `src/views/[模块]/[相关].vue`
*   **类型定义文件**: `src/types/[模块]/[相关].ts`
*   **国际化目录**: `src/locales/modules/[模块]/`

#### **2.3 核心数据结构 (可选，强烈推荐)**
*(如果本次迭代涉及数据结构变更，请在此处粘贴相关信息，如 SQL `CREATE TABLE` 语句、JSON 结构等，这对于AI理解后端逻辑至关重要)*

```sql
-- 在此粘贴数据结构信息
```

#### **2.4 需求变更清单**
*(请按页面或功能模块，结构化地列出所有具体的变更点)*

**A. [页面/组件A] 变更点:**
1.  ...
2.  ...

**B. [页面/组件B] 变更点:**
1.  ...
2.  ...

**C. [接口/类型] 变更点:**
1.  ...
2.  ...

---

## **3. 输出信息 (由 AI 执行并生成)**

#### **3.1 变更分析与执行**
*   **[AI执行]** 你将首先阅读并分析你在 `2.2` 中提供的所有相关文档，并结合 `2.3` 和 `2.4` 的信息，在内存中构建完整的迭代任务图谱。
*   **[AI执行]** 你将严格按照 **UI/线框图 -> 接口/类型定义 -> 需求/规格文档 -> 国际化文件** 的顺序，分步对相关文件进行 `edit_file` 操作，并等待我的确认。

#### **3.2 生成迭代交付物**
*   **[AI执行]** 在所有文件修改得到我的确认后，你将为我生成一份完整的迭代总结文档。

*   **文件名**: `[模块名称]-迭代纪要-V[版本号].md`
*   **存放路径**: `docs/[模块名称]/[子模块]/`
*   **文档内容**:
    *   **1. 需求背景与目标**: 对本次迭代的简要总结。
    *   **2. 需求变更详情 (Changelog)**: 清晰列出所有具体的变更项。
    *   **3. 开发实施计划**:
        *   包含后端、前端、测试等角色的详细任务分解表。
        *   前端任务中应包含对 `视图`、`类型`、`API服务` 和 `国际化` 文件的修改。
    *   **4. 面向AI工程师的详细开发提示词**: (可选，若需要) 生成一份事无巨细、可直接用于后续开发的提示词。

#### **3.3 文件保存**
*   **[AI执行]** 完成 `3.2` 中迭代纪要的生成后，你将调用 `edit_file` 工具，将这份新生成的文档保存在我指定的 `存放路径` 下。 