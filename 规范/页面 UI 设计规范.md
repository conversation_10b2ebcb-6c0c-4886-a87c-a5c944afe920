
## 页面 UI 设计规范 (供 AI 使用)

这份规范将指导 AI 在设计和实现 DMS 系统的页面 UI 时遵循统一的原则和约定，以确保用户体验的一致性、代码的可维护性，并充分利用 Element Plus 组件库。

### 1. 整体布局与结构

* **布局组件统一：** 所有业务页面必须基于 `<DefaultLayout.vue>` 布局组件，该组件提供统一的顶部导航、侧边栏和内容区域。
* **内容区域：** 每个业务页面（对应路由组件）应包含一个主要的 `<div class="page-container">` 或 `<main class="page-content">`，用于承载页面内容。
* **页面标题：** 所有业务页面应在内容区域顶部包含一个 `<h1 class="page-title">` 元素显示页面标题，其内容应与路由 `meta.title` 保持一致。
* **内边距与间距：**
    * 页面容器内边距：统一为 `padding: 20px;`。
    * 区块间距：主要内容区块（如搜索区、表格区、操作区）之间使用 `margin-bottom: 20px;`。
    * 表单项间距：Element Plus 默认的 `el-form-item` 间距足以。
* **背景与阴影：**
    * 页面背景：统一为 `#f5f7fa`（Element Plus 默认背景色）。
    * 内容卡片/区块：使用白色背景 `background-color: #fff;` 并添加轻微阴影 `box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);` 和圆角 `border-radius: 4px;`。

### 2. 响应式与自适应

* **宽度自适应：** 页面内容应宽度自适应，充分利用可用空间，不设置固定宽度。
* **表格滚动：** 当表格内容超出屏幕宽度时，应自动出现横向滚动条，而非导致页面溢出。Element Plus 的 `fixed` 列属性可用于此目的。
* **极端情况处理：** AI 在生成 UI 时应考虑到文本过长、数据为空、加载中等状态的显示。

### 3. 组件库使用规范 (Element Plus 优先)

* **组件优先选择：** 优先使用 Element Plus 提供的组件，除非 Element Plus 无法满足特定需求。
    * **布局：** `<el-container>`, `<el-header>`, `<el-aside>`, `<el-main>`, `<el-footer>`, `<el-row>`, `<el-col>`.
    * **数据展示：** `<el-table>`, `<el-pagination>`, `<el-descriptions>`, `<el-card>`, `<el-tag>`, `<el-empty>`.
    * **表单输入：** `<el-form>`, `<el-form-item>`, `<el-input>`, `<el-select>`, `<el-radio>`, `<el-checkbox>`, `<el-date-picker>`, `<el-time-picker>`, `<el-upload>`, `<el-switch>`, `<el-input-number>`.
    * **操作反馈：** `<el-button>`, `<el-dialog>`, `<el-drawer>`, `<el-popconfirm>`, `<el-message>`, `<el-notification>`, `<el-loading>`.
    * **导航：** `<el-menu>`, `<el-breadcrumb>`, `<el-tabs>`.
    * **图标：** 使用 `@element-plus/icons-vue`。
* **属性与事件：** 熟悉并正确使用 Element Plus 组件的 `props` 和 `events`。
* **表单验证：** 所有表单必须使用 `el-form` 的 `rules` 属性进行客户端验证。
* **Loading 状态：** 在数据加载期间，`el-table` 或其他数据密集型组件应使用 `v-loading` 指令。
* **消息提示：** 操作成功/失败/警告等反馈统一使用 `ElMessage` 或 `ElNotification`。

### 4. 页面类型通用规范

#### 4.1 列表页 (List Page)

* **结构：** 通常包含 **搜索区**、**操作工具栏**、**表格** 和 **分页器** 四个主要部分。
* **搜索区：**
    * 使用 `<el-form :inline="true">` 布局搜索条件，每个条件对应一个 `<el-form-item>`。
    * 搜索按钮和重置按钮放在搜索表单的末尾，并与搜索条件对齐。
    * `v-permission` 指令应正确应用于新增、批量删除等操作按钮。
* **操作工具栏：**
    * 位于搜索区下方、表格上方。
    * 包含“新增”、“批量删除”、“导入”、“导出”等页面级操作按钮。
    * 批量操作按钮应显示选中项的数量（例如：“批量删除 (3)”）。
* **表格：**
    * 使用 `<el-table>` 组件。
    * **列配置：** 至少包含 ID、关键业务字段、状态、创建时间、操作列。
    * **固定列：** VIN 码、ID 等重要且较长的字段可考虑 `fixed="left"`，操作列通常 `fixed="right"`。
    * **状态显示：** 使用 `<el-tag>` 渲染状态字段，根据状态类型选择不同的 `type`（success, info, warning, danger）。
    * **操作列：** 包含“详情”、“编辑”、“删除”等操作按钮，使用 `size="small"`。
    * **空数据：** 表格无数据时应显示 `<el-empty>`。
* **分页器：**
    * 使用 `<el-pagination>` 组件。
    * 布局：`layout="total, sizes, prev, pager, next, jumper"`。
    * 位置：通常放置在表格下方居右显示。

#### 4.2 详情页 (Detail Page)

* **结构：** 通常包含一个或多个 `<el-card>` 或 `<el-descriptions>` 用于展示不同分类的详情信息。
* **信息展示：**
    * 使用 `<el-descriptions>` 展示键值对信息。
    * 对于复杂的关联数据（如销售记录、维护记录），可使用嵌套表格或列表。
* **操作按钮：** 如“返回”、“编辑”等，通常放置在页面底部或顶部。

#### 4.3 表单页/弹窗 (Form Page/Dialog/Drawer)

* **提交方式：**
    * **模态框 (Dialog)：** 适用于简单的新增/编辑操作。
    * **抽屉 (Drawer)：** 适用于表单内容较多、操作流程较长的场景。
    * **独立页面：** 适用于非常复杂、需要全屏操作的表单。
* **表单验证：** 必须使用 `el-form` 的 `rules` 属性进行客户端验证，并根据验证结果显示错误提示。
* **必填项：** 使用 `el-form-item` 的 `required` 属性标记必填项。
* **提交与取消：** 底部通常包含“提交”、“取消”按钮。提交按钮在提交过程中应处于 `loading` 状态。

### 5. 交互行为规范

* **点击反馈：** 所有可点击元素（按钮、链接）应有明显的点击反馈（hover、active 状态）。
* **加载反馈：** 异步操作（数据请求、提交）应提供加载反馈（`v-loading`、`ElMessage`）。
* **错误提示：** 统一的错误提示（`ElMessage.error`）和表单验证错误提示。
* **确认弹窗：** 对于删除、批量操作等破坏性行为，必须使用 `ElMessageBox.confirm` 进行二次确认。
* **Tab 切换：** 如果页面有多个 Tab 切换，确保 Tab 状态与路由保持同步或通过 URL query 参数记录。

### 6. 国际化 (i18n) 规范

* **所有可见文本：** 页面中所有用户可见的文本（包括标题、标签、按钮文本、提示信息、表格列名、枚举值显示等）都必须通过 `$t('key.name')` 进行国际化。
* **变量与复数：** 正确使用 Vue I18n 的变量插值和复数规则。
* **Element Plus 国际化：** 确保 Element Plus 组件的默认文本（如分页器、日期选择器）也已国际化。

### 7. 样式与视觉规范

* **主题色：** 遵循 Element Plus 默认主题色，或根据 DMS 品牌指南进行定制。
* **字体：** 统一使用系统默认字体或指定字体族（如 `sans-serif`）。
* **颜色：** 遵循预定义的颜色变量，不使用硬编码颜色值。
* **图标：** 统一使用 Element Plus Icons，避免使用其他来源的图标。
* **间距与对齐：** 保持组件之间、元素之间的统一间距和对齐方式。
* **可读性：** 确保文本颜色与背景色对比度高，保证可读性。

### 8. 代码结构与命名规范

* **Vue 文件命名：** 大驼峰命名法（PascalCase），例如 `VehicleList.vue`, `UserFormDialog.vue`。
* **组件内部：**
    * `<script setup>` 优先。
    * `props`, `emits` 明确类型定义。
    * `ref`, `reactive` 明确类型。
    * 函数命名：动词开头，清晰表达功能（`fetchData`, `handleSave`, `resetForm`）。
* **CSS 类命名：** 遵循 BEM 命名规范（或团队约定的其他规范），避免全局污染。
* **注释：** 关键业务逻辑、复杂算法、非常规用法应添加注释。

---

**AI 在生成 UI 时，应严格参照以上规范，尤其是：**

* **组件选择和属性使用。**
* **国际化 `$t()` 函数的应用。**
* **`v-permission` 指令的应用。**
* **表单验证规则的生成。**
* **列表页、详情页、表单页的典型结构。**

