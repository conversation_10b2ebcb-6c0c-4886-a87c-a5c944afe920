# 环检单重构技术文档

本方案旨在根据《页面目录结构规范》和《页面重构技术规范》对 `src/views/InspectionForm/` 目录进行重构，提升代码的模块化、可维护性和可读性。

## 1. 重构目标

- **遵循目录规范**：将页面和相关文件迁移到 `src/views/afterSales/inspection/` 目录下。
- **模块化拆分**：将类型定义、API 请求、Mock 数据和子组件进行分离。
- **提升代码质量**：移除内联类型和数据，使用统一的 API 和类型定义。
- **增强可维护性**：通过组件化和模块化，降低单个文件的复杂性。

## 2. 重构分析

`src/views/InspectionForm/` 当前存在以下问题：

- **目录结构不规范**：直接位于 `src/views/`，未按 `afterSales` 模块进行组织。
- **缺少独立API模块**：没有专门的API模块，数据获取逻辑分散。
- **内联Mock数据**：Mock数据直接写在页面组件中，不便于维护和测试。
- **类型定义分散**：类型定义在 `@/types/module.d.ts` 中，应提取到独立的 `.d.ts` 文件。
- **组件结构混乱**：主页面和弹窗组件混合在同一目录层级。

## 3. 重构步骤

### 步骤 1：创建目录和文件结构

根据规范，创建以下目录和文件：

```
src/
├── views/afterSales/inspection/
│   ├── InspectionView.vue              # 主页面（原 InspectionFormList.vue）
│   └── components/
│       ├── AssignDialog.vue            # 分配技师弹窗组件
│       ├── DetailEditDialog.vue        # 详情编辑弹窗组件
│       ├── CustomerConfirmDialog.vue   # 客户确认弹窗组件
│       ├── InspectionSearchForm.vue    # 搜索表单组件
│       └── InspectionTable.vue         # 检查单表格组件
├── api/modules/afterSales/
│   └── inspection.ts                   # 环检单 API 模块
├── types/afterSales/
│   └── inspection.d.ts                 # 环检单类型定义
├── mock/data/afterSales/
│   └── inspection.ts                   # 环检单 Mock 数据
└── locales/modules/afterSales/
    ├── zh.json                         # 中文语言包（已存在，需更新）
    └── en.json                         # 英文语言包（已存在，需更新）
```

### 步骤 2：迁移和重构类型定义

创建 `src/types/afterSales/inspection.d.ts`，定义环检单相关的类型：

```typescript
// src/types/afterSales/inspection.d.ts

// 环检单状态枚举
export type InspectionStatus =
  | 'pending'         // 待处理
  | 'in_progress'     // 检查中
  | 'pending_confirm' // 待客户确认
  | 'confirmed';      // 已确认

// 登记类型枚举
export type RegisterType = 'appointment' | 'walk_in'; // 预约 | 到店

// 服务类型枚举
export type ServiceType = 'maintenance' | 'repair'; // 保养 | 维修

// 环检单列表项接口
export interface InspectionListItem {
  inspectionNo: string;
  inspectionStatus: InspectionStatus;
  repairmanName: string;
  repairmanPhone: string;
  licensePlateNo: string;
  vehicleModel: string;
  vehicleConfig: string;
  color: string;
  mileage: number;
  vehicleAge: number;
  serviceAdvisor: string;
  technician: string;
  registerType: RegisterType;
  serviceType: ServiceType;
  customerConfirmTime: string;
  createTime: string;
  updateTime: string;
  inspectionContent?: any;
}

// 环检单详情接口
export interface InspectionDetail extends InspectionListItem {
  inspectionContent: any; // 检查内容详情
}

// 环检单搜索参数接口
export interface InspectionSearchParams {
  inspectionNo?: string;
  inspectionStatus?: string;
  licensePlateNo?: string;
  repairmanName?: string;
  technician?: string;
  repairmanPhone?: string;
  createTimeRange?: [string, string] | null;
  page?: number;
  pageSize?: number;
}

// 环检单分页响应接口
export interface InspectionPageResponse {
  list: InspectionListItem[];
  total: number;
}

// 技师信息接口
export interface Technician {
  id: string;
  name: string;
  level: string;
  specialties: string[];
}

// 分配技师表单接口
export interface AssignTechnicianForm {
  inspectionNo: string;
  licensePlateNo: string;
  repairmanName: string;
  registerType: string;
  serviceType: string;
  technicianId: string;
}

// 客户确认表单接口
export interface CustomerConfirmForm {
  inspectionNo: string;
  confirmTime: string;
  customerSignature?: string;
  remarks?: string;
}
```

### 步骤 3：重构 Mock 数据

将 Mock 数据逻辑迁移到 `src/mock/data/afterSales/inspection.ts`：

```typescript
// src/mock/data/afterSales/inspection.ts

import type { 
  InspectionSearchParams, 
  InspectionPageResponse, 
  InspectionListItem,
  Technician,
  InspectionStatus,
  RegisterType,
  ServiceType
} from '@/types/afterSales/inspection.d.ts';

// 生成动态 Mock 数据
function generateMockInspectionData(): InspectionListItem[] {
  const data: InspectionListItem[] = [];
  const names = ['张三', '李四', '王五', '李华', '赵六', '王大锤', '钱七', '孙八'];
  const advisors = ['李四', '赵六', '钱七', '孙八'];
  const technicians = ['王五', '孙八', '李华'];
  const licensePlates = ['粤A12345', '京B67890', '沪C54321', '浙D98765', '苏E11111'];
  const vehicleModels = ['Model Y 2023', 'Model 3 2022', 'Model X 2024', 'Model S 2023'];
  const configs = ['长续航版', '标准续航版', '高性能版'];
  const colors = ['白色', '黑色', '蓝色', '红色', '银色'];
  const statuses: InspectionStatus[] = ['pending', 'in_progress', 'pending_confirm', 'confirmed'];
  const registerTypes: RegisterType[] = ['appointment', 'walk_in'];
  const serviceTypes: ServiceType[] = ['maintenance', 'repair'];

  for (let i = 1; i <= 28; i++) {
    const createDate = new Date();
    createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 30));
    const updateDate = new Date(createDate.getTime() + Math.random() * 86400000);

    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const confirmTime = status === 'confirmed' ? 
      new Date(updateDate.getTime() + Math.random() * 86400000).toISOString().slice(0, 16).replace('T', ' ') : '';

    data.push({
      inspectionNo: `IF${String(i).padStart(8, '0')}`,
      inspectionStatus: status,
      repairmanName: names[Math.floor(Math.random() * names.length)],
      repairmanPhone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
      licensePlateNo: licensePlates[Math.floor(Math.random() * licensePlates.length)],
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleConfig: configs[Math.floor(Math.random() * configs.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      mileage: Math.floor(Math.random() * 100000) + 5000,
      vehicleAge: Math.floor(Math.random() * 60) + 6,
      serviceAdvisor: advisors[Math.floor(Math.random() * advisors.length)],
      technician: technicians[Math.floor(Math.random() * technicians.length)],
      registerType: registerTypes[Math.floor(Math.random() * registerTypes.length)],
      serviceType: serviceTypes[Math.floor(Math.random() * serviceTypes.length)],
      customerConfirmTime: confirmTime,
      createTime: createDate.toISOString().slice(0, 16).replace('T', ' '),
      updateTime: updateDate.toISOString().slice(0, 16).replace('T', ' '),
      inspectionContent: null
    });
  }
  
  return data.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
}

const mockInspectionData = generateMockInspectionData();

// 技师Mock数据
const mockTechnicians: Technician[] = [
  { id: '1', name: '王五', level: '高级技师', specialties: ['发动机', '变速箱'] },
  { id: '2', name: '孙八', level: '中级技师', specialties: ['电气系统', '空调'] },
  { id: '3', name: '李华', level: '初级技师', specialties: ['轮胎', '刹车'] }
];

export const getInspectionList = (params: InspectionSearchParams): Promise<InspectionPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockInspectionData];

      // 应用筛选条件
      if (params.inspectionNo) {
        filteredData = filteredData.filter(item => 
          item.inspectionNo.toLowerCase().includes(params.inspectionNo!.toLowerCase())
        );
      }

      if (params.inspectionStatus) {
        filteredData = filteredData.filter(item => 
          item.inspectionStatus === params.inspectionStatus
        );
      }

      if (params.licensePlateNo) {
        filteredData = filteredData.filter(item => 
          item.licensePlateNo.includes(params.licensePlateNo!)
        );
      }

      if (params.repairmanName) {
        filteredData = filteredData.filter(item => 
          item.repairmanName.includes(params.repairmanName!)
        );
      }

      if (params.technician) {
        filteredData = filteredData.filter(item => 
          item.technician.includes(params.technician!)
        );
      }

      if (params.repairmanPhone) {
        filteredData = filteredData.filter(item => 
          item.repairmanPhone.includes(params.repairmanPhone!)
        );
      }

      if (params.createTimeRange && params.createTimeRange.length === 2) {
        const [startDate, endDate] = params.createTimeRange;
        filteredData = filteredData.filter(item => {
          const itemDate = item.createTime.split(' ')[0];
          return itemDate >= startDate && itemDate <= endDate;
        });
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};

export const getTechnicianList = (): Promise<Technician[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockTechnicians);
    }, 200);
  });
};

export const assignTechnician = (inspectionNo: string, technicianId: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        const technician = mockTechnicians.find(t => t.id === technicianId);
        if (technician) {
          mockInspectionData[index].technician = technician.name;
          mockInspectionData[index].inspectionStatus = 'in_progress';
          mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
        }
      }
      resolve({ success: true });
    }, 500);
  });
};

export const submitForConfirm = (inspectionNo: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        mockInspectionData[index].inspectionStatus = 'pending_confirm';
        mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      resolve({ success: true });
    }, 500);
  });
};

export const recallInspection = (inspectionNo: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        mockInspectionData[index].inspectionStatus = 'in_progress';
        mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      resolve({ success: true });
    }, 500);
  });
};

export const customerConfirm = (inspectionNo: string, confirmTime: string): Promise<{ success: boolean }> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockInspectionData.findIndex(item => item.inspectionNo === inspectionNo);
      if (index !== -1) {
        mockInspectionData[index].inspectionStatus = 'confirmed';
        mockInspectionData[index].customerConfirmTime = confirmTime;
        mockInspectionData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
      }
      resolve({ success: true });
    }, 500);
  });
};
```

### 步骤 4：创建 API 模块

创建 `src/api/modules/afterSales/inspection.ts`，统一管理 API 请求：

```typescript
// src/api/modules/afterSales/inspection.ts

import request from '@/api';
import type {
  InspectionSearchParams,
  InspectionPageResponse,
  Technician
} from '@/types/afterSales/inspection.d.ts';
import {
  getInspectionList as getMockInspectionList,
  getTechnicianList as getMockTechnicianList,
  assignTechnician as assignMockTechnician,
  submitForConfirm as submitMockForConfirm,
  recallInspection as recallMockInspection,
  customerConfirm as customerMockConfirm
} from '@/mock/data/afterSales/inspection';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getInspectionList = (params: InspectionSearchParams): Promise<InspectionPageResponse> => {
  if (USE_MOCK_API) {
    return getMockInspectionList(params);
  }
  return request.get<any, InspectionPageResponse>('/after-sales/inspection/list', { params });
};

export const getTechnicianList = (): Promise<Technician[]> => {
  if (USE_MOCK_API) {
    return getMockTechnicianList();
  }
  return request.get<any, Technician[]>('/after-sales/inspection/technicians');
};

export const assignTechnician = (inspectionNo: string, technicianId: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API) {
    return assignMockTechnician(inspectionNo, technicianId);
  }
  return request.post<any, { success: boolean }>('/after-sales/inspection/assign', {
    inspectionNo,
    technicianId
  });
};

export const submitForConfirm = (inspectionNo: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API) {
    return submitMockForConfirm(inspectionNo);
  }
  return request.post<any, { success: boolean }>(`/after-sales/inspection/${inspectionNo}/submit`);
};

export const recallInspection = (inspectionNo: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API) {
    return recallMockInspection(inspectionNo);
  }
  return request.post<any, { success: boolean }>(`/after-sales/inspection/${inspectionNo}/recall`);
};

export const customerConfirm = (inspectionNo: string, confirmTime: string): Promise<{ success: boolean }> => {
  if (USE_MOCK_API) {
    return customerMockConfirm(inspectionNo, confirmTime);
  }
  return request.post<any, { success: boolean }>(`/after-sales/inspection/${inspectionNo}/confirm`, {
    confirmTime
  });
};
```

### 步骤 5：更新国际化文件

在现有的 `src/locales/modules/afterSales/zh.json` 和 `en.json` 中，将环检单相关的翻译组织在 `inspection` 键下：

```json
// src/locales/modules/afterSales/zh.json (新增部分)
{
  "inspection": {
    "title": "环检单管理",
    "searchForm": {
      "inspectionNo": "检查单号",
      "inspectionNoPlaceholder": "请输入检查单号",
      "inspectionStatus": "检查单状态",
      "licensePlateNo": "车牌号",
      "licensePlateNoPlaceholder": "请输入车牌号",
      "repairmanName": "送修人姓名",
      "repairmanNamePlaceholder": "请输入送修人姓名",
      "technician": "技师",
      "technicianPlaceholder": "请选择技师",
      "repairmanPhone": "送修人手机",
      "repairmanPhonePlaceholder": "请输入送修人手机",
      "createTime": "创建时间"
    },
    "status": {
      "pending": "待处理",
      "in_progress": "检查中",
      "pending_confirm": "待客户确认",
      "confirmed": "已确认"
    },
    "table": {
      "inspectionNo": "检查单号",
      "inspectionStatus": "检查单状态",
      "repairmanName": "送修人姓名",
      "repairmanPhone": "送修人手机",
      "licensePlateNo": "车牌号",
      "vehicleModel": "车型",
      "vehicleConfig": "配置",
      "color": "颜色",
      "mileage": "里程",
      "vehicleAge": "车龄",
      "serviceAdvisor": "服务顾问",
      "technician": "技师",
      "registerType": "登记类型",
      "serviceType": "服务类型",
      "customerConfirmTime": "客户确认时间",
      "createTime": "创建时间",
      "updateTime": "更新时间"
    },
    "actions": {
      "assign": "分配技师",
      "submitForConfirm": "提交客户确认",
      "recall": "撤回",
      "customerConfirm": "客户确认",
      "viewDetail": "查看详情",
      "editDetail": "编辑详情"
    },
    "dialog": {
      "assignTitle": "分配技师",
      "confirmTitle": "客户确认",
      "detailTitle": "检查详情",
      "technicianLabel": "选择技师",
      "confirmTimeLabel": "确认时间",
      "remarksLabel": "备注",
      "remarksPlaceholder": "请输入备注信息"
    },
    "registerType": {
      "appointment": "预约",
      "walk_in": "到店"
    },
    "serviceType": {
      "maintenance": "保养",
      "repair": "维修"
    },
    "messages": {
      "assignSuccess": "技师分配成功",
      "submitSuccess": "提交成功",
      "recallSuccess": "撤回成功",
      "confirmSuccess": "确认成功",
      "selectTechnician": "请选择技师",
      "confirmAssign": "确定要分配技师吗？",
      "confirmSubmit": "确定要提交客户确认吗？",
      "confirmRecall": "确定要撤回吗？",
      "confirmCustomerConfirm": "确定要进行客户确认吗？"
    }
  }
}
```

### 步骤 6：创建子组件

#### `InspectionSearchForm.vue` - 搜索表单组件
```vue
<!-- src/views/afterSales/inspection/components/InspectionSearchForm.vue -->
<script setup lang="ts">
import { ElCard, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElButton, ElRow, ElCol } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InspectionSearchParams } from '@/types/afterSales/inspection.d.ts';

interface Props {
  searchParams: InspectionSearchParams;
  dateRange: [string, string] | null;
  technicianOptions: Array<{ label: string; value: string }>;
}

interface Emits {
  (e: 'update:searchParams', value: InspectionSearchParams): void;
  (e: 'update:dateRange', value: [string, string] | null): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const statusOptions = [
  { label: t('status.pending'), value: 'pending' },
  { label: t('status.in_progress'), value: 'in_progress' },
  { label: t('status.pending_confirm'), value: 'pending_confirm' },
  { label: t('status.confirmed'), value: 'confirmed' }
];

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  emit('reset');
};

const updateSearchParams = (field: keyof InspectionSearchParams, value: any) => {
  emit('update:searchParams', { ...props.searchParams, [field]: value });
};

const updateDateRange = (value: [string, string] | null) => {
  emit('update:dateRange', value);
};
</script>

<template>
  <el-card class="mb-20 search-card">
    <el-form :model="searchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.inspectionNo')">
            <el-input
              :model-value="searchParams.inspectionNo"
              @update:model-value="(val) => updateSearchParams('inspectionNo', val)"
              :placeholder="t('searchForm.inspectionNoPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.inspectionStatus')">
            <el-select
              :model-value="searchParams.inspectionStatus"
              @update:model-value="(val) => updateSearchParams('inspectionStatus', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.licensePlateNo')">
            <el-input
              :model-value="searchParams.licensePlateNo"
              @update:model-value="(val) => updateSearchParams('licensePlateNo', val)"
              :placeholder="t('searchForm.licensePlateNoPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.repairmanName')">
            <el-input
              :model-value="searchParams.repairmanName"
              @update:model-value="(val) => updateSearchParams('repairmanName', val)"
              :placeholder="t('searchForm.repairmanNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.technician')">
            <el-select
              :model-value="searchParams.technician"
              @update:model-value="(val) => updateSearchParams('technician', val)"
              :placeholder="t('searchForm.technicianPlaceholder')"
              clearable
            >
              <el-option
                v-for="option in technicianOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.repairmanPhone')">
            <el-input
              :model-value="searchParams.repairmanPhone"
              @update:model-value="(val) => updateSearchParams('repairmanPhone', val)"
              :placeholder="t('searchForm.repairmanPhonePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.createTime')">
            <el-date-picker
              :model-value="dateRange"
              @update:model-value="updateDateRange"
              type="daterange"
              range-separator="-"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item class="search-buttons">
            <el-button type="primary" :icon="Search" @click="handleSearch">{{ tc('search') }}</el-button>
            <el-button @click="handleReset">{{ tc('reset') }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<style scoped lang="scss">
.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
  .search-buttons {
    display: flex;
    align-items: flex-end;
    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
```

#### `InspectionTable.vue` - 检查单表格组件
```vue
<!-- src/views/afterSales/inspection/components/InspectionTable.vue -->
<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElButton, ElPagination, ElTag } from 'element-plus';
import { View, Edit, UserFilled, Check, RefreshLeft } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { InspectionListItem } from '@/types/afterSales/inspection.d.ts';

interface Props {
  inspectionList: InspectionListItem[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

interface Emits {
  (e: 'assign-technician', row: InspectionListItem): void;
  (e: 'submit-for-confirm', row: InspectionListItem): void;
  (e: 'recall-inspection', row: InspectionListItem): void;
  (e: 'customer-confirm', row: InspectionListItem): void;
  (e: 'view-detail', row: InspectionListItem): void;
  (e: 'edit-detail', row: InspectionListItem): void;
  (e: 'page-change', page: number): void;
  (e: 'page-size-change', size: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.inspection');

const getStatusType = (status: string) => {
  const statusMap = {
    pending: 'warning',
    in_progress: 'primary',
    pending_confirm: 'info',
    confirmed: 'success'
  };
  return statusMap[status as keyof typeof statusMap] || 'info';
};

const getRegisterTypeText = (type: string) => {
  return type === 'appointment' ? t('registerType.appointment') : t('registerType.walk_in');
};

const getServiceTypeText = (type: string) => {
  return type === 'maintenance' ? t('serviceType.maintenance') : t('serviceType.repair');
};

const handleAssignTechnician = (row: InspectionListItem) => {
  emit('assign-technician', row);
};

const handleSubmitForConfirm = (row: InspectionListItem) => {
  emit('submit-for-confirm', row);
};

const handleRecallInspection = (row: InspectionListItem) => {
  emit('recall-inspection', row);
};

const handleCustomerConfirm = (row: InspectionListItem) => {
  emit('customer-confirm', row);
};

const handleViewDetail = (row: InspectionListItem) => {
  emit('view-detail', row);
};

const handleEditDetail = (row: InspectionListItem) => {
  emit('edit-detail', row);
};

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handlePageSizeChange = (size: number) => {
  emit('page-size-change', size);
};
</script>

<template>
  <el-card class="table-card">
    <el-table
      :data="inspectionList"
      v-loading="loading"
      style="width: 100%"
      border
      :empty-text="tc('noData')"
    >
      <el-table-column type="index" :label="tc('index')" width="60" />
      <el-table-column prop="inspectionNo" :label="t('table.inspectionNo')" min-width="140" />
      <el-table-column prop="inspectionStatus" :label="t('table.inspectionStatus')" min-width="120">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.inspectionStatus)">
            {{ t(`status.${scope.row.inspectionStatus}`) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="repairmanName" :label="t('table.repairmanName')" min-width="100" />
      <el-table-column prop="repairmanPhone" :label="t('table.repairmanPhone')" min-width="120" />
      <el-table-column prop="licensePlateNo" :label="t('table.licensePlateNo')" min-width="100" />
      <el-table-column prop="vehicleModel" :label="t('table.vehicleModel')" min-width="120" />
      <el-table-column prop="color" :label="t('table.color')" min-width="80" />
      <el-table-column prop="serviceAdvisor" :label="t('table.serviceAdvisor')" min-width="100" />
      <el-table-column prop="technician" :label="t('table.technician')" min-width="100" />
      <el-table-column prop="registerType" :label="t('table.registerType')" min-width="100">
        <template #default="scope">
          {{ getRegisterTypeText(scope.row.registerType) }}
        </template>
      </el-table-column>
      <el-table-column prop="serviceType" :label="t('table.serviceType')" min-width="100">
        <template #default="scope">
          {{ getServiceTypeText(scope.row.serviceType) }}
        </template>
      </el-table-column>
      <el-table-column prop="customerConfirmTime" :label="t('table.customerConfirmTime')" min-width="140">
        <template #default="scope">
          {{ scope.row.customerConfirmTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" :label="t('table.createTime')" min-width="140" />
      <el-table-column :label="tc('operations')" width="300" fixed="right">
        <template #default="scope">
          <!-- 待处理状态 -->
          <template v-if="scope.row.inspectionStatus === 'pending'">
            <el-button type="primary" :icon="UserFilled" link @click="handleAssignTechnician(scope.row)">
              {{ t('actions.assign') }}
            </el-button>
          </template>

          <!-- 检查中状态 -->
          <template v-if="scope.row.inspectionStatus === 'in_progress'">
            <el-button type="success" :icon="Check" link @click="handleSubmitForConfirm(scope.row)">
              {{ t('actions.submitForConfirm') }}
            </el-button>
            <el-button type="primary" :icon="Edit" link @click="handleEditDetail(scope.row)">
              {{ t('actions.editDetail') }}
            </el-button>
          </template>

          <!-- 待客户确认状态 -->
          <template v-if="scope.row.inspectionStatus === 'pending_confirm'">
            <el-button type="warning" :icon="RefreshLeft" link @click="handleRecallInspection(scope.row)">
              {{ t('actions.recall') }}
            </el-button>
            <el-button type="success" :icon="Check" link @click="handleCustomerConfirm(scope.row)">
              {{ t('actions.customerConfirm') }}
            </el-button>
          </template>

          <!-- 通用操作 -->
          <el-button type="info" :icon="View" link @click="handleViewDetail(scope.row)">
            {{ t('actions.viewDetail') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :small="false"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.table-card {
  margin-bottom: 20px;

  :deep(.el-table) {
    .el-table__body td,
    .el-table__header th {
      white-space: nowrap;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
```

### 步骤 7：重构主页面 `InspectionView.vue`

- **移动和重命名**：将 `InspectionFormList.vue` 移动到 `src/views/afterSales/inspection/` 并重命名为 `InspectionView.vue`。
- **移除内联逻辑**：将搜索表单和表格逻辑移动到子组件。
- **更新数据获取**：使用新的 API 模块获取数据。
- **更新类型引用**：从 `@/types/afterSales/inspection.d.ts` 导入类型。
- **更新国际化引用**：使用 `useModuleI18n('afterSales.inspection')`。
- **引入子组件**：在模板中引入并使用子组件。

```vue
<!-- src/views/afterSales/inspection/InspectionView.vue -->
<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import {
  getInspectionList,
  getTechnicianList,
  assignTechnician,
  submitForConfirm,
  recallInspection,
  customerConfirm
} from '@/api/modules/afterSales/inspection';
import type {
  InspectionListItem,
  InspectionSearchParams,
  Technician
} from '@/types/afterSales/inspection.d.ts';

// 导入子组件
import InspectionSearchForm from './components/InspectionSearchForm.vue';
import InspectionTable from './components/InspectionTable.vue';
import AssignDialog from './components/AssignDialog.vue';
import DetailEditDialog from './components/DetailEditDialog.vue';
import CustomerConfirmDialog from './components/CustomerConfirmDialog.vue';

const { t, tc } = useModuleI18n('afterSales.inspection');

// 搜索相关
const searchParams = reactive<InspectionSearchParams>({
  inspectionNo: '',
  inspectionStatus: '',
  licensePlateNo: '',
  repairmanName: '',
  technician: '',
  repairmanPhone: '',
  createTimeRange: null,
});
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  searchParams.createTimeRange = newVal;
});

const inspectionList = ref<InspectionListItem[]>([]);
const loading = ref(false);
const technicians = ref<Technician[]>([]);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 弹窗相关
const assignDialogVisible = ref(false);
const detailEditDialogVisible = ref(false);
const customerConfirmDialogVisible = ref(false);
const currentRecord = ref<InspectionListItem | null>(null);

// 技师选项
const technicianOptions = computed(() =>
  technicians.value.map(tech => ({ label: tech.name, value: tech.name }))
);

// 获取环检单列表
const fetchInspectionList = async () => {
  loading.value = true;
  try {
    const response = await getInspectionList({
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    });
    inspectionList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch inspection list:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 获取技师列表
const fetchTechnicianList = async () => {
  try {
    const response = await getTechnicianList();
    technicians.value = response;
  } catch (error) {
    console.error('Failed to fetch technician list:', error);
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchInspectionList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    inspectionNo: '',
    inspectionStatus: '',
    licensePlateNo: '',
    repairmanName: '',
    technician: '',
    repairmanPhone: '',
    createTimeRange: null,
  });
  dateRange.value = null;
  pagination.page = 1;
  fetchInspectionList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchInspectionList();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchInspectionList();
};

// 分配技师
const handleAssignTechnician = (row: InspectionListItem) => {
  currentRecord.value = row;
  assignDialogVisible.value = true;
};

// 提交客户确认
const handleSubmitForConfirm = async (row: InspectionListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmSubmit'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await submitForConfirm(row.inspectionNo);
        ElMessage.success(t('messages.submitSuccess'));
        fetchInspectionList();
      } catch (error) {
        console.error('Failed to submit for confirm:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 撤回检查
const handleRecallInspection = async (row: InspectionListItem) => {
  ElMessageBox.confirm(
    t('messages.confirmRecall'),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await recallInspection(row.inspectionNo);
        ElMessage.success(t('messages.recallSuccess'));
        fetchInspectionList();
      } catch (error) {
        console.error('Failed to recall inspection:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 客户确认
const handleCustomerConfirm = (row: InspectionListItem) => {
  currentRecord.value = row;
  customerConfirmDialogVisible.value = true;
};

// 查看详情
const handleViewDetail = (row: InspectionListItem) => {
  currentRecord.value = row;
  detailEditDialogVisible.value = true;
};

// 编辑详情
const handleEditDetail = (row: InspectionListItem) => {
  currentRecord.value = row;
  detailEditDialogVisible.value = true;
};

// 分配技师确认
const handleAssignConfirm = async (technicianId: string) => {
  if (!currentRecord.value) return;

  try {
    await assignTechnician(currentRecord.value.inspectionNo, technicianId);
    ElMessage.success(t('messages.assignSuccess'));
    assignDialogVisible.value = false;
    fetchInspectionList();
  } catch (error) {
    console.error('Failed to assign technician:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 客户确认提交
const handleCustomerConfirmSubmit = async (confirmTime: string) => {
  if (!currentRecord.value) return;

  try {
    await customerConfirm(currentRecord.value.inspectionNo, confirmTime);
    ElMessage.success(t('messages.confirmSuccess'));
    customerConfirmDialogVisible.value = false;
    fetchInspectionList();
  } catch (error) {
    console.error('Failed to customer confirm:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTechnicianList();
  fetchInspectionList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('title') }}</h1>

    <!-- 搜索表单 -->
    <InspectionSearchForm
      v-model:search-params="searchParams"
      v-model:date-range="dateRange"
      :technician-options="technicianOptions"
      @search="handleSearch"
      @reset="resetSearch"
    />

    <!-- 数据表格 -->
    <InspectionTable
      :inspection-list="inspectionList"
      :loading="loading"
      :pagination="pagination"
      @assign-technician="handleAssignTechnician"
      @submit-for-confirm="handleSubmitForConfirm"
      @recall-inspection="handleRecallInspection"
      @customer-confirm="handleCustomerConfirm"
      @view-detail="handleViewDetail"
      @edit-detail="handleEditDetail"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 分配技师弹窗 -->
    <AssignDialog
      v-model:visible="assignDialogVisible"
      :record-data="currentRecord"
      :technicians="technicians"
      @confirm="handleAssignConfirm"
    />

    <!-- 详情编辑弹窗 -->
    <DetailEditDialog
      v-model:visible="detailEditDialogVisible"
      :record-data="currentRecord"
    />

    <!-- 客户确认弹窗 -->
    <CustomerConfirmDialog
      v-model:visible="customerConfirmDialogVisible"
      :record-data="currentRecord"
      @confirm="handleCustomerConfirmSubmit"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}
</style>
```

### 步骤 8：更新路由配置

修改 `src/router/index.ts` 或相关路由模块，更新环检单页面的路由配置：

```typescript
// src/router/index.ts
{
  path: '/after-sales/inspection',
  name: 'InspectionManagement',
  component: () => import('@/views/afterSales/inspection/InspectionView.vue'),
  meta: {
    title: 'menu.inspectionManagement',
    requiresAuth: true,
    icon: 'Document'
  }
}
```

## 4. 预期收益

- **结构清晰**：项目结构符合团队规范，新成员更容易上手。
- **职责明确**：每个文件和组件的职责更加单一，便于理解和修改。
- **易于维护**：修改或扩展功能时，只需关注相关模块，降低了代码耦合带来的风险。
- **提升开发效率**：模块化和组件化使得代码复用更加方便。
- **数据动态化**：Mock 数据支持动态生成，便于测试不同场景。
- **类型安全**：完整的 TypeScript 类型定义，提高代码质量和开发体验。

## 5. 重构检查清单

### 5.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/inspection/InspectionView.vue`
- [ ] API模块创建在 `src/api/modules/afterSales/inspection.ts`
- [ ] Mock数据创建在 `src/mock/data/afterSales/inspection.ts`
- [ ] 类型定义创建在 `src/types/afterSales/inspection.d.ts`
- [ ] 子组件创建在 `src/views/afterSales/inspection/components/`

### 5.2 代码质量验证
- [ ] 移除页面中的内联数据和复杂逻辑
- [ ] 使用统一的API调用替换分散的数据获取逻辑
- [ ] TypeScript类型安全，无编译错误
- [ ] 国际化完整覆盖，无硬编码文本
- [ ] Mock数据功能完整，支持完整的业务流程

### 5.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 分配技师功能正常工作
- [ ] 提交客户确认功能正常工作
- [ ] 撤回功能正常工作
- [ ] 客户确认功能正常工作
- [ ] 查看详情功能正常工作
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

### 5.4 路由验证
- [ ] 路由路径更新为新的模块化路径
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 6. 实施建议

### 6.1 实施顺序
1. **创建基础结构**：先创建目录和类型定义
2. **重构数据层**：创建Mock数据和API模块
3. **拆分组件**：创建子组件并测试
4. **重构主页面**：更新主页面逻辑
5. **更新配置**：修改路由和国际化
6. **测试验证**：全面测试功能正常性

### 6.2 风险控制
- **渐进式重构**：一次只重构一个模块，避免大范围影响
- **保持功能不变**：重构过程中不改变业务逻辑和用户体验
- **及时测试**：每个步骤完成后及时验证功能正常
- **代码备份**：重构前备份原始代码

---

**本技术方案基于页面目录结构规范和页面重构技术规范制定，为环检单页面的重构提供详细的实施指导。**
