# 派工管理重构技术方案

本方案旨在根据《页面目录结构规范》和《页面重构技术规范》对 `src/views/workAssignment/WorkAssignmentManagement.vue` 进行重构，提升代码的模块化、可维护性和可读性。

## 1. 重构目标

- **遵循目录规范**：将页面和相关文件迁移到 `src/views/afterSales/workAssignment/` 目录下。
- **模块化拆分**：将类型定义、API 请求、Mock 数据和子组件进行分离。
- **提升代码质量**：移除内联类型和数据，使用统一的 API 和类型定义。
- **增强可维护性**：通过组件化和模块化，降低单个文件的复杂性。

## 2. 重构分析

`WorkAssignmentManagement.vue` 当前存在以下问题：

- **文件位置不规范**：位于 `src/views/workAssignment/`，未按 `afterSales` 模块进行组织。
- **API 模块分散**：使用 `@/api/modules/workAssignment` 而非模块化的 `afterSales/workAssignment`。
- **类型定义位置不当**：类型定义在 `@/types/module` 中，应迁移到 `afterSales` 目录下。
- **组件复杂度高**：单文件 631 行，包含多个弹窗和复杂逻辑，应拆分为子组件。
- **缺少独立Mock数据**：没有专门的Mock数据模块，不便于测试和开发。

## 3. 重构步骤

### 步骤 1：创建目录和文件结构

根据规范，创建以下目录和文件：

```
src/
├── views/afterSales/workAssignment/
│   ├── WorkAssignmentView.vue               # 主页面（原 WorkAssignmentManagement.vue）
│   └── components/
│       ├── WorkAssignmentSearchForm.vue    # 搜索表单组件
│       ├── WorkAssignmentTable.vue         # 工单表格组件
│       ├── AssignmentDialog.vue            # 分配工单弹窗组件
│       └── ReassignmentDialog.vue          # 重新分配弹窗组件
├── api/modules/afterSales/
│   └── workAssignment.ts                   # 派工 API 模块
├── types/afterSales/
│   └── workAssignment.d.ts                 # 派工类型定义
├── mock/data/afterSales/
│   └── workAssignment.ts                   # 派工 Mock 数据
└── locales/modules/afterSales/
    ├── zh.json                             # 中文语言包（已存在，需更新）
    └── en.json                             # 英文语言包（已存在，需更新）
```

### 步骤 2：迁移和重构类型定义

将相关类型定义迁移到 `src/types/afterSales/workAssignment.d.ts`，并进行优化：

```typescript
// src/types/afterSales/workAssignment.d.ts

// 工单状态枚举
export type WorkOrderStatus =
  | 'pending_assign'    // 待分配
  | 'pending_start'     // 待开始
  | 'in_progress'       // 进行中
  | 'completed'         // 已完成
  | 'cancelled'         // 已取消
  | 'on_hold';          // 暂停

// 工单类型枚举
export type WorkOrderType = 'maintenance' | 'repair' | 'inspection' | 'insurance'; // 保养 | 维修 | 检查 | 保险

// 工单优先级枚举
export type WorkOrderPriority = 'low' | 'normal' | 'high' | 'urgent'; // 低 | 普通 | 高 | 紧急

// 技师状态枚举
export type TechnicianStatus = 'available' | 'busy' | 'offline'; // 空闲 | 忙碌 | 离线

// 工单列表项接口
export interface WorkOrderListItem {
  workOrderId: string;
  workOrderNo: string;
  customerName: string;
  customerPhone: string;
  licensePlate: string;
  vehicleModel: string;
  vehicleVin: string;
  status: WorkOrderStatus;
  workOrderType: WorkOrderType;
  priority: WorkOrderPriority;
  serviceAdvisorId: string;
  serviceAdvisorName: string;
  assignedTechnicianId?: string;
  assignedTechnicianName?: string;
  estimatedDuration: number; // 预计工时（分钟）
  estimatedStartTime?: string;
  actualStartTime?: string;
  actualEndTime?: string;
  creationTime: string;
  updateTime: string;
  description: string;
  notes?: string;
}

// 技师信息接口
export interface TechnicianInfo {
  technicianId: string;
  technicianName: string;
  technicianCode: string;
  status: TechnicianStatus;
  skillLevel: number; // 技能等级 1-5
  specialties: string[]; // 专长领域
  currentWorkload: number; // 当前工作负荷（百分比）
  maxWorkload: number; // 最大工作负荷（百分比）
  workingHours: {
    start: string; // 工作开始时间
    end: string;   // 工作结束时间
  };
  contactInfo: {
    phone: string;
    email?: string;
  };
  department: string;
  position: string;
  hireDate: string;
  isActive: boolean;
}

// 工单搜索参数接口
export interface WorkOrderListParams {
  workOrderId?: string;
  workOrderNo?: string;
  customerName?: string;
  licensePlate?: string;
  status?: WorkOrderStatus;
  workOrderType?: WorkOrderType;
  priority?: WorkOrderPriority;
  serviceAdvisorId?: string;
  assignedTechnicianId?: string;
  creationTimeStart?: string;
  creationTimeEnd?: string;
  estimatedStartTimeStart?: string;
  estimatedStartTimeEnd?: string;
  page?: number;
  pageSize?: number;
}

// 工单分页响应接口
export interface WorkOrderPageResponse {
  list: WorkOrderListItem[];
  total: number;
  page: number;
  pageSize: number;
}

// 分配工单请求接口
export interface AssignWorkOrderRequest {
  workOrderId: string;
  technicianId: string;
  estimatedStartTime?: string;
  estimatedDuration?: number;
  notes?: string;
}

// 重新分配工单请求接口
export interface ReassignWorkOrderRequest {
  workOrderId: string;
  fromTechnicianId: string;
  toTechnicianId: string;
  reason: string;
  estimatedStartTime?: string;
  notes?: string;
}

// 分配结果接口
export interface AssignmentResult {
  success: boolean;
  message: string;
  workOrderId: string;
  assignedTechnicianId: string;
  estimatedStartTime?: string;
}

// 技师工作负荷接口
export interface TechnicianWorkload {
  technicianId: string;
  technicianName: string;
  currentOrders: number;
  totalWorkload: number; // 总工作负荷（分钟）
  availableCapacity: number; // 可用容量（分钟）
  workloadPercentage: number; // 工作负荷百分比
}

// 派工统计接口
export interface WorkAssignmentStatistics {
  totalOrders: number;
  pendingAssignment: number;
  assignedOrders: number;
  inProgressOrders: number;
  completedOrders: number;
  averageAssignmentTime: number; // 平均分配时间（分钟）
  technicianUtilization: number; // 技师利用率（百分比）
}
```

### 步骤 3：重构 Mock 数据

创建 `src/mock/data/afterSales/workAssignment.ts`，提供完整的Mock数据支持：

```typescript
// src/mock/data/afterSales/workAssignment.ts

import type { 
  WorkOrderListParams, 
  WorkOrderPageResponse, 
  WorkOrderListItem,
  TechnicianInfo,
  AssignWorkOrderRequest,
  ReassignWorkOrderRequest,
  AssignmentResult,
  TechnicianWorkload,
  WorkAssignmentStatistics,
  WorkOrderStatus,
  WorkOrderType,
  WorkOrderPriority,
  TechnicianStatus
} from '@/types/afterSales/workAssignment.d.ts';

// 生成动态 Mock 数据
function generateMockWorkOrderData(): WorkOrderListItem[] {
  const data: WorkOrderListItem[] = [];
  const customerNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
  const advisors = ['李明', '王强', '张伟', '刘洋', '陈华'];
  const technicians = ['陈师傅', '刘师傅', '王师傅', '张师傅', '李师傅'];
  const licensePlates = ['京A12345', '沪B67890', '粤C54321', '浙D98765', '苏E11111'];
  const vehicleModels = ['Model Y 2023', 'Model 3 2022', 'Model X 2024', 'Model S 2023'];
  const statuses: WorkOrderStatus[] = ['pending_assign', 'pending_start', 'in_progress', 'completed', 'cancelled'];
  const types: WorkOrderType[] = ['maintenance', 'repair', 'inspection', 'insurance'];
  const priorities: WorkOrderPriority[] = ['low', 'normal', 'high', 'urgent'];

  for (let i = 1; i <= 100; i++) {
    const createDate = new Date();
    createDate.setDate(createDate.getDate() - Math.floor(Math.random() * 30));
    const updateDate = new Date(createDate.getTime() + Math.random() * 86400000 * 3);

    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const hasAssignedTechnician = status !== 'pending_assign';
    const assignedTechnician = hasAssignedTechnician ? technicians[Math.floor(Math.random() * technicians.length)] : undefined;

    data.push({
      workOrderId: `WO${String(i).padStart(8, '0')}`,
      workOrderNo: `WO${new Date().getFullYear()}${String(i).padStart(6, '0')}`,
      customerName: customerNames[Math.floor(Math.random() * customerNames.length)],
      customerPhone: `138${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
      licensePlate: licensePlates[Math.floor(Math.random() * licensePlates.length)],
      vehicleModel: vehicleModels[Math.floor(Math.random() * vehicleModels.length)],
      vehicleVin: `LFV${Math.random().toString(36).substr(2, 14).toUpperCase()}`,
      status,
      workOrderType: types[Math.floor(Math.random() * types.length)],
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      serviceAdvisorId: `SA${String(Math.floor(Math.random() * 5) + 1).padStart(3, '0')}`,
      serviceAdvisorName: advisors[Math.floor(Math.random() * advisors.length)],
      assignedTechnicianId: hasAssignedTechnician ? `T${String(Math.floor(Math.random() * 5) + 1).padStart(3, '0')}` : undefined,
      assignedTechnicianName: assignedTechnician,
      estimatedDuration: Math.floor(Math.random() * 240) + 60, // 60-300分钟
      estimatedStartTime: hasAssignedTechnician ? new Date(createDate.getTime() + Math.random() * 86400000).toISOString().slice(0, 16).replace('T', ' ') : undefined,
      actualStartTime: status === 'in_progress' || status === 'completed' ? new Date(createDate.getTime() + Math.random() * 86400000).toISOString().slice(0, 16).replace('T', ' ') : undefined,
      actualEndTime: status === 'completed' ? new Date(createDate.getTime() + Math.random() * 86400000 * 2).toISOString().slice(0, 16).replace('T', ' ') : undefined,
      creationTime: createDate.toISOString().slice(0, 16).replace('T', ' '),
      updateTime: updateDate.toISOString().slice(0, 16).replace('T', ' '),
      description: `${types[Math.floor(Math.random() * types.length)] === 'repair' ? '维修' : '保养'}服务`,
      notes: Math.random() > 0.7 ? '客户要求使用原厂配件' : undefined
    });
  }
  
  return data.sort((a, b) => new Date(b.creationTime).getTime() - new Date(a.creationTime).getTime());
}

// 生成技师Mock数据
function generateMockTechnicianData(): TechnicianInfo[] {
  const technicianNames = ['陈师傅', '刘师傅', '王师傅', '张师傅', '李师傅'];
  const specialties = [
    ['发动机维修', '变速箱维修'],
    ['电气系统', '空调系统'],
    ['底盘维修', '刹车系统'],
    ['车身维修', '喷漆'],
    ['轮胎更换', '四轮定位']
  ];
  const statuses: TechnicianStatus[] = ['available', 'busy', 'offline'];

  return technicianNames.map((name, index) => ({
    technicianId: `T${String(index + 1).padStart(3, '0')}`,
    technicianName: name,
    technicianCode: `TECH${String(index + 1).padStart(3, '0')}`,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    skillLevel: Math.floor(Math.random() * 5) + 1,
    specialties: specialties[index],
    currentWorkload: Math.floor(Math.random() * 100),
    maxWorkload: 100,
    workingHours: {
      start: '08:00',
      end: '18:00'
    },
    contactInfo: {
      phone: `139${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
      email: `${name.replace('师傅', '')}@company.com`
    },
    department: '维修部',
    position: '高级技师',
    hireDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().slice(0, 10),
    isActive: true
  }));
}

const mockWorkOrderData = generateMockWorkOrderData();
const mockTechnicianData = generateMockTechnicianData();

export const getWorkOrderList = (params: WorkOrderListParams): Promise<WorkOrderPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockWorkOrderData];

      // 应用筛选条件
      if (params.workOrderId) {
        filteredData = filteredData.filter(item => 
          item.workOrderId.toLowerCase().includes(params.workOrderId!.toLowerCase())
        );
      }

      if (params.workOrderNo) {
        filteredData = filteredData.filter(item => 
          item.workOrderNo.toLowerCase().includes(params.workOrderNo!.toLowerCase())
        );
      }

      if (params.customerName) {
        filteredData = filteredData.filter(item => 
          item.customerName.includes(params.customerName!)
        );
      }

      if (params.licensePlate) {
        filteredData = filteredData.filter(item => 
          item.licensePlate.includes(params.licensePlate!)
        );
      }

      if (params.status) {
        filteredData = filteredData.filter(item => item.status === params.status);
      }

      if (params.workOrderType) {
        filteredData = filteredData.filter(item => item.workOrderType === params.workOrderType);
      }

      if (params.priority) {
        filteredData = filteredData.filter(item => item.priority === params.priority);
      }

      if (params.serviceAdvisorId) {
        filteredData = filteredData.filter(item => item.serviceAdvisorId === params.serviceAdvisorId);
      }

      if (params.assignedTechnicianId) {
        filteredData = filteredData.filter(item => item.assignedTechnicianId === params.assignedTechnicianId);
      }

      if (params.creationTimeStart && params.creationTimeEnd) {
        filteredData = filteredData.filter(item => {
          const itemDate = item.creationTime.split(' ')[0];
          return itemDate >= params.creationTimeStart! && itemDate <= params.creationTimeEnd!;
        });
      }

      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
        page,
        pageSize
      });
    }, 300);
  });
};

export const getTechnicianList = (): Promise<TechnicianInfo[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockTechnicianData);
    }, 200);
  });
};

export const assignWorkOrder = (request: AssignWorkOrderRequest): Promise<AssignmentResult> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === request.workOrderId);
      if (index !== -1) {
        const technician = mockTechnicianData.find(t => t.technicianId === request.technicianId);
        if (technician) {
          mockWorkOrderData[index].assignedTechnicianId = request.technicianId;
          mockWorkOrderData[index].assignedTechnicianName = technician.technicianName;
          mockWorkOrderData[index].status = 'pending_start';
          mockWorkOrderData[index].estimatedStartTime = request.estimatedStartTime;
          mockWorkOrderData[index].estimatedDuration = request.estimatedDuration || mockWorkOrderData[index].estimatedDuration;
          mockWorkOrderData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
        }
      }

      resolve({
        success: true,
        message: '工单分配成功',
        workOrderId: request.workOrderId,
        assignedTechnicianId: request.technicianId,
        estimatedStartTime: request.estimatedStartTime
      });
    }, 800);
  });
};

export const reassignWorkOrder = (request: ReassignWorkOrderRequest): Promise<AssignmentResult> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const index = mockWorkOrderData.findIndex(item => item.workOrderId === request.workOrderId);
      if (index !== -1) {
        const technician = mockTechnicianData.find(t => t.technicianId === request.toTechnicianId);
        if (technician) {
          mockWorkOrderData[index].assignedTechnicianId = request.toTechnicianId;
          mockWorkOrderData[index].assignedTechnicianName = technician.technicianName;
          mockWorkOrderData[index].estimatedStartTime = request.estimatedStartTime;
          mockWorkOrderData[index].updateTime = new Date().toISOString().slice(0, 16).replace('T', ' ');
        }
      }

      resolve({
        success: true,
        message: '工单重新分配成功',
        workOrderId: request.workOrderId,
        assignedTechnicianId: request.toTechnicianId,
        estimatedStartTime: request.estimatedStartTime
      });
    }, 800);
  });
};

export const getTechnicianWorkload = (): Promise<TechnicianWorkload[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const workloadData = mockTechnicianData.map(technician => ({
        technicianId: technician.technicianId,
        technicianName: technician.technicianName,
        currentOrders: Math.floor(Math.random() * 5) + 1,
        totalWorkload: Math.floor(Math.random() * 480) + 120, // 120-600分钟
        availableCapacity: Math.floor(Math.random() * 240) + 60, // 60-300分钟
        workloadPercentage: technician.currentWorkload
      }));

      resolve(workloadData);
    }, 300);
  });
};

export const getAssignmentStatistics = (): Promise<WorkAssignmentStatistics> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const totalOrders = mockWorkOrderData.length;
      const pendingAssignment = mockWorkOrderData.filter(item => item.status === 'pending_assign').length;
      const assignedOrders = mockWorkOrderData.filter(item => item.status === 'pending_start').length;
      const inProgressOrders = mockWorkOrderData.filter(item => item.status === 'in_progress').length;
      const completedOrders = mockWorkOrderData.filter(item => item.status === 'completed').length;

      resolve({
        totalOrders,
        pendingAssignment,
        assignedOrders,
        inProgressOrders,
        completedOrders,
        averageAssignmentTime: Math.floor(Math.random() * 30) + 15, // 15-45分钟
        technicianUtilization: Math.floor(Math.random() * 40) + 60 // 60-100%
      });
    }, 300);
  });
};
```

### 步骤 4：创建 API 模块

创建 `src/api/modules/afterSales/workAssignment.ts`，统一管理 API 请求：

```typescript
// src/api/modules/afterSales/workAssignment.ts

import request from '@/api';
import type {
  WorkOrderListParams,
  WorkOrderPageResponse,
  TechnicianInfo,
  AssignWorkOrderRequest,
  ReassignWorkOrderRequest,
  AssignmentResult,
  TechnicianWorkload,
  WorkAssignmentStatistics
} from '@/types/afterSales/workAssignment.d.ts';
import {
  getWorkOrderList as getMockWorkOrderList,
  getTechnicianList as getMockTechnicianList,
  assignWorkOrder as assignMockWorkOrder,
  reassignWorkOrder as reassignMockWorkOrder,
  getTechnicianWorkload as getMockTechnicianWorkload,
  getAssignmentStatistics as getMockAssignmentStatistics
} from '@/mock/data/afterSales/workAssignment';

let USE_MOCK_API_TEMP = true;

export const getWorkOrderList = (params: WorkOrderListParams): Promise<WorkOrderPageResponse> => {
  if (USE_MOCK_API_TEMP) {
    return getMockWorkOrderList(params);
  }
  return request.get<any, WorkOrderPageResponse>('/after-sales/work-assignment/orders', { params });
};

export const getTechnicianList = (): Promise<TechnicianInfo[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianList();
  }
  return request.get<any, TechnicianInfo[]>('/after-sales/work-assignment/technicians');
};

export const assignWorkOrder = (data: AssignWorkOrderRequest): Promise<AssignmentResult> => {
  if (USE_MOCK_API_TEMP) {
    return assignMockWorkOrder(data);
  }
  return request.post<any, AssignmentResult>('/after-sales/work-assignment/assign', data);
};

export const reassignWorkOrder = (data: ReassignWorkOrderRequest): Promise<AssignmentResult> => {
  if (USE_MOCK_API_TEMP) {
    return reassignMockWorkOrder(data);
  }
  return request.post<any, AssignmentResult>('/after-sales/work-assignment/reassign', data);
};

export const getTechnicianWorkload = (): Promise<TechnicianWorkload[]> => {
  if (USE_MOCK_API_TEMP) {
    return getMockTechnicianWorkload();
  }
  return request.get<any, TechnicianWorkload[]>('/after-sales/work-assignment/workload');
};

export const getAssignmentStatistics = (): Promise<WorkAssignmentStatistics> => {
  if (USE_MOCK_API_TEMP) {
    return getMockAssignmentStatistics();
  }
  return request.get<any, WorkAssignmentStatistics>('/after-sales/work-assignment/statistics');
};
```

### 步骤 5：更新国际化文件

在现有的 `src/locales/modules/afterSales/zh.json` 和 `en.json` 中，将派工相关的翻译组织在 `workAssignment` 键下：

```json
// src/locales/modules/afterSales/zh.json (新增部分)
{
  "workAssignment": {
    "title": "派工管理",
    "searchForm": {
      "workOrderId": "工单ID",
      "workOrderIdPlaceholder": "请输入工单ID",
      "workOrderNo": "工单号",
      "workOrderNoPlaceholder": "请输入工单号",
      "customerName": "客户姓名",
      "customerNamePlaceholder": "请输入客户姓名",
      "licensePlate": "车牌号",
      "licensePlatePlaceholder": "请输入车牌号",
      "status": "工单状态",
      "workOrderType": "工单类型",
      "priority": "优先级",
      "serviceAdvisor": "服务顾问",
      "assignedTechnician": "分配技师",
      "creationTime": "创建时间"
    },
    "status": {
      "pending_assign": "待分配",
      "pending_start": "待开始",
      "in_progress": "进行中",
      "completed": "已完成",
      "cancelled": "已取消",
      "on_hold": "暂停"
    },
    "workOrderType": {
      "maintenance": "保养",
      "repair": "维修",
      "inspection": "检查",
      "insurance": "保险"
    },
    "priority": {
      "low": "低",
      "normal": "普通",
      "high": "高",
      "urgent": "紧急"
    },
    "technicianStatus": {
      "available": "空闲",
      "busy": "忙碌",
      "offline": "离线"
    },
    "table": {
      "workOrderNo": "工单号",
      "customerName": "客户姓名",
      "vehicleInfo": "车辆信息",
      "status": "状态",
      "workOrderType": "类型",
      "priority": "优先级",
      "serviceAdvisor": "服务顾问",
      "assignedTechnician": "分配技师",
      "estimatedDuration": "预计工时",
      "scheduledStartTime": "计划开始时间",
      "creationTime": "创建时间",
      "currentTechnician": "当前技师"
    },
    "actions": {
      "assign": "分配",
      "reassign": "重新分配",
      "detail": "详情",
      "cancel": "取消",
      "confirm": "确认",
      "search": "搜索",
      "reset": "重置",
      "export": "导出"
    },
    "dialog": {
      "assignTitle": "分配工单",
      "reassignTitle": "重新分配工单",
      "selectTechnician": "选择技师",
      "estimatedStartTime": "预计开始时间",
      "estimatedDuration": "预计工时",
      "notes": "备注",
      "notesPlaceholder": "请输入备注信息",
      "reason": "重新分配原因",
      "reasonPlaceholder": "请输入重新分配原因"
    },
    "technician": {
      "name": "技师姓名",
      "status": "状态",
      "skillLevel": "技能等级",
      "specialties": "专长",
      "currentWorkload": "当前工作负荷",
      "workingHours": "工作时间",
      "contactInfo": "联系方式",
      "currentTechnician": "当前技师",
      "selectTechnician": "请选择技师"
    },
    "messages": {
      "assignSuccess": "工单分配成功",
      "reassignSuccess": "工单重新分配成功",
      "operationFailed": "操作失败",
      "loadDataFailed": "数据加载失败",
      "selectTechnicianFirst": "请先选择技师",
      "confirmAssign": "确定要分配这个工单吗？",
      "confirmReassign": "确定要重新分配这个工单吗？"
    },
    "common": {
      "unassigned": "未分配",
      "notScheduled": "未安排",
      "noData": "暂无数据",
      "loading": "加载中...",
      "total": "共",
      "items": "条",
      "cancel": "取消",
      "confirm": "确认",
      "actions": "操作",
      "detail": "详情",
      "warning": "警告",
      "success": "成功",
      "error": "错误",
      "info": "信息"
    }
  }
}
```

### 步骤 6：创建子组件

#### `WorkAssignmentSearchForm.vue` - 搜索表单组件
```vue
<!-- src/views/afterSales/workAssignment/components/WorkAssignmentSearchForm.vue -->
<script setup lang="ts">
import { ElCard, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElDatePicker, ElButton, ElRow, ElCol } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { WorkOrderListParams, TechnicianInfo } from '@/types/afterSales/workAssignment.d.ts';

interface Props {
  searchParams: WorkOrderListParams;
  dateRange: [string, string] | null;
  technicianList: TechnicianInfo[];
  statusOptions: Array<{ label: string; value: string }>;
  typeOptions: Array<{ label: string; value: string }>;
  priorityOptions: Array<{ label: string; value: string }>;
}

interface Emits {
  (e: 'update:searchParams', value: WorkOrderListParams): void;
  (e: 'update:dateRange', value: [string, string] | null): void;
  (e: 'search'): void;
  (e: 'reset'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.workAssignment');

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  emit('reset');
};

const updateSearchParams = (field: keyof WorkOrderListParams, value: any) => {
  emit('update:searchParams', { ...props.searchParams, [field]: value });
};

const updateDateRange = (value: [string, string] | null) => {
  emit('update:dateRange', value);
};
</script>

<template>
  <el-card class="mb-20 search-card">
    <el-form :model="searchParams" class="search-form" label-position="top">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.workOrderNo')">
            <el-input
              :model-value="searchParams.workOrderNo"
              @update:model-value="(val) => updateSearchParams('workOrderNo', val)"
              :placeholder="t('searchForm.workOrderNoPlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.customerName')">
            <el-input
              :model-value="searchParams.customerName"
              @update:model-value="(val) => updateSearchParams('customerName', val)"
              :placeholder="t('searchForm.customerNamePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.licensePlate')">
            <el-input
              :model-value="searchParams.licensePlate"
              @update:model-value="(val) => updateSearchParams('licensePlate', val)"
              :placeholder="t('searchForm.licensePlatePlaceholder')"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.status')">
            <el-select
              :model-value="searchParams.status"
              @update:model-value="(val) => updateSearchParams('status', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item :label="t('searchForm.workOrderType')">
            <el-select
              :model-value="searchParams.workOrderType"
              @update:model-value="(val) => updateSearchParams('workOrderType', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in typeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.priority')">
            <el-select
              :model-value="searchParams.priority"
              @update:model-value="(val) => updateSearchParams('priority', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="option in priorityOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.assignedTechnician')">
            <el-select
              :model-value="searchParams.assignedTechnicianId"
              @update:model-value="(val) => updateSearchParams('assignedTechnicianId', val)"
              :placeholder="tc('pleaseSelect')"
              clearable
            >
              <el-option
                v-for="technician in technicianList"
                :key="technician.technicianId"
                :label="technician.technicianName"
                :value="technician.technicianId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item :label="t('searchForm.creationTime')">
            <el-date-picker
              :model-value="dateRange"
              @update:model-value="updateDateRange"
              type="daterange"
              range-separator="-"
              :start-placeholder="tc('startDate')"
              :end-placeholder="tc('endDate')"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" class="buttons-col">
          <el-button type="primary" :icon="Search" @click="handleSearch">{{ t('actions.search') }}</el-button>
          <el-button @click="handleReset">{{ t('actions.reset') }}</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<style scoped lang="scss">
.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
  .buttons-col {
    text-align: right;
    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
```

### 步骤 7：重构主页面 `WorkAssignmentView.vue`

- **移动和重命名**：将 `WorkAssignmentManagement.vue` 移动到 `src/views/afterSales/workAssignment/` 并重命名为 `WorkAssignmentView.vue`。
- **移除内联逻辑**：将搜索表单和表格逻辑移动到子组件。
- **更新数据获取**：使用新的 API 模块获取数据。
- **更新类型引用**：从 `@/types/afterSales/workAssignment.d.ts` 导入类型。
- **更新国际化引用**：使用 `useModuleI18n('afterSales.workAssignment')`。
- **引入子组件**：在模板中引入并使用子组件。

### 步骤 8：更新路由配置

修改 `src/router/index.ts`，更新派工管理页面的路由配置：

```typescript
// src/router/index.ts
{
  path: '/after-sales/work-assignment',
  name: 'WorkAssignmentManagement',
  component: () => import('@/views/afterSales/workAssignment/WorkAssignmentView.vue'),
  meta: {
    title: 'menu.workAssignmentManagement',
    requiresAuth: true,
    icon: 'Document'
  }
}
```

## 4. 预期收益

- **结构清晰**：项目结构符合团队规范，新成员更容易上手。
- **职责明确**：每个文件和组件的职责更加单一，便于理解和修改。
- **易于维护**：修改或扩展功能时，只需关注相关模块，降低了代码耦合带来的风险。
- **提升开发效率**：模块化和组件化使得代码复用更加方便。
- **数据动态化**：Mock 数据支持动态生成，便于测试不同场景。
- **类型安全**：完整的 TypeScript 类型定义，提高代码质量和开发体验。

## 5. 重构检查清单

### 5.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/workAssignment/WorkAssignmentView.vue`
- [ ] API模块创建在 `src/api/modules/afterSales/workAssignment.ts`
- [ ] Mock数据创建在 `src/mock/data/afterSales/workAssignment.ts`
- [ ] 类型定义创建在 `src/types/afterSales/workAssignment.d.ts`
- [ ] 子组件创建在 `src/views/afterSales/workAssignment/components/`

### 5.2 代码质量验证
- [ ] 移除页面中的内联数据和复杂逻辑
- [ ] 使用统一的API调用替换分散的数据获取逻辑
- [ ] TypeScript类型安全，无编译错误
- [ ] 国际化完整覆盖，无硬编码文本
- [ ] Mock数据功能完整，支持完整的业务流程

### 5.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 工单分配、重新分配功能正常工作
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

### 5.4 路由验证
- [ ] 路由路径更新为新的模块化路径
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 6. 实施建议

### 6.1 实施顺序
1. **创建基础结构**：先创建目录和类型定义
2. **重构数据层**：创建Mock数据和API模块
3. **拆分组件**：创建子组件并测试
4. **重构主页面**：更新主页面逻辑
5. **更新配置**：修改路由和国际化
6. **测试验证**：全面测试功能正常性

### 6.2 风险控制
- **渐进式重构**：一次只重构一个模块，避免大范围影响
- **保持功能不变**：重构过程中不改变业务逻辑和用户体验
- **及时测试**：每个步骤完成后及时验证功能正常
- **代码备份**：重构前备份原始代码

---

**本技术方案基于页面目录结构规范和页面重构技术规范制定，为派工管理页面的重构提供详细的实施指导。**
