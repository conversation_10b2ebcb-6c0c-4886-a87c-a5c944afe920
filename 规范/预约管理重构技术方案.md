# 预约管理页面重构技术方案

本方案旨在根据《页面目录结构规范》和《页面重构技术规范》对 `AppointmentManagementView.vue` 进行重构，提升代码的模块化、可维护性和可读性。

## 1. 重构目标

- **遵循目录规范**：将页面和相关文件迁移到 `src/views/afterSales/appointments/` 目录下。
- **模块化拆分**：将类型定义、API 请求、Mock 数据和子组件（详情、环检单弹窗）进行分离。
- **提升代码质量**：移除内联类型和数据，使用统一的 API 和类型定义。
- **增强可维护性**：通过组件化和模块化，降低单个文件的复杂性。

## 2. 重构分析

`AppointmentManagementView.vue` 当前存在以下问题：

- **文件位置不规范**：直接位于 `src/views/`，未按 `afterSales` 模块进行组织。
- **组件功能臃肿**：集列表、搜索、详情、环检单创建于一体，违反单一职责原则。
- **内联定义过多**：包含内联的 TypeScript 类型定义，应提取到独立的 `.d.ts` 文件。
- **Mock 数据耦合**：直接从 `@/mock/data/afterSales` 导入，未通过统一的 API 模块管理。

## 3. 重构步骤

### 步骤 1：创建目录和文件结构

根据规范，创建以下目录和文件：

```
src/
├── views/afterSales/appointments/
│   ├── AppointmentsView.vue              # 主页面（原 AppointmentManagementView.vue）
│   └── components/
│       ├── AppointmentDetailDialog.vue   # 预约详情弹窗组件
│       └── QualityInspectionDialog.vue   # 环检单确认弹窗组件
├── api/modules/afterSales/
│   └── appointments.ts                   # 预约管理 API 模块
├── types/afterSales/
│   └── appointments.d.ts                 # 预约管理类型定义
├── mock/data/afterSales/
│   └── appointments.ts                   # 预约管理 Mock 数据
└── locales/modules/afterSales/
    ├── zh.json                           # 中文语言包
    └── en.json                           # 英文语言包
```

### 步骤 2：迁移和重构类型定义

将 `AppointmentManagementView.vue` 中的内联类型定义迁移到 `src/types/afterSales/appointments.d.ts`。

```typescript
// src/types/afterSales/appointments.d.ts

export interface MaintenanceItem {
  code: string;
  name: string;
  quantity: number;
  price: number;
}

export interface AppointmentListItem {
  id: string;
  licensePlate: string;
  reservationContactName: string;
  reservationContactPhone: string;
  serviceContactName: string;
  serviceContactPhone: string;
  appointmentTime: string;
  timeSlot: string;
  serviceType: 'maintenance' | 'repair';
  status: 'arrived' | 'not_arrived' | 'cancelled' | 'pending_payment';
  serviceAdvisor?: { id: string; name: string };
  qualityInspectionId?: string;
  createdAt: string;
  inspectionCreated?: boolean;
  customerDescription?: string;
  vin: string;
  model: string;
  variant: string;
  color: string;
  mileage?: number;
  productionDate?: string;
}

export interface AppointmentDetail extends AppointmentListItem {
  store: {
    id: string;
    name: string;
    address: string;
  };
  paymentStatus?: 'paid' | 'unpaid' | 'refunded';
  paymentAmount?: number;
  paymentOrderNumber?: string;
  maintenancePackage?: {
    items: MaintenanceItem[];
    totalAmount: number;
  };
}

export interface AppointmentListParams {
  appointmentId?: string;
  licensePlate?: string;
  reservationPhone?: string;
  servicePhone?: string;
  dateRange?: [string, string];
  status?: string;
  serviceType?: string;
  serviceAdvisorId?: string;
  technicianId?: string;
  page?: number;
  pageSize?: number;
}

export interface AppointmentPageResponse {
  list: AppointmentListItem[];
  total: number;
}
```

### 步骤 3：重构 Mock 数据

将 Mock 数据逻辑迁移到 `src/mock/data/afterSales/appointments.ts`，并实现分页和筛选。

```typescript
// src/mock/data/afterSales/appointments.ts

import type { AppointmentListParams, AppointmentPageResponse, AppointmentListItem } from '@/types/afterSales/appointments.d.ts';

// (此处省略 mock 数据生成逻辑，与原文件类似)
const mockAppointmentList: AppointmentListItem[] = [/* ... */];

export const getAppointmentList = (params: AppointmentListParams): Promise<AppointmentPageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockAppointmentList];
      // (此处省略筛选逻辑)

      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;

      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length,
      });
    }, 300);
  });
};
```

### 步骤 4：创建 API 模块

创建 `src/api/modules/afterSales/appointments.ts`，统一管理 API 请求。

```typescript
// src/api/modules/afterSales/appointments.ts

import request from '@/api';
import type { AppointmentListParams, AppointmentPageResponse } from '@/types/afterSales/appointments.d.ts';
import { getAppointmentList as getMockAppointmentList } from '@/mock/data/afterSales/appointments';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getAppointments = (params: AppointmentListParams): Promise<AppointmentPageResponse> => {
  if (USE_MOCK_API) {
    return getMockAppointmentList(params);
  }
  return request.get<any, AppointmentPageResponse>('/after-sales/appointments', { params });
};

// (其他 API，如 getAppointmentDetail)
```

### 步骤 5：更新国际化文件

在 `src/locales/modules/afterSales/zh.json` 和 `en.json` 中，将预约管理相关的翻译文本组织在 `appointments` 键下。

```json
// src/locales/modules/afterSales/zh.json
{
  "appointments": {
    "appointmentManagement": "预约管理",
    "labels": {
      "appointmentId": "预约单号"
      // ... 其他标签
    },
    "headers": {
      "appointmentId": "预约单号"
      // ... 其他表头
    }
    // ...
  }
}
```

### 步骤 6：重构主页面 `AppointmentsView.vue`

- **移动和重命名**：将 `AppointmentManagementView.vue` 移动到 `src/views/afterSales/appointments/` 并重命名为 `AppointmentsView.vue`。
- **移除弹窗逻辑**：将详情和环检单的弹窗逻辑和模板代码，分别移动到 `AppointmentDetailDialog.vue` 和 `QualityInspectionDialog.vue`。
- **更新数据获取**：使用新的 API 模块 `getAppointments` 获取数据。
- **更新类型引用**：从 ` '@/types/afterSales/appointments.d.ts'` 导入类型。
- **更新国际化引用**：使用 `useModuleI18n('afterSales.appointments')`。
- **引入子组件**：在模板中引入并使用 `<AppointmentDetailDialog />` 和 `<QualityInspectionDialog />`。

### 步骤 7：创建子组件

#### `AppointmentDetailDialog.vue`
- 接收 `v-model:visible` 和 `appointment-detail` 作为 props。
- 包含详情展示的模板和逻辑。

#### `QualityInspectionDialog.vue`
- 接收 `v-model:visible` 和 `appointment-data` 作为 props。
- 包含环检单确认的模板和逻辑。
- 发出 `confirm` 事件通知父组件执行后续操作。

### 步骤 8：更新路由

修改 `src/router/index.ts` 或相关路由模块，更新预约管理页面的路由配置。

```typescript
// src/router/index.ts
{
  path: '/after-sales/appointments',
  name: 'Appointments',
  component: () => import('@/views/afterSales/appointments/AppointmentsView.vue'),
  meta: {
    title: 'menu.appointments', // 对应国际化键
    requiresAuth: true,
    icon: 'Calendar'
  }
}
```

## 4. 预期收益

- **结构清晰**：项目结构符合团队规范，新成员更容易上手。
- **职责明确**：每个文件和组件的职责更加单一，便于理解和修改。
- **易于维护**：修改或扩展功能时，只需关注相关模块，降低了代码耦合带来的风险。
- **提升开发效率**：模块化和组件化使得代码复用更加方便。
