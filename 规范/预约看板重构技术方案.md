# 预约看板页面重构技术方案

本方案旨在根据《页面目录结构规范》和《页面重构技术规范》对 `AppointmentDashboardView.vue` 进行重构，提升代码的模块化、可维护性和可读性。

## 1. 重构目标

- **遵循目录规范**：将页面和相关文件迁移到 `src/views/afterSales/dashboard/` 目录下。
- **模块化拆分**：将类型定义、API 请求、Mock 数据和子组件进行分离。
- **提升代码质量**：移除内联类型和数据，使用统一的 API 和类型定义。
- **增强可维护性**：通过组件化和模块化，降低单个文件的复杂性。

## 2. 重构分析

`AppointmentDashboardView.vue` 当前存在以下问题：

- **文件位置不规范**：直接位于 `src/views/`，未按 `afterSales` 模块进行组织。
- **内联数据耦合**：包含内联的 Mock 数据，应提取到独立的 Mock 文件。
- **缺少类型定义**：没有明确的 TypeScript 类型定义，应提取到独立的 `.d.ts` 文件。
- **API 模块缺失**：直接使用内联数据，未通过统一的 API 模块管理。

## 3. 重构步骤

### 步骤 1：创建目录和文件结构

根据规范，创建以下目录和文件：

```
src/
├── views/afterSales/dashboard/
│   ├── DashboardView.vue               # 主页面（原 AppointmentDashboardView.vue）
│   └── components/
│       ├── StatsCards.vue              # 统计卡片组件
│       ├── FilterButtons.vue           # 筛选按钮组件
│       └── AppointmentTable.vue        # 预约表格组件
├── api/modules/afterSales/
│   └── dashboard.ts                    # 预约看板 API 模块
├── types/afterSales/
│   └── dashboard.d.ts                  # 预约看板类型定义
├── mock/data/afterSales/
│   └── dashboard.ts                    # 预约看板 Mock 数据
└── locales/modules/afterSales/
    ├── zh.json                         # 中文语言包（已存在，需更新）
    └── en.json                         # 英文语言包（已存在，需更新）
```

### 步骤 2：迁移和重构类型定义

创建 `src/types/afterSales/dashboard.d.ts`，定义看板相关的类型：

```typescript
// src/types/afterSales/dashboard.d.ts

export interface DashboardAppointmentItem {
  plateNumber: string;
  appointmentDate: string;
  timeSlot: string;
  serviceType: 'maintenance' | 'repair';
  status: 'arrived' | 'notArrived' | 'notFulfilled';
}

export interface DashboardStats {
  totalAppointments: number;
  arrivedCount: number;
  notArrivedCount: number;
  notFulfilledCount: number;
  arrivalRate: number;
  tomorrowCount: number;
}

export interface DashboardSearchParams {
  date?: string;
  filterType?: 'all' | 'notArrived' | 'tomorrow';
}

export interface DashboardResponse {
  appointments: DashboardAppointmentItem[];
  stats: DashboardStats;
}
```

### 步骤 3：重构 Mock 数据

将 Mock 数据逻辑迁移到 `src/mock/data/afterSales/dashboard.ts`：

```typescript
// src/mock/data/afterSales/dashboard.ts

import type { 
  DashboardSearchParams, 
  DashboardResponse, 
  DashboardAppointmentItem,
  DashboardStats 
} from '@/types/afterSales/dashboard.d.ts';

// 生成动态 Mock 数据
function generateMockAppointments(): DashboardAppointmentItem[] {
  const plateNumbers = ['粤A12345', '京B67890', '沪C11111', '深D22222', '浙E33333', '苏F44444', '川G55555', '鲁H66666'];
  const serviceTypes: ('maintenance' | 'repair')[] = ['maintenance', 'repair'];
  const statuses: ('arrived' | 'notArrived' | 'notFulfilled')[] = ['arrived', 'notArrived', 'notFulfilled'];
  
  const appointments: DashboardAppointmentItem[] = [];
  const today = new Date().toISOString().split('T')[0];
  const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  
  // 生成今日预约数据
  for (let i = 0; i < 8; i++) {
    const hour = 9 + i;
    appointments.push({
      plateNumber: plateNumbers[i % plateNumbers.length],
      appointmentDate: today,
      timeSlot: `${hour.toString().padStart(2, '0')}:00-${(hour + 1).toString().padStart(2, '0')}:00`,
      serviceType: serviceTypes[Math.floor(Math.random() * serviceTypes.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)]
    });
  }
  
  // 生成明日预约数据
  for (let i = 0; i < 5; i++) {
    const hour = 9 + i;
    appointments.push({
      plateNumber: plateNumbers[(i + 3) % plateNumbers.length],
      appointmentDate: tomorrow,
      timeSlot: `${hour.toString().padStart(2, '0')}:00-${(hour + 1).toString().padStart(2, '0')}:00`,
      serviceType: serviceTypes[Math.floor(Math.random() * serviceTypes.length)],
      status: 'notArrived' // 明日预约默认未到店
    });
  }
  
  return appointments;
}

const mockAppointments = generateMockAppointments();

export const getDashboardData = (params: DashboardSearchParams): Promise<DashboardResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const today = new Date().toISOString().split('T')[0];
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      let filteredAppointments = [...mockAppointments];
      
      // 根据筛选类型过滤数据
      switch (params.filterType) {
        case 'notArrived':
          filteredAppointments = filteredAppointments.filter(item =>
            item.appointmentDate === today && item.status === 'notArrived'
          );
          break;
        case 'tomorrow':
          filteredAppointments = filteredAppointments.filter(item => 
            item.appointmentDate === tomorrow
          );
          break;
        default:
          filteredAppointments = filteredAppointments.filter(item => 
            item.appointmentDate === today
          );
      }
      
      // 计算统计数据
      const todayAppointments = mockAppointments.filter(item => item.appointmentDate === today);
      const totalAppointments = todayAppointments.length;
      const arrivedCount = todayAppointments.filter(item => item.status === 'arrived').length;
      const notArrivedCount = todayAppointments.filter(item => item.status === 'notArrived').length;
      const notFulfilledCount = todayAppointments.filter(item => item.status === 'notFulfilled').length;
      const arrivalRate = totalAppointments > 0 ? Math.round((arrivedCount / totalAppointments) * 100) : 0;
      const tomorrowCount = mockAppointments.filter(item => item.appointmentDate === tomorrow).length;
      
      const stats: DashboardStats = {
        totalAppointments,
        arrivedCount,
        notArrivedCount,
        notFulfilledCount,
        arrivalRate,
        tomorrowCount
      };
      
      resolve({
        appointments: filteredAppointments,
        stats
      });
    }, 300);
  });
};
```

### 步骤 4：创建 API 模块

创建 `src/api/modules/afterSales/dashboard.ts`，统一管理 API 请求：

```typescript
// src/api/modules/afterSales/dashboard.ts

import request from '@/api';
import type { DashboardSearchParams, DashboardResponse } from '@/types/afterSales/dashboard.d.ts';
import { getDashboardData as getMockDashboardData } from '@/mock/data/afterSales/dashboard';
import { USE_MOCK_API } from '@/utils/mock-config';

export const getDashboardData = (params: DashboardSearchParams): Promise<DashboardResponse> => {
  if (USE_MOCK_API) {
    return getMockDashboardData(params);
  }
  return request.get<any, DashboardResponse>('/after-sales/dashboard', { params });
};
```

### 步骤 5：更新国际化文件

在现有的 `src/locales/modules/afterSales/zh.json` 和 `en.json` 中，将预约看板相关的翻译组织在 `dashboard` 键下：

```json
// src/locales/modules/afterSales/zh.json (新增部分)
{
  "dashboard": {
    "pageTitle": "预约看板",
    "stats": {
      "totalAppointments": "今日预约总数",
      "arrivedCount": "已到店数量",
      "notArrivedCount": "未到店数量",
      "arrivalRate": "到店率"
    },
    "filters": {
      "all": "全部",
      "notArrived": "未到店",
      "tomorrow": "明日预约"
    },
    "table": {
      "plateNumber": "车牌号",
      "appointmentDate": "预约日期",
      "timeSlot": "时间段",
      "serviceType": "服务类型",
      "status": "状态"
    },
    "serviceTypes": {
      "maintenance": "保养",
      "repair": "维修"
    },
    "status": {
      "arrived": "已到店",
      "notArrived": "未到店",
      "notFulfilled": "未履约"
    },
    "emptyState": "暂无预约数据"
  }
}
```

### 步骤 6：创建子组件

#### `StatsCards.vue` - 统计卡片组件
```vue
<!-- src/views/afterSales/dashboard/components/StatsCards.vue -->
<script setup lang="ts">
import { ElCard, ElCol, ElRow } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { DashboardStats } from '@/types/afterSales/dashboard.d.ts';

interface Props {
  stats: DashboardStats;
}

defineProps<Props>();

const { t } = useModuleI18n('afterSales.dashboard');
</script>

<template>
  <el-row :gutter="20" class="stats-cards">
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-text">
            <div class="stat-label">{{ t('stats.totalAppointments') }}</div>
            <div class="stat-value">{{ stats.totalAppointments }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-text">
            <div class="stat-label">{{ t('stats.arrivedCount') }}</div>
            <div class="stat-value">{{ stats.arrivedCount }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-text">
            <div class="stat-label">{{ t('stats.notArrivedCount') }}</div>
            <div class="stat-value">{{ stats.notArrivedCount }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-text">
            <div class="stat-label">{{ t('stats.arrivalRate') }}</div>
            <div class="stat-value">{{ stats.arrivalRate }}%</div>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss">
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;

  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: box-shadow 0.3s ease;
  }
}

.stat-content {
  padding: 20px;
}

.stat-label {
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #333;
}
</style>
```

#### `FilterButtons.vue` - 筛选按钮组件
```vue
<!-- src/views/afterSales/dashboard/components/FilterButtons.vue -->
<script setup lang="ts">
import { ElButton, ElRow } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';

interface Props {
  activeFilter: string;
}

interface Emits {
  (e: 'filter-change', filterType: string): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const { t } = useModuleI18n('afterSales.dashboard');

const handleFilterChange = (filterType: string) => {
  emit('filter-change', filterType);
};
</script>

<template>
  <el-row class="mb-20">
    <el-button
      :type="activeFilter === 'all' ? 'primary' : 'default'"
      @click="handleFilterChange('all')"
    >
      {{ t('filters.all') }}
    </el-button>
    <el-button
      :type="activeFilter === 'notArrived' ? 'primary' : 'default'"
      @click="handleFilterChange('notArrived')"
    >
      {{ t('filters.notArrived') }}
    </el-button>
    <el-button
      :type="activeFilter === 'tomorrow' ? 'primary' : 'default'"
      @click="handleFilterChange('tomorrow')"
    >
      {{ t('filters.tomorrow') }}
    </el-button>
  </el-row>
</template>

<style scoped lang="scss">
.mb-20 {
  margin-bottom: 20px;
}

.el-button {
  margin-right: 12px;

  &:last-child {
    margin-right: 0;
  }
}
</style>
```

#### `AppointmentTable.vue` - 预约表格组件
```vue
<!-- src/views/afterSales/dashboard/components/AppointmentTable.vue -->
<script setup lang="ts">
import { ElCard, ElTable, ElTableColumn, ElTag } from 'element-plus';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { DashboardAppointmentItem } from '@/types/afterSales/dashboard.d.ts';

interface Props {
  appointments: DashboardAppointmentItem[];
}

defineProps<Props>();

const { t } = useModuleI18n('afterSales.dashboard');

// 获取服务类型标签样式
const getServiceTypeTag = (serviceType: string) => {
  return serviceType === 'maintenance' ? 'success' : 'warning';
};

// 获取服务类型显示文本
const getServiceTypeText = (serviceType: string) => {
  return t(`serviceTypes.${serviceType}`);
};

// 获取状态标签样式
const getStatusTag = (status: string) => {
  switch (status) {
    case 'arrived':
      return 'success';
    case 'notArrived':
      return 'info';
    case 'notFulfilled':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取状态显示文本
const getStatusText = (status: string) => {
  return t(`status.${status}`);
};
</script>

<template>
  <el-card class="table-card">
    <el-table :data="appointments" style="width: 100%" stripe>
      <el-table-column prop="plateNumber" :label="t('table.plateNumber')" />
      <el-table-column prop="appointmentDate" :label="t('table.appointmentDate')" />
      <el-table-column prop="timeSlot" :label="t('table.timeSlot')" />
      <el-table-column prop="serviceType" :label="t('table.serviceType')">
        <template #default="{ row }">
          <el-tag :type="getServiceTypeTag(row.serviceType)">
            {{ getServiceTypeText(row.serviceType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" :label="t('table.status')">
        <template #default="{ row }">
          <el-tag :type="getStatusTag(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!-- Empty State -->
    <div v-if="appointments.length === 0" class="empty-state">
      <p>{{ t('emptyState') }}</p>
    </div>
  </el-card>
</template>

<style scoped lang="scss">
.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}
</style>
```

### 步骤 7：重构主页面 `DashboardView.vue`

- **移动和重命名**：将 `AppointmentDashboardView.vue` 移动到 `src/views/afterSales/dashboard/` 并重命名为 `DashboardView.vue`。
- **移除内联数据**：将 Mock 数据和统计逻辑移动到 API 模块。
- **更新数据获取**：使用新的 API 模块 `getDashboardData` 获取数据。
- **更新类型引用**：从 `@/types/afterSales/dashboard.d.ts` 导入类型。
- **更新国际化引用**：使用 `useModuleI18n('afterSales.dashboard')`。
- **引入子组件**：在模板中引入并使用子组件。

```vue
<!-- src/views/afterSales/dashboard/DashboardView.vue -->
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { getDashboardData } from '@/api/modules/afterSales/dashboard';
import type { DashboardAppointmentItem, DashboardStats } from '@/types/afterSales/dashboard.d.ts';

// 导入子组件
import StatsCards from './components/StatsCards.vue';
import FilterButtons from './components/FilterButtons.vue';
import AppointmentTable from './components/AppointmentTable.vue';

const { t } = useModuleI18n('afterSales.dashboard');

// 响应式数据
const loading = ref(false);
const activeFilter = ref('all');
const appointments = ref<DashboardAppointmentItem[]>([]);
const stats = ref<DashboardStats>({
  totalAppointments: 0,
  arrivedCount: 0,
  notArrivedCount: 0,
  notFulfilledCount: 0,
  arrivalRate: 0,
  tomorrowCount: 0
});

// 获取看板数据
const fetchDashboardData = async () => {
  loading.value = true;
  try {
    const response = await getDashboardData({
      filterType: activeFilter.value as 'all' | 'notArrived' | 'tomorrow'
    });
    appointments.value = response.appointments;
    stats.value = response.stats;
  } catch (error) {
    console.error('获取看板数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理筛选变化
const handleFilterChange = (filterType: string) => {
  activeFilter.value = filterType;
  fetchDashboardData();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDashboardData();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('pageTitle') }}</h1>

    <!-- 统计卡片 -->
    <StatsCards :stats="stats" />

    <!-- 筛选按钮 -->
    <FilterButtons
      :active-filter="activeFilter"
      @filter-change="handleFilterChange"
    />

    <!-- 预约表格 -->
    <AppointmentTable :appointments="appointments" />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: 600;
}
</style>
```

### 步骤 8：更新路由

修改 `src/router/index.ts` 或相关路由模块，更新预约看板页面的路由配置：

```typescript
// src/router/index.ts
{
  path: '/after-sales/dashboard',
  name: 'AppointmentDashboard',
  component: () => import('@/views/afterSales/dashboard/DashboardView.vue'),
  meta: {
    title: 'menu.appointmentDashboard',
    requiresAuth: true,
    icon: 'Monitor'
  }
}
```

## 4. 预期收益

- **结构清晰**：项目结构符合团队规范，新成员更容易上手。
- **职责明确**：每个文件和组件的职责更加单一，便于理解和修改。
- **易于维护**：修改或扩展功能时，只需关注相关模块，降低了代码耦合带来的风险。
- **提升开发效率**：模块化和组件化使得代码复用更加方便。
- **数据动态化**：Mock 数据支持动态生成，便于测试不同场景。
- **类型安全**：完整的 TypeScript 类型定义，提高代码质量和开发体验。

## 5. 重构检查清单

### 5.1 目录结构验证
- [ ] 页面文件移动到 `src/views/afterSales/dashboard/DashboardView.vue`
- [ ] API模块创建在 `src/api/modules/afterSales/dashboard.ts`
- [ ] Mock数据创建在 `src/mock/data/afterSales/dashboard.ts`
- [ ] 类型定义创建在 `src/types/afterSales/dashboard.d.ts`
- [ ] 子组件创建在 `src/views/afterSales/dashboard/components/`

### 5.2 代码质量验证
- [ ] 移除页面中的内联数据和统计逻辑
- [ ] 使用统一的API调用替换内联Mock数据
- [ ] TypeScript类型安全，无编译错误
- [ ] 国际化完整覆盖，无硬编码文本
- [ ] Mock数据功能完整，支持动态生成和筛选

### 5.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 统计卡片数据正确显示
- [ ] 筛选功能正常工作
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

### 5.4 路由验证
- [ ] 路由路径更新为新的模块化路径
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 6. 实施建议

### 6.1 实施顺序
1. **创建基础结构**：先创建目录和类型定义
2. **重构数据层**：创建Mock数据和API模块
3. **拆分组件**：创建子组件并测试
4. **重构主页面**：更新主页面逻辑
5. **更新配置**：修改路由和国际化
6. **测试验证**：全面测试功能正常性

### 6.2 风险控制
- **渐进式重构**：一次只重构一个模块，避免大范围影响
- **保持功能不变**：重构过程中不改变业务逻辑和用户体验
- **及时测试**：每个步骤完成后及时验证功能正常
- **代码备份**：重构前备份原始代码

---

**本技术方案基于页面目录结构规范和页面重构技术规范制定，为预约看板页面的重构提供详细的实施指导。**
