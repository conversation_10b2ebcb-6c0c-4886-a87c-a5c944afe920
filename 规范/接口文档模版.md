# [模块名称]页面接口文档

## 📋 接口定义

### 1. [功能名称1]

**接口地址：** `/api/[模块]/[功能]`  
**请求方式：** `POST`  
**接口描述：** [功能说明]

#### 请求参数

```json
{
  "参数名1": "参数值示例",
  "参数名2": 123,
  "参数名3": true
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 参数名1 | string | 是 | 参数说明 |
| 参数名2 | integer | 否 | 参数说明，默认值xxx |
| 参数名3 | boolean | 否 | 参数说明，默认false |

#### 响应结果

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "字段1": "值1",
    "字段2": 123,
    "字段3": {
      "子字段1": "子值1",
      "子字段2": 456
    },
    "字段4": [
      {
        "数组项字段1": "值",
        "数组项字段2": 789
      }
    ]
  },
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalCount": 100,
    "totalPages": 5
  }
}
```

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 字段1 | string | 字段说明 |
| 字段2 | integer | 字段说明 |
| 字段3.子字段1 | string | 子字段说明 |
| 字段3.子字段2 | integer | 子字段说明 |
| 字段4[].数组项字段1 | string | 数组项字段说明 |
| 字段4[].数组项字段2 | integer | 数组项字段说明 |

---

### 2. [功能名称2]

**接口地址：** `/api/[模块]/[功能]`  
**请求方式：** `GET`  
**接口描述：** [功能说明]

#### 请求参数

```json
{
  "参数名": "参数值"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 参数名 | string | 是 | 参数说明 |

#### 响应结果

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "字段名": "字段值"
  }
}
```

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 字段名 | string | 字段说明 |

---

### 3. [功能名称3] - 批量操作

**接口地址：** `/api/[模块]/batch-[操作]`  
**请求方式：** `POST`  
**接口描述：** [批量操作功能说明]

#### 请求参数

```json
{
  "操作项目": [
    {
      "id": 1,
      "操作参数": "值"
    },
    {
      "id": 2,
      "操作参数": "值"
    }
  ],
  "操作人": "操作人姓名",
  "备注": "操作备注"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 操作项目 | array | 是 | 批量操作项目列表 |
| 操作项目[].id | integer | 是 | 操作对象ID |
| 操作项目[].操作参数 | string | 是 | 操作相关参数 |
| 操作人 | string | 是 | 执行操作的用户 |
| 备注 | string | 否 | 操作备注说明 |

#### 响应结果

```json
{
  "code": 200,
  "message": "批量操作成功",
  "data": {
    "操作单号": "BATCH202401150001",
    "成功数量": 2,
    "失败数量": 0,
    "操作详情": [
      {
        "id": 1,
        "status": "SUCCESS",
        "message": "操作成功"
      },
      {
        "id": 2,
        "status": "SUCCESS", 
        "message": "操作成功"
      }
    ]
  }
}
```

---

## 🎯 页面交互维度核心功能点

### 1. 🔍 [搜索筛选功能名称]

**功能描述：** [功能作用和价值说明]

- **搜索功能：** [具体搜索功能说明]
- **筛选功能：** [具体筛选功能说明]
- **重置功能：** [重置功能说明]

**交互特点：**
  - [交互方式1]
  - [交互方式2]
  - [用户体验特点]

### 2. 📊 [数据展示功能名称]

**功能描述：** [功能作用和价值说明]

- **数据监控：** [监控指标说明]
- **可视化展示：** [可视化方式说明]
- **趋势分析：** [趋势分析功能说明]

**交互特点：**
  - [展示方式1]
  - [展示方式2]
  - [交互体验特点]

### 3. 🔧 [操作工具功能名称]

**功能描述：** [功能作用和价值说明]

- **单项操作：** [单项操作功能说明]
- **批量操作：** [批量操作功能说明]
- **快捷操作：** [快捷操作功能说明]

**交互特点：**
  - [操作方式1]
  - [操作方式2]
  - [权限控制说明]

### 4. 📱 [弹窗交互功能名称]

#### [子功能1]弹窗

**功能描述：** [弹窗功能说明]

- **信息展示：** [展示内容说明]
- **操作功能：** [操作功能说明]
- **数据验证：** [数据验证规则]

**交互特点：**
  - [弹窗交互方式1]
  - [弹窗交互方式2]
  - [用户体验设计]

#### [子功能2]弹窗

**功能描述：** [弹窗功能说明]

- **表单输入：** [表单字段说明]
- **业务规则：** [业务规则说明]
- **操作确认：** [确认机制说明]

**交互特点：**
  - [表单交互特点]
  - [验证反馈机制]
  - [安全确认流程]

### 5. ⚡ [实时响应功能名称]

**功能描述：** [实时响应功能说明]

- **状态计算：** [状态计算规则]
- **即时反馈：** [反馈机制说明]
- **性能优化：** [性能优化措施]

**交互特点：**
  - [实时更新机制]
  - [性能表现特点]
  - [异常处理方式]

---

## 🔧 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 📝 [业务状态]枚举

| 状态值 | 中文名称 | 说明 |
|--------|----------|------|
| STATUS1 | 状态1 | 状态说明1 |
| STATUS2 | 状态2 | 状态说明2 |
| STATUS3 | 状态3 | 状态说明3 |

## 🔄 [操作类型]枚举

| 类型值 | 中文名称 | 说明 |
|--------|----------|------|
| TYPE1 | 操作类型1 | 操作说明1 |
| TYPE2 | 操作类型2 | 操作说明2 |
| TYPE3 | 操作类型3 | 操作说明3 |

## 📋 [分类枚举]枚举

| 枚举值 | 中文名称 | 说明 |
|--------|----------|------|
| CATEGORY1 | 分类1 | 分类说明1 |
| CATEGORY2 | 分类2 | 分类说明2 |
| CATEGORY3 | 分类3 | 分类说明3 |

---

## 📌 使用说明

### 编写指导
1. **替换占位符**：将所有 `[占位符]` 内容替换为实际的业务内容
2. **删除无用章节**：根据实际需要删除不适用的功能模块
3. **补充业务细节**：根据具体业务需求补充详细的字段说明
4. **调整接口数量**：根据实际接口数量调整模版内容

### 检查清单
- [ ] 所有占位符已替换为实际内容
- [ ] 接口地址格式正确
- [ ] 参数和响应字段说明完整
- [ ] 枚举值定义准确
- [ ] 交互功能描述清晰
- [ ] 文档格式符合规范 