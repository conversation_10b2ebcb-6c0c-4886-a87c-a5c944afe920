# 模态框样式规范

## 1. 模态框容器样式

### 1.1 模态框组件
- **组件**: `el-dialog`
- **宽度**: 900px
- **标题**: 动态显示发票详情标题
- **关闭方式**: `destroy-on-close="true"`
- **蒙版**: 启用背景蒙版

### 1.2 蒙版样式
- **背景色**: rgba(0, 0, 0, 0.5) (半透明黑色)
- **层级**: z-index 高于页面内容
- **点击关闭**: 支持点击蒙版关闭模态框
- **过渡效果**: 淡入淡出动画

### 1.3 模态框主体
- **背景色**: #ffffff (白色)
- **圆角**: 8px
- **阴影**: `0 4px 20px rgba(0, 0, 0, 0.15)`
- **最大高度**: 80vh
- **垂直居中**: 屏幕垂直居中显示

## 2. 模态框头部样式

### 2.1 标题区域
- **字体大小**: 18px
- **字体粗细**: 600 (semi-bold)
- **颜色**: #303133 (深灰色)
- **内边距**: 20px 20px 15px 20px
- **边框底部**: 1px solid #ebeef5

### 2.2 关闭按钮
- **位置**: 右上角
- **尺寸**: 24px × 24px
- **颜色**: #909399
- **悬停颜色**: #f56c6c
- **背景**: 透明

## 3. 模态框内容区域样式

### 3.1 内容容器
- **类名**: `invoice-detail`
- **内边距**: 20px
- **最大高度**: calc(80vh - 120px)
- **滚动**: 垂直滚动条（如需要）

### 3.2 信息区块样式
- **类名**: `detail-section`
- **下边距**: 30px
- **边框**: 无
- **背景**: 透明

## 4. 信息区块标题样式

### 4.1 主标题样式
- **标签**: `h3`
- **字体大小**: 16px
- **字体粗细**: 600 (semi-bold)
- **颜色**: #333333
- **下边距**: 15px
- **下边框**: 2px solid #e8e8e8
- **内边距底部**: 8px

### 4.2 副标题样式
- **标签**: `h4`
- **字体大小**: 14px
- **字体粗细**: 500 (medium)
- **颜色**: #555555
- **上边距**: 20px
- **下边距**: 10px

## 5. 信息项样式规范

### 5.1 信息项容器
- **类名**: `detail-item`
- **下边距**: 12px
- **显示方式**: 块级元素
- **对齐方式**: 左对齐

### 5.2 标签样式
- **标签**: `label`
- **显示方式**: inline-block
- **最小宽度**: 80px
- **字体粗细**: 600 (semi-bold)
- **颜色**: #666666
- **右边距**: 8px

### 5.3 值样式
- **标签**: `span`
- **颜色**: #333333
- **字体粗细**: 400 (normal)
- **换行**: 允许换行

## 6. 栅格布局样式

### 6.1 行容器
- **组件**: `el-row`
- **列间距**: `:gutter="16"`
- **下边距**: 根据内容调整

### 6.2 列容器
- **组件**: `el-col`
- **栅格**: `:span="6"` (4列布局)
- **响应式**: 小屏幕自动调整

## 7. 表格样式规范

### 7.1 表格容器
- **组件**: `el-table`
- **边框**: `border` 属性启用
- **宽度**: 100%
- **下边距**: 20px

### 7.2 表格列样式
- **最小宽度**: 根据内容设置
- **对齐方式**: 左对齐（默认），金额右对齐
- **文本溢出**: 显示省略号

### 7.3 金额列样式
- **对齐方式**: `align="right"`
- **前缀**: "RM " 货币符号
- **字体**: 等宽字体（数字）
- **颜色**: #333333

## 8. 特殊样式处理

### 8.1 右对齐样式
- **类名**: `text-right`
- **文本对齐**: `text-align: right`
- **用途**: 金额汇总行

### 8.2 金额显示
- **格式**: "RM 金额"
- **条件显示**: 大于0时显示金额，否则显示"-"
- **字体粗细**: 600 (semi-bold) 用于重要金额

## 9. 可编辑模式样式规范


### 9.2 表单容器样式
- **组件**: `el-form`
- **类名**: `invoice-edit-form`
- **标签位置**: `label-position="top"`
- **标签宽度**: 自适应
- **表单验证**: 启用实时验证

### 9.3 表单项样式
- **组件**: `el-form-item`
- **下边距**: 20px
- **标签样式**:
  - 字体大小: 14px
  - 字体粗细: 500 (medium)
  - 颜色: #606266
  - 下边距: 8px

### 9.4 输入控件样式

#### 9.4.1 文本输入框
- **组件**: `el-input`
- **高度**: 40px
- **边框**: 1px solid #dcdfe6
- **圆角**: 4px
- **内边距**: 12px 15px
- **字体大小**: 14px
- **占位符颜色**: #c0c4cc

#### 9.4.2 数字输入框
- **组件**: `el-input-number`
- **高度**: 40px
- **宽度**: 100%
- **精度**: 2位小数
- **最小值**: 0
- **控制按钮**: 右侧显示

#### 9.4.3 下拉选择框
- **组件**: `el-select`
- **高度**: 40px
- **宽度**: 100%
- **占位符**: 动态显示
- **清空按钮**: 启用

#### 9.4.4 日期选择器
- **组件**: `el-date-picker`
- **类型**: `type="datetime"`
- **格式**: YYYY-MM-DD HH:mm:ss
- **宽度**: 100%
- **占位符**: 请选择日期时间

#### 9.4.5 文本域
- **组件**: `el-input`
- **类型**: `type="textarea"`
- **行数**: `:rows="3"`
- **自动调整**: `autosize`
- **最大长度**: 500字符

### 9.5 表单验证样式
- **必填标识**: 红色星号 (*)
- **错误提示**: 红色文字，字体大小12px
- **错误边框**: 1px solid #f56c6c
- **成功边框**: 1px solid #67c23a

### 9.6 编辑状态指示
- **编辑中标识**: 表单项左侧蓝色竖线
- **未保存提示**: 页面顶部黄色提示条
- **保存状态**: 实时显示保存状态

### 9.7 操作按钮区域
- **容器类名**: `edit-actions`
- **位置**: 模态框底部
- **对齐方式**: 右对齐
- **按钮间距**: 12px
- **边框顶部**: 1px solid #ebeef5
- **内边距**: 20px

### 9.8 按钮样式规范
- **保存按钮**: `type="primary"`, 蓝色背景
- **取消按钮**: `type="default"`, 灰色边框
- **重置按钮**: `type="warning"`, 橙色背景
- **按钮高度**: 36px
- **按钮内边距**: 12px 20px

## 10. CSS样式定义

### 10.1 模态框基础样式
```scss
:deep(.el-dialog) {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  
  .el-dialog__header {
    padding: 20px 20px 15px 20px;
    border-bottom: 1px solid #ebeef5;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
    max-height: calc(80vh - 120px);
    overflow-y: auto;
  }
}

:deep(.el-dialog__wrapper) {
  background-color: rgba(0, 0, 0, 0.5);
}
```

### 10.2 内容样式
```scss
.invoice-detail {
  .detail-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e8e8e8;
      color: #333333;
      font-size: 16px;
      font-weight: 600;
    }

    h4 {
      margin-top: 20px;
      margin-bottom: 10px;
      color: #555555;
      font-size: 14px;
      font-weight: 500;
    }

    .detail-item {
      margin-bottom: 12px;

      label {
        display: inline-block;
        min-width: 80px;
        font-weight: 600;
        color: #666666;
        margin-right: 8px;
      }

      span {
        color: #333333;
        font-weight: 400;
      }
    }

    .text-right {
      text-align: right;
      
      label {
        font-weight: 600;
      }
      
      span {
        font-weight: 600;
        color: #333333;
      }
    }
  }
}
```

### 10.3 可编辑表单样式
```scss
.invoice-edit-form {
  .el-form-item {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-size: 14px;
      font-weight: 500;
      color: #606266;
      margin-bottom: 8px;
      line-height: 1.4;
      
      &::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }
    
    .el-form-item__content {
      .el-input,
      .el-select,
      .el-date-editor {
        width: 100%;
        
        .el-input__inner,
        .el-select__input {
          height: 40px;
          line-height: 40px;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          padding: 0 15px;
          font-size: 14px;
          
          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
          
          &::placeholder {
            color: #c0c4cc;
          }
        }
      }
      
      .el-textarea {
        .el-textarea__inner {
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          padding: 12px 15px;
          font-size: 14px;
          line-height: 1.5;
          resize: vertical;
          
          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }
      
      .el-input-number {
        width: 100%;
        
        .el-input__inner {
          text-align: left;
        }
      }
    }
    
    // 验证错误状态
    &.is-error {
      .el-input__inner,
      .el-textarea__inner,
      .el-select .el-input__inner {
        border-color: #f56c6c;
      }
    }
    
    // 验证成功状态
    &.is-success {
      .el-input__inner,
      .el-textarea__inner,
      .el-select .el-input__inner {
        border-color: #67c23a;
      }
    }
  }
  
  // 编辑状态指示
  &.edit-mode {
    .el-form-item {
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: -20px;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: #409eff;
        border-radius: 2px;
      }
    }
  }
}
```

### 10.4 编辑操作区域样式
```scss
.edit-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  padding: 20px 0 0 0;
  border-top: 1px solid #ebeef5;
  margin-top: 30px;
  
  .el-button {
    height: 36px;
    padding: 0 20px;
    font-size: 14px;
    border-radius: 4px;
    
    &.el-button--primary {
      background-color: #409eff;
      border-color: #409eff;
      
      &:hover {
        background-color: #66b1ff;
        border-color: #66b1ff;
      }
    }
    
    &.el-button--default {
      color: #606266;
      border-color: #dcdfe6;
      
      &:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
    }
    
    &.el-button--warning {
      background-color: #e6a23c;
      border-color: #e6a23c;
      
      &:hover {
        background-color: #ebb563;
        border-color: #ebb563;
      }
    }
  }
}
```

### 10.5 未保存提示样式
```scss
.unsaved-warning {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fdf6ec;
  border-bottom: 1px solid #f5dab1;
  color: #e6a23c;
  padding: 12px 20px;
  text-align: center;
  font-size: 14px;
  z-index: 3000;
  
  .warning-icon {
    margin-right: 8px;
  }
  
  .warning-text {
    font-weight: 500;
  }
}
```

### 10.6 表格样式
```scss
:deep(.el-table) {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  
  .el-table__header th {
    background-color: #fafafa;
    color: #303133;
    font-weight: 500;
  }
  
  .el-table__body td {
    padding: 8px 12px;
  }
  
  // 金额列样式
  .amount-column {
    text-align: right;
    font-family: 'Courier New', monospace;
    
    .currency-prefix {
      margin-right: 4px;
    }
  }
}
```

## 11. 响应式设计

### 11.1 模态框自适应
- **大屏幕**: 900px 固定宽度，90vh最大高度
- **中等屏幕**: 80% 宽度，90vh最大高度
- **小屏幕**: 95% 宽度，90vh最大高度
- **超小屏幕**: 100% 宽度，100vh高度（全屏显示）

### 11.2 高度控制规范
- **模态框最大高度**: 90vh
- **上下边距**: 5vh（确保不超出视口）
- **头部固定高度**: 60px
- **底部固定高度**: 80px（编辑模式）
- **内容区域高度**: calc(90vh - 140px)

### 11.3 滚动处理
- **模态框本身**: 不滚动，固定在视口中央
- **内容区域**: 独立滚动，自定义滚动条样式
- **滚动行为**: 平滑滚动
- **滚动指示**: 渐变阴影提示更多内容

### 11.4 响应式CSS
```scss
// 响应式高度控制
@media (max-height: 600px) {
  :deep(.el-dialog) {
    max-height: 95vh;
    margin: 2.5vh auto;
    
    .invoice-detail {
      max-height: calc(95vh - 140px);
    }
  }
}

@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 100% !important;
    height: 100vh !important;
    max-height: 100vh;
    margin: 0;
    border-radius: 0;
    
    .invoice-detail {
      max-height: calc(100vh - 140px);
    }
  }
  
  :deep(.el-dialog__wrapper) {
    padding: 0;
  }
}
```

### 11.5 栅格响应式
- **大屏幕**: 4列布局 (span="6")
- **中等屏幕**: 2列布局 (span="12")
- **小屏幕**: 1列布局 (span="24")

### 11.6 编辑表单响应式
- **小屏幕**: 表单项全宽显示
- **按钮区域**: 小屏幕下保持水平排列
- **输入框**: 保持最小触摸区域44px
- **底部操作区**: 始终固定在底部

## 12. 视觉层次规范

### 12.1 信息层次
- **一级信息**: 区块标题，16px，粗体
- **二级信息**: 子标题，14px，中等粗细
- **三级信息**: 标签，14px，中等粗细
- **四级信息**: 内容，14px，常规

### 12.2 颜色层次
- **主要文字**: #333333 (深灰色)
- **标签文字**: #606266 (中灰色)
- **辅助文字**: #909399 (浅灰色)
- **边框颜色**: #dcdfe6 (浅灰色)
- **错误颜色**: #f56c6c (红色)
- **成功颜色**: #67c23a (绿色)

## 13. 交互效果

### 13.1 模态框动画
- **打开动画**: 淡入 + 缩放
- **关闭动画**: 淡出 + 缩放
- **持续时间**: 300ms
- **缓动函数**: ease-out

### 13.2 表单交互效果
- **焦点效果**: 边框颜色变化 + 阴影
- **悬停效果**: 边框颜色轻微变化
- **输入动画**: 平滑的过渡效果
- **验证反馈**: 实时边框颜色变化

### 13.3 按钮交互效果
- **悬停效果**: 背景色加深
- **点击效果**: 轻微缩放
- **加载状态**: 旋转图标 + 禁用状态

## 14. 特殊处理规范

### 14.1 长文本处理
- **换行**: 允许自然换行
- **溢出**: 不截断，完整显示
- **最大宽度**: 根据容器自适应

### 14.2 空值处理
- **显示**: 显示 "-" 占位符
- **样式**: 与正常值相同样式
- **颜色**: 稍浅的灰色 #909399

### 14.3 金额格式化
- **货币符号**: "RM " 前缀
- **千分位**: 使用逗号分隔
- **小数位**: 保留2位小数
- **对齐**: 右对齐显示

### 14.4 表单验证处理
- **实时验证**: 失焦时触发验证
- **错误提示**: 表单项下方显示
- **必填提示**: 标签前红色星号
- **提交验证**: 提交前全表单验证

## 15. 可访问性考虑

### 15.1 键盘导航
- **Tab键**: 支持Tab键在可交互元素间切换
- **Escape键**: 支持Escape键关闭模态框
- **Enter键**: 表单提交快捷键
- **焦点管理**: 模态框打开时焦点管理

### 15.2 屏幕阅读器
- **语义化**: 使用语义化HTML标签
- **标签关联**: label与输入框正确关联
- **错误提示**: 与表单项关联
- **层次结构**: 清晰的标题层次结构

### 15.3 表单可访问性
- **必填标识**: 明确的必填字段标识
- **错误描述**: 清晰的错误信息描述
- **帮助文本**: 提供输入格式说明
- **状态反馈**: 保存、加载状态的明确反馈

## 16. 编辑模式交互流程

### 16.1 进入编辑模式
1. 点击编辑按钮
2. 界面切换为表单模式
3. 显示未保存提示
4. 焦点定位到第一个输入框

### 16.2 编辑过程
1. 实时表单验证
2. 自动保存草稿（可选）
3. 显示编辑状态指示
4. 提供重置功能

### 16.3 保存操作
1. 表单验证通过
2. 显示保存加载状态
3. 保存成功反馈
4. 切换回查看模式

### 16.4 取消操作
1. 确认是否放弃更改
2. 恢复原始数据
3. 切换回查看模式
4. 清除编辑状态
