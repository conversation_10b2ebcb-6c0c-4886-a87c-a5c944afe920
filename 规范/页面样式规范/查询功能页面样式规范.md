# WorkOrderListView 查询功能页面样式规范

## 1. 页面容器样式

### 1.1 根容器
- **类名**: `page-container`
- **内边距**: 20px
- **背景色**: #f5f5f5
- **最小高度**: 100vh

### 1.2 页面标题
- **类名**: `page-title`
- **字体大小**: 24px
- **字体粗细**: 600
- **颜色**: #303133
- **下边距**: 20px

## 2. 查询条件区域样式

### 2.1 搜索卡片容器
- **组件**: `el-card`
- **类名**: `search-card`
- **下边距**: 20px
- **卡片内边距**: 20px

### 2.2 表单布局样式
- **组件**: `el-form`
- **类名**: `search-form`
- **标签位置**: 顶部对齐 (`label-position="top"`)

### 2.3 栅格布局样式
- **行间距**: 20px (`:gutter="20"`)
- **列宽度**: 每列占6个栅格单位 (`:span="6"`)
- **每行控件数量**: 4个

### 2.4 表单项样式
- **下边距**: 15px
- **标签与控件间距**: 8px (通过顶部标签位置实现)

### 2.5 控件样式规范
- **输入框高度**: 32px (Element Plus 默认)
- **选择器高度**: 32px (Element Plus 默认)
- **控件宽度**: 100% (自适应容器)
- **控件间距**: 通过栅格系统的 20px 间距实现

## 3. 按钮区域样式

### 3.1 按钮容器样式
- **类名**: `buttons-col`
- **对齐方式**: 右对齐 (`text-align: right`)
- **与查询条件间距**: 通过表单项的下边距实现

### 3.2 按钮样式规范
- **按钮高度**: 32px (Element Plus 默认)
- **按钮间距**: 10px (`margin-left: 10px`)
- **最小宽度**: 根据文字内容自适应
- **主按钮**: `type="primary"` 蓝色背景
- **普通按钮**: 默认样式

## 4. 整体布局尺寸规范

### 4.1 查询条件布局
- **分行展示**: 查询条件分为多行展示
- **每行控件数**: 4个控件
- **控件间距**: 20px (水平间距)
- **行间距**: 15px (垂直间距，通过表单项下边距)

### 4.2 按钮区域布局
- **按钮对齐**: 右对齐查询条件区域
- **按钮间距**: 10px
- **与查询条件间距**: 通过表单布局自然间距

## 5. CSS 样式定义

### 5.1 搜索区域样式
```scss
.search-card {
  .search-form {
    .el-form-item {
      margin-bottom: 15px;
    }
  }
}
```

### 5.2 按钮区域样式
```scss
.buttons-col {
  text-align: right;
  .el-button {
    margin-left: 10px;
  }
}
```

### 5.3 卡片内边距覆盖
```scss
:deep(.el-card__body) {
  padding: 20px;
}
```

## 6. 响应式布局规范

### 6.1 栅格系统
- **总栅格数**: 24
- **每个控件占用**: 6个栅格 (1/4宽度)
- **列间距**: 20px
- **自适应**: 控件宽度根据容器自动调整

### 6.2 间距体系
- **页面级间距**: 20px
- **卡片间距**: 20px
- **控件间距**: 20px (水平), 15px (垂直)
- **按钮间距**: 10px

## 7. 视觉层次规范

### 7.1 卡片层次
- **背景色**: 白色 (#ffffff)
- **阴影**: Element Plus 默认卡片阴影
- **圆角**: Element Plus 默认圆角

### 7.2 间距层次
- **一级间距**: 20px (页面、卡片级)
- **二级间距**: 15px (表单项级)
- **三级间距**: 10px (按钮级)
- **四级间距**: 8px (标签与控件)

## 8. 颜色规范

### 8.1 背景色
- **页面背景**: #f5f5f5
- **卡片背景**: #ffffff
- **按钮背景**: Element Plus 主题色

### 8.2 文字颜色
- **标题颜色**: #303133
- **标签颜色**: Element Plus 默认
- **输入文字**: Element Plus 默认

## 9. 字体规范

### 9.1 字体大小
- **页面标题**: 24px
- **表单标签**: 14px (Element Plus 默认)
- **输入文字**: 14px (Element Plus 默认)
- **按钮文字**: 14px (Element Plus 默认)

### 9.2 字体粗细
- **页面标题**: 600 (semi-bold)
- **其他文字**: 400 (normal)