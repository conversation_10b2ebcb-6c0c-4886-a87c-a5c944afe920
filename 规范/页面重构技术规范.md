# 页面重构技术规范

基于 `PartArchivesView.vue` 重构实践，结合页面目录结构规范，形成的完整页面重构技术规范。

## 1. 重构适用场景

### 1.1 需要重构的页面特征
- ❌ 页面直接放在 `src/views/` 根目录
- ❌ 内联 Mock 数据和类型定义
- ❌ 缺少专门的 API 模块
- ❌ 国际化文件独立存在，未按模块组织

### 1.2 重构目标
- ✅ 符合目录结构规范
- ✅ 代码模块化分离
- ✅ 统一的模块化组织
- ✅ 可维护的架构

## 2. 重构技术方案

### 2.1 前期分析阶段

**步骤1：分析现有结构**
```bash
# 检查页面当前位置
ls src/views/[PageName].vue

# 检查相关文件
ls src/api/modules/          # API模块
ls src/mock/data/            # Mock数据
ls src/types/                # 类型定义
ls src/locales/modules/      # 国际化
```

**步骤2：确定目标模块**
根据页面功能确定所属模块：
- 销售相关 → `sales`
- 售后相关 → `afterSales`  
- 零件相关 → `parts`
- 基础功能 → `base`

### 2.2 目录结构创建

**标准模块目录结构：**
```
src/
├── views/模块名/功能名/
│   ├── 功能名View.vue              # 路由页面
│   └── components/                 # 非路由页面和组件
├── api/modules/模块名/
│   └── 功能名.ts                   # API接口
├── types/模块名/
│   └── 功能名.d.ts                 # 类型定义
├── mock/data/模块名/
│   └── 功能名.ts                   # Mock数据
└── locales/modules/模块名/
    ├── zh.json                     # 中文翻译
    └── en.json                     # 英文翻译
```

**创建命令示例：**
```bash
# 创建目录结构
mkdir -p src/views/parts/archives/components
mkdir -p src/api/modules/parts
mkdir -p src/types/parts
mkdir -p src/mock/data/parts
```

### 2.3 重构执行步骤

#### 步骤1：创建基础目录结构
```bash
mkdir -p src/views/{模块名}/{功能名}/components
mkdir -p src/api/modules/{模块名}
mkdir -p src/types/{模块名}
mkdir -p src/mock/data/{模块名}
```

#### 步骤2：创建类型定义
```typescript
// src/types/{模块名}/{功能名}.d.ts
export interface {功能名}Item {
  // 根据原页面内联接口定义
}

export interface {功能名}SearchParams {
  page?: number;
  pageSize?: number;
  // 其他搜索参数
}

export interface {功能名}PageResponse {
  list: {功能名}Item[];
  total: number;
}
```

#### 步骤3：重构Mock数据
```typescript
// src/mock/data/{模块名}/{功能名}.ts
import type { {功能名}SearchParams, {功能名}PageResponse } from '@/types/{模块名}/{功能名}';

// 动态生成模拟数据（推荐25-30条，便于测试分页）
function generateMockData() {
  const dataCount = Math.floor(Math.random() * 6) + 25;
  const mockData = [];
  
  for (let i = 0; i < dataCount; i++) {
    mockData.push({
      // 动态生成的字段
    });
  }
  
  return mockData;
}

const mockData = generateMockData();

export const get{功能名}List = (params: {功能名}SearchParams): Promise<{功能名}PageResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 搜索过滤逻辑
      let filteredData = [...mockData];
      
      // 分页处理
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      resolve({
        list: filteredData.slice(start, end),
        total: filteredData.length
      });
    }, 500);
  });
};
```

#### 步骤4：创建API模块
```typescript
// src/api/modules/{模块名}/{功能名}.ts
import request from '@/api';
import type { {功能名}SearchParams, {功能名}PageResponse } from '@/types/{模块名}/{功能名}';
import { get{功能名}List } from '@/mock/data/{模块名}/{功能名}';
import { USE_MOCK_API } from '@/utils/mock-config';

export const get{功能名} = (params: {功能名}SearchParams): Promise<{功能名}PageResponse> => {
  if (USE_MOCK_API) {
    return get{功能名}List(params);
  } else {
    return request.get<any, {功能名}PageResponse>('/{模块名}/{功能名}/list', { params });
  }
};
```

#### 步骤5：更新国际化文件
在 `src/locales/modules/{模块名}/zh.json` 中添加：
```json
{
  "{功能名}": {
    "title": "页面标题",
    // 其他翻译键
  }
}
```

#### 步骤6：重构页面文件
```vue
<!-- src/views/{模块名}/{功能名}/{功能名}View.vue -->
<script setup lang="ts">
import { get{功能名} } from '@/api/modules/{模块名}/{功能名}';
import type { {功能名}Item, {功能名}SearchParams } from '@/types/{模块名}/{功能名}';

const { t, tc } = useModuleI18n('{模块名}.{功能名}');

// 移除内联数据，使用API调用
const handleSearch = async () => {
  try {
    const response = await get{功能名}({
      ...searchParams,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    });
    
    tableData.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('搜索失败:', error);
  }
};
</script>
```

#### 步骤7：更新路由配置
```typescript
// src/router/index.ts
{
  path: '/{模块名}/{功能名}',
  name: '{功能名}',
  component: () => import('@/views/{模块名}/{功能名}/{功能名}View.vue'),
  meta: {
    title: 'menu.{功能名}',
    requiresAuth: true,
    icon: 'Files'
  }
}
```

## 3. Mock数据配置

### 3.1 环境配置文件
创建 `.env.development` 文件：
```bash
# 开发环境配置
VITE_APP_USE_MOCK_API=true
VITE_APP_BASE_API=http://localhost:8080/api/v1
```

保持 `.env` 文件用于生产环境：
```bash
VITE_APP_USE_MOCK_API=false
VITE_APP_BASE_API=http://production-api.example.com/api/v1
```

### 3.2 Mock数据最佳实践
- **数据量**：控制在 25-30 条（便于测试分页）
- **动态生成**：使用函数生成而非写死数据
- **真实场景**：包含真实的业务场景数据
- **功能完整**：支持搜索和分页功能

## 4. 国际化集成策略

### 4.1 模块化国际化结构
```
src/locales/modules/{模块名}/
├── zh.json                     # 包含该模块所有功能的中文翻译
└── en.json                     # 包含该模块所有功能的英文翻译
```

### 4.2 页面引用方式
```typescript
// 使用模块化引用
const { t, tc } = useModuleI18n('{模块名}.{功能名}');

// t() 访问当前模块翻译: {模块名}.{功能名}.title
// tc() 访问通用模块翻译: common.search
```

### 4.3 翻译文件组织
```json
{
  "{功能名A}": {
    "title": "功能A标题",
    "field1": "字段1"
  },
  "{功能名B}": {
    "title": "功能B标题",
    "field1": "字段1"
  }
}
```

## 5. 重构检查清单

### 5.1 目录结构验证
- [ ] 页面文件移动到 `src/views/{模块名}/{功能名}/{功能名}View.vue`
- [ ] API模块创建在 `src/api/modules/{模块名}/{功能名}.ts`
- [ ] Mock数据创建在 `src/mock/data/{模块名}/{功能名}.ts`
- [ ] 类型定义创建在 `src/types/{模块名}/{功能名}.d.ts`
- [ ] 国际化文件更新在 `src/locales/modules/{模块名}/`

### 5.2 代码质量验证
- [ ] 移除页面中的内联数据和类型定义
- [ ] 使用统一的API调用替换内联Mock数据
- [ ] TypeScript类型安全，无编译错误
- [ ] 国际化完整覆盖，无硬编码文本
- [ ] Mock数据功能完整，支持搜索分页

### 5.3 功能验证
- [ ] 页面正常加载，显示Mock数据
- [ ] 搜索功能正常工作
- [ ] 分页功能正常工作
- [ ] 国际化切换正常
- [ ] 控制台无错误信息

### 5.4 路由验证
- [ ] 路由路径更新为新的模块化路径
- [ ] 组件引用路径正确
- [ ] 菜单导航正常跳转
- [ ] 权限控制正常工作

## 6. 常见问题和解决方案

### 6.1 Mock数据不生效
**现象**：页面显示网络错误或调用真实API
**原因**：Mock配置未启用
**解决**：
1. 检查 `.env.development` 文件是否存在
2. 确保 `VITE_APP_USE_MOCK_API=true`
3. 重启开发服务器 `npm run dev`

### 6.2 国际化找不到翻译
**现象**：页面显示翻译键而非实际文本
**原因**：国际化模块路径不匹配
**解决**：
1. 检查 `useModuleI18n()` 的参数与文件结构是否匹配
2. 确认翻译文件中是否包含对应的键
3. 检查国际化文件是否正确加载

### 6.3 TypeScript类型错误
**现象**：编译时出现类型错误
**原因**：类型定义路径或结构问题
**解决**：
1. 检查类型定义文件的导入路径
2. 确保接口定义完整
3. 验证类型与实际数据结构匹配

### 6.4 路由404错误
**现象**：访问页面时出现404
**原因**：路由配置或组件路径错误
**解决**：
1. 检查路由配置中的 `path` 和 `component` 路径
2. 确认组件文件确实存在于指定位置
3. 验证路由名称的唯一性

### 6.5 页面功能异常
**现象**：搜索、分页等功能不工作
**原因**：API调用逻辑错误或Mock数据问题
**解决**：
1. 检查API函数的调用方式
2. 验证Mock数据的返回格式
3. 确认搜索参数的传递正确

## 7. 最佳实践建议

### 7.1 重构原则
1. **渐进式重构**：一次只重构一个页面，避免大范围影响
2. **保持功能不变**：重构过程中不改变业务逻辑和用户体验
3. **测试驱动**：每个步骤完成后及时验证功能正常
4. **文档同步**：更新相关文档和注释

### 7.2 开发流程
1. **分支管理**：使用feature分支进行重构
2. **代码审查**：重构完成后进行团队代码评审
3. **测试验证**：确保所有功能正常后再合并
4. **知识分享**：总结重构经验与团队分享

### 7.3 质量保证
1. **TypeScript严格模式**：启用严格类型检查
2. **ESLint规范**：遵循代码规范和最佳实践
3. **组件复用**：提取可复用的组件和逻辑
4. **性能优化**：合理使用缓存和懒加载

## 8. 实际案例参考

### 8.1 零件档案页面重构案例

**重构前结构：**
```
src/views/PartArchivesView.vue    # 页面文件
```

**重构后结构：**
```
src/
├── views/parts/archives/
│   ├── ArchivesView.vue                    # 重构后的页面
│   └── components/                         # 未来的组件目录
├── api/modules/parts/
│   └── archives.ts                         # API模块
├── types/parts/
│   └── archives.d.ts                       # 类型定义
├── mock/data/parts/
│   └── archives.ts                         # Mock数据
└── locales/modules/parts/
    ├── zh.json                             # 中文翻译（包含archives）
    └── en.json                             # 英文翻译（包含archives）
```

**关键改动点：**
1. 页面从根目录移动到模块化目录
2. 内联Mock数据提取到独立文件，支持动态生成
3. 内联类型定义提取到独立文件
4. 创建专门的API模块
5. 国际化集成到统一的parts模块
6. 路由路径从 `/part-archives` 改为 `/parts/archives`

**效果验证：**
- ✅ 页面功能完全正常
- ✅ Mock数据动态生成25-30条
- ✅ 支持搜索和分页测试
- ✅ 国际化正常工作
- ✅ 代码结构清晰可维护

---

**本技术规范基于实际项目重构实践总结，为DMS前端项目的页面重构提供标准化流程和最佳实践指导。**