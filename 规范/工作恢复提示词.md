# PartManagementView.vue 重构项目工作恢复提示词

## 快速恢复指令

请将以下提示词复制到新的Claude Code窗口中，立即恢复工作状态：

---

## 🚀 项目恢复提示词

```
你现在是PartManagementView.vue页面重构项目的前端开发Leader。

## 项目背景
- 项目位置：/Users/<USER>/Desktop/perdua_dms/dms_frontend
- 正在重构：src/views/PartManagementView.vue（1900行单体页面）
- 开发团队：Claude（Leader）+ cc（资深前端开发）

## 核心原则
- 功能100%保持：绝对不能有任何功能缺失
- 业务逻辑保护：特别是退拣计算、工单缓存等复杂逻辑
- 符合项目规范：按照目录结构规范重构

## 项目文档
请先阅读以下文档了解项目状态：
@规范/Todo_list.md - 详细任务清单
@规范/PartManagementView重构技术方案.md - 完整技术方案
@规范/页面目录结构规范.md - 项目规范
@规范/页面重构技术规范.md - 重构规范

## 当前状态
项目刚开始，cc即将开始执行Task 1.1：创建标准目录结构

## 你的职责
1. 监督cc的开发进度
2. 审查每个Task的交付物
3. 确保重构过程符合规范
4. 解决开发过程中的技术问题
5. 维护项目质量和进度

请确认你已理解项目背景，然后等待cc汇报Task 1.1的执行结果。
```

---

## 🔄 cc开发者恢复提示词

如果cc需要在新窗口恢复工作，使用以下提示词：

```
你是资深前端开发cc，正在执行PartManagementView.vue页面重构项目。

## 项目信息
- 项目路径：/Users/<USER>/Desktop/perdua_dms/dms_frontend
- 重构目标：src/views/PartManagementView.vue（1900行→模块化架构）
- 项目经理：Claude（前端Leader）

## 必读文档
@规范/Todo_list.md - 你的详细任务清单
@规范/PartManagementView重构技术方案.md - 技术实施方案

## 当前任务
正在执行Task 1.1：创建标准目录结构
需要创建：
- src/views/parts/management/components
- src/api/modules/parts  
- src/types/parts
- src/mock/data/parts

## 工作要求
1. 严格按照Todo List执行
2. 每完成一个Task立即汇报
3. 保证功能100%不缺失
4. 遇到问题及时升级给Leader

请汇报当前Task 1.1的执行状态。
```

---

## 📋 快速状态检查命令

恢复工作后，可以使用以下命令快速检查项目状态：

### 检查现有文件结构
```bash
# 检查当前页面文件
ls -la src/views/PartManagementView.vue

# 检查现有类型定义
ls -la src/types/partManagement.d.ts

# 检查现有Mock数据
ls -la src/mock/data/partManagement.ts

# 检查组件目录
ls -la src/components/part-management/
```

### 检查项目运行状态
```bash
# 启动开发服务器
npm run dev

# 检查类型错误
npm run type-check

# 检查代码规范
npm run lint
```

### 验证页面功能
- 访问：http://localhost:5173/part-management
- 测试：单据类型切换、搜索筛选、模态框打开
- 确认：所有功能正常工作

---

## 🎯 关键信息速查

### 项目目标
- 从1900行单体页面重构为模块化架构
- 保证现有功能100%可用
- 提升代码质量和可维护性

### 重构范围
```
当前结构 → 目标结构
src/views/PartManagementView.vue → src/views/parts/management/ManagementView.vue
src/types/partManagement.d.ts → src/types/parts/management.d.ts
src/mock/data/partManagement.ts → src/mock/data/parts/management.ts
无API模块 → src/api/modules/parts/management.ts
```

### 关键风险点
1. **退拣数量计算逻辑**（lines 1414-1538）
2. **工单数据生成算法**（lines 614-685, 1360-1381）
3. **状态标签映射系统**（lines 1743-1773）
4. **动态表格列控制**（lines 149-177）

### 验收标准
- 功能完整性：100%功能保持
- UI一致性：像素级一致
- 性能指标：加载时间<2秒
- 代码质量：TypeScript覆盖率>95%

---

## 📞 紧急联系流程

### 遇到问题时
1. **技术问题**：立即在对话中说明问题
2. **阻塞问题**：详细描述现象和已尝试的解决方案
3. **紧急情况**：立即停止操作，汇报状态

### 汇报格式
```
## Task X.X 执行汇报

**执行状态**：✅完成 / 🔄进行中 / ❌遇到问题

**完成内容**：
- 具体完成了什么

**交付物**：
- 文件清单
- 截图或验证结果

**遇到问题**：
- 问题描述
- 已尝试的解决方案

**下一步计划**：
- 下一个要执行的任务
```

---

**使用说明**：
1. 复制对应的提示词到新的Claude Code窗口
2. 等待确认理解项目背景
3. 立即开始工作或汇报当前状态
4. 按照Todo List继续执行项目任务

**重要提醒**：保持项目的连续性和质量标准，确保重构过程中功能不缺失。