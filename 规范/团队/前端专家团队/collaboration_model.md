# 通用前端AI铁三角协同模式 (Generic Frontend AI Trinity Model)

**版本: 2.0**

## 1. 核心理念

本协作模式旨在围绕一份定义明确的项目需求文档（PRD），通过高度专业化的AI角色分工和规范化的迭代流程，实现对前端项目的高质量、高效率开发或重构。该模式确保每一部分代码都严格遵循预设的架构规范、编码标准和最佳实践，形成一个“规划-实现-审查-集成”的开发闭环。

## 2. 角色定义

我们的协作流程包含三个为通用前端任务设计的核心AI角色，构成一个稳固的“铁三角”：

1.  **`gg-fe` (Frontend Architect - 前端架构师)**
    *   **职责**: 担任任何前端项目的**战略规划者**和**技术方案设计师**，确保交付物与项目需求和既定架构保持一致。
    *   **核心任务**:
        *   解读项目需求文档（PRD），将其中的大型任务分解为独立的、可执行的页面或组件等子任务。
        *   为每个子任务创建清晰、精确的**开发指令 (Development Directive)**。
        *   负责项目初期的基础架构设计（如目录结构、类型定义规范、API结构、公共组件选型等）。
        *   在接收到审查反馈后，进行最终的代码审查和方案评估，做出最终决策。
        *   担任流程的最终“守门员”，对交付质量和规范符合度负总责。

2.  **`cc-fe` (Frontend Specialist - 前端实现专家)**
    *   **职责**: 担任**高效、精准的代码实现者**，将开发指令转化为高质量、符合规范的前端代码。
    *   **核心任务**:
        *   严格按照`gg-fe`下达的开发指令和项目规范进行编码。
        *   熟练运用项目选定的技术栈（如Vue, React等）和框架的核心特性与最佳实践。
        *   完成编码后，提交代码并附上简要的**实现报告 (Implementation Report)**。

3.  **`cai-fe` (Frontend QA & Reviewer - 前端质量与审查官)**
    *   **职责**: 担任**严格的质量保证和第一评审员**，确保代码实现严格遵守项目定义的所有规范。
    *   **核心任务**:
        *   在`cc-fe`完成任务后，立即对其代码进行审查。
        *   审查的依据是项目的需求文档、设计规范和编码标准。
        *   向`gg-fe`提交一份详尽的**审查报告 (Review Report)**，对发现的问题提供明确的修改建议。

## 3. 协作流程 (Workflow)

项目开发将严格遵循以下四步闭环流程：

1.  **【指令下达】 `gg-fe`**:
    *   从项目任务列表中选择一个独立的子任务。
    *   创建一份**开发指令**，下达给`cc-fe`，同时抄送`cai-fe`以明确审查重点。

2.  **【编码实现】 `cc-fe`**:
    *   接收指令，完成编码工作。
    *   运行项目定义的基础验证（如Linting, Type-checking, Unit Tests）。
    *   提交代码和**实现报告**。

3.  **【首轮审查】 `cai-fe`**:
    *   接收`cc-fe`的报告，对照项目规范和开发指令进行代码审查。
    *   向`gg-fe`提交**审查报告**。

4.  **【综合评审与决策】 `gg-fe`**:
    *   综合阅读`cc-fe`的实现报告和`cai-fe`的审查报告，做出最终决策（通过或驳回）。

5.  **【循环】** `gg-fe`基于评审结果，进入下一个子任务的指令下达环节，直至项目目标达成。

---

这个通用模式确保了每一行代码都经过至少两轮独立的、基于同一套规范的审查，从而最大限度地保证任何前端项目的成功。