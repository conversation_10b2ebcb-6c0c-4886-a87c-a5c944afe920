# PartManagementView 重构任务清单 (To-Do List)

**项目所有者:** @项目所有者
**前端架构师:** `gg-fe`
**当前状态:** `cc-fe` 正在执行任务 `[T-01]`

---

### 第三阶段：页面组件重构

- [ ] **T-01: 重构主页面 `ManagementView.vue`**
  - **负责人:** `cc-fe`
  - **状态:** `进行中`
  - **描述:** 将旧的1900行文件重构为新的主视图，剥离数据逻辑，并接入新的API层。

- [ ] **T-02: 拆分核心模态框与抽屉组件**
  - **负责人:** `cc-fe` (由 `gg-fe` 分解为子任务)
  - **状态:** `待办`
  - **子任务:**
    - [ ] **T-02.1:** 拆分物料单列表组件 (`MaterialOrderDialog.vue`)
    - [ ] **T-02.2:** 拆分工单详情组件 (`WorkOrderDetailDialog.vue`)
    - [ ] **T-02.3:** 拆分退拣操作组件 (`ReturnPickingDialog.vue`)
    - [ ] **T-02.4:** 拆分叫料单抽屉组件 (`RequisitionDrawer.vue`)
    - [ ] **T-02.5:** 拆分零件报损对话框 (`ScrapDialog.vue`)
    - [ ] **T-02.6:** 拆分报损记录对话框 (`RecordDialog.vue`)

---

### 第四阶段：国际化与路由优化

- [ ] **T-03: 整合国际化文件**
  - **负责人:** `cc-fe`
  - **状态:** `待办`
  - **描述:** 按照模块化规范，重组 `zh.json` 和 `en.json` 文件，并更新所有页面的翻译键。

- [ ] **T-04: 更新路由配置**
  - **负责人:** `cc-fe`
  - **状态:** `待办`
  - **描述:** 将路由从 `/part-management` 切换到 `/parts/management`，并指向新的 `ManagementView.vue` 组件。

---

### 第五阶段：最终验证与清理

- [ ] **T-05: 完整功能与逻辑验证**
  - **负责人:** `cai-fe`
  - **状态:** `待办`
  - **描述:** 对重构后的整个模块进行端到端测试，重点验证复杂业务逻辑（如退拣计算）和数据流的正确性。

- [ ] **T-06: 项目文件清理**
  - **负责人:** `cc-fe`
  - **状态:** `待办`
  - **描述:** 删除所有在重构过程中被废弃的旧文件，如 `PartManagementView.vue`、`partManagement.d.ts` 等。

---
