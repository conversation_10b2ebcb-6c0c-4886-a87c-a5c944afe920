# DMS系统接口文档编写规范

## 📝 文档总体要求

### 1. 文档结构规范

#### 1.1 必须包含的章节
- **文档标题**：明确标识功能模块和文档性质
- **接口定义**：详细的API接口规格说明  
- **交互功能说明**：页面交互维度的核心功能点
- **数据字典**：状态码、枚举值等标准化定义

#### 1.2 可选章节
- **业务背景**：复杂业务场景的上下文说明
- **数据流图**：关键业务流程的数据流向
- **异常处理**：特殊场景的处理规则

### 2. 接口定义规范

#### 2.1 接口基本信息
每个接口必须包含以下信息：
- **接口地址**：完整的API路径
- **请求方式**：HTTP方法(GET/POST/PUT/DELETE)
- **接口描述**：简洁明确的功能说明

#### 2.2 请求参数格式
```markdown
#### 请求参数

```json
{
  "参数示例": "值示例"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 参数名 | 类型 | 是/否 | 详细说明 |
```

#### 2.3 响应结果格式
```markdown
#### 响应结果

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据结构
  }
}
```

| 字段名 | 类型 | 说明 |
|--------|------|------|
| 字段名 | 类型 | 详细说明 |
```

### 3. 编写标准与约定

#### 3.1 命名规范
- **接口路径**：使用REST风格，资源名词复数形式
- **参数名称**：驼峰命名法，见名知意
- **字段说明**：中文描述，准确简洁

#### 3.2 数据类型规范
- **integer**：整数类型
- **string**：字符串类型  
- **decimal**：小数类型
- **boolean**：布尔类型
- **array**：数组类型
- **object**：对象类型

#### 3.3 必填字段标识
- **是**：必传参数
- **否**：可选参数，需注明默认值

#### 3.4 状态码规范
- **200**：请求成功
- **400**：请求参数错误
- **401**：未授权访问
- **403**：权限不足
- **404**：资源不存在
- **500**：服务器内部错误

### 4. 交互功能说明规范

#### 4.1 功能模块化描述
按页面交互功能进行模块化拆分：
- 🔍 **搜索筛选功能**
- 📊 **数据展示功能** 
- 🔧 **操作工具功能**
- 📱 **弹窗交互功能**

#### 4.2 功能描述格式
```markdown
### 功能名称

**功能描述：** 简要说明功能作用和价值

- **子功能1：** 具体功能点说明
- **子功能2：** 具体功能点说明

**交互特点：**
  - 交互方式说明
  - 用户体验特点
  - 技术实现要点
```

### 5. 数据字典规范

#### 5.1 枚举值定义
```markdown
## 📝 [枚举名称]枚举

| 枚举值 | 中文名称 | 说明 |
|--------|----------|------|
| VALUE1 | 显示名称 | 详细说明 |
| VALUE2 | 显示名称 | 详细说明 |
```

#### 5.2 状态码定义
```markdown
## 🔧 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
```

### 6. 格式与排版规范

#### 6.1 标题层级
- **一级标题 #**：文档标题
- **二级标题 ##**：主要章节
- **三级标题 ###**：接口或功能模块
- **四级标题 ####**：具体接口字段

#### 6.2 图标使用规范
- 📋：接口定义
- 🎯：功能要点  
- 🔍：搜索相关
- 📊：数据展示
- 🔧：工具操作
- 📱：移动/弹窗
- ⚡：性能/实时
- 🔄：流程/状态
- 📝：数据字典

#### 6.3 代码块格式
- JSON示例使用 ```json 代码块
- 接口路径使用行内代码格式 `code`
- 参数名称使用行内代码格式

### 7. 质量检查清单

#### 7.1 完整性检查
- [ ] 所有接口都有完整的请求/响应定义
- [ ] 参数表格字段完整(参数名/类型/必填/说明)
- [ ] 响应字段都有对应的说明
- [ ] 枚举值和状态码定义完整

#### 7.2 准确性检查  
- [ ] 接口地址格式正确
- [ ] 数据类型标注准确
- [ ] 必填标识正确
- [ ] 示例数据符合实际业务

#### 7.3 可读性检查
- [ ] 标题层级清晰
- [ ] 表格格式规范
- [ ] 代码块格式正确
- [ ] 图标使用恰当

### 8. 维护更新规范

#### 8.1 版本管理
- 接口变更需要更新文档版本号
- 重大变更需要保留历史版本记录
- 废弃接口需要明确标注

#### 8.2 审核流程
- 开发人员编写初稿
- 产品经理审核业务逻辑
- 技术负责人审核技术规范
- 测试人员验证接口实现

---

## 📌 使用说明

1. **使用前**：请仔细阅读本规范，确保理解所有要求
2. **编写时**：参照模版文档，确保格式一致性
3. **完成后**：使用质量检查清单进行自检
4. **提交前**：通过规定的审核流程 