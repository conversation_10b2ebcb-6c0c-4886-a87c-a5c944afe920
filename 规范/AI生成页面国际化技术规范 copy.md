# AI生成页面国际化技术规范

本规范定义了AI生成页面时国际化部分的标准实现方式，确保生成的代码符合项目的国际化架构要求。

## 1. 基础配置

### 1.1 导入和初始化（必需）
```vue
<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'

// 根据页面所属模块选择正确的模块名
const { t, tc } = useModuleI18n('模块名')
</script>
```

### 1.2 模块选择规则

项目国际化文件按照五个核心模块进行组织：

| 模块 | 用途 | 内容范围 | 使用示例 |
|------|------|---------|----------|
| **common** | 通用文本，跨模块使用 | 按钮、操作、状态提示等 | `tc('save')`, `tc('cancel')`, `tc('operations')` |
| **sales** | 销售管理相关功能 | 车辆、订单、客户管理等 | `sales.prospects`, `sales.orders` |
| **aftersales** | 售后服务相关功能 | 维修、质检、客户服务等 | `aftersales.repairs`, `aftersales.warranty` |
| **parts** | 零件管理相关功能 | 库存、采购、供应商管理等 | `parts.management`, `parts.archives` |
| **base** | 基础系统功能 | 用户、角色、菜单管理等 | `base.users`, `base.roles` |

**选择原则**：
- 跨模块通用内容 → `common` 模块
- 特定业务功能 → 对应业务模块
- 尽量避免一个页面使用多个业务模块

### 1.3 多模块使用（特殊情况）

```vue
<script setup lang="ts">
import { useMultiModuleI18n } from '@/composables/useModuleI18n'

// 同时使用多个模块（尽量避免）
const { sales, parts, tc } = useMultiModuleI18n(['sales', 'parts'])
</script>
```

## 2. 代码使用规范

### 2.1 模板中的使用

**页面标题和标签**：
```vue
<!-- 页面主标题 -->
<h1 class="page-title">{{ t('title') }}</h1>

<!-- 表单字段标签 -->
<el-form-item :label="t('partName')">
<el-form-item :label="t('partNumber')">

<!-- 表格列标题 -->
<el-table-column :label="t('partName')" prop="partName" />
```

**输入框和选择器**：
```vue
<!-- 输入框占位符 -->
<el-input 
  v-model="form.partName"
  :placeholder="t('partNamePlaceholder')"
  clearable
/>

<!-- 选择器 -->
<el-select v-model="form.status" :placeholder="t('selectStatus')">
  <el-option :label="t('statusActive')" value="active" />
</el-select>

<!-- 日期选择器 -->
<el-date-picker
  v-model="form.dateRange"
  type="daterange"
  :range-separator="tc('to')"
  :start-placeholder="tc('startDate')"
  :end-placeholder="tc('endDate')"
/>
```

**按钮和操作**：
```vue
<!-- 通用按钮使用 tc() -->
<el-button type="primary" @click="handleSearch">{{ tc('search') }}</el-button>
<el-button @click="handleReset">{{ tc('reset') }}</el-button>

<!-- 业务特定按钮使用 t() -->
<el-button type="primary" @click="handleCreateOrder">{{ t('createOrder') }}</el-button>

<!-- 表格操作列 -->
<el-table-column :label="tc('operations')" width="200">
  <template #default="scope">
    <el-button link type="primary" size="small">{{ tc('detail') }}</el-button>
    <el-button link type="warning" size="small">{{ tc('edit') }}</el-button>
    <el-button link type="danger" size="small">{{ tc('delete') }}</el-button>
  </template>
</el-table-column>
```

### 2.2 JavaScript中的使用

**消息提示**：
```typescript
// 通用消息使用 tc()
ElMessage.success(tc('operationSuccessful'))
ElMessage.error(tc('operationFailed'))

// 业务特定消息使用 t()
ElMessage.success(t('orderCreatedSuccess'))
ElMessage.error(t('inventoryInsufficient'))
```

**确认对话框**：
```typescript
try {
  await ElMessageBox.confirm(
    t('confirmDeleteMessage', { name: item.name }),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning'
    }
  )
  
  await deleteItem(item.id)
  ElMessage.success(tc('deleteSuccess'))
} catch (error) {
  if (error !== 'cancel') {
    ElMessage.error(tc('operationFailed'))
  }
}
```

## 3. 键值命名规范

### 3.1 嵌套结构设计（强制）

**修复前（扁平结构 ❌）**：
```json
{
  "roleManagement": "角色管理",
  "addRole": "新增角色",
  "roleName": "角色名称",
  "roleNamePlaceholder": "请输入角色名称"
}
```

**修复后（嵌套结构 ✅）**：
```json
{
  "role": {
    "title": "角色管理",
    "fields": {
      "name": "角色名称",
      "namePlaceholder": "请输入角色名称"
    },
    "actions": {
      "add": "新增角色",
      "edit": "编辑角色",
      "delete": "删除角色"
    }
  }
}
```

### 3.2 标准命名模式

```json
{
  "页面对象": {
    "title": "页面标题",
    "subtitle": "页面副标题",
    "fields": {
      "fieldName": "字段显示名",
      "fieldNamePlaceholder": "字段占位符",
      "fieldNameTooltip": "字段提示信息"
    },
    "status": {
      "active": "激活",
      "inactive": "禁用",
      "pending": "待处理"
    },
    "actions": {
      "create": "新建",
      "import": "导入",
      "export": "导出"
    },
    "messages": {
      "createSuccess": "创建成功",
      "updateSuccess": "更新成功",
      "deleteConfirm": "确定要删除吗？"
    }
  }
}
```

### 3.3 键名映射对照表

| Vue文件中的用法 | JSON文件中的路径 | 中文显示 | 英文显示 |
|----------------|-----------------|---------|---------|
| `t('role.title')` | `base.role.title` | 角色管理 | Role Management |
| `t('role.fields.name')` | `base.role.fields.name` | 角色名称 | Role Name |
| `t('role.fields.namePlaceholder')` | `base.role.fields.namePlaceholder` | 请输入角色名称 | Please enter role name |
| `t('role.actions.add')` | `base.role.actions.add` | 新增角色 | Add Role |
| `tc('save')` | `common.save` | 保存 | Save |
| `tc('operations')` | `common.operations` | 操作 | Operations |

**重要提醒**: 如果页面显示类似 `base.role.title` 这样的原始键名而不是翻译文本，说明JSON文件结构与Vue文件用法不匹配，需要调整JSON文件为嵌套结构。

## 4. 模块特定规范

### 4.1 零件管理模块 (parts.*)
```json
{
  "inventory": {
    "title": "库存管理",
    "fields": {
      "partName": "零件名称",
      "partNumber": "零件编号",
      "stockQuantity": "库存数量"
    },
    "status": {
      "inStock": "有库存",
      "outOfStock": "缺货",
      "lowStock": "库存不足"
    }
  }
}
```

### 4.2 销售管理模块 (sales.*)
```json
{
  "order": {
    "title": "订单管理",
    "fields": {
      "orderNumber": "订单号",
      "customerName": "客户姓名",
      "vehicleModel": "车型"
    },
    "status": {
      "pending": "待处理",
      "confirmed": "已确认",
      "delivered": "已交付"
    }
  }
}
```

### 4.3 售后管理模块 (aftersales.*)
```json
{
  "repair": {
    "title": "维修管理",
    "fields": {
      "workOrderNumber": "工单号",
      "vehiclePlate": "车牌号",
      "serviceType": "服务类型"
    },
    "status": {
      "scheduled": "已预约",
      "inProgress": "维修中",
      "completed": "已完成"
    }
  }
}
```

## 5. 页面模板示例

### 5.1 列表页面模板
```vue
<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('list.title') }}</h1>
    
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('fields.name')">
              <el-input 
                v-model="searchForm.name"
                :placeholder="t('fields.namePlaceholder')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">{{ tc('search') }}</el-button>
              <el-button @click="handleReset">{{ tc('reset') }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    
    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" border>
        <el-table-column :label="t('fields.name')" prop="name" />
        <el-table-column :label="tc('operations')" width="200">
          <template #default="scope">
            <el-button link type="primary" @click="handleDetail(scope.row)">
              {{ tc('detail') }}
            </el-button>
            <el-button link type="warning" @click="handleEdit(scope.row)">
              {{ tc('edit') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('模块名')
</script>
```

### 5.2 表单页面模板
```vue
<template>
  <div class="form-container">
    <h1>{{ t('form.title') }}</h1>
    
    <el-form :model="form" :rules="rules" label-position="top">
      <el-form-item :label="t('fields.name')" prop="name">
        <el-input 
          v-model="form.name"
          :placeholder="t('fields.namePlaceholder')"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSave">{{ tc('save') }}</el-button>
        <el-button @click="handleCancel">{{ tc('cancel') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { useModuleI18n } from '@/composables/useModuleI18n'

const { t, tc } = useModuleI18n('模块名')

const rules = {
  name: [
    { required: true, message: t('validation.nameRequired'), trigger: 'blur' }
  ]
}
</script>
```

## 6. 验证和测试

### 6.1 页面显示检查
- **基本检查**: 确认所有文本显示为翻译后的内容而不是键名
- **按钮检查**: 确认所有按钮显示正确的文本
- **表单检查**: 确认标签和占位符显示正确
- **消息检查**: 触发操作，确认成功/错误消息正确显示

### 6.2 语言切换测试
- **中英文切换**: 切换语言后确认所有文本正确更新
- **页面刷新**: 切换语言后刷新页面，确认语言设置保持
- **新打开页面**: 在新标签页打开页面，确认使用正确语言

### 6.3 测试用例示例
```javascript
// 测试用例：角色管理页面
describe('Role Management i18n', () => {
  test('显示正确的页面标题', () => {
    expect(screen.getByText('角色管理')).toBeInTheDocument()
    // 不应该显示键名
    expect(screen.queryByText('base.role.title')).not.toBeInTheDocument()
  })
  
  test('按钮显示正确文本', () => {
    expect(screen.getByText('保存')).toBeInTheDocument()
    expect(screen.getByText('取消')).toBeInTheDocument()
  })
})
```

## 7. 质量检查清单

AI生成页面时必须确保：

### 7.1 基础检查
- [ ] 已导入 `useModuleI18n` composable
- [ ] 已正确初始化 `{ t, tc }` 解构
- [ ] 模块名选择正确（如 `parts.management`）
- [ ] 页面标题使用 `{{ t('title') }}`

### 7.2 模板检查
- [ ] 所有表单标签使用 `t()` 函数
- [ ] 占位符使用对应的 `*Placeholder` 键
- [ ] 通用按钮（搜索、重置、保存、取消等）使用 `tc()` 函数
- [ ] 业务特定按钮使用 `t()` 函数
- [ ] 表格操作列使用 `tc()` 获取通用操作文本

### 7.3 JavaScript检查
- [ ] 消息提示优先使用通用的 `tc()` 函数
- [ ] 业务特定消息使用 `t()` 函数
- [ ] 确认对话框按钮文本使用 `tc()` 函数

### 7.4 键值结构检查
- [ ] 使用嵌套结构组织翻译键
- [ ] 字段名使用语义化命名
- [ ] 占位符添加 `Placeholder` 后缀
- [ ] 状态值使用状态名作为键
- [ ] 消息提示使用国际化文本

## 8. 常见问题和解决方案

### 8.1 显示问题
**Q: 页面显示 `base.role.title` 而不是翻译文本？**
A: JSON文件结构与Vue文件用法不匹配，需要将扁平结构改为嵌套结构。

**Q: 如何判断内容应该放在哪个模块？**
A: 根据功能归属判断，跨模块通用内容放在 common 模块，特定业务功能放在对应业务模块。

### 8.2 使用问题
**Q: 可以在一个组件中使用多个模块吗？**
A: 可以，但应尽量避免。如果确实需要，使用 `useMultiModuleI18n` 方法。

**Q: 如何处理动态翻译？**
A: 使用模板字符串或参数传递：`t('confirmDelete', { name: item.name })`

### 8.3 常见错误

**❌ 错误：未导入 useModuleI18n**
```vue
<script setup>
// 缺少导入
const title = 'hardcoded title'
</script>
```

**✅ 正确：正确导入和使用**
```vue
<script setup>
import { useModuleI18n } from '@/composables/useModuleI18n'
const { t, tc } = useModuleI18n('parts.management')
</script>
```

**❌ 错误：混用 t 和 tc**
```vue
<!-- 通用按钮不应该使用 t -->
<el-button>{{ t('search') }}</el-button>
```

**✅ 正确：正确区分使用**
```vue
<!-- 通用按钮使用 tc -->
<el-button>{{ tc('search') }}</el-button>
<!-- 业务特定内容使用 t -->
<h1>{{ t('title') }}</h1>
```

## 9. 团队协作和最佳实践

### 9.1 团队协作规范
- **模块责任制**: 每个开发人员负责自己模块的国际化文件
- **通用内容协商**: 修改 common 模块时需要团队讨论
- **及时同步**: 新增翻译内容及时同步到所有语言版本
- **代码审查**: 国际化相关代码必须经过审查

### 9.2 性能优化建议
- **按需加载**: 系统自动按需加载相关模块的翻译文件
- **避免重复**: 通用内容统一放在 common 模块，避免重复定义
- **缓存机制**: 利用模块加载器的缓存机制提高性能
- **合理分割**: 避免单个翻译文件过大，合理分割模块

### 9.3 维护管理
- **定期整理**: 定期清理不再使用的翻译键
- **文档更新**: 及时更新翻译文档和示例
- **版本控制**: 重要修改时备份原始文件
- **一致性检查**: 定期检查中英文翻译的一致性

## 10. 迁移指南

### 10.1 从旧方式迁移
```javascript
// 旧方式 ❌
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const title = t('sales.vehicleList')
const saveBtn = t('common.save')

// 新方式 ✅
import { useModuleI18n } from '@/composables/useModuleI18n'
const { t, tc } = useModuleI18n('sales')
const title = t('vehicleList')
const saveBtn = tc('save')
```

### 10.2 迁移步骤
1. **替换导入**: 将 `useI18n` 替换为 `useModuleI18n`
2. **更新翻译键**: 移除模块前缀，直接使用键名
3. **区分模块**: 明确区分当前模块和通用模块的翻译
4. **测试验证**: 确保所有翻译正常工作

## 11. 工具支持

### 11.1 开发工具
- **自动检查**: 开发过程中自动检查翻译键的使用规范
- **类型提示**: TypeScript 提供完整的类型提示支持
- **热更新**: 开发环境支持翻译文件的热更新
- **ESLint规则**: 配置ESLint规则检查国际化规范

### 11.2 VS Code插件推荐
- **i18n Ally**: 提供翻译文件的可视化管理
- **Vue Language Features**: Vue 3的官方语言支持
- **TypeScript Importer**: 自动导入类型定义

### 11.3 构建工具集成
- **打包优化**: Vite自动优化翻译文件的打包和分割
- **类型检查**: 构建时检查翻译键的类型安全
- **未使用键检测**: 自动检测并报告未使用的翻译键

---

**重要提醒**：此规范是强制性的，AI生成的所有页面代码都必须严格遵循此规范，确保项目国际化的一致性和可维护性。

**最后更新**: 2024年12月

**文档版本**: v3.0 - 优化结构，消除重复，内容更加清晰简洁